<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.DataServices</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Controls.DataServices.DataServiceCollection`1">
            <summary>
            DataServiceCollection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.DataServiceCollection`1.TotalItemCount">
            <summary>
            Gets the total item count.
            </summary>
            <value>The total item count.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.DataServiceCollection`1.#ctor(System.Data.Services.Client.DataServiceContext)">
            <summary>
            Initializes a new instance of the DataServiceCollection class.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.DataServiceCollection`1.OnResultsReceived(System.IAsyncResult)">
            <summary>
            Called when the results arrive asynchronously from the server.
            </summary>
            <remarks>
            Since there is no guarantee that this method will be invoked on the UI thread,
            we have to marshal the response operation back to the main application thread 
            (the UI thread). Code that accesses entities, accesses the DataServiceContext or
            enumerates results of LINQ queries needs to happen on the UI thread.
            </remarks>
            <param name="result">The result.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs">
            <summary>
            Event arguments for a completed submit operation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs.DataServiceResponse">
            <summary>
            Gets the data service response.
            </summary>
            <value>The data service response.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs.HasError">
            <summary>
            Gets a value indicating whether the operation has failed. If true, inspect the Error property for details.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs.IsErrorHandled">
            <summary>
            Gets or sets a value indicating whether the operation error has been marked as handled by calling <see cref="M:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs.MarkErrorAsHandled"/> method.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs.MarkErrorAsHandled">
            <summary>
            Marks the error as handled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DataServices.DataServiceSubmittingChangesEventArgs">
            <summary>
            Event arguments for an in progress submit operation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.DataServiceSubmittingChangesEventArgs.SaveChangesOptions">
            <summary>
            Gets or sets the options for saving changes.
            </summary>
            <value>The options for saving changes.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs">
            <summary>
            Provides data for the LoadedData event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs.Entities">
            <summary>
            Gets all the top-level entities that were loaded.
            </summary>
            <value>The top-level entities that were loaded.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs.HasError">
            <summary>
            Gets a value indicating whether the operation has failed.
            </summary>
            <value><c>true</c> if the operation failed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs.TotalEntityCount">
            <summary>
            Gets the total entity count for the load operation.
            </summary>
            <value>The total entity count for the load operation.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs.IsErrorHandled">
            <summary>
            Gets a value indicating whether the operation error has been marked as handled by calling the MarkErrorAsHandled method.
            </summary>
            <value>
            	<c>true</c> if the error has been marked as handled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs.MarkErrorAsHandled">
            <summary>
            For a failed operation, marks the error as handled so the exception is not thrown.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DataServices.LoadingDataEventArgs">
            <summary>
            Provides data for the LoadingData event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.LoadingDataEventArgs.Query">
            <summary>
            Gets or sets the query that is executed remotely.
            </summary>
            <value>The query that is executed remotely.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase">
            <summary>
            Represents a view for accessing and manipulating a DataServiceContext.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.LoadingData">
            <summary>
            Occurs when a data loading operation is started.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.LoadedData">
            <summary>
            Occurs when a data loading operation is completed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.SubmittingChanges">
            <summary>
            Event raised whenever a submit operation is launched. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.SubmittedChanges">
            <summary>
            Event raised whenever a submit operation is completed. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.DataServiceContext">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.DataServiceContext"/> instance used for executing the load and submit operations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.IsLoading">
            <summary>
            Gets a value that indicates whether this collection is currently performing a Load operation.
            </summary>
            <value>
            	<c>true</c> if the collection is currently performing a Load operation; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.IsSubmittingChanges">
            <summary>
            Gets a value indicating whether the collection is currently performing a Submit operation.
            </summary>
            <value>
            	<c>true</c> if the collection is currently performing a Submit operation; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.HasChanges">
            <summary>
            Gets a value indicating whether this collection has changes.
            </summary>
            <value>
            	<c>true</c> if this instance has changes; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.CanLoad">
            <summary>
            Gets a value indicating whether a load operation could be performed based on the present state.
            </summary>
            <value><c>true</c> if this instance can load; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.IsBusy">
            <summary>
            Gets a value indicating whether this collection is loading or submitting data.
            </summary>
            <value><c>true</c> if this collection is loading or submitting; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.AutoLoad">
            <summary>
            Gets or sets a value indicating whether Load is automatically invoked when a change 
            occurs that impacts the query.
            </summary>
            <value><c>true</c> if Load will automatically be invoked when a change occurs 
            that impacts the query; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.Expand">
            <summary>
            Gets or sets the expand option.
            </summary>
            <value>The expand option.</value>
            <remarks>For more information, please visit <hyperlink value="http://msdn.microsoft.com/en-us/library/ee358709.aspx"/>.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.#ctor(System.Data.Services.Client.DataServiceContext,System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase"/> class.
            </summary>
            <param name="dataServiceContext">The data service context.</param>
            <param name="sourceCollection">The source collection.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnRefresh">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.Load">
            <summary>
            Initiates a Load operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.Load(System.Boolean)">
            <summary>
            Initiates a Load operation if possible.
            </summary>
            <param name="forceLoad">true if load should be performed even if the new 
            entity query is the same as the last successfully load one; otherwise, false. </param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.CancelLoad">
            <summary>
            Cancels the current data loading operation, if any.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.SubmitChanges">
            <summary>
            Initiates a Submit operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.CancelSubmit">
            <summary>
            Cancels the current submit operation if any.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.RejectChanges">
            <summary>
            Rejects all changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.PropertyChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.CanChangePage">
            <summary>
            Completes move to page operation. Raises PageChanged event and refreshes the data view.
            </summary>
            <value></value>
            <returns>
            true if the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex"/> value can change; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.CompletePageMove(System.Int32)">
            <summary>
            Completes the page move.
            </summary>
            <param name="newPageIndex">The index of the new page.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.MoveToPageCore(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnLoadingData(Telerik.Windows.Controls.DataServices.LoadingDataEventArgs)">
            <summary>
            Raises the <see cref="E:LoadingData" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.DataServices.LoadingDataEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnLoadedData(Telerik.Windows.Controls.DataServices.LoadedDataEventArgs)">
            <summary>
            Raises the <see cref="E:LoadedData" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.DataServices.LoadedDataEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnSubmittingChanges(Telerik.Windows.Controls.DataServices.DataServiceSubmittingChangesEventArgs)">
            <summary>
            Raises <see cref="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.SubmittingChanges"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.DataServices.DataServiceSubmittingChangesEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.OnSubmittedChanges(Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs)">
            <summary>
            Raises <see cref="E:Telerik.Windows.Controls.DataServices.QueryableDataServiceCollectionViewBase.SubmittedChanges"/> event.
            </summary>
            <param name="submittedEventArgs">The <see cref="T:Telerik.Windows.Controls.DataServices.DataServiceSubmittedChangesEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.RadDataServiceDataSource">
            <summary>
            Provides an object for loading, paging, filtering, sorting and editing entities coming from 
            a WCF Data Service.
            </summary>
            <remarks>
            Use this component to simplify the interaction between the user interface and data 
            from a DataServiceContext. After specifying a DataServiceContext and QueryName, the 
            RadDataServiceDataSource can load data and expose it through the DataView properties.
            This component also provides codeless integration with other data controls 
            like RadGridView, RadDataPager and RadDataFilter.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.AutoLoadProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.AutoLoad"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.AutoLoad">
            <summary>
            Gets or sets a value indicating whether Load is automatically invoked when a 
            change occurs that impacts the query composed by the data source.
            </summary>
            <value><c>true</c> if the data source is will automatically invoke Load when a 
            change occurs that impacts the query composed by the data source; otherwise, <c>false</c>.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.LoadDelayProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.LoadDelay"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.LoadDelay">
            <summary>
            Gets or sets the delay before an automatic data loading operation is started. 
            </summary>
            <value>The delay before an automatic data loading operation is started.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.DataView">
            <summary>
            Gets the current view of entities resulting from the last load operation, 
            using a <see cref="T:Telerik.Windows.Data.DataItemCollection"/>.
            </summary>
            <value>The current view of entities resulting from the last load operation.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.CanLoadProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.CanLoad"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.CanLoad">
            <summary>
            Gets a value indicating whether the control can perform a load operation based on the present state.
            </summary>
            <value><c>true</c> if this instance can load data; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.PageSize">
            <summary>
            Gets or sets the number of items displayed on each page of the view returned from Data, or 0 to disable paging. 
            A non-zero page size will cause the number of entities loaded with each Load operation to be limited as well, using server-side paging.
            </summary>
            <value>The size of the page.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.PageSizeProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.PageSize"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.HasChangesProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.HasChanges"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.HasChanges">
            <summary>
            Gets a value indicating whether the <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.DataServiceContext"/> currently has any entities with pending changes.
            </summary>
            <value>
            	<c>true</c> if <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.DataServiceContext"/> has changes; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.IsLoadingDataProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsLoadingData"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsLoadingData">
            <summary>
            Gets a value indicating whether <see cref="T:Telerik.Windows.Controls.RadDataServiceDataSource"/> is currently loading data.
            </summary>
            <value>
            	<c>true</c> if this instance is loading data; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.IsSubmittingChangesProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsSubmittingChanges"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsSubmittingChanges">
            <summary>
            Gets a value indicating whether <see cref="T:Telerik.Windows.Controls.RadDataServiceDataSource"/> is currently submitting changes as a result of a call to <see cref="M:Telerik.Windows.Controls.RadDataServiceDataSource.SubmitChanges"/>.
            </summary>
            <value>
            	<c>true</c> if this instance is submitting changes; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.IsBusyProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsBusy"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.IsBusy">
            <summary>
            Gets or sets a value indicating whether the dataService data source is busy (loading or submitting).
            </summary>
            <value><c>true</c> if the dataService data source is busy; otherwise, <c>false</c>.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.DataServiceContextProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.DataServiceContext"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.DataServiceContext">
            <summary>
            Gets or sets the WCF DataServiceContext instance used for executing the load and submit operations.
            </summary>
            <value>The WCF DataServiceContext.</value>
            <remarks>
            The only supported DataServiceContext.MergeOption is MergeOption.OverwriteChanges.
            Setting the MergeOption to anything else will result in an InvalidOperationException.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.QueryNameProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.QueryName"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.QueryName">
            <summary>
            Gets or sets the name of the query to use for loading.
            </summary>
            <value>The name of the query to use for loading data.</value>
            <remarks>
            The DataServiceContext will be searched for a method that returns an 
            EntityQuery, with a name that matches the name provided to 
            QueryName. The matched name can be with or without a "Query" suffix.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDataServiceDataSource.ExpandProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadDataServiceDataSource.Expand"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.Expand">
            <summary>
            Gets or sets the expand option.
            </summary>
            <value>The expand option.</value>
            <remarks>For more information, please visit <hyperlink value="http://msdn.microsoft.com/en-us/library/ee358709.aspx"/>.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.LoadCommand">
            <summary>
            Gets the command for loading data.
            </summary>
            <value>The command for loading data.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.CancelLoadCommand">
            <summary>
            Gets the command for cancelling load.
            </summary>
            <value>The command for cancelling load.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.SubmitChangesCommand">
            <summary>
            Gets the command for submitting changes.
            </summary>
            <value>The command for submitting changes.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.CancelSubmitCommand">
            <summary>
            Gets the command for cancelling submit.
            </summary>
            <value>The command for cancelling submit.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDataServiceDataSource.RejectChangesCommand">
            <summary>
            Gets the command for rejecting changes.
            </summary>
            <value>The command for rejecting changes.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadDataServiceDataSource"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.BeginInit">
            <summary>
            Starts the initialization process for this element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.EndInit">
            <summary>
            Indicates that the initialization process for the element is complete.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            	<see cref="M:System.Windows.FrameworkElement.EndInit"/> was called without <see cref="M:System.Windows.FrameworkElement.BeginInit"/> having previously been called on the element.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.Load">
            <summary>
            Initiates a Load operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.CancelLoad">
            <summary>
            Cancels the current load operation if any.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.SubmitChanges">
            <summary>
            Initiates a Submit operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.CancelSubmit">
            <summary>
            Cancels the current submit operation if any.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDataServiceDataSource.RejectChanges">
            <summary>
            Rejects all changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDataServiceDataSource.SubmittingChanges">
            <summary>
            Event raised whenever a submit operation is launched. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDataServiceDataSource.SubmittedChanges">
            <summary>
            Event raised whenever a submit operation is completed. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDataServiceDataSource.LoadingData">
            <summary>
            Occurs when a data loading operation is started.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDataServiceDataSource.LoadedData">
            <summary>
            Occurs when a data loading operation is completed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableDataServiceCollectionView`1">
            <summary>
            Represents a view for accessing and manipulating strongly-typed data 
            provided by a DataServiceContext.
            </summary>
            <typeparam name="TEntity">The type of the entity.</typeparam>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.DataServiceQuery">
            <summary>
            Gets the data service query.
            </summary>
            <value>The data service query.</value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.#ctor(System.Data.Services.Client.DataServiceContext,System.Data.Services.Client.DataServiceQuery{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableDataServiceCollectionView`1"/> class.
            </summary>
            <param name="dataServiceContext">The DataServiceContext.</param>
            <param name="dataServiceQuery">The DataServiceQuery.</param>
            <remarks>
            The only supported DataServiceContext.MergeOption is MergeOption.OverwriteChanges.
            Setting the MergeOption to anything else will result in an InvalidOperationException.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.CreateInternalList">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.CreateView">
            <summary>
            Returns <see cref="T:System.Linq.IQueryable"/> with applied filtering, sorting, grouping and paging.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.LoadCore(System.Data.Services.Client.DataServiceQuery,System.Int32)">
            <summary>
            Loads the data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.ReturnsSingleEntity(System.Data.Services.Client.DataServiceQuery{`0})">
            <summary>
            Determines whether the query will return a single resource,
            i.e. it has an Equals filter on its primary key column.
            </summary>
            <param name="query">The query.</param>
            <returns>
            true if the URI ends in an opening bracket, one or more digits and a closing bracket,
            i.e. something like "...Customers(1)" getting the first customer.
            </returns>
            <remarks>
            Using a entity key to select a single entity (see rules: collectionNavigation and keyPredicate)
            For example: <hyperlink value='http://services.odata.org/OData/OData.svc/Categories(1)'></hyperlink>
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.System#Collections#Generic#IEnumerable{TEntity}#GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.GetEffectiveItemCount">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.UpdateTotalItemCount">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnFilterDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnFilterDescriptorsLogicalOperatorChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnFilterDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.IFilterDescriptor})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnGroupDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnGroupDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.IGroupDescriptor})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnSortDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableDataServiceCollectionView`1.OnSortDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.ISortDescriptor})">
            <inheritdoc />
        </member>
    </members>
</doc>
