﻿<Popup x:Class="Everylang.App.View.Controls.SmartClick.ShortLink.ShortLinkPopup"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             mc:Ignorable="d" 
             AllowsTransparency="True" Focusable="False" Placement="Absolute" Closed="ShortLinkPopup_OnClosed"
             x:ClassModifier="internal">
    <Border BorderThickness="1">
        <StackPanel Orientation="Horizontal" Margin="8,2,2,2">
            <TextBlock FontSize="13" FontWeight="DemiBold" x:Name="TextBlockText" VerticalAlignment="Center" MaxWidth="500" TextWrapping="Wrap"/>
            <telerik:RadPathButton x:Name="ButtonCopy" Margin="8,0,0,0" Width="25" Height="25" Click="Copy" Cursor="Hand"  VerticalAlignment="Center">
                <wpf:MaterialIcon Width="20" Height="18" Kind="ContentCopy" />
            </telerik:RadPathButton>
            <TextBlock FontSize="11"  Margin="3,0,0,0" x:Name="TextBlockF1" VerticalAlignment="Center" Text="F1" Visibility="Collapsed"/>
            <telerik:RadPathButton x:Name="ButtonReplace" Margin="5,0,0,0" Width="25" Height="25" Click="Replace" Cursor="Hand"  VerticalAlignment="Center">
                <wpf:MaterialIcon Width="20" Height="20" Kind="Cached" />
            </telerik:RadPathButton>
            <TextBlock FontSize="11"  Margin="3,0,0,0" x:Name="TextBlockF2" VerticalAlignment="Center" Text="F2" Visibility="Collapsed"/>
        </StackPanel>
    </Border>
</Popup>
