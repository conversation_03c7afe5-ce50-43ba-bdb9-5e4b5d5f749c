﻿using Everylang.App.Callback;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class AboutSettingsViewModel : ViewModelBase
    {
        private bool UpdateAvailable { get; set; }

        public DelegateCommand DownloadCommand
        {
            get;
            private set;
        }

        public AboutSettingsViewModel()
        {
            DownloadCommand = new DelegateCommand(Download);
            GlobalEventsApp.EventUpdateAvailable += UpdateAvailableHandler;
        }

        private void UpdateAvailableHandler(string varsion, bool isBeta)
        {
            UpdateAvailable = true;
            _version = varsion;
            base.OnPropertyChanged(nameof(IsVisibleUpdate));
        }

        public string? SelfName => System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString();

        public bool IsAdmin => SettingsApp.SettingsManager.IsAdministrator();

        public Visibility IsVisibleUpdate
        {
            get
            {
                if (UpdateAvailable)
                {
                    return Visibility.Visible;
                }
                return Visibility.Collapsed;
            }
        }

        private string? _version;

        public string UpdateText => LocalizationManager.GetString("AboutSettingsUpdateAvailable") + $" {_version}";
        private void Download(object o)
        {
            Process.Start("https://everylang.net/download");
        }

    }
}
