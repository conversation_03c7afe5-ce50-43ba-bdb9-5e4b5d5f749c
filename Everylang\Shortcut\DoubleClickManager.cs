﻿using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using NHotkey;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Telerik.Windows.Controls;

namespace Everylang.App.Shortcut
{
    static class DoubleKeyDownManager
    {
        private static List<DoublePressData> _doublePressDataList;
        private static List<KeyData> _keyDataList;
        internal static Action DoubleKeyShift;


        static DoubleKeyDownManager()
        {
            _doublePressDataList = new List<DoublePressData>();
        }

        internal static void Start()
        {
            HookCallBackKeyUp.CallbackEventHandler += CallbackEventHandlerKeyUp;


            _keyDataList = new List<KeyData>()
            {
                new KeyData() {Code = "LeftOrRightCtrl", Value = new [] { "162","163"}, Name = LocalizationManager.GetString("HotKeyDoubleLeftOrRightCtrl")},
                new KeyData() {Code = "LeftCtrl", Value = new [] { "162"}, Name = LocalizationManager.GetString("HotKeyDoubleLeftCtrl")},
                new KeyData() {Code = "RightCtrl", Value = new [] { "163"}, Name = LocalizationManager.GetString("HotKeyDoubleRightCtrl")},
                new KeyData() {Code = "LeftOrRightShift", Value = new [] { "160","161"}, Name = LocalizationManager.GetString("HotKeyDoubleLeftOrRightShift")},
                new KeyData() {Code = "LeftShift", Value = new [] { "160"}, Name = LocalizationManager.GetString("HotKeyDoubleLeftShift")},
                new KeyData() {Code = "RightShift", Value = new [] { "161"}, Name = LocalizationManager.GetString("HotKeyDoubleRightShift")},
                new KeyData() {Code = "LeftAlt", Value = new [] { "164"}, Name = LocalizationManager.GetString("HotKeyDoubleLeftAlt")},
                new KeyData() {Code = "RightAlt", Value = new [] { "165"}, Name = LocalizationManager.GetString("HotKeyDoubleRightAlt")},
                new KeyData() {Code = "CapsLock", Value = new [] { "20"}, Name = "CapsLock"},
                new KeyData() {Code = "Scroll", Value = new [] { "145"}, Name = "ScrollLock"},
                new KeyData() {Code = "NumLock", Value = new [] { "144"}, Name = "NumLock"},
                new KeyData() {Code = "Insert", Value = new [] { "45"}, Name = "Insert"},
                new KeyData() {Code = "Pause", Value = new [] { "19"}, Name = "Pause"},
            };
            foreach (var value in _keyDataList)
            {
                _doublePressDataList.Add(new DoublePressData(value));
            }
        }

        private static DateTime _dateTimeDoubleKeyDown;
        private static DateTime _dateTimeDoublePress;
        private static int _lastKeyValue;
        private static int _lastDoublePress;

        private static void CallbackEventHandlerKeyUp(GlobalKeyEventArgs e)
        {
            if (KeyboardLayoutManager.Instance.IsProcessing || !KeyboardLayoutWorker.IsEnabled)
            {
                return;
            }
            if (_lastKeyValue == (int)e.KeyCode)
            {
                var timeSpan = DateTime.Now - _dateTimeDoubleKeyDown;
                if (100 < timeSpan.TotalMilliseconds && timeSpan.TotalMilliseconds < 400)
                {
                    var keyDounCount = KeyboardState.GetDownKeys().Count;
                    if (keyDounCount < 2)
                    {
                        KeyDoublePress(e);
                    }
                }
                else
                {
                    _dateTimeDoubleKeyDown = DateTime.Now;
                }
            }
            _lastKeyValue = (int)e.KeyCode;
            _dateTimeDoubleKeyDown = DateTime.Now;
        }

        private static async void KeyDoublePress(GlobalKeyEventArgs e)
        {
            if (!CheckActiveProcessFileName.CheckHotKeys())
            {
                return;
            }
            if ((DateTime.Now - _dateTimeDoublePress).TotalMilliseconds < 500 && _lastDoublePress == (int)e.KeyCode)
            {
                return;
            }
            _dateTimeDoublePress = DateTime.Now;
            _lastDoublePress = (int)e.KeyCode;
            await Task.Delay(100);

            DoublePressData doublePressData = _doublePressDataList.FirstOrDefault(x => x.KeyData.Value.Contains(((int)e.KeyCode).ToString()) && !string.IsNullOrEmpty(x.NameEvent));
            if (doublePressData != null)
            {
                var handler = doublePressData.Handler;
                if (handler != null)
                {
                    var eventHandler = handler;
                    eventHandler(null, null);
                }
            }
            else
            {
                if (e.KeyCode == VirtualKeycodes.LeftShift || e.KeyCode == VirtualKeycodes.RightShift)
                {
                    DoubleKeyShift();
                }
            }

        }

        internal static void AddNew(string name, string shortcut, EventHandler<HotkeyEventArgs> handler)
        {
            var keyData = GetKeyData(shortcut);
            if (keyData != null)
            {
                DoublePressData doublePressData = _doublePressDataList.FirstOrDefault(x => x.KeyData.Code.Equals(keyData.Code));
                if (doublePressData != null)
                {
                    doublePressData.NameEvent = name;
                    doublePressData.NameEventDop = name;
                    doublePressData.Handler = handler;
                    SetKeyBusyOn(doublePressData);
                }
            }

        }

        internal static void Remove(string name)
        {
            DoublePressData doublePressData = _doublePressDataList.FirstOrDefault(x => x.NameEvent == name);
            if (doublePressData != null)
            {
                doublePressData.Handler = null;
                doublePressData.NameEvent = "";
                doublePressData.NameEventDop = "";
                SetKeyBusyOff(doublePressData);
            }
        }

        internal static List<KeyData> GetFreeKeys(string nameEvent)
        {
            var freeList = _doublePressDataList.Where(x => string.IsNullOrEmpty(x.NameEventDop) || x.NameEventDop == nameEvent).Select(x => x.KeyData);
            return freeList.ToList();
        }

        internal static KeyData GetKeyData(string shortcut)
        {
            shortcut = ShortcutManager.GetKeysFromShortcut(shortcut);
            var keyData = _keyDataList.FirstOrDefault(x => x.Code.Equals(shortcut, StringComparison.InvariantCultureIgnoreCase));
            return keyData;
        }



        private static void SetKeyBusyOn(DoublePressData doublePressData)
        {
            if (doublePressData.KeyData.Code == "LeftOrRightCtrl")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftCtrl" || x.KeyData.Code == "RightCtrl").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                        pressData.NameEventDop = doublePressData.NameEvent;
                }
            }
            if (doublePressData.KeyData.Code == "LeftCtrl" || doublePressData.KeyData.Code == "RightCtrl")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftOrRightCtrl").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                        pressData.NameEventDop = doublePressData.NameEvent;
                }
            }

            if (doublePressData.KeyData.Code == "LeftOrRightShift")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftShift" || x.KeyData.Code == "RightShift").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                        pressData.NameEventDop = doublePressData.NameEvent;
                }
            }
            if (doublePressData.KeyData.Code == "LeftShift" || doublePressData.KeyData.Code == "RightShift")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftOrRightShift").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                        pressData.NameEventDop = doublePressData.NameEvent;
                }
            }
        }

        private static void SetKeyBusyOff(DoublePressData doublePressData)
        {
            if (doublePressData.KeyData.Code == "LeftOrRightCtrl")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftCtrl" || x.KeyData.Code == "RightCtrl").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                    {
                        pressData.NameEventDop = "";
                    }
                }
            }
            if (doublePressData.KeyData.Code == "LeftCtrl" || doublePressData.KeyData.Code == "RightCtrl")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftOrRightCtrl").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                    {
                        pressData.NameEventDop = "";
                    }
                }
            }

            if (doublePressData.KeyData.Code == "LeftOrRightShift")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftShift" || x.KeyData.Code == "RightShift").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                    {
                        pressData.NameEventDop = "";
                    }
                }
            }
            if (doublePressData.KeyData.Code == "LeftShift" || doublePressData.KeyData.Code == "RightShift")
            {
                foreach (var pressData in _doublePressDataList.Where(x => x.KeyData.Code == "LeftOrRightShift").ToList())
                {
                    if (string.IsNullOrEmpty(pressData.NameEvent))
                    {
                        pressData.NameEventDop = "";
                    }
                }
            }
        }
    }

    internal class KeyData
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string[] Value { get; set; }


    }

    class DoublePressData
    {
        internal DoublePressData(KeyData keyValue)
        {
            KeyData = keyValue;
        }

        internal string NameEvent { get; set; }
        internal string NameEventDop { get; set; }
        internal EventHandler<HotkeyEventArgs> Handler { get; set; }
        internal KeyData KeyData { get; set; }
    }
}
