﻿using System;
using System.Globalization;

namespace Everylang.App.License.LicenseCore;

internal class DateOnly
{
    internal DateOnly(byte pDay, byte pMonth, ushort pYear)
    {
        Day = pDay;
        Month = pMonth;
        Year = pYear;
    }

    internal DateOnly(DateTime sourceDate)
    {
        Year = (ushort)sourceDate.Year;
        Month = (byte)sourceDate.Month;
        Day = (byte)sourceDate.Day;
    }

    internal ushort Year { get; set; }

    internal byte Month { get; set; }

    internal byte Day { get; set; }

    internal int ToInt()
    {
        return (Year ^ Month) | Day;
    }

    public override string ToString()
    {
        return ((DateTime)this).ToString(CultureInfo.InvariantCulture);
    }

    public static implicit operator DateTime(DateOnly dateobj)
    {
        return new DateTime(dateobj.Year, dateobj.Month, dateobj.Day);
    }

    public static implicit operator DateOnly(DateTime dateobj)
    {
        return new DateOnly((byte)dateobj.Day, (byte)dateobj.Month, (ushort)dateobj.Year);
    }
}