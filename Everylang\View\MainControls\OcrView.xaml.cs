﻿using Everylang.App.Callback;
using Everylang.App.OCR;
using Everylang.App.View.SettingControls.Ocr;
using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Telerik.Windows.Media.Imaging;
using Telerik.Windows.Media.Imaging.FormatProviders;
using Vanara.PInvoke;
using Application = System.Windows.Application;
using Image = System.Drawing.Image;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using Path = System.IO.Path;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for OcrView.xaml
    /// </summary>
    internal partial class OcrView
    {

        internal static readonly DependencyProperty IsImageInClipboardProperty = DependencyProperty.Register(
            nameof(IsImageInClipboard), typeof(bool),
            typeof(OcrView)
        );

        internal bool IsImageInClipboard
        {
            get => (bool)GetValue(IsImageInClipboardProperty);
            set => SetValue(IsImageInClipboardProperty, value);
        }

        internal OcrView()
        {
            InitializeComponent();
            imageEditor.ToolSettingsContainer = this.CustomSettingsContainer;
            imageEditor.ZoomToCursor = true;
        }

        private void OpenFileClick(object sender, RoutedEventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.Filter = @"Image Files (bmp, jpg, tif, png, gif, pdf)|*.bmp;*.jpg;*.tif;*.png;*.gif;*.pdf|All Files|*";
            dialog.FilterIndex = 2;
            dialog.RestoreDirectory = true;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                var fileName = dialog.FileName;
                LoadImage((Bitmap)Image.FromFile(fileName));
            }
        }

        private void FromClipboardClick(object sender, RoutedEventArgs e)
        {
            if (System.Windows.Forms.Clipboard.ContainsImage())
            {
                LoadImage(System.Windows.Forms.Clipboard.GetImage() as Bitmap);
            }
        }

        private async void CaptureClick(object sender, RoutedEventArgs e)
        {
            if (Application.Current.MainWindow != null)
            {
                Application.Current.MainWindow.Hide();
                await Task.Delay(200);
                ScreenshotTaker screenshot = new ScreenshotTaker();
                screenshot.GetImageAction += bitmap =>
                {
                    LoadImage(bitmap);
                    Application.Current.MainWindow.Show();
                };
                screenshot.OpenScreen();
            }
        }

        internal void LoadImage(Bitmap? image)
        {
            var fileName = Path.GetTempFileName();
            image?.Save(fileName);
            GridStart.Visibility = Visibility.Hidden;
            OcrTab.Visibility = Visibility.Visible;
            RadButtonClearAll.Visibility = Visibility.Visible;
            RadButtonSettings.Visibility = Visibility.Visible;
            RadButtonOcr.Visibility = Visibility.Visible;
            edText.Text = "";
            if (image != null)
            {
                imageEditor.Image = new RadBitmap(new WriteableBitmap(ImageSourceFromBitmap(image)));
                imageEditor.ApplyTemplate();
                Recognize(image);
            }
        }

        private void Recognize(Bitmap image)
        {
            BusyIndicatorOcr.IsBusy = true;
            OcrWorker ocrEngine = new OcrWorker();
            ocrEngine.ReadImage(image, OcrManager.Instance.LangsAllForRequest).ContinueWith(task =>
            {
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    edText.Text = task.Result;
                    BusyIndicatorOcr.IsBusy = false;
                });
            });
        }

        internal BitmapSource ImageSourceFromBitmap(Bitmap bmp)
        {
            var handle = bmp.GetHbitmap();
            try
            {
                return Imaging.CreateBitmapSourceFromHBitmap(handle, IntPtr.Zero, Int32Rect.Empty, BitmapSizeOptions.FromEmptyOptions());
            }
            finally { Gdi32.DeleteObject(handle); }
        }


        private void SelectLangsClick(object sender, RoutedEventArgs e)
        {
            LangsForOcrView langsForOcrView = new LangsForOcrView();
            langsForOcrView.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
            };
            PageTransitionControl.Content = langsForOcrView;
        }

        private void OcrView_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                ClearAll();
            }
        }

        private void ClearAll(object sender, RoutedEventArgs e)
        {
            ClearAll();
        }

        private void ClearAll()
        {
            try
            {
                GridStart.Visibility = Visibility.Visible;
                OcrTab.Visibility = Visibility.Hidden;
                RadButtonClearAll.Visibility = Visibility.Hidden;
                RadButtonSettings.Visibility = Visibility.Hidden;
                RadButtonOcr.Visibility = Visibility.Hidden;
                edText.Text = "";
                imageEditor.Image = null;
            }
            catch (Exception)
            {
                // ignore
            }
        }


        private void SaveClick(object sender, RoutedEventArgs e)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = @"PNG Images (.png)|.png|BMP Images (.bmp)|.bmp;|All images|.*";
            sfd.FilterIndex = 3;
            sfd.RestoreDirectory = true;
            sfd.FileName = "Screenshot " + DateTime.Now.ToString("dd-MM-yy hh-mm-ss") + ".png";
            if (sfd.ShowDialog() == DialogResult.OK)
            {
                string extension = Path.GetExtension(sfd.FileName).ToLower();
                Stream stream = sfd.OpenFile();
                IImageFormatProvider formatProvider = ImageFormatProviderManager.GetFormatProviderByExtension(extension);
                if (formatProvider != null)
                {
                    using (stream)
                    {
                        formatProvider.Export(this.imageEditor.Image, stream);
                    }
                }
            }
        }

        private void CopyClick(object sender, RoutedEventArgs e)
        {
            try
            {
                Bitmap bmp;
                using (MemoryStream outStream = new MemoryStream())
                {
                    BitmapEncoder enc = new PngBitmapEncoder();
                    enc.Frames.Add(BitmapFrame.Create(imageEditor.Image.Bitmap));
                    enc.Save(outStream);
                    bmp = new Bitmap(outStream);
                }
                System.Windows.Forms.Clipboard.SetImage(bmp);
            }
            catch (Exception)
            {
                // ignore
            }
        }

        private void OcrClick(object sender, RoutedEventArgs e)
        {
            try
            {
                Bitmap bmp;
                using (MemoryStream outStream = new MemoryStream())
                {
                    BitmapEncoder enc = new PngBitmapEncoder();
                    enc.Frames.Add(BitmapFrame.Create(imageEditor.Image.Bitmap));
                    enc.Save(outStream);
                    bmp = new Bitmap(outStream);
                }
                Recognize(bmp);
                OcrTab.SelectedIndex = 1;
            }
            catch (Exception)
            {
                // ignore
            }
        }

        private void ContentCopyClick(object sender, RoutedEventArgs e)
        {
            if (edText.Text != null) System.Windows.Forms.Clipboard.SetText(edText.Text);
        }

        private void TranslateClick(object sender, RoutedEventArgs e)
        {
            if (edText.Text != null) GlobalEventsApp.OnEventGoToMainWindowTranslate(edText.Text);
        }

        private void GridStart_IsVisibleChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            ButtonFromClipboard.Visibility = System.Windows.Forms.Clipboard.ContainsImage()
                ? Visibility.Visible
                : Visibility.Collapsed;
        }


    }
}
