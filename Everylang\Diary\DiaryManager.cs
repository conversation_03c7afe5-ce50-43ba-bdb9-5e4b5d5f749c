﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using NHotkey;
using System;

namespace Everylang.App.Diary
{
    class DiaryManager : IDisposable
    {
        private bool _isHotKeyDiaryView;
        private static DiaryManager? _instance;

        internal static DiaryManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new DiaryManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            StartDiaryHistory();
        }


        private void StartDiaryHistory()
        {
            if (SettingsManager.Settings.DiaryIsOn && !_isHotKeyDiaryView)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.DiaryShowShortcut), SettingsManager.Settings.DiaryShowShortcut, PressedDiaryView);
                _isHotKeyDiaryView = true;
            }
        }

        internal void PressedDiaryView(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            GlobalEventsApp.OnEventDiaryView();
        }

        internal void DiaryStopHistory()
        {
            _isHotKeyDiaryView = false;
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.DiaryShowShortcut));
        }


        internal void Stop()
        {
            DiaryStopHistory();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
