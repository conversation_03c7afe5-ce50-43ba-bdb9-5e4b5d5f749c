﻿<telerik:RadWindow
    x:Class="Everylang.App.View.Controls.ClipboardFormatControls.ClipboardFormatImageWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:navigation="clr-namespace:Telerik.Windows.Controls.Navigation;assembly=Telerik.Windows.Controls.Navigation"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:clipboardFormatControls="clr-namespace:Everylang.App.View.Controls.ClipboardFormatControls"
    MinWidth="300"
    MinHeight="200"
    MaxWidth="500"
    MaxHeight="600"
    CaptionHeight="10"
    CornerRadius="3"
    Deactivated="me_Deactivated"
    Header="Image"
    navigation:RadWindowInteropHelper.AllowTransparency="False"
    ResizeMode="NoResize"
    SizeToContent="True"
    WindowStartupLocation="Manual" x:ClassModifier="internal">
    <telerik:RadWindow.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="clipboardFormatControls:ClipboardFormatImageWindow" />
        </ResourceDictionary>
    </telerik:RadWindow.Resources>
    <Grid>
        <Image x:Name="ImageMu" Stretch="Uniform" Loaded="ImageMu_Loaded"/>
    </Grid>
</telerik:RadWindow>