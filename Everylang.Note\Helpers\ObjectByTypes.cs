﻿using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Point = System.Windows.Point;

namespace Everylang.Note.Helpers
{
    public class ObjectByTypes
    {
        internal static object? GetObjectAtPoint<TItemContainer>(ItemsControl control, Point p) where TItemContainer : DependencyObject
        {
            // ItemContainer - can be ListViewItem, or TreeViewItem and so on(depends on control)
            TItemContainer? obj = GetContainerAtPoint<TItemContainer>(control, p);
            if (obj == null)
                return null;

            return control.ItemContainerGenerator.ItemFromContainer(obj);
        }

        private static TItemContainer? GetContainerAtPoint<TItemContainer>(ItemsControl control, Point p) where TItemContainer : DependencyObject
        {
            HitTestResult result = VisualTreeHelper.HitTest(control, p);
            if (result != null)
            {
                DependencyObject obj = result.VisualHit;

                while (obj != null && VisualTreeHelper.GetParent(obj) != null && !(obj is TItemContainer))
                {
                    obj = VisualTreeHelper.GetParent(obj)!;
                }

                // Will return null if not found
                return obj as TItemContainer;
            }
            return null;
        }

        public static object? GetObjectAtObject<TItemContainer>(ItemsControl control, DependencyObject objS) where TItemContainer : DependencyObject
        {
            TItemContainer? obj = GetContainerAtObject<TItemContainer>(objS);
            if (obj == null)
                return null;

            return control.ItemContainerGenerator.ItemFromContainer(obj);
        }

        private static TItemContainer? GetContainerAtObject<TItemContainer>(DependencyObject obj) where TItemContainer : DependencyObject
        {
            while (VisualTreeHelper.GetParent(obj) != null && !(obj is TItemContainer))
            {
                obj = VisualTreeHelper.GetParent(obj)!;
            }
            return obj as TItemContainer;
        }

        internal static T? GetParentCard<T>(FrameworkElement? element) where T : DependencyObject
        {
            try
            {
                var item = element;
                while (item != null && VisualTreeHelper.GetParent(item) != null && !(item is T))
                {
                    item = VisualTreeHelper.GetParent(item) as FrameworkElement;
                }
                if (!(item is T))
                {
                    return null;
                }
                return item as T;
            }
            catch
            {
                return null;
            }
        }

        internal static List<T>? GetChildOfType<T>(DependencyObject? depObj)
            where T : DependencyObject
        {
            if (depObj == null) return null;
            var results = new List<T>();
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);

                var result = (child as T);
                if (result != null)
                {
                    results.Add(result);
                }
                else
                {
                    var childResults = GetChildOfType<T>(child);
                    if (childResults != null && childResults.Count > 0)
                    {
                        results.AddRange(childResults);
                    }
                }
            }

            return results;

        }
    }
}
