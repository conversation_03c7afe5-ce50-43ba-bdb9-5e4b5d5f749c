﻿using Everylang.App.Miminote;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using Everylang.Note.SettingsApp;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;
using UserControl = System.Windows.Controls.UserControl;

namespace Everylang.App.View.SettingControls.Miminote
{
    /// <summary>
    /// Interaction logic for GeneralControl.xaml
    /// </summary>
    internal partial class MiminoteControl : UserControl
    {
        internal MiminoteControl()
        {
            InitializeComponent();

        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl hotKeyControl = new HotKeyControl(LocalizationManager.GetString("OpenMainWindowShortcut"), SettingsMiminoteManager.AppSettings.AddNewShortcut, nameof(SettingsMiminoteManager.AppSettings.AddNewShortcut), MiminoteManager.AddNewNoteShortcut);
            hotKeyControl.Save += (o, args) =>
            {
                VMContainer.Instance.NotesListControlViewModel.AddNewShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (sender1, e1) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

    }
}
