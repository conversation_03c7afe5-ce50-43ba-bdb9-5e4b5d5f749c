﻿<Popup x:Class="Everylang.App.View.Controls.Ocr.OcrWaitWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation" xmlns:richTextBoxEx="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx" xmlns:formatters1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
       mc:Ignorable="d" x:Name="me" PopupAnimation="Slide"
       MouseEnter="me_MouseEnter" MouseLeave="me_MouseLeave" AllowsTransparency="True"
       Placement="Absolute" MaxHeight="400" MaxWidth="600" MinHeight="150" MinWidth="200" Height="150" Width="200"
       StaysOpen="True" Focusable="False" x:ClassModifier="internal">

    <Popup.Resources>
        <ResourceDictionary>

            <Style x:Key="ProgressRingStyle" TargetType="telerik:RadBusyIndicator" BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="IsBusy" Value="{Binding IsVisibleResult, ElementName=me, Converter={StaticResource InvertedBooleanConverter}}"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleResult, ElementName=me}" Value="True">
                        <Setter Property="Visibility" Value="Hidden"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleResult, ElementName=me}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="GridStyle" TargetType="Grid">
                <Setter Property="Visibility" Value="Hidden" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsVisibleResult, ElementName=me}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />

                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="RichTextBoxStyle" TargetType="{x:Type richTextBoxEx:RichTextBoxEx}">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
          
            </Style>


        </ResourceDictionary>
    </Popup.Resources>
    <Border BorderThickness="2" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" CornerRadius="4"
            Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid Background="Transparent">
        <telerik:RadBusyIndicator Style="{StaticResource ProgressRingStyle}" BusyContent="{telerik:LocalizableResource Key=Loading}" Grid.ZIndex="1" IsIndeterminate="True"/>
        <Grid x:Name="ResultPanel" Style="{StaticResource GridStyle}" Margin="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition  Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <telerik:Label x:Name="LabelMu" HorizontalAlignment="Left" VerticalAlignment="Top" FontSize="17"/>
            <Button Grid.Row="0" Grid.Column="1" HorizontalAlignment="Right" Height="35" MinHeight="0" Content="{telerik:LocalizableResource Key=OcrEditImage}" Focusable="False" Click="EditImage_OnClick"/>
            <richTextBoxEx:RichTextBoxEx Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="1"  x:Name="resTextBox" IsReadOnly="True" Margin="3,1,3,3"       
                                         IsInactiveSelectionHighlightEnabled="True"
                                         Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                                         Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                         Style="{StaticResource RichTextBoxStyle}"
                                         Block.LineHeight="1">
                <richTextBoxEx:RichTextBoxEx.TextFormatter>
                    <formatters1:StandartFormatter />
                </richTextBoxEx:RichTextBoxEx.TextFormatter>                
            </richTextBoxEx:RichTextBoxEx>
            
        </Grid>
       
    </Grid>
    </Border>

</Popup>



