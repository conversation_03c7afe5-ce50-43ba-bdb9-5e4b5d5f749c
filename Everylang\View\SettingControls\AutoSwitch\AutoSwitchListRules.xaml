﻿<UserControl
    x:Class="Everylang.App.View.SettingControls.AutoSwitch.AutoSwitchListRules"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:helpers1="clr-namespace:Everylang.App.View.Helpers"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    mc:Ignorable="d" x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <helpers1:AutoSwitchRuleActionConverter x:Key="RuleActionConverter" x:Shared="false" />
    </UserControl.Resources>
    <Grid Margin="10,8,0,0" Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <StackPanel
            Grid.Row="0"
            VerticalAlignment="Top"
            Orientation="Horizontal">
            <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
            </telerik:RadButton>

            <TextBlock
                Margin="10,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                FontSize="15"
                Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsListRulesHeader}" />
        </StackPanel>
        <telerik:RadGridView
            Grid.Row="1"
            Margin="0,10,10,0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            AutoGenerateColumns="False"
            CanUserDeleteRows="True"
            CanUserSortColumns="True"
            NewRowPosition="Top"
            FrozenColumnsSplitterVisibility="Hidden"
            EnableColumnVirtualization="True"
            EnableRowVirtualization="True"
            RowEditEnded="DataGridListRules_OnRowEditEnded"
            Deleted="DataGridListRules_OnDeleted"
            ItemsSource="{Binding AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels}"
            Name="DataGridListRules"
            RowIndicatorVisibility="Collapsed"
            SelectionMode="Extended"
            SelectionUnit="FullRow"
            ShowGroupPanel="False"
            IsFilteringAllowed="False"
            TextSearch.TextPath="TextForSearch">
            <telerik:RadGridView.Columns>
                <telerik:GridViewDataColumn
                    Width="150"
                    DataMemberBinding="{Binding Text}"
                    Header="{telerik:LocalizableResource Key=AutoSwitcherSettingsCombination}"
                    IsReadOnly="False" />
                <telerik:GridViewComboBoxColumn
                    Width="150"
                    ItemsSourceBinding="{Binding RuleActionConverterList}"
                    DataMemberBinding="{Binding RuleActionConverter}"
                    Header="{telerik:LocalizableResource Key=AutoSwitcherSettingsAction}"
                    IsReadOnly="False" />
                <telerik:GridViewDataColumn
                    Width="220"
                    DataMemberBinding="{Binding TextAllLayouts}"
                    Header="{telerik:LocalizableResource Key=AutoSwitcherSettingsAllLayouts}"
                    IsReadOnly="True" />
            </telerik:RadGridView.Columns>
        </telerik:RadGridView>
        <StackPanel
            Grid.Row="2"
            Margin="0,0,0,5"
            Orientation="Horizontal">
            <telerik:RadAutoSuggestBox
                Width="250"
                Margin="0,0,0,0"
                WatermarkContent="{telerik:LocalizableResource Key=SearchHelperText}"
                ClearButtonVisibility="Auto"
                Name="TextBoxSearch"
                TextChanged="TextBoxBase_OnTextChanged" />
            <CheckBox
                x:Name="CheckBoxIsShowAll"
                Margin="10,0,0,0"
                Checked="CheckBoxIsShowAll_OnChecked"
                FontSize="14"
                Unchecked="CheckBoxIsShowAll_OnChecked">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsListRulesHeaderShowAll}" />
            </CheckBox>
        </StackPanel>
    </Grid>
</UserControl>