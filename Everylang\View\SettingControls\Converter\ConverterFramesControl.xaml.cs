﻿using Everylang.App.SettingsApp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Converter
{
    /// <summary>
    /// Interaction logic for ConverterFramesControl.xaml
    /// </summary>
    internal partial class ConverterFramesControl
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(ConverterFramesControl));

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        private List<string> _framesList;
        private List<string> _framesOrigList;
        private string _sampleText;

        internal ConverterFramesControl()
        {
            InitializeComponent();
            _sampleText = LocalizationManager.GetString("ConverterSampleText");
            if (SettingsManager.Settings.ConverterFramesList.Contains('#') && SettingsManager.Settings.ConverterFramesList.Contains('|') && !SettingsManager.Settings.ConverterFramesList.Contains('¿'))
            {
                ConvertOldToNew();
            }
            var arr = SettingsManager.Settings.ConverterFramesList.Split(new[] { '¿' }, StringSplitOptions.RemoveEmptyEntries);
            _framesOrigList = new List<string>();
            _framesList = new List<string>();
            foreach (var s in arr)
            {
                var frameArr = s.Split('‽');
                if (frameArr.Length > 1)
                {
                    _framesList.Add(frameArr[0] + _sampleText + frameArr[1]);
                    _framesOrigList.Add(frameArr[0] + "‽" + frameArr[1]);
                }
            }
            ListBoxFrames.ItemsSource = _framesList;
        }

        private void ConvertOldToNew()
        {
            SettingsManager.Settings.ConverterFramesList = SettingsManager.Settings.ConverterFramesList.Replace("#", "¿").Replace("|", "‽");
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }

        private void AddClick(object sender, RoutedEventArgs e)
        {
            if (TextBoxFrom.Text != "" && TextBoxTo.Text != "")
            {
                _framesList.Add(TextBoxFrom.Text + _sampleText + TextBoxTo.Text);
                _framesOrigList.Add(TextBoxFrom.Text + "‽" + TextBoxTo.Text);
            }
            GenerateFrames();
            ListBoxFrames.ItemsSource = _framesList;
            ListBoxFrames.Items.Refresh();
        }

        private void DeleteClick(object sender, RoutedEventArgs e)
        {
            if (ListBoxFrames.SelectedIndex != -1)
            {
                _framesList.RemoveAt(ListBoxFrames.SelectedIndex);
                _framesOrigList.RemoveAt(ListBoxFrames.SelectedIndex);
            }
            GenerateFrames();
            ListBoxFrames.ItemsSource = _framesList;
            ListBoxFrames.Items.Refresh();
            TextBoxFrom.Text = "";
            TextBoxTo.Text = "";
        }

        private void GenerateFrames()
        {
            var converterFramesList = "";
            foreach (var s in _framesOrigList)
            {
                converterFramesList += "¿" + s.Split('‽')[0] + "‽" + s.Split('‽')[1];
            }
            SettingsManager.Settings.ConverterFramesList = converterFramesList;
        }


        private void UpClick(object sender, RoutedEventArgs e)
        {
            if (ListBoxFrames.SelectedIndex != -1 && ListBoxFrames.SelectedIndex > 0)
            {
                var item = _framesList[ListBoxFrames.SelectedIndex];
                var itemOrig = _framesOrigList[ListBoxFrames.SelectedIndex];
                _framesList.RemoveAt(ListBoxFrames.SelectedIndex);
                _framesOrigList.RemoveAt(ListBoxFrames.SelectedIndex);
                _framesList.Insert(ListBoxFrames.SelectedIndex - 1, item);
                _framesOrigList.Insert(ListBoxFrames.SelectedIndex - 1, itemOrig);
            }
            GenerateFrames();
            ListBoxFrames.ItemsSource = _framesList;
            ListBoxFrames.Items.Refresh();
        }

        private void DownClick(object sender, RoutedEventArgs e)
        {
            if (ListBoxFrames.SelectedIndex != -1 && ListBoxFrames.SelectedIndex < ListBoxFrames.Items.Count)
            {
                var item = _framesList[ListBoxFrames.SelectedIndex];
                var itemOrig = _framesOrigList[ListBoxFrames.SelectedIndex];
                _framesList.RemoveAt(ListBoxFrames.SelectedIndex);
                _framesOrigList.RemoveAt(ListBoxFrames.SelectedIndex);
                if (_framesList.Count == ListBoxFrames.SelectedIndex)
                {
                    _framesList.Insert(ListBoxFrames.SelectedIndex, item);
                    _framesOrigList.Insert(ListBoxFrames.SelectedIndex, itemOrig);
                }
                else
                {
                    _framesList.Insert(ListBoxFrames.SelectedIndex + 1, item);
                    _framesOrigList.Insert(ListBoxFrames.SelectedIndex + 1, itemOrig);
                }

            }
            GenerateFrames();
            ListBoxFrames.ItemsSource = _framesList;
            ListBoxFrames.Items.Refresh();
        }

        private void ListBoxFrames_OnSelected(object sender, RoutedEventArgs e)
        {
            try
            {

                var itemOrig = _framesOrigList[ListBoxFrames.SelectedIndex].Split('‽');
                TextBoxFrom.Text = itemOrig[0];
                TextBoxTo.Text = itemOrig[1];
            }
            catch
            {
                // ignored
            }
        }
    }
}
