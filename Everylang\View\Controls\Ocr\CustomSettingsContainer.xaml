﻿<UserControl x:Class="Everylang.App.View.Controls.Ocr.CustomSettingsContainer"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             mc:Ignorable="d"  Visibility="Collapsed" x:ClassModifier="internal">
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
        <ContentControl x:Name="contentControl" Margin="10">

        </ContentControl>
        <Button VerticalAlignment="Bottom" Content="{telerik:LocalizableResource Key=ButtonClose}" Focusable="False" Click="ButtonBase_OnClick"/>
    </Grid>

</UserControl>
