﻿<Popup x:Class="Everylang.App.View.Controls.SmartClick.SmallSmartClickWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
        mc:Ignorable="d" Placement="Absolute" AllowsTransparency="True" StaysOpen="True" MouseEnter="SmallSmartClickWindow_OnMouseEnter" MouseLeave="SmallSmartClickWindow_OnMouseLeave"
        x:ClassModifier="internal"
        DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}"
        >
    <Popup.Resources>
        <ResourceDictionary>
            <Style x:Key="ImageButton" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Focusable" Value="False"/>
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Padding" Value="3" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="MinHeight" Value="0" />
            </Style>
            <Style x:Key="ImageButtonCommon" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Padding" Value="1" />
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeCommon }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeCommon }"
                                                    Kind="Apps" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonCopy" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="ContentCopy" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonSpell" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="Spellcheck" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonPaste" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="ContentPaste" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonPasteUnf" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="FormatClear" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonTranslate" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="Translate" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonConvert" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Height="{Binding UniversalWindowSettingsViewModel.SmartClickMiniSizeAll }"
                                                    Kind="Cached" />
                    </Setter.Value>
                </Setter>
            </Style>


        </ResourceDictionary>
    </Popup.Resources>
    <Border x:Name="BorderMu" Opacity="0.5" CornerRadius="3"  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel Orientation="Horizontal" Margin="0">
            <telerik:RadButton ToolTip="SmartClick" x:Name="BCommon" MouseEnter="ButtonCommon" MouseLeave="BCommon_OnMouseLeave" Margin="0,0,0,0" Style="{StaticResource ImageButtonCommon}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniCopy}" Visibility="Collapsed" x:Name="BCopy" Click="ButtonCopy" Margin="0,0,0,0" Style="{StaticResource ImageButtonCopy}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniTranslate}" Visibility="Collapsed" x:Name="BTranslate" Click="ButtonTranslate" Margin="6,0,0,0" Style="{StaticResource ImageButtonTranslate}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniSpellCheck}" Visibility="Collapsed" x:Name="BSpellCheck" Click="ButtoSnpellCheck" Margin="6,0,0,0" Style="{StaticResource ImageButtonSpell}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniConverter}" Visibility="Collapsed" x:Name="BConvert" Click="ButtonConvert" Margin="6,0,0,0" Style="{StaticResource ImageButtonConvert}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniPaste}" Visibility="Collapsed" x:Name="BPaste" Click="ButtonPaste" Margin="6,0,0,0" Style="{StaticResource ImageButtonPaste}"/>
            <telerik:RadButton ToolTip="{telerik:LocalizableResource Key=UniPasteUnf}" Visibility="Collapsed" x:Name="BPasteUnf" Click="ButtonPasteUnf" Margin="6,0,0,0" Style="{StaticResource ImageButtonPasteUnf}"/>
            <telerik:Label Visibility="Collapsed" x:Name="TextBlockCount" FontSize="14" Margin="6,0,0,0"/>
        </StackPanel>
    </Border>
</Popup>
