﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using Everylang.Common.LogManager;
using RestSharp;
using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace Everylang.App.Utilities
{
    static class Updater
    {
        private static System.Timers.Timer _timer = null!;

        internal static void Start()
        {
            _timer = new(TimeSpan.FromHours(12).TotalMilliseconds);
            _timer.Elapsed += (_, _) => StartCheckUpdate();
            _timer.Start();
            StartCheckUpdate();
        }

        internal static void Stop()
        {
            try
            {
                if (_timer.Enabled) _timer.Stop();
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        private static void StartCheckUpdate()
        {
            if (SettingsManager.Settings.IsCheckUpdate || SettingsManager.Settings.IsCheckUpdateBeta)
            {
                CheckUpdateAsync();
            }
        }

        private static void CheckUpdateAsync()
        {
            Task.Run(() =>
            {
                Task.Delay(10000).Wait();

                if (SettingsManager.Settings.IsCheckUpdate)
                {
                    var result = CheckUpdate(false);
                    if (!string.IsNullOrEmpty(result))
                    {
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            (ThreadStart)delegate
                            {
                                Callback.GlobalEventsApp.OnEventUpdateAvailable(result);
                            });
                    }
                }
                if (SettingsManager.Settings.IsCheckUpdateBeta)
                {
                    var result = CheckUpdate(true);
                    if (!string.IsNullOrEmpty(result))
                    {
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            (ThreadStart)delegate
                            {
                                Callback.GlobalEventsApp.OnEventUpdateBetaAvailable(result);
                            });
                    }
                }
            });
        }

        private static string CheckUpdate(bool beta)
        {
            try
            {
                var uri = "http://everylang.net/version";
                if (beta)
                {
                    uri = "http://everylang.net/dist/beta/version";
                }
                Uri updateLocation = new Uri(uri);

                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy()
                });
                var request = new RestRequest(updateLocation);
                var response = client.Execute(request);

                if (response.ErrorException != null)
                {
                    throw response.ErrorException;
                }
                var versionFile = response.Content;

                Version? version = null;

                if (!string.IsNullOrEmpty(versionFile)) version = new Version(versionFile);

                if (version == null)
                {
                    throw new Exception("Failed to get update version");
                }

                if (version > Assembly.GetExecutingAssembly().GetName().Version)
                {
                    return version.ToString(3);
                }
                return "";
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return "";
        }
    }
}
