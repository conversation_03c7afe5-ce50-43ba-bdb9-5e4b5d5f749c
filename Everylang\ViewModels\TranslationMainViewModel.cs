﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.Translator;
using Everylang.App.Translator.NetRequest;
using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Net;
using Telerik.Windows.Controls;
using Language = Everylang.App.Translator.Language;
using Translation = Everylang.App.Translator.Translation;

namespace Everylang.App.ViewModels
{
    public class TranslationMainViewModel : ViewModelBase
    {
        internal bool WithError;

        internal RequestSettings RequestSettings;
        internal Action<WebResultTranslator>? ShowTranlatedText;
        public ObservableCollection<Language> LanguagesFrom { get; private set; }
        public ObservableCollection<Language>? LanguagesTo { get; private set; }
        public ObservableCollection<string> TranslateServices { get; private set; }

        internal DateTime LastTimeClear { get; set; }

        public DelegateCommand ListenTransalatedSourseCommand
        {
            get;
            private set;
        }

        public DelegateCommand ListenTransalatedResultCommand
        {
            get;
            private set;
        }

        public DelegateCommand TranslateCommand
        {
            get;
            private set;
        }

        public DelegateCommand CopyTransalatedCommand
        {
            get;
            private set;
        }

        public DelegateCommand SiteSourceCommand
        {
            get;
            private set;
        }

        public DelegateCommand SwapLangCommand
        {
            get;
            private set;
        }

        public DelegateCommand ClearCommand
        {
            get;
            private set;
        }

        private readonly Translation _translation;

        readonly System.Timers.Timer _timerTranslate;

        public TranslationMainViewModel()
        {
            _timerTranslate = new System.Timers.Timer(1000);
            _timerTranslate.Elapsed += TimerTranslate_Elapsed;
            RequestSettings = new RequestSettings();
            RequestSettings.GetCurrentTranslateServiceLangs();
            _translation = new Translation();
            ClearCommand = new DelegateCommand(CommandClearText);
            SwapLangCommand = new DelegateCommand(CommandSwapLang);
            ListenTransalatedSourseCommand = new DelegateCommand(CommandListenTransalatedFromText, (_) => !WithError);
            ListenTransalatedResultCommand = new DelegateCommand(CommandListenTransalatedToText, (_) => !WithError);
            CopyTransalatedCommand = new DelegateCommand(CommandCopyTransalated, (_) => !WithError);
            SiteSourceCommand = new DelegateCommand(CommandSiteSource);
            TranslateCommand = new DelegateCommand(CommandTranslate);

            TranslateServices = new ObservableCollection<string>(TranslateCommonSettings.TranslateServices);
            LanguagesFrom = new ObservableCollection<Language>(RequestSettings.ListLangs);
            LanguageFromCurrent = TranslateCommonSettings.LanguageFromDefault(RequestSettings.ListLangs);
            _translation.CallBackTranslate += TranslationSuccess;
            GlobalEventsApp.EventOnlyFavoriteLangForTranslate += EventOnlyFavoriteLangForTranslate;

        }

        private void EventOnlyFavoriteLangForTranslate()
        {
            CurrentTranslateService = CurrentTranslateService;
        }

        private void TimerTranslate_Elapsed(object? sender, System.Timers.ElapsedEventArgs e)
        {
            _timerTranslate.Stop();
            if (!string.IsNullOrEmpty(SourceText.Trim()) && !RequestSettings.IsNowTranslating)
            {
                RequestSettings.IsNowTranslating = true;
                _translation.GetTranslation(RequestSettings);
            }
        }

        private void CommandSiteSource(object o)
        {

            var text = WebUtility.UrlEncode(SourceText);
            if (RequestSettings.CurrentTranslateServiceIndex == 0)
            {
                StartProcess("https://translate.google.com/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 1)
            {
                StartProcess("https://www.bing.com/translator/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 2)
            {
                StartProcess("https://translate.yandex.ru/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 3)
            {
                StartProcess("https://www.deepl.com/translator#" + text);
            }

        }

        private void StartProcess(string data)
        {
            try
            {
                Process.Start(data);
            }
            catch
            {
                // ignored
            }
        }

        internal void CommandTranslate(object? o)
        {
            _timerTranslate.Stop();
            if (!string.IsNullOrEmpty(SourceText.Trim()) && !RequestSettings.IsNowTranslating)
            {
                RequestSettings.IsNowTranslating = true;
                IsVisibleProgress = true;
                _translation.GetTranslation(RequestSettings);
            }
        }

        private void CommandSwapLang(object o)
        {
            var langFrom = LanguageFromCurrent;
            var langTo = LanguageToCurrent;
            if (LanguageFromCurrent?.Abbreviation == "auto")
            {
                if (LanguagesTo != null)
                    langFrom = LanguagesTo.FirstOrDefault(x =>
                        x.Abbreviation == SettingsManager.Settings.TranslateLangFrom);
            }
            LanguageFromCurrent = langTo;
            LanguageToCurrent = langFrom;

            base.OnPropertyChanged(nameof(LanguageFromCurrent));
            GetLanguagesTo();
            CommandTranslate(null);
        }

        internal void CommandClearText(object o)
        {

            TranslatedText = "";
            SourceText = "";
            if ((DateTime.Now - LastTimeClear).Minutes > 10)
            {
                LastTimeClear = DateTime.Now;
                RequestSettings.LanguageFromCurrent = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == "auto");
                RequestSettings.LanguageToCurrent = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo);
                if (RequestSettings.LanguageToCurrent == null)
                {
                    RequestSettings.LanguageToCurrent = RequestSettings.ListLangs.First();
                }
            }

            IsVisibleResultPanel = false;
            base.OnPropertyChanged(nameof(LanguageFromCurrent));
            base.OnPropertyChanged(nameof(LanguageToCurrent));
            base.OnPropertyChanged(nameof(TranslatedText));
            base.OnPropertyChanged(nameof(SourceText));
        }

        private void CommandCopyTransalated(object o)
        {
            try
            {
                ClipboardOperations.SetText(TranslatedTextWithNonChar);
            }
            catch
            {
                // ignored
            }
        }

        private void CommandListenTransalatedFromText(object o)
        {
            try
            {
                _translation.GetListening(SourceText.Trim(), _fromLanguageAbbreviation);
            }
            catch
            {
                // ignored
            }
        }

        private void CommandListenTransalatedToText(object o)
        {
            try
            {
                if (TranslatedTextWithNonChar != null)
                    _translation.GetListening(TranslatedTextWithNonChar.Trim(),
                        RequestSettings.LanguageToCurrent?.Abbreviation);
            }
            catch
            {
                // ignored
            }
        }

        public string? CurrentTranslateService
        {
            get { return TranslateCommonSettings.TranslateServices[RequestSettings.CurrentTranslateServiceIndex]; }
            set
            {
                if (value == null)
                {
                    return;
                }
                var languageName = RequestSettings.LanguageFromCurrent?.Name;
                RequestSettings.CurrentTranslateServiceIndex = Array.IndexOf(TranslateCommonSettings.TranslateServices, value);
                RequestSettings.GetCurrentTranslateServiceLangs();
                LanguagesFrom = new ObservableCollection<Language>(RequestSettings.ListLangs);
                var language = RequestSettings.ListLangs.FirstOrDefault(x => x.Name != null && x.Name.Equals(languageName));
                if (language == null)
                {
                    RequestSettings.LanguageFromCurrent = TranslateCommonSettings.LanguageFromDefault(RequestSettings.ListLangs);
                }
                else
                {
                    RequestSettings.LanguageFromCurrent = language;
                }
                base.OnPropertyChanged(nameof(LanguagesFrom));
                base.OnPropertyChanged(nameof(CurrentTranslateService));
                base.OnPropertyChanged(nameof(LanguageFromCurrent));
                GetLanguagesTo();
                CommandTranslate(null);
            }
        }

        private bool _isVisibleResultPanel;

        public bool IsVisibleResultPanel
        {
            get { return _isVisibleResultPanel; }
            set
            {
                _isVisibleResultPanel = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isStayOnTop;

        internal bool IsStayOnTop
        {
            get { return _isStayOnTop; }
            set
            {
                _isStayOnTop = value;
                base.OnPropertyChanged();
            }
        }

        public Language? LanguageFromCurrent
        {
            get
            {
                return RequestSettings.LanguageFromCurrent;
            }
            set
            {
                if (value == null) return;
                RequestSettings.LanguageFromCurrent = value;
                base.OnPropertyChanged();
                GetLanguagesTo();
                CommandTranslate(null);
            }
        }

        public Language? LanguageToCurrent
        {
            get
            {
                return RequestSettings.LanguageToCurrent;
            }
            set
            {
                if (value == null) return;
                RequestSettings.LanguageToCurrent = value;
                base.OnPropertyChanged();
                CommandTranslate(null);
            }
        }

        private void GetLanguagesTo()
        {
            LanguagesTo = new ObservableCollection<Language>(LanguagesFrom);
            LanguagesTo.Remove(LanguagesTo.FirstOrDefault(x => x.Abbreviation == "auto")!);
            if (LanguageToCurrent != null && !LanguagesTo.Contains(LanguageToCurrent))
            {
                LanguageToCurrent = LanguagesTo.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo);
                if (LanguageToCurrent == null || !LanguagesTo.Contains(LanguageToCurrent))
                {
                    LanguageToCurrent = LanguagesTo.First();
                }
            }
            base.OnPropertyChanged(nameof(LanguagesTo));
        }

        private string? _fromLanguageAbbreviation;

        private string? _fromLang;

        public string? FromLang
        {
            get
            {
                if (!string.IsNullOrEmpty(_fromLang)) return LocalizationManager.GetString("FromLang") + " " + _fromLang;
                return "";
            }
            set
            {
                _fromLang = value;
                base.OnPropertyChanged();
            }
        }

        private string? _toLang;

        public string? ToLang
        {
            get
            {
                if (!string.IsNullOrEmpty(_toLang)) return LocalizationManager.GetString("ToLang") + " " + _toLang;
                return "";
            }
            set
            {
                _toLang = value;
                base.OnPropertyChanged();
            }
        }

        public string? TranslatedText
        {
            get { return RequestSettings.TranslatedText; }
            set
            {
                RequestSettings.TranslatedText = value;
                base.OnPropertyChanged();
            }
        }

        internal string? TranslatedTextWithNonChar
        {
            get { return RequestSettings.TranslatedTextWithNonChar; }
        }

        public string SourceText
        {
            get
            {
                return RequestSettings.SourceText;
            }
            set
            {
                _timerTranslate.Stop();
                if (!string.IsNullOrEmpty(value))
                {
                    _timerTranslate.Start();
                }
                RequestSettings.SourceText = value;
                IsVisibleResultPanel = false;
                base.OnPropertyChanged();
            }
        }

        internal string? HistText
        {
            set
            {
                if (value == null)
                {
                    return;
                }
                RequestSettings.SourceText = value;
                base.OnPropertyChanged(nameof(SourceText));
                CommandTranslate(null);
            }
        }

        private bool _isVisibleProgress;

        public bool IsVisibleProgress
        {
            get { return _isVisibleProgress; }
            set
            {
                _isVisibleProgress = value;
                base.OnPropertyChanged();

            }
        }

        internal bool IsTranslationComplete
        {
            get { return !_isVisibleProgress && !WithError; }

        }

        public string FontFam
        {
            get
            {
                return SettingsManager.Settings.TranslateFontFamily;
            }
        }

        public string FontSize
        {
            get
            {
                return SettingsManager.Settings.TranslateFontSize;
            }
        }

        private void TranslationSuccess(WebResultTranslator webResultTranslator)
        {
            IsVisibleProgress = false;
            if (!webResultTranslator.WithError && !string.IsNullOrEmpty(webResultTranslator.ResultText))
            {

                WithError = false;
                RequestSettings.TranslatedText = webResultTranslator.ResultText;
                RequestSettings.TranslatedTextWithNonChar = webResultTranslator.ResultTextWithNonChar;
                RequestSettings.TranslatedTextLatin = webResultTranslator.LatinText;
                Language? langFrom = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == webResultTranslator.FromLang);
                if (langFrom != null)
                {
                    if (RequestSettings.LanguageFromCurrent != LanguagesFrom.First())
                    {
                        RequestSettings.LanguageFromCurrent = langFrom;
                        OnPropertyChanged(nameof(LanguageFromCurrent));
                    }

                    FromLang = langFrom.Name;
                    _fromLanguageAbbreviation = langFrom.Abbreviation;
                }
                Language? langTo = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == webResultTranslator.ToLang);
                if (langTo != null)
                {
                    RequestSettings.LanguageToCurrent = langTo;
                    OnPropertyChanged(nameof(LanguageToCurrent));
                    ToLang = langTo.Name;
                }
                OnPropertyChanged(nameof(TranslatedText));
                IsVisibleResultPanel = true;
                base.OnPropertyChanged(nameof(IsTranslationComplete));
            }
            else
            {
                WithError = true;
                RequestSettings.TranslatedText = LocalizationManager.GetString("TranslateError") + Environment.NewLine + webResultTranslator.ErrorText;
                IsVisibleResultPanel = false;
                OnPropertyChanged(nameof(TranslatedText));
                if (ShowTranlatedText != null) ShowTranlatedText(webResultTranslator);
            }
            RequestSettings.IsNowTranslating = false;
        }

    }
}
