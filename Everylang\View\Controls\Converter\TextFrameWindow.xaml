﻿<Popup x:Class="Everylang.App.View.Controls.Converter.TextFrameWindow"
                      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                      xmlns:helpers1="clr-namespace:Everylang.App.View.Helpers"
                      x:Name="me" PopupAnimation="Slide" AllowsTransparency="True"
                      Placement="Mouse" StaysOpen="True" Focusable="false" MinHeight="100" MinWidth="150"
                      x:ClassModifier="internal">
    <Popup.Resources>
        <ResourceDictionary>
            <helpers1:IndexConverterForFastAction x:Key="IndexConverterMu"></helpers1:IndexConverterForFastAction>
        </ResourceDictionary>

    </Popup.Resources>

    <Grid Background="Transparent">
        <Border BorderThickness="2" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" CornerRadius="4"
                Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
            <ListBox Name="lvFastAction"
                     ScrollViewer.HorizontalScrollBarVisibility="Disabled" SelectionMode="Extended"
                     PreviewMouseDoubleClick="LvFastAction_OnPreviewMouseDoubleClick">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="15" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="{Binding Index, Converter={StaticResource IndexConverterMu}}"
                                       FontSize="10"
                                       VerticalAlignment="Center" HorizontalAlignment="Left"
                                       ToolTip="{Binding Path=FastActionIndex, ElementName=me, Mode=Default}" />
                                <TextBlock Grid.Column="1" FontSize="12" Text="{Binding Text}" 
                                           HorizontalAlignment="Stretch" VerticalAlignment="Center" 
                                           Cursor="IBeam" 
                                           Background="Transparent" TextWrapping="Wrap">
                                    <TextBlock.ToolTip> 
                                         <StackPanel> 
                                             <TextBlock Text="{Binding FullText}" /> 
                                         </StackPanel> 
                                     </TextBlock.ToolTip> 
                                 </TextBlock> 
                            </Grid>
                            <Separator Opacity="0.3" />
                        </StackPanel>

                    </DataTemplate>
                </ListBox.ItemTemplate>
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource {x:Type ListBoxItem}}">
                        <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListBoxItem_MouseLeftButtonDown" />
                    </Style>
                </ListBox.ItemContainerStyle>
            </ListBox>
        </Border>
    </Grid>
</Popup>
