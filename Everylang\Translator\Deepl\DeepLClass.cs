﻿using System.Collections.Generic;

namespace Everylang.App.Translator.Deepl
{
    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class Beam
    {
        internal string? postprocessed_sentence { get; set; }
        internal int num_symbols { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class Translation
    {
        internal List<Beam> beams { get; set; }
        internal string quality { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class DetectedLanguages
    {
        internal double EN { get; set; }
        internal double DE { get; set; }
        internal double FR { get; set; }
        internal double ES { get; set; }
        internal double PT { get; set; }
        internal double IT { get; set; }
        internal double NL { get; set; }
        internal double PL { get; set; }
        internal double RU { get; set; }
        internal double ZH { get; set; }
        internal double JA { get; set; }
        internal double BG { get; set; }
        internal double CS { get; set; }
        internal double DA { get; set; }
        internal double EL { get; set; }
        internal double ET { get; set; }
        internal double FI { get; set; }
        internal double HU { get; set; }
        internal double LT { get; set; }
        internal double LV { get; set; }
        internal double RO { get; set; }
        internal double SK { get; set; }
        internal double SL { get; set; }
        internal double SV { get; set; }
        internal double unsupported { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class Result
    {
        internal List<Translation> translations { get; set; }
        internal string target_lang { get; set; }
        internal string source_lang { get; set; }
        internal bool source_lang_is_confident { get; set; }
        internal DetectedLanguages detectedLanguages { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class RootResponse
    {
        internal string jsonrpc { get; set; }
        internal int id { get; set; }
        internal Result result { get; set; }
    }

    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class Job
    {
        internal string kind { get; set; }
        internal string? raw_en_sentence { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true)]
    internal class Lang
    {
        internal List<string> user_preferred_langs { get; set; }
        internal string? source_lang_user_selected { get; set; }
        internal string? target_lang { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class Params
    {
        internal List<Job> jobs { get; set; }
        internal Lang lang { get; set; }
        internal int priority { get; set; }
        internal long timestamp { get; set; }
    }
    [System.Reflection.ObfuscationAttribute(Exclude = true, ApplyToMembers = true)]
    internal class RootRequest
    {
        internal int id { get; set; }
        internal string jsonrpc { get; set; }
        internal string method { get; set; }
        internal Params @params { get; set; }

    }


    internal class DeeplError
    {
        internal int code { get; set; }
        internal string message { get; set; }
    }

    internal class DeeplRoot
    {
        internal string jsonrpc { get; set; }
        internal DeeplError error { get; set; }
    }
}
