﻿/*************************************************************************************

   Extended WPF Toolkit

   Copyright (C) 2007-2013 Xceed Software Inc.

   This program is provided to you under the terms of the Microsoft Public
   License (Ms-PL) as published at http://wpftoolkit.codeplex.com/license 

   For more features, controls, and fast professional support,
   pick up the Plus Edition at http://xceed.com/wpf_toolkit

   Stay informed: follow @datagrid on Twitter or Like http://facebook.com/datagrids

  ***********************************************************************************/

using System.Windows.Documents;

namespace Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters
{
    /// <summary>
    /// Formats the RichTextBox text as plain text
    /// </summary>
    internal class PlainTextFormatter : ITextFormatter
    {
        string? ITextFormatter.GetText(FlowDocument document)
        {
            return new TextRange(document.ContentStart, document.ContentEnd).Text;
        }

        void ITextFormatter.SetText(FlowDocument document, string? text)
        {
            new TextRange(document.ContentStart, document.ContentEnd).Text = text;
        }
    }
}
