﻿using Everylang.App.Translator.NetRequest;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace Everylang.App.Translator.Google;

/// <summary>
/// Represents a translator for the new Google Translate RPC API.
/// </summary>
internal class GoogleTranslator3 : IDisposable
{
    private const string TranslateRpcId = "MkEWBc";
    private static readonly Uri DefaultBaseAddress = new("https://translate.google.com/");
    private const int MaxTextLength = 5000;

    private RequestSettings _requestSettings = null!;
    private string? _languageFromCurrent;
    private string? _languageToCurrent;
    private string? _latinText;
    private bool _isSecond;

    private HttpClient _httpClient;

    internal async Task<WebResultTranslator?> Translate(RequestSettings requestSettings)
    {
        _requestSettings = requestSettings;
        _httpClient = new HttpClient();
        _httpClient.BaseAddress ??= DefaultBaseAddress;
        return await TranslateAsync(false);
    }

    private async Task<WebResultTranslator?> TranslateAsync(bool second)
    {
        try
        {
            WebResultTranslator? webResult = new WebResultTranslator();

            _isSecond = second;
            if (!second)
            {
                _languageFromCurrent = _requestSettings.LanguageFromCurrent?.Abbreviation;
                _languageToCurrent = _requestSettings.LanguageToCurrent?.Abbreviation;
            }

            string sourceLanguage = string.IsNullOrEmpty(_languageFromCurrent) ? "auto" : _languageFromCurrent;

            object[] payload = [new object[] { _requestSettings.SourceTextTrimmed, sourceLanguage, _languageToCurrent, 1 }, Array.Empty<object>()];
            using var request = BuildRequest(TranslateRpcId, payload);
            using var document = await SendAndParseResponseAsync(request);

            var root = document.RootElement;

            string target = root[1][1].GetString() ?? _languageToCurrent;
            string source = root[1][3].GetString()!;

            if (source == "auto")
            {
                source = root.GetArrayLength() > 2
                    ? root[2].GetString()!
                    : "en"; // Source language is not present, this happens when the text is a hyperlink and fromLanguage is null
            }

            string translation;
            if (root[1][0][0].GetArrayLength() > 5 && root[1][0][0][5].ValueKind == JsonValueKind.Array)
            {
                translation = string.Join(" ", root[1][0][0][5].EnumerateArray().Select(x => x[0].GetString()));
            }
            else
            {
                // no chunks found, could be a link or gender-specific translation
                // should we provide the value of the link and the gender-specific translations in separate properties?
                translation = root[1][0][0][0].GetString()!;
            }

            string? targetTransliteration = root[1][0][0][1].GetString();
            string? sourceTransliteration = root[0].ValueKind == JsonValueKind.Array ? root[0][0].GetString() : null;

            //RestClient restClient = new RestClient(new RestClientOptions()
            //{
            //    Proxy = NetLib.GetProxy()
            //});

            //string sourceLanguage = string.IsNullOrEmpty(_languageFromCurrent) ? "auto" : _languageFromCurrent;
            //string url = $"{ApiEndpoint}?client=gtx&sl={sourceLanguage}&tl={_languageToCurrent}&dt=t&dt=bd&hl={CultureInfo.CurrentCulture.TwoLetterISOLanguageName.ToLower()}&dj=1&source=input&tk={MakeToken(_requestSettings.SourceTextTrimmed.AsSpan())}";
            //var req = new RestRequest(new Uri(url), Method.Post);
            //req.AddParameter("application/x-www-form-urlencoded", "q=" + WebUtility.UrlEncode(_requestSettings.SourceTextTrimmed), ParameterType.RequestBody);
            //var result = restClient.Execute(req);

            //if (result.IsSuccessful && result.Content?.Length > 0 && result.Content.Contains("\"trans\""))
            //{
            //    webResult = ProcessingOfTheRequest(result.Content);
            //    webResult.LatinText = _latinText;
            //}
            //else
            //{
            //    webResult.WithError = true;
            //    webResult.ResultText = result.ErrorMessage;
            //    webResult.ResultTextWithNonChar = result.Content;
            //}

            //webResult.FromLang = _languageFromCurrent;
            //webResult.ToLang = _languageToCurrent;
            return webResult;
        }
        catch (Exception e)
        {
            return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
        }
    }

    private static HttpRequestMessage BuildRequest(string rpcId, object?[] payload)
    {
        var serializedPayload = JsonSerializer.Serialize(payload);
        object?[][][] request = [[[rpcId, serializedPayload, null, "generic"]]];

        return new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"_/TranslateWebserverUi/data/batchexecute?rpcids={rpcId}", UriKind.Relative),
            Content = new FormUrlEncodedContent([new KeyValuePair<string, string>("f.req", JsonSerializer.Serialize(request))])
        };
    }

    private async Task<JsonDocument> SendAndParseResponseAsync(HttpRequestMessage request)
    {
        using var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
        using var document = await GetJsonDocumentAsync(response).ConfigureAwait(false);

        // get the actual data
        string data = document.RootElement[0][2].GetString() ?? throw new Exception("Unable to get the data from the response.");
        return JsonDocument.Parse(data);
    }

    private static async Task<JsonDocument> GetJsonDocumentAsync(HttpResponseMessage response)
    {
        using var stream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);

        // skip magic chars
        if (stream.CanSeek)
        {
            stream.Seek(6, SeekOrigin.Begin);
            return await JsonDocument.ParseAsync(stream).ConfigureAwait(false);
        }

        byte[] bytes = await response.Content.ReadAsByteArrayAsync().ConfigureAwait(false);
        return JsonDocument.Parse(bytes.AsMemory(6, bytes.Length - 6));
    }

    public void Dispose()
    {
        _httpClient.Dispose();
    }
}