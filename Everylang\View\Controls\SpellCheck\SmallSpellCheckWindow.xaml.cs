﻿using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SpellCheck;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading;
using System.Windows;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Application = System.Windows.Application;
using Timer = System.Timers.Timer;

namespace Everylang.App.View.Controls.SpellCheck
{
    /// <summary>
    /// Interaction logic for SmallSpellCheckWindow.xaml
    /// </summary>
    internal partial class SmallSpellCheckWindow
    {
        private readonly Timer _timer;
        internal SmallSpellCheckWindow()
        {
            InitializeComponent();
            //GlobalEventsApp.EventSpellCheck += SpellCheckSuccess;
            _timer = new Timer();
            _timer.Interval = 3000;
            _timer.Elapsed += (_, _) =>
            {
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)CloseThis);
            };
            TrueWordItems = new ObservableCollection<string>();
            IsVisibleProgressRing = true;
            IsVisibleGrid = false;
            Error = false;
            ThumbMy.DragDelta += OnDragDelta;
            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            HookCallBackKeyDown.CallbackEventHandler += KeyDown;
        }

        private int _currentSelected;

        private void KeyDown(GlobalKeyEventArgs e)
        {
            e.Handled = true;
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                CloseThis();
            }
            if (e.KeyCode == VirtualKeycodes.Enter)
            {
                Replace(null);
            }
            if (e.KeyCode == VirtualKeycodes.C && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy(null);
            }
            if (e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {

                if (lvFastAction.SelectedIndex == -1)
                {
                    _currentSelected = 0;
                    lvFastAction.SelectedIndex = 0;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.UpArrow)
                {
                    if (_currentSelected == 0)
                    {
                        _currentSelected = lvFastAction.Items.Count - 1;
                    }
                    else
                    {
                        _currentSelected -= 1;
                    }
                    if (_currentSelected < -1)
                    {
                        _currentSelected = -1;
                    }
                    lvFastAction.SelectedIndex = _currentSelected;
                }
                if (e.KeyCode == VirtualKeycodes.DownArrow)
                {
                    if (_currentSelected == lvFastAction.Items.Count - 1)
                    {
                        _currentSelected = 0;
                    }
                    else
                    {
                        _currentSelected += 1;
                    }
                    if (_currentSelected < -1)
                    {
                        _currentSelected = -1;
                    }
                    lvFastAction.SelectedIndex = _currentSelected;
                }
                lvFastAction.UpdateLayout();
                if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
                    lvFastAction.ScrollIntoView(lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1]);
                lvFastAction.UpdateLayout();

            }
        }

        private void MouseOverHide(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (!IsMouseOver && Mouse.DirectlyOver == null)
            {
                CloseThis();
            }
        }

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {
            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;
        }

        private void Replace(object? o)
        {
            if (lvFastAction.SelectedItems != null)
            {
                CloseThis();
                string? replacedText = lvFastAction.SelectedItems[0]?.ToString();
                Utilities.SendText.SendStringByPaste(replacedText, false);
            }
        }

        private void Copy(object? o)
        {
            if (lvFastAction.SelectedItems != null)
            {
                string replacedText = lvFastAction.SelectedItems[0]?.ToString();
                ClipboardOperations.SetText(replacedText);
                CloseThis();
            }
        }

        private void SpellCheckSuccess(List<string> webResult)
        {
            IsVisibleProgressRing = false;
            TrueWordItems.Clear();
            IsNoOptions = false;
            IsOk = webResult.Count == 0;
            IsVisibleGrid = !IsOk;

            SpellCheckDictionary = new List<SpellCheckDictionary>() { new SpellCheckDictionary() { Suggestions = webResult } };
            PasteText = LocalizationManager.GetString("bReplaceText");
            bool isTrueWordAdded = false;
            if (SpellCheckDictionary.Count > 0)
            {
                foreach (var suggestion in SpellCheckDictionary[0].Suggestions)
                {
                    TrueWordItems.Add(suggestion);
                    isTrueWordAdded = true;
                }
                lvFastAction.ItemsSource = TrueWordItems;
            }
            if (!isTrueWordAdded && !IsOk)
            {
                IsVisibleGrid = false;
                IsNoOptions = true;
                _timer.Start();
                return;
            }

            if (IsOk)
            {
                if (SettingsManager.Settings.SpellCheckCloseByTimer)
                {
                    _timer.Start();
                }
            }
            if (lvFastAction.Items.Count > 0)
            {
                lvFastAction.SelectedIndex = 0;
                lvFastAction.UpdateLayout();
                if (lvFastAction.ActualHeight < 95)
                {
                    lvFastAction.Height = 95;
                }
                else
                {
                    if (lvFastAction.ActualHeight > 225)
                    {
                        lvFastAction.Height = 225;
                    }
                    else
                    {
                        Height += lvFastAction.ActualHeight - lvFastAction.Height;
                    }
                }
                lvFastAction.UpdateLayout();
                lvFastAction.IsScrollIntoViewEnabled = true;
            }


        }

        private void CloseThis()
        {
            _timer.Stop();
            HookCallBackMouseDown.CallbackEventHandler -= MouseOverHide;
            HookCallBackKeyDown.CallbackEventHandler -= KeyDown;
            IsOpen = false;
        }

        private void lvFastAction_DoubleClick(object sender, MouseButtonEventArgs e)
        {
            Replace(null);
        }

        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            CloseThis();
        }

        internal void Show(string? text)
        {
            IsOpen = true;
            UpdateLayout();
            if (text != null)
            {
                SpellCheckSuccess(SpellCheckWhileTyping.CheckWordForSmallSpellCheck(text));
            }
            //var asd = SpellCheckWhileTyping.CheckWordForSmallSpellCheck(text);

            //LanguageToolSpellCheck orfoSpellCheck = new LanguageToolSpellCheck();
            //if (text != null) SpellCheckSuccess(orfoSpellCheck.Start(text).GetAwaiter().GetResult());
        }

        // --------------------------------------------------------------------------------------------

        internal static readonly DependencyProperty PasteTextProperty =
            DependencyProperty.Register("PasteText",
                typeof(string),
                typeof(SmallSpellCheckWindow),
                new FrameworkPropertyMetadata(""));

        internal string PasteText
        {
            get { return (string)GetValue(PasteTextProperty); }
            set { SetValue(PasteTextProperty, value); }
        }

        // ----------------

        internal static readonly DependencyProperty IsFromMainWindowProperty =
            DependencyProperty.Register("IsFromMainWindow",
                typeof(bool),
                typeof(SmallSpellCheckWindow));

        internal static readonly DependencyProperty IsOkProperty =
            DependencyProperty.Register("IsOk",
                typeof(bool),
                typeof(SmallSpellCheckWindow));

        internal static readonly DependencyProperty IsNoOptionsProperty =
            DependencyProperty.Register("IsNoOptions",
                typeof(bool),
                typeof(SmallSpellCheckWindow));

        internal static readonly DependencyProperty IsVisibleProgressRingProperty =
            DependencyProperty.Register("IsVisibleProgressRing",
                typeof(bool),
                typeof(SmallSpellCheckWindow));

        internal static readonly DependencyProperty IsVisibleGridProperty =
            DependencyProperty.Register("IsVisibleGrid",
                typeof(bool),
                typeof(SmallSpellCheckWindow));

        internal static readonly DependencyProperty ErrorProperty =
            DependencyProperty.Register("Error",
                typeof(bool),
                typeof(SmallSpellCheckWindow));


        internal bool IsFromMainWindow
        {
            get { return (bool)GetValue(IsFromMainWindowProperty); }
            set { SetValue(IsFromMainWindowProperty, value); OnPropertyChanged(nameof(IsFromMainWindow)); }
        }

        internal bool IsOk
        {
            get { return (bool)GetValue(IsOkProperty); }
            set { SetValue(IsOkProperty, value); OnPropertyChanged(nameof(IsOk)); }
        }

        internal bool IsNoOptions
        {
            get { return (bool)GetValue(IsNoOptionsProperty); }
            set { SetValue(IsNoOptionsProperty, value); OnPropertyChanged(nameof(IsNoOptions)); }
        }

        internal bool IsVisibleProgressRing
        {
            get { return (bool)GetValue(IsVisibleProgressRingProperty); }
            set { SetValue(IsVisibleProgressRingProperty, value); OnPropertyChanged(nameof(IsVisibleProgressRing)); }
        }

        internal bool IsVisibleGrid
        {
            get { return (bool)GetValue(IsVisibleGridProperty); }
            set { SetValue(IsVisibleGridProperty, value); OnPropertyChanged(nameof(IsVisibleGrid)); }
        }

        internal bool Error
        {
            get { return (bool)GetValue(ErrorProperty); }
            set { SetValue(ErrorProperty, value); OnPropertyChanged(nameof(Error)); }
        }

        public ObservableCollection<string> TrueWordItems { get; set; }
        internal List<SpellCheckDictionary> SpellCheckDictionary;

        internal event PropertyChangedEventHandler? PropertyChanged;
        private void OnPropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
}
