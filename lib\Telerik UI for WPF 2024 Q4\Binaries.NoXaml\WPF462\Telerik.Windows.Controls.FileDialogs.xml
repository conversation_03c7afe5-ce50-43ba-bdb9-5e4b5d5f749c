<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.FileDialogs</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer">
            <summary>
            The AutomationPeer associated with the <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer.#ctor(Telerik.Windows.Controls.DialogWindowBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer"/> class.
            </summary>
            <param name="dialog">The owner <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> instance.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DialogWindowBaseAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarAutomationPeer">
            <summary>
            UI Automation Peer class for FileDialogsBreadcrumbBar.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarAutomationPeer.#ctor(Telerik.Windows.Controls.Breadcrumb.RadBreadcrumbBar)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarAutomationPeer" /> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarAutomationPeer.CreateItemAutomationPeer(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarDataItemAutomationPeer">
            <summary>
            UI Automation Peer class for FileDialogsBreadcrumbBarDataItem.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarDataItemAutomationPeer.#ctor(System.Object,System.Windows.Automation.Peers.ItemsControlAutomationPeer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarDataItemAutomationPeer" /> class.
            </summary>
            <param name="owner">The owner.</param>
            <param name="parent">The parent.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarDataItemAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FileDialogsBreadcrumbBarDataItemAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer">
            <summary>
            The AutomationPeer associated with the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer.#ctor(Telerik.Windows.Controls.RadOpenFileDialog)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer"/> class.
            </summary>
            <param name="dialog">The owner <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/> instance.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFileDialogAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer">
            <summary>
            The AutomationPeer associated with the <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer.#ctor(Telerik.Windows.Controls.RadOpenFolderDialog)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer"/> class.
            </summary>
            <param name="dialog">The owner <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> instance.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadOpenFolderDialogAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer">
            <summary>
            The AutomationPeer associated with the <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer.#ctor(Telerik.Windows.Controls.RadSaveFileDialog)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer"/> class.
            </summary>
            <param name="dialog">The owner <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> instance.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSaveFileDialogAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer">
            <summary>
            Automation peer class for <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.#ctor(Telerik.Windows.Controls.RadFilePathPicker)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.System#Windows#Automation#Provider#IValueProvider#Value">
            <summary>
            Returns string representing the value of the RadFilePathPicker control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.System#Windows#Automation#Provider#IValueProvider#IsReadOnly">
            <summary>
            Returns whether the FilePicker control is read-only.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.SetValue(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetChildrenCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadFilePathPickerAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.Helpers.PathHelper">
            <summary>
            Class responsible for validating and expanding environment variables.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.Helpers.PathHelper.CapitalizeNetworkPath(System.String)">
            <summary>
             Converts '\\NetworkPC\users' => '\\NetworkPC\Users'.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk">
            <summary>
            Represents the displayable information for a single USB disk.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk"/> class.
            </summary>
            <param name="name">The Windows drive letter assigned to this device.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.FreeSpace">
            <summary>
            Gets the available free space on the disk, specified in bytes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.Model">
            <summary>
            Get the model of this disk.  This is the manufacturer's name.
            </summary>
            <remarks>
            When this class is used to identify a removed USB device, the Model
            property is set to String.Empty.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.Name">
            <summary>
            Gets the name of this disk.  This is the Windows identifier, drive letter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.Size">
            <summary>
            Gets the total size of the disk, specified in bytes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.Volume">
            <summary>
            Get the volume name of this disk.  This is the friendly name ("Stick").
            </summary>
            <remarks>
            When this class is used to identify a removed USB device, the Volume
            property is set to String.Empty.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk.ToString">
            <summary>
            Pretty print the disk.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbManager">
            <summary>
            Discovers USB disk devices and monitors for device state changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbManager.#ctor(System.Windows.Window)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbManager"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChange">
            <summary>
            Specifies the various state changes for USB disk devices.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChange.Added">
            <summary>
            A device has been added and is now available.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChange.Removing">
            <summary>
            A device is about to be removed;
            allows consumers to intercept and deny the action.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChange.Removed">
            <summary>
            A device has been removed and is no longer available.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventHandler">
            <summary>
            Defines the signature of an event handler method for internally handling USB device state changes.
            </summary>
            <param name="e">A description of the device state change.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventArgs">
            <summary>
            Define the arguments passed internally for the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventHandler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventArgs.#ctor(Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChange,Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbDisk)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventArgs"/> class.
            </summary>
            <param name="state">The state change code.</param>
            <param name="disk">The USB disk description.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventArgs.Disk">
            <summary>
            Gets the USB disk information.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogs.Helpers.UsbStateChangedEventArgs.State">
            <summary>
            Gets the state change code.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.RenamingEventArgs">
            <summary>
            Event args for the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.Renaming"/> and <see cref="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Renaming" /> events.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.RenamingEventArgs.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.RenamingEventArgs" /> class.
            </summary>
            <param name="wrapper"></param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.RenamingEventArgs.FileInfo">
            <summary>
            Gets the FileSystemInfoWrapper object for the file system info which is edited (renamed).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.RenamingEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the editing (renaming) mode should start or not.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs">
            <summary>
            Event args for the ShellContextMenuOpening event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs.#ctor(System.Collections.Generic.IList{Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs" /> class.
            </summary>
            <param name="selectedFiles">The files on which the context menu is triggered.</param>
            <param name="isOpeningOnEmptySpace">True if context menu is opening in the empty area.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs.SelectedFiles">
            <summary>
            Gets the list of files on which the context menu is being opened.
            If the list is empty, context menu is opened on empty space in the dialog or explorer control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether this context menu opening should be cancelled.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs.IsOpeningOnEmptySpace">
            <summary>
            Gets a value indicating whether this context menu is opening on empty space.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs.ShortContextMenuOptions">
            <summary>
            Gets or sets a value indicating all available options for the short context menu (shown on empty places on no selected files).
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.DirectoryRequesting"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs.Directory">
            <summary>
            The DirectoryInfo which is requested.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the directory wrapper creation should be cancelled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserExceptionRaisedEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.ExceptionRaised"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserExceptionRaisedEventArgs.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserExceptionRaisedEventArgs"/> class.
            </summary>
            <param name="exception"></param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserExceptionRaisedEventArgs.Exception">
            <summary>
            Gets the thrown exception.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands">
            <summary>
            Provides commands for FileDialogs.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Open">
            <summary>
            Open command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Cancel">
            <summary>
            Cancel command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.SelectFolder">
            <summary>
            SelectFolder command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Save">
            <summary>
            Save command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Edit">
            <summary>
            Edit command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.NewFolder">
            <summary>
            New Folder command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Copy">
            <summary>
            Copy command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Cut">
            <summary>
            Cut command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Paste">
            <summary>
            Paste command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.CommandId.Delete">
            <summary>
            Delete command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Open">
            <summary>
            Gets the open command.
            </summary>
            <value>The open command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Cancel">
            <summary>
            Gets the cancel command.
            </summary>
            <value>The cancel command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.SelectFolder">
            <summary>
            Gets the selectFolder command.
            </summary>
            <value>The selectFolder command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Save">
            <summary>
            Gets the save command.
            </summary>
            <value>The save command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.NewFolder">
            <summary>
            Gets the new folder command.
            </summary>
            <value>The newFolder command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Edit">
            <summary>
            Gets the edit command.
            </summary>
            <value>The edit command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Copy">
            <summary>
            Gets the copy command.
            </summary>
            <value>The copy command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Cut">
            <summary>
            Gets the cut command.
            </summary>
            <value>The cut command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Paste">
            <summary>
            Gets the paste command.
            </summary>
            <value>The paste command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogsCommands.Delete">
            <summary>
            Gets the delete command.
            </summary>
            <value>The delete command.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1">
            <summary>
            Defines the basic methods of a generic FileDialogs DragDropBehavior.
            </summary>
            <typeparam name="TState">The state type.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.GetDragDropEffects(`0)">
            <summary>
            Returns the DragDropEffects for the current drag drop operation. The return value affects the mouse cursor.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <remarks>
            This method is called only in the context of the drop target control.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.CanStartDrag(`0)">
            <summary>
            Returns a value specifying whether the drag operation can be started.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <returns>True if the drag operation can be completed, otherwise false.</returns>
            <remarks>
            This method is called only in the context of the drag source control.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.CanDrop(`0)">
            <summary>
            Returns a value specifying whether the current drag operation can be completed.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <returns>True if the drag operation can be completed, otherwise false.</returns>
            <remarks>
            This method is called only in the context of the drop target control.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.CoerceDraggedItems(`0)">
            <summary>
            When overridden in a derived class, filters the dragged items collection if necessary.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <returns>The filtered dragged items.</returns>
            <remarks>
            This method is called immediately before the drag operation starts.
            Only the Items property of the DragDropState is valid.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.DragDropCanceled(`0)">
            <summary>
            When overridden in a derived class cleans up a cancelled drag operation. This method is called only in the context of the drag source control.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <remarks>
            This method is called only when the drag operation is cancelled by the user. If this method is called, the source's DragDropCompleted and 
            target's Drop methods are not called.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.DragDropCompleted(`0)">
            <summary>
            When overridden in a derived class completes the drag operation. This method is called only in the context of the drag source control.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <remarks>
            When the drag source and the drop target are the same control, this method is called after Drop. 
            This method is called only when the drag operation completed successfully. If this method is called, DragDropCanceled is not called.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.Drop(`0)">
            <summary>
            Completes the drop operation. This method is called only in the context of the drop target control.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.IsCopyingItems(`0)">
            <summary>
            Returns true if the dragged files should be copied to the DropFolder, otherwise false.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropBehavior`1.IsLinkingItems(`0)">
            <summary>
            Returns true if a shortcut to the dragged files should be created in the DropFolder, otherwise false.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2">
            <summary>
            Binds the DragDropManager events for the different controls in a <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> with a DragDropBehavior.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.DragDropBehavior">
            <summary>
            Gets or sets the DragDropBehavior that will be used in the drag-drop operations.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.GetDraggedItems(System.Windows.FrameworkElement)">
            <summary>
            When overridden in a derived class this method returns the items that should be dragged.
            </summary>
            <param name="draggedItem">The source control of the drag operation.</param>
            <returns>The items that should be dragged.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.FindDragSource(System.Windows.FrameworkElement)">
            <summary>
            Finds the control that contains the provided element. In case of a ListBox drag-drop implementation, this method should return a ListBox.
            </summary>
            <param name="element">The element that raised the DragInitialize event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.FindDraggedItem(System.Windows.FrameworkElement)">
            <summary>
            Finds the control that contains the provided element. In case of a ListBox drag-drop implementation, this method should return the ListBoxItem that is going to be dragged.
            </summary>
            <param name="element">The element that raised the DragInitialize event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.FindDropItemsControl(System.Windows.FrameworkElement,System.Object)">
            <summary>
            Finds the target control that should accept the dragged items. In case of a ListBox drag-drop implementation, this method should return a ListBox.
            </summary>
            <param name="element">The element that raised the Drop event.</param>
            <param name="data">The DataObject for the current drag operation.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.FindDropTarget(System.Windows.FrameworkElement)">
            <summary>
            Finds an item from the target control that contains the provided element. In case of a ListBox drag-drop implementation, this method should return a ListBoxItem.
            </summary>
            <param name="element">The element that raised the Drop event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.DragDropCompleted(System.Windows.FrameworkElement,System.Windows.FrameworkElement,System.Object,System.Boolean)">
            <summary>
            When overridden in a derived class allows calling custom code on the DragDropCompleted event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropHelper`2.MouseOverItemChanged(System.Windows.FrameworkElement,System.Windows.FrameworkElement)">
            <summary>
            Called when the mouse over FrameworkElement is changed.
            </summary>
            <param name="oldItem">The old mouse over FrameworkElement.</param>
            <param name="newItem">The new mouse over FrameworkElement.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState">
            <summary>
            Provides a context for a file dialog drag-drop operation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.DraggedFiles">
            <summary>
            Gets the paths of the files and/or folders that are being dragged.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.DropFolder">
            <summary>
            Gets the folder where it is being dropped.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.AreInTheSameDrive">
            <summary>
            Gets a value indicating whether the dragged files/folders are from the same drive as the drop folder. Returns true if the<see cref="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.DropFolder"/> is null.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.IsControlDown">
            <summary>
            Gets or sets the state of the Control key in the moment when the DragDropState was created.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.IsShiftDown">
            <summary>
            Gets or sets the state of the Shift key in the moment when the DragDropState was created.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState.IsAltDown">
            <summary>
            Gets or sets the state of the Alt key in the moment when the DragDropState was created.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails">
            <summary>
            Provides a context for a file dialog drag-over operation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.Count">
            <summary>
            Gets or sets the count of the dragged files/folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.Icon">
            <summary>
            Gets or sets the icon of the dragged files/folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.Operation">
            <summary>
            Gets or sets the current <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation"/> that will be performed if drop i successful.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.ShowDropIndicationInformation">
            <summary>
            Gets or sets a value indicating whether to show drop information in the drag visual.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.DropFolderName">
            <summary>
            Gets or sets the name of the drop folder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.DropFolderPath">
            <summary>
            Gets or sets the path of the drop folder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.DropIndicationDetails.DraggedFiles">
            <summary>
            Gets or sets the files/folders being dragged.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior">
            <summary>
            Defines a DragDropBehavior for drag-drop operations in a <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior.CanStartDrag(Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior.CanDrop(Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior.Drop(Telerik.Windows.Controls.FileDialogs.DragDrop.DragDropState)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual">
            <summary>
            Represents a drag visual that provides feedback for the dragged elements in the <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.DragVisualOffsetProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.DragVisualOffset"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.IconTemplateProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.IconTemplate"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.DragVisualOffset">
            <summary>
            Gets or sets the offset of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual"/> relative to the mouse position.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.IconTemplate">
            <summary>
            Gets or sets that icon template.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragVisual.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper">
            <summary>
            Binds the DragDropManager events for the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView"/> with a <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.FindDraggedItem(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.FindDragSource(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.FindDropItemsControl(System.Windows.FrameworkElement,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.FindDropTarget(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.GetDraggedItems(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserGridViewDragDropHelper.MouseOverItemChanged(System.Windows.FrameworkElement,System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper">
            <summary>
            Binds the DragDropManager events for the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox"/> with a <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.FindDraggedItem(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.FindDragSource(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.FindDropItemsControl(System.Windows.FrameworkElement,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.FindDropTarget(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.GetDraggedItems(System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserListBoxDragDropHelper.MouseOverItemChanged(System.Windows.FrameworkElement,System.Windows.FrameworkElement)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation">
            <summary>
            Specifies the operation of a drag-and-drop operation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation.Move">
            <summary>
            The data from the drag source is moved to the drop target.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation.Copy">
            <summary>
            The data is copied to the drop target.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation.Link">
            <summary>
            The data from the drag source is linked to the drop target.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileOperationToStringConverter">
            <summary>
            A converter to return string from <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileOperationToStringConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Convert method to return string for the <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileOperation"/>.
            </summary>
            <param name="value">FileOperation.</param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns>Returns String.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileOperationToStringConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Convert back method is not implemented.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox">
            <summary>
            Encapsulates the editing logic for the main pane views that use ListBox and GridView.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnLostFocus(System.Windows.RoutedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnTextInput(System.Windows.Input.TextCompositionEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnTextChanged(System.Windows.Controls.TextChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserEditTextBox.OnPreviewKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl">
            <summary>
            Control used for the navigation part of the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> and <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/>
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterDescriptorsProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterDescriptors"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedSafeFileNamesProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedSafeFileNames"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileNamesProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileNames"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileNameProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileName"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterIndexProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterIndex"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Filter"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CustomPlacesPathsProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CustomPlacesPaths"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsDragDropEnabledProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsDragDropEnabled"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.DragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.DragDropBehavior"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.MultiselectProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Multiselect"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.LayoutProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Layout"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowserProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowser"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CurrentDirectoryPathProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CurrentDirectoryPath"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ExpandToCurrentDirectoryProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ExpandToCurrentDirectory"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ShowHiddenFilesProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ShowHiddenFiles"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.LoadDrivesInBackgroundProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.LoadDrivesInBackground"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CanUserRenameProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CanUserRename"/> dependency property. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ExceptionRaised">
            <summary>
            Occurs when Exception during File (or Directory) creation / observation / modification is raised.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.DirectoryRequesting">
            <summary>
            Occurs when the DirectoryInfo wrapper is about to be created.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.DirectoryNavigating">
            <summary>
            Occurs when the current folder is about to change.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ShellContextMenuOpening">
            <summary>
            Occurs when context menu is about to open.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Renaming">
            <summary>
            Occurs when a file or directory is about to enter edit mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CustomPlacesPaths">
            <summary>
            Gets or sets the paths to the custom places navigation tree view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsDragDropEnabled">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> allows drag-drop operations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.DragDropBehavior">
            <summary>
            Gets or sets the <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior"/> for this <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Multiselect">
            <summary>
            Gets or sets a value indicating whether multiple selection of files or folders is allowed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Layout">
            <summary>
            Gets or sets the current <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/> of the Main pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowser">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> instance is used for directories selection or both directories and files.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CurrentDirectoryPath">
            <summary>
            Gets or sets a value indicating the path of the current directory in this <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ExpandToCurrentDirectory">
            <summary>
            Gets or sets a value indicating whether the main navigation pane expands and brings into view the current directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterIndex">
            <summary>
            Gets or sets a value indicating the current filter index from the list of filter strings specified by the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Filter"/> property.
            It has effect only when <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowser"/> is False.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Filter">
            <summary>
            Gets or sets the filter string that describes the list of extensions to filter by.
            It has effect only when <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowser"/> is False.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.FilterDescriptors">
            <summary>
            Gets the list of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileFilterDescriptor"/> specified by the <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.Filter"/> property.
            It has effect only when <see cref="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.IsFolderBrowser"/> is False.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileName">
            <summary>
            Gets the full path of the currently selected file or directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedFileNames">
            <summary>
            Gets the list of full paths of the currently selected files and / or folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.SelectedSafeFileNames">
            <summary>
            Gets the list of names of the currently selected files and / or folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ShowHiddenFiles">
            <summary>
            Gets or sets a boolean value indicating whether hidden files and folders should be visible in the Explorer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.LoadDrivesInBackground">
            <summary>
            Gets or sets a value indicating whether the control loads the drives under 'This PC ' node in background thread.
            Could be used in scenarios when some drives are expected to load slower than normal.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.CanUserRename">
            <summary>
            Gets or sets a value indicating whether the user can rename file or folders via mouse click or keyboard F2 press in main pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ShowNetworkLocations">
            <summary>
            Gets or sets a value indicating whether the dialog shows network computers in the navigation tree.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControl.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControl.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions">
            <summary>
            ShortContextMenuOptions is Flags enum indicating the available options in the context menu which appears on empty spaces in <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions.View">
            <summary>
            View menu option.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions.Paste">
            <summary>
            Paste menu option.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions.NewFolder">
            <summary>
            New folder menu option.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions.Properties">
            <summary>
            Properties menu option.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShortContextMenuOptions.All">
            <summary>
            All options in the menu are available.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl">
            <summary>
            Control used for the history navigation part of the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> and <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/>
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.DirectoryHistoryProperty">
            <summary>
            Identifies the <see>DirectoryHistory</see> dependency property. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.DirectoryHistory">
            <summary>
            Gets or sets the collection of selected <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HistoryNavigationPaneControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands">
            <summary>
            Provides commands for HistoryNavigationCommands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.CommandId.Forward">
            <summary>
            Forward command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.CommandId.Back">
            <summary>
            Back command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.CommandId.Up">
            <summary>
            Up command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.Forward">
            <summary>
            Gets the forward command.
            </summary>
            <value>The forward command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.Back">
            <summary>
            Gets the back command.
            </summary>
            <value>The back command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.HistoryNavigationCommands.Up">
            <summary>
            Gets the up command.
            </summary>
            <value>The up command.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DirectorySizeCalculator">
            <summary>
            Extracts size of directory from <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/>.
            Logic is not included in Size property to avoid slow data grid loading due to the size column.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectorySizeCalculator.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Gets the size of the given <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/> represented in user-friendly string.
            </summary>
            <returns>The size of the directory as string.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectorySizeCalculator.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserAutocompleteBoxesItemStyleSelector">
            <summary>
            A style selector that returns a different style whether a <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/> is a file or directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserAutocompleteBoxesItemStyleSelector.FileStyle">
            <summary>
            Gets or sets the style for item that is a <see cref="T:Telerik.Windows.Controls.FileDialogs.FileInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserAutocompleteBoxesItemStyleSelector.DirectoryStyle">
            <summary>
            Gets or sets the style for item that is <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserAutocompleteBoxesItemStyleSelector.SelectStyle(System.Object,System.Windows.DependencyObject)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserDragOverHighlightBehavior">
            <summary>
            An attached behavior for synchronization of the current drag-over item in the context of a drag-drop operation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserDragOverHighlightBehavior.IsDragOverProperty">
            <summary>
            Identifies the IsDragOver attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserDragOverHighlightBehavior.GetIsDragOver(System.Windows.DependencyObject)">
            <summary>
            Returns the value of the IsDragOver property related to the specified object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserDragOverHighlightBehavior.SetIsDragOver(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the value of the IsDragOver property related to the specified object.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView">
            <summary>
            Represents a GridView control for listing detailed information for files and folders in the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> 
            and <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.FileBrowserDragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.FileBrowserDragDropBehavior"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.FileBrowserDragDropBehavior">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.FileBrowserDragDropBehavior"/> for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.RegisterCopyPasteApplicationCommands">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.OnTextInput(System.Windows.Input.TextCompositionEventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridView.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserGridViewSelectedItemsBindingBehavior">
            <summary>
            An attached behavior for synchronization of <see cref="T:Telerik.Windows.Controls.RadGridView"/> selected items.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserGridViewSelectedItemsBindingBehavior.SelectedItemsProperty">
            <summary>
            Identifies the SelectedItems attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridViewSelectedItemsBindingBehavior.GetSelectedItems(System.Windows.DependencyObject)">
            <summary>
            Returns the value of the SelectedItems property related to the specified object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserGridViewSelectedItemsBindingBehavior.SetSelectedItems(System.Windows.DependencyObject,System.Collections.Specialized.INotifyCollectionChanged)">
            <summary>
            Sets the value of the SelectedItems property related to the specified object.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox">
            <summary>
            Represents a ListBox control for listing files and folders in the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> 
            and <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.FileBrowserDragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.FileBrowserDragDropBehavior"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.FileBrowserDragDropBehavior">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.FileBrowserDragDropBehavior"/> for this <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.OnItemsSourceChanged(System.Collections.IEnumerable,System.Collections.IEnumerable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserListBox.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl">
            <summary>
            Represents a RadTabControl control for displaying the different layout views of files and folders in the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> 
            and <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl.TabStripVisibilityProperty">
            <summary>
            Identifies the TabStripVisibility dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl.TabStripVisibility">
            <summary>
            Gets or sets the visibility of the tab strip.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTabControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView">
            <summary>
            Represents a TreeView control for displaying the hierarchical folder structure in the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>, <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> 
            and <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.FileBrowserDragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.FileBrowserDragDropBehavior"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.FileBrowserDragDropBehavior">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.FileBrowserDragDropBehavior"/> for this <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.IsItemItsOwnContainerOverride(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.GetContainerForItemOverride">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView.TreeViewItem_MouseDown(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Ensures edit is committed on mouse down , although selection is performed on mouse up.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeViewItem">
            <summary>
            Represents an item in the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeView">FileBrowserTreeView</see> control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeViewItem.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeViewItem.IsItemItsOwnContainerOverride(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeViewItem.GetContainerForItemOverride">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileBrowserTreeViewItem.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileInfoToolTipTemplateSelector">
            <summary>
            Converter to select file or folder tooltip template based on the type of the file info wrapper.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileInfoToolTipTemplateSelector.FileToolTipTemplate">
            <summary>
            File tooltip template.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileInfoToolTipTemplateSelector.DirectoryToolTipTemplate">
            <summary>
            Directory tooltip template.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileInfoToolTipTemplateSelector.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts the wrapper type into corresponding tooltip template.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileInfoToolTipTemplateSelector.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileSizeType">
            <summary>
            Options that specify the file size type in the main pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileSizeType.B">
            <summary>
            Specifies bytes file size type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileSizeType.KB">
            <summary>
            Specifies kilobytes file size type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileSizeType.MB">
            <summary>
            Specifies megabytes file size type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileSizeType.GB">
            <summary>
            Specifies gigabytes file size type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileSizeType.TB">
            <summary>
            Specifies terabytes file size type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.HiddenFilesToTreeViewItemVisibilityConverter">
            <summary>
            Converts ShowHiddenFiles and IsHidden to Visibility of RadTreeViewItems in the navigation trees and RadBreadCrumbBarItems in BreadCrumb.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HiddenFilesToTreeViewItemVisibilityConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Returns Collapsed when ShowHiddenFiles is false and file is hidden. Otherwise returns false.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.HiddenFilesToTreeViewItemVisibilityConverter.ConvertBack(System.Object,System.Type[],System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel">
            <summary>
            Defines a layout for the Open file, Save file, and Open folder dialogs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.#ctor(Telerik.Windows.Controls.FileDialogs.LayoutType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel"/> class.
            </summary>
            <param name="layoutType">The type of the layout.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.IconSize">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.IconSize"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.LayoutType">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.LayoutType"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.NameKey">
            <summary>
            Gets the NameKey to be used for localizable strings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.GetSizeForType(Telerik.Windows.Controls.FileDialogs.LayoutType)">
            <summary>
            A method to provide the <see cref="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.IconSize"/> for the <see cref="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.LayoutType"/>.
            </summary>
            <param name="layoutType"></param>
            <returns>The chosen IconSize.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.GetNameKeyForType(Telerik.Windows.Controls.FileDialogs.LayoutType)">
            <summary>
            A method to provide the localization key for the <see cref="P:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel.LayoutType"/>.
            </summary>
            <param name="layoutType"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector">
            <summary>
            A style selector that returns a different style based on the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/> of the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel"/> of the control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.SmallIconsStyle">
            <summary>
            Gets or sets the style for item with LayoutType.SmallIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.MediumIconsStyle">
            <summary>
            Gets or sets the style for item with LayoutType.MediumIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.LargeIconsStyle">
            <summary>
            Gets or sets the style for item with LayoutType.LargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.ExtraLargeIconsStyle">
            <summary>
            Gets or sets the style for item with LayoutType.ExtraLargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.ListStyle">
            <summary>
            Gets or sets the style for item with LayoutType.List.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.TilesStyle">
            <summary>
            Gets or sets the style for item with LayoutType.Tiles.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.DetailsStyle">
            <summary>
            Gets or sets the style for item with LayoutType.Details.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.DefaultStyle">
            <summary>
            Gets or sets the default style for item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorStyleSelector.SelectStyle(System.Object,System.Windows.DependencyObject)">
            <summary>
            Selects a <see cref="T:System.Windows.Style"/> based on the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/> of the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel"/>.
            </summary>
            <param name="item"></param>
            <param name="container"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector">
            <summary>
            Template selector that is used to provide the <see cref="T:System.Windows.DataTemplate"/> based on the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/> of the model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.SmallIconsDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.SmallIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.MediumIconsDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.MediumIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.LargeIconsDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.LargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.ExtraLargeIconsDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.ExtraLargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.ListDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.List.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.TilesDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.Tiles.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.DetailsDataTemplate">
            <summary>
            Gets or sets the DataTemplate for item with LayoutType.Details.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutConfiguratorTemplateSelector.SelectTemplate(System.Object,System.Windows.DependencyObject)">
            <summary>
            Returns the appropriate <see cref="T:System.Windows.DataTemplate"/> for <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel"/> item depending on its <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/>.
            </summary>
            <param name="item"></param>
            <param name="container"></param>
            <returns>The chosen DataTemplate.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.LayoutType">
            <summary>
            Options that specify the type of the layout of the Main pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.SmallIcons">
            <summary>
            Specifies layout type with small icons.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.MediumIcons">
            <summary>
            Specifies layout type with medium icons.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.LargeIcons">
            <summary>
            Specifies layout type with large icons.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.ExtraLargeIcons">
            <summary>
            Specifies layout type with extra large icons.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.List">
            <summary>
            Specifies list layout type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.Tiles">
            <summary>
            Specifies tiles layout type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.LayoutType.Details">
            <summary>
            Specifies details layout type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter">
            <summary>
            A converter to return the appropriate <see cref="T:System.Windows.Media.ImageSource"/> based on the <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/> for the current item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.ExtraLargeIconImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.ExtraLargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.LargeIconImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.ExtraLargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.MediumIconImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.ExtraLargeIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.SmallIconImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.SmallIcons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.ListImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.List.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.TilesImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.Tiles.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.DetailsImageSource">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.ImageSource"/> for LayoutType.Details.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Convert method to return the IconSource for the LayoutType.
            </summary>
            <param name="value">LayoutType.</param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns>IconSource.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.LayoutTypeToIconConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Convert back method. Returns the value.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.RoundFileSizeConverter">
            <summary>
            A converter that received a long value for file size and returns is formatted.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.RoundFileSizeConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a file size value (long) to a string with value (number) (modifier) where 1&lt;number&lt;1024 and modifier is bytes/KB/MB/GB/TB.
            Parameter can be set to "KB" and the method would return the value converted only to KB.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.RoundFileSizeConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DialogViewModel">
            <summary>
            Base class for the different file dialogs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.Dispose(System.Boolean)">
            <summary>
             Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.Boolean,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DialogViewModel"/> class.
            </summary>
            <param name="fileSystemInfoWrapperFactory">The <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase"/> used for creating <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.</param>
            <param name="rootDirPath">The path of the initial directory.</param>
            <param name="showNetwork">Boolean indicating whether network locations are shown.</param>
            <param name="loadDrivesInBackground">Boolean indicating whether local drives are loaded in the background.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.MainNavigationRoot">
            <summary>
            Gets the main root <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/> used for folder navigation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.MainNavigationRootSource">
            <summary>
            Gets the root <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/> used for folder navigation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.Path">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.Name">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.ShowHiddenFiles">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.InitialDirectory">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CurrentParentDirectory">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.SelectedFileSystemObject">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CurrentFileSystemObjects">
            <summary>
            Gets the currently visible <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.RootAndCurrentFileSystemObjects">
            <summary>
            Gets the currently root and current visible <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.IsDirectorySelected">
            <summary>
            Return true if the selected item is directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CustomPlaces">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.SelectedLayout">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.Layouts">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.IsMainPaneLoadingNetwork">
            <summary>
            Used to trigger a busy indicator while network location is being loaded for the main pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.FileSystemInfoWrapperFactory">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase"/> for creating wrappers for <see cref="T:System.IO.FileSystemInfo"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CreateNewChildFolder">
            <summary>
            Creates new child folder in the <see cref="P:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CurrentParentDirectory"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.CanExecuteAccept">
            <summary>
            Defines the method that determines whether the accept command can execute in its current state.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.InvokeSelectedFileSystemObjects">
            <summary>
            Execute logic when accept is executed for the selected <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.GetCurrentFileSystemInfoWrappers(Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper)">
            <summary>
            Gets the current visible <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.OnSelectionChanged">
            <summary>
            Notifies when the SelectedFileSystemObject changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.ResetSelection">
            <summary>
            Resets the selection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogViewModel.FindFirstMissing(System.Int32[],System.Int32,System.Int32)">
            <summary>
            Finds first missing number in array of integers with no duplicates.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs">
            <summary>
            Event args for DirectoryNavigating event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs"/> class.
            </summary>
            <param name="newDirectoryPath">The new directory path to be navigated.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether to cancel the change of the current directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs.DirectoryPath">
            <summary>
            The path of the new directory path which is about to get current.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.NetworkLoader">
            <summary>
            This class is responsible for loading network locations via Shell API.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.NetworkLoader.NetworkLoaded">
            <summary>
            Fires when network PCs are loaded.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.NetworkLoader.LoadNetworkComputers">
            <summary>
            Loads network PCs in background and fires the <see cref="P:Telerik.Windows.Controls.FileDialogs.NetworkLoader.NetworkLoaded"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.NetworkLoader.IsPossibleUncPath(System.String)">
            <summary>
            This method used prevent further exceptions in Directory.Exist with paths like '\\some_PC_name'.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl">
            <summary>
            Control used for the Content of a <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogControl">
            <summary>
            Control used for the Content of a <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogControl.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb">
            <summary>
            Custom RadBreadcrumb in order to meet the needs of FileDialogs. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.#ctor">
            <summary>
            Initializes a new instance of the FileDialogsBreadcrumb class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.ExceptionRaised">
            <summary>
            Occurs when the <see cref="T:System.Exception"/> is raised.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.FindCurrentItemRecursively(System.String,System.Collections.IEnumerable,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.OnKeyUp(System.Windows.Input.KeyEventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogsBreadcrumb.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl">
            <summary>
            Control used for the Content of a <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogControl.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DetailsGridItemsSourceConverter">
            <summary>
            Converter which switches the source of the Details GridView - search results from search pane or current files / folders from main view model.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DetailsGridItemsSourceConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Switches SearchResults or CurrentFileSystemObjects depending on IsSearchViewActive property of search pane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DetailsGridItemsSourceConverter.ConvertBack(System.Object,System.Type[],System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not supported because the purpose of converter is to support one way binding to ItemsSource.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane">
            <summary>
            Control which wraps the search box of the FileDialogs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.IsSearchViewActiveProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.IsSearchViewActive"/> property.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.PropertyChanged">
            <summary>
            PropertyChanged event handler delegate.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.CurrentSearchResults">
            <summary>
            Collection of search results. We do not use ViewModels CurrentFileSystemObjects in order to be process the results in background successfully.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.IsSearchViewActive">
            <summary>
            Gets or sets the <see cref="F:Telerik.Windows.Controls.FileDialogs.FileDialogSearchPane.IsSearchViewActiveProperty"/>. When true, it indicates the Dialog's Main Pane shows results from search operation. 
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ExplorerControlFileViewModel">
            <summary>
            File ViewModel class for <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> in standalone usage (not placed in <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ExplorerControlFileViewModel.InvokeSelectedFileSystemObjects">
            <summary>
            Only navigation - no commands executed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel">
            <summary>
            Base ViewModel class for <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/> and <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> controls. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.Boolean,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.FilterIndex">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.DefaultExt">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.Filter">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.IsFilterApplied">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.FilterDescriptors">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.SelectedFilterName">
            <summary>
            Gets the FilterName of the currently applied filter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.IsSearchActive">
            <summary>
            SearchPane's IsSearchViewActive is OneWayToSource bound to IsSearchActive.
            This is the reason for the public modifier here.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.InvokeSelectedFileSystemObjects">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.GetCurrentFileSystemInfoWrappers(Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileDialogViewModel.CoerceFilterIndexStandalone(System.Int32)">
            <summary>
            In Standalone usage FilterIndex of ExplorerControl is 0-based, not like in Dialogs (1-based).
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ViewModelHelper">
            <summary>
            Helper class for providing standalone-suitable view models for <see cref="T:Telerik.Windows.Controls.FileDialogs.ExplorerControl"/> .
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileFilterDescriptor">
            <summary>
            Class which describes a filter operation - extensions and their user friendly group name.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileFilterDescriptor.FilterName">
            <summary>
            The group name of the extensions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileFilterDescriptor.Extensions">
            <summary>
            List of all extensions in the current group.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ICurrentDirectoryViewModel">
            <summary>
            Interface that provides information about current drilled directory and currently selected file or folder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ICurrentDirectoryViewModel.CurrentParentDirectory">
            <summary>
            Gets the currently drilled directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ICurrentDirectoryViewModel.SelectedFileSystemObject">
            <summary>
            Gets the currently selected file or folder.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ICurrentDirectoryViewModel.CreateNewChildFolder">
            <summary>
            Creates new child folder of the <see cref="P:Telerik.Windows.Controls.FileDialogs.ICurrentDirectoryViewModel.CurrentParentDirectory"/>
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IDialogViewModel">
            <summary>
            Interface that provides base information for all file dialog view models.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.Path">
            <summary>
            Gets a string representing the path to the current folder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.Name">
            <summary>
            Gets or sets a string representing the full path to the current file system object.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.ShowHiddenFiles">
            <summary>
            Gets or sets a boolean value indicating whether the dialog shows hidden files and folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.InitialDirectory">
            <summary>
            Gets or sets the initial directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.CustomPlaces">
            <summary>
            Gets or sets the collection of custom places.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.Layouts">
            <summary>
            Gets the available layouts for the dialog explorer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IDialogViewModel.SelectedLayout">
            <summary>
            Gets the selected <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutConfigurationModel"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable">
            <summary>
            Interface that provides properties for filtering files by extension.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable.Filter">
            <summary>
            Gets or sets the filter string that describes the list of extensions to filter by.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable.FilterIndex">
            <summary>
            Gets or sets the currently selected extension filter from the extensions described in the <see cref="P:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable.Filter"/> property.
            The index value of the first filter entry is 1.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable.DefaultExt">
            <summary>
            Gets or sets a value that specifies the default extension string to use to filter the list of files that are displayed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IFileFilterableModel">
            <summary>
            Interface that extends the <see cref="T:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable"/> interface by providing a notion for filter descriptions and boolean property to determine if filtering is currently active or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IFileFilterableModel.IsFilterApplied">
            <summary>
            Gets or sets a value indicating whether the current filtering is active or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IFileFilterableModel.FilterDescriptors">
            <summary>
            Gets or sets the list of <see cref="T:Telerik.Windows.Controls.FileDialogs.FileFilterDescriptor"/>s described by the <see cref="P:Telerik.Windows.Controls.FileDialogs.IFileExtensionFilterable.Filter"/> property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IMultiSelectable">
            <summary>
            Interface that provides properties for multiple selection of files or folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IMultiSelectable.Multiselect">
            <summary>
            Gets or sets a value indicating whether multiple selection of files or folders is allowed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IMultiSelectable.SelectedFileSystemInfoWrappers">
            <summary>
            Gets a collection of selected <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IMultiFilesSelectable">
            <summary>
            Interface that provides a collection of selected files.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IMultiFilesSelectable.FileNames">
            <summary>
            Gets or sets a string containing the selected file names, including their paths.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.IMultiFilesSelectable.SafeFileNames">
            <summary>
            Gets or sets a string containing the selected file names, excluding their paths.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel">
            <summary>
            Provides a view model for setup and configuration of RadOpenFileDialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.Boolean,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel"/> class.
            </summary>
            <param name="fileSystemInfoWrapperFactory">The factory used from this <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel"/> for creation of file system wrappers.</param>        
            <param name="rootDirPath">The path of the initial directory.</param>
            <param name="showNetwork">Boolean indicating whether network locations are shown.</param>
            <param name="loadDrivesInBackground">Boolean indicating whether local drives are loaded in the background.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.DereferenceLinks">
            <summary>
            Gets or sets a value indicating whether a file dialog returns either the location
            of the file referenced by a shortcut or the location of the shortcut file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.Multiselect">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.ReadOnlyChecked">
            <summary>
            Gets or sets a value indicating whether the read-only check box is selected.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.ShowReadOnly">
            <summary>
            Gets or sets a value indicating whether the dialog contains a read-only check box.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.SelectedFileSystemInfoWrappers">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.InvokeSelectedFileSystemObjects">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.CanExecuteAccept">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.ResetSelection">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel.OnPropertyChanged(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel">
            <summary>
            Provides a view model for setup and configuration of RadOpenFolderDialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.Boolean,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel"/> class.
            </summary>
            <param name="fileSystemInfoWrapperFactory">The factory used from this <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel"/> for creation of file system wrappers.</param>
            <param name="rootDirPath">The path of the initial directory.</param>
            <param name="showNetwork">Boolean indicating whether network locations are shown.</param>
            <param name="loadDrivesInBackground">Boolean indicating whether local drives are loaded in the background.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.Multiselect">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.SelectedFileSystemInfoWrappers">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.InvokeSelectedFileSystemObjects">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.CanExecuteAccept">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.ResetSelection">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.GetCurrentFileSystemInfoWrappers(Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.OpenFolderDialogViewModel.OnPropertyChanged(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel">
            <summary>
            Provides a view model for setup and configuration of RadSaveFileDialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.Boolean,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel"/> class.
            </summary>
            <param name="fileSystemInfoWrapperFactory">The factory used from this <see cref="T:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel"/> for creation of file system wrappers.</param>
            <param name="rootDirPath">The path of the initial directory.</param>
            <param name="showNetwork">Boolean indicating whether network locations are shown.</param>
            <param name="loadDrivesInBackground">Boolean indicating whether local drives are loaded in the background.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel.InvokeSelectedFileSystemObjects">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.SaveFileDialogViewModel.OnSelectionChanged">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.ChildFileDeleted"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs.#ctor(System.String,Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs"/> class.
            </summary>
            <param name="filePath">The path that no longer exists.</param>
            <param name="parentWrapper">The DirectoryInfoWrapper of the parent folder.</param>
            <param name="isRename">Indicates whether the file is being renamed causing deleted event.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs.ParentWrapper">
            <summary>
            Gets the DirectoryInfoWrapper of the parent folder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs.FilePath">
            <summary>
            Gets the path that no longer exists.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ChildFileDeletedEventArgs.IsRenamed">
            <summary>
            Gets a value indicating whether the deleted file is a result of rename operation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper">
            <summary>
            Base class for <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/>, <see cref="T:Telerik.Windows.Controls.FileDialogs.ShellDirectory"/> and <see cref="T:Telerik.Windows.Controls.FileDialogs.ServerDirectory"/> instances.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/> class. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,System.IO.DirectoryInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/> class. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.ChildDirectories">
            <summary>
            Child directories representing local or network folders.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.Children">
            <summary>
            Gets the child <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.IsExpanded">
            <summary>
            Gets or sets a value indicating whether the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/> is expanded.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.CanExpand">
            <summary>
            Gets or sets a value indicating whether the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/> can expand.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.IsInEditMode">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper.Size">
            <summary>
            This property is not implemented for directories.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper">
            <summary>
            Provides a wrapper for <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper.Size">
            <summary>
            This property is not implemented for directories.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper.IsShortcut">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper.ShortcutLocation">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper.SortOrder">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileInfoWrapper">
            <summary>
            Provides a wrapper for <see cref="T:System.IO.FileInfo"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileInfoWrapper.Size">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileInfoWrapper.SortOrder">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactory.TryGetConcreteFileSystemInfoWrapper(System.IO.FileSystemInfo,Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper@)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactory.CleanupPath(System.String,Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactory.ShouldCreateRequestedInfo(System.IO.DirectoryInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase">
            <summary>
            Base factory class for creating <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s from <see cref="T:System.IO.FileSystemInfo"/>s.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.DirectoryRequesting">
            <summary>
            Occurs when DirectoryInfo wrapper is about to be created. User can cancel the creation by handling the event and setting the
            <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs"/> 's Cancel property to true. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.ExceptionRaised">
            <summary>
            Occurs when creating a FileSystemInfo wrapper or DirectoryInfo wrapper produces an exception.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.ChildFileDeleted">
            <summary>
            Occurs when a child file or folder is deleted/removed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.TryGetConcreteFileSystemInfoWrapper(System.IO.FileSystemInfo,Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper@)">
            <summary>
            Returns <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s from <see cref="T:System.IO.FileSystemInfo"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.TryGetDirectoryInfoWrapper(System.IO.DirectoryInfo,Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper@)">
            <summary>
            Returns the <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/> for a specific <see cref="T:System.IO.DirectoryInfo"/>
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.CleanupPath(System.String,Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper,System.Boolean)">
            <summary>
            Called when a file path no longer exists (for example when a file is moved, renamed or deleted).
            </summary>
            <param name="filePath">The path that no longer exists.</param>
            <param name="parentWrapper">The DirectoryInfoWrapper of the parent folder.</param>
            <param name="isRename">Indicates whether the file is being renamed causing deleted event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.OnExceptionRaised(System.Exception)">
            <summary>
            Called when exception is thrown when working with <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.OnChildFileDeleted(System.String,Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper,System.Boolean)">
            <summary>
            Raises the <see href="ChildFileDeleted"/> event.
            </summary>
            <param name="filePath">The path that no longer exists.</param>
            <param name="parentFolderPath">The DirectoryInfoWrapper of the parent folder.</param>
            <param name="isRename">Indicates whether the file is being renamed causing deleted event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase.ShouldCreateRequestedInfo(System.IO.DirectoryInfo)">
            <summary>
            Returns a value indicating whether a <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/> will be created for the passed <see cref="T:System.IO.DirectoryInfo"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper">
            <summary>
            Base class that provides a wrapper for <see cref="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.FileSystemInfo"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.Size">
            <summary>
            Gets or sets the size, in bytes of the selected file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.DateCreated">
            <summary>
            Gets the creation <see cref="T:System.DateTime"/> of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.TypeText">
            <summary>
            Gets the type of file as descriptive text.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsShortcut">
            <summary>
            Gets whether the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/> is a shortcut.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.ShortcutLocation">
            <summary>
            Gets the shortcut location of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/> if it is a shortcut.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.SmallImage">
            <summary>
            Gets the small icon of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.MediumImage">
            <summary>
            Gets the medium icon of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.LargeImage">
            <summary>
            Gets the large icon of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.ExtraLargeImage">
            <summary>
            Gets the extra large icon of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.Name">
            <summary>
            Gets the selected file name, excluding the path.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.Label">
            <summary>
            Gets the volume label of a Drive. For non-drives, it returns the Name.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsInEditMode">
            <summary>
            Gets or sets whether the file or folder name is currently being edited.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsSelected">
            <summary>
            Gets or sets whether the file or folder name is currently selected.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsCut">
            <summary>
            Gets or sets whether the file or folder name is cut from main pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsCutFromTreeView">
            <summary>
            Gets or sets whether the file or folder name is cut from tree view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsHidden">
            <summary>
            Gets a value indicating whether the file or folder name is hidden.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.IsSystemHiddenActual">
            <summary>
            Gets a value indicating whether the file / folder is hidden and is system file / folder and is not Drive.
            Drives have Hidden but should be always visible in navigation tree.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.LastWriteTime">
            <summary>
            Gets the time when the current file or directory was last written to.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.Path">
            <summary>
            Gets the selected file name, including the path.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.SortOrder">
            <summary>
            Gets or a value indicating the sort order of this file system info entry.
            This is used when sorting in GridView.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.IconSize">
            <summary>
            Options to specify the size of icons to return.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.IconSize.Small">
            <summary>
            Specifies small icon - 16 pixels by 16 pixels.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.IconSize.Medium">
            <summary>
            Specifies medium icon - 32 pixels by 32 pixels.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.IconSize.Large">
            <summary>
            Specifies large icon - 48 pixels by 48 pixels.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.IconSize.ExtraLarge">
            <summary>
            Specifies extra large icon - 256 pixels by 256 pixels. (Vista+) and fails on XP or lower.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ServerDirectory">
            <summary>
            Class representing a server folder - special network folder which contains shared network folders.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ServerDirectory.#ctor(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase,Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.ServerDirectory"/> class.
            </summary>
            <param name="factory">FileFactory for creating child <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper" />instances.</param>
            <param name="parent">The parent <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ServerDirectory.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ServerDirectory.TypeText">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ServerDirectory.TryLoadServerPathAsChildDirectoryInfo(System.String)">
            <summary>
            Some server folders might not be returned by <see cref="M:Telerik.Windows.Controls.FileDialogs.ServerHelper.GetSharedFolders(System.String,Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase)"/> due to denied access / other network error.
            Still, direct child folder might be accessed via DirectoryInfo creation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.SharedLocationType">
            <summary>
            The type of shared location.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.SharedLocationType.Disk">
            <summary>
            Disk type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.SharedLocationType.Printer">
            <summary>
            Printer type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.SharedLocationType.Device">
            <summary>
            Device type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.SharedLocationType.IPC">
            <summary>
            IPC type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.SharedLocationType.Special">
            <summary>
            Special type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.SharedLocationInfoFull">
            <summary>
            Shared location information, for NT based OS, first type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.SharedLocationInfoShort">
            <summary>
            Shared location information, for NT based OS, second type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellDirectory">
            <summary>
            Class responsible for representing network computers.
            These models should be children of <see cref="T:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory"/> and their children should be <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryInfoWrapper"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellDirectory.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Windows.Controls.FileDialogs.DirectoryBaseWrapper"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellDirectory.Size">
            <summary>
            This property is not implemented for directories.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellDirectory.TypeText">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory">
            <summary>
            Class which represents the root node of the network. It is responsible for reloading network PCs.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory.Parent">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory.Path">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory.Name">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellNetworkRootDirectory.Children">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ContextMenuHelper.GetCommandStringA(Telerik.Windows.Controls.FileDialogs.ShellHelpers.IContextMenu,System.UInt32,System.Boolean)">
            <summary>
            Retrieves the command string for a specific item from an iContextMenu (ANSI).
            </summary>
            <param name="icontextMenu">The IContextMenu to receive the string from.</param>
            <param name="idcmd">The id of the specific item.</param>
            <param name="executeString">Indicating whether it should return an execute string or not.</param>
            <returns>If executeString is true it will return the executeString for the item, otherwise it will return the help info string.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ContextMenuHelper.GetCommandStringW(Telerik.Windows.Controls.FileDialogs.ShellHelpers.IContextMenu,System.UInt32,System.Boolean)">
            <summary>
            Retrieves the command string for a specific item from an iContextMenu (Unicode).
            </summary>
            <param name="icontextMenu">The IContextMenu to receive the string from.</param>
            <param name="idcmd">The id of the specific item.</param>
            <param name="executeString">Indicating whether it should return an execute string or not.</param>
            <returns>If executeString is true it will return the executeString for the item, otherwise it will return the help info string.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NaturalStringComparer">
            <summary>
            Uses the file name comparison methods used in Windows.
            For example 'new folder 10' comes after 'new folder 9'.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NaturalStringComparer.Instance">
            <summary>
            The single <see cref="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NaturalStringComparer"/> instance that is ready to use.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NaturalStringComparer.Compare(Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper,Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper)">
            <summary>
            Wraps the <see cref="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.StrCmpLogicalW(System.String,System.String)"/> method. 
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.IconManager">
            <summary>
            Provides icons for files and folders.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.IconManager.GetMyComputerIcon(Telerik.Windows.Controls.FileDialogs.IconSize)">
            <summary>
            Gets an <see cref="T:System.Windows.Media.ImageSource"/> for the "My Computer" icon.
            </summary>
            <returns></returns>      
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.IconManager.GetIcon(System.String,Telerik.Windows.Controls.FileDialogs.IconSize,System.Nullable{System.Boolean})">
            <summary>
            Gets an <see cref="T:System.Windows.Media.ImageSource"/> for the icon for the specified path and specified <see cref="T:Telerik.Windows.Controls.FileDialogs.IconSize"/>.
            </summary>
            <param name="fileName">The file path.</param>
            <param name="size">The <see cref="T:Telerik.Windows.Controls.FileDialogs.IconSize"/>.</param>
            <param name="isDir">Boolean value indicating the type of icon, used for performance optimization of <see cref="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.SHGetFileInfo(System.String,System.UInt32,Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHFILEINFO@,System.Int32,Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHGFI)"/>.</param> 
            <returns>ImageSource result.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuManager.ShowContextMenu(System.Collections.Generic.IList{Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapper},Telerik.Windows.Controls.FileDialogs.ExplorerControl,System.Windows.Media.Visual,System.Boolean,System.Boolean,System.Boolean,Telerik.Windows.Controls.FileDialogs.LayoutType)">
            <summary>
            Show shell context menu.
            </summary>
            <param name="selectedFileSystemInfoWrappers">Selected FileSystemInfoWrappers.</param>
            <param name="explorer">Explorer control.</param> 
            <param name="hwndSource">HWND source.</param>
            <param name="canRename">Can rename.</param>
            <param name="shouldInvokeOpenCommand">Should invoke open command.</param>
            <param name="shouldOpenContextMenuOnEmptySpaces">Should open context menu on empty spaces.</param>
            <param name="selectedLayoutType">Selected layout type.</param>
            <returns>Shell contextMenu command.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.TPM">
            <summary>
             Specifies how TrackPopupMenuEx positions the shortcut menu horizontally.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.CSIDL">
            <summary>
            Used to retrieve directory paths to system special folders.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.CMIC">
            <summary>
            Flags used with the CMINVOKECOMMANDINFOEX structure.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.CMF">
            <summary>
             Specifies how the shortcut menu can be changed when calling IContextMenu::QueryContextMenu.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.MFT">
            <summary>
             Specifies the content of the new menu item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.MIIM">
            <summary>
             Specifies the content of the new menu item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.MFS">
            <summary>
             Specifies the state of the new menu item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.SFGAO">
            <summary>
            The attributes that the caller is requesting, when calling IShellFolder::GetAttributesOf.
            </summary> 
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHGNO">
            <summary>
            Defines the values used with the IShellFolder::GetDisplayNameOf and IShellFolder::SetNameOf 
            methods to specify the type of file or folder names used by those methods.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.GCS">
            <summary>
             Flags specifying the information to return when calling IContextMenu::GetCommandString.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.FILE_ATTRIBUTE">
            <summary>
            Flags that specify the file information to retrieve with SHGetFileInfo.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHGFI">
            <summary>	
            Flags that specify the file information to retrieve with SHGetFileInfo.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHIL">
            <summary>	
            Flags that specify the file information to retrieve with SHGetImageList.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.MENUITEMINFO">
            <summary>
            Contains information about a menu item.
            </summary> 
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.SHFILEINFO">
            <summary>
             Contains information about a file object.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.CMINVOKECOMMANDINFOEX">
            <summary>
             Contains extended information about a shortcut menu command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.StrCmpLogicalW(System.String,System.String)">
            <summary>
            Compares two file name strings in natural order used in windows file system.
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.NetShareEnum(System.String,System.Int32,System.IntPtr@,System.Int32,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            Enumerate shares in NT based windows.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.NetApiBufferFree(System.IntPtr)">
            <summary>
            Frees buffer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.ShellHelpers.NativeMethods.OpenDirectory(System.String)">
            <summary>
            Opens a directory, returns it's handle or zero.
            </summary>
            <param name="dirPath">Path to the directory, e.g. "C:\\dir".</param>
            <returns>Handle to the directory. Close it with CloseHandle().</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand">
            <summary>
            Selected Shell ContextMenu command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Rename">
            <summary>
            Rename command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Copy">
            <summary>
            Copy command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Cut">
            <summary>
            Cut command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Paste">
            <summary>
            Paste command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Delete">
            <summary>
            Delete command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Open">
            <summary>
            Open command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Tiles">
            <summary>
            Tiles command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.SmallIcons">
            <summary>
            Small Icons command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.MediumIcons">
            <summary>
            Medium Icons command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.LargeIcons">
            <summary>
            Large Icons command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.ExtraLargeIconsIcons">
            <summary>
            Extra Large Icons command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.List">
            <summary>
            List command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Details">
            <summary>
            Details command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.Properties">
            <summary>
            Properties command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.PasteShortCut">
            <summary>
            PasteShortCut command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.ShellHelpers.ShellContextMenuCommand.NewFolder">
            <summary>
            NewFolder command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.RadFilePathPickerCommands">
            <summary>
            Static class listing all public <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/> commands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.RadFilePathPickerCommands.CommandId.ShowDialog">
            <summary>
            Shows the file or folder dialog of the <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.RadFilePathPickerCommands.CommandId.Clear">
            <summary>
            Clears the text from the watermark textbox.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.RadFilePathPickerCommands.ShowDialog">
            <summary>
            Gets the ShowDialog command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.RadFilePathPickerCommands.Clear">
            <summary>
            Gets the Clear command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.Converters.DialogTypeToWatermarkContentConverter">
            <summary>
            Provides the watermark content based on the DialogType property and the current culture.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.Converters.DialogTypeToWatermarkContentConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Gets the localized watermark content based on the DialogType property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.Converters.DialogTypeToWatermarkContentConverter.ConvertBack(System.Object,System.Type[],System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.Converters.FilePathToIconImageSourceConverter">
            <summary>
            Class for converting an existing file path to ImageSource corresponding to the file path image from the file system.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.Converters.FilePathToIconImageSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a given file path to ImageSource of the file.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.Converters.FilePathToIconImageSourceConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DialogType">
            <summary>
            Represents the type of the dialog the <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/> can use.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DialogType.OpenFile">
            <summary>
            <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/> is used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DialogType.OpenFolder">
            <summary>
            <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> is used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FileDialogs.DialogType.SaveFile">
            <summary>
            <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> is used.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.BrowseFileHelper">
            <summary>
            Helper class for <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.BrowseFileHelper.GetIcon(System.String)">
            <summary>
            Gets the windows icon for the given file path type.
            </summary>
            <param name="filePath">The path to the file.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.DialogOpeningEventArgs">
            <summary>
            Event args for the <see cref="E:Telerik.Windows.Controls.RadFilePathPicker.DialogOpening"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.DialogOpeningEventArgs.#ctor(Telerik.Windows.Controls.DialogWindowBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.DialogOpeningEventArgs"/> class.
            </summary>
            <param name="dialog"></param>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogOpeningEventArgs.Dialog">
            <summary>
            Gets the file / folder dialog instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.DialogOpeningEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the open operation should be cancelled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FilePathChangedEventArgs">
            <summary>
            Event arguments for the <see cref="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanged"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FilePathChangedEventArgs.#ctor(System.Windows.RoutedEvent,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FilePathChangedEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FilePathChangedEventArgs.FilePath">
            <summary>
            Gets the file path that is about to be selected.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FileDialogs.FilePathChangingEventArgs">
            <summary>
            Event arguments for the <see cref="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanging"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FileDialogs.FilePathChangingEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FileDialogs.FilePathChangingEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FileDialogs.FilePathChangingEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the changing of the FilePath property should be cancelled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DialogWindowBase">
            <summary>
            Base class for file and folder dialogs.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.ResponseButtonProperty">
            <summary>
            Identifies the <see>ResponseButton</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.DialogResultProperty">
            <summary>
            Identifies the <see>DialogResult</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.BorderBackgroundProperty">
            <summary>
            Identifies the <see>BorderBackground</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.IconProperty">
            <summary>
            Identifies the <see>Icon</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.IconTemplateProperty">
            <summary>
            Identifies the <see>IconTemplate</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.DialogStartupLocationProperty">
            <summary>
            Identifies the <see>DialogStartupLocation</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.IsDragDropEnabledProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DialogWindowBase.IsDragDropEnabled"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.DragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DialogWindowBase.DragDropBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.ExpandToCurrentDirectoryProperty">
            <summary>
            Identifies the <see>ExpandToCurrentDirectory</see> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.ShowHiddenFilesProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DialogWindowBase.ShowHiddenFiles"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.LoadDrivesInBackgroundProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DialogWindowBase.LoadDrivesInBackground"/> dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DialogWindowBase.CanUserRenameProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DialogWindowBase.CanUserRename"/> dependency property. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.Closed">
            <summary>
            Occurs when the DialogWindowBase is closed.
            </summary>
            <remarks>
            The event is raised right next after the <see>PreviewClosed</see> event. Both events are raise by
            <see>Close</see> method.
            </remarks>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.PreviewClosed">
            <summary>
            Occurs when the DialogWindowBase is closing.
            </summary>
            <remarks>
            The event is raised whenever the <see>Close</see> method is invoked.
            </remarks>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.ExceptionRaised">
            <summary>
            Occurs when the <see cref="T:System.Exception"/> is raised.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.DirectoryRequesting">
            <summary>
            Occurs when the DirectoryInfo wrapper is about to be created.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.DirectoryNavigating">
            <summary>
            Occurs when the current folder is about to change.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.ShellContextMenuOpening">
            <summary>
            Occurs when context menu is about to open.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DialogWindowBase.Renaming">
            <summary>
            Occurs when a file or directory is about to enter edit mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.FileName">
            <summary>
            Gets or sets a string containing the selected file name, including the path.
            Short file name without the full path is not supported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.ShowNetworkLocations">
            <summary>
            Gets or sets a value indicating whether the dialog shows network computers in the navigation tree.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.LoadDrivesInBackground">
            <summary>
            Gets or sets a value indicating whether the control loads the drives under 'This PC' node in background thread.
            Could be used in scenarios when some drives are expected to load slower than normal.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.InitialDirectory">
            <summary>
            Gets or sets the initial directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.RestoreDirectory">
            <summary>
            Gets or sets a value indicating whether the dialog box restores the directory to the previously selected directory before closing.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.CustomPlaces">
            <summary>
            Gets or sets the collection of custom places.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.DialogResult">
            <summary>
            Gets or sets a DialogResult value for the <Strong>RadWindow</Strong>. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.BorderBackground">
            <summary>
            Gets or sets a brush to be used to draw the title of <Strong>DialogWindow</Strong>.
            This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.Icon">
            <summary>
            Gets or sets the Icon image to appear in the <Strong>RadWindow</Strong> title bar. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.IconTemplate">
            <summary>
            Gets or sets the IconTemplate property. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.DialogStartupLocation">
            <summary>
            Gets or sets a DialogStartupLocation value for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.IsDragDropEnabled">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> allows drag-drop operations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.DragDropBehavior">
            <summary>
            Gets or sets the <see cref="T:Telerik.Windows.Controls.FileDialogs.DragDrop.FileBrowserDragDropBehavior"/> for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.ExpandToCurrentDirectory">
            <summary>
            Gets or sets a value indicating whether the main navigation pane expands and brings into view the current directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.ShowHiddenFiles">
            <summary>
            Gets or sets a boolean value indicating whether hidden files and folders should be visible in the Dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.CanUserRename">
            <summary>
            Gets or sets a value indicating whether the user can rename file or folders via mouse click or keyboard F2 press in main pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.InitialSelectedLayout">
            <summary>
            Gets or sets the initial <see cref="T:Telerik.Windows.Controls.FileDialogs.LayoutType"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DialogWindowBase.FileSystemInfoWrapperFactory">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FileDialogs.FileSystemInfoWrapperFactoryBase"/> for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetParentDialogWindow(System.Windows.DependencyObject)">
            <summary>  
            This walks the visual tree for a parent of a specific type.
            </summary>  
            <param name="child">The object which parent is searched.</param>  
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetResponseButton(System.Windows.DependencyObject)">
            <summary>
            Gets the response button.
            </summary>
            <param name="button">The button.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.SetResponseButton(System.Windows.DependencyObject,Telerik.Windows.Controls.ResponseButton)">
            <summary>
            Sets the response button.
            </summary>
            <param name="button">The button.</param>
            <param name="value">The ResponseButton value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnApplyTemplate">
            <summary>
                When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) 
                call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.ShowDialog">
            <summary>
            Opens modal <strong>DialogWindow</strong> in case it hasn't been already opened.
            </summary>
            <returns>The DialogResult value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.ShowDialogInternal(System.Boolean,System.Boolean)">
            <summary>
            Show for testing purposes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetShouldUpdateActiveState">
            <summary>
            Indicates whether IsInActiveState property should be updated when dialog is deactivated.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetDialogViewModel">
            <summary>
            Gets the DialogViewModel that will be used as DataContext for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetContent">
            <summary>
            Gets the content for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.ShouldFocusOnActivate">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OpenFileInternal(System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Creates a <see cref="T:System.IO.FileStream"/> with the specified <see cref="T:System.IO.FileMode"/> and <see cref="T:System.IO.FileAccess"/>.
            </summary>
            <exception cref="T:System.ArgumentNullException">Selected file is null.</exception>
            <exception cref="T:System.ArgumentException">Selected file is empty string, contains only white space, contains one or more invalid characters, or refers to a non-file device.</exception>
            <exception cref="T:System.NotSupportedException">Selected file refers to a non-file device.</exception>
            <exception cref="T:System.FileNotFoundException">Selected file cannot be found, such as when mode is FileMode.Truncate or FileMode.Open, and the file specified by path does not exist. The file must already exist in these modes.</exception>
            <exception cref="T:System.IOException">An I/O error occurred or the stream has been closed.</exception>
            <exception cref="T:System.SecurityException">The caller does not have the required permission.</exception>
            <exception cref="T:System.DirectoryNotFoundException">Selected file is invalid, such as being on an unmapped drive.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The access requested is not permitted by the operating system for the specified path, such as when access is Write or ReadWrite and the file or directory is set for read-only access.</exception>
            <exception cref="T:System.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The specified mode contains an invalid value.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">Indicates whether transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetDialogViewModelCustomPlaces">
            <summary>
            Provides the CustomPlaces as list of DirectoryInfoWrappers to the DialogViewModel of this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetDialogViewModelInitialDirectory">
            <summary>
            Provides the InitialDirectory as DirectoryInfoWrapper to the <see cref="T:Telerik.Windows.Controls.FileDialogs.DialogViewModel"/> of this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> instance.
            Returns null if DirectoryInfo cannot be created due to incorrect InitialDirectory string or access rights.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetWindowOwnerHandle">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnPreviewClosed(Telerik.Windows.Controls.WindowPreviewClosedEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewClose"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.WindowPreviewClosedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnClosed(Telerik.Windows.Controls.WindowClosedEventArgs)">
            <summary>
            Raises the <see cref="E:Closed"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.WindowClosedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.SaveStateOnAccept">
            <summary>
            Save information from the <see cref="T:Telerik.Windows.Controls.FileDialogs.OpenFileDialogViewModel"/> into this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/> instance before the window is closed on Accept.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.GotFocus"/> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnLostFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.LostFocus"/> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.KeyDown"/> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnClosing">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnPreviewClosed">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetWindowOwner">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.GetWindowStartupLocation">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnClosed">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.RaiseDirectoryRequesting(Telerik.Windows.Controls.FileDialogs.DirectoryRequestingEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.DirectoryRequesting"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.RaiseExceptionRaisedEvent(Telerik.Windows.Controls.FileDialogs.FileBrowserExceptionRaisedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.ExceptionRaised"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.RaiseDirectoryNavigatinEvent(Telerik.Windows.Controls.FileDialogs.DirectoryNavigatingEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.DirectoryNavigating"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DialogWindowBase.RaiseShellContextMenuOpening(Telerik.Windows.Controls.FileDialogs.ContextMenuOpeningEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.DialogWindowBase.ShellContextMenuOpening"/> event.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadOpenFileDialog">
            <summary>
            Represents a dialog that allows user selection of filename for a file to be opened.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadOpenFileDialog"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.DefaultExt">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.Filter">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.FilterIndex">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.Multiselect">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.ReadOnlyChecked">
            <summary>
            Gets or sets a value indicating whether the read-only check box is selected.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.ShowReadOnly">
            <summary>
            Gets or sets a value indicating whether the dialog contains a read-only check box.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.FileNames">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.SafeFileNames">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFileDialog.DereferenceLinks">
            <summary>
            Gets or sets a value indicating whether a file dialog returns either the location
            of the file referenced by a shortcut or the location of the shortcut file.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.OpenFile">
            <summary>Opens a read-only stream for the file that is selected by the user using RadOpenFileDialog />.</summary>
            <returns>A new <see cref="T:System.IO.Stream" /> that contains the selected file.</returns>
            <exception cref="T:System.ArgumentNullException">Selected file is null.</exception>
            <exception cref="T:System.ArgumentException">Selected file is empty string, contains only white space, contains one or more invalid characters, or refers to a non-file device.</exception>
            <exception cref="T:System.NotSupportedException">Selected file refers to a non-file device.</exception>
            <exception cref="T:System.FileNotFoundException">Selected file cannot be found, such as when mode is FileMode.Truncate or FileMode.Open, and the file specified by path does not exist. The file must already exist in these modes.</exception>
            <exception cref="T:System.IOException">An I/O error occurred or the stream has been closed.</exception>
            <exception cref="T:System.SecurityException">The caller does not have the required permission.</exception>
            <exception cref="T:System.DirectoryNotFoundException">Selected file is invalid, such as being on an unmapped drive.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The access requested is not permitted by the operating system for the specified path, such as when access is Write or ReadWrite and the file or directory is set for read-only access.</exception>
            <exception cref="T:System.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The specified mode contains an invalid value.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.OnOpenExecuted">
            <summary>
            Called when the Open command is executed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.GetDialogViewModel">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.GetViewModel">
            <summary>
            Gets the OpenFileDialogViewModel that will be used as DataContext for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.GetContent">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFileDialog.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.RadSaveFileDialog">
            <summary>
            Represents a dialog that allows user selection of filename for file to be saved.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadSaveFileDialog"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSaveFileDialog.DefaultExt">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSaveFileDialog.Filter">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSaveFileDialog.FilterIndex">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.OpenFile">
            <summary>Creates a read-write file stream for the filename selected by the user using RadSaveFileDialog" />.</summary>
            <returns>A new <see cref="T:System.IO.Stream" /> that contains the selected file.</returns>
            <exception cref="T:System.ArgumentNullException">Selected file is null.</exception>
            <exception cref="T:System.ArgumentException">Selected file is empty string, contains only white space, contains one or more invalid characters, or refers to a non-file device.</exception>
            <exception cref="T:System.NotSupportedException">Selected file refers to a non-file device.</exception>
            <exception cref="T:System.FileNotFoundException">Selected file cannot be found, such as when mode is FileMode.Truncate or FileMode.Open, and the file specified by path does not exist. The file must already exist in these modes.</exception>
            <exception cref="T:System.IOException">An I/O error occurred or the stream has been closed.</exception>
            <exception cref="T:System.SecurityException">The caller does not have the required permission.</exception>
            <exception cref="T:System.DirectoryNotFoundException">Selected file is invalid, such as being on an unmapped drive.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The access requested is not permitted by the operating system for the specified path, such as when access is Write or ReadWrite and the file or directory is set for read-only access.</exception>
            <exception cref="T:System.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The specified mode contains an invalid value.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.GetDialogViewModel">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.GetContent">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSaveFileDialog.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FilePathPickerIconTemplateSelector">
            <summary>
            Selects <see cref="T:System.Windows.DataTemplate"/> for the icon based on the FilePath property of the <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/> control.
            If null or empty string is provided - EmptyTemplate is used, otherwise - NonEmptyTemplate.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FilePathPickerIconTemplateSelector.NonEmptyTemplate">
            <summary>
            Icon template for existing file path.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FilePathPickerIconTemplateSelector.EmptyTemplate">
            <summary>
            Icon template for empty file path.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FilePathPickerIconTemplateSelector.SelectTemplate(System.Object,System.Windows.DependencyObject)">
            <summary>
            Selects DataTemplate based on the icon object provided.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadFilePathPicker">
            <summary>
            Control used for choosing a valid file / folder path via file / folder dialog or via typing in watermark text box.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.FilePathProperty">
            <summary>
            Initializes the FilePath property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.DialogTypeProperty">
            <summary>
            Initializes the DialogType property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.WatermarkContentProperty">
            <summary>
            Identifies the WatermarkContent dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.WatermarkTemplateProperty">
            <summary>
            Identifies the WatermarkTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.ShowDialogButtonContentProperty">
            <summary>
            Identifies the ShowDialogButtonContent dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.ShowDialogButtonTemplateProperty">
            <summary>
            Identifies the ShowDialogButtonTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.ClearButtonContentProperty">
            <summary>
            Identifies the ClearButtonContent dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.ClearButtonTemplateProperty">
            <summary>
            Identifies the ClearButtonTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.TextProperty">
            <summary>
            Identifies the Text dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.IconTemplateSelectorProperty">
            <summary>
            Identifies the IconTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.IsReadOnlyProperty">
            <summary>
            Identifies the IsReadOnly dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.IconVisibilityProperty">
            <summary>
            Identifies the IconVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.EditorVisibilityProperty">
            <summary>
            Identifies the EditorVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.FilePathChangingEvent">
            <summary>
            Registers the <see cref="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanging"/> routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadFilePathPicker.FilePathChangedEvent">
            <summary>
            Registers the <see cref="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanged"/> routed event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadFilePathPicker"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadFilePathPicker.DialogOpening">
            <summary>
            Occurs when the file dialog for is about to be opened.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadFilePathPicker.DialogClosed">
            <summary>
            Occurs when the file dialog is closed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanging">
            <summary>
            Occurs when a new file path is about to be selected. User can cancel it and / or override its ImageSource used for the file path icon.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadFilePathPicker.FilePathChanged">
            <summary>
            Occurs when a new valid file path is selected via file browser, via typing or via programmatic change of the property <see cref="P:Telerik.Windows.Controls.RadFilePathPicker.FilePath"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.FilePath">
            <summary>
            Gets or sets the path to a given file or folder.
            This property accepts only a valid (existing) file / folder path.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.DialogType">
            <summary>
            Gets or sets a value indicating the type of the dialog that is opened by the show dialog button.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.WatermarkContent">
            <summary>
            Gets or sets the content to be shown when the TextBox is empty and not focused.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.WatermarkTemplate">
            <summary>
            Gets or sets the template for presenting the content, shown when the TextBox is empty and not focused.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.ShowDialogButtonContent">
            <summary>
            Gets or sets the content of the show dialog button.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.ShowDialogButtonTemplate">
            <summary>
            Gets or sets the content template of the show dialog button.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.ClearButtonContent">
            <summary>
            Gets or sets the content of the clear button.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.ClearButtonTemplate">
            <summary>
            Gets or sets the content template of the clear button.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.Text">
            <summary>
            Gets or sets the text of the textbox.
            If the text is a valid file / folder path, <see cref="P:Telerik.Windows.Controls.RadFilePathPicker.FilePath"/> property will be updated.
            Otherwise FilePath is set to empty string.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.IconTemplateSelector">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Controls.DataTemplateSelector"/> which chooses icon template based on the given <see cref="P:Telerik.Windows.Controls.RadFilePathPicker.FilePath"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.IsReadOnly">
            <summary>
            Gets or sets a value indicating whether the watermark textbox is in read-only mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.IconVisibility">
            <summary>
            Gets or sets a value indicating the visibility of the file path icon.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadFilePathPicker.EditorVisibility">
            <summary>
            Gets or sets a value indicating the visibility of the watermark textbox.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.ShowDialog">
            <summary>
            Opens the file / folder dialog for choosing a file path.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.OnFilePathChanged">
            <summary>
            Called when <see cref="P:Telerik.Windows.Controls.RadFilePathPicker.FilePath"/> property is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadFilePathPicker.OnShowDialog">
            <summary>
            Called when the file dialog is about to be opened.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadOpenFolderDialog">
            <summary>
            Represents a dialog that allows user selection of folders.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadOpenFolderDialog"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFolderDialog.Multiselect">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFolderDialog.FileNames">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.RadOpenFolderDialog.SafeFileNames">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.OnOpenExecuted">
            <summary>
            Called when the Open command is executed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.GetViewModel">
            <summary>
            Gets the OpenFolderDialogViewModel that will be used as DataContext for this <see cref="T:Telerik.Windows.Controls.DialogWindowBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.GetDialogViewModel">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.GetContent">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.OnInitialized(System.EventArgs)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadOpenFolderDialog.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:FileDialogs.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:FileDialogs.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:FileDialogs.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
    </members>
</doc>
