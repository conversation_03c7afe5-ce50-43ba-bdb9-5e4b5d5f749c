﻿using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System;
using System.Threading.Tasks;
using System.Windows;
using MousePosition = Everylang.App.Utilities.MousePosition;
using Point = System.Windows.Point;

namespace Everylang.App.View.Controls.SmartClick.ShortLink
{
    /// <summary>
    /// Interaction logic for ShortLinkPopup.xaml
    /// </summary>
    internal partial class ShortLinkPopup
    {
        private string? _text;
        private bool _isKeyboard;

        internal ShortLinkPopup(string? text)
        {
            InitializeComponent();
            _text = text;
            TextBlockText.Text = _text;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
        }

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                IsOpen = false;
                e.Handled = true;
            }
            if (_isKeyboard)
            {
                if (e.KeyCode == VirtualKeycodes.F1)
                {
                    if (_text != null) System.Windows.Forms.Clipboard.SetText(_text);
                    e.Handled = true;
                    IsOpen = false;
                }
                if (e.KeyCode == VirtualKeycodes.F2)
                {
                    SendText.SendStringByPaste(_text, false);
                    e.Handled = true;
                    IsOpen = false;
                }
            }
        }


        internal async void Show(bool isKeyboard)
        {
            _isKeyboard = isKeyboard;
            IsOpen = true;
            await Task.Delay(100);
            System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
            if (_isKeyboard)
            {
                HorizontalOffset = centrePos.X - 50;
                VerticalOffset = centrePos.Y;
                TextBlockF1.Visibility = Visibility.Visible;
                TextBlockF2.Visibility = Visibility.Visible;
            }
            else
            {
                var curretPos = MousePosition.GetMousePoint();
                if (curretPos.X != 0 && curretPos.Y != 0)
                {
                    var pos = MousePosition.GetMousePoint(new Point(curretPos.X, curretPos.Y));
                    HorizontalOffset = pos.X + 20;
                    VerticalOffset = pos.Y - 20;
                }
                else
                {
                    HorizontalOffset = centrePos.X - 50;
                    VerticalOffset = centrePos.Y;
                }
            }
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                IsOpen = false;
            }
        }

        private void Copy(object sender, RoutedEventArgs e)
        {
            if (_text != null) System.Windows.Forms.Clipboard.SetText(_text);
            IsOpen = false;
        }

        private void Replace(object sender, RoutedEventArgs e)
        {
            SendText.SendStringByPaste(_text, false);
            IsOpen = false;
        }

        private void ShortLinkPopup_OnClosed(object sender, EventArgs e)
        {
            HookCallBackMouseDown.CallbackEventHandler -= HookManagerMouseDown;
        }
    }
}
