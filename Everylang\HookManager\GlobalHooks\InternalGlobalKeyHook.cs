﻿using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

//Code based from the answer of <PERSON><PERSON><PERSON><PERSON> at https://stackoverflow.com/questions/604410/global-keyboard-capture-in-c-sharp-application and https://stackoverflow.com/questions/11607133/global-mouse-event-handler
namespace Everylang.App.HookManager.GlobalHooks
{

    class GlobalKeyboardHookEventArgs : HandledEventArgs
    {
        internal InternalGlobalKeyHook.KeyboardState KeyboardState { get; private set; }
        internal InternalGlobalKeyHook.LowLevelKeyboardInputEvent KeyboardData { get; private set; }

        internal GlobalKeyboardHookEventArgs(InternalGlobalKeyHook.LowLevelKeyboardInputEvent keyboardData, InternalGlobalKeyHook.KeyboardState keyboardState)
        {
            KeyboardData = keyboardData;
            KeyboardState = keyboardState;
        }
    }

    //Based on https://gist.github.com/Stasonix
    class InternalGlobalKeyHook
    {
        private static User32.SafeHHOOK? _hookId;
        internal event EventHandler<GlobalKeyboardHookEventArgs> KeyboardPressed;

        internal InternalGlobalKeyHook()
        {
            _hookId = GlobalHook.CreateKeyboardHook(LowLevelKeyboardProc);
        }


        [StructLayout(LayoutKind.Sequential)]
        internal struct LowLevelKeyboardInputEvent
        {
            /// <summary>
            /// A virtual-key code. The code must be a value in the range 1 to 254.
            /// </summary>
            internal VirtualKeycodes VirtualCode;

            /// <summary>
            /// A hardware scan code for the key. 
            /// </summary>
            internal int HardwareScanCode;

            /// <summary>
            /// The extended-key flag, event-injected Flags, context code, and transition-state flag. This member is specified as follows. An application can use the following values to test the keystroke Flags. Testing LLKHF_INJECTED (bit 4) will tell you whether the event was injected. If it was, then testing LLKHF_LOWER_IL_INJECTED (bit 1) will tell you whether or not the event was injected from a process running at lower integrity level.
            /// </summary>
            internal int Flags;

            /// <summary>
            /// The time stamp stamp for this message, equivalent to what GetMessageTime would return for this message.
            /// </summary>
            internal int TimeStamp;

            /// <summary>
            /// Additional information associated with the message. 
            /// </summary>
            internal IntPtr AdditionalInformation;
        }


        //const int HC_ACTION = 0;

        internal enum KeyboardState
        {
            KeyDown = 0x0100,
            KeyUp = 0x0101,
            SysKeyDown = 0x0104,
            SysKeyUp = 0x0105
        }


        internal IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam)
        {

            bool fEatKeyStroke = false;

            var wparamTyped = wParam.ToInt32();
            if (nCode < 0)
            {
                if (_hookId != null) return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
            }

            object? o = Marshal.PtrToStructure(lParam, typeof(LowLevelKeyboardInputEvent));
            if (o != null)
            {
                LowLevelKeyboardInputEvent p = (LowLevelKeyboardInputEvent)o;
                var eventArguments = new GlobalKeyboardHookEventArgs(p, (KeyboardState)wparamTyped);
                EventHandler<GlobalKeyboardHookEventArgs> handler = KeyboardPressed;
                handler?.Invoke(this, eventArguments);
                fEatKeyStroke = eventArguments.Handled;
            }

            if (_hookId != null)
                return fEatKeyStroke ? (IntPtr)(-1) : User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
            return IntPtr.Zero;
        }
    }

}