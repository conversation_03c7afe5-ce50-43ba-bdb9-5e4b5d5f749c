<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.Expressions</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer">
            <summary>
            An AutomationPeer type for the ExpressionTextBox type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.#ctor(Telerik.Windows.Controls.Expressions.ExpressionTextBox)">
            <summary>
             Initializes a new instance of the ExpressionTextBoxAutomationPeer class.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Windows.Controls.Expressions.ExpressionTextBox"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.OwningExpressionTextBox">
            <summary>
            Gets the owning <see cref="T:Telerik.Windows.Controls.Expressions.ExpressionTextBox"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the automation control type.
            </summary>
            <returns>The group automation control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetClassNameCore">
            <summary>
            Returns the class name.
            </summary>
            <returns>The string "ExpressionTextBox".</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName"/>.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetAutomationIdCore">
            <summary>
            Gets the string that uniquely identifies the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. Called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationId"/>.
            </summary>
            <returns>
            The automation identifier for the element associated with the <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>, or <see cref="F:System.String.Empty"/> if there isn't an automation identifier.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetHelpTextCore">
            <summary>
            Gets the description of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            An <see cref="F:System.String.Empty"/> string.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ExpressionTextBoxAutomationPeer.GetItemStatusCore">
            <summary>
            Gets a string that communicates the visual status of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetItemStatus"/>.
            </summary>
            <returns>
            The string that contains the <see cref="P:System.Windows.Automation.AutomationProperties.ItemStatus"/> that is returned by <see cref="M:System.Windows.Automation.AutomationProperties.GetItemStatus(System.Windows.DependencyObject)"/>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer">
            <summary>
            An AutomationPeer type for the RadExpressionEditor type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.#ctor(Telerik.Windows.Controls.RadExpressionEditor)">
            <summary>
             Initializes a new instance of the RadExpressionEditorAutomationPeer class.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.OwningExpressionEditor">
            <summary>
            Gets the owning <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the automation control type.
            </summary>
            <returns>The group automation control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetClassNameCore">
            <summary>
            Returns the class name.
            </summary>
            <returns>The string "RadExpressionEditor".</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName"/>.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetHelpTextCore">
            <summary>
            Gets the description of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            An <see cref="F:System.String.Empty"/> string.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetItemStatusCore">
            <summary>
            Gets a string that communicates the visual status of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetItemStatus"/>.
            </summary>
            <returns>
            The string that contains the <see cref="P:System.Windows.Automation.AutomationProperties.ItemStatus"/> that is returned by <see cref="M:System.Windows.Automation.AutomationProperties.GetItemStatus(System.Windows.DependencyObject)"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadExpressionEditorAutomationPeer.GetAutomationIdCore">
            <summary>
            Gets the string that uniquely identifies the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. Called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationId"/>.
            </summary>
            <returns>
            The automation identifier for the element associated with the <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>, or <see cref="F:System.String.Empty"/> if there isn't an automation identifier.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.ExpressionTextBox">
            <summary>
            Allows assisted editing of an expression string. Do not use directly.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionTextBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.ExpressionTextBox" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionTextBox.OnApplyTemplate">
            <summary>When overridden in a derived class, is invoked whenever application
            code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            In simplest terms, this means the method is called just before a UI element displays
            in an application. For more information, see Remarks.</summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionTextBox.CachedAutomationId">
            <summary>
            Gets or sets the manually set AutomationId value (in case there is such one).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionTextBox.OnCreateAutomationPeer">
            <summary>
            When implemented in a derived class, returns class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementations for the automation infrastructure.
            </summary>
            <returns>
            The class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> subclass to return.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionTextBox.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionTextBox.SyntaxColors">
            <summary>
            Gets or sets the colors used to highlight different syntax elements in the expression.
            </summary>
            <value>
            The syntax colors used in the expression.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.ExpressionTextBox.SyntaxColorsProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.ExpressionTextBox.SyntaxColors"/> property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.SyntaxColors">
            <summary>
            Defines colors for different syntax elements in an expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.SyntaxColors.StringColor">
            <summary>
            Gets or sets the color for a string literal. Default value is <see cref="P:System.Windows.Media.Colors.Black"/>
            </summary>
            <value>
            The color for a string literal.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.SyntaxColors.StringColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.SyntaxColors.StringColor"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.SyntaxColors.DateTimeColor">
            <summary>
            Gets or sets the color for a date-time literal. Default value is <see cref="P:System.Windows.Media.Colors.Black"/>
            </summary>
            <value>
            The color for a date-time literal.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.SyntaxColors.DateTimeColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.SyntaxColors.DateTimeColor"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.SyntaxColors.MemberColor">
            <summary>
            Gets or sets the color for a member name literal. Default value is <see cref="P:System.Windows.Media.Colors.Black"/>
            </summary>
            <value>
            The color for a member name literal.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.SyntaxColors.MemberColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.SyntaxColors.MemberColor"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.SyntaxColors.FunctionColor">
            <summary>
            Gets or sets the color for a function name literal. Default value is <see cref="P:System.Windows.Media.Colors.Black"/>
            </summary>
            <value>
            The color for a function name literal.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.SyntaxColors.FunctionColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.SyntaxColors.FunctionColor"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.SyntaxColors.DefaultColor">
            <summary>
            Gets or sets the default color. Default value is <see cref="P:System.Windows.Media.Colors.Black"/>
            </summary>
            <value>
            The default color.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.Expressions.SyntaxColors.DefaultColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.Expressions.SyntaxColors.DefaultColor"/> property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.ConstantEditorItemModel">
            <summary>
            Describes a constant item in the expression editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ConstantEditorItemModel.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.ConstantEditorItemModel" /> class.
            </summary>
            <param name="constant">The string representation of the constant.</param>
            <param name="description">The description of the constant.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.EditorItemModel">
            <summary>
            Describes an item in the expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.EditorItemModel.QuickInfo">
            <summary>
            Gets the name of this item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.EditorItemModel.Description">
            <summary>
            Gets the description of the item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.EditorItemModel.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.EditorItemModel" /> class.
            </summary>
            <param name="name">The name of the model.</param>
            <param name="description">The description for the model.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.EditorItemModel.ToString">
            <summary>
            Returns the name of this model.
            </summary>
            <returns>The name of this model.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.EditorCategoryModel">
            <summary>
            Describes a category of items in the expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.EditorCategoryModel.Children">
            <summary>
            Gets the children of this category.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.EditorCategoryModel.ChildCategories">
            <summary>
            Gets the child categories of this category.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.EditorCategoryModel.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.EditorCategoryModel" /> class.
            </summary>
            <param name="name">The name of the category.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.EditorCategoryModel.#ctor(System.String,System.Collections.Generic.IEnumerable{Telerik.Windows.Controls.Expressions.EditorModelBase})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.EditorCategoryModel" /> class.
            </summary>
            <param name="name">The name of the category.</param>
            <param name="children">A collection with the child categories and child items.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.EditorModelBase">
            <summary>
            Describes the basic properties of elements in the expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.EditorModelBase.Name">
            <summary>
            Gets the name of the element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.EditorModelBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.EditorModelBase" /> class.
            </summary>
            <param name="name">The name of the model.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.FieldEditorItemModel">
            <summary>
            Describes a field item in the expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.Parent">
            <summary>
            Gets the parent editor item model of this instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.Children">
            <summary>
            Gets the children of this category.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.QuickInfo">
            <summary>
            Gets a user-friendly text for this field and its type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.ItemPropertyInfo">
            <summary>
            Gets the metadata of the field.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.IsDrillDownEnabled">
            <summary>
            Gets a value indicating whether sub properties will be drilled into.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.#ctor(System.ComponentModel.ItemPropertyInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.FieldEditorItemModel" /> class.
            </summary>
            <param name="itemProperty">The metadata of the field.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.#ctor(System.ComponentModel.ItemPropertyInfo,System.Collections.Generic.IList{Telerik.Windows.Controls.Expressions.EditorModelBase})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.FieldEditorItemModel" /> class.
            </summary>
            <param name="itemProperty">The metadata of the field.</param>
            <param name="children">The child items of the field item model.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FieldEditorItemModel.ToString">
            <summary>
            Returns the expression token value of this model.
            </summary>
            <returns>The expression token value of this model.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel">
            <summary>
            Describes a function item in the expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.QuickInfo">
            <summary>
            Gets a user-friendly text for this function and its arguments.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.MethodInfo">
            <summary>
            Gets the method info represented by this function item mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.Category">
            <summary>
            Gets the category if this item model.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.#ctor(Telerik.Expressions.DefinitionBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel" /> class.
            </summary>
            <param name="definition">The definition of the function.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.#ctor(System.Reflection.MethodInfo,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel" /> class.
            </summary>
            <param name="methodInfo">The method info of the function this item model represents.</param>
            <param name="category">The category of this function item model.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.FunctionEditorItemModel.ToString">
            <summary>
            Returns the expression token value of this model.
            </summary>
            <returns>The expression token value of this model.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.OperatorEditorItemModel">
            <summary>
            Describes an operator item in the expression editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.OperatorEditorItemModel.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.OperatorEditorItemModel" /> class.
            </summary>
            <param name="operatorToken">The string representation of the operator.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel">
            <summary>
            A ViewModel used to loosely-couple components in RadExpressionEditor. Do not use directly.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.PreviewResult">
            <summary>
            Gets the result of the current expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.Categories">
            <summary>
            Gets the categories of editor items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.SelectedCategory">
            <summary>
            Gets or sets the selected category.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.CategoryItems">
            <summary>
            Gets the items in the selected category.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.SelectedItem">
            <summary>
            Gets or sets the selected item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.IsFieldsDrillDownEnabled">
            <summary>
            Gets a value indicating whether drill down in fields is enabled.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GenerateCategories">
            <summary>
            Generates all default categories displayed in the <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/>.
            </summary>
            <returns>A collection of <see cref="T:Telerik.Windows.Controls.Expressions.EditorCategoryModel"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GetFunctionsItemModels">
            <summary>
            Generates the function item models.
            </summary>
            <returns>A collection of <see cref="T:Telerik.Windows.Controls.Expressions.EditorModelBase"/></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GetOperatorsItemModels">
            <summary>
            Generates the operator item models.
            </summary>
            <returns>A collection of <see cref="T:Telerik.Windows.Controls.Expressions.EditorModelBase"/></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GetConstantsItemModels">
            <summary>
            Generates the constant item models.
            </summary>
            <returns>A collection of <see cref="T:Telerik.Windows.Controls.Expressions.EditorModelBase"/></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GetFieldsItemModels(System.Object)">
            <summary>
            Generates the field item models for the provided object.
            </summary>
            <param name="currentItem">The object which properties should be displayed in the fields category.</param>
            <returns>A collection of <see cref="T:Telerik.Windows.Controls.Expressions.EditorModelBase"/></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Expressions.ExpressionEditorViewModel.GetFieldsCategory">
            <summary>
            Creates the fields category model.
            </summary>
            <returns>An instance of <see cref="T:Telerik.Windows.Controls.Expressions.EditorCategoryModel"/>.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Expressions.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Expressions.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadExpressionEditor">
            <summary>
            A control that lets the user input an expression in string form, which it exposes as a LINQ expression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.IsFieldsDrillDownEnabledProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.IsFieldsDrillDownEnabled"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ExpressionProperty">
            <summary>
            Identifies the read-only <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.Expression"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.IsExpressionValidProperty">
            <summary>
            Identifies the read-only <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.IsExpressionValid"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ErrorsProperty">
            <summary>
            Identifies the read-only <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.Errors"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ExpressionTextProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.ExpressionText"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ItemProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadExpressionEditor.Item"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ExpressionChangedEvent">
            <summary>
            Identifies the ExpressionChanged routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadExpressionEditor.ExpressionErrorEvent">
            <summary>
            Identifies the ExpressionError routed event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadExpressionEditor" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadExpressionEditor.ExpressionChanged">
            <summary>
            Occurs when the expression changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadExpressionEditor.ExpressionError">
            <summary>
            Occurs when an error occurs while parsing an expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.Expression">
            <summary>
            Gets the LINQ Expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.IsExpressionValid">
            <summary>
            Gets a value indicating whether the string the user has entered is a valid expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.Errors">
            <summary>
            Gets the errors from a failed parsing operation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.IsFieldsDrillDownEnabled">
            <summary>
            Gets or sets a value indicating whether users can drill down into field objects in the Expression editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.ExpressionText">
            <summary>
            Gets or sets the expression string.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.Item">
            <summary>
            Gets or sets the item the expression will be evaluated against.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.ViewModel">
            <summary>
            Gets or sets the view model of the <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/>.
            </summary>
            <exception cref="T:System.ArgumentNullException"> if value is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.OnApplyTemplate">
            <summary>When overridden in a derived class, is invoked whenever application
            code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            In simplest terms, this means the method is called just before a UI element displays
            in an application. For more information, see Remarks.</summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditor.CachedAutomationId">
            <summary>
            Gets or sets the manually set AutomationId value (in case there is such one).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.OnCreateAutomationPeer">
            <summary>
            When implemented in a derived class, returns class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementations for the automation infrastructure.
            </summary>
            <returns>
            The class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> subclass to return.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing,
            or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditor.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources;
            <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.RadExpressionEditorCommands">
            <summary>
            Provides a number of useful RadExpressionEditor functions exposed through Commanding.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadExpressionEditorCommands.InsertText">
            <summary>
            Gets the InsertText command, which inserts the string value of its parameter at the current caret position in the RadExpressionEditor textbox.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadExpressionEditorExtensions">
            <summary>
            A class that contains various helpful extension methods for <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/>
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadExpressionEditorExtensions.TryParse(Telerik.Windows.Controls.RadExpressionEditor,System.String,System.Linq.Expressions.Expression@)">
            <summary>
            Converts the string representation of an expression to its <see cref="T:System.Linq.Expressions.Expression"/> equivalent.
            A return value indicates whether the conversion was successful.
            </summary>
            <param name="expressionEditor">
            The <see cref="T:Telerik.Windows.Controls.RadExpressionEditor"/> instance which will perform the conversion.
            </param>
            <param name="expressionText">
            A string containing a well-formed expression to convert.
            </param>
            <param name="result">
            When this method returns, contains the LINQ Expression equivalent to the well-formed expression contained in
            <paramref name="expressionText"/>, if the conversion succeeded, or null if it failed.
            This parameter is passed uninitialized.
            </param>
            <returns>true if <paramref name="expressionText"/> was converted successfully; otherwise, false.</returns>
        </member>
    </members>
</doc>
