﻿using Everylang.App.Callback;
using Everylang.App.LangFlag;
using Everylang.Common.LogManager;
using Everylang.Common.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Threading;
using Vanara.PInvoke;

namespace Everylang.App.SwitcherLang
{
    internal static class KeyboardLayoutCommon
    {
        private static IntPtr _currentKeyboardLayout;

        internal static IntPtr CurrentKeyboardLayout => _currentKeyboardLayout;
        // хранит введенный пользователем текст для разных ракладок
        internal static Dictionary<string, string> AutoSwitcherTextByLayouts = null!;
        // хранит возможные сочетания букв для разных ракладок
        internal static Dictionary<string, List<string>> AutoSwitcherPossibleList = null!;
        internal static Dictionary<string, List<string>> AutoSwitcherPossibleDicList = null!;

        // список раскладок с кодами
        internal static Dictionary<string, IntPtr> AutoSwitcherLayouts = null!;
        // список кодов раскладок и их хендлов
        internal static Dictionary<IntPtr, IntPtr> LangCodeList = null!;

        // список хендлов раскладок
        internal static List<IntPtr> LayoutHandleList = null!;

        // список имен раскладок
        internal static List<string?> LayoutOrderListForTest = null!;



        //private static Timer? _timerForCheckKeyboardLayuots;

        internal static void Start()
        {
            try
            {
                _currentKeyboardLayout =
                    User32.GetKeyboardLayout(
                        User32.GetWindowThreadProcessId(ForegroundWindow.GetForeground(), out _)).DangerousGetHandle();
                GlobalEventsApp.EventKeyboardLayoutChanged += KeyboardLayoutChanged;

                AutoSwitcherTextByLayouts = new Dictionary<string, string?>();
                AutoSwitcherPossibleList = new Dictionary<string, List<string>>();
                AutoSwitcherPossibleDicList = new Dictionary<string, List<string>>();
                AutoSwitcherLayouts = new Dictionary<string, IntPtr>();
                LangCodeList = new Dictionary<IntPtr, IntPtr>();
                LayoutHandleList = new List<IntPtr>();

                LayoutOrderListForTest = new List<string?>();

                foreach (InputLanguage inputLanguage in KeyboardLayoutMethods.GetAvailableInputLanguages())
                {
                    var nameLang = inputLanguage.Culture.TwoLetterISOLanguageName;
                    LayoutOrderListForTest.Add(nameLang);

                    var autoSwitchProtoList = new List<string>();
                    var nameProto = "autolang." + nameLang;
                    var protoList = GetProto(nameProto);
                    if (protoList.Count > 0)
                    {
                        autoSwitchProtoList.AddRange(protoList);
                        if (!AutoSwitcherPossibleList.ContainsKey(nameLang))
                            AutoSwitcherPossibleList.Add(nameLang, autoSwitchProtoList);
                    }

                    var autoSwitchProtoDicList = new List<string>();
                    var nameProtoDic = "autolangdic." + nameLang;
                    var protoListDic = GetProto(nameProtoDic);
                    if (protoListDic.Count > 0)
                    {
                        autoSwitchProtoDicList.AddRange(protoListDic);
                        if (!AutoSwitcherPossibleDicList.ContainsKey(nameLang))
                            AutoSwitcherPossibleDicList.Add(nameLang, autoSwitchProtoDicList);
                    }


                    if (!AutoSwitcherTextByLayouts.ContainsKey(nameLang))
                    {
                        AutoSwitcherTextByLayouts.Add(nameLang, "");
                    }

                    var installedInputLanguage = new IntPtr(inputLanguage.Handle.ToInt32() & 0x0000FFFF);
                    if (!AutoSwitcherLayouts.ContainsKey(inputLanguage.Culture.TwoLetterISOLanguageName))
                    {
                        AutoSwitcherLayouts.Add(inputLanguage.Culture.TwoLetterISOLanguageName, installedInputLanguage);
                    }
                    if (!LangCodeList.ContainsKey(installedInputLanguage))
                    {
                        LangCodeList.Add(installedInputLanguage, inputLanguage.Handle);
                        LayoutHandleList.Add(inputLanguage.Handle);
                    }

                }

                // _timerForCheckKeyboardLayuots = new Timer(TimeSpan.FromSeconds(30).TotalMilliseconds);
                // _timerForCheckKeyboardLayuots.AutoReset = true;
                // _timerForCheckKeyboardLayuots.Elapsed += (_, _) => CheckKeyboardLayuots();



            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void Stop()
        {
            try
            {
                GlobalEventsApp.EventKeyboardLayoutChanged -= KeyboardLayoutChanged;
                // _timerForCheckKeyboardLayuots?.Dispose();
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        private static void KeyboardLayoutChanged(IntPtr layout)
        {
            try
            {
                if (_currentKeyboardLayout != layout)
                {
                    _currentKeyboardLayout = layout;
                    GlobalEventsApp.OnEventKeyboardLayoutChangedForLangFlag(layout);
                }
            }
            catch
            {
                // Ignore
            }
        }

        // проверка если изменится порядок языков в языковой панели
        internal static void CheckKeyboardLayuots()
        {
            bool isChange = false;
            if (LayoutOrderListForTest.Count != KeyboardLayoutMethods.GetInputLangs().Count)
            {
                isChange = true;
            }
            else
            {
                int count = 0;
                foreach (CultureInfo? lang in KeyboardLayoutMethods.GetInputLangs())
                {
                    if (LayoutOrderListForTest[count] != lang?.TwoLetterISOLanguageName)
                    {
                        isChange = true;
                        break;
                    }
                    count++;
                }
            }

            if (isChange)
            {
                System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new ThreadStart(
                    () =>
                    {
                        Stop();
                        Start();
                        LangInfoManager.Stop();
                        LangInfoManager.Start();
                    }
                ));
            }
        }

        private static List<string> GetProto(string name)
        {
            List<string> result = new List<string>();
            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();
            try
            {
                var resName = resourceNames.FirstOrDefault(x => x.Contains(name));
                if (resName != null)
                {
                    var file = assembly.GetManifestResourceStream(resName);
                    if (file != null)
                    {
                        using var reader = new StreamReader(file, Encoding.UTF8);
                        while (reader.Peek() >= 0)
                        {
                            result.Add(reader.ReadLine() ?? string.Empty);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return result;
        }
    }
}
