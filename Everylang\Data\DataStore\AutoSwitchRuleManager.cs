﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    class AutoSwitchRuleManager
    {
        internal static IEnumerable<AutoSwitchRuleDataModel> GetAllAutoSwitchRules()
        {
            var collection = new List<AutoSwitchRuleDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {

                    var schemelessCollection = db.GetCollection("AutoSwitchRuleDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        AutoSwitchRuleDataModel autoSwitchRuleDataModel = new AutoSwitchRuleDataModel();
                        if (!bsonDocument["_id"].IsNull) autoSwitchRuleDataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["Text"].IsNull) autoSwitchRuleDataModel.Text = bsonDocument["Text"].AsString;
                        if (!bsonDocument["IsCaseSensitive"].IsNull) autoSwitchRuleDataModel.IsCaseSensitive = bsonDocument["IsCaseSensitive"].AsBoolean;
                        if (!bsonDocument["IsNotSwitch"].IsNull) autoSwitchRuleDataModel.IsNotSwitch = bsonDocument["IsNotSwitch"].AsBoolean;
                        if (!bsonDocument["IsSwitch"].IsNull) autoSwitchRuleDataModel.IsSwitch = bsonDocument["IsSwitch"].AsBoolean;
                        if (!bsonDocument["ManualSwitchCount"].IsNull) autoSwitchRuleDataModel.ManualSwitchCount = bsonDocument["ManualSwitchCount"].AsInt32;
                        collection.Add(autoSwitchRuleDataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddAutoSwitchRule(AutoSwitchRuleDataModel autoSwitchRuleDataModel)
        {
            try
            {
                if (autoSwitchRuleDataModel == null || string.IsNullOrEmpty(autoSwitchRuleDataModel.Text))
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutoSwitchRuleDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    autoSwitchRuleDataModel.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = autoSwitchRuleDataModel.Id;
                    bsonDocument["Text"] = autoSwitchRuleDataModel.Text;
                    bsonDocument["IsCaseSensitive"] = autoSwitchRuleDataModel.IsCaseSensitive;
                    bsonDocument["IsNotSwitch"] = autoSwitchRuleDataModel.IsNotSwitch;
                    bsonDocument["IsSwitch"] = autoSwitchRuleDataModel.IsSwitch;
                    bsonDocument["ManualSwitchCount"] = autoSwitchRuleDataModel.ManualSwitchCount;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllAutoSwitchRules()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("AutoSwitchRuleDataModel"))
                    {
                        db.DropCollection("AutoSwitchRuleDataModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelAutoSwitchRule(AutoSwitchRuleDataModel autoSwitchRuleDataMode)
        {
            try
            {
                if (autoSwitchRuleDataMode == null)
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection<AutoSwitchRuleDataModel>("AutoSwitchRuleDataModel");
                    if (autoSwitchRuleDataMode.Id != null) schemelessCollection.Delete(autoSwitchRuleDataMode.Id);
                    else schemelessCollection.DeleteMany(x => x.Text == autoSwitchRuleDataMode.Text);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void UpdateData(AutoSwitchRuleDataModel autoSwitchRuleDataMode)
        {
            try
            {
                if (autoSwitchRuleDataMode == null)
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutoSwitchRuleDataModel");
                    var bsonDocument = schemelessCollection.FindById(autoSwitchRuleDataMode.Id);
                    if (bsonDocument != null)
                    {
                        bsonDocument["Text"] = autoSwitchRuleDataMode.Text;
                        bsonDocument["IsCaseSensitive"] = autoSwitchRuleDataMode.IsCaseSensitive;
                        bsonDocument["IsNotSwitch"] = autoSwitchRuleDataMode.IsNotSwitch;
                        bsonDocument["IsSwitch"] = autoSwitchRuleDataMode.IsSwitch;
                        bsonDocument["ManualSwitchCount"] = autoSwitchRuleDataMode.ManualSwitchCount;
                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void UpdateData(AutoSwitchRuleDataModel autoSwitchRuleDataModelOld, AutoSwitchRuleDataModel autoSwitchRuleDataModelNew)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutoSwitchRuleDataModel");
                    var bsonDocument = schemelessCollection.FindOne(Query.EQ("Text", autoSwitchRuleDataModelOld.Text));
                    if (bsonDocument != null)
                    {
                        bsonDocument["Text"] = autoSwitchRuleDataModelNew.Text;
                        bsonDocument["IsCaseSensitive"] = autoSwitchRuleDataModelNew.IsCaseSensitive;
                        bsonDocument["IsNotSwitch"] = autoSwitchRuleDataModelNew.IsNotSwitch;
                        bsonDocument["IsSwitch"] = autoSwitchRuleDataModelNew.IsSwitch;
                        bsonDocument["ManualSwitchCount"] = autoSwitchRuleDataModelNew.ManualSwitchCount;
                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void SaveAllData(IEnumerable<AutoSwitchRuleDataModel> autoSwitchRuleDataModels)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutoSwitchRuleDataModel");
                    foreach (var autoSwitchRuleDataModel in autoSwitchRuleDataModels)
                    {
                        BsonDocument bsonDocument = new BsonDocument();
                        if (autoSwitchRuleDataModel.Id == null)
                        {
                            autoSwitchRuleDataModel.Id = ObjectId.NewObjectId();
                        }
                        bsonDocument["_id"] = autoSwitchRuleDataModel.Id;
                        bsonDocument["Text"] = autoSwitchRuleDataModel.Text;
                        bsonDocument["IsCaseSensitive"] = autoSwitchRuleDataModel.IsCaseSensitive;
                        bsonDocument["IsNotSwitch"] = autoSwitchRuleDataModel.IsNotSwitch;
                        bsonDocument["IsSwitch"] = autoSwitchRuleDataModel.IsSwitch;
                        bsonDocument["ManualSwitchCount"] = autoSwitchRuleDataModel.ManualSwitchCount;
                        schemelessCollection.Insert(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

        }
    }
}
