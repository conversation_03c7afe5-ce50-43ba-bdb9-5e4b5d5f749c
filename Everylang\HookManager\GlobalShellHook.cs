﻿using Everylang.App.Callback;
using Everylang.App.Utilities;
using Microsoft.Win32;
using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Threading;
using Vanara.PInvoke;

namespace Everylang.App.HookManager
{
    static class GlobalShellHook
    {
        private static bool _started;
        private static HwndSource? _src;
        private const int WM_USER = 0x0400;
        private const int WM_LANGUAGE_CHANGED = WM_USER + 7;

        [DllImport("ELshellkhook64.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "RegisterGlobalShellHook")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool RegisterGlobalShellHook64(IntPtr hwnd);

        [DllImport("ELshellkhook64.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "UnregisterGlobalShellHook")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnregisterGlobalShellHook64();

        [DllImport("ELshellkhook32.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "RegisterGlobalShellHook")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool RegisterGlobalShellHook32(IntPtr hwnd);

        [DllImport("ELshellkhook32.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "UnregisterGlobalShellHook")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnregisterGlobalShellHook32();

        static GlobalShellHook()
        {
            LoadLibs();
            //SystemEvents.PowerModeChanged += SystemEvents_PowerModeChanged;
        }

        private static void LoadLibs()
        {
            string globalShellHook = "ELshellkhook32.dll";
            if (Environment.Is64BitOperatingSystem) globalShellHook = "ELshellkhook64.dll";
            var path = CopyLibs(globalShellHook);
            try
            {
                Kernel32.LoadLibrary(path);
            }
            catch
            {
                // Ignore
            }
        }

        private static string CopyLibs(string name)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceNames = assembly.GetManifestResourceNames();
                var file = assembly.GetManifestResourceStream(resourceNames.First(x => x.EndsWith(name)));
                if (file != null)
                {
                    var tempFolder = Path.GetTempPath();
                    if (!CheckDirectoryAccess.Check(tempFolder))
                    {
                        DirectoryInfo directoryInfo =
                            new DirectoryInfo(
                                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData));
                        var dirs = directoryInfo.EnumerateDirectories();
                        foreach (var dirInfo in dirs)
                        {
                            if (!dirInfo.FullName.ToLower().Contains("everylang") && CheckDirectoryAccess.Check(dirInfo.FullName))
                            {
                                tempFolder = dirInfo.FullName;
                                break;
                            }
                        }
                    }

                    string dllPath = Path.Combine(tempFolder, name);
                    try
                    {
                        if (File.Exists(dllPath)) File.Delete(dllPath);
                        using var fileStream = File.Create(dllPath);
                        file.Seek(0, SeekOrigin.Begin);
                        file.CopyTo(fileStream);
                    }
                    catch
                    {
                        // Ignore
                    }

                    return dllPath;
                }
            }
            catch
            {
                // Ignore
            }
            return "";
        }

        private static void SystemEvents_PowerModeChanged(object sender, PowerModeChangedEventArgs e)
        {
            if (e.Mode == PowerModes.Suspend)
            {
                Stop();
            }
            else if (e.Mode == PowerModes.Resume)
            {
                Start();
            }
        }

        internal static void Start()
        {
            if (_started)
            {
                return;
            }
            _started = true;
            if (Application.Current.MainWindow != null)
            {
                IntPtr handle = new WindowInteropHelper(Application.Current.MainWindow).Handle;
                try
                {
                    if (Environment.Is64BitOperatingSystem) RegisterGlobalShellHook64(handle);
                    else RegisterGlobalShellHook32(handle);
                }
                catch
                {
                    // Ignore
                }
                _src = HwndSource.FromHwnd(handle) ?? null;
            }

            if (_src != null)
            {
                try
                {
                    _src.AddHook(WndProc);
                }
                catch
                {
                    // Ignore
                }
            }
        }

        internal static void Stop()
        {
            if (!_started)
            {
                return;
            }
            _started = false;
            try
            {
                if (Environment.Is64BitOperatingSystem) UnregisterGlobalShellHook64();
                else UnregisterGlobalShellHook32();
            }
            catch
            {
                // Ignore
            }
            _src?.RemoveHook(WndProc);
        }

        internal static IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            try
            {
                if (msg == WM_LANGUAGE_CHANGED)
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                        (ThreadStart)delegate
                        {
                            GlobalLangChangeHook.SetNewKeyboardLayout(lParam);
                            GlobalEventsApp.OnEventKeyboardLayoutChanged(lParam);
                        });
            }
            catch
            {
                // Ignore
            }
            return IntPtr.Zero;
        }

    }
}
