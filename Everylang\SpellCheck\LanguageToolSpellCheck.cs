﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace Everylang.App.SpellCheck
{
    class LanguageToolSpellCheck
    {
        internal Task<SpellCheckResult> Start(string text)
        {
            return Task.Run(() =>
            {
                try
                {
                    var langList = "";
                    foreach (InputLanguage inputLanguage in InputLanguage.InstalledInputLanguages)
                    {
                        langList += "," + inputLanguage.Culture.Name;
                    }
                    langList = langList.Trim(',');
                    string url = @"https://api.languagetool.org/v2/check?c=1&v=standalone";
                    var id = GetId;
                    string data =
                        $"text={HttpUtility.UrlEncode(text)}&disabledRules=WHITESPACE_RULE,UPPERCASE_SENTENCE_START&enabledOnly=false&language=auto&preferredVariants={langList}&useragent=webextension-opera-ng&textSessionId={id}";
                    var webResult = StartPostWebRequest(url, data);
                    var spellCheckResult = new SpellCheckResult
                    {
                        ResultText = webResult.ResultText,
                        WithError = webResult.WithError,
                    };
                    if (spellCheckResult.ResultText != null)
                    {
                        if (!spellCheckResult.WithError)
                        {
                            spellCheckResult = ProcessingOfTheRequest(spellCheckResult, text);
                            spellCheckResult.SourceText = text;
                        }
                    }

                    return spellCheckResult;
                }
                catch (Exception e)
                {
                    return new SpellCheckResult
                    {
                        SourceText = text,
                        ResultText = "Error",
                        WithError = true,
                    };
                }
            });
        }

        private string GetId
        {
            get
            {
                return new Random().Next(10000, 99999) + ":" + Math.Round(DateTime.Now.Subtract(new DateTime(1970, 01, 01)).TotalMilliseconds);
            }
        }

        internal WebResult StartPostWebRequest(string url, string dataStr)
        {

            var webResult = new WebResult();
            try
            {
                byte[] data = Encoding.UTF8.GetBytes(dataStr);
                var request = (HttpWebRequest)WebRequest.Create(url);
                request.Method = WebRequestMethods.Http.Post;

                request.Timeout = 10000;
                request.Proxy = NetLib.GetProxy();
                request.UserAgent = SettingsManager.UserAgent;
                request.Referer = "https://languagetool.org";
                request.ContentLength = data.Length;
                request.ContentType = "application/x-www-form-urlencoded; charset=UTF-8";

                var stream = request.GetRequestStream();
                stream.Write(data, 0, data.Length);
                var response = (HttpWebResponse)request.GetResponse();
                webResult = ReadStreamFromResponse(response);
                return webResult;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        private WebResult ReadStreamFromResponse(WebResponse response)
        {
            using (Stream responseStream = response.GetResponseStream())
                if (responseStream != null)
                    using (var sr = new StreamReader(responseStream))
                    {
                        var text = sr.ReadToEnd();
                        var webResult = new WebResult { ResultText = text, WithError = false };
                        return webResult;
                    }
            return new WebResult { ResultText = "", WithError = true };
        }

        private SpellCheckResult ProcessingOfTheRequest(SpellCheckResult spellCheckResult, string text)
        {
            try
            {
                JObject json = JObject.Parse(spellCheckResult.ResultText);
                if (json.TryGetValue("matches", out var matchesToken))
                {
                    foreach (var token in matchesToken)
                    {
                        var pos = (string)token.SelectToken("offset");
                        var length = (string)token.SelectToken("length");
                        var spellCheckDictionary = new SpellCheckDictionary { Pos = Convert.ToInt32(pos), Len = Convert.ToInt32(length) };
                        spellCheckDictionary.Key = text.Substring(spellCheckDictionary.Pos, spellCheckDictionary.Len);
                        if (token.SelectToken("replacements") != null)
                        {
                            var synToken = token.SelectToken("replacements");
                            foreach (var syn in synToken)
                            {
                                var value = (string)syn.SelectToken("value");
                                if (value != null)
                                {
                                    if (pos == "0" && Char.IsLower(text[0]))
                                    {
                                        value = char.ToLower(value[0]) + value.Substring(1);
                                    }
                                    spellCheckDictionary.Suggestions.Add(value);
                                }

                            }
                        }
                        spellCheckResult.SpellCheckDictionary.Add(spellCheckDictionary);
                    }
                }
                if (spellCheckResult.SpellCheckDictionary.Count == 0)
                {
                    spellCheckResult.IsOk = true;
                }
            }
            catch (Exception e)
            {
                spellCheckResult.WithError = true;
            }

            return spellCheckResult;
        }
    }
}
