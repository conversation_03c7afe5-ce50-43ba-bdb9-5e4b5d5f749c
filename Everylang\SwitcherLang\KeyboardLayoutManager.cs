﻿using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using NHotkey;
using System;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Input;
using WindowsInput;

namespace Everylang.App.SwitcherLang
{
    class KeyboardLayoutManager : IDisposable
    {
        private static KeyboardLayoutManager? _instance;
        internal bool IsProcessing { get; set; }

        private bool _isKeyDown;
        private bool _isMouseDown;

        internal event EventHandler? KeyCombinationPressedSwitcher;
        internal event EventHandler? KeyCombinationPressedSwitcherSelectedText;

        internal event EventHandler? KeyCombinationPressedSwitcherLine;

        internal event Action<GlobalKeyEventArgs>? KeyDownSwitcher;
        internal event Action<GlobalKeyEventArgs>? KeyUpSwitcher;

        internal static KeyboardLayoutManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new KeyboardLayoutManager();
                }
                return _instance;
            }
        }

        internal KeyboardLayoutManager()
        {
            HookCallBackKeyDown.CallbackEventHandler += HookManagerSwitcherKeyDown;
            HookCallBackKeyUp.CallbackEventHandler += HookManagerSwitcherKeyUp;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerSwitcherMouseDown;
            HookCallBackMouseUp.CallbackEventHandler += HookManagerSwitcherMouseUp;
        }

        internal void Start()
        {
            KeyboardLayoutCommon.Start();
            KeyboardLayoutWorker.Start();

            if (SettingsManager.Settings.SwitcherIsOn)
            {
                StartSwitcher();
            }
            KeyboardLayoutSetDefaultLayout.Start();
        }

        internal void Stop()
        {
            KeyboardLayoutCommon.Stop();
            KeyboardLayoutWorker.Stop();
            SwitcherStop();
            KeyboardLayoutSetDefaultLayout.Stop();
        }

        private void SwitcherStop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SwitcherSwitchTextLangShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsInvert));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsUp));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsDown));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut));
            for (int i = 0; i < KeyboardLayoutCommon.AutoSwitcherLayouts.Keys.ToList().Count; i++)
            {
                var autoSwitcherLayout = KeyboardLayoutCommon.AutoSwitcherLayouts.Keys.ToList()[i];
                ShortcutManager.RemoveShortcut("HotKeySwitcherCtrlNumber" + autoSwitcherLayout);
            }
        }

        private void StartSwitcher()
        {
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut), SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut, PressedSwitcherSelectedText);
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SwitcherSwitchTextLangShortcut), SettingsManager.Settings.SwitcherSwitchTextLangShortcut, PressedSwitcherSwitchLangForWord);
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut), SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut, PressedSwitcherSwitchLangForAllLine);

            if (SettingsManager.Settings.SwitcherSwitchLangByCtrlPlusNumberIsOn)
            {
                for (int i = 0; i < KeyboardLayoutCommon.AutoSwitcherLayouts.Keys.ToList().Count; i++)
                {
                    if (i > 9)
                    {
                        break;
                    }
                    var autoSwitcherLayout = KeyboardLayoutCommon.AutoSwitcherLayouts.Keys.ToList()[i];
                    ShortcutManager.RegisterAutoGeneratedShortcutHotkey("HotKeySwitcherCtrlNumber" + autoSwitcherLayout, "Ctrl+" + (i + 1), PressedSwitcherCtrlNumber);
                }
            }
        }

        private void PressedSwitcherCtrlNumber(object? obj, HotkeyEventArgs e)
        {
            var name = e.Name.Substring(e.Name.Length - 2, 2);
            var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[name]];
            KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
            e.Handled = true;
        }

        private bool _lastCapsLockState;
        private int _lastKeyValue;
        private DateTime _lastKeyDownTime;

        private void HookManagerSwitcherKeyDown(GlobalKeyEventArgs e)
        {
            //Trace.WriteLine("dddd " + e.KeyCode);
            //if (e.KeyCode == VirtualKeycodes.RightShift && IsProcessing)
            //{
            //    Trace.WriteLine("shiftr++");
            //    IsRShiftDown++;
            //}
            //if (e.KeyCode == VirtualKeycodes.LeftShift && IsProcessing)
            //{
            //    Trace.WriteLine("shiftl++");
            //    IsLShiftDown++;
            //}
            if (SettingsManager.IsStopWorking)
            {
                return;
            }

            if (IsProcessing)
            {
                return;
            }
            if (!SettingsManager.Settings.SwitcherIsOn)
            {
                if ((int)e.KeyCode != 20)
                {
                    var handler = KeyDownSwitcher;
                    if (handler != null) handler(e);
                }
                return;
            }
            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }
            _lastKeyValue = (int)e.KeyCode;
            if (!_isKeyDown) _lastKeyDownTime = DateTime.Now;
            _isKeyDown = true;

            // 5 Caps Lock
            // 8 По правому Ctrl или Caps Lock
            if (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 5 || SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 8)
            {
                if (e.Alt == ModifierKeySide.None && e.Control == ModifierKeySide.None)
                {
                    if ((int)e.KeyCode == 160 && ((Keyboard.GetKeyStates(Key.RightShift) & KeyStates.Down) > 0))
                    {
                        _lastCapsLockState = true;
                        ToggleCapsLock();
                        _lastCapsLockState = false;
                        e.Handled = true;
                        return;
                    }
                    if ((int)e.KeyCode == 161 && ((Keyboard.GetKeyStates(Key.LeftShift) & KeyStates.Down) > 0))
                    {
                        _lastCapsLockState = true;
                        ToggleCapsLock();
                        _lastCapsLockState = false;
                        e.Handled = true;
                        return;
                    }
                }
                if ((int)e.KeyCode == 20 && !_lastCapsLockState)
                {
                    e.Handled = true;
                    return;
                }
            }
            if ((int)e.KeyCode != 20)
            {
                var handler = KeyDownSwitcher;
                if (handler != null) handler(e);
            }
        }

        private void HookManagerSwitcherKeyUp(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (_isMouseDown)
            {
                _isMouseDown = false;
                _isKeyDown = false;
                return;
            }
            _isKeyDown = false;
            //if (e.KeyCode == VirtualKeycodes.RightShift && IsProcessing)
            //{
            //    Trace.WriteLine("shiftr--");
            //    IsRShiftDown--;
            //}
            //if (e.KeyCode == VirtualKeycodes.LeftShift && IsProcessing)
            //{
            //    Trace.WriteLine("shiftl--");
            //    IsLShiftDown--;
            //}
            if (IsProcessing)
            {
                return;
            }
            if (!SettingsManager.Settings.SwitcherIsOn)
            {
                var handler = KeyUpSwitcher;
                if (handler != null) handler(e);
                return;
            }
            #region switch by key

            bool lastKeyUpIsKeyDown = _lastKeyValue == (int)e.KeyCode;

            if (lastKeyUpIsKeyDown && (DateTime.Now - _lastKeyDownTime).TotalMilliseconds > 700) return;

            _lastKeyValue = (int)e.KeyCode;
            // 1 Правому Ctrl
            // 8 По правому Ctrl или Caps Lock
            if ((int)e.KeyCode == 163 && (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 1 || SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 8 || SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("rctrl")) && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Shift == ModifierKeySide.None)
                {
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey() && Mouse.LeftButton == MouseButtonState.Released)
                    {
                        //                        System.Windows.Forms.Application.DoEvents();
                        if (SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("rctrl"))
                        {
                            SwitchToLang("rctrl");
                        }
                        else
                        {
                            KeyboardLayoutSwitcher.SwitchLayout();
                        }
                    }
                }
            }
            // 5 Caps Lock
            // 8 По правому Ctrl или Caps Lock
            if ((int)e.KeyCode == 20 && (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 5 || SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 8))
            {
                if (!_lastCapsLockState)
                {
                    // System.Windows.Forms.Application.DoEvents();
                    KeyboardLayoutSwitcher.SwitchLayout();
                }
            }
            // 2 Левому Ctrl
            if ((int)e.KeyCode == 162 && (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 2 || SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("lctrl")) && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Shift == ModifierKeySide.None)
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey())
                    {
                        // System.Windows.Forms.Application.DoEvents();
                        if (SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("lctrl"))
                        {
                            SwitchToLang("lctrl");
                        }
                        else
                        {
                            KeyboardLayoutSwitcher.SwitchLayout();
                        }
                    }
            }
            // 3 Правому Shift
            if ((int)e.KeyCode == 161 && (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 3 || SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("rshift")) && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Control == ModifierKeySide.None)
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey())
                    {
                        // System.Windows.Forms.Application.DoEvents();
                        if (SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("rshift"))
                        {
                            SwitchToLang("rshift");
                        }
                        else
                        {
                            KeyboardLayoutSwitcher.SwitchLayout();
                        }
                    }
            }
            // 4 Левому Shift
            if ((int)e.KeyCode == 160 && (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 4 || SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("lshift")) && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Control == ModifierKeySide.None)
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey())
                    {
                        // System.Windows.Forms.Application.DoEvents();
                        if (SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Contains("lshift"))
                        {
                            SwitchToLang("lshift");
                        }
                        else
                        {
                            KeyboardLayoutSwitcher.SwitchLayout();
                        }

                    }
            }
            // 6 По правому или левому Ctrl
            if (((int)e.KeyCode == 162 || (int)e.KeyCode == 163) && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 6 && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Shift == ModifierKeySide.None)
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey())
                    {
                        // System.Windows.Forms.Application.DoEvents();
                        KeyboardLayoutSwitcher.SwitchLayout();
                    }
            }
            // 7 По правому или левому Shift
            if (((int)e.KeyCode == 160 || (int)e.KeyCode == 161) && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 7 && lastKeyUpIsKeyDown)
            {
                if (e.Alt == ModifierKeySide.None && e.Control == ModifierKeySide.None)
                    if (CheckActiveProcessFileName.CheckLayoutSwitcherForHotkey())
                    {
                        // System.Windows.Forms.Application.DoEvents();
                        KeyboardLayoutSwitcher.SwitchLayout();
                    }
            }

            #endregion

            var handler1 = KeyUpSwitcher;
            if (handler1 != null) handler1(e);
        }

        private static void SwitchToLang(string key)
        {

            var listKeys = SettingsManager.Settings.SwitcherLangAndKeysForSwitch.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            var lang = listKeys.FirstOrDefault(x => x.Split('+')[1] == key);
            if (lang != null)
            {
                lang = lang.Split('+')[0];
            }
            var input = KeyboardLayoutMethods.GetInputLangs().FirstOrDefault(x => x != null && x.TwoLetterISOLanguageName.ToLower().Equals(lang));
            if (input != null)
            {
                var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[input.TwoLetterISOLanguageName]];
                KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
            }
        }

        private void HookManagerSwitcherMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            _isMouseDown = true;
        }
        private void HookManagerSwitcherMouseUp(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!_isKeyDown)
            {
                _isMouseDown = false;
            }
        }

        private static void ToggleCapsLock()
        {
            var sim = new InputSimulator();
            sim.Keyboard.KeyPress(VirtualKeyCode.CAPITAL);
        }

        internal void PressedSwitcherSwitchLangForAllLine(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedSwitcherLine;
            if (handler != null) handler(this, EventArgs.Empty);
        }

        internal void PressedSwitcherSwitchLangForWord(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedSwitcher;
            handler?.Invoke(this, EventArgs.Empty);
        }

        internal void PressedSwitcherSelectedText(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedSwitcherSelectedText;
            if (handler != null) handler(this, EventArgs.Empty);
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
