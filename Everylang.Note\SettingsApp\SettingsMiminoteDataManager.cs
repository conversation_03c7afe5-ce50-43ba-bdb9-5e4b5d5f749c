﻿using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Linq;

namespace Everylang.Note.SettingsApp
{
    class SettingsMiminoteDataManager
    {
        private static bool _isGetNow;

        public static void GetSettings(AppSettings settings)
        {
            _isGetNow = true;
            var db = SettingsMiminoteManager.LiteDb;
            try
            {
                var schemelessCollection = db.GetCollection("SettingsDataModel");
                if (schemelessCollection != null)
                {
                    var bsonDocument = schemelessCollection.FindAll().FirstOrDefault();
                    if (bsonDocument != null)
                    {
                        if (!bsonDocument["IsOnNotes"].IsNull)
                            settings.IsOnNotes = bsonDocument["IsOnNotes"].AsBoolean;
                        if (!bsonDocument["FontDefault"].IsNull)
                            settings.FontDefault = bsonDocument["FontDefault"].AsString;
                        if (!bsonDocument["SizeFontDefault"].IsNull)
                            settings.SizeFontDefault = bsonDocument["SizeFontDefault"].AsDouble;
                        if (!bsonDocument["TransparencyForNotes"].IsNull)
                            settings.TransparencyForNotes = bsonDocument["TransparencyForNotes"].AsInt32;
                        if (!bsonDocument["AddNewShortcut"].IsNull)
                            settings.AddNewShortcut = bsonDocument["AddNewShortcut"].AsString;
                        if (!bsonDocument["AddNewShortcutIsEnabled"].IsNull)
                            settings.AddNewShortcutIsEnabled = bsonDocument["AddNewShortcutIsEnabled"].AsBoolean;
                    }
                    else
                    {
                        CreateSettings(settings);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

            _isGetNow = false;
        }

        public static void CreateSettings(AppSettings settings)
        {
            var db = SettingsMiminoteManager.LiteDb;
            try
            {
                var schemelessCollection = db.GetCollection("SettingsDataModel");
                BsonDocument bsonDocument = new BsonDocument();
                bsonDocument["_id"] = ObjectId.NewObjectId();
                bsonDocument["IsOnNotes"] = settings.IsOnNotes;
                bsonDocument["SizeFontDefault"] = settings.SizeFontDefault;
                bsonDocument["TransparencyForNotes"] = settings.TransparencyForNotes;
                bsonDocument["AddNewShortcutIsEnabled"] = settings.AddNewShortcutIsEnabled;
                bsonDocument["AddNewShortcut"] = settings.AddNewShortcut;
                bsonDocument["FontDefault"] = settings.FontDefault;
                schemelessCollection.Insert(bsonDocument);
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        public static void SaveSettings(AppSettings settings)
        {
            if (_isGetNow) return;
            var db = SettingsMiminoteManager.LiteDb;
            try
            {
                var schemelessCollection = db.GetCollection("SettingsDataModel");
                var bsonDocument = schemelessCollection.FindAll().FirstOrDefault();
                if (bsonDocument != null)
                {
                    bsonDocument["IsOnNotes"] = settings.IsOnNotes;
                    bsonDocument["SizeFontDefault"] = settings.SizeFontDefault;
                    bsonDocument["TransparencyForNotes"] = settings.TransparencyForNotes;
                    bsonDocument["AddNewShortcutIsEnabled"] = settings.AddNewShortcutIsEnabled;
                    bsonDocument["AddNewShortcut"] = settings.AddNewShortcut;
                    bsonDocument["FontDefault"] = settings.FontDefault;
                    schemelessCollection.Update(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
