﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using WpfScreenHelper;

namespace Everylang.App.OCR.OcrTool
{
    internal class ScreenshotTool
    {
        internal event Action<Bitmap>? BitmapRegionAction;
        internal event Action? ScreenshotWindowClosedAction;

        List<ScreenshotWindow>? _regionSelectionWindows;

        internal void Capture()
        {
            _regionSelectionWindows = new List<ScreenshotWindow>();
            foreach (var screen in Screen.AllScreens)
            {
                var bounds = screen.WpfBounds;
                var dpiScale = screen.ScaleFactor;
                var x = (int)(bounds.X * dpiScale);
                var y = (int)(bounds.Y * dpiScale);
                var width = (int)(bounds.Width * dpiScale);
                var height = (int)(bounds.Height * dpiScale);
                ScreenshotWindow screenshotWindow = new ScreenshotWindow(bounds, x, y, width, height, dpiScale);
                screenshotWindow.BitmapRegionAction += ScreenshotWindowOnBitmapRegionAction;
                screenshotWindow.Closed += ScreenshotWindowOnClosed;
                _regionSelectionWindows.Add(screenshotWindow);
                screenshotWindow.Show();
                screenshotWindow.Activate();
            }
        }

        private void ScreenshotWindowOnClosed(object? sender, EventArgs e)
        {
            var window = sender as ScreenshotWindow;
            if (_regionSelectionWindows != null)
            {
                if (window != null)
                {
                    _regionSelectionWindows.Remove(window);
                }
                if (_regionSelectionWindows.Count == 0)
                {
                    ScreenshotWindowClosedAction?.Invoke();
                }
            }
        }

        private void ScreenshotWindowOnBitmapRegionAction(Bitmap? obj)
        {
            if (_regionSelectionWindows != null)
            {
                var windowList = _regionSelectionWindows.ToList();
                foreach (var window in windowList)
                {
                    window.Close();
                }

                if (obj != null) BitmapRegionAction?.Invoke(obj);
            }
        }

    }
}