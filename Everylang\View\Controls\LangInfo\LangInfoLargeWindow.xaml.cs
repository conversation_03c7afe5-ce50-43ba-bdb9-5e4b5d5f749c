﻿using Everylang.App.Main;
using Everylang.App.SwitcherLang;
using Everylang.App.ViewModels;
using System.Windows.Controls.Primitives;
using System.Windows.Input;

namespace Everylang.App.View.Controls.LangInfo
{
    /// <summary>
    /// Interaction logic for Window1.xaml
    /// </summary>
    internal partial class LangInfoLargeWindow : Popup
    {
        internal LangInfoLargeWindow()
        {

            InitializeComponent();
            MouseDown += OnMouseDown;

            ThumbMy.DragDelta += OnDragDelta;
        }

        private void OnMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left && e.ClickCount == 2)
            {
                AppHookManager.Instance.PressedMain(this, null);
                return;
            }
            if (e.ChangedButton == MouseButton.Left)
            {
                ThumbMy.RaiseEvent(e);
            }
            if (e.ChangedButton == MouseButton.Right)
            {
                KeyboardLayoutSwitcher.SwitchLayout();
            }

        }

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {

            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;

            VMContainer.Instance.LangFlagSettingsViewModel.LangInfoLargeWindowPosX = HorizontalOffset;
            VMContainer.Instance.LangFlagSettingsViewModel.LangInfoLargeWindowPosY = VerticalOffset;
        }

    }
}
