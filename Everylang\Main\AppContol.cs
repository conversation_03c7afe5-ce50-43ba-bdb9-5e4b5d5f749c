﻿using Everylang.App.Clipboard;
using Everylang.App.Converter;
using Everylang.App.Diary;
using Everylang.App.HookManager;
using Everylang.App.LangFlag;
using Everylang.App.License;
using Everylang.App.Miminote;
using Everylang.App.OCR;
using Everylang.App.SmartClick;
using Everylang.App.Snippets;
using Everylang.App.SpellCheck;
using Everylang.App.SwitcherLang;
using Everylang.App.Translator;
using Everylang.App.Utilities;

namespace Everylang.App.Main
{
    internal static class AppContol
    {
        internal static void FirstStart()
        {
            LicenseManager.Init();
            KeyboardLayoutManager.Instance.Start();
        }

        internal static void Start()
        {
            GlobalHookManager.Start();
            SpellCheckManager.Instance.Start();
            TranslateManager.Instance.Start();
            AppHookManager.Instance.Start();
            ClipboardManager.Start();
            OcrManager.Instance.Start();
            DiaryManager.Instance.Start();
            SnippetsManager.Instance.Start();
            SmartClickManager.Instance.StartWatcher();
            MiminoteManager.Start();
            LangInfoManager.Start();
            ConverterManager.Instance.Start();
            Updater.Start();
        }

        internal static void Stop()
        {
            GlobalHookManager.Stop();
            KeyboardLayoutManager.Instance.Stop();
            SpellCheckManager.Instance.Stop();
            TranslateManager.Instance.Stop();
            ClipboardManager.Stop();
            LangInfoManager.Stop();
            OcrManager.Instance.Stop();
            DiaryManager.Instance.Stop();
            SnippetsManager.Instance.Stop();
            SmartClickManager.Instance.StopWatcher();
            MiminoteManager.Stop();
            ConverterManager.Instance.Stop();
        }
    }
}
