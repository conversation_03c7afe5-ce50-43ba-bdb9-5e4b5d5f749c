﻿<UserControl x:Class="Everylang.App.View.SettingControls.LangFlag.LangFlagControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <StackPanel  Margin="20,10,0,0" IsEnabled="{Binding LangFlagSettingsViewModel.jgebhdhs}">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=LangFlagTab}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>

            <CheckBox Focusable="False"  Margin="0,3,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoInTray}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoInTray}" />
            </CheckBox>
            <CheckBox  Focusable="False"  Margin="0,0,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoWindowShowForMouse}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoWindowShowForMouse}" />
            </CheckBox>
            <CheckBox  Focusable="False"  Margin="0,0,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoWindowShowForCaret}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoWindowShowForCaret}" />
            </CheckBox>
            <CheckBox  Focusable="False"  Margin="0,0,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoWindowShowLargeWindow}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoWindowShowLargeWindow}" />
            </CheckBox>
            <StackPanel Margin="0,5,0,0">

                <RadioButton  Focusable="False" MinHeight="0" IsEnabled="{Binding LangFlagSettingsViewModel.ShowIconsIsOn}" FontSize="13" Margin="0,0,0,0"  IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoShowIcons}" Background="Transparent">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoShowIconsImage}" />
                </RadioButton>

                <RadioButton  Focusable="False" MinHeight="0" IsEnabled="{Binding LangFlagSettingsViewModel.ShowIconsIsOn}" FontSize="13" Margin="0,0,0,0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsLangInfoShowIconsNot}" Background="Transparent">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsLangInfoShowIconsText}" />
                </RadioButton>

            </StackPanel>

            <TextBlock Margin="0, 3, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=OpacityIconLangInfo}" />
            <telerik:RadSlider Width="250" IsEnabled="{Binding LangFlagSettingsViewModel.ShowIconsIsOn }" Margin="0, 3, 0, 0" Value="{Binding Path=LangFlagSettingsViewModel.OpacityIconLangInfo}" AutoToolTipPlacement="BottomRight" HorizontalAlignment="Left" Minimum="10" Maximum="100"/>

            <TextBlock Margin="0, 0, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=SizeIconLangInfo}" />
            <telerik:RadSlider Width="250" IsEnabled="{Binding LangFlagSettingsViewModel.ShowIconsIsOn }" Margin="0, 3, 0, 0" Value="{Binding Path=LangFlagSettingsViewModel.SizeIconLangInfo}" AutoToolTipPlacement="BottomRight" HorizontalAlignment="Left" Minimum="0" Maximum="50"/>



            <TextBlock Margin="0, 3, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=PosMouse}" />
            <StackPanel  Margin="0,3,0,0" Orientation="Horizontal">
                <TextBlock FontSize="14" Text="X- " HorizontalAlignment="Left" VerticalAlignment="Center"/>
                <TextBox  Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"
                         
                         Text="{Binding LangFlagSettingsViewModel.PosMouseX,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
                <TextBlock Margin="10, 0, 0, 0" FontSize="14" Text="Y- " HorizontalAlignment="Left" VerticalAlignment="Center"/>
                <TextBox  Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"
                         
                         Text="{Binding LangFlagSettingsViewModel.PosMouseY,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
            </StackPanel>
            <TextBlock Margin="0, 0, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=PosCarret}" />
            <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                <TextBlock FontSize="14" Text="X- " HorizontalAlignment="Left" VerticalAlignment="Center"/>
                <TextBox  Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"
                         Text="{Binding LangFlagSettingsViewModel.PosCarretX,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
                <TextBlock Margin="10, 0, 0, 0" FontSize="14" Text="Y- " HorizontalAlignment="Left" VerticalAlignment="Center"/>
                <TextBox Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"
                         Text="{Binding LangFlagSettingsViewModel.PosCarretY,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
            </StackPanel>

            

            <CheckBox  Focusable="False"  Margin="0,5,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsHideIndicateInFullScreenApp}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsHideIndicateInFullScreenApp}" />
            </CheckBox>

            <CheckBox  Focusable="False"  Margin="0,5,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsIndicateCapsLockState}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsIndicateCapsLockState}" />
            </CheckBox>

            <CheckBox  Focusable="False"  Margin="0,5,0,0" FontSize="14" MinHeight="0" IsChecked="{Binding Path=LangFlagSettingsViewModel.IsIndicateNumLockState}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsIndicateNumLockState}" />
            </CheckBox>
        </StackPanel>
    </Grid>
</UserControl>
