﻿using System;
using System.IO;

namespace Everylang.App.Utilities
{
    internal class CheckDirectoryAccess
    {
        private const string TEMP_FILE = "tempFile.tmp";

        /// <summary>
        /// Checks the ability to create and write to a file in the supplied directory.
        /// </summary>
        /// <param name="directory">String representing the directory path to check.</param>
        /// <returns>True if successful; otherwise false.</returns>
        internal static bool Check(string directory)
        {
            bool success = false;
            string fullPath = Path.Combine(directory, TEMP_FILE);

            if (Directory.Exists(directory))
            {
                try
                {
                    using (FileStream fs = new FileStream(fullPath, FileMode.CreateNew,
                        FileAccess.Write))
                    {
                        fs.WriteByte(0xff);
                    }

                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                        success = true;
                    }
                }
                catch (Exception)
                {
                    success = false;
                }
            }
            return success;
        }
    }
}
