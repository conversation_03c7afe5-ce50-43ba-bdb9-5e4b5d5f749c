﻿using System.Drawing;

namespace Everylang.App.Clipboard
{
    internal class ClipboardDataObject
    {
        internal bool IsOk
        {
            get { return !string.IsNullOrEmpty(TextData) && !string.IsNullOrEmpty(TextPlain) && (IsRtf || IsHtml || IsFiles || IsPlainText) || IsImage; }
        }

        internal string? TextPlain { get; set; }
        internal string? TextData { get; set; }
        internal Image? ImageData { get; set; }
        internal bool IsImage { get; set; }
        internal bool IsRtf { get; set; }
        internal bool IsHtml { get; set; }
        internal bool IsFiles { get; set; }
        internal bool IsPlainText { get; set; }
    }
}
