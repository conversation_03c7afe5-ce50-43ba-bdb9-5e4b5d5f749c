﻿using System;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

namespace Everylang.Common.Utilities
{
    public static class ForegroundWindow
    {
        private static IntPtr CurrentForegroundWindow { get; set; }

        public static void StoreForegroundWindow()
        {
            CurrentForegroundWindow = GetForeground();
        }

        public static IntPtr RestoreForegroundWindow()
        {
            User32.SetForegroundWindow(CurrentForegroundWindow);
            return CurrentForegroundWindow;
        }

        public static IntPtr GetForegroundNative()
        {
            return User32.GetForegroundWindow().DangerousGetHandle();
        }

        public static IntPtr GetForeground()
        {
            var lpgui = new User32.GUITHREADINFO();
            lpgui.cbSize = (uint)Marshal.SizeOf(lpgui.GetType());
            User32.GetGUIThreadInfo(0, ref lpgui);
            if (lpgui.hwndFocus == IntPtr.Zero)
                return lpgui.hwndActive.DangerousGetHandle();
            return lpgui.hwndFocus.DangerousGetHandle();
        }



    }
}
