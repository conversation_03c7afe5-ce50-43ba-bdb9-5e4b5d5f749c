﻿using LiteDB;
using System;

namespace Everylang.App.Data.DataModel
{
    public class DiaryDataModel
    {
        internal ObjectId? Id { get; set; }
        public string? Text { get; set; }
        public string TextPrev => (Text != null && Text.Length > 4000 ? Text.Substring(0, 4000) + "......" : Text) ?? string.Empty;
        public string? ShortText { get; set; }
        public string? Application { get; set; }
        public string? DateText { get; set; }
        public DateTime DateTime { get; set; }
        public DateTime DateTimeDate
        {
            get { return new DateTime(DateTime.Year, DateTime.Month, DateTime.Day); }
        }
    }
}
