﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Everylang.App.OCR.OcrEngine
{
    internal class TessdataDownloader
    {
        private GithubDownloader GithubDownloader { get; } = new("tesseract-ocr", "tessdata");

        internal async Task EnsureDataFolder(string language = "eng",
            string? workingDirectory = null,
            string dataFolder = "tessdata")
        {
            var dataDirectory = new DirectoryInfo(Path.Combine(workingDirectory ?? Environment.CurrentDirectory, dataFolder ?? "tessdata"));
            if (!dataDirectory.Exists) dataDirectory.Create();
            var localFiles = dataDirectory.Exists ? dataDirectory.GetFiles().ToList() : new List<FileInfo>();
            if (localFiles.FirstOrDefault(x => x.Name.StartsWith(language)) == null)
            {
                var remoteFiles = await GithubDownloader.GetFiles(language);

                using var client = new HttpClient();

                foreach (var remoteFile in remoteFiles.Where(remoteFile => localFiles.All(o => o.Name != remoteFile.Name)))
                {
                    using var fileStream = await client.GetStreamAsync(remoteFile.DownloadUrl);
                    var fileInfo = new FileInfo(Path.Combine(dataDirectory.FullName, remoteFile.Name));

                    using var localStream = fileInfo.OpenWrite();
                    await fileStream.CopyToAsync(localStream);
                }
            }


        }
    }
}