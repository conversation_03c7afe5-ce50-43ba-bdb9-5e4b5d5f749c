﻿<UserControl x:Class="Everylang.App.View.SettingControls.ProgramsSetLayout.ProgramsSetLayoutControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <Style TargetType="TextBlock" x:Key="TextBlockStyle">
            <Style.Triggers>
                <DataTrigger Binding="{Binding ProgramsSetLayoutViewModel.AddNewNotStarted}" Value="False">
                    <Setter Property="Visibility" Value="Visible" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ProgramsSetLayoutViewModel.AddNewNotStarted}" Value="True">
                    <Setter Property="Visibility" Value="Hidden" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <Grid IsEnabled="{Binding Path=ProgramsSetLayoutViewModel.jgebhdhs}" Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>

        <StackPanel Margin="20,10,0,0" Grid.Row="0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=ProgramsSetLayoutTabHeader}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>

            <TextBlock Margin="0,3,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsProgramsList}" />
            <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" Focusable="False" MaxDropDownHeight="300" Margin="0,3,20,0"   HorizontalAlignment="Stretch" ItemsSource="{Binding Path=ProgramsSetLayoutViewModel.ProgramsList, IsAsync=True}" SelectedItem="{Binding Path=ProgramsSetLayoutViewModel.ProgramsListCurrent}">
                <telerik:RadComboBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Image Width="18" Height="18" Stretch="Fill" Source="{Binding Icon}" />
                            <TextBlock Margin="10,0,0,0" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="13" Text="{Binding Name}" />
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadComboBox.ItemTemplate>
                <telerik:RadComboBox.ItemContainerStyle>
                    <Style TargetType="telerik:RadComboBoxItem" BasedOn="{StaticResource RadComboBoxItemStyle}">
                        <Setter Property="Padding" Value="3" />
                        <Setter Property="Margin" Value="0" />
                        <Setter Property="MinHeight" Value="0" />
                        <Setter Property="Height" Value="25" />
                    </Style>
                </telerik:RadComboBox.ItemContainerStyle>
            </telerik:RadComboBox>
            <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsSetLayoutViewModel.IsProgramsListCurrentSelected}"  Margin="0,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNew}" Command="{Binding Path=ProgramsSetLayoutViewModel.AddNewFromListCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewExeFile}" Command="{Binding Path=ProgramsSetLayoutViewModel.AddNewExeFileCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewFilesFromFolder}" Command="{Binding Path=ProgramsSetLayoutViewModel.AddNewFilesFromFolderCommand}" />
            </StackPanel>
            <TextBlock Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsFromPoint}" />
            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsSetLayoutViewModel.AddNewNotStarted}"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNew}" Command="{Binding Path=ProgramsSetLayoutViewModel.AddNewCommand}" />
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsSetLayoutViewModel.AddNewNotStarted}" Margin="10,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewTitle}" Command="{Binding Path=ProgramsSetLayoutViewModel.AddNewTitleCommand}" />
                <TextBlock Style="{DynamicResource ResourceKey=TextBlockStyle}" VerticalAlignment="Center" Margin="5,0,0,0" FontSize="13" Text="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewHelp}" />
            </StackPanel>
        </StackPanel>
        <telerik:RadListBox Grid.Row="1" Margin="20,5,20,0" HorizontalAlignment="Stretch" ItemsSource="{Binding ProgramsSetLayoutViewModel.ProgramsSetLayoutList}" SelectedItem="{Binding Path=ProgramsSetLayoutViewModel.SelectedProgram, Mode=TwoWay}">
            <telerik:RadListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0">
                            <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" Focusable="False" ItemsSource="{Binding AllLang}" SelectedItem="{Binding Lang}" FontSize="12" Width="70" SelectionChanged="Selector_OnSelected" VerticalAlignment="Center"/>
                            <TextBlock Margin="5,0,0,0" Text="{Binding ProgramTitle}" VerticalAlignment="Center" FontSize="13"/>
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                    <Setter Property="Padding" Value="3" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Height" Value="50" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
        <StackPanel Grid.Row="2" Margin="20,5,0,5" >
            <StackPanel Orientation="Horizontal" IsEnabled="{Binding Path=ProgramsSetLayoutViewModel.IsSelected}">
                <telerik:RadButton Focusable="False" Width="100" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsDelete}" Command="{Binding Path=ProgramsSetLayoutViewModel.DeleteSelectionCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0" Width="100" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=Edit}" Command="{Binding Path=ProgramsSetLayoutViewModel.EditSelectionCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
