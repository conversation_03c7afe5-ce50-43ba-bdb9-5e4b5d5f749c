<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.FixedDocumentViewers</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Controls.FixedDocumentViewerBase">
            <summary>
            Base document viewing control that can host <see cref="T:Telerik.Windows.Documents.Fixed.Model.RadFixedDocument"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized" /> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized" />
            is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FixedDocumentViewerBase" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleMode">
            <summary>
            Gets or sets the scale mode.
            </summary>
            <value>The scale mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.RotationAngle">
            <summary>
            Gets the rotation angle.
            </summary>
            <value>The rotation angle.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleModeProperty">
            <summary>
            Identifies Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleMode property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.DocumentProperty">
            <summary>
            Identifies Telerik.Windows.Controls.FixedDocumentViewerBase.Document property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.CommandDescriptorsProperty">
            <summary>
            Identifies Teleerik.Windows.Controls.FixedDocumentViewerBase.CommandDescriptors property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleFactorProperty">
            <summary>
            Identifies Teleerik.Windows.Controls.FixedDocumentViewerBase.ScaleFactor property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.ModeProperty">
            <summary>
            Identifies Telerik.Windows.Controls.FixedDocumentViewerBase.Mode property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.CurrentPageNumberProperty">
            <summary>
            Identifies Telerik.Windows.Controls.FixedDocumentViewerBase.CurrentPageNumber property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FixedDocumentViewerBase.SelectionFillProperty">
            <summary>
            Identifies Telerik.Windows.Controls.FixedDocumentViewerBase.SelectionFill property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.OnDocumentChangedOverride(Telerik.Windows.Documents.Fixed.Model.RadFixedDocument,Telerik.Windows.Documents.Fixed.Model.RadFixedDocument)">
            <summary>
            Called when the document is changed.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.SetRotationAngle(Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Sets the rotation angle.
            </summary>
            <param name="rotationAngle">The rotation angle.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.RegisterPresenter(System.String,Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Registers the presenter.
            </summary>
            <param name="presenterName">Name of the presenter.</param>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.GetRegisteredPresenter(System.String)">
            <summary>
            Gets the registered presenter.
            </summary>
            <param name="presenterName">Name of the presenter.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.HasRegisteredPresenter(System.String)">
            <summary>
            Determines whether [has registered presenter] [the specified presenter name].
            </summary>
            <param name="presenterName">Name of the presenter.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.SetDocumentSource(Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource)">
            <summary>
            Sets the document source.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application
            code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate" />.
            In simplest terms, this means the method is called just before a UI element displays
            in an application. For more information, see Remarks.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.ModeChanged">
            <summary>
            Occurs when the mode is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleModeChanged">
            <summary>
            Occurs when the scale mode is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.FixedDocumentPresenterChanged">
            <summary>
            Occurs when [fixed document presenter changed].
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.DocumentChanged">
            <summary>
            Occurs when the PDF document is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.CurrentPageChanged">
            <summary>
            Occurs when the current page is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.AnnotationClicked">
            <summary>
            Occurs when annotation is clicked.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleFactorChanged">
            <summary>
            Occurs when the scale factor is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FixedDocumentViewerBase.HyperlinkClicked">
            <summary>
            Occurs when the hyperlink is clicked.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.FixedDocumentPresenter">
            <summary>
            Gets or sets the document presenter. The fixed document presenter is used to visualize <see cref="T:Telerik.Windows.Documents.Fixed.Model.RadFixedDocument" /> instance.
            </summary>
            <value>The document presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.SelectionFill">
            <summary>
            Gets or sets the selection fill.
            </summary>
            <value>The selection fill.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.CommandDescriptors">
            <summary>
            Gets or sets the command descriptors.
            </summary>
            <value>The command descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.Cursors">
            <summary>
            Gets the cursors.
            </summary>
            <value>The cursors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.HorizontalScrollBar">
            <summary>
            Gets the horizontal scroll bar.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.VerticalScrollBar">
            <summary>
            Gets the vertical scroll bar.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.CanHorizontallyScroll">
            <summary>
            Gets the can horizontally scroll.
            </summary>
            <value>The can horizontally scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.CanVerticallyScroll">
            <summary>
            Gets the can vertically scroll.
            </summary>
            <value>The can vertically scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.HorizontalScrollOffset">
            <summary>
            Gets the horizontal scroll offset.
            </summary>
            <value>The horizontal scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.VerticalScrollOffset">
            <summary>
            Gets the vertical scroll offset.
            </summary>
            <value>The vertical scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.Mode">
            <summary>
            Gets or sets the mode that the FixedDocumentViewerBase class works.
            </summary>
            <value>The mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.Document">
            <summary>
            Gets or sets the document for the viewer.
            </summary>
            <value>The document.</value>
            <remarks>Instance of RadFixedDocument can be created from PdfFormatProvider class. Otherwise the DocumentSource property should be used.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.Settings">
            <summary>
            Gets or sets the settings for the RadPdfViewerBase class.
            </summary>
            <value>The settings.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.DefaultImportSettings">
            <summary>
            Gets or sets the default import settings for the current FixedDocumentViewerBase. This property is used in the OpenPdfDocumentCommand.
            </summary>
            <value>The default import settings.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.CurrentPage">
            <summary>
            Gets the current page.
            </summary>
            <value>The current page.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.DocumentSource">
            <summary>
            Gets or sets the document source for the viewer.
            </summary>
            <value>The document source.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.ScaleFactor">
            <summary>
            Gets or sets the scale factor for the viewer.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.CurrentPageNumber">
            <summary>
            Gets or sets the current page number.
            </summary>
            <value>The current page number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FixedDocumentViewerBase.PagesCount">
            <summary>
            Gets the number of pages of the currently loaded document.
            </summary>
            <value>The pages count.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Copy">
            <summary>
            Copies the selected text in the Clipboard.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Select(Telerik.Windows.Documents.Fixed.Text.TextPosition,Telerik.Windows.Documents.Fixed.Text.TextPosition)">
            <summary>
            Creates selection from start position to end position.
            </summary>
            <param name="startPosition">The start position.</param>
            <param name="endPosition">The end position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Select(Telerik.Windows.Documents.Fixed.Text.TextRange)">
            <summary>
            Selects the specified range.
            </summary>
            <param name="range">The range.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.GetSelectedText">
            <summary>
            Gets the selected text.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.GetSelectedTextAsync">
            <summary>
            Gets the selected text asynchronously.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Find(System.String)">
            <summary>
            Finds next match for the specified text in the current document.
            </summary>
            <param name="text">The text.</param>
            <returns>The result.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Find(System.String,Telerik.Windows.Documents.Fixed.Search.TextSearchOptions)">
            <summary>
            Finds next match for the specified text in the current document using specified options.
            </summary>
            <param name="text">The text.</param>
            <param name="options">The options.</param>
            <returns>The result.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FindAll(System.String)">
            <summary>
            Finds all matches for the specified text in the current document.
            </summary>
            <param name="text">The text.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FindAll(System.String,Telerik.Windows.Documents.Fixed.Search.TextSearchOptions)">
            <summary>
            Finds all matches for the specified text in the current document using specified options.
            </summary>
            <param name="text">The text.</param>
            <param name="options">The options.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FindPrevious(System.String)">
            <summary>
            Finds the previous text in the current document.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FindPrevious(System.String,Telerik.Windows.Documents.Fixed.Search.TextSearchOptions)">
            <summary>
            Finds the previous text in the current document using specified options.
            </summary>
            <param name="text">The text.</param>
            <param name="options">The options.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FitToWidth">
            <summary>
            Fits FixedDocumentViewerBase content to page width.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.FitToPage">
            <summary>
            Fits FixedDocumentViewerBase content to page size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Print">
            <summary>
            Prints the current document using the default print settings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Print(Telerik.Windows.Documents.Fixed.Print.PrintSettings)">
            <summary>
            Prints the current document.
            </summary>
            <param name="settings">The print settings.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.Print(System.Windows.Controls.PrintDialog,Telerik.Windows.Documents.Fixed.Print.PrintSettings)">
            <summary>
            Prints with the specified print dialog. The dialog should already be initialized.
            </summary>
            <param name="printDialog">The print dialog. .</param>
            <param name="settings">The print settings.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.GoToPage(System.Int32)">
            <summary>
            Goes to specified page.
            </summary>
            <param name="pageNo">The page number.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.PageUp">
            <summary>
            Goes a page up.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.PageDown">
            <summary>
            Goes a page down.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.GoToDestination(Telerik.Windows.Documents.Fixed.Model.Navigation.Destination,System.Boolean)">
            <summary>
            Goes to given destination.
            </summary>
            <param name="destination">The destination.</param>
            <param name="syncBookmark">If set to <c>true</c> the SyncCurrentBookmarkItemCommand is executed as well.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.ScrollToHorizontalOffset(System.Double)">
            <summary>
            Scrolls to horizontal offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.ScrollToVerticalOffset(System.Double)">
            <summary>
            Scrolls to vertical offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.UpdatePresenterLayout">
            <summary>
            Updates the current presenter layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FixedDocumentViewerBase.ClearDocument">
            <summary>
            Clears the document.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadPdfViewer">
            <summary>
            Represents a control for visualization of PDF documents.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPdfViewer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized" /> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized" />
            is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPdfViewer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadPdfViewer" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPdfViewer.KeyBindings">
            <summary>
            Gets or sets the key bindings.
            </summary>
            <value>The key bindings.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPdfViewer.DocumentSourceProperty">
            <summary>
            Identifies Telerik.Windows.Controls.RadPdfViewer.DocumentSource property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPdfViewer.DocumentSource">
            <summary>
            A <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource"/> instance representing a PDF file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPdfViewer.SignatureValidationProperties">
            <summary>
            Gets or sets the signature validation properties.
            </summary>
            <value>The signature validation properties.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPdfViewer.Commands">
            <summary>
            Gets or sets the commands.
            </summary>
            <value>The commands.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPdfViewer.Save(System.IO.Stream)">
            <summary>
            Saves the current PDF file in a stream. This method preserves changes made while viewing the PDF file. For instance, such changes may occur while editing interactive forms.
            </summary>
            <param name="stream"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPdfViewer.InvalidatePageUI(Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            The method invalidates the page UI. This will trigger re-render of the page content on the next layout pass.
            The method can be used when the document is imported with the PdfProcessing model.
            </summary>
            <param name="page">The page whose UI will be invalidated.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPdfViewer.OnCreateAutomationPeer">
            <summary>
            Returns class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> implementations for the Windows Presentation Foundation (WPF) infrastructure.
            </summary>
            <returns>
            The type-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> implementation.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands">
            <summary>
            Represents commands for a given <see cref="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FixedDocumentViewer"/> instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FixedDocumentViewer">
            <summary>
            Gets the fixed document viewer.
            </summary>
            <value>The fixed document viewer.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SetFixedDocumentPagesPresenterCommand">
            <summary>
            Gets the set fixed document pages presenter command.
            </summary>
            <value>The set fixed document pages presenter command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SetFixedDocumentSinglePageViewPresenterCommand">
            <summary>
            Gets the set fixed document single page view presenter command.
            </summary>
            <value>The set fixed document single page view presenter command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FitToWidthFixedDocumentPagesPresenterCommand">
            <summary>
            Gets the fit to width fixed document pages presenter command.
            </summary>
            <value>The fit to width fixed document pages presenter command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ActivateBookmarkItemCommand">
            <summary>
            Gets the activate bookmark item command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SyncCurrentBookmarkItemCommand">
            <summary>
            Gets the sync current bookmark item command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FitToPageFixedDocumentSinglePageViewPresenterCommand">
            <summary>
            Gets the fit to page fixed document single page view presenter command.
            </summary>
            <value>
            The fit to page fixed document single page view presenter command.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.RotateClockwiseCommand">
            <summary>
            Gets the rotate clockwise command.
            </summary>
            <value>The rotate clockwise command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.RotateCounterClockwiseCommand">
            <summary>
            Gets the rotate counter clockwise command.
            </summary>
            <value>The rotate counter clockwise command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FitToPageCommand">
            <summary>
            Gets the fit to page command.
            </summary>
            <value>The fit to page command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FitToWidthCommand">
            <summary>
            Gets the fit to width command.
            </summary>
            <value>The fit to width command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ShowFindDialogCommand">
            <summary>
            Gets the show find dialog command.
            </summary>
            <value>The show find dialog command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ScrollLeftCommand">
            <summary>
            Gets the scroll left command.
            </summary>
            <value>The scroll left command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ScrollRightCommand">
            <summary>
            Gets the scroll right command.
            </summary>
            <value>The scroll right command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ScrollUpCommand">
            <summary>
            Gets the scroll up command.
            </summary>
            <value>The scroll up command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ScrollDownCommand">
            <summary>
            Gets the scroll down command.
            </summary>
            <value>The scroll down command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.OpenPdfDocumentCommand">
            <summary>
            Gets the open PDF document command.
            </summary>
            <value>The open PDF document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SaveAsPdfDocumentCommand">
            <summary>
            Gets the save as PDF document command.
            </summary>
            <value>The save as PDF document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.PrintPdfDocumentCommand">
            <summary>
            Gets the print PDF document command.
            </summary>
            <value>The print PDF document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.PageUpCommand">
            <summary>
            Gets the page up command.
            </summary>
            <value>The page up command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.PageDownCommand">
            <summary>
            Gets the page down command.
            </summary>
            <value>The page down command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ZoomInCommand">
            <summary>
            Gets the zoom in command.
            </summary>
            <value>The zoom in command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ZoomOutCommand">
            <summary>
            Gets the zoom out command.
            </summary>
            <value>The zoom out command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FindCommand">
            <summary>
            Gets the find command.
            </summary>
            <value>The find command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.FindPreviousCommand">
            <summary>
            Gets the find previous command.
            </summary>
            <value>The find previous command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.CopyCommand">
            <summary>
            Gets the copy command.
            </summary>
            <value>The copy command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SelectAllCommand">
            <summary>
            Gets the select all command.
            </summary>
            <value>The select all command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretLineDownCommand">
            <summary>
            Gets the move caret line down command.
            </summary>
            <value>The move caret line down command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretLineUpCommand">
            <summary>
            Gets the move caret line up command.
            </summary>
            <value>The move caret line up command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToEndOfDocumentCommand">
            <summary>
            Gets the move caret to end of document command.
            </summary>
            <value>The move caret to end of document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToLineEndCommand">
            <summary>
            Gets the move caret to line end command.
            </summary>
            <value>The move caret to line end command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToLineStartCommand">
            <summary>
            Gets the move caret to line start command.
            </summary>
            <value>The move caret to line start command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToNextWordCommand">
            <summary>
            Gets the move caret to next word command.
            </summary>
            <value>The move caret to next word command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToNextCharacterCommand">
            <summary>
            Gets the move caret to next character command.
            </summary>
            <value>The move caret to next character command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToPreviousCharacterCommand">
            <summary>
            Gets the move caret to previous character command.
            </summary>
            <value>The move caret to previous character command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToPreviousWordCommand">
            <summary>
            Gets the move caret to previous word command.
            </summary>
            <value>The move caret to previous word command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.MoveCaretToStartOfDocumentCommand">
            <summary>
            Gets the move caret to start of document command.
            </summary>
            <value>The move caret to start of document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ValidateSignaturesCommand">
            <summary>
            Gets the validate signatures command.
            </summary>
            <value>The validate signatures command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ShowSignaturePropertiesDialogCommand">
            <summary>
            Gets the show signature properties dialog command.
            </summary>
            <value>The show signature properties dialog command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.ShowSignSignatureDialogCommand">
            <summary>
            Gets the show sign signature dialog command.
            </summary>
            <value>The show sign signature dialog command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommands.SignSignatureFieldCommand">
            <summary>
            Gets the sign signature field command.
            </summary>
            <value>The sign signature field command.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ActivateBookmarkItemCommand">
            <summary>
            Command representing the Bookmark activated action (usually a user click event).
            When executed, the viewer will display the related Bookmark destination or execute the provided action.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ActivateBookmarkItemCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ActivateBookmarkItemCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">Instance of the document viewer the command will be applied for.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ActivateBookmarkItemCommand.Execute(System.Object)">
            <summary>
            Applies the destination, if any, for the activated <see cref="T:Telerik.Windows.Documents.Fixed.Model.Navigation.BookmarkItem" /> instance or executes the provided action.
            </summary>
            <param name="parameter">The <see cref="T:Telerik.Windows.Documents.Fixed.Model.Navigation.BookmarkItem" /> instance to be activated.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.CompositeCommand">
            <summary>
            Represents a composite command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CompositeCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,System.Windows.Input.ICommand[])">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.CompositeCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
            <param name="commands">The commands.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CompositeCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CompositeCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.CopyCommand">
            <summary>
            Represents copy command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CopyCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.CopyCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CopyCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.CopyCommand.Execute(System.Object)">
            <summary>
            Executes the command.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptor">
            <summary>
            Represents a wrapper for commands.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptor.#ctor(Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptor" /> class.
            </summary>
            <param name="command">The command.</param>
            <param name="isEnabled">The is enabled.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptor.Default">
            <summary>
            Gets the default command descriptor.
            </summary>
            <value>The default.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptor.Command">
            <summary>
            Gets the command.
            </summary>
            <value>The command.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup">
            <summary>
            Represents a group of command descriptors.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup.#ctor(Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase[])">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup" /> class.
            </summary>
            <param name="commandDescriptors">The command descriptors.</param>
        </member>
        <member name="E:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup.PropertyChanged">
            <summary>
            Occurs when a property is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup.Empty">
            <summary>
            Gets empty command descriptor group.
            </summary>
            <value>The empty command descriptor group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorGroup.IsEnabled">
            <summary>
            Gets or sets a value indicating whether the group is enabled.
            </summary>
            <value>The is enabled.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors">
            <summary>
            Represents command descriptors for a given <see cref="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FixedDocumentViewer"/> instance. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.PropertyChanged">
            <summary>
            Do not use this event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.AdditionalCommandDescriptorsGroup">
            <summary>
            Gets the additional command descriptors group.
            </summary>
            <value>The additional command descriptors group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.TextSelectionCommandDescriptorsGroup">
            <summary>
            Gets the common command descriptors group.
            </summary>
            <value>The common command descriptors group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ViewCommandDescriptorsGroup">
            <summary>
            Gets the view command descriptors group.
            </summary>
            <value>The view command descriptors group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SetFixedDocumentPagesPresenterCommandDescriptor">
            <summary>
            Gets the set fixed document pages presenter command descriptor.
            </summary>
            <value>The set fixed document pages presenter command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SetFixedDocumentSinglePageViewPresenterCommandDescriptor">
            <summary>
            Gets the set fixed document single page view presenter command descriptor.
            </summary>
            <value>
            The set fixed document single page view presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FitToPageFixedDocumentSinglePageViewPresenterCommandDescriptor">
            <summary>
            Gets the fit to page fixed document single page view presenter command descriptor.
            </summary>
            <value>
            The fit to page fixed document single page view presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FitToWidthFixedDocumentPagesPresenterCommandDescriptor">
            <summary>
            Gets the fit to width fixed document pages presenter command descriptor.
            </summary>
            <value>
            The fit to width fixed document pages presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.RotateClockwiseCommandDescriptor">
            <summary>
            Gets the rotate clockwise command descriptor.
            </summary>
            <value>The rotate clockwise command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.RotateCounterClockwiseCommandDescriptor">
            <summary>
            Gets the rotate counter clockwise command descriptor.
            </summary>
            <value>The rotate counter clockwise command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.OpenCommandDescriptor">
            <summary>
            Gets the open command descriptor.
            </summary>
            <value>The open command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SaveAsCommandDescriptor">
            <summary>
            Gets the save as command descriptor.
            </summary>
            <value>The save as command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.PageUpCommandDescriptor">
            <summary>
            Gets the page up command descriptor.
            </summary>
            <value>The page up command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.PageDownCommandDescriptor">
            <summary>
            Gets the page down command descriptor.
            </summary>
            <value>The page down command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.CopyCommandDescriptor">
            <summary>
            Gets the copy command descriptor.
            </summary>
            <value>The copy command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SelectAllCommandDescriptor">
            <summary>
            Gets the select all command descriptor.
            </summary>
            <value>The select all command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ZoomInCommandDescriptor">
            <summary>
            Gets the zoom in command descriptor.
            </summary>
            <value>The zoom in command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ZoomOutCommandDescriptor">
            <summary>
            Gets the zoom out command descriptor.
            </summary>
            <value>The zoom out command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.PrintCommandDescriptor">
            <summary>
            Gets the print command descriptor.
            </summary>
            <value>The print command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretLineDownCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretLineUpCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToEndOfDocumentCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToLineEndCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToLineStartCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToNextCharacterCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToNextWordCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToPreviousCharacterCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToPreviousWordCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveCaretToStartOfDocumentCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveDownCommandDescriptor">
            <summary>
            Gets the move down command descriptor.
            </summary>
            <value>The move down command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveUpCommandDescriptor">
            <summary>
            Gets the move up command descriptor.
            </summary>
            <value>The move up command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveLeftCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.MoveRightCommandDescriptor">
            <summary>
            Gets the move right command descriptor.
            </summary>
            <value>The move right command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ShowFindDialogCommandDescriptor">
            <summary>
            Gets the show find dialog command descriptor.
            </summary>
            <value>The show find dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FitToWidthCommandDescriptor">
            <summary>
            Gets the fit to width command descriptor.
            </summary>
            <value>The fit to width command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FitToPageCommandDescriptor">
            <summary>
            Gets the fit to page command descriptor.
            </summary>
            <value>The fit to page command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FindCommandDescriptor">
            <summary>
            Gets the find command descriptor.
            </summary>
            <value>The find command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FindPreviousCommandDescriptor">
            <summary>
            Gets the find previous command descriptor.
            </summary>
            <value>The find previous command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ValidateSignaturesCommandDescriptor">
            <summary>
            Gets the validate signatures command descriptor.
            </summary>
            <value>The validate signatures command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ShowSignaturePropertiesDialogCommandDescriptor">
            <summary>
            Gets the show signature properties dialog command descriptor.
            </summary>
            <value>The show signature properties dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ShowSignSignatureDialogCommandDescriptor">
            <summary>
            Gets the show sign signature dialog command descriptor.
            </summary>
            <value>The show sign signature dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.ActivateBookmarkItemCommandDescriptor">
            <summary>
            Gets the activate bookmark item command descriptor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SyncCurrentBookmarkItemCommandDescriptor">
            <summary>
            Gets the sync current bookmark item command descriptor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.SignSignatureCommandDescriptor">
            <summary>
            Gets the sign signature command descriptor.
            </summary>
            <value>The sign signature command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptors.FixedDocumentViewer">
            <summary>
            Gets the fixed document viewer.
            </summary>
            <value>The fixed document viewer.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.CompositeCommandDescriptor`1">
            <summary>
            Represents composite command descriptor class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CompositeCommandDescriptor`1.#ctor(System.Boolean,System.Collections.Generic.KeyValuePair{`0,Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase}[])">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.CompositeCommandDescriptor`1" /> class.
            </summary>
            <param name="isEnabled">The is enabled.</param>
            <param name="data">The data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CompositeCommandDescriptor`1.OnCommandChanged">
            <summary>
            Called when the command is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CompositeCommandDescriptor`1.GetCommand(`0)">
            <summary>
            Gets the command.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase">
            <summary>
            Represents a wrapper for commands.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase" /> class.
            </summary>
            <param name="isEnabled">The is enabled.</param>
        </member>
        <member name="E:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase.Command">
            <summary>
            Gets the command.
            </summary>
            <value>The command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase.IsEnabled">
            <summary>
            Gets or sets a value indicating the command descriptor is enabled.
            </summary>
            <value>The is enabled.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase.OnPropertyChanged(System.String)">
            <summary>
            Called when [property changed].
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor">
            <summary>
            Represents arrow key command descriptor class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,System.Collections.Generic.KeyValuePair{Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode,Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase}[])">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
            <param name="data">The data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,System.Boolean,System.Collections.Generic.KeyValuePair{Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode,Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase}[])">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
            <param name="isEnabled">The is enabled.</param>
            <param name="data">The data.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.ArrowKeyCommandDescriptor.Command">
            <summary>
            Gets the command.
            </summary>
            <value>The command.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors">
            <summary>
            Represents default command descriptors for FixedDocumentViewerBase.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FitToPageFixedDocumentSinglePageViewPresenterCommandDescriptor">
            <summary>
            Gets the fit to page fixed document single page view presenter command descriptor.
            </summary>
            <value>
            The fit to page fixed document single page view presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FitToWidthFixedDocumentPagesPresenterCommandDescriptor">
            <summary>
            Gets the fit to width fixed document pages presenter command descriptor.
            </summary>
            <value>
            The fit to width fixed document pages presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SetFixedDocumentPagesPresenterCommandDescriptor">
            <summary>
            Gets the set fixed document pages presenter command descriptor.
            </summary>
            <value>The set fixed document pages presenter command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SetFixedDocumentSinglePageViewPresenterCommandDescriptor">
            <summary>
            Gets the set fixed document single page view presenter command descriptor.
            </summary>
            <value>
            The set fixed document single page view presenter command descriptor.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.RotateCounterClockwiseCommandDescriptor">
            <summary>
            Gets the rotate counter clockwise command descriptor.
            </summary>
            <value>The rotate counter clockwise command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.RotateClockwiseCommandDescriptor">
            <summary>
            Gets the rotate clockwise command descriptor.
            </summary>
            <value>The rotate clockwise command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FindCommandDescriptor">
            <summary>
            Gets the find command descriptor.
            </summary>
            <value>The find command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FindPreviousCommandDescriptor">
            <summary>
            Gets the find previous command descriptor.
            </summary>
            <value>The find previous command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToPreviousCharacterCommandDescriptor">
            <summary>
            Gets the move caret to previous character command.
            </summary>
            <value>The move caret to previous character command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToNextCharacterCommandDescriptor">
            <summary>
            Gets the move caret to next character command.
            </summary>
            <value>The move caret to next character command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToPreviousWordCommandDescriptor">
            <summary>
            Gets the move caret to previous word command.
            </summary>
            <value>The move caret to previous word command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToNextWordCommandDescriptor">
            <summary>
            Gets the move caret to next word command.
            </summary>
            <value>The move caret to next word command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToLineEndCommandDescriptor">
            <summary>
            Gets the move caret to line end command.
            </summary>
            <value>The move caret to line end command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToLineStartCommandDescriptor">
            <summary>
            Gets the move caret to line start command.
            </summary>
            <value>The move caret to line start command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToEndOfDocumentCommandDescriptor">
            <summary>
            Gets the move caret to end of document command.
            </summary>
            <value>The move caret to end of document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretToStartOfDocumentCommandDescriptor">
            <summary>
            Gets the move caret to start of document command.
            </summary>
            <value>The move caret to start of document command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretLineUpCommandDescriptor">
            <summary>
            Gets the move caret line up command.
            </summary>
            <value>The move caret line up command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveCaretLineDownCommandDescriptor">
            <summary>
            Gets the move caret line down command.
            </summary>
            <value>The move caret line down command.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.AdditionalCommandDescriptorsGroup">
            <summary>
            Gets the additional commands descriptor group.
            </summary>
            <value>The additional commands descriptor group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ViewCommandDescriptorsGroup">
            <summary>
            Gets the view command descriptors group.
            </summary>
            <value>The view command descriptors group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.TextSelectionCommandDescriptorsGroup">
            <summary>
            Gets the text selection command descriptors group.
            </summary>
            <value>The text selection command descriptors group.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SelectAllCommandDescriptor">
            <summary>
            Gets the select all command descriptor.
            </summary>
            <value>The select all command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.CopyCommandDescriptor">
            <summary>
            Gets the copy command descriptor.
            </summary>
            <value>The copy command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.PrintCommandDescriptor">
            <summary>
            Gets the print command descriptor.
            </summary>
            <value>The print command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ZoomInCommandDescriptor">
            <summary>
            Gets the zoom in command descriptor.
            </summary>
            <value>The zoom in command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ZoomOutCommandDescriptor">
            <summary>
            Gets the zoom out command descriptor.
            </summary>
            <value>The zoom out command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.OpenCommandDescriptor">
            <summary>
            Gets the open command descriptor.
            </summary>
            <value>The open command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SaveAsCommandDescriptor">
            <summary>
            Gets the save as command descriptor.
            </summary>
            <value>The save as command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.PageDownCommandDescriptor">
            <summary>
            Gets the page down command descriptor.
            </summary>
            <value>The page down command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.PageUpCommandDescriptor">
            <summary>
            Gets the page up command descriptor.
            </summary>
            <value>The page up command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveDownCommandDescriptor">
            <summary>
            Gets the move down command descriptor.
            </summary>
            <value>The move down command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveUpCommandDescriptor">
            <summary>
            Gets the move up command descriptor.
            </summary>
            <value>The move up command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveLeftCommandDescriptor">
            <summary>
            Gets the move left command descriptor.
            </summary>
            <value>The move left command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.MoveRightCommandDescriptor">
            <summary>
            Gets the move right command descriptor.
            </summary>
            <value>The move right command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ShowFindDialogCommandDescriptor">
            <summary>
            Gets the show find dialog command descriptor.
            </summary>
            <value>The show find dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FitToWidthCommandDescriptor">
            <summary>
            Gets the fit to width command descriptor.
            </summary>
            <value>The fit to width command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.FitToPageCommandDescriptor">
            <summary>
            Gets the fit to page command descriptor.
            </summary>
            <value>The fit to page command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ActivateBookmarkItemCommandDescriptor">
            <summary>
            Gets the activate bookmark item command descriptor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SyncCurrentBookmarkItemCommandDescriptor">
            <summary>
            Gets the sync current bookmark item command descriptor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ValidateSignaturesCommandDescriptor">
            <summary>
            Gets the validate signatures command descriptor.
            </summary>
            <value>The validate signatures command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ShowSignaturePropertiesDialogCommandDescriptor">
            <summary>
            Gets the show signature properties dialog command descriptor.
            </summary>
            <value>The show signature properties dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.ShowSignSignatureDialogCommandDescriptor">
            <summary>
            Gets the show sign signature dialog command descriptor.
            </summary>
            <value>The show sign signature dialog command descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.Descriptors.DefaultCommandDescriptors.SignSignatureCommandDescriptor">
            <summary>
            Gets the sign signature command descriptor.
            </summary>
            <value>The sign signature command descriptor.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.Descriptors.ValidateSignaturesCommandDescriptor">
            <summary>
            Represents the validate signatures command descriptor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.Descriptors.ValidateSignaturesCommandDescriptor.#ctor(Telerik.Windows.Documents.Commands.ValidateSignaturesCommand,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.Descriptors.ValidateSignaturesCommandDescriptor" /> class.
            </summary>
            <param name="command">The command.</param>
            <param name="isEnabled">if set to <c>true</c> [is enabled].</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.EventArgs.SyncCurrentBookmarkItemEventArgs">
            <summary>
            Class containing event data related to the sync current bookmark event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.EventArgs.SyncCurrentBookmarkItemEventArgs.HasResult">
            <summary>
            Gets whether the event has result data related to it.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.EventArgs.SyncCurrentBookmarkItemEventArgs.BookmarkItemResult">
            <summary>
            Gets the synced <see cref="T:Telerik.Windows.Documents.Fixed.Model.Navigation.BookmarkItem"/> instance.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FindCommand">
            <summary>
            Represents find command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FindCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindCommand.Execute(System.Object)">
            <summary>
            Executes the command.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FindCommandBase">
            <summary>
            Represents the base find command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindCommandBase.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FindCommandBase" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FindCommandBase.SearchOptions">
            <summary>
            Gets or sets the search options for the command.
            </summary>
            <value>The search options.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindCommandBase.HandleSearchResult(Telerik.Windows.Documents.Fixed.Search.SearchResult)">
            <summary>
            Handles the given search result.
            </summary>
            <param name="result">The search result.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FindPreviousCommand">
            <summary>
            Represents find previous command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindPreviousCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FindPreviousCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindPreviousCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FindPreviousCommand.Execute(System.Object)">
            <summary>
            Executes the command.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FitCommandBase">
            <summary>
            Represents fit command base.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitCommandBase.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FitCommandBase" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitCommandBase.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FitToPageCommand">
            <summary>
            Represents fit to page command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitToPageCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FitToPageCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitToPageCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FitToWidthCommand">
            <summary>
            Represents fit to width command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitToWidthCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FitToWidthCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FitToWidthCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase">
            <summary>
            Represents the base FixedDocumentViewer command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.Viewer">
            <summary>
            Gets or sets the viewer.
            </summary>
            <value>The viewer.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.CanExecute(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in
            its current state.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
            <returns>true if this command can be executed; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="E:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.CanExecuteChanged">
            <summary>
            Occurs when changes occur that affect whether or not the command should
            execute.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.FixedDocumentViewerCommandBase.OnCanExecuteChanged">
            <summary>
            Called when CanExecute is changed..
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.KeyBindingCollection">
            <summary>
            Represents KeyBindings collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.KeyBindingCollection.SetInputBindings(System.Windows.Input.InputBindingCollection)">
            <summary>
            Sets the input bindings.
            </summary>
            <param name="inputBindings">The input bindings.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.KeyBindingCollection.Add(System.Windows.Input.InputBinding)">
            <summary>
            Adds the specified input binding.
            </summary>
            <param name="inputBinding">The input binding.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.KeyBindingCollection.AddRange(System.Collections.Generic.IEnumerable{System.Windows.Input.InputBinding})">
            <summary>
            Adds the range.
            </summary>
            <param name="inputBindings">The input bindings.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.KeyBindingCollection.Clear">
            <summary>
            Clears the bindings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.KeyBindingCollection.RegisterCommandDescriptor(Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
            <summary>
            Registers the command descriptor.
            </summary>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
            <param name="modifierKeys">The modifier keys.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretCommandBase">
            <summary>
            Represents move caret command base.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretCommandBase.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretCommandBase" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretCommandBase.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretLineDownCommand">
            <summary>
            Represents move caret line down command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretLineDownCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretLineDownCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretLineDownCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretLineUpCommand">
            <summary>
            Represents move caret line up command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretLineUpCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretLineUpCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretLineUpCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToEndOfDocumentCommand">
            <summary>
            Represents move caret to end of document command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToEndOfDocumentCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToEndOfDocumentCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToEndOfDocumentCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToLineEndCommand">
            <summary>
            Represents move caret to line end command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToLineEndCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToLineEndCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToLineEndCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToLineStartCommand">
            <summary>
            Represents move caret to line start command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToLineStartCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToLineStartCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToLineStartCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToNextCharacterCommand">
            <summary>
            Represents move caret to next character command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToNextCharacterCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToNextCharacterCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToNextCharacterCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToNextWordCommand">
            <summary>
            Represents move caret to next word command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToNextWordCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToNextWordCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToNextWordCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToPreviousCharacterCommand">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToPreviousCharacterCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToPreviousCharacterCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToPreviousCharacterCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToPreviousWordCommand">
            <summary>
            Represents move caret to previous word command. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToPreviousWordCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToPreviousWordCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToPreviousWordCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.MoveCaretToStartOfDocumentCommand">
            <summary>
            Represents move caret to start of document command. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToStartOfDocumentCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.MoveCaretToStartOfDocumentCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.MoveCaretToStartOfDocumentCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.RotateClockwiseCommand">
            <summary>
            Represents rotate clockwise command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateClockwiseCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.RotateClockwiseCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateClockwiseCommand.Execute(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.RotateCommandBase">
            <summary>
            Represents rotate command base.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCommandBase.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.RotateCommandBase" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCommandBase.CanExecuteOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCommandBase.RegisterRotation(Telerik.Windows.Documents.UI.RotationAngle,Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Registers the rotation.
            </summary>
            <param name="initialAngle">The initial angle.</param>
            <param name="rotatedAngle">The rotated angle.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCommandBase.GetRotatedAngle(Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Gets the rotated angle.
            </summary>
            <param name="angle">The angle.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCommandBase.SetRotationAngle(Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Sets the rotation angle.
            </summary>
            <param name="angle">The angle.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.RotateCounterClockwiseCommand">
            <summary>
            Represents rotate counter clockwise command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCounterClockwiseCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.RotateCounterClockwiseCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.RotateCounterClockwiseCommand.Execute(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SaveAsPdfDocumentCommand">
            <summary>
            Represents the save as pdf document command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SaveAsPdfDocumentCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SaveAsPdfDocumentCommand" /> class.
            </summary>
            <param name="viewer">The viewer</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SaveAsPdfDocumentCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SaveAsPdfDocumentCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ScrollCommand">
            <summary>
            Represents scroll command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ScrollCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ScrollCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
            <param name="offsetX">The offset X.</param>
            <param name="offsetY">The offset Y.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ScrollCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.OpenPdfDocumentCommand">
            <summary>
            Represents open pdf document command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.OpenPdfDocumentCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.OpenPdfDocumentCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.OpenPdfDocumentCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.PageDownCommand">
            <summary>
            Represents page down command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageDownCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.PageDownCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageDownCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageDownCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.PageUpCommand">
            <summary>
            Represents page up command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageUpCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.PageUpCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageUpCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PageUpCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.PrintPdfDocumentCommand">
            <summary>
            Represents print pdf document command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PrintPdfDocumentCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.PrintPdfDocumentCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PrintPdfDocumentCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.PrintPdfDocumentCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SelectAllCommand">
            <summary>
            Represents select all command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SelectAllCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SelectAllCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SelectAllCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SelectAllCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SetFixedDocumentPresenterCommand">
            <summary>
            Represents set fixed document presenter command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SetFixedDocumentPresenterCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SetFixedDocumentPresenterCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SetFixedDocumentPresenterCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ShowFindDialogCommand">
            <summary>
            Represents ShowFindDialog command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowFindDialogCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ShowFindDialogCommand" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowFindDialogCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowFindDialogCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommand">
            <summary>
            Represents the show signature properties dialog command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommandContext">
            <summary>
            Represents the show signature properties dialog command context.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommandContext.SignatureField">
            <summary>
            Gets or sets the signature field.
            </summary>
            <value>The signature field.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ShowSignaturePropertiesDialogCommandContext.SignatureValidationProperties">
            <summary>
            Gets or sets the signature validation properties.
            </summary>
            <value>The signature validation properties.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommand">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommand.CanExecuteOverride(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in
            its current state.
            </summary>
            <param name="parameter">The paraammeter should of type <see cref="T:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommandContext"/>.</param>
            <returns>true if this command can be executed; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">The paraammeter should of type <see cref="T:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommandContext"/>.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommandContext">
            <summary>
            Represents the show sign signature dialog command context.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ShowSignSignatureDialogCommandContext.SignatureField">
            <summary>
            Gets or sets the signature field.
            </summary>
            <value>The signature field.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommand">
            <summary>
            Represents the sign signature field command
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SignSignatureFieldCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SignSignatureFieldCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter should be of type <see cref="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext"/>.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SignSignatureFieldCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">The parameter should be of type <see cref="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext"/>.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext">
            <summary>
            Represents the sign signature field command context.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext.#ctor(Telerik.Windows.Documents.Fixed.Model.InteractiveForms.SignatureField,System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext" /> class.
            </summary>
            <param name="signatureField">The signature field.</param>
            <param name="certificate">The certificate.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext.SignatureField">
            <summary>
            Gets or sets the signature field.
            </summary>
            <value>The signature field.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext.Certificate">
            <summary>
            Gets or sets the certificate used for signing the signature.
            </summary>
            <value>The certificate.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.SignSignatureFieldCommandContext.Appearances">
            <summary>
            Gets or sets the form source instances used for presenting the appearances of the signature.
            </summary>
            <value>The form source instances.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.SyncCurrentBookmarkItemCommand">
            <summary>
            Command representing the action of synchronizing to the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Navigation.BookmarkItem" /> instance related to the viewer's current page.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SyncCurrentBookmarkItemCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.SyncCurrentBookmarkItemCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">Instance of the document viewer the command will be applied for.</param>
        </member>
        <member name="E:Telerik.Windows.Documents.Commands.SyncCurrentBookmarkItemCommand.OnExecuted">
            <summary>
            Invoked after the command is executed. The event arguments contain data related to the result of the command execution.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.SyncCurrentBookmarkItemCommand.Execute(System.Object)">
            <summary>
            Finds the most appropriate <see cref="T:Telerik.Windows.Documents.Fixed.Model.Navigation.BookmarkItem" /> instance according to the viewer's current page.
            </summary>
            <param name="parameter">Can be set to <c>null</c>.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand">
            <summary>
            Represents validate signatures command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand" /> class.
            </summary>
            <param name="viewer">The viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand.Result">
            <summary>
            Gets the validation result.
            </summary>
            <value>The validation result.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ValidateSignaturesCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ValidateSignaturesCommandContext">
            <summary>
            Represents the validate signatures command context.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ValidateSignaturesCommandContext.FieldNameCollection">
            <summary>
            Gets or sets the field name collection.
            </summary>
            <value>The field name collection.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Commands.ValidateSignaturesCommandContext.ValidationProperties">
            <summary>
            Gets or sets the validation properties.
            </summary>
            <value>The validation properties.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ZoomInCommand">
            <summary>
            Represents zoom in command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomInCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ZoomInCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomInCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomInCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Commands.ZoomOutCommand">
            <summary>
            Represents zoom out command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomOutCommand.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Commands.ZoomOutCommand" /> class.
            </summary>
            <param name="fixedDocumentViewerBase">The fixed document viewer base.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomOutCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require
            data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Commands.ZoomOutCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether the command can be executed.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>Returns if the command can be executed.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs">
            <summary>
            Provides data for the HyperlinkClicked event.
            </summary> 
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs.Url">
            <summary>
            The URL of the hyperlink.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs.Handled">
            <summary>
            Get or set a value indicating whether the event is handled.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs.IsTrustedUrl">
            <summary>
            Gets a value tha indicates if the URL passes our validation. If the URL is not trusted, the hyperlink will not be opened. Set this property to true to open the hyperlink.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.HyperlinkClickedEventArgs"/> class.
            </summary>
            <param name="url">The URL of the hyperlink.</param> 
            <param name="isTrustedUrl">A value indicating whether the URL is trusted.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.PdfDocumentSourceValueConverter">
            <summary>
            Represents value converter that can be used for data binding <see cref="T:Telerik.Windows.Controls.RadPdfViewer"/>. 
            Supports conversion from string representing URI, Uri and stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSourceValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the source data before passing it to the target for display
            in the UI.
            </summary>
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the target dependency property.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSourceValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the target data before passing it to the source object.  This
            method is called only in <see cref="F:System.Windows.Data.BindingMode.TwoWay" />
            bindings.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.NoEditing">
            <summary>
            Used when not editing the annotation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.ReleasingNoEditingUI">
            <summary>
            Used when the widget is initially clicked and its static appearance should be removed from the UI.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.InitializingEditingUI">
            <summary>
            Used when the widget is initializing its editing UI (textbox, combobox, listbox...).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.IsEditingVisibleUI">
            <summary>
            Used when the widget is being edited and is visible in the UI.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.IsEditingHiddenUI">
            <summary>
            Used when the widget is being edited and is not visible in the UI. (when the user click on a combo for instance and scrolls a little so the combo is not visible).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.UIProviders.VariableContentWidgetUIProvider.EditingState.ReleasingEditingUI">
            <summary>
            Used when the widget modified value is being applied to the field.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Actions.ActionsManager.Execute(Telerik.Windows.Documents.UI.IFixedDocumentPresenter,Telerik.Windows.Documents.Fixed.Model.Actions.Action)">
            <summary>
            Executes the specified action.
            </summary>
            <param name="presenter">The current PdfViewer control.</param>
            <param name="action">The action.</param>
            <returns>Returns true if the action was executed successfully, otherwise false.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.CursorMode">
            <summary>
            Represents cursor mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.CursorMode.Default">
            <summary>
            Represents default cursor mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.CursorMode.Text">
            <summary>
            Represents text cursor mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.CursorMode.Link">
            <summary>
            Represents link cursor mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.CursorMode.Pan">
            <summary>
            Represents pan cursor mode.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.CursorsCollection">
            <summary>
            Represents cursors collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.CursorsCollection.Item(Telerik.Windows.Documents.Fixed.UI.CursorMode)">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Input.Cursor" /> for the specified cursor mode.
            </summary>
            <value>The cursor.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.FixedDocumentViewerDialogContext">
            <summary>
            Represents context for FixedDocumentViewer dialog instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Dialogs.FixedDocumentViewerDialogContext.FixedDocumentViewer">
            <summary>
            Gets the fixed document viewer.
            </summary>
            <value>The fixed document viewer.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.FixedDocumentViewerDialogContext.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.FixedDocumentViewerDialogContext" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.FindDialogContext">
            <summary>
            Represents FixedDialogContext class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.FindDialogContext.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.FindDialogContext" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.IFindDialog">
            <summary>
            Represents find dialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.IFindDialog.ShowDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.FindDialogContext)">
            <summary>
            Shows the dialog.
            </summary>
            <param name="context">The FindDialogContext.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.IPasswordRequiredDialog">
            <summary>
            Represents the password required dialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.IPasswordRequiredDialog.ShowDialog">
            <summary>
            Shows the dialog.
            </summary>
            <returns>If the password is entered.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Dialogs.IPasswordRequiredDialog.Password">
            <summary>
            Gets or sets the current document password.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignaturePropertiesDialog">
            <summary>
            Represents the singature properties dialog interface.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignaturePropertiesDialog.ShowDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext)">
            <summary>
            Shows the dialog.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignSignatureDialog">
            <summary>
            Represents the sing singature dialog interface.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignSignatureDialog.ShowDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.SignSignatureDialogContext)">
            <summary>
            Shows the dialog.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext">
            <summary>
             Represents the signature properties dialog context.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext.#ctor(Telerik.Windows.Documents.Fixed.Model.InteractiveForms.SignatureField,Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext" /> class.
            </summary>
            <param name="signatureField">The signature field.</param>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext.SignatureField">
            <summary>
            Gets the signature field.
            </summary>
            <value>The signature field.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignaturePropertiesDialogContext.SignatureValidationProperties">
            <summary>
            Gets or sets the signature validation properties.
            </summary>
            <value>The signature validation properties.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignSignatureDialogContext">
            <summary>
            Represents the sign signature dialog context.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignSignatureDialogContext.#ctor(Telerik.Windows.Documents.Fixed.Model.InteractiveForms.SignatureField,Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignSignatureDialogContext" /> class.
            </summary>
            <param name="signatureField">The signature field.</param>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Dialogs.SignSignatureDialogContext.SignatureField">
            <summary>
            Gets the signature field.
            </summary>
            <value>The signature field.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers">
            <summary>
            Contains all default UILayers names.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers.PagesLayer">
            <summary>
            Pages layer name
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers.ContentElementsUILayer">
            <summary>
            Content elements layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers.ForegroundLayer">
            <summary>
            Foreground layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers.SelectionLayer">
            <summary>
            Selection layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.Layers.DefaultUILayers.AnnotationsUILayer">
            <summary>
            Annotations layer name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer">
            <summary>
            Represents UI layer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer.Initialize(Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext)">
            <summary>
            Initializes the UI layer.
            </summary>
            <param name="context">The initialize context.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer.Update(Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext)">
            <summary>
            Updates the UI layer.
            </summary>
            <param name="context">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer.Clear">
            <summary>
            Clears this layer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer.UIElement">
            <summary>
            Gets the UI element for the UI layer.
            </summary>
            <value>The UI element.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayerContainer">
            <summary>
            Represents UI layer container.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayerContainer.UILayers">
            <summary>
            Gets the UI layers.
            </summary>
            <value>The UI layers.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.IUILayerContainer.IsLayerSupported(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer)">
            <summary>
            Determines whether the layer is supported.
            </summary>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.PrintingWorkaroundForNetFramework46UILayer">
            <summary>
            <para>This layer was introduced as a workaround for breaking change introduced in .NET Framework 4.6.</para>
            <para>The bug is reported here: https://connect.microsoft.com/VisualStudio/feedback/details/1980419/printing-with-printdialog-fails-when-net-4-6-is-installed </para>
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext">
            <summary>
            Represents UI layer initialize context
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.Presenter">
            <summary>
            Gets the presenter.
            </summary>
            <value>The presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.Page">
            <summary>
            Gets the page.
            </summary>
            <value>The page.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.Owner">
            <summary>
            Gets the owner.
            </summary>
            <value>The owner.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.AddFocusableElement(System.Windows.DependencyObject)">
            <summary>
            Adds a focusable element.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerInitializeContext.RemoveFocusableElement(System.Windows.DependencyObject)">
            <summary>
            Removes a focusable element.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder">
            <summary>
            Represents UI layers builder
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder.BuildUILayersOverride(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayerContainer)">
            <summary>
            Builds the UI layers.
            </summary>
            <param name="uiLayerContainer">The UI layer container.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerStack">
            <summary>
            Represents UI layers stack.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext">
            <summary>
            Represents UI layer update context.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext.#ctor(System.Windows.Rect)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext" /> class.
            </summary>
            <param name="viewport">The viewport.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext.Viewport">
            <summary>
            Gets the viewport.
            </summary>
            <value>The viewport.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext.ShouldShowSelectionMarkers">
            <summary>
            Gets or sets the should show selection markers.
            </summary>
            <value>The should show selection markers.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager">
            <summary>
            Provides properties for customizing PdfViewer UI functionality.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.MaxImageSize">
            <summary>
            Gets or sets the maximum component size for image.
            </summary>
            <value>The maximum size. If the value is null the image will not be resized.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterFindDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.IFindDialog)">
            <summary>
            Registers the find dialog.
            </summary>
            <param name="findDialog">The find dialog.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterSignaturePropertiesDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignaturePropertiesDialog)">
            <summary>
            Registers the signature properties dialog.
            </summary>
            <param name="signaturePropertiesDialog">The signature properties dialog.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterSignSignatureDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.ISignSignatureDialog)">
            <summary>
            Registers the sign signature dialog.
            </summary>
            <param name="signSignatureDialog">The sign signature dialog.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterPasswordRequiredDialog(Telerik.Windows.Documents.Fixed.UI.Dialogs.IPasswordRequiredDialog)">
            <summary>
            Registers the PasswordRequired dialog. 
            </summary>
            <param name="passwordRequiredDialog">The PasswordReqired dialog</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterLayersBuilder(Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder)">
            <summary>
            Registers the default layers builder for RadPDFViewer.
            </summary>
            <param name="layersBuilder">The layers builder.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterPrintLayersBuilder(Telerik.Windows.Documents.Fixed.UI.Layers.UILayersBuilder)">
            <summary>
            Registers the print layers builder for RadPDFViewer in case it is differs from the default.
            </summary>
            <param name="printLayersBuilder">The print layers builder.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Extensibility.ExtensibilityManager.RegisterFilter(Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Filters.IPdfFilter)">
            <summary>
            Registers the filter.
            </summary>
            <param name="filter">The filter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.FormComboBox">
            <summary>
            Represents a combo box used for editing of the combo box interactive form.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.FormListBox">
            <summary>
            Represents a list box used for editing of the list box interactive form.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.FormListBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.FormListBox" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.FormTextBox">
            <summary>
            Represents a text box used for editing of the text box interactive form.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.FormTextBox.OnTouchDown(System.Windows.Input.TouchEventArgs)">
            <summary>
            Handles TouchDown and focuses the FormTextBox element. 
            </summary>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.PointerHandlers.PointerHandlersController">
            <summary>
            Represents pointer handlers controller class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.PointerHandlers.PointerHandlersController.#ctor(Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.PointerHandlers.PointerHandlersController" /> class.
            </summary>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.Page">
            <summary>
            Represents a Fixed Page UI.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Page.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized" /> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized" />
            is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.UI.Page.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.UI.Page" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode">
            <summary>
            Represents FixedDocumentViewer mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode.None">
            <summary>
            Represents default mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode.Pan">
            <summary>
            Represents pan mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.FixedDocumentViewerMode.TextSelection">
            <summary>
            Represents text selection mode.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.ScaleMode">
            <summary>
            Represents scale mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.ScaleMode.Normal">
            <summary>
            Represents normal mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.ScaleMode.FitToWidth">
            <summary>
            Represents fit to width mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.UI.ScaleMode.FitToPage">
            <summary>
            Represents fit to page mode.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.UI.UIElementsHelper">
            <summary>
            This class is used to map PDF content element classes to corresponding WPF/SL UIElements classes. This allows faster re-drawing of elements.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase">
            <summary>
            Represents pages layout manager base.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.#ctor(Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase" /> class.
            </summary>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.ContentSize">
            <summary>
            Gets or sets the size of the content.
            </summary>
            <value>The size of the content.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.PageMargins">
            <summary>
            Gets the page margins.
            </summary>
            <value>The page margins.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.ScaleFactor">
            <summary>
            Gets the scale factor.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.Presenter">
            <summary>
            Gets the presenter.
            </summary>
            <value>The presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.RotationAngleToDouble">
            <summary>
            Gets the rotation angle numeric value.
            </summary>
            <value>The numeric value of the rotation angle.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.UpdateLayout(System.Windows.Size)">
            <summary>
            Updates the layout.
            </summary>
            <param name="viewportSize">Size of the viewport.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetPageVerticalOffset(System.Int32)">
            <summary>
            Gets the page vertical offset.
            </summary>
            <param name="pageNo">The page no.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetViewPointFromLocation(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Point,System.Windows.Point@)">
            <summary>
            Gets the view point from location.
            </summary>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <param name="point">The point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.Release">
            <summary>
            Releases this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.RotateSize(System.Windows.Size,Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Rotates the size.
            </summary>
            <param name="pageSize">Size of the page.</param>
            <param name="rotationAngle">The rotation angle.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.CreateTransformations(System.Windows.Size,Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Creates the transformations.
            </summary>
            <param name="pageSize">Size of the page.</param>
            <param name="rotationAngle">The rotation angle.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetRotationAngleNumeric(Telerik.Windows.Documents.UI.RotationAngle)">
            <summary>
            Gets the rotation angle numeric.
            </summary>
            <param name="angle">The angle.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetPageLayoutInfoFromPage(Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            Gets the page layout info from page.
            </summary>
            <param name="page">The page.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetScaledPageSize(Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            Gets the size of the scaled page.
            </summary>
            <param name="page">The page.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetNearestPageLayoutInfo(System.Windows.Point)">
            <summary>
            Gets the nearest page layout info.
            </summary>
            <param name="point">The point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.PagesLayoutManagerBase.GetPagesLayoutInfos">
            <summary>
            Gets the pages layout infos.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo">
            <summary>
            Represents fixed page layout info.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.#ctor(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Rect)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo" /> class.
            </summary>
            <param name="page">The page.</param>
            <param name="positionInView">The position in view.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.#ctor(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Rect,System.Windows.Media.GeneralTransform)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo" /> class.
            </summary>
            <param name="page">The page.</param>
            <param name="positionInView">The position in view.</param>
            <param name="transformations">The transformations.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.#ctor(Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo" /> class.
            </summary>
            <param name="other">The other.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.FixedPage">
            <summary>
            Gets the fixed page.
            </summary>
            <value>The fixed page.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.PositionInView">
            <summary>
            Gets the position in view.
            </summary>
            <value>The position in view.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.Transformations">
            <summary>
            Gets the transformations.
            </summary>
            <value>The transformations.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.InverseTransformations">
            <summary>
            Gets the inverse transformations.
            </summary>
            <value>The inverse transformations.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.CompareFixedPageLayoutInfo(Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo,System.Double)">
            <summary>
            Compares the fixed page layout info.
            </summary>
            <param name="info">The info.</param>
            <param name="offsetY">The offset Y.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.GetViewportIntersectionRect(System.Windows.Rect)">
            <summary>
            Gets the viewport intersection rect.
            </summary>
            <param name="viewport">The viewport.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo.ApplyTransformation(System.Windows.Rect,System.Windows.Media.GeneralTransform)">
            <summary>
            Applies the transformation.
            </summary>
            <param name="rect">The rect.</param>
            <param name="transformation">The transformation.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo">
            <summary>
            Represents the single page info.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.#ctor(System.Double,System.Double,System.Boolean,System.Boolean,Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Rect,System.Windows.Media.GeneralTransform)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo" /> class.
            </summary>
            <param name="topOffsetInPresenter">The top offset in presenter.</param>
            <param name="bottomPositionInView">The bottom position in view.</param>
            <param name="intersectsHorizontally">The intersects hotizontally.</param>
            <param name="intersectsVertically">The intersects vertically.</param>
            <param name="page">The page.</param>
            <param name="positionInView">The position in view.</param>
            <param name="transformations">The transformations.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.IntersectsWithViewportVertically">
            <summary>
            Gets a value indicating wheter the page intersects with the viewport vertically.
            </summary>
            <value>The value indicating wheter the page intersects with the viewport vertically.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.IntersectsWithViewportHorizontally">
            <summary>
            Gets a value indicating wheter the page intersects with the viewport horizontally.
            </summary>
            <value>The value indicating wheter the page intersects with the viewport horizontally.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.IntersectsWithViewport">
            <summary>
            Gets a value indicating wheter the page intersects with the viewport.
            </summary>
            <value>The value indicating wheter the page intersects with the viewport.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.VerticalOffset">
            <summary>
            Gets the vertical offset.
            </summary>
            <value>The vertical offset.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.BottomPositionInView">
            <summary>
            Gets the bottom position in view.
            </summary>
            <value>The bottom position in view.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.TopPositionInView">
            <summary>
            Gets the top position in view.
            </summary>
            <value>The top position in view.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Layout.SinglePageInfo.LeftPositionInView">
            <summary>
            Gets the left position in view.
            </summary>
            <value>The left position in view.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.DocumentChangedEventArgs">
            <summary>
            Represents document changed event args.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.DocumentChangedEventArgs.#ctor(Telerik.Windows.Documents.Fixed.Model.RadFixedDocument,Telerik.Windows.Documents.Fixed.Model.RadFixedDocument)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.DocumentChangedEventArgs" /> class.
            </summary>
            <param name="oldDocument">The old document.</param>
            <param name="newDocument">The new document.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.DocumentChangedEventArgs.OldDocument">
            <summary>
            Gets or sets the old document.
            </summary>
            <value>The old document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.DocumentChangedEventArgs.NewDocument">
            <summary>
            Gets or sets the new document.
            </summary>
            <value>The new document.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.CurrentPageChangedEventArgs">
            <summary>
            Represents current page changed event args.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.CurrentPageChangedEventArgs.#ctor(Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.CurrentPageChangedEventArgs" /> class.
            </summary>
            <param name="page">The page.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.CurrentPageChangedEventArgs.CurrentPage">
            <summary>
            Gets or sets the current page.
            </summary>
            <value>The current page.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource">
            <summary>
            Represents a fixed document source, which can load a document from URI or stream.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.Loaded">
            <summary>
            Occurs when document is loaded. It can be loaded asynchronously in case the provided URI is absolute.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.DocumentUnhandledException">
            <summary>
            Occurs when unhandled exception is thrown during document import.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.Document">
            <summary>
            Gets the fixed document loaded from this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.Finalize">
            <summary>
            Ensures that any unmanaged resources are released when object instance is finalized.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.#ctor(System.Uri,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource" /> class.
            </summary>
            <param name="uri">The URI from which to load the fixed document.</param>
            <param name="settings">The settings.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.#ctor(System.IO.Stream,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource" /> class.
            </summary>
            <param name="stream">The stream from which to load the fixed document.</param>
            <param name="settings">The settings.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.ExecuteLoadAction(System.Action)">
            <summary>
            This method is created in order to allow a better testing experience.
            </summary>
            <param name="action">The action for loading the document.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.OnDocumentLoaded(System.EventArgs)">
            <summary>
            Raises the <see cref="E:DocumentLoaded"/> event.
            </summary>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.OnLoaded(System.EventArgs)">
            <summary>
            Raises the <see cref="E:Loaded"/> event.
            </summary>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource.CreateDocumentFromStreamOverride(System.IO.Stream,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Creates the document from a stream.
            </summary>
            <param name="stream">The stream from which to create the document.</param>
            <param name="settings">The settings.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter">
            <summary>
            Converts an <see cref="T:System.Uri"/> to <see cref="T:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSource"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter.CreateFixedDocumentStreamSourceFromUri(System.Uri,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Creates the fixed document stream source from URI.
            </summary>
            <param name="uri">The URI.</param>
            <param name="settings">The settings.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether the type converter can convert an object from the specified
            type to the type of this converter.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="sourceType">The type you want to convert from.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether the type converter can convert an object to the specified
            type.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="destinationType">The type you want to convert to.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts from the specified value to the intended conversion type of
            the converter.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to
            use as the current culture.</param>
            <param name="value">The value to convert to the type of this converter.</param>
            <exception cref="T:System.NotImplementedException">
            <see cref="M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)" />
            not implemented in base <see cref="T:System.ComponentModel.TypeConverter" />.
            </exception>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.FixedDocumentStreamSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Creates the fixed document stream source from URI.
            </summary>
            <param name="context">The context.</param>
            <param name="culture">The culture.</param>
            <param name="value">The value.</param>
            <param name="settings">The settings.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource">
            <summary>
            Represents a PDF document source, which can load a document from URI or stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource"/> class.
            </summary>
            <param name="uri">The URI from which to load the PDF document.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.#ctor(System.Uri,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource" /> class.
            </summary>
            <param name="uri">The URI from which to load the PDF document.</param>
            <param name="settings">The settings that will be used to load the PDF document.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource"/> class.
            </summary>
            <param name="stream">The stream from which to load the PDF document.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.#ctor(System.IO.Stream,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource" /> class.
            </summary>
            <param name="stream">The stream from which to load the PDF document.</param>
            <param name="settings">The settings that will be used to load the PDF document.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.CreateDocumentFromStreamOverride(System.IO.Stream,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Creates the document from a stream.
            </summary>
            <param name="stream">The stream from which to create the document.</param>
            <param name="settings">The settings</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSource.Finalize">
            <summary>
             Ensures that any unmanaged resources are released when object instance is finalized.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.PdfDocumentSourceConverter">
            <summary>
            Converts an <see cref="T:System.Uri"/> to <see cref="T:Telerik.Windows.Documents.Fixed.PdfDocumentSource"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.PdfDocumentSourceConverter.CreateFixedDocumentStreamSourceFromUri(System.Uri,Telerik.Windows.Documents.Fixed.FormatProviders.Pdf.Import.PdfImportSettings)">
            <summary>
            Creates the fixed document stream source from URI.
            </summary>
            <param name="uri">The URI.</param>
            <param name="settings">The settings.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Print.PrintSettings">
            <summary>
            Represents print settings. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Print.PrintSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Print.PrintSettings" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Print.PrintSettings.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Print.PrintSettings" /> class.
            </summary>
            <param name="documentName">Name of the document.</param>
            <param name="useDefaultPrinter">The use default printer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Print.PrintSettings.Default">
            <summary>
            Gets the default.
            </summary>
            <value>The default.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Print.PrintSettings.DocumentName">
            <summary>
            Gets or sets the name of the document.
            </summary>
            <value>The name of the document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Print.PrintSettings.UseDefaultPrinter">
            <summary>
            Gets or sets the use default printer.
            </summary>
            <value>The use default printer.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Print.PrintSettings.PageMargins">
            <summary>
            Gets or sets the page margins.
            </summary>
            <value>The page margins.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.BoolToVisibilityConverter">
            <summary>
            Represents a bool to <see cref="T:System.Windows.Visibility"/> converter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.BoolToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value from bool to <see cref="T:System.Windows.Visibility"/>.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.BoolToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:System.Windows.Visibility"/> to bool.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.DoubleToStringPercentConverter">
            <summary>
            Represents double to string percent converter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.DoubleToStringPercentConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the source data before passing it to the target for display
            in the UI.
            </summary>
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the target dependency property.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.DoubleToStringPercentConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the target data before passing it to the source object.  This
            method is called only in <see cref="F:System.Windows.Data.BindingMode.TwoWay" />
            bindings.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.EnumToBooleanConverter`1">
            <summary>
            Represents enum to boolean converter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.EnumToBooleanConverter`1.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the source data before passing it to the target for display
            in the UI.
            </summary>
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the target dependency property.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.EnumToBooleanConverter`1.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the target data before passing it to the source object.  This
            method is called only in <see cref="F:System.Windows.Data.BindingMode.TwoWay" />
            bindings.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.FixedDocumentViewerModeConverter">
            <summary>
            Represents fixed document viewer mode converter.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.IndexConverter">
            <summary>
            Represents index converter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.IndexConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the source data before passing it to the target for display
            in the UI.
            </summary>
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the target dependency property.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.IndexConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the target data before passing it to the source object.  This
            method is called only in <see cref="F:System.Windows.Data.BindingMode.TwoWay" />
            bindings.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.ScaleModeConverter">
            <summary>
            Represents scale mode converter.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Converters.ThumbnailsConverter">
            <summary>
            Represents thumbnail converter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Converters.ThumbnailsConverter.ThumbnailsHeight">
            <summary>
            Gets or sets the height of the thumbnails.
            </summary>
            <value>The height of the thumbnails.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.ThumbnailsConverter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Converters.ThumbnailsConverter" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.ThumbnailsConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the source data before passing it to the target for display
            in the UI.
            </summary>
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the target dependency property.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Converters.ThumbnailsConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Modifies the target data before passing it to the source object.  This
            method is called only in <see cref="F:System.Windows.Data.BindingMode.TwoWay" />
            bindings.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by
            the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions">
            <summary>
            Represents input binding collection extensions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptor(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
            <summary>
            Registers the command descriptor.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
            <param name="modifierKeys">The modifier keys.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptorOrShift(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
            <summary>
            Registers the command descriptor or shift.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
            <param name="modifierKeys">The modifier keys.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptorOrShiftOrAlt(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key,System.Windows.Input.ModifierKeys)">
            <summary>
            Registers the command descriptor or shift or alt.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
            <param name="modifierKeys">The modifier keys.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptorWithCtrlOrApple(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key)">
            <summary>
            Registers the command descriptor with CTRL or apple.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptorWithCtrlOrApplePlusShift(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key)">
            <summary>
            Registers the command descriptor with CTRL or apple plus shift.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Extensions.InputBindingCollectionExtensions.RegisterCommandDescriptorWithCtrlOrApplePlusAlt(System.Windows.Input.InputBindingCollection,Telerik.Windows.Documents.Commands.Descriptors.CommandDescriptorBase,System.Windows.Input.Key)">
            <summary>
            Registers the command descriptor with CTRL or apple plus alt.
            </summary>
            <param name="thisInstance">The this instance.</param>
            <param name="commandDescriptor">The command descriptor.</param>
            <param name="key">The key.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentPresenterNames">
            <summary>
            Represents fixed document presenter names.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.FixedDocumentPresenterNames.FixedDocumentPagesPresenter">
            <summary>
            Name for FixedDocumentPagesPresenter.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.FixedDocumentPresenterNames.FixedDocumentSinglePageViewPresenter">
            <summary>
            Name for FixedDocumentSinglePageViewPresenter.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter">
            <summary>
            Represents fixed document single page presenter.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.PageProperty">
            <summary>
            Identifies the Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.Page property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.Settings">
            <summary>
            Gets or sets the settings.
            </summary>
            <value>The settings.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.Page">
            <summary>
            Gets or sets the page.
            </summary>
            <value>The page.</value>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.ContentCreated">
            <summary>
            Occurs when the content is created.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePagePresenter.IsLayerSupported(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer)">
            <summary>
            Determines whether the layer is supported.
            </summary>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedPageContentPresenter.IsLayerSupported(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer)">
            <summary>
            Determines whether the layer is supported.
            </summary>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedPagePrintPresenter">
            <summary>
            Represents fixed page print presenter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedPagePrintPresenter.IsLayerSupported(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer)">
            <summary>
            Determines whether the layer is supported.
            </summary>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter">
            <summary>
            Represents fixed document with pages.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized" /> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized" />
            is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.PagesLayoutManager">
            <summary>
            Gets the pages layout manager.
            </summary>
            <value>The pages layout manager.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.CurrentPageLayoutInfo">
            <summary>
            Gets or sets the current page layout info.
            </summary>
            <value>The current page layout info.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.CurrentPageNo">
            <summary>
            Gets the current page number.
            </summary>
            <value>The current page number.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.GetLocationFromViewPoint(System.Windows.Point,Telerik.Windows.Documents.Fixed.Model.RadFixedPage@,System.Windows.Point@)">
            <summary>
            Gets the location from view point.
            </summary>
            <param name="positionInView">The position in view.</param>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.UpdateScrollOffsetFromPosition(Telerik.Windows.Documents.Fixed.Text.TextPosition)">
            <summary>
            Updates the scroll offset from position.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.MeasureOverride(System.Windows.Size)">
            <summary>
            When overridden in a derived class, measures the size in layout required
            for child elements and determines a size for the <see cref="T:System.Windows.FrameworkElement" />-derived
            class.
            </summary>
            <param name="constraint"></param>
            <returns>
            The size that this element determines it needs during layout, based
            on its calculations of child element sizes.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.ArrangeOverride(System.Windows.Size)">
            <summary>
            Called to arrange and size the content of a <see cref="T:System.Windows.Controls.Control" />
            object.
            </summary>
            <param name="arrangeBounds">The computed size that is used to arrange the content.</param>
            <returns>The size of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPagesPresenter.ReleaseDocumentResourcesOverride">
            <summary>
            Releases the document resources override.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase">
            <summary>
            Represents base document presenter class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.CurrentPageChanged">
            <summary>
            Occurs when current page is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.CurrentPage">
            <summary>
            Gets the current page.
            </summary>
            <value>The current page.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.PointerHandlersController">
            <summary>
            Gets the mouse handlers controller.
            </summary>
            <value>The mouse handlers controller.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.Owner">
            <summary>
            Gets the owner.
            </summary>
            <value>The owner.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.VisiblePagesCacheOffset">
            <summary>
            Defines the amount of pages to be cached before and after the visible ones. The default value is one.
            </summary>
            <value>The amount of pages to cache before and after the visible ones. The default value is one.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.PagesLayoutManager">
            <summary>
            Gets the pages layout manager.
            </summary>
            <value>The pages layout manager.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.CurrentPageNo">
            <summary>
            Gets the current page number.
            </summary>
            <value>The current page number.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.VisiblePresenters">
            <summary>
            Gets the visible presenters.
            </summary>
            <value>The visible presenters.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ReleasedPresenters">
            <summary>
            Gets the released presenters.
            </summary>
            <value>The released presenters.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.LayoutRoot">
            <summary>
            Gets the layout root.
            </summary>
            <value>The layout root.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ShouldShowSelectionMarkers">
            <summary>
            Gets or sets the value indicating wheter the selection markes should be shown.
            </summary>
            <value>The value indicating wheter the selection markes should be shown.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ViewportSize">
            <summary>
            Gets or sets the size of the viewport.
            </summary>
            <value>The size of the viewport.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.VisiblePages">
            <summary>
            Gets or sets the visible pages. Setting this property ensures that visible pages content is loaded.
            </summary>
            <value>The visible pages.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.PageUp">
            <summary>
            Goes page up.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.PageDown">
            <summary>
            Goes page down.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GoToPage(System.Int32)">
            <summary>
            Goes to page.
            </summary>
            <param name="pageNo">The page no.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GoToDestination(Telerik.Windows.Documents.Fixed.Model.Navigation.Destination)">
            <summary>
            Goes to destination.
            </summary>
            <param name="destination">The destination.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GetLocationFromViewPoint(System.Windows.Point,Telerik.Windows.Documents.Fixed.Model.RadFixedPage@,System.Windows.Point@)">
            <summary>
            Gets the location from view point.
            </summary>
            <param name="viewPoint">The view point.</param>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GetViewPointFromLocation(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Point,System.Windows.Point@)">
            <summary>
            Gets the view point from location.
            </summary>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <param name="viewPoint">The view point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ShowSelectionMarkers">
            <summary>
            Shows the selection markers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.HideSelectionMarkers">
            <summary>
            Hides the selection markers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.UpdatePresenterLayout">
            <summary>
            Updates the presenter layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.Initialize(Telerik.Windows.Documents.UI.IFixedDocumentViewer)">
            <summary>
            Initializes the presenter.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.Initialize(Telerik.Windows.Documents.UI.IFixedDocumentViewer,Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Initializes the specified owner.
            </summary>
            <param name="owner">The owner.</param>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.Release">
            <summary>
            Releases the presenter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.UpdateScrollOffsetFromPosition(Telerik.Windows.Documents.Fixed.Text.TextPosition)">
            <summary>
            Updates the scroll offset from position.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ReleaseOverride">
            <summary>
            Releases this instance override.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ReleaseDocumentResourcesOverride">
            <summary>
            Releases the document resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.DoOnScaleFactorChangedOverride(System.Double,System.Double)">
            <summary>
            Does the on scale factor changed.
            </summary>
            <param name="oldScaleFactor">The old scale factor.</param>
            <param name="newScaleFactor">The new scale factor.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.UpdateScrollBars(System.Windows.Size)">
            <summary>
            Updates the scroll bars.
            </summary>
            <param name="viewportSize">Size of the viewport.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ReleasePagePresenter(System.Int32)">
            <summary>
            Releases the page presenter.
            </summary>
            <param name="pageNo">The page no.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GetPagePresenter(Telerik.Windows.Documents.Fixed.Layout.FixedPageLayoutInfo)">
            <summary>
            Gets the page presenter.
            </summary>
            <param name="pageInfo">The page info.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.ArrangeOverride(System.Windows.Size)">
            <summary>
            Called to arrange and size the content of a <see cref="T:System.Windows.Controls.Control" />
            object.
            </summary>
            <param name="arrangeBounds">The computed size that is used to arrange the content.</param>
            <returns>The size of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnCurrentPageChanged(Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            Called when current page is changed.
            </summary>
            <param name="page">The page.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnMouseWheel(System.Windows.Input.MouseWheelEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.MouseWheel" />
            event occurs to provide handling for the event in a derived class without attaching
            a delegate.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Input.MouseWheelEventArgs" /> that
            contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.MouseLeftButtonDown" />
            event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.MouseLeftButtonUp" />
            event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.MouseMove" />
            event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.OnLostMouseCapture(System.Windows.Input.MouseEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.LostMouseCapture" />
            event occurs to provide handling for the event in a derived class without attaching
            a delegate.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Input.MouseEventArgs" /> that contains
            the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentPresenterBase.GetFocusedPresenter">
            <summary>
            Gets the focused presenter.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentViewerSettings">
            <summary>
            Represents fixed document viewer settings.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentViewerSettings.PrintSettings">
            <summary>
            Gets or sets the print settings.
            </summary>
            <value>The print settings.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedPagePresenter">
            <summary>
            Represents fixed page presenter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedPagePresenter.UpdateLayers(Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext)">
            <summary>
            Updates the layers.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedPagePresenterBase">
            <summary>
            Represents the base class for fixed page presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedPagePresenterBase.UILayers">
            <summary>
            Gets the UI layers.
            </summary>
            <value>The UI layers.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedPagePresenterBase.IsLayerSupported(Telerik.Windows.Documents.Fixed.UI.Layers.IUILayer)">
            <summary>
            Determines whether the layer is supported.
            </summary>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedPagePresenterBase.UpdateLayers(Telerik.Windows.Documents.Fixed.UI.Layers.UILayerUpdateContext)">
            <summary>
            Updates the layers.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.IFixedDocumentPresenter">
            <summary>
            Represents fixed document presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.Owner">
            <summary>
            Gets the owner.
            </summary>
            <value>The owner.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.PointerHandlersController">
            <summary>
            Gets the mouse handlers controller.
            </summary>
            <value>The mouse handlers controller.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.CurrentPage">
            <summary>
            Gets the current page.
            </summary>
            <value>The current page.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.ShowSelectionMarkers">
            <summary>
            Shows the selection markers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.HideSelectionMarkers">
            <summary>
            Hides the selection markers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.GetLocationFromViewPoint(System.Windows.Point,Telerik.Windows.Documents.Fixed.Model.RadFixedPage@,System.Windows.Point@)">
            <summary>
            Gets the location from view point.
            </summary>
            <param name="viewPoint">The view point.</param>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.GetViewPointFromLocation(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Point,System.Windows.Point@)">
            <summary>
            Gets the view point from location.
            </summary>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <param name="viewPoint">The view point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.PageUp">
            <summary>
            Pages up.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.PageDown">
            <summary>
            Pages down.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.GoToPage(System.Int32)">
            <summary>
            Goes to page.
            </summary>
            <param name="pageNo">The page no.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.GoToDestination(Telerik.Windows.Documents.Fixed.Model.Navigation.Destination)">
            <summary>
            Goes to destination.
            </summary>
            <param name="destination">The destination.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.InvalidateMeasure">
            <summary>
            Invalidates the measure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.InvalidateArrange">
            <summary>
            Invalidates the arrange.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.UpdatePresenterLayout">
            <summary>
            Updates the presenter layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.Initialize(Telerik.Windows.Documents.UI.IFixedDocumentViewer)">
            <summary>
            Initializes the presenter.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.Initialize(Telerik.Windows.Documents.UI.IFixedDocumentViewer,Telerik.Windows.Documents.UI.IFixedDocumentPresenter)">
            <summary>
            Initializes the specified owner.
            </summary>
            <param name="owner">The owner.</param>
            <param name="presenter">The presenter.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.Release">
            <summary>
            Releases the presenter.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.IFixedDocumentPresenter.CurrentPageChanged">
            <summary>
            Occurs when current page is changed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.IFixedDocumentViewer">
            <summary>
            Represents fixed document viewer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.Settings">
            <summary>
            Gets the settings.
            </summary>
            <value>The settings.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.RotationAngle">
            <summary>
            Gets the rotation angle.
            </summary>
            <value>The rotation angle.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.Mode">
            <summary>
            Gets or sets the mode.
            </summary>
            <value>The mode.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.HorizontalScrollBar">
            <summary>
            Gets the horizontal scroll bar.
            </summary>
            <value>The horizontal scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.VerticalScrollBar">
            <summary>
            Gets the vertical scroll bar.
            </summary>
            <value>The vertical scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.ScaleFactor">
            <summary>
            Gets or sets the scale factor.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.Cursors">
            <summary>
            Gets the cursors.
            </summary>
            <value>The cursors.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.CanHorizontallyScroll">
            <summary>
            Gets the can horizontally scroll.
            </summary>
            <value>The can horizontally scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.CanVerticallyScroll">
            <summary>
            Gets the can vertically scroll.
            </summary>
            <value>The can vertically scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.HorizontalScrollOffset">
            <summary>
            Gets the horizontal scroll offset.
            </summary>
            <value>The horizontal scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.VerticalScrollOffset">
            <summary>
            Gets the vertical scroll offset.
            </summary>
            <value>The vertical scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.IFixedDocumentViewer.FixedDocumentPresenter">
            <summary>
            Gets or sets the fixed document presenter.
            </summary>
            <value>The fixed document presenter.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentViewer.ScrollToHorizontalOffset(System.Double)">
            <summary>
            Scrolls to horizontal offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.IFixedDocumentViewer.ScrollToVerticalOffset(System.Double)">
            <summary>
            Scrolls to vertical offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.IFixedDocumentViewer.ScaleFactorChanged">
            <summary>
            Occurs when the scale factor is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.IFixedDocumentViewer.DocumentChanged">
            <summary>
            Occurs when the document is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.IFixedDocumentViewer.HyperlinkClicked">
            <summary>
            Occurs when a hyperlink is clicked.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer">
            <summary>
            Represents fixed document viewer container for RadBook.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.RotationAngle">
            <summary>
            Gets the rotation angle.
            </summary>
            <value>The rotation angle.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Cursors">
            <summary>
            Gets the cursors.
            </summary>
            <value>The cursors.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Settings">
            <summary>
            Gets or sets the settings.
            </summary>
            <value>The settings.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Mode">
            <summary>
            Gets or sets the mode that the FixedDocumentViewerBase class works.
            </summary>
            <value>The mode.</value>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.ModeProperty">
            <summary>
            Identifies the Telerik.Windows.Documents.UIRadBookFixedDocumentViewer.Mode property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.CanHorizontallyScroll">
            <summary>
            Gets the can horizontally scroll.
            </summary>
            <value>The can horizontally scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.CanVerticallyScroll">
            <summary>
            Gets the can vertically scroll.
            </summary>
            <value>The can vertically scroll.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Telerik#Windows#Documents#UI#IFixedDocumentPresenter#Owner">
            <summary>
            Gets the owner.
            </summary>
            <value>The owner.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Telerik#Windows#Documents#UI#IFixedDocumentViewer#HorizontalScrollBar">
            <summary>
            Gets the horizontal scroll bar.
            </summary>
            <value>The horizontal scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Telerik#Windows#Documents#UI#IFixedDocumentViewer#VerticalScrollBar">
            <summary>
            Gets the vertical scroll bar.
            </summary>
            <value>The vertical scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Telerik#Windows#Documents#UI#IFixedDocumentViewer#ScaleFactor">
            <summary>
            Gets or sets the scale factor.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Telerik#Windows#Documents#UI#IFixedDocumentPresenter#CurrentPage">
            <summary>
            Gets the current page.
            </summary>
            <value>The current page.</value>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.CurrentPageChanged">
            <summary>
            Occurs when current page is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.ScaleFactorChanged">
            <summary>
            Occurs when scale factor is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.GetLocationFromViewPoint(System.Windows.Point,Telerik.Windows.Documents.Fixed.Model.RadFixedPage@,System.Windows.Point@)">
            <summary>
            Gets the location from view point.
            </summary>
            <param name="viewPoint">The view point.</param>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.GetViewPointFromLocation(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Point,System.Windows.Point@)">
            <summary>
            Gets the view point from location.
            </summary>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <param name="viewPoint">The view point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.UpdatePresenterLayout">
            <summary>
            Updates the presenter layout.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.HorizontalScrollOffset">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.VerticalScrollOffset">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.FixedDocumentPresenter">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.ScrollToHorizontalOffset(System.Double)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.ScrollToVerticalOffset(System.Double)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.PointerHandlersController">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.Initialize(Telerik.Windows.Documents.UI.IFixedDocumentViewer)">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.DocumentChanged">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Windows.Documents.UI.RadBookFixedDocumentViewer.HyperlinkClicked">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.UI.RotationAngle">
            <summary>
            Represents rotation angle.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.RotationAngle.Degrees0">
            <summary>
            Represents 0 degrees.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.RotationAngle.Degrees90">
            <summary>
            Represents 90 degrees.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.RotationAngle.Degrees180">
            <summary>
            Represents 180 degrees.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.UI.RotationAngle.Degrees270">
            <summary>
            Represents 270 degrees.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.ScaleFactorChangedEventArgs">
            <summary>
            Represents scale factor changed event args.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.ScaleFactorChangedEventArgs.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.ScaleFactorChangedEventArgs" /> class.
            </summary>
            <param name="oldScaleFactor">The old scale factor.</param>
            <param name="newScaleFactor">The new scale factor.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.ScaleFactorChangedEventArgs.OldScaleFactor">
            <summary>
            Gets the old scale factor.
            </summary>
            <value>The old scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.ScaleFactorChangedEventArgs.NewScaleFactor">
            <summary>
            Gets the new scale factor.
            </summary>
            <value>The new scale factor.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter">
            <summary>
            Represents fixed document single page view presenter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized" />
            event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized" />
            is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains
            the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.CurrentPageNo">
            <summary>
            Gets the current page number.
            </summary>
            <value>The current page number.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.PagesLayoutManager">
            <summary>
            Gets the pages layout manager.
            </summary>
            <value>The pages layout manager.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.GetLocationFromViewPoint(System.Windows.Point,Telerik.Windows.Documents.Fixed.Model.RadFixedPage@,System.Windows.Point@)">
            <summary>
            Gets the location from view point.
            </summary>
            <param name="positionInView">The position in view.</param>
            <param name="page">The page.</param>
            <param name="location">The location.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.UpdateScrollOffsetFromPosition(Telerik.Windows.Documents.Fixed.Text.TextPosition)">
            <summary>
            Updates the scroll offset from position.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.DoOnScaleFactorChangedOverride(System.Double,System.Double)">
            <summary>
            Does the on scale factor changed.
            </summary>
            <param name="oldScaleFactor">The old scale factor.</param>
            <param name="newScaleFactor">The new scale factor.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.MeasureOverride(System.Windows.Size)">
            <summary>
            When overridden in a derived class, measures the size in layout required
            for child elements and determines a size for the <see cref="T:System.Windows.FrameworkElement" />-derived
            class.
            </summary>
            <param name="constraint"></param>
            <returns>
            The size that this element determines it needs during layout, based
            on its calculations of child element sizes.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.ArrangeOverride(System.Windows.Size)">
            <summary>
            Called to arrange and size the content of a <see cref="T:System.Windows.Controls.Control" />
            object.
            </summary>
            <param name="arrangeBounds">The computed size that is used to arrange the content.</param>
            <returns>The size of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.FixedDocumentSinglePageViewPresenter.ReleaseDocumentResourcesOverride">
            <summary>
            Releases the document resources.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.Thumbnail">
            <summary>
            Represents a thumbnail.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.Thumbnail.Size">
            <summary>
            Gets the thumbnail size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.Thumbnail.ImageSource">
            <summary>
            Gets the image source.
            </summary>
            <value>The image source.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.Thumbnail.Page">
            <summary>
            Gets the page.
            </summary>
            <value>The page.</value>
        </member>
        <member name="E:Telerik.Windows.Documents.UI.Thumbnail.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.Thumbnail.#ctor(Telerik.Windows.Documents.UI.ThumbnailsCollection,Telerik.Windows.Documents.Fixed.Model.RadFixedPage)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.Thumbnail" /> class.
            </summary>
            <param name="collection">The thumbnails collection.</param>
            <param name="page">The page.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.ThumbnailFactory">
            <summary>
            Responsible for creating thumbnails from RadFixedPage.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.ThumbnailFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.ThumbnailFactory" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.ThumbnailFactory.CreateThumbnail(Telerik.Windows.Documents.Fixed.Model.RadFixedPage,System.Windows.Size)">
            <summary>
            Creates thumbnail from given RadFixedPage.
            </summary>
            <param name="page">The page.</param>
            <param name="thumbnailSize">The size of the thumbnail.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.UI.ThumbnailsCollection">
            <summary>
            Represents thumbnails collection for given document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.ThumbnailsCollection.#ctor(Telerik.Windows.Documents.Fixed.Model.RadFixedDocument,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.UI.ThumbnailsCollection" /> class.
            </summary>
            <param name="document">The document.</param>
            <param name="thumbnailHeight">The height of the thumbnail.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.UI.ThumbnailsCollection.Document">
            <summary>
            Gets the RadFixedDocument.
            </summary>
            <value>The document.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.UI.ThumbnailsCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
            be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Automation.FixedDocumentViewer.TextRangeProvider">
            <summary>
            Represents TextRange provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.FixedDocumentViewer.TextRangeProvider.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase,Telerik.Windows.Documents.Fixed.Text.TextPosition,Telerik.Windows.Documents.Fixed.Text.TextPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.FixedDocumentViewer.TextRangeProvider" /> class.
            </summary>
            <param name="fixedDocumentViewer">The fixed document viewer.</param>
            <param name="startPosition">The start position.</param>
            <param name="endPosition">The end position.</param>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer">
            <summary>
            Represents FixedDocumentViewerBase automation peer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.#ctor(Telerik.Windows.Controls.FixedDocumentViewerBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer" /> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Gets the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>
            The <see cref="F:System.Windows.Automation.Peers.AutomationControlType.Custom" /> enumeration value.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetClassNameCore">
            <summary>
            Gets the name of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName" />.
            </summary>
            <returns>
            An <see cref="F:System.String.Empty" /> string.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetHelpTextCore">
            <summary>
            Gets the string that describes the functionality of the <see cref="T:System.Windows.ContentElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.ContentElementAutomationPeer" />. Called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText" />.
            </summary>
            <returns>
            The help text, usually from the <see cref="T:System.Windows.Controls.ToolTip" />, or <see cref="F:System.String.Empty" /> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the control pattern for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />.
            </summary>
            <param name="patternInterface">A value from the enumeration.</param>
            <returns>
            An object that implements the <see cref="T:System.Windows.Automation.Provider.ISynchronizedInputProvider" /> interface if <paramref name="patternInterface" /> is <see cref="F:System.Windows.Automation.Peers.PatternInterface.SynchronizedInput" />; otherwise, null.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetChildren" />.
            </summary>
            <returns>
            A list of child <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> elements.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FixedDocumentViewerBaseAutomationPeer.GetItemStatusCore">
            <summary>
            Gets a string that communicates the visual status of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetItemStatus" />.
            </summary>
            <returns>
            The string that contains the <see cref="P:System.Windows.Automation.AutomationProperties.ItemStatus" /> that is returned by <see cref="M:System.Windows.Automation.AutomationProperties.GetItemStatus(System.Windows.DependencyObject)" />.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadPdfViewerAutomationPeer">
            <summary>
            Represents RadPdfViewer automation peer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPdfViewerAutomationPeer.#ctor(Telerik.Windows.Controls.RadPdfViewer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadPdfViewerAutomationPeer" /> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPdfViewerAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
    </members>
</doc>
