﻿using Everylang.App.SettingsApp;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using Telerik.Windows.Controls;

namespace Everylang.App.Translator
{
    class GetLanguages
    {
        private readonly Dictionary<string, int> _languageColumns = new()
        {
            {"en", 0}, // English (default) - now at index 0
            {"cs", 1}, // Czech - shifted from 2 to 1
            {"de", 2}, // German - shifted from 3 to 2
            {"es", 3}, // Spanish - shifted from 4 to 3
            {"fr", 4}, // French - shifted from 5 to 4
            {"it", 5}, // Italian - shifted from 6 to 5
            {"pl", 6}, // Polish - shifted from 7 to 6
            {"pt", 7}, // Portuguese - shifted from 8 to 7
            {"ru", 8}, // Russian - shifted from 9 to 8
            {"uk", 9} // Ukrainian - shifted from 10 to 9
        };

        private readonly Dictionary<string, string> _allLanguagesTranslations = new();

        private void LoadAllLanguagesTranslations()
        {
            if (_allLanguagesTranslations.Count > 0) return;

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();
            var stream = assembly.GetManifestResourceStream(resourceNames.First(x => x.EndsWith("all_languages.txt")));

            if (stream != null)
            {
                var file = new StreamReader(stream);
                var uiLang = SettingsManager.Settings.AppUILang ?? "en";
                var columnIndex = _languageColumns.ContainsKey(uiLang) ? _languageColumns[uiLang] : _languageColumns["en"];

                file.ReadLine();

                while (file.ReadLine() is { } line)
                {
                    var parts = line.Split('|');
                    if (parts.Length >= columnIndex + 1)
                    {
                        var englishName = parts[0];
                        var translatedName = parts[columnIndex];
                        _allLanguagesTranslations[englishName] = translatedName;
                    }
                }
            }
        }

        private List<Language> LoadLanguages(string fileName)
        {
            var langList = new List<Language>();

            LoadAllLanguagesTranslations();

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();
            var stream = assembly.GetManifestResourceStream(resourceNames.First(x => x.EndsWith(fileName)));

            if (stream != null)
            {
                var file = new StreamReader(stream);
                while (file.ReadLine() is { } line)
                {
                    var parts = line.Split('|');
                    if (parts.Length >= 2)
                    {
                        var englishName = parts[0]; // English name
                        var languageCode = parts[1]; // Language code

                        if (_allLanguagesTranslations.ContainsKey(englishName))
                        {
                            var translatedName = _allLanguagesTranslations[englishName];
                            langList.Add(Language.AddLanguage(translatedName, languageCode));
                        }
                        else
                        {
                            langList.Add(Language.AddLanguage(englishName, languageCode));
                        }
                    }
                }
            }

            return langList;
        }

        internal List<Language> LoadLangs(string langService)
        {
            var languages = new List<Language> { Language.AddLanguage(LocalizationManager.GetString("TransLangAuto"), "auto") };

            languages.AddRange(LoadLanguages($"{langService}.txt").OrderBy(x => x.Name).ToList());
            return languages;
        }
    }
}
