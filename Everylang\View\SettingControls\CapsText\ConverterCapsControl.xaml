﻿<UserControl x:Class="Everylang.App.View.SettingControls.CapsText.ConverterCapsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                       HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                       CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <StackPanel Margin="20,10,0,10" Orientation="Horizontal">
            <TextBlock FontSize="15" FontWeight="Bold"  Text="{telerik:LocalizableResource Key=CapsTab}" />
            <TextBlock FontSize="15" FontWeight="Bold" Margin="7,0,0,0" Text="{telerik:LocalizableResource Key=OnlyPro}" />
        </StackPanel>
        <ScrollViewer Margin="20,35,0,10" VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled" >
            <StackPanel  HorizontalAlignment="Left" VerticalAlignment="Top" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">

                <TextBlock Margin="0,10,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsCapsOpenWindow}" />
                <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindowCaps}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindowCaps}"/>
                    <telerik:RadButton Focusable="False" Margin="5,0,0,0" Click="SwitchSelectedCapsOpenWindowClick" HorizontalAlignment="Left" Padding="5,0,5,0" VerticalAlignment="Center" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock  Margin="0,20,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCaps}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCaps}"/>
                    <Button  Focusable="False" Margin="5,0,0,0" Click="SwitchSelectedCapsShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
                <TextBlock Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsUp}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsUp}"/>
                    <Button  Focusable="False" Margin="5,0,0,0" Click="SwitchSelectedCapsUpShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
                <TextBlock  Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsDown}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsDown}"/>
                    <Button Focusable="False"  Margin="5,0,0,0" Click="SwitchSelectedCapsDownShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
                <TextBlock  Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsFirstLetterToUp}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToUp}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToUp}"/>
                    <Button Focusable="False"  Margin="5,0,0,0" Click="SwitchFirstLetterToUp" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
                <TextBlock  Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsFirstLetterToDown}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToDown}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToDown}" />
                    <Button Focusable="False"  Margin="5,0,0,0" Click="SwitchFirstLetterToDown" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
