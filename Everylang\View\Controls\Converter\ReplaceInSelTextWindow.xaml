﻿<telerik:RadWindow x:Class="Everylang.App.View.Controls.Converter.ReplaceInSelTextWindow"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                   xmlns:richTextBoxEx="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
                   xmlns:formatters="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
                   xmlns:converter="clr-namespace:Everylang.App.View.Controls.Converter"
                   HideMaximizeButton="True"
                   HideMinimizeButton="True"
                   IsTopmost="True"
                   Loaded="ReplaceInSelTextWindow_OnLoaded"
                   Focusable="false"  WindowStartupLocation="CenterScreen" Header="{telerik:LocalizableResource Key=ConverterReplaceSelText}"
                   MinHeight="300" MinWidth="600" Height="300" Width="600" MaxWidth="1200" MaxHeight="800" ResizeMode="CanResizeWithGrip"
                   x:ClassModifier="internal">
    <telerik:RadWindow.Style>
        <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="converter:ReplaceInSelTextWindow" />
    </telerik:RadWindow.Style>
    <telerik:RadWindow.Resources>
        <ResourceDictionary>
            <RoutedUICommand x:Key="CommandClose"/>
            <RoutedUICommand x:Key="CommandCancel"/>
            <RoutedUICommand x:Key="CommandReplace"/>
            <RoutedUICommand x:Key="CommandPaste"/>
        </ResourceDictionary>
    </telerik:RadWindow.Resources>
    <telerik:RadWindow.InputBindings>
        <KeyBinding Key="Escape"  Command="{StaticResource CommandClose}" />
        <KeyBinding Key="Z" Modifiers="Control"  Command="{StaticResource CommandCancel}" />
        <KeyBinding Key="Enter" Modifiers="Control"  Command="{StaticResource CommandReplace}" />
        <KeyBinding Key="Enter" Modifiers="Shift"  Command="{StaticResource CommandPaste}" />
    </telerik:RadWindow.InputBindings>
    <telerik:RadWindow.CommandBindings>
        <CommandBinding Command="{StaticResource CommandClose}" Executed="CloseClick" />
        <CommandBinding Command="{StaticResource CommandCancel}" Executed="CancelClick" />
        <CommandBinding Command="{StaticResource CommandReplace}" Executed="ReplaceClick" />
        <CommandBinding Command="{StaticResource CommandPaste}" Executed="PasteClick" />
    </telerik:RadWindow.CommandBindings>

    <Grid Margin="0,0,0,0"  Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="20"/>
        </Grid.RowDefinitions>
        <StackPanel Margin="5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Margin="10,0,0,0" Width="80" FontSize="14" Text="{telerik:LocalizableResource Key=Search}" VerticalAlignment="Center"/>
                <TextBox Margin="10,0,0,0" Width="250" x:Name="TextBoxFind" TextChanged="TextBoxFind_OnTextChanged" TabIndex="0"/>
                <Button  Margin="10,0,0,0" Focusable="False" Click="ReplaceClick"  Content="{telerik:LocalizableResource Key=bReplace}"  Width="110" ToolTip="Ctrl+Enter" TabIndex="2"/>
                <Button  Margin="10,0,0,0" Focusable="False" Click="CopyClick" Width="110" Content="{telerik:LocalizableResource Key=bCopy}"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                <TextBlock Margin="10,0,0,0" Width="80" FontSize="14" Text="{telerik:LocalizableResource Key=ReplaceTo}" VerticalAlignment="Center"/>
                <TextBox Margin="10,0,0,0" Width="250" x:Name="TextBoxReplace" TabIndex="1"/>
                <Button  Margin="10,0,0,0" Focusable="False"  Click="CancelClick" Content="{telerik:LocalizableResource Key=AutochangeHelperCancel}" Width="110"  ToolTip="Ctrl+Z"/>
                <Button  Margin="10,0,0,0" Focusable="False" Click="PasteClick" Content="{telerik:LocalizableResource Key=bReplaceText}" Width="110" ToolTip="Shift+Enter"/>
            </StackPanel>
        </StackPanel>
        <richTextBoxEx:RichTextBoxEx x:Name="RichTextBoxMu" Grid.Row="1"
                                      BorderThickness="0"
                                      CaretBrush="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                      Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                                      Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                      Margin="5" Block.LineHeight="1" ScrollViewer.VerticalScrollBarVisibility="Auto" TextChanged="RichTextBoxTextChanged">
            <richTextBoxEx:RichTextBoxEx.TextFormatter>
                <formatters:PlainTextFormatter />
            </richTextBoxEx:RichTextBoxEx.TextFormatter>
            </richTextBoxEx:RichTextBoxEx>
        <TextBlock Grid.Row="2" Margin="10,0,0,0" x:Name="TextBlockStatus"></TextBlock>
    </Grid>
</telerik:RadWindow>
