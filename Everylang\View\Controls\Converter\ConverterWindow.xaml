﻿<Popup x:Class="Everylang.App.View.Controls.Converter.ConverterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
        mc:Ignorable="d" 
        Height="300" Width="300" AllowsTransparency="True" Placement="Absolute" StaysOpen="True" Focusable="False"
        x:ClassModifier="internal"
        DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Popup.Resources>
        <ResourceDictionary>

            
            <telerik:RadRadialMenuItem Click="ButtonClickConvertExpressions" x:Key="UniConvertExpressions" ToolTipContent="{telerik:LocalizableResource Key=UniConvertExpressions}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextConvertExpressions}"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickTranslit" x:Key="UniTranslit" ToolTipContent="{telerik:LocalizableResource Key=UniTranslit}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextTranslit}"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickEnclose" x:Key="UniEnclose" ToolTipContent="{telerik:LocalizableResource Key=UniEnclose}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextEnclose}"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSearchAndReplace" x:Key="ConverterReplaceSelText" ToolTipContent="{telerik:LocalizableResource Key=ConverterReplaceSelText}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextSearch}"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickCamelCase" x:Key="UniCamelCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsCamelCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="camelCase"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSnakeCase" x:Key="UniSnakeCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsSnakeCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="snake_case"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickKebabCase" x:Key="UniKebabCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsKebabCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="kebab-case"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickPascalCase" x:Key="UniPascalCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsPascalCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="PascalCase"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <Style TargetType="telerik:RadialMenuButton" BasedOn="{StaticResource RadialMenuButtonStyle}">
                <Setter Property="ContentTemplate">
                    <Setter.Value>
                        <DataTemplate>
                            <wpf:MaterialIcon Kind="Adjust" />
                        </DataTemplate>
                    </Setter.Value>
                </Setter>
                <EventSetter Event="Click" Handler="EventSetter_OnHandler"></EventSetter>
            </Style>
        </ResourceDictionary>
    </Popup.Resources>
    <Grid>
        <telerik:RadRadialMenu x:Name="RadialMenuMu" VerticalAlignment="Center" HorizontalAlignment="Center" Navigated="RadialMenuMu_OnNavigated"
                               MinWidth="300" MinHeight="300"
                               InnerNavigationRadiusFactor="0.92"/>
    </Grid>
</Popup>































































































































































































































































































































































































































































































































