﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Everylang.App.Utilities.NetRequest;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;

namespace Everylang.App.Translator.Google
{
    internal class GoogleTranslator2
    {
        private const string Salt1 = "+-a^+6";
        private const string Salt2 = "+-3^+b+-f";
        private const string ApiEndpoint = "https://translate.googleapis.com/translate_a/single";



        private RequestSettings _requestSettings = null!;
        private string? _languageFromCurrent;
        private string? _languageToCurrent;
        private string? _latinText;
        private bool _isSecond;

        internal WebResultTranslator? Translate(RequestSettings requestSettings)
        {
            _requestSettings = requestSettings;
            return Translate(false);
        }

        private WebResultTranslator? Translate(bool second)
        {
            try
            {
                WebResultTranslator? webResult = new WebResultTranslator();
                _isSecond = second;
                if (!second)
                {
                    _languageFromCurrent = _requestSettings.LanguageFromCurrent?.Abbreviation;
                    _languageToCurrent = _requestSettings.LanguageToCurrent?.Abbreviation;
                }

                RestClient restClient = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy()
                });

                string sourceLanguage = string.IsNullOrEmpty(_languageFromCurrent) ? "auto" : _languageFromCurrent;
                string url = $"{ApiEndpoint}?client=gtx&sl={sourceLanguage}&tl={_languageToCurrent}&dt=t&dt=bd&hl={CultureInfo.CurrentCulture.TwoLetterISOLanguageName.ToLower()}&dj=1&source=input&tk={MakeToken(_requestSettings.SourceTextTrimmed.AsSpan())}";
                var req = new RestRequest(new Uri(url), Method.Post);
                req.AddParameter("application/x-www-form-urlencoded", "q=" + WebUtility.UrlEncode(_requestSettings.SourceTextTrimmed), ParameterType.RequestBody);
                var result = restClient.Execute(req);

                if (result.IsSuccessful && result.Content?.Length > 0 && result.Content.Contains("\"trans\""))
                {
                    webResult = ProcessingOfTheRequest(result.Content);
                    webResult.LatinText = _latinText;
                }
                else
                {
                    webResult.WithError = true;
                    webResult.ResultText = result.ErrorMessage;
                    webResult.ResultTextWithNonChar = result.Content;
                }

                webResult.FromLang = _languageFromCurrent;
                webResult.ToLang = _languageToCurrent;
                return webResult;
            }
            catch (Exception e)
            {
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }
        }

        private WebResultTranslator? ProcessingOfTheRequest(string? resultHttp)
        {
            var webResult = new WebResultTranslator();
            try
            {
                var resultText = new List<string>();
                if (resultHttp != null)
                {
                    _latinText = "";
                    JObject json = JObject.Parse(resultHttp);

                    _languageFromCurrent = json.SelectToken("src")?.Value<string>();
                    if (!_isSecond)
                    {
                        if (_languageFromCurrent == _languageToCurrent ||
                            (resultText.Count > 0 && _requestSettings.SourceTextTrimmed.ToLower() == resultText[0]?.ToLower()))
                        {
                            if (!string.Equals(_languageToCurrent, SettingsManager.Settings.TranslateLangTo,
                                    StringComparison.InvariantCultureIgnoreCase) &&
                                SettingsManager.Settings.TranslateLangTo != _languageFromCurrent)
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                            }
                            else if (!string.Equals(_languageToCurrent, SettingsManager.Settings.TranslateLangFrom,
                                StringComparison.InvariantCultureIgnoreCase))
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                            }

                            if (_languageFromCurrent == _languageToCurrent &&
                                _languageToCurrent != SettingsManager.Settings.TranslateLangTo)
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                            }
                            return Translate(true);
                        }
                    }

                    if (json.TryGetValue("sentences", out var sentencesToken))
                    {
                        if (sentencesToken.Any())
                        {
                            foreach (var jToken in sentencesToken)
                            {
                                resultText.Add(jToken.SelectToken("trans")?.Value<string>());
                            }
                        }

                        if (sentencesToken.Count() > 1 && sentencesToken[1] != null)
                        {
                            var translitToken = sentencesToken[1]?.SelectToken("translit");
                            if (translitToken == null)
                            {
                                translitToken = sentencesToken[1]?.SelectToken("src_translit");
                            }
                            if (translitToken != null)
                            {
                                _latinText = translitToken.Value<string>();
                            }
                        }
                    }

                    // Handle additional translations (dictionary entries)
                    var traslateResultStructList = new List<TraslateResultStruct>();
                    if (json.TryGetValue("dict", out var termsToken))
                    {
                        var terms = termsToken.ToList();
                        foreach (var term in terms)
                        {
                            var traslateResultStruct = new TraslateResultStruct();
                            var termArr = term.ToList();
                            var jToken = termArr[0].First;
                            if (jToken != null)
                            {
                                traslateResultStruct.Pos = jToken.Value<string>();
                                traslateResultStruct.Terms = new List<TermsStruct>();
                                foreach (var item in termArr[2])
                                {
                                    var itemArr = item.ToList();
                                    foreach (var itemTerm in itemArr)
                                    {
                                        var termsStruct = new TermsStruct();
                                        termsStruct.ReverseTranslation = new List<string?>();
                                        termsStruct.Word = itemTerm.First?.First?.Value<string>();
                                        // Additional meanings
                                        foreach (var entryColl in itemTerm["reverse_translation"]!)
                                        {
                                            termsStruct.ReverseTranslation.Add(entryColl.Value<string>());
                                        }

                                        traslateResultStruct.Terms.Add(termsStruct);
                                    }
                                }
                            }

                            traslateResultStructList.Add(traslateResultStruct);
                        }
                    }

                    string result = "";
                    foreach (var item in resultText)
                    {
                        result += item;
                    }

                    webResult.ResultTextWithNonChar = _requestSettings.StartNonCharList + result + _requestSettings.EndNonCharList;

                    foreach (var traslateResultStruct in traslateResultStructList)
                    {
                        result = "<bold>" + result + "\n" + "\n";
                        if (traslateResultStruct.Pos == "")
                        {
                            result += "\n<underline>" + _requestSettings.SourceTextTrimmed + ":\n";
                        }
                        else
                        {
                            result += "\n<underline>" + traslateResultStruct.Pos + ":\n";
                        }
                        foreach (var item in traslateResultStruct.Terms)
                        {
                            result += "      " + item.Word + " (";
                            for (int index = 0; index < item.ReverseTranslation.Count; index++)
                            {
                                var text = item.ReverseTranslation[index];
                                if (index + 1 == item.ReverseTranslation.Count)
                                {
                                    result += text;
                                }
                                else
                                {
                                    result += text + ", ";
                                }
                            }
                            result += ")\n";
                        }
                    }

                    webResult.ResultText = result;
                    return webResult;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return webResult;
        }


        private static string MakeToken(ReadOnlySpan<char> text)
        {
            long a = DateTimeOffset.UtcNow.ToUnixTimeSeconds() / 3600, b = a;

            foreach (char ch in text)
            {
                a = WorkToken(a + ch, Salt1);
            }

            a = WorkToken(a, Salt2);

            if (a < 0)
            {
                a = (a & int.MaxValue) + int.MaxValue + 1;
            }

            a %= 1000000;

            return $"{a}.{a ^ b}";
        }

        private static long WorkToken(long num, string seed)
        {
            for (int i = 0; i < seed.Length - 2; i += 3)
            {
                int d = seed[i + 2];

                if (d >= 'a')
                {
                    d -= 'W';
                }

                if (seed[i + 1] == '+')
                {
                    num = (num + (num >> d)) & uint.MaxValue;
                }
                else
                {
                    num ^= num << d;
                }
            }

            return num;
        }

        internal struct TraslateResultStruct
        {
            internal List<TermsStruct> Terms;
            internal string? Pos;
        }

        internal struct TermsStruct
        {
            internal List<string?> ReverseTranslation;
            internal string? Word;
        }

    }
}
