﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.Translator.NetRequest;
using Everylang.App.View.Controls.Common.RichTextBoxEx;
using Everylang.App.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Vanara.PInvoke;
using Color = System.Windows.Media.Color;
using FlowDirection = System.Windows.FlowDirection;

namespace Everylang.App.View.Controls.Translator
{

    internal partial class TranslationWindow
    {
        internal static readonly DependencyProperty IsStayOnTopProperty = DependencyProperty.Register(
                                                                            nameof(IsStayOnTop), typeof(bool),
                                                                            typeof(TranslationWindow)
                                                                            );
        internal bool IsStayOnTop
        {
            get => (bool)GetValue(IsStayOnTopProperty);
            set => SetValue(IsStayOnTopProperty, value);
        }

        public TranslationMiniViewModel ViewModel { get; set; }

        internal TranslationWindow()
        {
            ViewModel = new TranslationMiniViewModel();
            ViewModel.ShowTranlatedText += ShowTranlatedText;
            InitializeComponent();
            Opened += OnOpened;
            HookCallBackKeyDown.CallbackEventHandler += OnKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            resTextBox.CaretBrush = new SolidColorBrush(Color.FromArgb(0, 0, 0, 0));
            resTextBox.Text = "";
            DataContext = ViewModel;
            Width = MinWidth;
            Height = MinHeight;
        }

        internal void Translate(string? text)
        {
            if (text != null) ViewModel.Translate(text);
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;

                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {
            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;
        }

        private void ThumbDragDelta(object sender, DragDeltaEventArgs e)
        {
            Thumb? t = sender as Thumb;
            if (t != null && (t.Name == "ThumbRight" || t.Name == "ThumbRightBottom"))
            {
                Width = Math.Min(MaxWidth,
                    Math.Max(Width + e.HorizontalChange,
                        MinWidth));
            }

            if (t != null && (t.Name == "ThumbBottom" || t.Name == "ThumbRightBottom"))
            {
                Height = Math.Min(MaxHeight,
                    Math.Max(Height + e.VerticalChange,
                        MinHeight));
            }

            if (t?.Name == "ThumbLeft")
            {
                if ((e.HorizontalChange < 0 && Width < MaxWidth) || (e.HorizontalChange > 0 && Width > MinWidth))
                {
                    HorizontalOffset += e.HorizontalChange;
                    Width = Math.Min(MaxWidth,
                        Math.Max(Width + (e.HorizontalChange * -1),
                            MinWidth));
                }
            }
            if (t?.Name == "ThumbTop")
            {
                if ((e.VerticalChange < 0 && Height < MaxHeight) || (e.VerticalChange > 0 && Height > MinHeight))
                {
                    VerticalOffset += e.VerticalChange;
                    Height = Math.Min(MaxHeight,
                        Math.Max(Height + (e.VerticalChange * -1),
                            MinHeight));
                }
            }
        }

        private void WindowMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (Math.Abs(this.Width - this.MaxWidth) < 5)
            {
                this.Width = this.MinWidth;
                this.Height = this.MinHeight;
            }
            else
            {
                this.Width = this.MaxWidth;
                this.Height = this.MaxHeight;
            }
        }

        private void MouseOverHide(GlobalMouseEventArgs globalMouseEventArgs)
        {

            if (!IsMouseOver && !IsStayOnTop && Mouse.DirectlyOver == null)
            {
                Close();
            }
            if (IsMouseOver && ListViewLangFrom.IsOpen && !ListViewLangFrom.IsMouseOver)
            {
                ListViewLangFrom.IsOpen = false;
            }
            if (IsMouseOver && ListViewLangTo.IsOpen && !ListViewLangTo.IsMouseOver)
            {
                ListViewLangTo.IsOpen = false;
            }
        }

        private void OnKeyDown(GlobalKeyEventArgs e)
        {
            if (e.KeyCode == VirtualKeycodes.C && (e.Control == ModifierKeySide.Left || e.Control == ModifierKeySide.Right))
            {
                var sel = resTextBox.Selection.Text;
                if (!string.IsNullOrEmpty(sel))
                {
                    ClipboardOperations.SetText(sel);
                    e.Handled = true;
                }
                else
                {
                    ViewModel.CopyTransalatedCommand.Execute(null);
                    e.Handled = true;
                }
            }
            if (e.KeyCode == VirtualKeycodes.Enter && !IsStayOnTop)
            {
                e.Handled = true;
                ReplaceText(null, null);
            }
            if (e.KeyCode == VirtualKeycodes.Esc && !IsStayOnTop)
            {
                e.Handled = true;
                Close();
            }
        }

        private void Close()
        {
            HookCallBackKeyDown.CallbackEventHandler -= OnKeyDown;
            HookCallBackMouseDown.CallbackEventHandler -= MouseOverHide;
            //HookCallBackMouseWheel.CallbackEventHandler -= HookManagerMouseWheel;
            IsOpen = false;
        }

        private void ShowTranlatedText(WebResultTranslator webResult)
        {
            try
            {
                resTextBox.FlowDirection = FlowDirection.LeftToRight;
                if (ViewModel.LanguageToCurrent?.Abbreviation == "ar" || ViewModel.LanguageToCurrent?.Abbreviation == "he")
                {
                    resTextBox.FlowDirection = FlowDirection.RightToLeft;
                }
                resTextBox.Text = ViewModel.TranslatedText;
            }
            catch (Exception)
            {
                // ignore
            }
        }

        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private string? _lastText = "";

        private void RichTextBoxTextChanged(object sender, TextChangedEventArgs e)
        {
            if (resTextBox.Text != _lastText)
            {
                var formattedText = resTextBox.Document.GetFormattedText(this);
                if (formattedText != null && formattedText.WidthIncludingTrailingWhitespace + 20 < MaxWidth)
                {
                    this.Width = formattedText.WidthIncludingTrailingWhitespace + 20;
                    this.Height = formattedText.Height + 85;
                }
                else
                {
                    this.Width += 250;
                    UpdateLayout();
                    this.Height += (this.resTextBox.ExtentHeight - this.resTextBox.ViewportHeight) + 10;
                    UpdateLayout();
                    if (this.resTextBox.ExtentHeight > this.resTextBox.ViewportHeight)
                    {
                        this.Width = MaxWidth;
                        UpdateLayout();
                        this.Height += (this.resTextBox.ExtentHeight - this.resTextBox.ViewportHeight) + 10;
                    }
                }
                _lastText = resTextBox.Text;
            }
        }

        private void ReplaceText(object? sender, ExecutedRoutedEventArgs? e)
        {
            IsStayOnTop = false;
            Close();
            Utilities.SendText.SendStringByPaste(ViewModel.TranslatedTextWithNonChar, false);
        }

        private void ButtonClickLatin(object sender, RoutedEventArgs e)
        {
            var text = this.resTextBox.Text;
            if (text != null && (ViewModel.RequestSettings.TranslatedTextLatin == "" || text.Contains("[" + ViewModel.RequestSettings.TranslatedTextLatin + "]")))
            {
                return;
            }

            if (ViewModel.TranslatedText != null && (ViewModel.RequestSettings.IsOneWord || ViewModel.TranslatedText.Contains("<underline>")))
            {
                if (ViewModel.TranslatedText.IndexOf("\n", StringComparison.Ordinal) != -1)
                {
                    this.resTextBox.Text = this.resTextBox.Text?.Insert(ViewModel.TranslatedText.IndexOf("\n", StringComparison.Ordinal), "\n[" + ViewModel.RequestSettings.TranslatedTextLatin + "]");
                }
                else
                {
                    this.resTextBox.Text += "\n\n[" + ViewModel.RequestSettings.TranslatedTextLatin + "]";
                }
            }
            else
            {
                this.resTextBox.Text += "\n\n[" + ViewModel.RequestSettings.TranslatedTextLatin + "]";
            }

        }

        private void CommandCopyresTextBox_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(resTextBox.Selection.Text);
        }

        private void CloseThis(object sender, ExecutedRoutedEventArgs e)
        {
            Close();
        }

        private void LanguageSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel.IsTranslationComplete)
            {
                Translate(ViewModel.RequestSettings.LastSourceText);
            }
        }

        private void StayOnTopClick(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = !IsStayOnTop;
        }

        private void GoToMainWindowClick(object sender, RoutedEventArgs e)
        {
            Close();
            GlobalEventsApp.OnEventGoToMainWindowTranslate(ViewModel.RequestSettings.LastSourceText);
        }
    }
}
