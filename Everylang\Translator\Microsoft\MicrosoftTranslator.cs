﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Everylang.App.Utilities.NetRequest;
using Everylang.Common.LogManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;

namespace Everylang.App.Translator.Microsoft
{
    internal class MicrosoftTranslator
    {
        private const string API_ENDPOINT = "api.cognitive.microsofttranslator.com";
        private const string API_VERSION = "3.0";

        private readonly byte[] _privateKey = {
            0xa2, 0x29, 0x3a, 0x3d, 0xd0, 0xdd, 0x32, 0x73,
            0x97, 0x7a, 0x64, 0xdb, 0xc2, 0xf3, 0x27, 0xf5,
            0xd7, 0xbf, 0x87, 0xd9, 0x45, 0x9d, 0xf0, 0x5a,
            0x09, 0x66, 0xc6, 0x30, 0xc6, 0x6a, 0xaa, 0x84,
            0x9a, 0x41, 0xaa, 0x94, 0x3a, 0xa8, 0xd5, 0x1a,
            0x6e, 0x4d, 0xaa, 0xc9, 0xa3, 0x70, 0x12, 0x35,
            0xc7, 0xeb, 0x12, 0xf6, 0xe8, 0x23, 0x07, 0x9e,
            0x47, 0x10, 0x95, 0x91, 0x88, 0x55, 0xd8, 0x17
        };

        internal WebResultTranslator? Translate(RequestSettings requestSettings)
        {
            var sourceText = requestSettings.SourceTextTrimmed;
            var languageFromCurrent = requestSettings.LanguageFromCurrent!.Abbreviation;
            var languageToCurrent = requestSettings.LanguageToCurrent!.Abbreviation;
            var result = Translate(sourceText, languageFromCurrent!, languageToCurrent!);
            return result;
        }

        public WebResultTranslator Translate(string sourceText, string languageFromCurrent, string languageToCurrent)
        {
            if (languageFromCurrent == "auto")
            {
                languageFromCurrent = "";
            }
            if (languageFromCurrent == languageToCurrent)
            {
                languageToCurrent = SettingsManager.Settings.TranslateLangFrom!;
            }


            try
            {
                string url = $"{API_ENDPOINT}/translate?api-version={API_VERSION}&to={languageToCurrent}";
                if (languageFromCurrent != "")
                {
                    url += $"&from={languageFromCurrent}";
                }

                using var request = new HttpRequestMessage(HttpMethod.Post, new Uri($"https://{url}"));
                request.Headers.Add("X-MT-Signature", GetSignature(url));
                var json = JsonConvert.SerializeObject(new[] { new MicrosoftTranslatorRequest { Text = sourceText } });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                request.Content = content;

                var httpClient = new HttpClient(new HttpClientHandler() { Proxy = NetLib.GetProxy() });
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd(SettingsManager.UserAgent);

                using var response = httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead).GetAwaiter().GetResult();
                if (response.IsSuccessStatusCode)
                {
                    var result = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                    if (!string.IsNullOrEmpty(result))
                    {
                        var translationResult = ProcessingOfTheRequest(result);
                        if (!translationResult.WithError)
                        {
                            if (languageFromCurrent != "")
                            {
                                translationResult.FromLang = languageFromCurrent;
                                translationResult.ToLang = languageToCurrent;
                            }
                            else
                            {
                                if (translationResult.FromLang == languageToCurrent)
                                {
                                    languageToCurrent = SettingsManager.Settings.TranslateLangFrom!;
                                    if (translationResult.FromLang == languageToCurrent)
                                    {
                                        languageToCurrent = SettingsManager.Settings.TranslateLangTo!;
                                    }
                                    return Translate(sourceText, translationResult.FromLang, languageToCurrent);
                                }
                            }
                        }
                        return translationResult;

                    }

                }
                return new WebResultTranslator() { WithError = true, ErrorText = response.ReasonPhrase };
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }

        }

        private string GetSignature(string url)
        {
            string guid = Guid.NewGuid().ToString("N");
            string escapedUrl = Uri.EscapeDataString(url);
            string dateTime = DateTimeOffset.UtcNow.ToString("ddd, dd MMM yyyy HH:mm:ssG\\MT", CultureInfo.InvariantCulture);

            byte[] bytes = Encoding.UTF8.GetBytes($"MSTranslatorAndroidApp{escapedUrl}{dateTime}{guid}".ToLowerInvariant());

            using var hmac = new HMACSHA256(_privateKey);
            byte[] hash = hmac.ComputeHash(bytes);

            return $"MSTranslatorAndroidApp::{Convert.ToBase64String(hash)}::{dateTime}::{guid}";
        }

        private WebResultTranslator ProcessingOfTheRequest(string resultText)
        {
            try
            {
                var jsonArray = JArray.Parse(resultText);

                foreach (var item in jsonArray)
                {
                    var detectedLanguage = item["detectedLanguage"];
                    string language = detectedLanguage?["language"]?.ToString() ?? string.Empty;
                    double score = detectedLanguage?["score"]?.ToObject<double>() ?? 0;

                    var translations = item["translations"];
                    if (translations != null)
                    {
                        foreach (var translation in translations)
                        {
                            return new WebResultTranslator()
                            {
                                ResultText = translation["text"]?.ToString() ?? string.Empty,
                                FromLang = language,
                                ToLang = translation["to"]?.ToString() ?? string.Empty,
                                ResultTextWithNonChar = translation["text"]?.ToString() ?? string.Empty
                            };
                        }
                    }
                }
            }
            catch (Exception e)
            {
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }

            return new WebResultTranslator() { WithError = true, ErrorText = "Processing with error" }; ;
        }
    }




    internal class DetectedLanguage
    {
        public string Language { get; set; } = string.Empty;
        public double Score { get; set; }
    }

    internal class Translation
    {
        public string Text { get; set; } = string.Empty;
        public string To { get; set; } = string.Empty;
    }

    internal class TranslationResult
    {
        public DetectedLanguage DetectedLanguage { get; set; } = null!;
        public List<Translation> Translations { get; set; } = null!;
    }

    internal sealed class MicrosoftTranslatorRequest
    {
        public string Text { get; set; } = string.Empty;
    }
}
