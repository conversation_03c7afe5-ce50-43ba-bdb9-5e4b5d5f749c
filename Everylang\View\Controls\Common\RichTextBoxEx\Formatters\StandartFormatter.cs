﻿using System.Linq;
using System.Windows.Documents;

namespace Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters
{
    class StandartFormatter : ITextFormatter
    {
        string? ITextFormatter.GetText(FlowDocument document)
        {
            return new TextRange(document.ContentStart, document.ContentEnd).Text;
        }

        void ITextFormatter.SetText(FlowDocument document, string? text)
        {
            if (text == null)
            {
                return;
            }
            new TextRange(document.ContentStart, document.ContentEnd).Text = text.Replace("\r\n\r\n", "\n").Replace("\n\n", "\n").Replace("\r\n \r\n", "\n").Replace("\n \n", "\n");

            foreach (var block in document.Blocks.ToList())
            {
                var line = (Paragraph)block;
                string textBlock = new TextRange(line.ContentStart, line.ContentEnd).Text;
                line.Inlines.Clear();
                var stringLines = textBlock.Split('\n');
                foreach (var stringLine in stringLines)
                {
                    if (stringLine.StartsWith("<bold>"))
                    {
                        line.Inlines.Add(new Bold(new Run(stringLine.Replace("<bold>", ""))));
                    }
                    else if (stringLine.StartsWith("<italic>"))
                    {
                        line.Inlines.Add(new Italic(new Run(stringLine.Replace("<italic>", ""))));
                    }
                    else if (stringLine.StartsWith("<underline>"))
                    {
                        line.Inlines.Add(new Underline(new Run(stringLine.Replace("<underline>", ""))));
                    }
                    else
                    {
                        line.Inlines.Add(new Run(stringLine));
                    }
                }
            }

        }
    }
}
