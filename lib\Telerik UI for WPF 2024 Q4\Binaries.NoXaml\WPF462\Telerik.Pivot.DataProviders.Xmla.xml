<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Pivot.DataProviders.Xmla</name>
    </assembly>
    <members>
        <member name="T:Telerik.Pivot.Xmla.XmlaAggregateDescription">
            <summary>
            Used to specify aggregation parameters for <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaAggregateDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaFilterDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaFilterDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaLevelFilterDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaLevelFilterDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaLevelFilterDescription" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaLevelFilterDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaLevelGroupDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaLevelGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaConnectionSettings">
            <summary>
            Connection setting class used by <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaConnectionSettings" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.DefaultEncoding">
            <summary>
            Gets the default encoding used for XMLA service calls.
            </summary>
            <value>
            The default encoding.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.QueryProperties">
            <summary>
            Properties that are used for Discover and Execute methods.
            </summary>
            <value>
            The query properties.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.Cube">
            <summary>
            Gets or sets the name of the cube that will be used.
            </summary>
            <value>Cube name.</value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.Database">
            <summary>
            Gets or sets the database to connect to.
            </summary>
            <value>Database name.</value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.ServerAddress">
            <summary>
            Gets or sets the server address.
            </summary>
            <value>The server address.</value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.Credentials">
            <summary>
            Gets or sets the client credentials used for XMLA service calls.  
            </summary>
            <value>The credentials.</value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaConnectionSettings.Encoding">
            <summary>
            Gets or sets the encoding that is used for XMLA service calls.
            </summary>
            <value>
            The encoding.
            </value>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.op_Equality(Telerik.Pivot.Xmla.XmlaConnectionSettings,Telerik.Pivot.Xmla.XmlaConnectionSettings)">
            <summary>
            Compares two instances for equality.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>True, if instances are equal.</returns>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaConnectionSettings.op_Inequality(Telerik.Pivot.Xmla.XmlaConnectionSettings,Telerik.Pivot.Xmla.XmlaConnectionSettings)">
            <summary>
            Compares two instances for non-equality.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>True, if instances are not equal.</returns>
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaFieldDescriptionProvider">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> implementation for Xmla sources.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaFieldDescriptionProvider.#ctor(Telerik.Pivot.Xmla.XmlaConnectionSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaFieldDescriptionProvider" /> class.
            </summary>
            <param name="connectionSettings">The connection settings.</param>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaFieldDescriptionProvider.GetLoader">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaGroupDescription">
            <summary>
            Used to specify grouping parameters for <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaDataProvider">
            <summary>
            Provides Cube data access and operations using Xmla.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.FilterDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Core.FilterDescription"/> that specified how the pivot items should be filtered.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.RowGroupDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Xmla.XmlaGroupDescription"/> that specified how the pivot should be grouped by rows.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.ColumnGroupDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Xmla.XmlaGroupDescription"/> that specified how the pivot should be grouped by columns.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.AggregateDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Xmla.XmlaAggregateDescription"/> that specified how the pivot should be aggregated for the groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.ConnectionSettings">
            <summary>
            Gets or sets the connection settings that are used for establishing connection to the data server.
            </summary>
            <value>The connection settings.</value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.Results">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaDataProvider.State">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.RefreshOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.BlockUntilRefreshCompletes">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.CreateFieldDescriptionsProvider">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.GetAggregateDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.GetGroupDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.SetAggregateFunctionToAggregateDescription(Telerik.Pivot.Core.IAggregateDescription,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaDataProvider.GetFilterDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaNetworkCredential">
            <summary>
            Provides credentials used for XMLA service calls.  
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaNetworkCredential.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaNetworkCredential" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaNetworkCredential.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaNetworkCredential" /> class.
            </summary>
            <param name="username">The username.</param>
            <param name="password">The password.</param>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaNetworkCredential.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Xmla.XmlaNetworkCredential" /> class.
            </summary>
            <param name="username">The username.</param>
            <param name="password">The password.</param>
            <param name="domain">The domain.</param>
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaPivotSerializationHelper">
            <summary>
            Provides all knownTypes necessary for serializing <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/>.
            Use this class to extract these knownTypes and concatenate them with your knownTypes, so you can pass them to <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> for example.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaPivotSerializationHelper.KnownTypes">
            <summary>
            Gets known types in <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/> to use with serializer such as <see cref="T:System.Runtime.Serialization.DataContractSerializer"/>.
            </summary>
            <returns>An enumeration with the known serializable classes for the <see cref="T:Telerik.Pivot.Xmla.XmlaDataProvider"/> <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.</returns>
        </member>
        <member name="T:Telerik.Pivot.Xmla.XmlaQueryProperty">
            <summary>
            Represents a property that is used when using Discover and Execute methods.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaQueryProperty.Name">
            <summary>
            Gets or sets the name of the property.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Xmla.XmlaQueryProperty.Value">
            <summary>
            Gets or sets the value of the property.
            </summary>
            <value>
            The value.
            </value>
        </member>
        <member name="M:Telerik.Pivot.Xmla.XmlaQueryProperty.ToString">
            <inheritdoc />
        </member>
    </members>
</doc>
