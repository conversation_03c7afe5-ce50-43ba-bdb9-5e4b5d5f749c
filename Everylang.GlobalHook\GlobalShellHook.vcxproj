﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A16BB20E-4E10-4C13-9BF4-F945FCE4E87C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>GlobalShellHook</RootNamespace>
    <ProjectName>Everylang.GlobalHook</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <TargetName>$(ProjectName)$(PlatformArchitecture)</TargetName>
    <OutDir>$(SolutionDir)bin\x86\$(Configuration)\</OutDir>
    <IntDir>bin\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <TargetName>$(ProjectName)$(PlatformArchitecture)</TargetName>
    <OutDir>$(SolutionDir)bin\x64\$(Configuration)\</OutDir>
    <IntDir>bin\$(Platform)\$(Configuration)\</IntDir>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <TargetName>$(ProjectName)$(PlatformArchitecture)</TargetName>
    <OutDir>$(SolutionDir)bin\x86\$(Configuration)\</OutDir>
    <IntDir>bin\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <TargetName>$(ProjectName)$(PlatformArchitecture)</TargetName>
    <OutDir>$(SolutionDir)bin\x64\$(Configuration)\</OutDir>
    <IntDir>bin\$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Create</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;GLOBALSHELLHOOK_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <CompileAsManaged>false</CompileAsManaged>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <FixedBaseAddress>false</FixedBaseAddress>
      <TurnOffAssemblyGeneration>true</TurnOffAssemblyGeneration>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Create</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;GLOBALSHELLHOOK_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <EnablePREfast>false</EnablePREfast>
      <CompileAsManaged>false</CompileAsManaged>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <FixedBaseAddress>false</FixedBaseAddress>
      <TurnOffAssemblyGeneration>true</TurnOffAssemblyGeneration>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Create</PrecompiledHeader>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;GLOBALSHELLHOOK_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <CompileAsManaged>false</CompileAsManaged>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <CLRUnmanagedCodeCheck>false</CLRUnmanagedCodeCheck>
      <CLRSupportLastError>Disabled</CLRSupportLastError>
      <FixedBaseAddress>false</FixedBaseAddress>
      <TurnOffAssemblyGeneration>true</TurnOffAssemblyGeneration>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Create</PrecompiledHeader>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;GLOBALSHELLHOOK_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <CompileAsManaged>false</CompileAsManaged>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <CLRUnmanagedCodeCheck>false</CLRUnmanagedCodeCheck>
      <CLRSupportLastError>Disabled</CLRSupportLastError>
      <FixedBaseAddress>false</FixedBaseAddress>
      <TurnOffAssemblyGeneration>true</TurnOffAssemblyGeneration>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="GlobalShellHook.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.c">
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</CompileAsManaged>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</CompileAsManaged>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="GlobalShellHook.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="GlobalShellHook.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>