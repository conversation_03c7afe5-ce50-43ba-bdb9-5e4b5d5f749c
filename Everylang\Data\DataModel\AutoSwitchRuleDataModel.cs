﻿using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using LiteDB;
using System.Collections.Generic;
using Telerik.Windows.Controls;

namespace Everylang.App.Data.DataModel
{
    public class AutoSwitchRuleDataModel
    {
        internal ObjectId? Id { get; set; }
        public string? Text { get; set; }

        public string TextAllLayouts
        {
            get
            {
                if (string.IsNullOrEmpty(Text)) return "";
                string result = "";
                var resList = new List<string>();
                var texts = KeyboardLayoutMethods.GetTextForAllLayouts(Text);
                foreach (var keyValuePair in texts)
                {
                    resList.Add(keyValuePair.Key + " (" + keyValuePair.Value + ") ");
                }
                foreach (var res in resList)
                {
                    if (res.Contains(Text))
                    {
                        result = res;
                        resList.Remove(res);
                        break;
                    }
                }
                foreach (var res in resList)
                {
                    result += res;
                }
                return result;
            }
        }

        internal string TextForSearch
        {
            get
            {
                if (string.IsNullOrEmpty(Text)) return "";
                string result = "";
                var texts = KeyboardLayoutMethods.GetTextForAllLayouts(Text);
                foreach (var keyValuePair in texts)
                {
                    result += keyValuePair.Value + " ";
                }
                return result;
            }
        }

        internal bool IsSwitch { get; set; }
        internal bool IsNotSwitch { get; set; }
        internal bool IsCaseSensitive { get; set; }
        internal int ManualSwitchCount { get; set; }

        public string RuleActionConverter
        {
            get
            {
                if (ManualSwitchCount == SettingsManager.Settings.AutoSwitcherCountCheckRule)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionConvert");
                }
                if (ManualSwitchCount == -1)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionNotConvert");
                }
                if (ManualSwitchCount > -1 && ManualSwitchCount < SettingsManager.Settings.AutoSwitcherCountCheckRule)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionIntermediate");
                }
                return "";
            }
            set
            {
                if (value == LocalizationManager.GetString("AutoSwitcherSettingsRuleActionConvert"))
                {
                    ManualSwitchCount = SettingsManager.Settings.AutoSwitcherCountCheckRule;
                }
                if (value == LocalizationManager.GetString("AutoSwitcherSettingsRuleActionNotConvert"))
                {
                    ManualSwitchCount = -1;
                }
                if (value == LocalizationManager.GetString("AutoSwitcherSettingsRuleActionIntermediate"))
                {
                    ManualSwitchCount = 0;
                }
            }
        }

        public List<string> RuleActionConverterList { get; set; } = new() { LocalizationManager.GetString("AutoSwitcherSettingsRuleActionConvert"), LocalizationManager.GetString("AutoSwitcherSettingsRuleActionNotConvert"), LocalizationManager.GetString("AutoSwitcherSettingsRuleActionIntermediate") };

    }
}
