﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.Translation.TranslationControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:translation="clr-namespace:Everylang.App.View.SettingControls.Translation"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.Row="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <StackPanel Margin="20,10,0,0">
            <TextBlock
                FontSize="15"
                FontWeight="Bold"
                Text="{telerik:LocalizableResource Key=TransSettingsHeader}" />

            <TextBlock
                FontSize="14"
                Margin="0,20,0,0"
                Text="{telerik:LocalizableResource Key=TransSettingsNativeLanguage}" />
            <telerik:RadComboBox
                BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                Focusable="False"
                HorizontalAlignment="Left"
                ItemsSource="{Binding Path=TranslationSettingsViewModel.LanguagesForNativeLanguage}"
                Margin="0,0,0,0"
                SelectedItem="{Binding Path=TranslationSettingsViewModel.LanguageNative}"
                Width="230" />


            <TextBlock
                FontSize="14"
                Margin="0,0,0,0"
                Text="{telerik:LocalizableResource Key=TransSettingsLanguageFromTranslate}" />
            <telerik:RadComboBox
                BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                Focusable="False"
                HorizontalAlignment="Left"
                ItemsSource="{Binding Path=TranslationSettingsViewModel.LanguagesForTheTranslation}"
                Margin="0,0,0,0"
                SelectedItem="{Binding Path=TranslationSettingsViewModel.LanguageForTheTranslation}"
                Width="230" />

            <TextBlock
                FontSize="14"
                Margin="0,0,0,0"
                Text="{telerik:LocalizableResource Key=TransSettingsProviderOfTranslation}" />
            <telerik:RadComboBox
                BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                Focusable="False"
                HorizontalAlignment="Left"
                ItemsSource="{Binding Path=TranslationSettingsViewModel.ProvidersForTranslation}"
                Margin="0,0,0,0"
                SelectedItem="{Binding Path=TranslationSettingsViewModel.ProviderForTranslation}"
                Width="230" />

            <TextBlock
                FontSize="14"
                Margin="0,0,0,0"
                Text="{telerik:LocalizableResource Key=TransSettingsFavoriteLanguages}" />
            <telerik:RadComboBox
                AllowMultipleSelection="True"
                BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                EmptyText="{telerik:LocalizableResource Key=TransSettingsChooseYourFavoriteLanguages}"
                Focusable="False"
                HorizontalAlignment="Stretch"
                ItemsSource="{Binding Path=TranslationSettingsViewModel.AllLanguages}"
                translation:ComboBoxSelectionUtilities.SelectedItems="{Binding Path=TranslationSettingsViewModel.FavoriteLanguages}"
                Margin="0,0,20,0">
            </telerik:RadComboBox>

            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=TranslationSettingsViewModel.TranslateOnlyFavoriteLanguages}"
                IsEnabled="{Binding Path=TranslationSettingsViewModel.TranslateOnlyFavoriteLanguagesEnabled}"
                Margin="0,0,0,0">
                <TextBlock
                    Focusable="False"
                    FontSize="14"
                    Text="{telerik:LocalizableResource Key=TranslateOnlyFavoriteLanguages}" />
            </CheckBox>

            <StackPanel Margin="0,0,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=TransSettingsKeyboardShortcuts}" />
                <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                    <TextBox
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=TranslationSettingsViewModel.Shortcut}"
                        ToolTip="{Binding Path=TranslationSettingsViewModel.Shortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="NewShortCutClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
            </StackPanel>

            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=TranslationSettingsViewModel.TranslationIsAlways}"
                Margin="0,0,0,0">
                <TextBlock
                    Focusable="False"
                    FontSize="14"
                    Text="{telerik:LocalizableResource Key=TransSettingsTranslationIsAlways}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=HistoryViewModel.IsEnabled}"
                Margin="0,0,0,0">
                <TextBlock
                    Focusable="False"
                    FontSize="14"
                    Text="{telerik:LocalizableResource Key=TransSettingsHistoryIsOn}" />
            </CheckBox>
            <telerik:RadButton
                Command="{Binding Path=HistoryViewModel.ClearAllCommand}"
                Content="{telerik:LocalizableResource Key=TransSettingsClearAllHistory}"
                Focusable="False"
                HorizontalAlignment="Left"
                Margin="0,0,0,0" />
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <telerik:RadButton
                    Command="{Binding Path=TranslationSettingsViewModel.SetFontCommand}"
                    Content="{telerik:LocalizableResource Key=SetFont}"
                    Focusable="False"
                    HorizontalAlignment="Left"
                    Width="150" />
                <TextBlock
                    Focusable="False"
                    FontSize="14"
                    Margin="10,0,0,0"
                    Text="{Binding TranslationSettingsViewModel.FontText}"
                    VerticalAlignment="Center" />
            </StackPanel>

        </StackPanel>
    </Grid>
</UserControl>
