﻿<UserControl x:Class="Everylang.App.View.SettingControls.Spellchecking.SpellcheckingControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton  IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick" CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help"/>
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Grid.Row="0" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <StackPanel Margin="20,10,0,0" >
            <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=SpellcheckingSettingsHeader}" />            
            <StackPanel Margin="0,15,0,0">
                <TextBlock  FontSize="14"  Text="{telerik:LocalizableResource Key=SpellcheckingKeyboardShortcuts}" />
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0" >
                    <TextBox Focusable="False" Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=SpellcheckingSettingsViewModel.Shortcut}" ToolTip="{Binding Path=SpellcheckingSettingsViewModel.Shortcut}"/>
                    <telerik:RadButton Focusable="False"  Margin="5,0,0,0" Click="NewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
            </StackPanel>
            <CheckBox Focusable="False" Margin="0,0,0,0" FontSize="14" IsChecked="{Binding Path=SpellcheckingSettingsViewModel.CheckSpellingCloseByTimer}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SpellcheckingSettingsCloseByTimer}" />
            </CheckBox>

            <StackPanel Margin="0,20,0,0" Visibility="{Binding Path=SpellcheckingSettingsViewModel.IsPlatformSupportedSpellCheckWhileTyping, Converter={StaticResource BoolToVis}}">
                <CheckBox Focusable="False" FontSize="14" IsChecked="{Binding Path=SpellcheckingSettingsViewModel.CheckSpellingWhileTyping}">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SpellcheckingSettingsWhileTyping}" />
                </CheckBox>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                    <CheckBox  Focusable="False" FontSize="14" IsChecked="{Binding Path=SpellcheckingSettingsViewModel.CheckSpellingWhileTypingSoundOn}">
                        <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SpellcheckingSettingsWhileTypingSoundOn}" />
                    </CheckBox>
                    <telerik:RadButton Focusable="False"  Margin="5,0,0,0" Click="SoundClick" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5,5,5,5" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>

                <CheckBox Focusable="False" Margin="0,0,0,0" FontSize="14" IsChecked="{Binding Path=SpellcheckingSettingsViewModel.CheckSpellingUseNumber}">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SpellcheckingSettingsWhileTypingUseNumber}" />
                </CheckBox>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
