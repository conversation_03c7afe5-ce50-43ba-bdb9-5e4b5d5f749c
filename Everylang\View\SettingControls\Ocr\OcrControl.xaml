﻿<UserControl x:Class="Everylang.App.View.SettingControls.Ocr.OcrControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}" IsEnabled="{Binding Path=OcrViewModel.jgebhdhs}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <telerik:RadTransitionControl  Grid.Row="0" Grid.RowSpan="3" Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>

        <StackPanel Grid.Row="0"  Margin="20,10,0,0" Orientation="Horizontal">
            <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OcrHeader}" />
            <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
        </StackPanel>
        <telerik:RadButton Grid.Row="0" IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>

        <StackPanel Margin="20,10,0,0" Grid.Row="1">
            <StackPanel >
                <TextBlock  FontSize="14"  Text="{telerik:LocalizableResource Key=OcrKeyboardShortcuts}" />
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0" >
                    <TextBox IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=OcrViewModel.Shortcut}" ToolTip="{Binding Path=OcrViewModel.Shortcut}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="NewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
            </StackPanel>

            <TextBlock HorizontalAlignment="Left" VerticalAlignment="Top" FontSize="14" Margin="0,5,0,0"  Text="{telerik:LocalizableResource Key=OcrDescDefault}"></TextBlock>
        </StackPanel>

        <telerik:RadListBox Margin="20,10,0,10" Grid.Row="2" Width="400" HorizontalAlignment="Left" VerticalAlignment="Stretch" ItemsSource="{Binding OcrViewModel.Languages}" ScrollViewer.HorizontalScrollBarVisibility="Disabled" ScrollViewer.VerticalScrollBarVisibility="Visible">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <CheckBox MinHeight="0" Focusable="False" MinWidth="0" Padding="3,0,0,0" Margin="0" IsChecked="{Binding IsChecked}" Checked="ToggleButton_OnChecked" Unchecked="ToggleButton_OnChecked">
                        <TextBlock
                            FontSize="15"
                            Margin="10,0,0,0"
                            FontWeight="Bold"
                            Text="{Binding Path=Item.Name}" />
                    </CheckBox>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                    <Setter Property="Padding" Value="3" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Height" Value="35" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
    </Grid>
</UserControl>
