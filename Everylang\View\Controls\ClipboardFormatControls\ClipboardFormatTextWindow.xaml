﻿<telerik:RadWindow
    x:Class="Everylang.App.View.Controls.ClipboardFormatControls.ClipboardFormatTextWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:navigation="clr-namespace:Telerik.Windows.Controls.Navigation;assembly=Telerik.Windows.Controls.Navigation"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:richTextBoxEx="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
    xmlns:clipboardFormatControls="clr-namespace:Everylang.App.View.Controls.ClipboardFormatControls"
    xmlns:formatters="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
    MinWidth="300"
    MinHeight="200"
    MaxWidth="800"
    MaxHeight="700"        
    CornerRadius="3"
    Deactivated="me_Deactivated"
    Header="Text"
    SizeToContent="True"    
    navigation:RadWindowInteropHelper.AllowTransparency="False"
    ResizeMode="CanResize"
    WindowStartupLocation="Manual" HideMaximizeButton="True" HideMinimizeButton="True" x:ClassModifier="internal">
    <telerik:RadWindow.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="clipboardFormatControls:ClipboardFormatTextWindow" />
        </ResourceDictionary>
    </telerik:RadWindow.Resources>
    <Grid>
        <richTextBoxEx:RichTextBoxEx
            x:Name="richTextBox"
            Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"
            BorderBrush="{x:Null}"
            Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
            IsReadOnly="True"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Text=""
            TextChanged="RichTextBox_TextChanged"
            VerticalScrollBarVisibility="Auto"
            Loaded="richTextBox_Loaded">
                <richTextBoxEx:RichTextBoxEx.Resources>
                    <Style TargetType="{x:Type Paragraph}">
                        <Setter Property="Margin" Value="0" />
                    </Style>
                </richTextBoxEx:RichTextBoxEx.Resources>
                <richTextBoxEx:RichTextBoxEx.TextFormatter>
                    <formatters:PlainTextFormatter />
                </richTextBoxEx:RichTextBoxEx.TextFormatter>
                <richTextBoxEx:RichTextBoxEx.CommandBindings>
                    <CommandBinding Command="{x:Static ApplicationCommands.Copy}" Executed="CommandCopyresTextBox_Executed" />
                </richTextBoxEx:RichTextBoxEx.CommandBindings>
            </richTextBoxEx:RichTextBoxEx>     
        

    </Grid>
</telerik:RadWindow>