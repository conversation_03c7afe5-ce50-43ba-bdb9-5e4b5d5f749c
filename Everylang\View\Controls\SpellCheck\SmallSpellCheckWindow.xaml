﻿<Popup x:Class="Everylang.App.View.Controls.SpellCheck.SmallSpellCheckWindow"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                   xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
                   x:Name="me" Focusable="True" StaysOpen="True" Placement="Mouse"
                   MinHeight="120" MinWidth="200" Height="120" Width="200" MaxHeight="250" MaxWidth="350"
                   AllowsTransparency="True" x:ClassModifier="internal"
                   DataContext="{Binding ElementName=me}">
    <Popup.Resources>
        <ResourceDictionary>
            <Style x:Key="ProgressRingStyle" TargetType="telerik:RadBusyIndicator" BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Height" Value="50"/>
                <Setter Property="Width" Value="50"/>
                <Setter Property="Visibility" Value="Visible"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleProgressRing}" Value="False">
                        <Setter Property="Visibility" Value="Hidden"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="LabelOkStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="DemiBold" />
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Visibility" Value="Hidden"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsOk}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>

            </Style>

            <Style x:Key="LabelIsNoOptionsStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Visibility" Value="Hidden"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsNoOptions}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>

            </Style>

            <Style x:Key="LabelErrorStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Visibility" Value="Hidden"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Error}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>

            </Style>
            <Style x:Key="GridVisibility" TargetType="telerik:RadListBox" BasedOn="{StaticResource {x:Type telerik:RadListBox}}">
                <Setter Property="Visibility" Value="Hidden"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleGrid}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ImageButton" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Focusable" Value="False"/>
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Padding" Value="3" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="MinHeight" Value="0" />
            </Style>

            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                                    Height="14"
                                                    Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonPaste" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                                    Height="14"
                                                    Kind="ArrowDown" />
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>

    </Popup.Resources>

    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="25" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Thumb x:Name="ThumbMy" Opacity="0" Grid.ZIndex="1"/>
        <telerik:RadButton Grid.Row="0"  Grid.ZIndex="1" Style="{StaticResource ImageButtonClose}" HorizontalAlignment="Right" Margin="0,0,5,0" Click="ButtonClickClose" ToolTip="{telerik:LocalizableResource Key=TransCloseHeaderButton}" IsCancel="True"/>
        <telerik:Label Content="{telerik:LocalizableResource Key=SpellCheckHeader}" Grid.Row="0" FontSize="12" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0,0,0,0"/>
        <telerik:RadBusyIndicator Style="{StaticResource ProgressRingStyle}" Grid.Row="1"  />
        <telerik:Label Name="LabelNoErrors" Content="{telerik:LocalizableResource Key=LabelNoErrors}" Grid.Row="1" Margin="0,20,0,0" Style="{StaticResource LabelOkStyle}"/>
        <telerik:Label Content="{telerik:LocalizableResource Key=LabelError}" Grid.Row="1" Style="{StaticResource LabelErrorStyle}"/>
        <telerik:Label Content="{telerik:LocalizableResource Key=LabelOptionsNo}" Grid.Row="1" Style="{StaticResource LabelIsNoOptionsStyle}"/>
        <telerik:RadListBox Grid.Row="1" Name="lvFastAction" ScrollViewer.HorizontalScrollBarVisibility="Disabled" Style="{StaticResource GridVisibility}" FontSize="14" BorderBrush="{x:Null}">
            <telerik:RadListBox.ItemContainerStyle>
                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                    <Setter Property="Padding" Value="15,0,0,0" />
                    <Setter Property="Margin" Value="0,0,0,0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Height" Value="30" />
                    <Setter Property="Focusable" Value="False" />
                    <EventSetter Event="MouseDoubleClick" Handler="lvFastAction_DoubleClick" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
    </Grid>
</Popup>
