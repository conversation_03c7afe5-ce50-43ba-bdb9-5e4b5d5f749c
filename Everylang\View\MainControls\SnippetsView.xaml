﻿<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Everylang.App.View.MainControls.SnippetsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:mainControls="clr-namespace:Everylang.App.View.MainControls"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    xmlns:dataModel="clr-namespace:Everylang.App.Data.DataModel"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <Style TargetType="telerik:RadListBoxItem" x:Key="DraggableListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
            <Setter Property="telerik:DragDropManager.AllowCapturedDrag" Value="True" />
        </Style>
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"  IsEnabled="{Binding Path=SnippetsViewModel.jgebhdhs}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200*" />
        </Grid.RowDefinitions>
        <WrapPanel
            Grid.Row="0"
            HorizontalAlignment="Left"
            Margin="5"
            Orientation="Horizontal"
            VerticalAlignment="Top">
            <telerik:RadAutoSuggestBox
                ClearButtonVisibility="Auto"
                Name="SearchTextBox"
                WatermarkContent="{telerik:LocalizableResource Key=SearchHelperText}"
                TextChanged="TextBoxSearch_OnTextChanged"
                Width="250" />
            <telerik:RadButton
                Click="DeleteClick"
                Content="{telerik:LocalizableResource Key=Delete}"
                Focusable="False"
                Margin="10,0,0,0" />
            <telerik:RadButton
                Click="EditClick"
                Content="{telerik:LocalizableResource Key=Edit}"
                Focusable="False"
                Margin="10,0,0,0" />
            <telerik:RadButton
                Click="NewClick"
                Content="{telerik:LocalizableResource Key=New}"
                Focusable="False"
                Margin="10,0,0,0" />

        </WrapPanel>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <telerik:RadListBox
                AllowDrop="True"
                Grid.Column="0"
                ItemContainerStyle="{StaticResource DraggableListBoxItem}"
                Margin="5,0,5,5"
                MouseDoubleClick="LvSnippets_OnMouseDoubleClick"
                SelectionMode="Extended"
                VerticalAlignment="Stretch"
                x:Name="LvSnippets">
                <telerik:RadListBox.DragDropBehavior>
                    <mainControls:MyListBoxDragDropBehavior />
                </telerik:RadListBox.DragDropBehavior>
                <telerik:RadListBox.DragVisualProvider>
                    <telerik:ScreenshotDragVisualProvider />
                </telerik:RadListBox.DragVisualProvider>
                <telerik:RadListBox.ItemTemplate>
                    <DataTemplate DataType="{x:Type dataModel:SnippetsDataModel}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="30" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                FontSize="14"
                                HorizontalAlignment="Left"
                                Text="{Binding ShortText}"
                                ToolTip="{Binding Text}"
                                VerticalAlignment="Center" />
                            <Border
                                Background="Transparent"
                                Grid.Column="1"
                                ToolTip="{Binding TextToolTip}"
                                Visibility="{Binding IsSnippets, Converter={StaticResource BoolToVis}}">
                                <wpf:MaterialIcon
                                    HorizontalAlignment="Right"
                                    Kind="Autorenew"
                                    Height="18"
                                    Width="18"
                                    Margin="0,0,0,0" />
                            </Border>

                        </Grid>
                    </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
            </telerik:RadListBox>

            <telerik:RadListBox
                Focusable="False"
                Grid.Column="1"
                Margin="5,0,5,5"
                MinWidth="150"
                SelectionChanged="LvTags_SelectionChanged"
                SelectionMode="Extended"
                VerticalAlignment="Stretch"
                x:Name="LvTags">
                <telerik:RadListBox.ItemTemplate>
                    <DataTemplate>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                FontSize="14"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,0"
                                MinHeight="0"
                                Text="{Binding}"
                                VerticalAlignment="Center" />
                            <Border
                                Background="Transparent"
                                Cursor="Hand"
                                Grid.Column="1"
                                Margin="0,0,0,0"
                                MouseUp="ButtonsTags_OnDeleteClick"
                                VerticalAlignment="Center">
                                <wpf:MaterialIcon Kind="Close" />
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="True">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="False">
                                                <Setter Property="Visibility" Value="Hidden" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                            </Border>
                        </Grid>
                    </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
            </telerik:RadListBox>
        </Grid>
    </Grid>
</UserControl>