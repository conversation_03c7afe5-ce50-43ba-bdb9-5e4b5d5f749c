﻿<UserControl x:Class="Everylang.App.View.SettingControls.Clipboard.ClipboardControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:globalization="clr-namespace:System.Globalization;assembly=mscorlib"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="400" d:DesignWidth="350" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <StackPanel Margin="20,10,0,0" HorizontalAlignment="Left" VerticalAlignment="Top">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=ClipboardTab}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>
            <StackPanel Margin="0,0,0,0">
                <telerik:RadToggleSwitchButton
                    Margin="0,0,0,0"
                    HorizontalAlignment="Left"
                    Focusable="False"
                    VerticalAlignment="Center"
                    CheckedContent="{telerik:LocalizableResource Key=ClipboardOn}"
                    ContentPosition="Right"
                    FontSize="14"
                    FontWeight="DemiBold"
                    IsChecked="{Binding Path=ClipboardViewModel.IsEnabled}"
                    UncheckedContent="{telerik:LocalizableResource Key=ClipboardOff}" />
                <TextBlock Margin="0,0,0,0" FontSize="13"  Text="{telerik:LocalizableResource Key=ClipboardKeyboardViewShortcuts}" />
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0" IsEnabled="{Binding ClipboardViewModel.IsEnabled}">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ClipboardSettingsViewModel.ShortcutView}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ShortcutView}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="ClipboardClipboardShortcutViewClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
            </StackPanel>
            <StackPanel Margin="0,0,0,0" IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <CheckBox MinHeight="0" FontSize="13" Focusable="False" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcutIsOn}" >
                    <TextBlock  FontSize="13"  Text="{telerik:LocalizableResource Key=ClipboardKeyboardShortcuts}" />
                </CheckBox>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0" IsEnabled="{Binding ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcutIsOn}">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="ClipboardPasteWithoutFormattingShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>
            </StackPanel>

            <StackPanel Margin="0,0,0,0" IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <CheckBox MinHeight="0" FontSize="13" Focusable="False" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteRoundIsOn}" >
                    <TextBlock TextWrapping="Wrap"  FontSize="13"  Text="{telerik:LocalizableResource Key=ClipboardKeyboardRoundShortcuts}" />
                </CheckBox>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0" IsEnabled="{Binding ClipboardSettingsViewModel.ClipboardPasteRoundIsOn}">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ClipboardSettingsViewModel.ShortcutRound}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ShortcutRound}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="ClipboardShortcutRoundClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>

            </StackPanel>
            <CheckBox MinHeight="0" Margin="0,0,0,0" Focusable="False" FontSize="13" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardReplaceWithoutChangeClipboard}"  IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <TextBlock TextWrapping="Wrap" FontSize="13" Text="{telerik:LocalizableResource Key=ClipboardSettingsReplaceWithoutChangeClipboard}" />
            </CheckBox>
            <CheckBox MinHeight="0" Margin="0,0,0,0" Focusable="False" FontSize="13" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteByIndexIsOn}"  IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <TextBlock TextWrapping="Wrap" FontSize="13" Text="{telerik:LocalizableResource Key=ClipboardSettingsPasteByIndexIsOn}" />
            </CheckBox>
            <CheckBox MinHeight="0" Margin="0,0,0,0"  Focusable="False" FontSize="13" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardSaveFilePath}"  IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <TextBlock TextWrapping="Wrap" FontSize="13" Text="{telerik:LocalizableResource Key=ClipboardSettingsSaveFilePath}" />
            </CheckBox>
            <CheckBox MinHeight="0" Margin="0,0,0,0" Focusable="False" FontSize="13" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardSaveImage}"  IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <TextBlock TextWrapping="Wrap" FontSize="13" Text="{telerik:LocalizableResource Key=ClipboardSettingsSaveImage}" />
            </CheckBox>

            <StackPanel Orientation="Horizontal" IsEnabled="{Binding ClipboardViewModel.IsEnabled}"  Margin="0,0,0,0">
                <CheckBox FontSize="14" Focusable="False" IsChecked="{Binding Path=ClipboardSettingsViewModel.ClipboardSountIsOn}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ClipboardSound}" />
                </CheckBox>
                <telerik:RadButton Focusable="False"  Margin="5,0,0,0" Click="SoundClick" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5" Content="{telerik:LocalizableResource Key=Edit}"/>
            </StackPanel>

            <StackPanel Orientation="Horizontal"  Margin="0,0,0,0" IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                <TextBlock Text="{telerik:LocalizableResource Key=ClipboardMaxClipboardItems}" VerticalAlignment="Center" FontSize="13"/>
                <telerik:RadNumericUpDown  Margin="10,0,0,0" Width="110"
                                        VerticalAlignment="Center"
                                        FontSize="13"
                                        Value="{Binding Path=ClipboardSettingsViewModel.MaxClipboardItems}" >
                    <telerik:RadNumericUpDown.NumberFormatInfo>
                        <globalization:NumberFormatInfo NumberDecimalDigits="0" />
                    </telerik:RadNumericUpDown.NumberFormatInfo>
                </telerik:RadNumericUpDown>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
