﻿using System;

namespace Everylang.App.Clipboard.ClipboardWinApi
{
    /// <summary>
    /// Holds clipboard data of a single data format.
    /// </summary>
    [Serializable]
    internal class ClipboardData
    {
        private uint _format;

        internal uint Format
        {
            get => _format;
            set => _format = value;
        }

        private string _formatName;

        internal string FormatName
        {
            get => _formatName;
            set => _formatName = value;
        }
        private byte[]? _buffer;

        private readonly int _size;

        internal byte[]? Buffer
        {
            get => _buffer;
            set => _buffer = value;
        }

        internal UIntPtr Size
        {
            get
            {
                if (_buffer != null)
                {
                    //Read the correct size from buffer, if it is not null
                    return new UIntPtr(Convert.ToUInt32(_buffer.GetLength(0)));
                }
                else
                {
                    //else return the optional set size
                    return new UIntPtr(Convert.ToUInt32(_size));
                }
            }
        }
        /// <summary>
        /// Start a Clip Data object, containing one clipboard data and its format
        /// </summary>
        /// <param name="format"></param>
        /// <param name="formatName"></param>
        /// <param name="buffer"></param>
        internal ClipboardData(uint format, string formatName, byte[]? buffer)
        {
            this._format = format;
            this._formatName = formatName;
            this._buffer = buffer;
            this._size = 0;
        }

    }


}
