﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Snippets;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;
using Telerik.Windows.DragDrop;
using Telerik.Windows.DragDrop.Behaviors;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for SnippetsView.xaml
    /// </summary>
    internal partial class SnippetsView
    {
        private List<string> _tagList;
        private List<string> _lastSelectedTagList;
        private List<SnippetsDataModel?> _snippetList;
        private RadObservableCollection<SnippetsDataModel?> _autochangeList;

        internal SnippetsView()
        {
            InitializeComponent();
            _lastSelectedTagList = new List<string>();
            LoadData("");
            DragDropManager.AddDragDropCompletedHandler(LvSnippets, OnDragDropCompleted);
        }

        private void OnDragDropCompleted(object sender, DragDropCompletedEventArgs e)
        {
            LoadItems();
        }

        internal void LoadItems()
        {
            if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
            {
                LvSnippets.ItemsSource = _snippetList.OrderByDescending(x => x?.CountUsage);
            }

            if (SettingsManager.Settings.SnippetsIsEnabledSortingAlphabet)
            {
                LvSnippets.ItemsSource = _snippetList.OrderBy(x => x?.ShortText);
            }
        }

        private bool _isLoading;

        internal void LoadData(string filter)
        {
            _isLoading = true;
            VMContainer.Instance.SnippetsViewModel.GetAllDataFromDb();
            _autochangeList = VMContainer.Instance.SnippetsViewModel.SnippetsList;
            if (!string.IsNullOrEmpty(filter))
            {
                var searchList = VMContainer.Instance.SnippetsViewModel.SnippetsList.Where(x => x?.ShortText != null && x.Text != null && (x.Text.ToLower().Contains(filter.ToLower()) || x.ShortText.ToLower().Contains(filter.ToLower()))).ToList();
                _autochangeList.Clear();
                foreach (var snippetsDataModel in searchList)
                {
                    _autochangeList.Add(snippetsDataModel);
                }

            }
            _tagList = new List<string>();
            //SearchTextBox.Text = "";
            _snippetList = new List<SnippetsDataModel?>();
            _tagList.Add(LocalizationManager.GetString("All"));
            var tagList = new Dictionary<string, int>();
            LvTags.SelectedIndex = -1;
            foreach (var snippetsDataModel in _autochangeList)
            {
                if (snippetsDataModel?.Tags != null)
                {
                    var tags = snippetsDataModel.Tags.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    foreach (var tag in tags)
                    {
                        if (!tagList.ContainsKey(tag))
                        {
                            tagList.Add(tag, snippetsDataModel.CountUsage);
                        }
                        else
                        {
                            tagList[tag] += snippetsDataModel.CountUsage;
                        }
                    }
                }
            }

            var ordered = tagList.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            _tagList.AddRange(ordered.Keys);
            LvTags.ItemsSource = _tagList;
            if (_lastSelectedTagList.Count > 0)
            {
                foreach (var tag in _lastSelectedTagList)
                {
                    LvTags.SelectedItems.Add(tag);
                }
            }
            else
            {
                LvTags.SelectedIndex = 0;
            }
            _isLoading = false;
        }

        private void TextBoxSearch_OnTextChanged(object sender, Telerik.Windows.Controls.AutoSuggestBox.TextChangedEventArgs e)
        {
            if (e.Reason == Telerik.Windows.Controls.AutoSuggestBox.TextChangeReason.UserInput)
            {
                string text = SearchTextBox.Text;
                LoadData(text);
            }
        }

        private void DeleteClick(object sender, RoutedEventArgs e)
        {
            foreach (var selectedItem in LvSnippets.SelectedItems)
            {
                SnippetsDataModel snippet = (SnippetsDataModel)selectedItem;
                SnippetsManager.DelSnippetsData(snippet);
            }
            LoadData("");
        }

        private void EditClick(object sender, RoutedEventArgs e)
        {
            EditSnippet();
        }

        private void NewClick(object sender, RoutedEventArgs e)
        {
            ShowHelperWindow(null);
            LoadData("");
        }

        private bool ShowHelperWindow(SnippetsDataModel? dataModel)
        {
            SnippetsHelperWindow? snippetsHelperWindow;
            if (_lastSelectedTagList.Count == 1 && _lastSelectedTagList[0] != LocalizationManager.GetString("All"))
            {
                snippetsHelperWindow = new SnippetsHelperWindow(dataModel, "", _lastSelectedTagList[0]);
            }
            else
            {
                snippetsHelperWindow = new SnippetsHelperWindow(dataModel, "", "");
            }
            Application curApp = Application.Current;
            if (curApp.MainWindow != null)
            {
                Window mainWindow = curApp.MainWindow;
                snippetsHelperWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                snippetsHelperWindow.Owner = mainWindow;
            }

            var result = snippetsHelperWindow.ShowDialog();
            snippetsHelperWindow.TextBox.Focus();
            return result != null && result.Value;
        }

        private void LvSnippets_OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            EditSnippet();
        }

        private void EditSnippet()
        {
            if (LvSnippets.SelectedItem != null)
            {
                SnippetsDataModel snippet = (SnippetsDataModel)LvSnippets.SelectedItem;
                if (ShowHelperWindow(snippet))
                {
                    LoadData("");
                }
            }
        }

        private void LvTags_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _snippetList.Clear();
            LvSnippets.ItemsSource = null;
            if (!_isLoading)
            {
                _lastSelectedTagList.Clear();
            }
            if (LvTags.SelectedItems.Count > 0)
            {
                var selectedItems = LvTags.SelectedItems.Cast<string>().ToList();
                if (!_isLoading) _lastSelectedTagList = new List<string>(selectedItems);
                if (selectedItems.Contains(LocalizationManager.GetString("All")))
                {
                    _snippetList = new List<SnippetsDataModel?>(_autochangeList);
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        LvSnippets.ItemsSource = _snippetList.OrderByDescending(x => x?.CountUsage);
                    }

                    if (SettingsManager.Settings.SnippetsIsEnabledSortingAlphabet)
                    {
                        LvSnippets.ItemsSource = _snippetList.OrderBy(x => x?.ShortText);
                    }
                    if (_autochangeList.Count > 0)
                    {
                        LvSnippets.SelectedIndex = 0;
                    }
                }
                else
                {
                    foreach (var selectedItem in selectedItems)
                    {
                        foreach (var snippet in _autochangeList)
                        {
                            if (!string.IsNullOrEmpty(snippet?.Tags) && (snippet.Tags.Contains(selectedItem) && !_snippetList.Contains(snippet)))
                            {
                                _snippetList.Add(snippet);
                            }
                        }
                    }
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        LvSnippets.ItemsSource = _snippetList.OrderByDescending(x => x?.CountUsage);
                    }

                    if (SettingsManager.Settings.SnippetsIsEnabledSortingAlphabet)
                    {
                        LvSnippets.ItemsSource = _snippetList.OrderBy(x => x?.ShortText);
                    }
                    if (_snippetList.Count > 0)
                    {
                        LvSnippets.SelectedIndex = 0;
                    }

                }
            }
        }

        private void ButtonsTags_OnDeleteClick(object sender, MouseButtonEventArgs e)
        {
            if (LvTags.SelectedItem == null)
            {
                return;
            }

            if (LvTags.SelectedItem is string tag)
            {
                var snippetList = _autochangeList.Where(x => x?.Tags != null && x.Tags.Contains(tag)).ToList();
                if (snippetList.Count > 0)
                {
                    RadWindow.Confirm(new DialogParameters()
                    {
                        Content = LocalizationManager.GetString("AutochangeDelWithTag"),
                        Closed = (_, args) =>
                        {
                            if (args.DialogResult == true)
                            {
                                foreach (var snippet in snippetList)
                                {
                                    var tags = snippet?.Tags?.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                    if (tags != null && tags.Count > 1)
                                    {
                                        if (snippet != null)
                                        {
                                            snippet.Tags = snippet.Tags?.Replace(tag, "");
                                            SnippetsManager.UpdateData(snippet);
                                        }
                                    }
                                    else
                                    {
                                        if (snippet != null) SnippetsManager.DelSnippetsData(snippet);
                                    }
                                }
                                LoadData("");
                            }
                        }
                    });
                }
            }
        }
    }
    internal class MyListBoxDragDropBehavior : ListBoxDragDropBehavior
    {
        public override void Drop(Telerik.Windows.DragDrop.Behaviors.DragDropState state)
        {
            var droppedData = state.DraggedItems.OfType<SnippetsDataModel>().ToList().FirstOrDefault();
            int insertIndex = state.InsertIndex;
            if (insertIndex == -1)
            {
                insertIndex = state.SourceItemsSource.Count - 1;
            }

            if (droppedData != null && state.SourceItemsSource[insertIndex] is SnippetsDataModel target)
            {
                int removedIdx = state.SourceItemsSource.IndexOf(droppedData);
                int targetIdx = state.SourceItemsSource.IndexOf(target);
                if (removedIdx < targetIdx)
                {
                    droppedData.CountUsage = target.CountUsage - 1;
                }
                else
                {
                    droppedData.CountUsage = target.CountUsage + 1;
                }
            }

            if (droppedData != null) SnippetsManager.UpdateData(droppedData);
        }

    }
}
