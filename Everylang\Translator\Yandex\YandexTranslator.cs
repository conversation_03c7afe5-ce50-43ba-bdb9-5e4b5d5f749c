﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Everylang.Common.LogManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Web;

namespace Everylang.App.Translator.Yandex
{
    class YandexTranslator
    {
        private static string? _id;
        private static DateTime _idLastTime;
        private string? _languageFromCurrent;
        private string? _languageToCurrent;
        private RequestSettings? _requestSettings;
        private WebResultTranslator? _webResult;

        internal WebResultTranslator? Translate(RequestSettings? requestSettings)
        {
            _requestSettings = requestSettings;
            return Translate(false);
        }

        private WebResultTranslator? Translate(bool isSecond)
        {
            if ((DateTime.Now - _idLastTime).TotalHours > 1.0 || string.IsNullOrEmpty(_id))
            {
                _id = "";
                GetId();
                if (!string.IsNullOrEmpty(_id))
                {
                    _idLastTime = DateTime.Now;
                }
            }

            if (!isSecond)
            {
                _languageFromCurrent = _requestSettings?.LanguageFromCurrent.Abbreviation;
                _languageToCurrent = _requestSettings?.LanguageToCurrent.Abbreviation;
            }

            if (_languageFromCurrent == _languageToCurrent)
            {
                _languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
            }
            if (_languageFromCurrent == _languageToCurrent)
            {
                _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
            }
            string? lang = _languageFromCurrent + "-" + _languageToCurrent;
            if (_languageFromCurrent == "auto")
            {
                lang = _languageToCurrent;
            }

            List<string> textForRequestList = new List<string>();
            string[] sentences = Regex.Split(_requestSettings?.SourceTextTrimmed!, @"(?<=[.!?])");
            string textRes = "";
            foreach (var sentence in sentences)
            {
                if ((textRes + sentence).Length > 3000)
                {
                    textForRequestList.Add(textRes);
                    textRes = sentence;
                }
                else
                {
                    textRes += sentence;
                }
            }
            if (!string.IsNullOrEmpty(textRes))
            {
                textForRequestList.Add(textRes);
            }

            _webResult = new WebResultTranslator();
            for (var i = 0; i < textForRequestList.Count; i++)
            {
                var textForRequest = textForRequestList[i];
                string text = HttpUtility.UrlEncode(textForRequest);
                string urlPlus = @"https://translate.yandex.net/api/v1/tr.json";
                string query = $"?ucid={_id}" +
                                "&srv=android" +
                                "&format=text";
                string data = $"text={text}&lang={lang}";

                var netLib = new NetLibTranslator(urlPlus + $"/translate{query}", data, @"https://translate.yandex.ru/");
                var webResult = netLib.StartPostWebRequestYandex();
                if (webResult.ResultText != null)
                {
                    if (!webResult.WithError)
                    {
                        if (_languageFromCurrent == "auto")
                        {
                            var res = Regex.Match(webResult.ResultText, "\"lang\".\"(.*?)-.*?\"");
                            if (res.Success)
                            {
                                _languageFromCurrent = res.Groups[1].Value;
                            }
                        }
                        bool isOtherLangStart = false;
                        if (_languageFromCurrent == _languageToCurrent)
                        {
                            _languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                            isOtherLangStart = true;
                        }
                        if (_languageFromCurrent == _languageToCurrent)
                        {
                            _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                            isOtherLangStart = true;
                        }

                        if (isOtherLangStart)
                        {
                            return Translate(true);
                        }
                        _webResult.ResultText += ProcessingOfTheRequest(webResult.ResultText);
                    }
                    else
                    {
                        _webResult.WithError = true;
                        _webResult.ErrorText = webResult.ErrorText;
                    }

                }

                if (_requestSettings != null && _requestSettings.IsOneWord)
                {
                    lang = _languageFromCurrent + "-" + _languageToCurrent;
                    string urlPlus1 = @"https://translate.yandex.net/dicservice.json/lookup";
                    string urldic =
                        urlPlus1 + $"?srv=android&ucid={_id}&ui={CultureInfo.CurrentCulture.TwoLetterISOLanguageName.ToLower()}";
                    string data1 = $"text={text.Trim()}&lang={lang}";

                    var netLibdic = new NetLibTranslator(urldic, data1, @"https://translate.yandex.ru/" + "?lang=" + lang);
                    var webResultdic = netLibdic.StartPostWebRequestYandex();
                    if (webResultdic.ResultText != null)
                    {
                        if (!webResultdic.WithError)
                        {
                            _webResult.ResultTextWithNonChar = _requestSettings.StartNonCharList + _webResult.ResultText + _requestSettings.EndNonCharList;
                            _webResult.ResultText = ProcessingOfTheRequestDict(webResultdic.ResultText, _webResult.ResultText);

                        }
                        else
                        {
                            _webResult.WithError = true;
                            _webResult.ErrorText = webResult.ErrorText;
                        }
                    }
                }
                else
                {
                    _webResult.ResultTextWithNonChar = _requestSettings?.StartNonCharList + _webResult.ResultText + _requestSettings?.EndNonCharList;
                    _webResult.ResultText = _webResult.ResultTextWithNonChar;
                }
            }


            _webResult.FromLang = _languageFromCurrent;
            _webResult.ToLang = _languageToCurrent;

            return _webResult;
        }

        private string ProcessingOfTheRequest(string? resultHttp)
        {
            var resultText = "";
            if (resultHttp != null)
            {
                try
                {
                    JObject json = JObject.Parse(resultHttp);
                    if (json.TryGetValue("text", out var dictJToken))
                    {
                        foreach (var token in dictJToken)
                        {
                            resultText += token.Value<string>();
                        }
                    }
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            }
            return resultText;
        }

        private string? ProcessingOfTheRequestDict(string? resultHttp, string? mainText)
        {
            var resultText = "";
            if (resultHttp != null)
            {

                try
                {
                    JObject json = JObject.Parse(resultHttp);

                    var traslateResultStructList = new List<TraslateResultStructYa>();
                    if (json.TryGetValue("def", out var dictJToken))
                    {
                        try
                        {
                            foreach (var token in dictJToken)
                            {

                                var traslateResultStructYa = new TraslateResultStructYa();
                                traslateResultStructYa.Pos = token.SelectToken("pos")?.ToString();
                                var trTokenList = token.SelectToken("tr");
                                traslateResultStructYa.TextList = new List<TextStructYa>();
                                if (trTokenList != null)
                                    foreach (var trToken in trTokenList)
                                    {
                                        var textStructYa = new TextStructYa();
                                        textStructYa.TextList = new List<string?>();
                                        textStructYa.MeanList = new List<string>();

                                        textStructYa.TextList.Add(trToken.SelectToken("text")?.ToString());
                                        if (trToken.SelectToken("syn") != null)
                                        {
                                            var synToken = trToken.SelectToken("syn");
                                            if (synToken != null)
                                            {
                                                foreach (var syn in synToken)
                                                {
                                                    textStructYa.TextList.Add(syn.SelectToken("text")?.ToString());
                                                }
                                            }
                                        }

                                        if (trToken.SelectToken("mean") != null)
                                        {
                                            var meanToken = trToken.SelectToken("mean");
                                            if (meanToken != null)
                                            {
                                                foreach (var mean in meanToken)
                                                {
                                                    textStructYa.MeanList.Add(mean.SelectToken("text")?.ToString() ?? string.Empty);
                                                }
                                            }
                                        }

                                        traslateResultStructYa.TextList.Add(textStructYa);

                                        var exampleStructYaList = new List<ExampleStructYa>();

                                        if (trToken.SelectToken("ex") != null)
                                        {
                                            var exListTokenList = trToken.SelectToken("ex");
                                            if (exListTokenList != null)
                                                foreach (var exListToken in exListTokenList)
                                                {
                                                    var exampleStructYa = new ExampleStructYa();
                                                    exampleStructYa.ExListSource =
                                                        exListToken.SelectToken("text")?.ToString();
                                                    exampleStructYa.ExListTrans =
                                                        exListToken.SelectToken("tr")?[0]?.SelectToken("text")
                                                            ?.ToString();
                                                    exampleStructYaList.Add(exampleStructYa);
                                                }
                                        }

                                        traslateResultStructYa.ExampleStructList = exampleStructYaList;
                                    }

                                traslateResultStructList.Add(traslateResultStructYa);
                            }
                        }
                        catch (Exception e)
                        {
                            Logger.LogTo.Error(e, e.Message);
                        }
                    }

                    foreach (var traslateResultStructYa in traslateResultStructList)
                    {
                        resultText += "\n<underline>" + traslateResultStructYa.Pos + ":";

                        foreach (var textList in traslateResultStructYa.TextList)
                        {
                            resultText += "\n      ";
                            for (int index = 0; index < textList.TextList.Count; index++)
                            {
                                if (index + 1 == textList.TextList.Count)
                                {
                                    resultText += textList.TextList[index];
                                }
                                else
                                {
                                    resultText += textList.TextList[index] + ", ";
                                }
                            }
                            if (textList.MeanList.Count > 0) resultText += "\n      ";

                            for (int index = 0; index < textList.MeanList.Count; index++)
                            {
                                if (index == 0)
                                {
                                    resultText += "(";
                                }
                                if (index + 1 == textList.MeanList.Count)
                                {
                                    resultText += textList.MeanList[index] + ")";
                                }
                                else
                                {
                                    resultText += textList.MeanList[index] + ", ";
                                }
                            }
                        }

                        foreach (var exampleStruct in traslateResultStructYa.ExampleStructList)
                        {
                            resultText += "\n<italic>      " + exampleStruct.ExListSource + " - " + exampleStruct.ExListTrans;
                        }
                    }



                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
                if (resultText == "")
                {
                    resultText = mainText;
                }
                else
                {
                    resultText = "<bold>" + mainText + "\n" + "\n" + resultText;
                }

            }
            return resultText;
        }


        internal void GetId()
        {
            _id = Guid.NewGuid().ToString("N");
            if (!string.IsNullOrEmpty(_id))
            {
                _idLastTime = DateTime.Now;
            }
        }

        internal string Reverse(string s)
        {
            char[] charArray = s.ToCharArray();
            Array.Reverse(charArray);
            return new string(charArray);
        }

        internal struct TraslateResultStructYa
        {
            internal List<ExampleStructYa> ExampleStructList;
            internal List<TextStructYa> TextList;
            internal string? Pos;
        }

        internal struct ExampleStructYa
        {
            internal string? ExListTrans;
            internal string? ExListSource;
        }

        internal struct TextStructYa
        {
            internal List<string?> TextList;
            internal List<string> MeanList;
        }
    }


}
