﻿using Everylang.App.SettingsApp;
using RestSharp;
using System;
using System.Net;
using System.Threading.Tasks;

namespace Everylang.App.Utilities.NetRequest
{
    internal class NetLib
    {
        private readonly string _uri;
        private readonly string? _data;

        internal NetLib(string uri, string? data = null)
        {
            _uri = uri;
            _data = data;
        }

        internal WebResult StartGetWebRequest()
        {
            var webResult = new WebResult();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = GetProxy(),
                    UserAgent = SettingsManager.UserAgent
                });
                var request = new RestRequest(_uri);
                var response = client.Get(request);
                webResult.ResultText = response.Content;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal async Task<WebResult> StartGetWebRequestWithFormUrlencodedAsync()
        {
            var webResult = new WebResult();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy()
                });
                var req = new RestRequest(_uri);
                req.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                var res = await client.ExecuteAsync(req);

                webResult.ResultText = res.Content;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal WebResult StartPostWebRequest()
        {

            var webResult = new WebResult();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = GetProxy(),
                    UserAgent = SettingsManager.UserAgent

                });
                var req = new RestRequest(_uri, Method.Post);
                if (_data != null)
                    req.AddParameter("application/x-www-form-urlencoded", _data, ParameterType.RequestBody);
                var res = client.Execute(req);
                webResult.ResultText = res.Content;
                return webResult;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal static async Task<bool> CheckInternetConnectionAsync()
        {
            try
            {
                var client = new RestClient();
                var request = new RestRequest("https://www.google.com", Method.Head);
                var response = await client.ExecuteAsync(request);
                return response.IsSuccessful;
            }
            catch (Exception)
            {
                return false;
            }
        }

        //internal static bool CheckInternetConnection()
        //{
        //    try
        //    {
        //        var request = (HttpWebRequest)WebRequest.Create("http://g.cn/generate_204");
        //        request.UserAgent = SettingsManager.UserAgent;
        //        request.Proxy = GetProxy();
        //        using (var response = (HttpWebResponse)request.GetResponse())
        //        {
        //            if (response.ContentLength == 0 && response.StatusCode == HttpStatusCode.NoContent)
        //            {
        //                return true;
        //            }
        //            else
        //            {
        //                return false;
        //            }
        //        }
        //    }
        //    catch { }
        //    return false;
        //}

        internal static IWebProxy? GetProxy()
        {
            IWebProxy? defaultProxy = null;
            try
            {
                if (!SettingsManager.Settings.ProxyUseIE && !string.IsNullOrEmpty(SettingsManager.Settings.ProxyServer) && !string.IsNullOrEmpty(SettingsManager.Settings.ProxyPort))
                {
                    defaultProxy = new WebProxy(SettingsManager.Settings.ProxyServer + ":" + SettingsManager.Settings.ProxyPort);
                    if (string.IsNullOrEmpty(SettingsManager.Settings.ProxyUserName))
                    {
                        defaultProxy.Credentials = CredentialCache.DefaultCredentials;
                    }
                    else
                    {
                        defaultProxy.Credentials = new NetworkCredential(SettingsManager.Settings.ProxyUserName, SettingsManager.Settings.ProxyPassword);
                    }
                }
                else
                {
                    defaultProxy = WebRequest.GetSystemWebProxy();
                    defaultProxy.Credentials = CredentialCache.DefaultCredentials;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return defaultProxy;
        }
    }
}
