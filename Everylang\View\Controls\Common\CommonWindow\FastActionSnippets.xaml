﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.Controls.Common.CommonWindow.FastActionSnippets"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:commonWindow="clr-namespace:Everylang.App.View.Controls.Common.CommonWindow"
    xmlns:helpers="clr-namespace:Everylang.App.View.Helpers" x:ClassModifier="internal">
    <UserControl.Resources>
        <commonWindow:ViewIndexConverter x:Key="ViewIndexConverter" />
        <commonWindow:ViewIndexTooltipConverter x:Key="ViewIndexTooltipConverter" />
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="100" />
        </Grid.ColumnDefinitions>
        <telerik:RadListBox
            Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"
            BorderThickness="0"
            PreviewMouseDoubleClick="LvSnippets_OnPreviewMouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
            SelectionMode="Extended"
            VerticalAlignment="Stretch"
            x:Name="LvSnippets">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <Border
                        BorderThickness="1"
                        CornerRadius="2"
                        Margin="5"
                        Padding="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="10" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock
                                    FontSize="10"
                                    Text="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Mode=OneWay, Converter={StaticResource ViewIndexConverter}}"
                                    ToolTip="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Mode=OneWay, Converter={StaticResource ViewIndexTooltipConverter}}"
                                    VerticalAlignment="Center" />
                                <helpers:TextBlockWithSelection
                                    FontSize="12"
                                    Grid.Column="1"
                                    HorizontalAlignment="Left"
                                    Text="{Binding ShortText}"
                                    ToolTip="{Binding Text}"
                                    VerticalAlignment="Center" />
                            </Grid>
                            <Border
                                Background="Transparent"
                                Cursor="Hand"
                                Grid.Column="1"
                                Margin="0,0,0,0"
                                MouseUp="OpenMenuMouseDown"
                                VerticalAlignment="Center">
                                <wpf:MaterialIcon Kind="DotsVertical" />
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="True">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="False">
                                                <Setter Property="Visibility" Value="Hidden" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}" TargetType="telerik:RadListBoxItem">
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Background" Value="{telerik:Windows11Resource ResourceKey=OverlayBrush}" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
        <Separator Grid.Column="1" VerticalAlignment="Stretch" />
        <telerik:RadListBox
            Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"
            BorderThickness="0"
            Grid.Column="2"
            SelectionChanged="LvTags_SelectionChanged"
            SelectionMode="Extended"
            VerticalAlignment="Stretch"
            x:Name="LvTags">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <Border
                        BorderThickness="1"
                        CornerRadius="2"
                        Margin="5"
                        Padding="5">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Left"
                                Text="{Binding}"
                                ToolTip="{Binding}" />
                        </StackPanel>
                    </Border>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}" TargetType="telerik:RadListBoxItem">
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Background" Value="{telerik:Windows11Resource ResourceKey=OverlayBrush}" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
    </Grid>
</UserControl>
