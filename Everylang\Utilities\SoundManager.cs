﻿//using Everylang.App.SettingsApp;
//using Everylang.Common.LogManager;
//using System.IO;
//using System.Reflection;
//using System.Windows.Media;

//namespace Everylang.App.Utilities
//{
//    static class SoundManager
//    {

//        internal static Dictionary<string, string> AllSounds;

//        static SoundManager()
//        {
//            AllSounds = new Dictionary<string, string>
//            {
//                { "Suled", "EveryLang.Utilities.sounds.Suled.wav" },
//                { "Honond", "EveryLang.Utilities.sounds.Honond.wav" },
//                { "Ceech", "EveryLang.Utilities.sounds.Ceech.wav" },
//                { "Denot", "EveryLang.Utilities.sounds.Denot.wav" },
//                { "Nykorm", "EveryLang.Utilities.sounds.Nykorm.wav" },
//                { "Burrod", "EveryLang.Utilities.sounds.Burrod.wav" },
//                { "Lorsam", "EveryLang.Utilities.sounds.Lorsam.wav" },
//                { "Untos", "EveryLang.Utilities.sounds.Untos.wav" },
//                { "Riltur", "EveryLang.Utilities.sounds.Riltur.wav" },
//                { "Reskel", "EveryLang.Utilities.sounds.Reskel.wav" },
//                { "Ormild", "EveryLang.Utilities.sounds.Ormild.wav" },
//                { "Eldyp", "EveryLang.Utilities.sounds.Eldyp.wav" },
//                { "Tesul", "EveryLang.Utilities.sounds.Tesul.wav" }
//            };
//        }

//        internal static void PlayForSpellCheck()
//        {
//            PlaySound(AllSounds[SettingsManager.Settings.SoundForSpellCheck], (float)SettingsManager.Settings.SoundVolumeForSpellCheck / 100);
//        }

//        internal static void PlayForSwitch()
//        {
//            PlaySound(AllSounds[SettingsManager.Settings.SoundForLangSwitch], (float)SettingsManager.Settings.SoundVolumeForLangSwitch / 100);
//        }

//        internal static void PlayForClipboard()
//        {
//            PlaySound(AllSounds[SettingsManager.Settings.SoundForClipboard], (float)SettingsManager.Settings.SoundVolumeForClipboard / 100);
//        }

//        internal static void PlaySound(string soundPath, float volume)
//        {
//            Task.Run(() =>
//            {
//                try
//                {
//                    var name = soundPath;
//                    var assembly = Assembly.GetExecutingAssembly();
//                    using var ms = assembly.GetManifestResourceStream(name);
//                    if (ms != null)
//                    {
//                        if (ms != Stream.Null)
//                        {
//                            ms.Position = 0;
//                            var tempFile = Path.GetTempFileName();
//                            File.Move(tempFile, tempFile + ".wav");
//                            tempFile += ".wav";
//                            using (var fileStream = File.Create(tempFile))
//                            {
//                                ms.Seek(0, SeekOrigin.Begin);
//                                ms.CopyTo(fileStream);
//                            }
//                            var soundPlayer = new MediaPlayer
//                            {
//                                Volume = volume
//                            };
//                            soundPlayer.Open(new Uri(Path.GetFullPath(tempFile)));
//                            Thread.Sleep(200);
//                            soundPlayer.Play();
//                            Thread.Sleep(2000);
//                            soundPlayer.Close();
//                            File.Delete(tempFile);
//                        }
//                    }
//                }
//                catch (Exception e)
//                {
//                    Logger.LogTo.Error(e, e.Message);
//                }
//            });
//        }
//    }
//}

using Everylang.App.SettingsApp;
using Everylang.Common.LogManager;
using NAudio.Wave;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace Everylang.App.Utilities
{
    static class SoundManager
    {

        internal static Dictionary<string, string> AllSounds;

        static SoundManager()
        {
            AllSounds = new Dictionary<string, string>();
            AllSounds.Add("Suled", "Suled.wav");
            AllSounds.Add("Honond", "Honond.wav");
            AllSounds.Add("Ceech", "Ceech.wav");
            AllSounds.Add("Denot", "Denot.wav");
            AllSounds.Add("Nykorm", "Nykorm.wav");
            AllSounds.Add("Burrod", "Burrod.wav");
            AllSounds.Add("Lorsam", "Lorsam.wav");
            AllSounds.Add("Untos", "Untos.wav");
            AllSounds.Add("Riltur", "Riltur.wav");
            AllSounds.Add("Reskel", "Reskel.wav");
            AllSounds.Add("Ormild", "Ormild.wav");
            AllSounds.Add("Eldyp", "Eldyp.wav");
            AllSounds.Add("Tesul", "Tesul.wav");
        }

        internal static void PlayForSpellCheck()
        {
            PlaySound(AllSounds[SettingsManager.Settings.SoundForSpellCheck], (float)SettingsManager.Settings.SoundVolumeForSpellCheck / 100);
        }

        internal static void PlayForSwitch()
        {
            PlaySound(AllSounds[SettingsManager.Settings.SoundForLangSwitch], (float)SettingsManager.Settings.SoundVolumeForLangSwitch / 100);
        }

        internal static void PlayForClipboard()
        {
            PlaySound(AllSounds[SettingsManager.Settings.SoundForClipboard], (float)SettingsManager.Settings.SoundVolumeForClipboard / 100);
        }

        internal static void PlaySound(string soundPath, float volume)
        {
            Task.Run(() =>
            {
                try
                {
                    var assembly = Assembly.GetExecutingAssembly();
                    var resourceNames = assembly.GetManifestResourceNames();
                    var ms = assembly.GetManifestResourceStream(resourceNames.First(x => x.EndsWith(soundPath)));
                    if (ms != null)
                    {
                        if (ms != Stream.Null)
                        {
                            ms.Position = 0;
                            using WaveStream blockAlignedStream =
                                new BlockAlignReductionStream(
                                    WaveFormatConversionStream.CreatePcmStream(
                                        new WaveFileReader(ms)));
                            using WaveOut waveOut = new WaveOut(WaveCallbackInfo.FunctionCallback());
                            waveOut.Init(blockAlignedStream);
                            waveOut.Volume = volume;
                            waveOut.Play();
                            while (waveOut.PlaybackState == PlaybackState.Playing)
                            {
                                System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(delegate { }));
                                Thread.Sleep(20);
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            });
        }

    }
}

