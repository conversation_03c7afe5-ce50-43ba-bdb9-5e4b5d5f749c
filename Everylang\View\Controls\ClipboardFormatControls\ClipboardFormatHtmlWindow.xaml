﻿<telerik:RadWindow
    x:Class="Everylang.App.View.Controls.ClipboardFormatControls.ClipboardFormatHtmlWindow"
    x:Name="me"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:navigation="clr-namespace:Telerik.Windows.Controls.Navigation;assembly=Telerik.Windows.Controls.Navigation"
    xmlns:clipboardFormatControls="clr-namespace:Everylang.App.View.Controls.ClipboardFormatControls"
    Width="450"
    Height="350"
    Deactivated="me_Deactivated"
    ResizeMode="CanResizeWithGrip"
    CaptionHeight="10"
    navigation:RadWindowInteropHelper.AllowTransparency="False"
    Header="Html"
    CornerRadius="3"
    WindowStartupLocation="Manual" x:ClassModifier="internal">
    <telerik:RadWindow.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="clipboardFormatControls:ClipboardFormatHtmlWindow" />
        </ResourceDictionary>
    </telerik:RadWindow.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="250*" />
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" Margin="10,0,10,0">
            <Hyperlink Name="hyperlink" RequestNavigate="Hyperlink_RequestNavigate">
                <TextBlock Text="{Binding Path=LinkText, ElementName=me, Mode=Default}" />
            </Hyperlink>
        </TextBlock>
        <WebBrowser
            Grid.Row="1"
            Width="Auto"
            Name="webBrowser" Loaded="webBrowser_Loaded"/>
    </Grid>
</telerik:RadWindow>