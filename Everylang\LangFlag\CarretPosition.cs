﻿using Everylang.App.HookManager;
using System;
using System.Runtime.InteropServices;
using System.Windows;
using Vanara.PInvoke;
using Accessible = Everylang.App.Utilities.Automation.Accessible.Accessible;
using POINT = Vanara.PInvoke.POINT;

namespace Everylang.App.LangFlag
{
    internal class CarretPosition
    {
        internal static Point GetPosition()
        {
            var pos = GetPositionHandle();
            if (Math.Abs(pos.X - pos.Y) < 0.0001)
            {
                if (GlobalLangChangeHook.IsEditControl)
                {
                    pos = new Point(GlobalLangChangeHook.ControlRect.Left, GlobalLangChangeHook.ControlRect.Bottom);
                }
            }
            return pos;
        }

        private static Point GetPositionHandle()
        {
            try
            {
                var guiInfo = new User32.GUITHREADINFO();
                guiInfo.cbSize = (uint)Marshal.SizeOf(guiInfo.GetType());
                User32.GetGUIThreadInfo(0, ref guiInfo);
                if (guiInfo.hwndFocus == IntPtr.Zero || guiInfo.hwndCaret == IntPtr.Zero)
                {
                    IntPtr hWnd = guiInfo.hwndFocus.DangerousGetHandle();
                    if (hWnd == IntPtr.Zero)
                        hWnd = guiInfo.hwndActive.DangerousGetHandle();
                    return GetCaretPositionAcc(hWnd);
                }

                var point = new POINT(guiInfo.rcCaret.Left, guiInfo.rcCaret.Top);
                User32.ClientToScreen(guiInfo.hwndCaret, ref point);
                return new Point(point.X, point.Y);
            }
            catch
            {
                // ignored
            }
            return new Point(0, 0);
        }

        private static Point GetCaretPositionAcc(IntPtr hWnd)
        {
            Point location = new Point(0, 0);
            try
            {
                Accessible.FromWindow(hWnd, out var accWindow);
                Accessible.FromWindow(hWnd, out var accCaret, 0xFFFFFFF8);

                if (accWindow != null)
                {
                    _ = accWindow.Name;
                    if (accCaret != null)
                    {
                        location.X = accCaret.Location.X;
                        location.Y = accCaret.Location.Y;
                    }
                }
            }
            catch
            {
                // ignored
            }
            return location;
        }




    }
}
