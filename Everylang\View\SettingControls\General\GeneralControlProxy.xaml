﻿<UserControl x:Class="Everylang.App.View.SettingControls.General.GeneralControlProxy"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel>
            <StackPanel  Margin="20,10,0,0" Orientation="Horizontal" VerticalAlignment="Top">
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock Margin="10,0,0,0" FontSize="15" VerticalAlignment="Center" HorizontalAlignment="Left" Text="{telerik:LocalizableResource Key=GeneralSettingsProxy}" />
            </StackPanel>
            <StackPanel Margin="50,30,0,0">
                <CheckBox FontSize="14" IsChecked="{Binding Path=GeneralSettingsViewModel.IsProxyUseIE}" Focusable="False">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsIsProxyUseIE}" />
                </CheckBox>
                <StackPanel  Margin="0,15,0,0"  Orientation="Horizontal">
                    <telerik:RadWatermarkTextBox Width="180" IsEnabled="{Binding Path=GeneralSettingsViewModel.IsNotProxyUseIE}" WatermarkContent="{telerik:LocalizableResource Key=GeneralSettingsProxyServer}" Text="{Binding Path=GeneralSettingsViewModel.ProxyServer}"/>
                    <telerik:RadWatermarkTextBox Margin="15,0,0,0" Width="180" IsEnabled="{Binding Path=GeneralSettingsViewModel.IsNotProxyUseIE}" WatermarkContent="{telerik:LocalizableResource Key=GeneralSettingsProxyPort}" Text="{Binding Path=GeneralSettingsViewModel.ProxyPort}"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <telerik:RadWatermarkTextBox Width="180" IsEnabled="{Binding Path=GeneralSettingsViewModel.IsNotProxyUseIE}" WatermarkContent="{telerik:LocalizableResource Key=GeneralSettingsProxyUsername}" Text="{Binding Path=GeneralSettingsViewModel.ProxyUserName}"/>
                    <telerik:RadPasswordBox  Margin="15,0,0,0" Width="180" Name="txtPassword"  IsEnabled="{Binding Path=GeneralSettingsViewModel.IsNotProxyUseIE}" WatermarkContent="{telerik:LocalizableResource Key=GeneralSettingsProxyPassword}"/>
                </StackPanel>
                <telerik:RadButton Focusable="False"  HorizontalAlignment="Left" Margin="0,15,0,0" Content="{telerik:LocalizableResource Key=GeneralSettingsSaveProxy}" Command="{Binding Path=GeneralSettingsViewModel.SaveProxyCommand}" CommandParameter="{Binding ElementName=txtPassword}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
