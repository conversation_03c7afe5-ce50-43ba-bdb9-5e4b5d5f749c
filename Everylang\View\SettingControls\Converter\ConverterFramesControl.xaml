﻿<UserControl x:Class="Everylang.App.View.SettingControls.Converter.ConverterFramesControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" x:ClassModifier="internal">
    <Grid Margin="10,8,0,0" Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel  Margin="15,0,0,0">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Top">
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock Margin="10,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontSize="15" TextWrapping="Wrap" Text="{telerik:LocalizableResource Key=ConverterSettingsEncloseTextQuotationMarks}">
                </TextBlock>
            </StackPanel>
            <StackPanel Margin="40,20,0,0"  Orientation="Horizontal">
                <ListBox x:Name="ListBoxFrames" HorizontalAlignment="Stretch" Height="250" Width="180" Margin="0,0,10,0" SelectionChanged="ListBoxFrames_OnSelected"/>
                <StackPanel Margin="10,0,0,0">
                    <StackPanel  Orientation="Horizontal">
                        <telerik:Label Margin="0,0,0,0" Content="{telerik:LocalizableResource Key=ConverterStart}" Width="80"/>
                        <TextBox x:Name="TextBoxFrom" Margin="0,0,0,0" Width="100"/>
                    </StackPanel>
                    <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                        <telerik:Label  Content="{telerik:LocalizableResource Key=ConverterEnd}" Width="80"/>
                        <TextBox x:Name="TextBoxTo" Margin="0,0,0,0" Width="100"/>
                    </StackPanel>
                    <Button  Content="{telerik:LocalizableResource Key=ConverterAdd}" Margin="0,5,0,0" Click="AddClick"/>
                    <Button  Margin="0,5,0,0" Content="{telerik:LocalizableResource Key=ConverterDelete}" Click="DeleteClick"/>

                    <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                        <Button Click="UpClick" Width="45" Focusable="False">
                            <wpf:MaterialIcon Width="15"
                                                        Height="15"
                                                        Kind="ArrowUp" />
                        </Button>
                        <Button Margin="10,0,0,0" Click="DownClick"  Width="45"  Focusable="False">
                            <wpf:MaterialIcon Width="15"
                                                        Height="15"
                                                        Kind="ArrowDown" />
                        </Button>
                    </StackPanel>
                    
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
