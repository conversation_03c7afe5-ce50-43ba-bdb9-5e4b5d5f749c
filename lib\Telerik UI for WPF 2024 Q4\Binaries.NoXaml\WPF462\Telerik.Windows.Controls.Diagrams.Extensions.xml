<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.Diagrams.Extensions</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer">
            <summary>
            UI Automation peer class for RadGeometryButton.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer.GetHelpTextCore">
            <summary>
            Returns the string that describes the functionality of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            The help text, or <see cref="F:System.String.Empty"/> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryButtonAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer">
            <summary>
            UI Automation peer class for RadGeometryDropDownButton.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer.GetHelpTextCore">
            <summary>
            Returns the string that describes the functionality of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            The help text, or <see cref="F:System.String.Empty"/> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryDropDownButtonAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer">
            <summary>
            UI Automation peer class for RadGeometryRadioButton.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer.GetHelpTextCore">
            <summary>
            Returns the string that describes the functionality of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            The help text, or <see cref="F:System.String.Empty"/> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryRadioButtonAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer">
            <summary>
            UI Automation peer class for RadGeometryToggleButton.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer.GetHelpTextCore">
            <summary>
            Returns the string that describes the functionality of the <see cref="T:System.Windows.FrameworkElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText"/>.
            </summary>
            <returns>
            The help text, or <see cref="F:System.String.Empty"/> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGeometryToggleButtonAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer">
            <summary>
            UI Automation Peer class for RadDiagramRuler.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName" />.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramRulerAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer">
            <summary>
            UI Automation Peer class for SettingsPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.ExpandCollapseState">
            <summary>
            Gets the state (expanded or collapsed) of the control.
            </summary>
            <returns>The state (expanded or collapsed) of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.Collapse">
            <summary>
            Hides all nodes, controls, or content that are descendants of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.Expand">
            <summary>
            Displays all child nodes, controls, or content of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets a control pattern that is associated with this AutomationPeer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetClassNameCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetChildrenCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetAutomationControlTypeCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.IsEnabledCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer">
            <summary>
            UI AutomationPeer class for SettingsPaneView.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SettingsPaneViewAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer">
            <summary>
            UI AutomationPeer class for DiagramNavigationPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.ExpandCollapseState">
            <summary>
            Gets the state (expanded or collapsed) of the control.
            </summary>
            <returns>The state (expanded or collapsed) of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.Collapse">
            <summary>
            Hides all nodes, controls, or content that are descendants of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.Expand">
            <summary>
            Displays all child nodes, controls, or content of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets a control pattern that is associated with this AutomationPeer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetClassNameCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetChildrenCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetAutomationControlTypeCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.IsEnabledCore">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DiagramNavigationPaneAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer">
            <summary>
            UI AutomationPeer class for DiagramThumbnail.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName" />.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramThumbnailAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer">
            <summary>
            UI Automation peer class for RadDiagramToolBoxItem.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName" />.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxItemAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer">
            <summary>
            UI Automation peer class for RAdDiagramToolBoxGroup.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetChildrenCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName" />.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxGroupAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer">
            <summary>
            UI Automation Peer class for RadDiagramToolbox.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.#ctor(Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetNameCore">
            <summary>
            Returns the text label of the <see cref="T:System.Windows.FrameworkElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName" />.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDiagramToolboxAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons">
            <summary>
            This class provides attached properties for controlling the look and feel of the Geometry Buttons.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GeometryFillProperty">
            <summary>
            Identifies the GeometryFill dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.EllipseWidthProperty">
            <summary>
            Identifies the EllipseWidth dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.EllipseHeightProperty">
            <summary>
            Identifies the EllipseHeight dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.EllipseVisibilityProperty">
            <summary>
            Identifies the EllipseVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GeometryStretchProperty">
            <summary>
            Identifies the GeometryStretch dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GeometryVisibilityProperty">
            <summary>
            Identifies the GeometryVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GeometryStrokeProperty">
            <summary>
            Identifies the GeometryStroke dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GeometryStrokeThicknessProperty">
            <summary>
            Identifies the GeometryStrokeThickness dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetGeometryFill(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry fill attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetGeometryFill(System.Windows.DependencyObject,System.Windows.Media.Brush)">
            <summary>
            Sets the geometry fill attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetEllipseWidth(System.Windows.DependencyObject)">
            <summary>
            Gets the width of the ellipse attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetEllipseWidth(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the width of the ellipse attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetEllipseHeight(System.Windows.DependencyObject)">
            <summary>
            Gets the height of the ellipse attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetEllipseHeight(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the height of the ellipse attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetEllipseVisibility(System.Windows.DependencyObject)">
            <summary>
            Gets the ellipse visibility attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetEllipseVisibility(System.Windows.DependencyObject,System.Windows.Visibility)">
            <summary>
            Sets the ellipse visibility attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetGeometryStretch(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry stretch attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetGeometryStretch(System.Windows.DependencyObject,System.Windows.Media.Stretch)">
            <summary>
            Sets the geometry stretch attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetGeometryVisibility(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry visibility attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetGeometryVisibility(System.Windows.DependencyObject,System.Windows.Visibility)">
            <summary>
            Sets the geometry visibility attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetGeometryStroke(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry stroke attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetGeometryStroke(System.Windows.DependencyObject,System.Windows.Media.Brush)">
            <summary>
            Sets the geometry stoke attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.GetGeometryStrokeThickness(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry stroke thickness attached property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GeometryButtons.SetGeometryStrokeThickness(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the geometry stroke thickness attached property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton">
            <summary>
            Represents a Geometry RadioButton Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton.GeometryProperty">
            <summary>
            Registers the Geometry dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton.Geometry">
            <summary>
            Gets or sets the geometry.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryRadioButton.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontStyleConverter">
            <summary>
            A converter which takes a boolean parameter and returns a FontStyle value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontStyleConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontStyleConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontWeightConverter">
            <summary>
            A converter which takes a boolean parameter and returns a FontWeight value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontWeightConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.BooleanToFontWeightConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DoubleToSizeConverter">
            <summary>
            A converter which takes a double parameter and returns a Size value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DoubleToSizeConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DoubleToSizeConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PageSizeNameConverter">
            <summary>
            Converter from <see cref="T:System.Printing.PageMediaSize" /> to its name-size representation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PageSizeNameConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts PageMediaSize to formatted string including name and size of the paper in inches.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PageSizeNameConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PercentageConverter">
            <summary>
            A converter which takes a double parameter and returns the value multiplied by 100.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PercentageConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PercentageConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ReciprocalDoubleConverter">
            <summary>
            Converts double to its reciprocal value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ReciprocalDoubleConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ReciprocalDoubleConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ThicknessToDoubleConverter">
            <summary>
            Converts Thickness to Double.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ThicknessToDoubleConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a Thickness to double only if Top==Right==Left==Bottom.
            </summary>	
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ThicknessToDoubleConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.PrintSettingsChanged">
            <summary>
            Occurs when <see cref="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPageOrientation"/> or <see cref="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPageSize"/> property changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.PrintQueues">
            <summary>
            Gets the print queues.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPrintQueue">
            <summary>
            Gets or sets the selected print queue.
            </summary>
            <value>
            The selected print queue.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.PageOrientations">
            <summary>
            Gets the page orientations.
            </summary>
            <value>
            The page orientations.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.DiagramPrintPositions">
            <summary>
            Gets all available diagram print positions over the printing pages.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPageOrientation">
            <summary>
            Gets or sets the selected page orientation.
            </summary>
            <value>
            The selected page orientation.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.PageSizes">
            <summary>
            Gets the page sizes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPageSize">
            <summary>
            Gets or sets the selected page size.
            </summary>
            <value>
            The selected page size.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.ActualSelectedPageSize">
            <summary>
            Gets the actual size of the selected page taking into account the page orientation.
            </summary>
            <value>
            The actual size of the selected page.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.GetPrintTicket">
            <summary>
            Gets the print ticket.
            </summary>
            <returns>The print ticket.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.MergeAndValidatePrintTicket(System.Printing.PrintTicket,System.Printing.PrintTicket)">
            <summary>
            Merge and validate the tickets using the <see cref="P:Telerik.Windows.Controls.Diagrams.Extensions.PrintSettingsViewModel.SelectedPrintQueue"/>.
            </summary>
            <param name="originalTicket">The original ticket.</param>
            <param name="modificationTicket">The modification ticket.</param>
            <returns>Merged print ticket.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview">
            <summary>
            Visual control used to display how the diagram will be paginated before printing.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PreviewPrintEvent">
            <summary>
            Identifies the PreviewPrint routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintedEvent">
            <summary>
            Identifies the Printed routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.ScaleDownFactorProperty">
            <summary>
            Identifies the ScaleDownFactor dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintContentMarginProperty">
            <summary>
            Identifies the PrintContentMargin dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.DpiProperty">
            <summary>
            Identifies the Dpi dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintScaleFactorProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintScaleFactor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.ItemStyleProperty">
            <summary>
            Identifies the ItemStyle dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PreviewPrint">
            <summary>
            Occurs before diagram printing starts.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.Printed">
            <summary>
            Occurs after diagram printing is completed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.Diagram">
            <summary>
            Gets or sets the diagram.
            </summary>
            <value>The diagram.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.ScaleDownFactor">
            <summary>
            Gets or sets the scale down factor.
            </summary>
            <value>The scale down factor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintContentMargin">
            <summary>
            Gets or sets the margin for every page.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.Dpi">
            <summary>
            Gets or sets the resolution used during print.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.ItemStyle">
            <summary>
            Gets or sets the print preview item style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PagesInfo">
            <summary>
            Gets the pages info.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.HasPrintErrors">
            <summary>
            Checked whether there are print errors.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.PrintScaleFactor">
            <summary>
            Gets or sets the scale factor.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.Print(System.String)">
            <summary>
            Prints the associated diagram.
            </summary>
            <param name="title">Title used in the print documents queue.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.SetBestFitScaleFactor(System.Int32,System.Double,Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem)">
            <summary>
            Aims to fit the print preview pages in the current print preview dialog's viewport.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreview.DoPrint(System.String)">
            <summary>
            Prints the images in Pages property. Selected printer and settings are used during the print process.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog">
            <summary>
            Dialog control used to display how the diagram will be paginated before printing. Contains a print preview inside.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog.Diagram">
            <summary>
            Gets or sets the diagram.
            </summary>
            <value>The diagram.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewDialog.OnPrintedOverride(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            Called when print is completed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem">
            <summary>
            Visualizes a decorated image of print preview page.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageSourceProperty">
            <summary>
            Identifies the PagePreviewImageSource dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageHeightProperty">
            <summary>
            Identifies the PagePreviewImageHeight dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageWidthProperty">
            <summary>
            Identifies the PagePreviewImageWidth dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageSource">
            <summary>
            Gets or sets the page preview image source.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageHeight">
            <summary>
            Gets or sets the page preview image height.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.PagePreviewImageWidth">
            <summary>
            Gets or sets the page preview image width.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.ApplyScaleToImage">
            <summary>
            Down scale is applied to print preview image instead of making snapshot with upscaled size in order to downscale diagram image.
            Upscaling is performed with increasing print pages.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramPrintPreviewItem.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionCommands">
            <summary>
            Contains extension diagram commands.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionCommands.SketchCommand">
            <summary>
            Gets the sketch command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionCommands.Print">
            <summary>
            Gets the open print dialog command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties">
            <summary>
            Static class for additional customization of Diagram Items behavior.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.ShouldDetachConnectionsProperty">
            <summary>
            Identifies the ShouldDetachConnections attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.HasImageShapesProperty">
            <summary>
            Identifies the HasImageShapes attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.PendingCommandProperty">
            <summary>
            Identifies the PendingCommand attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.GetHasImageShapes(System.Windows.DependencyObject)">
            <summary>
            Gets the has image shapes.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.SetHasImageShapes(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the has image shapes.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">If set to <c>true</c> [value].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.GetShouldDetachConnections(System.Windows.DependencyObject)">
            <summary>
            Gets the ShouldDetachConnections attached property.
            <list type="bullet">
            <item> If set to <c>true</c> connections will be detached when rotated or dragged in multi selection.</item>
            </list>
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DiagramExtensionProperties.SetShouldDetachConnections(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the ShouldDetachConnections attached property. 
            <list type="bullet">
            <item> If set to <c>true</c> connections will be detached when rotated or dragged in multi selection.</item>
            </list>
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.IGeometryButton">
            <summary>
            Represents the IGeometryButton interface.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.IGeometryButton.Geometry">
            <summary>
            Gets or sets the geometry.
            </summary>
            <value>The geometry.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton">
            <summary>
            Represents a Geometry Button Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton.GeometryProperty">
            <summary>
            Registers the Geometry dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton.Geometry">
            <summary>
            Gets or sets the geometry.
            </summary>
            <value>
            The geometry.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryButton.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton">
            <summary>
            Represents a Geometry DropDown Button Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.GeometryProperty">
            <summary>
            Registers the Geometry dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.DropDownPopupHorizontalOffsetProperty">
            <summary>
            Registers the DropDownPopupHorizontalOffset dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.DropDownPopupVerticalOffsetProperty">
            <summary>
            Registers the DropDownPopupVerticalOffset dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.CloseOnClickProperty">
            <summary>
            Registers the CloseOnClick dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.ShouldCenterPopupProperty">
            <summary>
            Registers the ShouldCenterPopup dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.Geometry">
            <summary>
            Gets or sets the geometry.
            </summary>
            <value>
            The geometry.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.DropDownPopupHorizontalOffset">
            <summary>
            Gets or sets the horizontal offset of the dropdown popup.
            </summary>
            <value>The horizontal offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.DropDownPopupVerticalOffset">
            <summary>
            Gets or sets the vertical offset of the dropdown popup.
            </summary>
            <value>The vertical offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.CloseOnClick">
            <summary>
            Gets or sets whether the drop down popup should be closed on click inside it.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.ShouldCenterPopup">
            <summary>
            Gets or sets whether the drop down popup should be centered.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:Initialized"/> event.
            </summary>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryDropDownButton.HidePopupOnChildItemActivated(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            Hides the Popup when one of observed Children is activated.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton">
            <summary>
            Represents a Geometry ToggleButton Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton.GeometryProperty">
            <summary>
            Registers the Geometry dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton.Geometry">
            <summary>
            Gets or sets the geometry.
            </summary>
            <value>
            The geometry.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadGeometryToggleButton.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGraphSourceConverter">
            <summary>
            A converter which takes a hierarchical collection and creates a GraphSource.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGraphSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGraphSourceConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.FileLocation">
            <summary>
            Enumerates the storage targets.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.FileLocation.Disk">
            <summary>
            The local user disk.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.FileLocation.IsolatedStorage">
            <summary>
            The application's isolated storage.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.FileManager">
            <summary>
            File management utility geared towards RadDiagram.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.FileManager.#ctor(Telerik.Windows.Controls.RadDiagram)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.FileManager"/> class.
            </summary>
            <param name="diagram">The diagram.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.FileManager.CurrentFile">
            <summary>
            Gets or sets the current file path.
            </summary>
            <value>
            The current file.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.FileManager.SaveToFile(Telerik.Windows.Controls.Diagrams.Extensions.FileLocation)">
            <summary>
            Saves the diagram to file.
            </summary>
            <param name="location">The location.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.FileManager.LoadFromFile(Telerik.Windows.Controls.Diagrams.Extensions.FileLocation,System.Action)">
            <summary>
            Loads the diagram from file.
            </summary>
            <param name="location">Specifies the type location of the file to open - file system or isolated storage.</param>
            <param name="previewActionOnLoad">Action to be executed first on OK button click of the <see cref="T:Microsoft.Win32.OpenFileDialog"/></param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Properties.ExceptionResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Properties.ExceptionResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Properties.ExceptionResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Properties.ExceptionResources.DiagramControlPropertyNotSet">
            <summary>
              Looks up a localized string similar to DiagramControl property is not set..
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramSketchShape">
            <summary>
            Represents a Sketchy type of DiagramShape.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramSketchShape.Serialize">
            <summary>
            Serializes this shape.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramSketchShape.Deserialize(Telerik.Windows.Diagrams.Core.SerializationInfo)">
            <summary>
            Deserializes the specified info.
            </summary>
            <param name="info">The info.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition">
            <summary>
            Represents a description of scale in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition.MaxZoom">
            <summary>
            Gets or sets the max zoom.
            </summary>
            <value>
            The max zoom.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition.ItemDefinitions">
            <summary>
            Gets the scale item definitions.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinitionCollection">
            <summary>
            Represents a collection containing <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition"/> elements.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemDefinition">
            <summary>
            Represents a description of item in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemDefinition.Interval">
            <summary>
            Gets or sets the interval.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemDefinition.Type">
            <summary>
            Gets or sets the type of the item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType">
            <summary>
            Represents the type of <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> tick item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.XSmallTick">
            <summary>
            Represents x-small tick.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.SmallTick">
            <summary>
            Represents small tick.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.MediumTick">
            <summary>
            Represents medium tick.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.LargeTick">
            <summary>
            Represents large tick.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.Label">
            <summary>
            Represents a label.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.Custom">
            <summary>
            Represents custom tick.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleItemType.Undefined">
            <summary>
            Represents undefined tick or label.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit">
            <summary>
            Enumeration of the supported measurement units.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit.Dip">
            <summary>
            Device independent pixel.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit.Cm">
            <summary>
            Centimeter.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit.Inch">
            <summary>
            Inch.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter">
            <summary>
            Converts between inches, centimeters and DPIs (device independent pixel).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.InchToDip(System.Double)">
            <summary>
            Converts inches to DIPs (Device Independent Pixel).
            </summary>
            <param name="inch">An inch value.</param>
            <returns>A Dip value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.DipToInch(System.Double)">
            <summary>
            Converts DIPs (Device Independent Pixel) to inches.
            </summary>
            <param name="dip">An inch value.</param>
            <returns>A Dip value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.CmToDip(System.Double)">
            <summary>
            Converts centimeters to DIPs (Device Independent Pixel).
            </summary>
            <param name="cm">A centimeter value.</param>
            <returns>A Dip value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.DipToCm(System.Double)">
            <summary>
            Converts DIPs (Device Independent Pixel) to centimeters.
            </summary>
            <param name="dip">A Dip value.</param>
            <returns>A centimeter value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.FromDip(System.Double,Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit)">
            <summary>
            Converts DIPs (Device Independent Pixel) to value in provided measurement unit.
            </summary>
            <param name="dipValue">A Dip value.</param>
            <param name="measurementUnit">A measurement unit.</param>
            <returns>A value in the provided measurement unit.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnitConverter.ToDip(System.Double,Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit)">
            <summary>
            Converts value in provided measurement unit to DIPs (Device Independent Pixel).
            </summary>
            <param name="unitValue">A value.</param>
            <param name="measurementUnit">A measurement unit.</param>
            <returns>A Dip value.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler">
            <summary>
            Represents ruler for <see cref="T:Telerik.Windows.Controls.RadDiagram"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.PlacementProperty">
            <summary>
            Identifies the Placement dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MeasurementUnitProperty">
            <summary>
            Identifies the MeasurementUnit dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.StartOffsetProperty">
            <summary>
            Identifies the StartOffset dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MouseIndicatorVisibilityProperty">
            <summary>
            Identifies the MouseIndicatorVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MouseIndicatorStyleProperty">
            <summary>
            Identifies the MouseIndicatorStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.ScaleDefinitionsProperty">
            <summary>
            Identifies the ScaleDefinitions dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.Diagram">
            <summary>
            Gets or sets the diagram.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.Placement">
            <summary>
            Gets or sets the placement.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MeasurementUnit">
            <summary>
            Gets or sets the measurement unit.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.StartOffset">
            <summary>
            Gets or sets start offset for the scale (ticks).
            Provided value is summed with the beginning of Diagram viewport and result is used as a start for the ruler scale.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MouseIndicatorVisibility">
            <summary>
            Gets or sets the visibility of mouse position indicator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.MouseIndicatorStyle">
            <summary>
            Gets or sets the style of mouse position indicator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.ScaleDefinitions">
            <summary>
            Gets or sets the scale definitions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.Refresh">
            <summary>
            Forces full refresh of the ruler (ticks and labels are recreated).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes
            (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            In simplest terms, this means the method is called just before a UI element displays in an application.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.GetContainerTypeForItemOverride(Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem)">
            <summary>
            Gets the container type for item.
            </summary>
            <param name="scaleItem">The scale data item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler.OnCreateAutomationPeer">
            <summary>
            Returns class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> implementations for the Windows Presentation Foundation (WPF) infrastructure.
            </summary>
            <returns>
            The type-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> implementation.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RulerScale">
            <summary>
            Represents the data model for scale in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem">
            <summary>
            Represents the data model for item in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem.Equals(Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            True if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerScaleItem.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier">
            <summary>
            Represent a type descriptor about <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> item container.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.FromType``1">
            <summary>
            Creates an instance of type TContainer/>.
            </summary>
            <typeparam name="TContainer">The type of the container.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.op_Equality(Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier,Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.op_Inequality(Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier,Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.Create">
            <summary>
            Creates an instance of type declared in the <see cref="T:System.Type"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.Equals(Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            True if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ContainerTypeIdentifier.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel">
            <summary>
            Represent a panel for <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> supporting UI virtualization of the ticks.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel.Refresh(System.Windows.Rect,System.Double,System.Windows.Controls.Dock,Telerik.Windows.Controls.Diagrams.Extensions.MeasurementUnit,System.Collections.Generic.IEnumerable{Telerik.Windows.Controls.Diagrams.Extensions.DiagramScaleDefinition})">
            <summary>
            Refreshes the scale.
            </summary>
            <param name="viewport">The viewport.</param>
            <param name="zoom">The zoom.</param>
            <param name="placement">The placement.</param>
            <param name="measurementUnit">The measurement unit.</param>
            <param name="scaleDefinitions">The scale definitions.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel.MeasureOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the Measure pass of Silverlight layout.
            Classes can override this method to define their own Measure pass behavior.
            </summary>
            <param name="availableSize">The available size that this object can give to child objects.
            Infinity (<see cref="F:System.Double.PositiveInfinity"/>) can be specified as a value
            to indicate that the object will size to whatever content is available.</param>
            <returns>
            The size that this object determines it needs during layout,
            based on its calculations of the allocated sizes for child objects;
            or based on other considerations, such as a fixed container size.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RulerVirtualizingPanel.ArrangeOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the Arrange pass of Silverlight layout.
            Classes can override this method to define their own Arrange pass behavior.
            </summary>
            <param name="finalSize">The final area within the parent
            that this object should use to arrange itself and its children.</param>
            <returns>
            The actual size that is used after the element is arranged in layout.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.IRulerItemContainer">
            <summary>
            Represent visual container containing data for <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/> item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.IRulerItemContainer.DataItem">
            <summary>
            Gets or sets the data item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer">
            <summary>
            Represent a visual container for labels in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.Telerik#Windows#Controls#Diagrams#Extensions#IRulerItemContainer#DataItem">
            <summary>
            Gets or sets the data item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes
            (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            In simplest terms, this means the method is called just before a UI element displays in an application.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.ArrangeOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the Arrange pass of Silverlight layout.
            Classes can override this method to define their own Arrange pass behavior.
            </summary>
            <param name="finalSize">The final area within the parent that this object should use to arrange itself and its children.</param>
            <returns>
            The actual size that is used after the element is arranged in layout.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.LabelContainerDataContextProxy">
            <summary>
            Represent a wrapper around the DataContext property of <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer"/> object.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.LabelContainer.LabelContainerDataContextProxy.Value">
            <summary>
            Gets or sets the value.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.LargeTickContainer">
            <summary>
            Represent a visual container for large ticks in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.LargeTickContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.MediumTickContainer">
            <summary>
            Represent a visual container for medium large ticks in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.MediumTickContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SmallTickContainer">
            <summary>
            Represent a visual container for medium small ticks in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SmallTickContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.XSmallTickContainer">
            <summary>
            Represent a visual container for small ticks in the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramRuler"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.XSmallTickContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.IMultiValueProvider">
            <summary>
            Interface for objects storing multiple values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.IMultiValueProvider.GetValues">
            <summary>
            Gets the values.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle">
            <summary>
            Represents a class storing Fill, Brush and particular Order. Used in SettingsPane ColorSelectors.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.OrderId">
            <summary>
            Gets or sets the order id.
            </summary>
            <value>The order id.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.Fill">
            <summary>
            Gets or sets the fill.
            </summary>
            <value>The fill.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.Stroke">
            <summary>
            Gets or sets the stroke.
            </summary>
            <value>The stroke.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.GetValues">
            <summary>
            Gets the values.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ColorStyle.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeDashArrayExtension">
            <summary>
            StrokeDashArray MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeDashArrayExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeDashArrayExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeDashArrayExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeThicknessExtension">
            <summary>
            StrokeThickness MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeThicknessExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeThicknessExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.StrokeThicknessExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.GradientColorsExtension">
            <summary>
            GradientColors MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.GradientColorsExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.GradientColorsExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.GradientColorsExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.SolidColorsExtension">
            <summary>
            SolidColors MarkupExtension. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.SolidColorsExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.SolidColorsExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.SolidColorsExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontColorsExtension">
            <summary>
            FontColors MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontColorsExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontColorsExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontColorsExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontSizeExtension">
            <summary>
            FontSize MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontSizeExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontSizeExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontSizeExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontFamilyExtension">
            <summary>
            FontFamily MarkupExtension.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontFamilyExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontFamilyExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.FontFamilyExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.CapTypeExtension">
            <summary>
            Represents a custom markup extension for creating connection caps.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.CapTypeExtension.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Data.CapTypeExtension"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Data.CapTypeExtension.ProvideValue(System.IServiceProvider)">
            <summary>
            When implemented in a derived class, returns an object that is set as the value of the target property for this markup extension.
            </summary>
            <param name="serviceProvider">Object that can provide services for the markup extension.</param>
            <returns>
            The object value to set on the property where the extension is applied.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.EditItemTypeTypeConverter">
            <summary>
            Represents a type converter for the EditItemType enumeration.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.EditItemTypeTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.EditItemTypeTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase">
            <summary>
            Represents a base class for the SettingsPane and SettingsPaneView.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.IsActiveProperty">
            <summary>
            Identifies the IsActive dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.Diagram">
            <summary>
            Gets or sets the diagram control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.IsActive">
            <summary>
            Gets or sets a value indicating whether this instance is active.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.OnIsActivePropertyChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when is active property is changed.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.OnDiagramPropertyChanged(Telerik.Windows.Controls.RadDiagram,Telerik.Windows.Controls.RadDiagram)">
            <summary>
            Called when diagram control property is changed.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.OnDiagramSelectionChanged(System.Object,System.Windows.Controls.SelectionChangedEventArgs)">
            <summary>
            Called when diagram control selection is changed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.Controls.SelectionChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.OnDiagramAdditionalContentActivated(System.Object,Telerik.Windows.Controls.Diagrams.AdditionalContentActivatedEventArgs)">
            <summary>
            Called when diagram addition control is activated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneBase.OnDiagramSelectionBoundsChanged(System.Object,System.EventArgs)">
            <summary>
            Called when diagram selection bounds is changed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType">
            <summary>
            Represents the type of the objects on which the Settings Pane Editor Control work.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType.Shapes">
            <summary>
            Shapes only.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType.Connections">
            <summary>
            Connections only.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType.Custom">
            <summary>
            Custom type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.EditItemType.All">
            <summary>
            All types.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ContainerEditProperty">
            <summary>
            Represents a property determining a SettingsPane inner Control's enabled state or visibility.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ContainerEditProperty.IsEnabled">
            <summary>
            When set the inner control will be disabled.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ContainerEditProperty.Visibility">
            <summary>
            When set the inner control will be collapse.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper">
            <summary>
            Utility to scan properties through reflection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.SetProperty(System.String,System.Object,System.Object)">
            <summary>
            Sets the value of the property.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="source">The source.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.SetPropertyViaPropertyPath(System.String,Telerik.Windows.Diagrams.Core.IDiagramItem,System.Object)">
            <summary>
            Sets property of the given IDiagramItem. The path to the property is given in the propertyPath string parameter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.GetValue(System.String,System.Object)">
            <summary>
            Gets the value of the property.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.GetCoercedValue(System.String,System.Object)">
            <summary>
            Gets the coerced value.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.GetValueByPropertyPath(System.String,System.Object,System.Boolean@)">
            <summary>
            Gets the value of property specified by a propertyPath string.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.ConvertValue(System.Type,System.Object)">
            <summary>
            Converts the value.
            </summary>
            <param name="targetType">Type of the target.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PropertyHelper.GetDefaultValue(System.Type)">
            <summary>
            Gets the default value.
            </summary>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView">
            <summary>
            Represents the SettingsPaneView Control.
            </summary>
            <summary>
            Represents the SettingsPaneView Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.EditorPropertyNameProperty">
            <summary>
            Identifies the EditorPropertyName dependency property. It indicates the property of the shape/connection/container shape which will be 
            affected by the value of the EditorValue property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.EditorFallbackPropertyNameProperty">
            <summary>
            Identifies the EditorFallbackPropertyName dependency property.
            </summary>
            <remarks>
            If set, the editor will get its default or fallback value from this property instead of the SettingsPaneView.EditorPropertyName.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.EditorValueProperty">
            <summary>
            Identifies the EditorValue dependency property. It indicates which property of the editor will be used for editing the shape/connection/container shape.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.EditorTypeItemProperty">
            <summary>
            Identifies the EditorItemType dependency property.
            </summary>
            <remarks>
            Identifies what type of items can the editor control affect - shapes/connections/custom or both of them.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.ContainerEditPropertyProperty">
            <summary>
            Identifies the ContainerEditProperty attached property.
            </summary>
            <remarks>
            It is always used in combination with the ContainerEditItemType property.
            Identifies whether the IsEnabled or Visibility property of the container should be changed based on the ContainerEditItemType 
            that is set and the currently edited object - shape / connection.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.ContainerEditItemTypeProperty">
            <summary>
            Identifies the ContainerEditType attached property.
            </summary>
            <remarks>
            It is always used in combination with ContainerEditProperty property.
            When the SettingsPane is used on the type set by the ContainerEditType the property set by the ContainerEditProperty 
            is activated (on the container that is used).
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.IsRadioButtonPanelProperty">
            <summary>
            Identifies the IsRadioButtonPanel attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.RadioButtonValueProperty">
            <summary>
            Identifies the RadioButtonValue attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetEditorFallbackPropertyName(System.Windows.DependencyObject)">
            <summary>
            Gets the EditorFallbackPropertyName property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetEditorFallbackPropertyName(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the EditorFallbackPropertyName property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetContainerEditProperty(System.Windows.DependencyObject)">
            <summary>
            Gets the container property.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetContainerEditProperty(System.Windows.DependencyObject,Telerik.Windows.Controls.Diagrams.Extensions.ContainerEditProperty)">
            <summary>
            Sets the container property.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetContainerEditItemType(System.Windows.DependencyObject)">
            <summary>
            Gets the type of the container edit.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetContainerEditItemType(System.Windows.DependencyObject,Telerik.Windows.Controls.Diagrams.Extensions.EditItemType)">
            <summary>
            Sets the type of the container edit.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetEditorItemType(System.Windows.DependencyObject)">
            <summary>
            Gets the EditorItemType property. Identifies what type of items can the editor control affect - shapes/connections/custom or both of them.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetEditorItemType(System.Windows.DependencyObject,Telerik.Windows.Controls.Diagrams.Extensions.EditItemType)">
            <summary>
            Sets the EditorItemType property. Identifies what type of items can the editor control affect - shapes/connections/custom or both of them.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetEditorPropertyName(System.Windows.DependencyObject)">
            <summary>
            Gets the EditorPropertyName property. It indicates the property of the shape/connection/container shape which will be 
            affected by the value of the EditorValue property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetEditorPropertyName(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the EditorPropertyName property. It indicates the property of the shape/connection/container shape which will be 
            affected by the value of the EditorValue property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetEditorValue(System.Windows.DependencyObject)">
            <summary>
            Gets the EditorValue property. It indicates which property of the editor will be used for editing the shape/connection/container shape.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetEditorValue(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the EditorValue property. It indicates which property of the editor will be used for editing the shape/connection/container shape.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetIsRadioButtonPanel(System.Windows.DependencyObject)">
            <summary>
            Gets the IsToggleButtonPanel value.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetIsRadioButtonPanel(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the IsToggleButtonPanel value.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">If set to <c>true</c> [value].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.GetRadioButtonValue(System.Windows.DependencyObject)">
            <summary>
            Gets the RadioButtonValue.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.SetRadioButtonValue(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the RadioButtonValue.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.CurrentEditTypeProperty">
            <summary>
            The type of the edited objects - shapes/connections or both.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.RegisterEditorEvent">
            <summary>
            This event fires when a control that edits a property of a shape/connection is loaded.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.RegisterContainerEvent">
            <summary>
            This event fires when a control that edits a property of a shape/connection is loaded.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.RegisterEditor">
            <summary>
            Occurs when value editor is registered.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.RegisterContainer">
            <summary>
            Occurs when container is registered.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.ContextItems">
            <summary>
            Gets or sets the context items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.CurrentEditType">
            <summary>
            Gets or sets the type of the current edit type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnValueChanged(System.String,System.Object,Telerik.Windows.Controls.Diagrams.Extensions.EditItemType,System.Object)">
            <summary>
            Called when [value changed].
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="newValue">The new value.</param>
            <param name="editItemType">Type of the edit item.</param>
            <param name="editor">The editor.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnIsActivePropertyChanged(System.Boolean,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnDiagramAdditionalContentActivated(System.Object,Telerik.Windows.Controls.Diagrams.AdditionalContentActivatedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnDiagramSelectionChanged(System.Object,System.Windows.Controls.SelectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneView.ResolveCurrentItemType">
            <summary>
            When overridden, resolves the CurrentItemType property value.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane">
            <summary>
            Represents the SettingsPane Control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewWidthProperty">
            <summary>
            Identifies the SettingsPaneViewWidth dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewHeightProperty">
            <summary>
            Identifies the SettingsPaneViewHeight dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewStyleProperty">
            <summary>
            Identifies the SettingsPaneViewStyle dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewHeight">
            <summary>
            Gets or sets the height of the settings pane view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewWidth">
            <summary>
            Gets or sets the width of the settings pane view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.SettingsPaneViewStyle">
            <summary>
            Styles the SettingsPaneView.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnMouseWheel(System.Windows.Input.MouseWheelEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnIsActivePropertyChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when [is active property changed].
            </summary>
            <param name="oldValue">If set to <c>true</c> [old value].</param>
            <param name="newValue">If set to <c>true</c> [new value].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnDiagramSelectionChanged(System.Object,System.Windows.Controls.SelectionChangedEventArgs)">
            <summary>
            Called when [diagram control selection changed].
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.Controls.SelectionChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnDiagramAdditionalContentActivated(System.Object,Telerik.Windows.Controls.Diagrams.AdditionalContentActivatedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnDiagramPropertyChanged(Telerik.Windows.Controls.RadDiagram,Telerik.Windows.Controls.RadDiagram)">
            <summary>
            Called when diagram control property is changed.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPane.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneHomeControl">
            <summary>
            SettingsPaneHomeControl represents the Content of the Home Tab in the SettingsPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneHomeControl.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneSizeControl">
            <summary>
            SettingsPaneSizeControl represents the Content of the Size Tab in the SettingsPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneSizeControl.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneStyleControl">
            <summary>
            SettingsPaneStyleControl represents the Content of the Style Tab in SettingsPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneStyleControl.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneStyleControl.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneTextControl">
            <summary>
            SettingsPaneTextControl represents the Content of the Text Tab in the SettingsPane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SettingsPaneTextControl.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect">
            <summary>
            Represents the SketchGeometry effect.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.IsSketchyProperty">
            <summary>
            Identifies the IsSketchy attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.SketchGeometryProperty">
            <summary>
            Identifies the SketchGeometry attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.GetIsSketchy(System.Windows.DependencyObject)">
            <summary>
            Gets the is sketchy.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.SetIsSketchy(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the is sketchy.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">If set to <c>true</c> [value].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.GetSketchGeometry(System.Windows.DependencyObject)">
            <summary>
            Gets the sketch geometry.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchGeometryEffect.SetSketchGeometry(System.Windows.DependencyObject,System.Windows.Media.Geometry)">
            <summary>
            Sets the sketch geometry that will be used if provided when the shape is sketched.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper">
            <summary>
            Utility related to path segments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.GetLastPoint(System.Windows.Media.PathSegment)">
            <summary>
            Gets the last point.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.GetPoint(System.Windows.Media.PathSegment,System.Int32)">
            <summary>
            Gets the point.
            </summary>
            <param name="segment">The segment.</param>
            <param name="index">The index.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.CreatePolyBezierSegment(System.Collections.Generic.IList{System.Windows.Point},System.Int32,System.Int32)">
            <summary>
            Creates the poly bezier segment.
            </summary>
            <param name="points">The points.</param>
            <param name="start">The start.</param>
            <param name="count">The count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.CreateLineSegment(System.Windows.Point)">
            <summary>
            Creates the line segment.
            </summary>
            <param name="point">The point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.CreateBezierSegment(System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
            Creates the bezier segment.
            </summary>
            <param name="point1">The point1.</param>
            <param name="point2">The point2.</param>
            <param name="point3">The point3.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.ArcToBezierSegments(System.Windows.Media.ArcSegment,System.Windows.Point)">
            <summary>
            Arcs to bezier segments.
            </summary>
            <param name="arcSegment">The arc segment.</param>
            <param name="startPoint">The start point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.GetSimpleSegments(System.Windows.Media.PathSegment,System.Windows.Point)">
            <summary>
            Gets the simple segments.
            </summary>
            <param name="segment">The segment.</param>
            <param name="start">The start.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentHelper.SyncPolylineSegment(System.Windows.Media.PathSegmentCollection,System.Int32,System.Collections.Generic.IList{System.Windows.Point},System.Int32,System.Int32)">
            <summary>
            Syncs the polyline segment.
            </summary>
            <param name="collection">The collection.</param>
            <param name="index">The index.</param>
            <param name="points">The points.</param>
            <param name="start">The start.</param>
            <param name="count">The count.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PolylineHelper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylineHelper.GetWrappedPolylines(System.Collections.Generic.IList{Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo},System.Double@)">
            <summary>
            Gets the wrapped polylines.
            </summary>
            <param name="lines">The lines.</param>
            <param name="startArcLength">Start length of the arc.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylineHelper.PathMarch(Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo,System.Double,System.Double,System.Func{Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition,System.Double})">
            <summary>
            Path march.
            </summary>
            <param name="polyline">The polyline.</param>
            <param name="startArcLength">Start length of the arc.</param>
            <param name="cornerThreshold">The corner threshold.</param>
            <param name="stopCallback">The stop callback.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RandomEngine">
            <summary>
            Represents a random engine.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RandomEngine.#ctor(System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RandomEngine"/> class.
            </summary>
            <param name="seed">The seed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RandomEngine.NextGaussian(System.Double,System.Double)">
            <summary>
            Returns the next gaussian value.
            </summary>
            <param name="mean">The mean.</param>
            <param name="variance">The variance.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RandomEngine.NextUniform(System.Double,System.Double)">
            <summary>
            Returns the uniform value.
            </summary>
            <param name="min">The min.</param>
            <param name="max">The max.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentInfo">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentInfo.#ctor(System.Windows.Point,System.Windows.Media.PathSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentInfo"/> class.
            </summary>
            <param name="startPoint">The start point.</param>
            <param name="pathSegment">The path segment.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentInfo.PathSegment">
            <summary>
            Gets the path segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PathSegmentInfo.StartPoint">
            <summary>
            Gets the start point.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.#ctor(System.Collections.Generic.IList{System.Windows.Point})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo"/> class.
            </summary>
            <param name="points">The points.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.AccumulatedLength">
            <summary>
            Gets the length of the accumulated.
            </summary>
            <value>
            The length of the accumulated.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Angles">
            <summary>
            Gets the angles.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Count">
            <summary>
            Gets the count.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.IsClosed">
            <summary>
            Gets a value indicating whether this instance is closed.
            </summary>
            <value>
              <c>true</c> if this instance is closed; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Lengths">
            <summary>
            Gets the lengths.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Normals">
            <summary>
            Gets the normal vectors.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Points">
            <summary>
            Gets the points.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.TotalLength">
            <summary>
            Gets the total length.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.Difference(System.Int32)">
            <summary>
            Differences the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo.SmoothNormal(System.Int32,System.Double,System.Double)">
            <summary>
            Smooth the normal.
            </summary>
            <param name="index">The index.</param>
            <param name="fraction">The fraction.</param>
            <param name="cornerRadius">The corner radius.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PolylineStopPositionType">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.PolylineStopPositionType.CompleteStep">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.PolylineStopPositionType.CompletePolyline">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.PolylineStopPositionType.CornerPoint">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.After">
            <summary>
            Gets the after.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Before">
            <summary>
            Gets the before.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Index">
            <summary>
            Gets the index.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Ratio">
            <summary>
            Gets the ratio.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Reason">
            <summary>
            Gets the reason.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Remain">
            <summary>
            Gets the remain.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.Create(Telerik.Windows.Controls.Diagrams.Extensions.PolylineStopPositionType,System.Int32,System.Double,System.Double,System.Double)">
            <summary>
            Creates the specified reason.
            </summary>
            <param name="reason">The reason.</param>
            <param name="index">The index.</param>
            <param name="before">The before.</param>
            <param name="after">The after.</param>
            <param name="remain">The remain.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.GetArcLength(System.Collections.Generic.IList{System.Double})">
            <summary>
            Gets the length of the arc.
            </summary>
            <param name="accumulatedLengths">The accumulated lengths.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.GetNormal(Telerik.Windows.Controls.Diagrams.Extensions.PolylineInfo,System.Double)">
            <summary>
            Gets the normal.
            </summary>
            <param name="polyline">The polyline.</param>
            <param name="cornerRadius">The corner radius.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.PolylinePosition.GetPoint(System.Collections.Generic.IList{System.Windows.Point})">
            <summary>
            Gets the point.
            </summary>
            <param name="points">The points.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Segment">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Segment.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Segment"/> class from being created.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Segment.SegmentType">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.Segment.SegmentType.Line">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.Segment.SegmentType.CubicBeizer">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Points">
            <summary>
            Gets the points.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Type">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Create(System.Windows.Point,System.Windows.Point)">
            <summary>
            Creates the specified point0.
            </summary>
            <param name="point0">The point0.</param>
            <param name="point1">The point1.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Create(System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
            Creates the specified point0.
            </summary>
            <param name="point0">The point0.</param>
            <param name="point1">The point1.</param>
            <param name="point2">The point2.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Create(System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
            Creates the specified point0.
            </summary>
            <param name="point0">The point0.</param>
            <param name="point1">The point1.</param>
            <param name="point2">The point2.</param>
            <param name="point3">The point3.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Segment.Flatten(System.Collections.Generic.IList{System.Windows.Point},System.Double,System.Collections.Generic.IList{System.Double})">
            <summary>
            Flattens the specified result polyline.
            </summary>
            <param name="resultPolyline">The result polyline.</param>
            <param name="tolerance">The tolerance.</param>
            <param name="resultParameters">The result parameters.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.SketchEffect">
            <summary>
            Represents the sketch effect.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchEffect.#ctor(System.Windows.Media.PathGeometry)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.SketchEffect"/> class.
            </summary>
            <param name="originalGeometry">The original geometry.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.SketchEffect.Sketch">
            <summary>
            Sketches this instance.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail">
            <summary>
            Represents the RadDiagramThumbnail.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ViewportStyleProperty">
            <summary>
            Identifies the ViewportStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.IsAutoRefreshEnabledProperty">
            <summary>
            Identifies the IsAutoRefreshEnabled dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ViewportRectProperty">
            <summary>
            Identifies the ViewportRect dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ImageSourceProperty">
            <summary>
            Identifies the ImageSource dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ImageSource">
            <summary>
            Gets or sets the background brush.
            </summary>		
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ViewportRect">
            <summary>
            Gets or sets the viewport rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.Zoom">
            <summary>
            Gets the zoom.
            </summary>
            <value>The zoom.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ViewportStyle">
            <summary>
            Gets or sets the viewport border style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.Diagram">
            <summary>
            Gets or sets the diagram.
            </summary>
            <value>The diagram.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.IsAutoRefreshEnabled">
            <summary>
            Gets or sets the isAutoRefreshEnabled property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.RefreshThumbnail">
            <summary>
            Refreshes the RadDiagramThumbnail.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.ClipFrameworkElement(System.Windows.FrameworkElement)">
            <summary>
            Clips the framework element.
            </summary>
            <param name="frameworkElement">The framework element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.GetImageRectangle">
            <summary>
            Gets the image rectangle.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnDiagramChanged(Telerik.Windows.Controls.RadDiagram,Telerik.Windows.Controls.RadDiagram)">
            <summary>
            Called when the diagram property changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.CreateImageSource(System.Windows.Rect,System.Windows.Size)">
            <summary>
            Gets the image's source that will be displayed.
            </summary>
            <param name="enclosingBounds">The image's enclosing bounds.</param>
            <param name="returnImageSize">The return size of image.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnMouseWheel(System.Windows.Input.MouseWheelEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramThumbnail.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane">
            <summary>
            Represents the RadDiagramNavigationPane control.
            </summary>
            Template Parts?
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.DiagramProperty">
            <summary>
            Identifies the Diagram dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.ThumbnailStyleProperty">
            <summary>
            Identifies the ThumbnailStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.SliderStyleProperty">
            <summary>
            Identifies the SliderStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.AutofitButtonStyleProperty">
            <summary>
            Identifies the AutofitButtonStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.ExpandButtonStyleProperty">
            <summary>
            Identifies the ExpandButtonStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.IsAutofitButtonVisibleProperty">
            <summary>
            Identifies the IsAutofitButtonVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.IsExpandedProperty">
            <summary>
            Identifies the IsExpanded dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.ThumbnailStyle">
            <summary>
            Gets or sets the thumbnail style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.SliderStyle">
            <summary>
            Gets or sets the slider style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.AutofitButtonStyle">
            <summary>
            Gets or sets the button style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.ExpandButtonStyle">
            <summary>
            Gets or sets the toggle button style.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.Diagram">
            <summary>
            Gets or sets the RadDiagram.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.IsAutofitButtonVisible">
            <summary>
            Gets or sets a value indicating whether this instance is auto fit button visible.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.IsExpanded">
            <summary>
            Gets or sets a value indicating whether this instance is expanded.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.RefreshThumbnail">
            <summary>
            Refreshes the underlying thumbnail.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.RefreshZoomSlider">
            <summary>
            Refreshes the zoom slider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.OnApplyTemplate">
            <inheritdoc />	
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.OnCreateAutomationPeer">
            <summary>
            Creates the corresponding Automation Peer class.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramNavigationPane.OnIsExpandedChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the navigation pane is expanded or collapsed.
            </summary>
            <param name="newValue">The new value.</param>
            <param name="oldValue">The old value.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.Gallery">
            <summary>
            ViewModel containing list of all GalleryItems.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.Gallery.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.Gallery"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Gallery.Header">
            <summary>
            Gets or sets the header.
            </summary>
            <value>
            The header.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.Gallery.Items">
            <summary>
            Gets the items.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem">
            <summary>
            ViewModel representing the items in the Gallery.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.#ctor(System.String,Telerik.Windows.Controls.Diagrams.RadDiagramShapeBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem" /> class.
            </summary>
            <param name="header">The header.</param>
            <param name="shape">The shape.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.#ctor(System.String,Telerik.Windows.Controls.Diagrams.RadDiagramShapeBase,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem" /> class.
            </summary>
            <param name="header">The header.</param>
            <param name="shape">The shape.</param>
            <param name="type">The type.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.Header">
            <summary>
            Gets or sets the header.
            </summary>
            <value>
            The header.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.ItemType">
            <summary>
            Gets or sets the type of the item.
            </summary>
            <value>
            The type of the item.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.Shape">
            <summary>
            Gets or sets the shape.
            </summary>
            <value>
            The shape.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.ToolboxItemBackground">
            <summary>
            Gets or sets the background of the corresponding toolbox item.
            </summary>
            <value>
            The background.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItem.ToolboxItemForeground">
            <summary>
            Gets or sets the foreground of the corresponding toolbox item.
            </summary>
            <value>
            The background.
            </value>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItemsCollection">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItemsCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItemsCollection" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.GalleryItemsCollection.GetItemsByType(System.String)">
            <summary>
            Gets items by type.
            </summary>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGalleryItemsCollection">
            <summary>
            List of all built-in galleries.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGalleryItemsCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.HierarchicalGalleryItemsCollection" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem">
            <summary>
            Represents a <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem"/> item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.ViewContentProperty">
            <summary>
            The ViewContent dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.ViewContentTemplateProperty">
            <summary>
            The ViewContentTemplate dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.ViewContent">
            <summary>
            Gets or sets the view content.
            </summary>
            <value>
            The view content.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.ViewContentTemplate">
            <summary>
            Gets or sets the view content's template.
            </summary>
            <value>
            The view content's template.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.OnMouseEnter(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseEnter"/> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.OnMouseLeave(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseLeave"/> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxItem.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox">
            <summary>
            Custom animated list box to collect droppable RadDiagram shapes and other diagram elements.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.HeaderProperty">
            <summary>
            The Header dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.HeaderTemplateProperty">
            <summary>
            The HeaderTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.TitleProperty">
            <summary>
            The Title dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.TitleTemplateProperty">
            <summary>
            The TitleTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.IsOpenProperty">
            <summary>
            The IsOpen dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.CloseButtonStyleProperty">
            <summary>
            The CloseButtonStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OpenCloseButtonStyleProperty">
            <summary>
            The OpenCloseToggleButton dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.Header">
            <summary>
            Gets or sets the header.
            </summary>
            <value>
            The header.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.HeaderTemplate">
            <summary>
            Gets or sets the header's template.
            </summary>
            <value>
            The header's template.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.Title">
            <summary>
            Gets or sets the title.
            </summary>
            <value>
            The title.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.TitleTemplate">
            <summary>
            Gets or sets the title's template.
            </summary>
            <value>
            The title's template.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.IsOpen">
            <summary>
            Gets or sets a value indicating whether this instance is open.
            </summary>
            <value>
              <c>true</c> if this instance is open; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.CloseButtonStyle">
            <summary>
            Gets or sets the close button's style.
            </summary>
            <value>
            The close button's style.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OpenCloseButtonStyle">
            <summary>
            Gets or sets the open-close toggle button's style.
            </summary>
            <value>
            The open-close toggle button's style.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OnSelectionChanged(Telerik.Windows.Controls.RadSelectionChangedEventArgs)">
            <summary>
            Called after SelectionChanged event is fired.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own container.
            </summary>
            <param name="item">The item to check.</param>
            <returns>
            True if the item is (or is eligible to be) its own container; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.GetContainerForItemOverride">
            <summary>
            Creates or identifies the element that is used to display the given item.
            </summary>
            <returns>
            The element that is used to display the given item.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolbox.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxCategoryNames">
            <summary>
            Represents toolbox category names.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxCategoryNames.Arrows">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxCategoryNames.BasicShapes">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxCategoryNames.FlowChart">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxCategoryNames.Container">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup">
            <summary>
            Represents a <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup"/> item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.IsSelectedProperty">
            <summary>
            Identifies the <c cref="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.IsSelected"/> property.
            </summary>
            <seealso cref="P:Telerik.Windows.Controls.RadTabItem.IsSelected"/>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.IsSelected">
            <summary>
            Gets a value indicating whether this element is visible in the user interface (UI).
            </summary>
            <returns>true if the element is visible; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.Telerik#Windows#Controls#TabControl#IRadTabItem#Control">
            <summary>
            Gets an a reference to the control that implements the IRadTabItem interface.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.Telerik#Windows#Controls#TabControl#IRadTabItem#TabStripPlacement">
            <summary>
            Gets the tab strip placement.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.Telerik#Windows#Controls#TabControl#IRadTabItem#SetTabOrientation(System.Windows.Controls.Orientation)">
            <summary>
            Sets the tab orientation.
            </summary>
            <param name="orientation">The orientation.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.Telerik#Windows#Controls#TabControl#IRadTabItem#UpdateHeaderPresenterContent">
            <summary>
            Updates the content of the header presenter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.Telerik#Windows#Controls#TabControl#IRadTabItem#UpdateTabStripPlacement(System.Windows.Controls.Dock)">
            <summary>
            Updates the tab strip placement.
            </summary>
            <param name="placement">The placement.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">Indicates whether transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            Prepares the specified element to display the specified item.
            </summary>
            <param name="element">Element used to display the specified item.</param>
            <param name="item">Specified item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.GetContainerForItemOverride">
            <summary>
            Creates or identifies the element that is used to display the given item.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own container.
            </summary>
            <param name="item">The item to check.</param>
            <returns>
            True if the item is (or is eligible to be) its own container; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.ArrangeOverride(System.Windows.Size)">
            <summary>Called to arrange and size the content of a <see cref="T:System.Windows.Controls.Control"/>
            object. </summary>
            <returns>The size of the control.</returns>
            <param name="finalSize">The computed size that is used to arrange the content.
            </param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnMouseEnter(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseEnter"/> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnMouseLeave(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseLeave"/> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.RadDiagramToolboxGroup.OnIsSelectedChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IsSelected property has changed.
            </summary>
            <param name="oldValue">The old value of the IsSelected property.</param>
            <param name="newValue">The new value of the IsSelected property.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxDragService">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ToolboxDragService.OnDragInitialized(System.Object,Telerik.Windows.DragDrop.DragInitializeEventArgs)">
            <summary>
            Called when [drag initialized].
            </summary>
            <param name="sender">The sender.</param>
            <param name="args">The <see cref="T:Telerik.Windows.DragDrop.DragInitializeEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1">
            <summary>
            Base class for MVVM container nodes.
            </summary>
            <typeparam name="TNode">The type of the node.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1.#ctor">
            <summary>
            Initializes a new instance of the ContainerNodeViewModelBase class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1.InternalItems">
            <summary>
            Gets the internal items collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1.Telerik#Windows#Diagrams#Core#IContainerItem#Items">
            <summary>
            Gets the items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1.AddItem(System.Object)">
            <summary>
            Adds the item to its children.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ContainerNodeViewModelBase`1.RemoveItem(System.Object)">
            <summary>
            Removes the item from its children.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2">
            <summary>
            Base implementation of the <see cref="T:Telerik.Windows.Diagrams.Core.IGraphSource"/> which can be used to create an MVVM sources for RadDiagram. 
            </summary>
            <remarks>This MVVM source is bidirectional.</remarks>
            <typeparam name="TNode">
            The data type of the node.
            </typeparam>
            <typeparam name="TLink">
            The data type of the link.
            </typeparam>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.InternalItems">
            <summary>
            Gets the internal items collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.InternalLinks">
            <summary>
            Gets the internal links collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.Items">
            <summary>
            Gets the node items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.Links">
            <summary>
            Gets the links or connections of this diagram source.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.AddNode(`0)">
            <summary>
            Adds a node (shape) to this diagram source.
            </summary>
            <param name="node">
            The node to add.
            </param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.AddLink(`1)">
            <summary>
            Adds the given link to this diagram source.
            </summary>
            <param name="link">The link to add.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.RemoveLink(`1)">
            <summary>
            Removes the link from this source.
            </summary>
            <param name="link">The link.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.RemoveItem(`0)">
            <summary>
            Removes the item from the source.
            </summary>
            <param name="node">The node.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.GraphSourceBase`2.Clear">
            <summary>
            Removes all items and links from this source.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.HierarchicalNodeViewModel">
            <summary>
            MVVM representation of a hierarchical node.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.HierarchicalNodeViewModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.HierarchicalNodeViewModel" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.HierarchicalNodeViewModel.Children">
            <summary>
            Gets or sets the children of the current node.
            </summary>
            <value>The children.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.HierarchicalNodeViewModel.HasChildren">
            <summary>
            Gets a value indicating whether this instance has children.
            </summary>
            <value>
            	<c>True</c> if this instance has children; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase.Visibility">
            <summary>
            Gets or sets the visibility.
            </summary>
            <value>The visibility.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase.Position">
            <summary>
            Gets or sets the position.
            </summary>
            <value>The position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase.Content">
            <summary>
            Gets or sets the content or label of this connection.
            </summary>
            <value>
            The content.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ItemViewModelBase.IsSelected">
            <summary>
            Gets or sets a value indicating whether this instance is selected.
            </summary>
            <value>
            	<c>true</c> if this instance is selected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1">
            <summary>
            A generic base class for the link or connection in a diagram source.
            </summary>
            <typeparam name="T"> A type inheriting from <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase"/>.
            </typeparam>
            <example>
            If you want to map the Content of the link to the Content of the connection you
            need to add a Style like so 
            <code lang="C#">
            &lt;Style  TargetType="telerik:RadDiagramConnection"&gt;
              &lt;Setter Property="ContentTemplate"  &gt;
                &lt;Setter.Value&gt;
                  &lt;DataTemplate&gt;
                    &lt;TextBlock Text="{Binding Content}"/&gt;
                  &lt;/DataTemplate&gt;
                &lt;/Setter.Value&gt;
              &lt;/Setter&gt;
            &lt;/Style&gt;
            </code>.
            </example>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.#ctor(`0,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1"/> class.
            </summary>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.Source">
            <summary>
            Gets or sets the source of the connection.
            </summary>
            <value>
            The source.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.Target">
            <summary>
            Gets or sets the target of this connection.
            </summary>
            <value>
            The target.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.SourceCapType">
            <summary>
            Gets or sets the type of the source cap.
            </summary>
            <value>The type of the source cap.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.LinkViewModelBase`1.TargetCapType">
            <summary>
            Gets or sets the type of the target cap.
            </summary>
            <value>The type of the target cap.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase">
            <summary>
            Base class for MVVM nodes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase.Width">
            <summary>
            Gets or sets the width.
            </summary>
            <value>
            The width.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase.Height">
            <summary>
            Gets or sets the height.
            </summary>
            <value>
            The height.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.NodeViewModelBase.RotationAngle">
            <summary>
            Gets or sets the rotation of the shape.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2">
            <summary>
            Represents a base class for an observable graph source.
            </summary>
            <typeparam name="TNode">The type of the node.</typeparam>
            <typeparam name="TLink">The type of the link.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#AddLink(Telerik.Windows.Diagrams.Core.ILink)">
            <summary>
            Adds a link in the Links collection.
            </summary>
            <param name="link">The link.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#AddNode(System.Object)">
            <summary>
            Adds a node in the Items collection.
            </summary>
            <param name="node">The node.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#CreateLink(System.Object,System.Object)">
            <summary>
            Creates a link based on the associated source and target nodes.
            </summary>
            <param name="source">The source node.</param>
            <param name="target">The target node.</param>
            <returns>
            Returns the created link.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#CreateNode(Telerik.Windows.Diagrams.Core.IShape)">
            <summary>
            Creates a node based on an associated shape.
            </summary>
            <param name="shape">The associated shape.</param>
            <returns>
            Returns the created node.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#RemoveLink(Telerik.Windows.Diagrams.Core.ILink)">
            <summary>
            Removes a link from the Links collection.
            </summary>
            <param name="link">The link.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.Telerik#Windows#Diagrams#Core#IObservableGraphSource#RemoveNode(System.Object)">
            <summary>
            Removes a node from the Items collection.
            </summary>
            <param name="node">The node.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.CreateLink(System.Object,System.Object)">
            <summary>
            Creates a link based on the associated source and target nodes.
            </summary>
            <param name="source">The source node.</param>
            <param name="target">The target node.</param>
            <returns>
            Returns the created link.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.ObservableGraphSourceBase`2.CreateNode(Telerik.Windows.Diagrams.Core.IShape)">
            <summary>
            Creates a node based on an associated shape.
            </summary>
            <param name="shape">The associated shape.</param>
            <returns>
            Returns the created node.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2">
            <summary>
            Represents a base class for a serializable graph source.
            </summary>
            <typeparam name="TNode">The type of the node.</typeparam>
            <typeparam name="TLink">The type of the link.</typeparam>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.CachedNodes">
            <summary>
            Cache for the deserialized nodes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.SourceUniqueIdKey">
            <summary>
            Gets the source unique id key. This key is used to save the unique id of the source of the link in the serialization info.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.TargetUniqueIdKey">
            <summary>
            Gets the target unique id key. This key is used to save the unique id of the target of the link in the serialization info.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.NodeUniqueIdKey">
            <summary>
            Gets the node unique id key. This key is used to save the unique id of the node in the serialization info.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.GetNodeUniqueId(`0)">
            <summary>
            Gets the node's unique id. This id should be always unique and should not rely on the object's reference.
            </summary>
            <param name="node">The node.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.DeserializeLink(Telerik.Windows.Diagrams.Core.IConnection,Telerik.Windows.Diagrams.Core.SerializationInfo)">
            <summary>
            Deserializes the link. When overridden, this method creates a new instance of the link and restores any saved information from the serialization info.
            </summary>
            <param name="connection">The connection.</param>
            <param name="info">The info.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.DeserializeNode(Telerik.Windows.Diagrams.Core.IShape,Telerik.Windows.Diagrams.Core.SerializationInfo)">
            <summary>
            Deserializes the node. When overridden, this method creates a new instance of the node and restores any saved information from the serialization info.
            </summary>
            <param name="shape">The shape.</param>
            <param name="info">The info.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.SerializeNode(`0,Telerik.Windows.Diagrams.Core.SerializationInfo)">
            <summary>
            Serializes the node. When overridden, this method saves important information about the node in the specified serialization info.
            </summary>
            <param name="node">The node.</param>
            <param name="info">The info.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.SerializeLink(`1,Telerik.Windows.Diagrams.Core.SerializationInfo)">
            <summary>
            Serializes the link. When overridden, this method saves important information about the link in the specified serialization info.
            </summary>
            <param name="link">The link.</param>
            <param name="info">The info.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Diagrams.Extensions.ViewModels.SerializableGraphSourceBase`2.ClearCache">
            <summary>
            Clears the deserialization cache. The cache contains the generated items when deserializing the saved data.
            The cache should be cleared either before or after the deserialization of the graph source.
            </summary>
        </member>
    </members>
</doc>
