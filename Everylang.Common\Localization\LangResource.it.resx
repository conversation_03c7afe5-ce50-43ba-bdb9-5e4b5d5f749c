<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>Informazioni</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Nuova versione disponibile, riavviare l'applicazione</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Chiaro</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Periodo di prova attivato per 40 giorni, tutte le funzionalità incluse</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Vicino</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Copia</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Copia HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Copia RTF</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Clicca sul numero per inserire il testo</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Dividi il testo nel carattere di nuova riga</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Dividi il testo per spazio</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Uscita</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Finestra bloccata</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>Dalla lingua:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Mostra la cronologia</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Crollo</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Espandere</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>SÌ</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>NO</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Inserire</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Apri la finestra principale con la traduzione</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Aprire il sito web del servizio di traduzione</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Scorciatoia da tastiera per aprire la finestra principale</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Impostazioni</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Apri l'elenco nella finestra principale</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Scorciatoia da tastiera per disabilitare tutte le funzionalità</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Disabilitare il programma</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Abilita programma</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>Il programma è disabilitato</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Non chiudere</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>In latino</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Lingua:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Tradurre</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Si è verificato un errore durante il processo di traduzione, riprova o seleziona un altro servizio di traduzione</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Ascoltare</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aggiornamento</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>IMPOSTAZIONI</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>STORIA</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>APPUNTI</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>DIARIO</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>SNIPPET</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Attiva PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Storia</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Appunti</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Tasti di scelta rapida</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Impossibile registrare la combinazione di tasti</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Aggiornamento disponibile, riavvia l'applicazione</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>L'applicazione è in esecuzione e ridotta a icona</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>L'applicazione è stata aggiornata, l'elenco delle modifiche è sul sito</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Affinché il programma funzioni correttamente con tutte le applicazioni, è necessario eseguirlo come amministratore</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancellare</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Si è verificato un errore e l'applicazione verrà terminata. Si prega di inviare un testo con <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Sul computer è installato più di un layout di tastiera per alcune lingue, il che potrebbe influire negativamente sul corretto funzionamento della funzione di cambio layout.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Trovare...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(Versione PRO)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Eliminare</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Creare</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tutto</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Tutti i programmi</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Solo nella versione PRO</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>Il programma è ridotto a icona nel vassoio</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Prima di utilizzare il programma, leggere la documentazione</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Scheda - Cerca.  Esc: annulla e cancella</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Sostantivo</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronome</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Aggettivo</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verbo</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Avverbio</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Pretesto</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Unione</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Interiezione</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Comunione</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Verbo ausiliare</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Parola introduttiva</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Controllo ortografico</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Nessun errore</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Lingua non supportata</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Si è verificato un errore</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Il testo è troppo lungo</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Vicino</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Opzioni:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Nessuna opzione</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Verifica completata</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Saltare</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Salta tutto</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Sostituire</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Sostituisci tutto</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Inserisci testo</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Copia</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Ritorno</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Ripristina le impostazioni</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Premere la combinazione di tasti</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>russo</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Inglese</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>francese</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italiano</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ucraino</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>IL PROGRAMMA</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Contratto di licenza</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Un assistente universale per lavorare con testo in diverse lingue</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Versione:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>È disponibile la nuova versione del programma</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Aggiornamento</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>FEEDBACK</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Scrivi i tuoi commenti e/o domande</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Ripristina tutte le impostazioni del programma</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Apri la finestra di benvenuto</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Ripristinare tutte le impostazioni e i dati del programma?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Modulo di contatto</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>Il programma è in esecuzione come amministratore</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>Il programma non è in esecuzione come amministratore</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>COMPONENTI</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Modifica della disposizione</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Cambia il layout dell'ultima parola</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Usa Pausa</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Cambia il layout del testo selezionato</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Selezione di un metodo di commutazione del layout</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulazione di tasti per cambiare layout</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Esecuzione di un comando di Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Mantieni il testo selezionato dopo aver cambiato layout</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Cambio di layout abilitato</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Utilizza Ctrl+(numero) per passare a una lingua specifica</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Utilizzare il doppio clic sul tasto Maiusc</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Passa dall'inizio della riga</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Utilizzare il doppio clic sul pulsante ScrollLock</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Cambia layout tramite pulsante</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Impostazioni di sistema</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Ctrl destro</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Ctrl sinistro</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Spostamento a destra</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Spostamento a sinistra</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Ctrl destro o sinistro</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Cambio a destra o a sinistra</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Ctrl destro o CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Per abilitare o disabilitare la funzione CapsLock, premere contemporaneamente i tasti Shift destro e sinistro</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Suono di cambio layout</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Impostazione del suono per cambiare layout</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Elenco delle lingue a cui passerà il layout</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Impostazione dei tasti per passare a una lingua specifica</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Disabilitare il cambio automatico del layout?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Abilitare il cambio automatico del layout?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Cambio automatico del layout</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Commutazione automatica abilitata</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Correggi due lettere maiuscole all'inizio di una parola</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Risolto il problema con la pressione accidentale di CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Non cambiare se tutte le lettere di una parola sono in maiuscolo</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Correggere il layout dopo aver premuto il tasto Invio</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Aggiungi parole di una lettera alle regole</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Aggiungi automaticamente regole di cambio</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Non correggere il layout se è stato precedentemente modificato manualmente</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Cambia layout solo dopo aver inserito una parola intera</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Cambia layout dopo aver smesso di digitare</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Elimina tutte le regole di cambio automatico</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Testo</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Tutti i layout</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Azione</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Aggiungi regole solo dopo la conferma</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Interruttore</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Non cambiare</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Candidato</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Regole di cambio automatico</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Elenco delle lingue per le quali funzionerà la commutazione automatica</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Elenco delle regole per la commutazione automatica</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Mostra i candidati</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Apri l'elenco delle regole di cambio automatico</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Numero di commutazioni manuali del layout delle parole da includere nelle regole</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Aggiungere una parola alle regole del cambio automatico? Inserisci - SÌ</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Lingua di input corrente sul puntatore del mouse</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Lingua di input corrente nel cursore di testo</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Finestra di indicazione della lingua separata</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Lingua corrente nella barra delle applicazioni</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Funzionalità avanzate</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Mostra la bandiera del paese</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Mostra il nome della lingua</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Trasparenza dell'indicatore</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Aumento della dimensione dell'indicatore in percentuale</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Indicazione della lingua corrente sulla tastiera</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Posizione dell'indicatore sul puntatore del mouse</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Nascondi l'indicatore nei programmi eseguiti a schermo intero</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Mostra lo stato di CapsLock</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock abilitato</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Posizione dell'indicatore nel cursore del testo</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Controllo ortografico</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Controllo ortografico abilitato</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Controlla l'ortografia durante la digitazione</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Suono del controllo ortografico durante la digitazione</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Utilizza i numeri per una sostituzione rapida</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Impostazioni audio per il controllo ortografico</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Scorciatoia da tastiera per controllare l'ortografia del testo selezionato</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Controlla l'ortografia del testo selezionato</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Chiudere la finestra se non ci sono errori dopo 3 secondi</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Gestore degli appunti</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Incolla il testo senza formattazione e incolla il percorso del file copiato</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Gestione appunti abilitata</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Apri la cronologia degli appunti utilizzando la scorciatoia da tastiera</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Incolla in sequenza il testo dalla cronologia degli appunti per la finestra corrente</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Incollare in sequenza il testo dalla cronologia degli appunti</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Incolla il testo con Ctrl+Maiusc+(numero) - numero 1, 2, 3, 4, 5, 6, 7, 8, 9 - indice della voce nella cronologia degli appunti</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Salva il percorso del file copiato nella cronologia degli appunti</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Salva la cronologia del buffer delle immagini</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Quando incolli testo dalla cronologia, sostituisci il valore corrente negli appunti</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Dimensioni della cronologia degli appunti</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Gli appunti cambiano suono</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Convertitore di testo</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Converti in base al layout della tastiera corrente</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Converti numeri e date in stringhe, valuta espressioni</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Imposta i tasti di scelta rapida</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Inverte le maiuscole e minuscole del testo selezionato</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Converti il ​​testo selezionato in maiuscolo</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Testo selezionato in minuscolo</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Rende minuscolo il primo carattere della parola sotto il cursore</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Maiuscolo il primo carattere della parola sotto il cursore</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Traslittera il testo selezionato</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Converti il ​​testo in stile camelCase</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Trova e sostituisci il testo nel testo selezionato</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Testo selezionato in cornice con simboli (esempio)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Aggiungere</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Sinistra</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Giusto</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Eliminare</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>testo di esempio</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Chiudere l'ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Sostituisci il testo INVIO</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Traduzione</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Lingua predefinita in cui tradurre</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Lingua predefinita da cui tradurre</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Lingua principale per la traduzione</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Servizio di traduzione</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Scorciatoia da tastiera per tradurre il testo selezionato</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Premere la combinazione di tasti</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Facendo doppio clic sul pulsante Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Traduci quando selezioni il testo con il mouse</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Traduzione inclusa</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Impostazioni generali</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Lingua dell'interfaccia del programma</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Riavviare l'applicazione per cambiare la lingua?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Varie</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Ridurre a icona nel vassoio</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Scappando da Windows</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Esegui con diritti di amministratore</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Controlla gli aggiornamenti</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Aggiornamento alla versione beta</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Soggetto</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>Giorno o notte</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Stili di progettazione</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Cartella per il salvataggio delle impostazioni del programma</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Utilizza le impostazioni proxy del sistema</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Inserisci l'indirizzo del server</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Entra nel porto</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Inserisci il tuo nome utente</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Inserisci la tua password</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Salva le impostazioni del proxy</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Impostazioni proxy</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Errore nelle impostazioni del server proxy, modificare le impostazioni</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Usa un tema scuro nelle ore serali</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Ore serali dalle</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Ore serali fino al</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Disabilita tutte le funzioni nei programmi in esecuzione in modalità a schermo intero</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Importa impostazioni</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Impostazioni di esportazione</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Impostazioni di base</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Traduttore</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Controllo ortografico</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Indicatore di disposizione</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Funzionalità PRO</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Appunti</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Convertitore di testo</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Cambiare layout</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Commutazione automatica</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Programmi d'eccezione</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Layout predefiniti</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>Informazioni sul programma</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Licenza</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Snippet</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Tradurre</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Copia</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Controlla l'ortografia</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Chiama la ricerca</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Apri il collegamento nel browser</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Traduci il sito utilizzando il collegamento</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Generazione di un breve collegamento</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Crea un messaggio di posta</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Inserisci testo</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Incolla il testo senza formattazione</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Apri la cronologia degli appunti</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Apri la cronologia delle traduzioni</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Diario aperto</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Cambia il caso del testo selezionato</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Apri l'elenco degli snippet</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Convertitore di testo</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Testo selezionato in minuscolo</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Converti il ​​testo selezionato in maiuscolo</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Inverte le maiuscole e minuscole del testo selezionato</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Incornicia il testo selezionato con i simboli</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Traslittera il testo selezionato</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Converti numeri e date in stringhe, valuta espressioni</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Convertitore</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Traduzione</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Copia</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Ortografia</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Ricerca</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Collegamento</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Traduzione di siti web</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL più breve</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Inserire</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Inserimento senza formato.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>senza formato.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Cronologia del buffer</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>respingente</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Snippet</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Registrati</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Registrati</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Inverti registro</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Testo in cornice</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Traslitterazione</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Stuoia. espressioni</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Inizio:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>FINE:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Eliminare</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Chiaro</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Incluso</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TESTO</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>APPLICAZIONE</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Programmi d'eccezione</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Aggiungere</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Aggiungi il file exe</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Aggiungi cartella</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Aggiungi per titolo della finestra</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Indicatore di disposizione</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Cambiare layout</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Snippet</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Tasti di scelta rapida</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Cambio automatico della lingua</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Diario di inserimento testo</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Convertitore e custodia di testo</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Cronologia degli appunti</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Salva immagini</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Eliminare</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Fare clic sul programma desiderato</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Segna le funzioni che funzioneranno per il programma</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Aggiunta dall'elenco dei programmi</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Aggiunta con il cursore</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Lingue del programma</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Lingua predefinita per i programmi selezionati</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Attivazione delle funzioni PRO</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Stato</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Codice di attivazione</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Attivare</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Provalo</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO attivato</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Periodo di prova per 40 giorni</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Informazioni sulla licenza</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Data di attivazione:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Periodo di validità della licenza:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>E-mail:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Proprietario:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Informazioni sulla licenza</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Numero di posti:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Numero di riattivazioni disponibili:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Posti disponibili:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Tipo di licenza:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO non attivato</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO attivato per il periodo di prova</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Acquistare</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Inserisci l'e-mail</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Inserisci il codice</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Le informazioni sulla tua licenza sono state inviate a:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Rimuovi la licenza</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Inserisci il codice e l'e-mail</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Attivazione completata con successo</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Attivazione completata con errore</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Attivazione completata con errore, la tua licenza è bloccata</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>Il programma è stato attivato sul nuovo posto di lavoro, ma è stato disattivato sul computer</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Attivazione completata con errore, controlla la tua connessione internet</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Attivazione completata con errore, potrebbe essere stata inserita una email errata</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>Il periodo di prova è scaduto</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>La licenza è scaduta</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>L'attivazione non è possibile a causa del superamento del limite di riattivazione. Acquista postazioni aggiuntive per questa licenza</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>L'attivazione non è possibile a causa della riattivazione di questa licenza su un altro computer</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Il tuo codice di attivazione è stato aggiornato</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Licenza perpetua</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Hai superato il limite del numero di postazioni per la licenza, tutte le funzionalità PRO verranno disabilitate</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>La versione PRO è scaduta, è possibile acquistare una nuova licenza sul sito EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>La versione PRO scadrà a breve</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>È stato superato il limite di riattivazione della licenza negli ultimi 30 giorni, tutte le funzionalità PRO verranno disabilitate</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>La tua licenza è bloccata, tutte le funzionalità PRO saranno disabilitate</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick abilitato</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Seleziona un servizio di ricerca per SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Controlla le funzionalità che saranno disponibili</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Aprire premendo il pulsante sinistro e poi destro del mouse</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Apri facendo doppio clic con il pulsante centrale del mouse</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Utilizzare i tasti di scelta rapida per aprire</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Tasti di scelta rapida per l'apertura della finestra SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Mostra la finestra della guida dopo la selezione del testo</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Dimensione della finestra</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Posizione della finestra rispetto al puntatore del mouse</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Diario aperto</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Diario incluso</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Password del diario</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Inserisci la tua vecchia password del diario</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Nuova password salvata</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Reimpostazione della password</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Numero di voci nel diario</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>La password inserita non è corretta, reimpostare la password attuale? In questo caso, tutti i dati del diario verranno eliminati</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Mantieni frasi di una sola parola</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Testo da sostituire (facoltativo):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Testo del frammento:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>In quale lingua devo cambiare il layout?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Non cambiare</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Tag (separati da spazi):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Tag</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Descrizione:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Cancellare</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Mantieni la posizione del cursore</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Sostituisci durante la digitazione</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Snippet</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Nuovo frammento</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Eliminare tutti gli snippet con questo tag?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Snippetst abilitato</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Modifica frammenti</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Cosa sostituire</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>Con cosa sostituire</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Aggiungere</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Eliminare</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Sostituisci quando digiti in un altro layout</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Abbina la custodia delle lettere</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Mostra suggerimento durante la digitazione</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Ordina per frequenza di utilizzo</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Sostituisci con:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Snippet abilitati</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Apri l'elenco degli snippet da inserire</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Aggiungi il testo evidenziato agli snippet</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Tasto tabulazione</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Inserisci la chiave</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tabulazione o Invio</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Spazio</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Spazio o Invio</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Tasti di scelta rapida</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Doppia pressione del tasto</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Facendo clic su un pulsante del mouse</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Tasti di scelta rapida abilitati</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Assente</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Premere i tasti di scelta rapida</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Combinazione</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Doppio tocco</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Facendo clic su un pulsante del mouse</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Selezionare un tasto da premere due volte</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Seleziona un pulsante aggiuntivo del mouse</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Tasti di scelta rapida disabilitati</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Ctrl sinistro o destro</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Ctrl sinistro</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Ctrl destro</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Spostamento a sinistra o a destra</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Spostamento a sinistra</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Spostamento a destra</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Alt. sinistra</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Alt. destra</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Suono attivo</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Seleziona un suono</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Prima di utilizzare il programma, leggere la documentazione</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Esplora le funzionalità del programma</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Guarda la video presentazione</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Sito web del programma</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Acquista una licenza</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Riconoscimento del testo</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Scorciatoia da tastiera per avviare l'OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Seleziona le lingue predefinite</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Seleziona le lingue</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>L'utilizzo delle lingue europee e asiatiche non è supportato</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Lingue europee:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Lingue asiatiche:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Seleziona l'area dello schermo</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Apri l'immagine</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Riconoscere</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Riconoscere il codice a barre</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Seleziona le lingue</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Seleziona un'area sullo schermo o carica un file per il riconoscimento</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Riconoscimento del testo</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Per favore attendi, il modulo si sta caricando</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Fare clic per scaricare il modulo</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Testo copiato</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Testo non riconosciuto</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Copia immagine</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Copia testo</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Sostituisci con:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Ricerca:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Corrispondenze totali trovate:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Nessuna corrispondenza trovata!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Sostituzioni totali effettuate:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATA</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TIPO</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Cancella filtro</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Seleziona Colonne</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filtro</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>E</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Contiene</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Non contiene</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Termina con</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Contenuto in</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Vuoto</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Uguali</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Più di</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Maggiore o uguale a</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Meno di</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Minore o uguale a</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Non contenuto in</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>non vuoto</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>non uguale</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Non è nullo</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Nullo</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Maiuscole e minuscole</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Seleziona tutto</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Mostra le righe con il valore che</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Inizia con</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Trascina l'intestazione di una colonna e rilasciala qui per raggrupparla in base a questa colonna</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Intestazione del gruppo</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Raggruppati per:</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Ricerca nel testo completo</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Il diario è disattivato</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Diario incluso</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>La gestione degli appunti è disabilitata</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Disabilitato</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Incluso</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Cambio di layout disabilitato</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>La commutazione automatica del layout è disabilitata</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Commutazione automatica del layout abilitata</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Fare clic qui per aggiungere un nuovo elemento</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Copia il testo tradotto</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Cancella tutto</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Apri nel browser</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Chiudere il programma?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Lingue in primo piano</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Seleziona le tue lingue preferite</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Memorizza la cronologia delle traduzioni</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Cancella la cronologia delle traduzioni</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Funzioni</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Caricamento</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Modifica delle dimensioni della tela</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Vicino</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Correzione</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Somma</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Sfondo:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Colore del bordo:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Spessore del bordo:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Dimensioni della tela</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>bianco</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Ordinare</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Testo dell'immagine</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Il tuo testo</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Disegno</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Colore pennello:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Dimensione del pennello:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Sfocatura</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Luminosità</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Contrasto</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Cambiare tonalità</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Inverti i colori</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Saturazione</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Affilare</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Capovolgi orizzontalmente</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Capovolgi verticalmente</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Dimensione del carattere</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Altezza:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Posizione orizzontale</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Allineamento delle immagini</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Anteprima dell'immagine</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Dimensione dell'immagine</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Aprire</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Opzioni</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Mantieni le proporzioni originali</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Raggio:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Ritorno</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Dimensione relativa</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Ridimensionare</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Ruota di 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Ruota di 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Ruota di 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Giro</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Angoli arrotondati</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Figura</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Ellisse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Programma</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rettangolo</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Colore del bordo</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Spessore del bordo</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Modulo di riempimento tubi</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Blocca le proporzioni</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Figura</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Riempire una forma</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Testo</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Colore del testo</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Impossibile aprire il file.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Impossibile aprire il file. Questo potrebbe essere bloccato da un'altra applicazione.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Convertire</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Impossibile salvare il file.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Cancellare</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Questo formato di file non è supportato.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Posizione verticale</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Larghezza:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Resetta tutto</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Dagli appunti</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Modifica immagine</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Incolla l'input di emulazione</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Editor di frammenti</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Testo sostitutivo:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Il modulo di riconoscimento del testo non è caricato</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Aspetto</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Ordine delle funzioni</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Mostra solo le lingue selezionate</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Mostra tutto</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Riavviare il programma per cambiare il tema?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Per inserire del testo, premere il tasto</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Mostra note</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>All'archivio</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Aggiungi una nota</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Elenco delle note</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Nota il colore</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Converti in nota</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Converti in elenco attività</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Incolla come testo normale</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Incolla come testo</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Archivio delle note</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Ripristinare</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Famiglia e dimensione dei caratteri per le note</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Trasparenza delle note inattive</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick disabilitato</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Diario disabilitato</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Snippet disabilitati</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Mostra pulsante di chiusura</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Timeout per l'emulazione della sequenza di tasti</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Immagine</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Testo riconosciuto</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>L'aggiornamento non è stato completato, aggiorna manualmente.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Errore di aggiornamento di EveryLang</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Taggato</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Incluso</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Inserisci il testo da cercare</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Dall'archivio</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Disabilitato</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>Nell'archivio</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Disposizione della tastiera</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Caso di testo</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Mostra lo stato di Bloc Num</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock è disabilitato</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>Bloc Num abilitato</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Apre una finestra con le funzioni del convertitore</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Maiuscola la prima lettera</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Per prima cosa</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Prima lettera minuscola</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Primo giù</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Apre una finestra con le funzioni di modifica del caso</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Converti il ​​testo nello stile snake_case</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Conversione del testo in stile kebab-case</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Conversione del testo in stile PascalCase</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Caso di testo</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Fare clic per scaricare</value>
  </data>
  <data name="AutochangeSortingByAlphabet" xml:space="preserve">
    <value>Ordina per alfabeto</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Preferiti</value>
  </data>
  <data name="RemoveFavorite" xml:space="preserve">
    <value>Rimuovi dai preferiti</value>
  </data>
  <data name="AddFavorite" xml:space="preserve">
    <value>Aggiungi ai preferiti</value>
  </data>
  <data name="ClipboardFavorite" xml:space="preserve">
    <value>Appunti preferiti</value>
  </data>
</root>