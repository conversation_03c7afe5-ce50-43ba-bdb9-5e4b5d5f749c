﻿using Everylang.App.Callback;
using Everylang.App.License;
using Everylang.App.License.LicenseCore;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Windows;
using Telerik.Windows.Controls;
using Application = System.Windows.Application;

namespace Everylang.App.View.SettingControls.ProSettings
{

    internal partial class ProSettingsControl
    {

        internal ProStatusStrings ProStatusStringsLoc;

        internal ProSettingsControl()
        {
            InitializeComponent();
            ProStatusStringsLoc = ProStatusStrings.Instance;
            DataContext = ProStatusStringsLoc;
        }

        private async void Activate()
        {
            if (Application.Current.MainWindow == null) return;

            if (string.IsNullOrEmpty(ProStatusStringsLoc.Code) && string.IsNullOrEmpty(ProStatusStringsLoc.Email))
            {
                Application.Current.MainWindow.WindowState = WindowState.Normal;
                Application.Current.MainWindow.Activate();
                RadWindow.Alert(LocalizationManager.GetString("ProSettingsCodeEmail"));
                return;
            }

            if (string.IsNullOrEmpty(ProStatusStringsLoc.Code))
            {
                Application.Current.MainWindow.WindowState = WindowState.Normal;
                Application.Current.MainWindow.Activate();
                RadWindow.Alert(LocalizationManager.GetString("ProSettingsCode"));
                return;
            }

            if (string.IsNullOrEmpty(ProStatusStringsLoc.Email))
            {
                Application.Current.MainWindow.WindowState = WindowState.Normal;
                Application.Current.MainWindow.Activate();
                RadWindow.Alert(LocalizationManager.GetString("ProSettingsEmail"));
                return;
            }

            ProStatusStringsLoc.IsProgress = true;
            LicenseInfo licDataUtils = new LicenseInfo();
            if (ProStatusStringsLoc.Code.StartsWith("el"))
            {
                string licst = await licDataUtils.CheckLicInternal(ProStatusStringsLoc.Code, ProStatusStringsLoc.Email);
                if (Secure.DecryptStringAES(licst, ProStatusStringsLoc.Code).ToLower().Contains("error"))
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationErrorEmail"));
                    ProStatusStringsLoc.IsProgress = false;
                    GlobalEventsApp.OnEventPro("ve");
                    return;
                }
            }

            SettingsManager.LicenseModel = await licDataUtils.CheckLicExternal(ProStatusStringsLoc.Code);
            if (SettingsManager.LicenseModel.IsConnectOk)
            {
                if (ProStatusStringsLoc.Code.StartsWith("cod") || ProStatusStringsLoc.Code.StartsWith("lic"))
                {
                    ProStatusStringsLoc.Code = SettingsManager.LicenseModel.License;
                    ProStatusStringsLoc.Email = SettingsManager.LicenseModel.Email;
                    SettingsManager.Settings.LicIsActive = true;
                }

                if (SettingsManager.LicenseModel.ErrorLic != "")
                {
                    SettingsManager.Settings.LicIsActive = false;
                    ProStatusStringsLoc.IsProgress = false;
                    DeActivateSettings();
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationError"));
                    GlobalEventsApp.OnEventPro("ve");
                    return;
                }

                if (SettingsManager.LicenseModel.IsBlockedByEarlyActivation)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsBlockedByEarlyActivation"));
                    GlobalEventsApp.OnEventPro("ve");
                    ProStatusStringsLoc.IsProgress = false;
                    return;
                }

                if (SettingsManager.LicenseModel.IsBlockedByMonthActivateLimit)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsBlockedByMonthActivateLimit"));
                    GlobalEventsApp.OnEventPro("ve");
                    ProStatusStringsLoc.IsProgress = false;
                    return;
                }

                if (SettingsManager.LicenseModel.LicIsBlocked)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationBlocked"));
                    GlobalEventsApp.OnEventPro("ve");
                    ProStatusStringsLoc.IsProgress = false;
                    return;
                }

                if (SettingsManager.LicenseModel.LicExpiryDate != DateTime.MinValue &&
                    SettingsManager.LicenseModel.LicExpiryDate < DateTime.Now)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsExpired"));
                    GlobalEventsApp.OnEventPro("ve");
                    ProStatusStringsLoc.IsProgress = false;
                    GlobalEventsApp.OnEventProExp();
                    return;
                }

                if (SettingsManager.LicenseModel.IsReactivated)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationReactivated") + " " + SettingsManager.LicenseModel.LastSeat);
                }

                SettingsManager.LicStatus = Secure.EncryptStringAES("koholbok", SettingsManager.LicenseModel.License);
                SettingsManager.Settings.LicIsActive = true;
                ProStatusStringsLoc.ActivationDate = SettingsManager.LicenseModel.ActivateDate;
                ProStatusStringsLoc.ExpiryDate = SettingsManager.LicenseModel.LicExpiryDate;
                if (SettingsManager.LicenseModel.UsersCount > 999999)
                {
                    ProStatusStringsLoc.UserCount = "∞";
                }
                else
                {
                    ProStatusStringsLoc.UserCount = SettingsManager.LicenseModel.UsersCount.ToString();
                }

                ProStatusStringsLoc.UserName = SettingsManager.LicenseModel.UserName;
                if (ProStatusStringsLoc.UserName != null && ProStatusStringsLoc.UserName.Contains("??"))
                {
                    ProStatusStringsLoc.UserName = "";
                }

                ProStatusStringsLoc.IsEnterprise = SettingsManager.LicenseModel.IsEnterprise;
                ProStatusStringsLoc.IsEvaluate = false;
                ProStatusStringsLoc.IsEvaluateExpired = false;

                SettingsManager.Settings.LicEvaluateCode = "";
                SettingsManager.Settings.LicIsEvaluate = false;
                ActivateSettingsPro();
                ProStatusStringsLoc.Status = LocalizationManager.GetString("ProSettingsStatusOk");
                ProStatusStringsLoc.UpdateProperties();
            }

            if (!SettingsManager.LicenseModel.IsConnectOk && (!string.IsNullOrEmpty(SettingsManager.LicenseModel.ErrorConnect) ||
                                                      !string.IsNullOrEmpty(SettingsManager.LicenseModel.ErrorParce)))
            {
                if (!string.IsNullOrEmpty(SettingsManager.LicenseModel.ErrorParce))
                {
                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationError") +
                                    Environment.NewLine +
                                    SettingsManager.LicenseModel.ErrorParce);
                }
                var isConnected = await NetLib.CheckInternetConnectionAsync();
                if (!isConnected)
                {
                    await ProStatusStringsLoc.ValidateLic();
                    ProStatusStringsLoc.UpdateProperties();
                }
                else
                {

                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationInternetError"));
                }

            }

            ProStatusStringsLoc.IsProgress = false;
            GlobalEventsApp.OnEventPro("ve");
        }

        private async void Evaluate()
        {
            SettingsManager.Settings.LicEvaluateCode = "";
            SettingsManager.Settings.LicCode = "";
            SettingsManager.Settings.LicIsActive = false;
            SettingsManager.Settings.LicIsEvaluate = false;
            ProStatusStringsLoc.IsProgress = true;
            LicenseInfo licDataUtils = new LicenseInfo();
            SettingsManager.LicenseModel = await licDataUtils.CheckTrial();
            if (SettingsManager.LicenseModel.IsConnectOk)
            {
                if (SettingsManager.LicenseModel.ErrorLic != "")
                {
                    ProStatusStringsLoc.IsProgress = false;
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationError"));
                    return;
                }

                if ((SettingsManager.LicenseModel.LicExpiryDate != DateTime.MinValue &&
                     SettingsManager.LicenseModel.LicExpiryDate < DateTime.Now) ||
                    (DateTime.Now - SettingsManager.Settings.LicEvaluateStart).Days > 40)
                {
                    ProStatusStringsLoc.IsProgress = false;
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationError"));
                    return;
                }

                SettingsManager.LicStatus = Secure.EncryptStringAES("errorholbok", SettingsManager.LicenseModel.License);
                ProStatusStringsLoc.IsEvaluate = true;
                ProStatusStringsLoc.ActivationDate = SettingsManager.LicenseModel.ActivateDate;
                ProStatusStringsLoc.ExpiryDate = SettingsManager.LicenseModel.LicExpiryDate;
                SettingsManager.Settings.LicEvaluateStart = ProStatusStringsLoc.ActivationDate;
                SettingsManager.Settings.LicEvaluateCode = SettingsManager.LicenseModel.License;
                ActivateSettings();
                ProStatusStringsLoc.Status =
                    LocalizationManager.GetString("ProSettingsStatusEvaluate");
                ProStatusStringsLoc.UpdateProperties();
                GlobalEventsApp.OnEventPro("ver");
            }

            if (!SettingsManager.LicenseModel.IsConnectOk && !string.IsNullOrEmpty(SettingsManager.LicenseModel.ErrorConnect))
            {
                if (Application.Current.MainWindow != null)
                {
                    Application.Current.MainWindow.WindowState = WindowState.Normal;
                    Application.Current.MainWindow.Activate();
                }

                RadWindow.Alert(
                    LocalizationManager.GetString("ProSettingsActivationInternetError"));
                GlobalEventsApp.OnEventPro("ve");
            }

            ProStatusStringsLoc.IsProgress = false;
        }


        private void ActivateSettings()
        {
            if (ProStatusStringsLoc.IsActivated || ProStatusStringsLoc.IsEvaluate)
            {
                ProStatusStringsLoc.Code = "";
                ProStatusStringsLoc.Email = "";
                SettingsManager.Settings.LicIsActive = ProStatusStringsLoc.IsActivated;
                SettingsManager.Settings.LicIsEvaluate = ProStatusStringsLoc.IsEvaluate;
                if (!ProStatusStringsLoc.IsEvaluate)
                {
                    SettingsManager.Settings.LicEvaluateCode = "";
                }
            }
        }

        private void ActivateSettingsPro()
        {
            if (ProStatusStringsLoc.IsActivated)
            {
                var code = SettingsManager.Settings.LicCode;
                var email = SettingsManager.Settings.LicEmail;
                ProStatusStringsLoc.Code = code;
                ProStatusStringsLoc.Email = email;
                SettingsManager.Settings.LicIsActive = ProStatusStringsLoc.IsActivated;
                SettingsManager.Settings.LicIsEvaluate = false;
            }
        }

        private void DeActivateSettings()
        {
            SettingsManager.Settings.LicEvaluateCode = "";
            SettingsManager.Settings.LicIsActive = false;
            SettingsManager.Settings.LicIsEvaluate = false;
        }

        private void ButtonBase_OnClick(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start("http://everylang.net/download");
            }
            catch
            {
                // ignore
            }
        }

        private void EvaluateClick(object sender, RoutedEventArgs e)
        {
            Evaluate();
        }

        private void ActivateClick(object sender, RoutedEventArgs e)
        {
            Activate();
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void LicenseInfoClick(object sender, RoutedEventArgs e)
        {
            try
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.Append("LICENSE=" + SettingsManager.Settings.LicCode);
                string data = "=" + HttpUtility.UrlEncode(Secure.EncryptStringAES(stringBuilder.ToString(),
                    "nfdgo45utwc3nh89tr43cbt8734htr943tg3"));
                Task.Run(() =>
                {
                    NetLib netLib = new NetLib("https://data.everylang.net/api/LicInfo", data);
                    netLib.StartPostWebRequest();
                });
                RadWindow.Alert(LocalizationManager.GetString("ProSendEmailLic") + " " +
                                SettingsManager.Settings.LicEmail);
            }
            catch (Exception)
            {
                // ignore
            }
        }

        internal static string Decompress(string compressedString)
        {
            if (String.IsNullOrEmpty(compressedString))
            {
                return compressedString;
            }

            using (var decompressedStream = new MemoryStream())
            {
                using (var compressedStream = new MemoryStream(Convert.FromBase64String(compressedString)))
                {
                    using (var decompressorStream = new DeflateStream(compressedStream, CompressionMode.Decompress))
                    {
                        decompressorStream.CopyTo(decompressedStream);
                    }

                    return Encoding.UTF8.GetString(decompressedStream.ToArray());
                }
            }
        }

        internal static string Compress(string uncompressedString)
        {
            if (String.IsNullOrEmpty(uncompressedString))
            {
                return uncompressedString;
            }

            using (var compressedStream = new MemoryStream())
            {
                using (var uncompressedStream = new MemoryStream(Encoding.UTF8.GetBytes(uncompressedString)))
                {
                    using (var compressorStream = new DeflateStream(compressedStream, CompressionMode.Compress, true))
                    {
                        uncompressedStream.CopyTo(compressorStream);
                    }

                    return Convert.ToBase64String(compressedStream.ToArray());
                }
            }
        }

        private void DeleteLicClick(object sender, RoutedEventArgs e)
        {
            ProStatusStringsLoc.Code = "";
            ProStatusStringsLoc.Email = "";
            DeActivateSettings();
            ProStatusStringsLoc.AutoStart(true);
            GlobalEventsApp.OnEventPro("ver");
        }
    }

    internal class ProStatusStrings : INotifyPropertyChanged
    {
        private static ProStatusStrings? _instance;
        private bool _isProgress;

        internal DateTime ActivationDate { get; set; }
        internal DateTime ExpiryDate { get; set; }

        public bool IsSetActivateDateStr
        {
            get { return ActivateDateStr != ""; }
        }

        public string ActivateDateStr
        {
            get
            {
                if (ActivationDate.Year != DateTime.MinValue.Year && ActivationDate.Year != 2000)
                {
                    return ActivationDate.ToString("dd MMMM yyyy");
                }
                return "";

            }
        }

        public string ExpiryDateStr
        {
            get
            {
                if (ExpiryDate.Year != DateTime.MaxValue.Year)
                {
                    return ExpiryDate.ToString("dd MMMM yyyy");
                }
                return LocalizationManager.GetString("ProSettingsNoExpiry");
            }
        }

        public string Code
        {
            get
            {
                return SettingsManager.Settings.LicCode;
            }
            set
            {
                SettingsManager.Settings.LicCode = value;
            }
        }

        public string BlockedText
        {
            get
            {
                if (SettingsManager.LicenseModel != null && SettingsManager.LicenseModel.IsBlockedByEarlyActivation)
                {
                    return LocalizationManager.GetString("ProSettingsBlockedByEarlyActivation");
                }
                if (SettingsManager.LicenseModel != null && SettingsManager.LicenseModel.IsBlockedByMonthActivateLimit)
                {
                    return LocalizationManager.GetString("ProSettingsBlockedByMonthActivateLimit");
                }
                if (SettingsManager.LicenseModel != null && SettingsManager.LicenseModel.LicIsBlocked)
                {
                    return LocalizationManager.GetString("ProSettingsActivationBlocked");
                }
                return "";
            }
        }

        public bool IsBlocked =>
            SettingsManager.LicenseModel != null && (SettingsManager.LicenseModel.IsBlockedByEarlyActivation ||
                                                     SettingsManager.LicenseModel.IsBlockedByMonthActivateLimit || SettingsManager.LicenseModel.LicIsBlocked);

        public string? Status { get; set; }

        public string Email
        {
            get => SettingsManager.Settings.LicEmail;
            set => SettingsManager.Settings.LicEmail = value;
        }

        public string? UserCount { get; set; }

        public bool IsSetUserName => !string.IsNullOrEmpty(UserName);

        public string? UserName { get; set; }

        internal bool IsEnterprise { get; set; }

        internal string IsEnterpriseStr => !IsEnterprise ? "Standart" : "Enterprise";

        public bool IsActivated => SettingsManager.LicIsActivated && !SettingsManager.Settings.LicIsEvaluate;

        public bool IsEvaluateExpired { get; set; }


        public bool IsProgress
        {
            get { return _isProgress; }
            set
            {
                _isProgress = value;
                OnPropertyChanged(nameof(IsProgress));
            }
        }

        public bool IsEvaluate
        {
            get
            {
                return SettingsManager.Settings.LicIsEvaluate;
            }
            set
            {
                SettingsManager.Settings.LicIsEvaluate = value;
            }
        }

        public bool IsNotPro
        {
            get
            {
                return !IsActivated && !IsEvaluate;
            }
        }

        public bool IsLicenseInfo
        {
            get
            {
                return (IsActivated || IsEvaluate);
            }
        }

        internal void UpdateProperties()
        {
            OnPropertyChanged(nameof(Status));
            OnPropertyChanged(nameof(ActivateDateStr));
            OnPropertyChanged(nameof(ExpiryDateStr));
            OnPropertyChanged(nameof(IsEvaluate));
            OnPropertyChanged(nameof(Email));
            OnPropertyChanged(nameof(UserCount));
            OnPropertyChanged(nameof(UserName));
            OnPropertyChanged(nameof(IsSetUserName));
            OnPropertyChanged(nameof(IsEnterpriseStr));
            OnPropertyChanged(nameof(IsEvaluateExpired));
            OnPropertyChanged(nameof(Code));
            OnPropertyChanged(nameof(IsActivated));
            OnPropertyChanged(nameof(IsLicenseInfo));
            OnPropertyChanged(nameof(IsNotPro));
            OnPropertyChanged(nameof(BlockedText));
            OnPropertyChanged(nameof(IsBlocked));
            OnPropertyChanged(nameof(IsSetActivateDateStr));
        }


        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string? propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) handler(this, new PropertyChangedEventArgs(propertyName));
        }

        public static ProStatusStrings Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ProStatusStrings();
                }
                return _instance;
            }
        }

        private bool _callbackExpEvent;

        internal async void AutoStart(bool forDelete = false)
        {
            CheckInternal();
            bool isEva = false;
            if (string.IsNullOrEmpty(Code) && !SettingsManager.Settings.LicIsEvaluate)
            {
                isEva = await StartEv();
                //if (resEv)
                //{
                //    GlobalEventsApp.OnEventPro("ve");

                //    GlobalEventsApp.OnEventProStartEv();
                //}
            }
            GlobalEventsApp.OnEventProStart();
            if (!SettingsManager.Settings.LicIsEvaluate && !forDelete && SettingsManager.Settings.LicIsActive)
            {
                await Task.Delay(19050);
            }
            _callbackExpEvent = false;

            IsProgress = true;
            Status = LocalizationManager.GetString("ProSettingsLicenseNotPro");
            SettingsManager.LicStatus = "";
            bool asd = false;
            if (Code.StartsWith("el") || (Code.StartsWith("cod") || Code.StartsWith("lic")))
            {
                asd = true;

                await ValidateLic();
            }
            if (SettingsManager.Settings.LicIsEvaluate)
            {
                asd = true;
                await ValidateEva();
            }
            if (!asd)
            {
                DeActivateSettings();
            }
            else if (!_callbackExpEvent)
            {
                var isEx = await LicHelper.CheckExp();
                if (isEx)
                {
                    DeActivateSettings();
                    GlobalEventsApp.OnEventProExp();
                }
            }
            if (ExpiryDate > DateTime.Now && (ExpiryDate - DateTime.Now).TotalDays < 3)
            {
                GlobalEventsApp.OnEventProLastDay();
            }
            IsProgress = false;
            UpdateProperties();
            GlobalEventsApp.OnEventPro("ve");
            if (isEva)
            {
                GlobalEventsApp.OnEventProStartEv();
            }
        }

        private async void CheckInternal()
        {
            if (!string.IsNullOrEmpty(SettingsManager.Settings.LicCode) && !string.IsNullOrEmpty(SettingsManager.Settings.LicEmail))
            {
                LicenseInfo licDataUtils = new LicenseInfo();
                SettingsManager.LicStatus = await licDataUtils.CheckLicInternal(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail);
                var fromProductCode = LicenseHandler.FromProductCode<StandardKey>(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail);
                if (SettingsManager.LicIsActivated)
                {
                    SettingsManager.LicStatus = Secure.EncryptStringAES("koholbok", SettingsManager.Settings.LicCode);
                    if (fromProductCode != null) ExpiryDate = fromProductCode.ExpiryDate;
                    UserCount = "";
                    Status = LocalizationManager.GetString("ProSettingsStatusOk");
                    if (ExpiryDate > DateTime.Now)
                    {
                        UpdateProperties();
                    }
                }
                GlobalEventsApp.OnEventPro("ve");
            }

        }

        private async Task ValidateEva()
        {
            try
            {
                if ((DateTime.Now - SettingsManager.Settings.LicEvaluateStart).Days > 40)
                {
                    IsEvaluateExpired = true;
                    IsEvaluate = false;
                    DeActivateSettings();
                    Status = LocalizationManager.GetString("ProSettingsExpiredEva");
                    _callbackExpEvent = true;
                    GlobalEventsApp.OnEventProExp();
                    return;
                }

                var fromProductCode =
                    LicenseHandler.FromProductCode<StandardKey>(SettingsManager.Settings.LicEvaluateCode, "eltrial");
                if (fromProductCode != null && DateTime.Now > fromProductCode.ExpiryDate)
                {
                    IsEvaluateExpired = true;
                    IsEvaluate = false;
                    DeActivateSettings();
                    Status = LocalizationManager.GetString("ProSettingsExpiredEva");
                    _callbackExpEvent = true;
                    GlobalEventsApp.OnEventProExp();
                    return;
                }

                if (fromProductCode != null) ExpiryDate = fromProductCode.ExpiryDate;
                IsEvaluate = true;

                LicenseInfo licDataUtils = new LicenseInfo();
                SettingsManager.LicenseModel = await licDataUtils.CheckTrial();
                if (SettingsManager.LicenseModel.IsConnectOk)
                {
                    if (SettingsManager.LicenseModel.ErrorLic != "")
                    {
                        IsEvaluate = false;
                        SettingsManager.LicenseModel.LicExpiryDate = DateTime.MinValue;
                        return;
                    }

                    if (SettingsManager.LicenseModel.LicIsBlocked)
                    {
                        IsEvaluateExpired = true;
                        IsEvaluate = false;
                        DeActivateSettings();
                        Status = LocalizationManager.GetString("ProSettingsExpiredEva");
                        _callbackExpEvent = true;
                        GlobalEventsApp.OnEventProExp();
                        return;
                    }

                    if ((SettingsManager.LicenseModel.LicExpiryDate != DateTime.MinValue &&
                         SettingsManager.LicenseModel.LicExpiryDate < DateTime.Now) ||
                        (DateTime.Now - SettingsManager.Settings.LicEvaluateStart).Days > 40)
                    {
                        IsEvaluateExpired = true;
                        IsEvaluate = false;
                        DeActivateSettings();
                        Status = LocalizationManager.GetString("ProSettingsExpiredEva");
                        _callbackExpEvent = true;
                        GlobalEventsApp.OnEventProExp();
                    }
                    else
                    {
                        if ((DateTime.Now - SettingsManager.Settings.LicEvaluateStart).Days < 41)
                        {
                            ActivationDate = SettingsManager.LicenseModel.ActivateDate;
                            ExpiryDate = SettingsManager.LicenseModel.LicExpiryDate;
                            SettingsManager.Settings.LicEvaluateStart = ActivationDate;
                            SettingsManager.Settings.LicEvaluateCode = SettingsManager.LicenseModel.License;
                            IsEvaluate = true;
                            Status = LocalizationManager.GetString("ProSettingsStatusEvaluate");
                            SettingsManager.LicStatus =
                                Secure.EncryptStringAES("errorholbok", SettingsManager.LicenseModel.License);
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        internal async Task ValidateLic()
        {
            LicenseInfo licDataUtils = new LicenseInfo();

            if (Code.StartsWith("el"))
            {
                string licst = await licDataUtils.CheckLicInternal(Code, Email);
                if (Secure.DecryptStringAES(licst, Code).ToLower().Contains("error"))
                {
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationErrorEmail"));
                    GlobalEventsApp.OnEventPro("ve");
                    return;
                }
                if (Secure.DecryptStringAES(licst, Code).ToLower().Contains("expired"))
                {
                    DeActivateSettings();
                    Status = LocalizationManager.GetString("ProSettingsExpired");
                    _callbackExpEvent = true;
                    GlobalEventsApp.OnEventProExp();
                    return;
                }
            }

            SettingsManager.LicenseModel = await licDataUtils.CheckLicExternal(Code);
            SettingsManager.LicenseModel.Email = SettingsManager.Settings.LicEmail;
            SettingsManager.LicenseModel.License = SettingsManager.Settings.LicCode;
            if (SettingsManager.LicenseModel.IsConnectOk)
            {
                if (SettingsManager.LicenseModel.ErrorLic != "")
                {
                    Code = "";
                    Email = "";
                    SettingsManager.Settings.LicIsActive = false;
                    DeActivateSettings();
                    return;
                }
                if (Code.StartsWith("cod") || Code.StartsWith("lic"))
                {
                    Code = SettingsManager.LicenseModel.License;
                    Email = SettingsManager.LicenseModel.Email;
                    SettingsManager.Settings.LicIsActive = true;
                }
                if (SettingsManager.LicenseModel.IsBlockedByEarlyActivation)
                {
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsBlockedByEarlyActivation"));
                    DeActivateSettings();
                    return;
                }
                if (SettingsManager.LicenseModel.IsBlockedByMonthActivateLimit)
                {
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsBlockedByMonthActivateLimit"));
                    DeActivateSettings();
                    return;
                }
                if (SettingsManager.LicenseModel.LicIsBlocked)
                {
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationBlocked"));
                    DeActivateSettings();
                    return;
                }
                if (SettingsManager.LicenseModel.LicExpiryDate != DateTime.MinValue && SettingsManager.LicenseModel.LicExpiryDate < DateTime.Now)
                {
                    DeActivateSettings();
                    Status = LocalizationManager.GetString("ProSettingsExpired");
                    _callbackExpEvent = true;
                    GlobalEventsApp.OnEventProExp();
                    return;
                }
                if (SettingsManager.LicenseModel.IsReactivated)
                {
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        Application.Current.MainWindow.Activate();
                    }

                    RadWindow.Alert(LocalizationManager.GetString("ProSettingsActivationReactivated") + " " + SettingsManager.LicenseModel.LastSeat);
                }

                SettingsManager.LicStatus = Secure.EncryptStringAES("koholbok", SettingsManager.LicenseModel.License);
                ActivationDate = SettingsManager.LicenseModel.ActivateDate;
                ExpiryDate = SettingsManager.LicenseModel.LicExpiryDate;
                if (SettingsManager.LicenseModel.UsersCount > 999999)
                {
                    UserCount = "∞";
                }
                else
                {
                    UserCount = SettingsManager.LicenseModel.UsersCount.ToString();
                }
                UserName = SettingsManager.LicenseModel.UserName;
                if (UserName != null && UserName.Contains("??"))
                {
                    UserName = "";
                }
                SettingsManager.Settings.LicEvaluateCode = "";
                SettingsManager.Settings.LicIsEvaluate = false;
                IsEnterprise = SettingsManager.LicenseModel.IsEnterprise;
                IsEvaluate = false;
                IsEvaluateExpired = false;
                Status = LocalizationManager.GetString("ProSettingsStatusOk");
                UpdateProperties();

            }
            if (!SettingsManager.LicenseModel.IsConnectOk && !string.IsNullOrEmpty(SettingsManager.LicenseModel.ErrorConnect))
            {
                var isConnected = await NetLib.CheckInternetConnectionAsync();
                if (!isConnected)
                {
                    SettingsManager.LicStatus = await licDataUtils.CheckLicInternal(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail);
                    var fromProductCode = LicenseHandler.FromProductCode<StandardKey>(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail);
                    if (!SettingsManager.LicIsActivated)
                    {
                        DeActivateSettings();
                    }
                    else
                    {
                        SettingsManager.LicStatus = Secure.EncryptStringAES("koholbok", SettingsManager.Settings.LicCode);
                        if (fromProductCode != null)
                        {
                            ExpiryDate = fromProductCode.ExpiryDate;
                            IsEnterprise = fromProductCode.LicensedEdition == StandardKey.Edition.Enterprise;
                        }

                        UserCount = "";
                        Status = LocalizationManager.GetString("ProSettingsStatusOk");
                        if (ExpiryDate < DateTime.Now)
                        {
                            DeActivateSettings();
                            Status = LocalizationManager.GetString("ProSettingsExpired");
                            _callbackExpEvent = true;
                            GlobalEventsApp.OnEventProExp();
                        }
                    }
                }
                else
                {
                    if (CheckFuck())
                    {
                        DeActivateSettings();
                    }
                }
            }
        }

        private static bool CheckFuck()
        {
            try
            {
                if (File.Exists(@"C:\Windows\System32\drivers\etc\hosts"))
                {
                    var fs = new FileStream(@"C:\Windows\System32\drivers\etc\hosts", FileMode.Open, FileAccess.Read, FileShare.Read);
                    using var sr = new StreamReader(fs);
                    var content = sr.ReadToEnd().Replace(" ", "");
                    if (content.Contains("127.0.0.1data.everylang.net") || content.Contains("127.0.0.137.140.192.131"))
                    {
                        return true;
                    }
                }
            }
            catch
            {
                // ignore
            }
            return false;
        }

        internal static async Task<bool> StartEv()
        {
            if (await IsFirstStart() is false)
            {
                return false;
            }
            SettingsManager.Settings.LicEvaluateCode = "";
            SettingsManager.Settings.LicCode = "";
            SettingsManager.Settings.LicIsActive = false;
            SettingsManager.Settings.LicIsEvaluate = false;
            LicenseInfo licDataUtils = new LicenseInfo();
            SettingsManager.LicenseModel = await licDataUtils.CheckTrial();
            if (SettingsManager.LicenseModel.IsConnectOk)
            {
                if (SettingsManager.LicenseModel.ErrorLic != "")
                {
                    return false;
                }
                if ((SettingsManager.LicenseModel.LicExpiryDate != DateTime.MinValue && SettingsManager.LicenseModel.LicExpiryDate < DateTime.Now) || (DateTime.Now - SettingsManager.Settings.LicEvaluateStart).Days > 40)
                {
                    return false;
                }
                SettingsManager.LicStatus = Secure.EncryptStringAES("errorholbok", SettingsManager.LicenseModel.License);
                SettingsManager.Settings.LicEvaluateStart = DateTime.Now;
                SettingsManager.Settings.LicEvaluateCode = SettingsManager.LicenseModel.License;
                SettingsManager.Settings.LicIsEvaluate = true;
                //VMContainer.Instance.HistoryViewModel.IsEnabled = true;
                //VMContainer.Instance.ClipboardViewModel.IsEnabled = true;
                //VMContainer.Instance.ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcutIsOn = true;
                //VMContainer.Instance.ClipboardSettingsViewModel.ClipboardPasteRoundIsOn = true;
                //VMContainer.Instance.DiaryViewModel.IsEnabled = true;
                //VMContainer.Instance.UniversalWindowSettingsViewModel.UniversalWindowIsOn = true;
                //VMContainer.Instance.LangFlagSettingsViewModel.IsLangInfoInTray = true;
                //VMContainer.Instance.SnippetsViewModel.IsOnSnippets = true;
                //VMContainer.Instance.LangFlagSettingsViewModel.IsLangInfoWindowShowForCaret = true;
                //VMContainer.Instance.LangFlagSettingsViewModel.IsLangInfoWindowShowForMouse = true;
                //VMContainer.Instance.SwitcherSettingsViewModel.SwitcherIsOn = true;
                //VMContainer.Instance.ProgramsSetLayoutViewModel.jgebhdhs = true;
                //VMContainer.Instance.AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch = true;
                return true;
            }
            return false;
        }

        internal static Task<bool> IsFirstStart()
        {
            return Task.Run(() =>
            {
                string UrlToCheck = "https://data.everylang.net/api/CheckMachineId";
                string data = "=" + WebUtility.UrlEncode(SettingsManager.MachineId);
                NetLib netLib = new NetLib(UrlToCheck, data);
                var res = netLib.StartPostWebRequest();
                if (!res.WithError)
                {
                    if (res.ResultText != null && res.ResultText.Contains("true"))
                    {
                        return true;
                    }
                }
                return false;
            });
        }

        private void DeActivateSettings()
        {
            SettingsManager.Settings.LicEvaluateCode = "";
            SettingsManager.Settings.LicEvaluateStart = DateTime.MaxValue;
            SettingsManager.Settings.LicIsActive = false;
            SettingsManager.Settings.LicIsEvaluate = false;
            GlobalEventsApp.OnEventPro("ver");
        }
    }
}
