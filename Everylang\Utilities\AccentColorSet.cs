﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Media;

namespace Everylang.App.Utilities
{
    internal enum ThemeVariation
    {
        Dark = 0,
        Light = 1
    }

    internal class AccentColorSet
    {
        static AccentColorSet[]? _accentColorSetCollection;
        static AccentColorSet? _activeColorSet;
        private uint _colorSet;

        AccentColorSet(uint colorSet, bool active)
        {
            _colorSet = colorSet;
            Active = active;
        }

        // would read the current theme variation from the registry
        internal static ThemeVariation Variation
        {
            get
            {
                var reg = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                if (reg == null)
                {
                    return ThemeVariation.Light;
                }
                var regValue = reg.GetValue("AppsUseLightTheme");
                if (regValue != null)
                {
                    return (ThemeVariation)regValue;
                }
                return ThemeVariation.Light;
            }
        }

        internal static Color RgbFromArgbAndBackgroundColor(Color targetColor, Color backgroundColor)
        {
            var baseAlpha = targetColor.A / 255.0;
            var reverseAlpha = 1.0 - baseAlpha;
            byte redValue = (byte)((targetColor.R * baseAlpha) + (backgroundColor.R * reverseAlpha));
            byte greenValue = (byte)((targetColor.G * baseAlpha) + (backgroundColor.G * reverseAlpha));
            byte blueValue = (byte)((targetColor.B * baseAlpha) + (backgroundColor.B * reverseAlpha));

            return Color.FromArgb(255, redValue, greenValue, blueValue);
        }

        internal static byte TransfromPercent(double d)
        {
            return (byte)(d * 255.0);
        }

        internal static AccentColorSet[]? AccentColorSetCollection
        {
            get
            {
                if (_accentColorSetCollection == null)
                {
                    UInt32 colorSetCount = UXTheme.GetImmersiveColorSetCount();

                    List<AccentColorSet> colorSets = new List<AccentColorSet>();
                    for (UInt32 i = 0; i < colorSetCount; i++)
                    {
                        colorSets.Add(new AccentColorSet(i, false));
                    }

                    AccentColorSetCollection = colorSets.ToArray();
                }

                return _accentColorSetCollection;
            }
            private set => _accentColorSetCollection = value;
        }

        internal static AccentColorSet? ActiveSet
        {
            get
            {
                UInt32 activeSet = UXTheme.GetImmersiveUserColorSetPreference(false, false);
                ActiveSet = AccentColorSetCollection?[Math.Min(activeSet, AccentColorSetCollection.Length - 1)];
                return _activeColorSet;
            }
            private set
            {
                if (_activeColorSet != null) _activeColorSet.Active = false;

                if (value != null)
                {
                    value.Active = true;
                    _activeColorSet = value;
                }
            }
        }

        internal Boolean Active { get; private set; }

        internal Color this[String colorName]
        {
            get
            {
                IntPtr name = IntPtr.Zero;
                UInt32 colorType;

                try
                {
                    name = Marshal.StringToHGlobalUni("Immersive" + colorName);
                    colorType = UXTheme.GetImmersiveColorTypeFromName(name);
                    try
                    {
                        if (colorType == 0xFFFFFFFF) throw new InvalidOperationException();
                    }
                    catch (Exception)
                    {
                        MessageBox.Show("The OS system on your machine is not Windows 10. This project required to use Windows 10.");
                    }
                }
                finally
                {
                    if (name != IntPtr.Zero)
                    {
                        Marshal.FreeHGlobal(name);
                        name = IntPtr.Zero;
                    }
                }

                return this[colorType];
            }
        }

        internal Color this[UInt32 colorType]
        {
            get
            {
                UInt32 nativeColor = UXTheme.GetImmersiveColorFromColorSetEx(this._colorSet, colorType, false, 0);
                //if (nativeColor == 0)
                //    throw new InvalidOperationException();
                return Color.FromArgb(
                    (Byte)((0xFF000000 & nativeColor) >> 24),
                    (Byte)((0x000000FF & nativeColor) >> 0),
                    (Byte)((0x0000FF00 & nativeColor) >> 8),
                    (Byte)((0x00FF0000 & nativeColor) >> 16)
                    );
            }
        }

        //internal UInt32 ColorSet
        //{
        //    get { return _colorSet; }
        //    set { _colorSet = value; }
        //}

        //// HACK: GetAllColorNames collects the available color names by brute forcing the OS function.
        ////   Since there is currently no known way to retrieve all possible color names,
        ////   the method below just tries all indices from 0 to 0xFFF ignoring errors.
        //internal List<String> GetAllColorNames()
        //{
        //    List<String> allColorNames = new List<String>();
        //    for (UInt32 i = 0; i < 0xFFF; i++)
        //    {
        //        IntPtr typeNamePtr = UXTheme.GetImmersiveColorNamedTypeByIndex(i);
        //        if (typeNamePtr != IntPtr.Zero)
        //        {
        //            IntPtr typeName = (IntPtr)Marshal.PtrToStructure(typeNamePtr, typeof(IntPtr))!;
        //            allColorNames.Add(Marshal.PtrToStringUni(typeName)!);
        //        }
        //    }

        //    return allColorNames;
        //}

        static class UXTheme
        {
            [DllImport("uxtheme.dll", EntryPoint = "#98", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
            internal static extern UInt32 GetImmersiveUserColorSetPreference(Boolean forceCheckRegistry, Boolean skipCheckOnFail);

            [DllImport("uxtheme.dll", EntryPoint = "#94", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
            internal static extern UInt32 GetImmersiveColorSetCount();

            [DllImport("uxtheme.dll", EntryPoint = "#95", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
            internal static extern UInt32 GetImmersiveColorFromColorSetEx(UInt32 immersiveColorSet, UInt32 immersiveColorType,
                Boolean ignoreHighContrast, UInt32 highContrastCacheMode);

            [DllImport("uxtheme.dll", EntryPoint = "#96", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
            internal static extern UInt32 GetImmersiveColorTypeFromName(IntPtr name);

            [DllImport("uxtheme.dll", EntryPoint = "#100", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto)]
            internal static extern IntPtr GetImmersiveColorNamedTypeByIndex(UInt32 index);
        }
    }
}
