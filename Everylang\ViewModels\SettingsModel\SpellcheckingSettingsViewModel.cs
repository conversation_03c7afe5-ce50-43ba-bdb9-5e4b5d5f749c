﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.SpellCheck;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class SpellcheckingSettingsViewModel : ViewModelBase
    {
        public string Shortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SpellCheckShortcut);
            }
            set
            {
                SettingsManager.Settings.SpellCheckShortcut = value;
                base.OnPropertyChanged();
            }
        }

        internal bool CheckSpellingIsOn
        {
            get
            {
                return SettingsManager.Settings.SpellCheckIsOn;
            }
            set
            {
                SettingsManager.Settings.SpellCheckIsOn = value;
                if (!value)
                {
                    SpellCheckManager.Instance.Stop();
                }
                else
                {
                    SpellCheckManager.Instance.Start();
                }
                base.OnPropertyChanged();
            }
        }

        public bool CheckSpellingCloseByTimer
        {
            get
            {
                return SettingsManager.Settings.SpellCheckCloseByTimer;
            }
            set
            {
                SettingsManager.Settings.SpellCheckCloseByTimer = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsPlatformSupportedSpellCheckWhileTyping
        {
            get
            {
                //                return true;
                return PlatformSpellCheck.SpellChecker.IsPlatformSupported();
            }
        }

        public bool CheckSpellingWhileTyping
        {
            get
            {
                return SettingsManager.Settings.SpellCheckWhileTyping;
            }
            set
            {
                SettingsManager.Settings.SpellCheckWhileTyping = value;
                base.OnPropertyChanged();
            }
        }

        public bool CheckSpellingWhileTypingSoundOn
        {
            get
            {
                return SettingsManager.Settings.SpellCheckWhileTypingSoundOn;
            }
            set
            {
                SettingsManager.Settings.SpellCheckWhileTypingSoundOn = value;
                base.OnPropertyChanged();
            }
        }

        public bool CheckSpellingUseNumber
        {
            get
            {
                return SettingsManager.Settings.SpellCheckWhileTypingUseNumber;
            }
            set
            {
                SettingsManager.Settings.SpellCheckWhileTypingUseNumber = value;
                base.OnPropertyChanged();
            }
        }


    }
}
