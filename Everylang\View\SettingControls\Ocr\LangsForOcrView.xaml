﻿<UserControl x:Class="Everylang.App.View.SettingControls.Ocr.LangsForOcrView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" x:ClassModifier="internal"
             DataContext="{Binding RelativeSource={RelativeSource Self}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
            <StackPanel  Margin="15,10,0,0" Orientation="Horizontal">
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock Margin="10,0,0,0" FontSize="15" VerticalAlignment="Center" HorizontalAlignment="Left"   Text="{telerik:LocalizableResource Key=OcrDescLang}" />
            </StackPanel>
            <telerik:RadListBox Grid.Row="1" Width="400"  ItemsSource="{Binding Languages}" Margin="0,5,0,10"  ScrollViewer.VerticalScrollBarVisibility="Visible" ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <CheckBox MinHeight="0"  MinWidth="0" Padding="3,0,0,0"  Margin="0" Focusable="False" IsChecked="{Binding IsChecked}" Checked="ToggleButton_OnChecked" Unchecked="ToggleButton_OnChecked">
                            <TextBlock
                                FontSize="15"
                                Margin="10,0,0,0"
                                FontWeight="Bold"
                                Text="{Binding Path=Item.Name}" />
                        </CheckBox>
                    </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
                <telerik:RadListBox.ItemContainerStyle>
                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                        <Setter Property="Padding" Value="3" />
                        <Setter Property="Margin" Value="0" />
                        <Setter Property="MinHeight" Value="0" />
                        <Setter Property="Height" Value="35" />
                    </Style>
                </telerik:RadListBox.ItemContainerStyle>
            </telerik:RadListBox>
    </Grid>
</UserControl>
