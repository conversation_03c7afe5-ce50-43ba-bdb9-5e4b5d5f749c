﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.About
{
    /// <summary>
    /// Interaction logic for AboutControl.xaml
    /// </summary>
    internal partial class AboutControl : UserControl
    {
        internal AboutControl()
        {
            InitializeComponent();
        }

        private void GoHardcodet(object sender, RoutedEventArgs e)
        {
            StartProcess("http://www.hardcodet.net/projects/wpf-notifyicon");
        }

        private void GoNewtonsoft(object sender, RoutedEventArgs e)
        {
            StartProcess("http://json.codeplex.com/");
        }

        private void ExtendedWPFToolkit(object sender, RoutedEventArgs e)
        {
            StartProcess("http://wpftoolkit.codeplex.com/");
        }

        private void GoIconpharm(object sender, RoutedEventArgs e)
        {
            StartProcess("http://www.iconpharm.com");
        }

        private void GoToWebsite(object sender, RoutedEventArgs e)
        {
            StartProcess("http://everylang.net");
        }

        private void GoToContactForm(object sender, RoutedEventArgs e)
        {
            StartProcess("http://everylang.net/contact");
        }

        private void GoToWebsiteUpdate(object sender, RoutedEventArgs e)
        {
            StartProcess("http://everylang.net/download");
        }

        private void StartProcess(string data)
        {
            try
            {
                Process.Start(data);
            }
            catch
            { }
        }

        private void ShowLicense(object sender, RoutedEventArgs e)
        {
            LicenseWindow licenseWindow = new LicenseWindow();
            licenseWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
            licenseWindow.Owner = Application.Current.MainWindow;
            licenseWindow.ShowDialog();

        }

        private void ResetSettings(object sender, RoutedEventArgs e)
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("AboutResetSettingsQuestion"),
                Closed = (s, args) =>
                {
                    if (args.DialogResult == true)
                    {
                        SettingsManager.DeleteAllSettings();
                        GlobalEventsApp.OnEventRestart(false);
                    }
                }
            });
        }

        private void GoToContactFormMail(object sender, RoutedEventArgs e)
        {
            StartProcess("mailto:<EMAIL>");
        }
    }
}
