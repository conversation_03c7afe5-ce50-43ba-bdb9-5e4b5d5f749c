﻿using Everylang.App.Data.DataModel;
using Everylang.App.SettingsApp;
using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Helpers
{
    [ValueConversion(typeof(string), typeof(string))]
    class ConvertFromLang : IValueConverter
    {

        public object? Convert(object? value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                string result = (string)value;

                result = LocalizationManager.GetString("FromLang") + " " + result;
                return result;
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                string result = (string)value;
                result = result.Replace(LocalizationManager.GetString("FromLang") + " ", "");
                return result;
            }
            return null;
        }
    }

    [ValueConversion(typeof(string), typeof(string))]
    class ConvertToLang : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                string result = (string)value;
                result = LocalizationManager.GetString("ToLang") + " " + result;
                return result;
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                string result = (string)value;
                result = result.Replace(LocalizationManager.GetString("ToLang") + " ", "");
                return result;
            }
            return null;

        }
    }

    class AutoSwitchRuleActionConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value != null)
            {
                if ((int)value == SettingsManager.Settings.AutoSwitcherCountCheckRule)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionConvert");
                }
                if ((int)value == -1)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionNotConvert");
                }
                if ((int)value > -1 && (int)value < SettingsManager.Settings.AutoSwitcherCountCheckRule)
                {
                    return LocalizationManager.GetString("AutoSwitcherSettingsRuleActionIntermediate");
                }
                return null;
            }
            return null;
        }

        public object? ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return null;

        }
    }

    [ValueConversion(typeof(bool), typeof(Visibility))]
    internal sealed class BoolToVisibilityConverter : IValueConverter
    {
        internal Visibility TrueValue { get; set; }
        internal Visibility FalseValue { get; set; }

        internal BoolToVisibilityConverter()
        {
            // set defaults
            TrueValue = Visibility.Visible;
            FalseValue = Visibility.Collapsed;
        }

        public object? Convert(object? value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (!(value is bool))
                return null;
            return (bool)value ? TrueValue : FalseValue;
        }

        public object? ConvertBack(object value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (Equals(value, TrueValue))
                return true;
            if (Equals(value, FalseValue))
                return false;
            return null;
        }
    }


    [ValueConversion(typeof(bool), typeof(Visibility))]
    internal sealed class BoolToVisibilityRtfConverter : IValueConverter
    {
        internal Visibility TrueValue { get; set; }
        internal Visibility FalseValue { get; set; }

        internal BoolToVisibilityRtfConverter()
        {
            // set defaults
            TrueValue = Visibility.Visible;
            FalseValue = Visibility.Collapsed;
        }

        public object? Convert(object? value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (!(value is ClipboardDataModel))
                return null;
            return ((ClipboardDataModel)value).IsRtf ? TrueValue : FalseValue;
        }

        public object? ConvertBack(object value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (Equals(value, TrueValue))
                return true;
            if (Equals(value, FalseValue))
                return false;
            return null;
        }
    }

    [ValueConversion(typeof(bool), typeof(Visibility))]
    internal sealed class BoolToVisibilityHtmlConverter : IValueConverter
    {
        internal Visibility TrueValue { get; set; }
        internal Visibility FalseValue { get; set; }

        internal BoolToVisibilityHtmlConverter()
        {
            // set defaults
            TrueValue = Visibility.Visible;
            FalseValue = Visibility.Collapsed;
        }

        public object? Convert(object? value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (!(value is ClipboardDataModel))
                return null;
            return ((ClipboardDataModel)value).IsHtml ? TrueValue : FalseValue;
        }

        public object? ConvertBack(object? value, Type targetType,
            object parameter, CultureInfo culture)
        {
            if (Equals(value, TrueValue))
                return true;
            if (Equals(value, FalseValue))
                return false;
            return null;
        }
    }

    class IndexConverterForFastAction : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            int index = (int)value;
            if (index > 8)
            {
                return "";
            }
            return index + 1;
        }
        public object ConvertBack(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            throw new NotSupportedException("Cannot convert back");
        }
    }

}
