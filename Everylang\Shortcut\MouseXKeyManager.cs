﻿using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.Utilities;
using NHotkey;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Shortcut
{
    static class MouseXKeyManager
    {
        private static List<MouseKeyData> _keyDataList;
        private static bool _isHandled;
        internal static bool FromMouseX { get; set; }
        internal static void Start()
        {
            HookCallBackMouseDown.CallbackEventHandler += CallbackEventHandlerMouseDown;
            HookCallBackMouseUp.CallbackEventHandler += CallbackEventHandlerMouseUp;
            _keyDataList = new List<MouseKeyData>();
            _keyDataList.Add(new MouseKeyData() { MouseButton = GHMouseButtons.XButton1, MouseButtonName = "XButton1" });
            _keyDataList.Add(new MouseKeyData() { MouseButton = GHMouseButtons.XButton2, MouseButtonName = "XButton2" });
            //            _keyDataList.Add(new MouseKeyData() { MouseButton = MouseButtonsMy.XButton3, MouseButtonName = "XButton3" });
            //            _keyDataList.Add(new MouseKeyData() { MouseButton = MouseButtonsMy.XButton4, MouseButtonName = "XButton4" });
        }

        private static void CallbackEventHandlerMouseUp(GlobalMouseEventArgs e)
        {
            if (_isHandled)
            {
                e.Handled = true;
            }
        }

        private static void CallbackEventHandlerMouseDown(GlobalMouseEventArgs e)
        {
            FromMouseX = false;
            _isHandled = false;
            if (!CheckActiveProcessFileName.CheckHotKeys())
            {
                return;
            }
            if (e.Button == GHMouseButtons.XButton1 || e.Button == GHMouseButtons.XButton2)
            {
                var handler = _keyDataList.First(x => x.MouseButton == e.Button);
                if (handler != null && handler.Handler != null)
                {
                    _isHandled = true;
                    e.Handled = true;
                    FromMouseX = true;
                    var eventHandler = handler.Handler;
                    eventHandler(null, null);
                }
            }
        }

        internal static List<string> GetFree(string nameEvent)
        {
            var freeList = _keyDataList.Where(x => string.IsNullOrEmpty(x.NameEvent) || x.NameEvent == nameEvent).Select(x => x.MouseButtonName);
            return freeList.ToList();
        }

        internal static void AddNew(string name, string shortcut, EventHandler<HotkeyEventArgs> handler)
        {
            var mouseKey = ShortcutManager.GetMouseKeyFromShortcut(shortcut);
            if (!string.IsNullOrEmpty(mouseKey))
            {
                MouseKeyData mouseKeyData = _keyDataList.FirstOrDefault(x => x.MouseButtonName == mouseKey);
                if (mouseKeyData != null)
                {
                    mouseKeyData.NameEvent = name;
                    mouseKeyData.Handler = handler;
                }
            }

        }

        internal static void Remove(string name)
        {
            MouseKeyData mouseKeyData = _keyDataList.FirstOrDefault(x => x.NameEvent == name);
            if (mouseKeyData != null)
            {
                mouseKeyData.Handler = null;
                mouseKeyData.NameEvent = "";
            }
        }

        class MouseKeyData
        {
            internal string NameEvent { get; set; }
            internal EventHandler<HotkeyEventArgs> Handler { get; set; }
            internal GHMouseButtons MouseButton { get; set; }
            internal string MouseButtonName { get; set; }
        }
    }
}
