﻿using System.Text;
using System.Windows.Input;

namespace Everylang.App.Shortcut
{
    internal class HotKey
    {
        internal Key Key { get; }

        internal ModifierKeys ModifierKeys { get; }

        internal HotKey(Key key)
        {
            Key = key;
            ModifierKeys = ModifierKeys.None;
        }

        internal HotKey(Key key, ModifierKeys modifiers)
        {
            Key = key;
            ModifierKeys = modifiers;
        }

        public override string ToString()
        {
            var str = new StringBuilder();

            if (ModifierKeys.HasFlag(ModifierKeys.Control))
                str.Append("Ctrl + ");
            if (ModifierKeys.HasFlag(ModifierKeys.Shift))
                str.Append("Shift + ");
            if (ModifierKeys.HasFlag(ModifierKeys.Alt))
                str.Append("Alt + ");
            if (ModifierKeys.HasFlag(ModifierKeys.Windows))
                str.Append("Win + ");
            str.Append(GetStringFromKey(Key));
            return str.ToString();
        }

        private string GetStringFromKey(Key key)
        {
            string resKeys = key.ToString();
            switch (key)
            {
                case Key.OemTilde:
                    resKeys = "~";
                    break;
                case Key.D0:
                    resKeys = "0";
                    break;
                case Key.D1:
                    resKeys = "1";
                    break;
                case Key.D2:
                    resKeys = "2";
                    break;
                case Key.D3:
                    resKeys = "3";
                    break;
                case Key.D4:
                    resKeys = "4";
                    break;
                case Key.D5:
                    resKeys = "5";
                    break;
                case Key.D6:
                    resKeys = "6";
                    break;
                case Key.D7:
                    resKeys = "7";
                    break;
                case Key.D8:
                    resKeys = "8";
                    break;
                case Key.D9:
                    resKeys = "9";
                    break;
            }
            return resKeys;
        }
    }
}
