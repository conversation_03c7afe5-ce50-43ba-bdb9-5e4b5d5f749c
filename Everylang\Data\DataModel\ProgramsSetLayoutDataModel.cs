﻿using Everylang.App.SwitcherLang;
using LiteDB;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataModel
{
    public class ProgramsSetLayoutDataModel
    {
        public ProgramsSetLayoutDataModel()
        {
            Lang = AllLang.FirstOrDefault();
        }
        internal ObjectId Id { get; set; }
        internal string? Program { get; set; } = "";
        internal string? Title { get; set; } = "";

        public string? ProgramTitle
        {
            get
            {
                if (Title != "")
                {
                    return "<TITLE> " + Title;
                }
                return Program;
            }
        }

        public string Lang { get; set; }

        public List<string> AllLang
        {
            get
            {
                var langList = new List<string>();
                var list = KeyboardLayoutMethods.GetInputLangs();
                foreach (var cultureInfo in list)
                {
                    langList.Add(cultureInfo.TwoLetterISOLanguageName.ToUpper());
                }
                return langList;
            }
        }

    }
}
