﻿<UserControl x:Class="Everylang.App.View.SettingControls.ProSettings.ProSettingsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             xmlns:proSettings="clr-namespace:Everylang.App.View.SettingControls.ProSettings"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static proSettings:ProStatusStrings.Instance}}">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <Style x:Key="HideLicForLicStyle" TargetType="{x:Type StackPanel}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsActivated}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsActivated}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="HideLicForPassStyle" TargetType="{x:Type TextBox}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsActivated}" Value="True">
                        <Setter Property="IsReadOnly" Value="True"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsActivated}" Value="False">
                        <Setter Property="IsReadOnly" Value="False"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="True">
                        <Setter Property="IsEnabled" Value="False"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="False">
                        <Setter Property="IsEnabled" Value="True"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ActivateProStyle" TargetType="{x:Type telerik:RadButton}" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsActivated}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsActivated}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="ActivateStyle" TargetType="{x:Type telerik:RadButton}" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsActivated}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsActivated}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="True">
                        <Setter Property="IsEnabled" Value="False"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="False">
                        <Setter Property="IsEnabled" Value="True"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="EvaluateStyle" TargetType="{x:Type telerik:RadButton}" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsNotPro}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsNotPro}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsEvaluateExpired}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="True">
                        <Setter Property="IsEnabled" Value="False"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="False">
                        <Setter Property="IsEnabled" Value="True"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="IsActivateStyle" TargetType="{x:Type StackPanel}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsEvaluate}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsEvaluate}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsSetActivateDateStr}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="LicenseInfoStyle" TargetType="{x:Type StackPanel}" >
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsLicenseInfo}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsLicenseInfo}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="LicenseBlockedStyle" TargetType="{x:Type StackPanel}" >
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsBlocked}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsBlocked}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ProgressText" TargetType="{x:Type TextBlock}" >
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsProgress}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ProgressBar" TargetType="{x:Type telerik:RadProgressBar}" BasedOn="{StaticResource {x:Type telerik:RadProgressBar}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsProgress}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProgress}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="Owner" TargetType="{x:Type StackPanel}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsSetUserName}" Value="True">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsSetUserName}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsEvaluate}" Value="True">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <StackPanel Margin="20,10,0,0">
            <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=ProSettingsHeader}" />

            <TextBlock Style="{StaticResource ProgressText}"  HorizontalAlignment="Left" Margin="0,7,0,0" FontSize="16" FontWeight="Bold" TextWrapping="Wrap" Text="{Binding Path=Status}" />
            <telerik:RadProgressBar Style="{StaticResource ProgressBar}" IsIndeterminate="True" Margin="0,7,0,0"/>

            <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Width="150" Margin="0,10,0,0" Content="{telerik:LocalizableResource Key=ProSettingsEvaluation}" Click="EvaluateClick" Style="{StaticResource EvaluateStyle}"/>

            <StackPanel HorizontalAlignment="Left" Width="310" Margin="0,10,0,0"
                        Style="{StaticResource HideLicForLicStyle}">
                <TextBlock FontSize="14"
                           Text="{telerik:LocalizableResource Key=ProSettingsInput}" />
                <TextBox HorizontalContentAlignment="Left" Margin="0,5,0,0"
                         Text="{Binding Path=Code, UpdateSourceTrigger=PropertyChanged}" />
            </StackPanel>
            <StackPanel HorizontalAlignment="Left" Width="310" Margin="0,10,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ProSettingsLicenseEmail}"/>
                <TextBox HorizontalContentAlignment="Left" Margin="0,5,0,0" Text="{Binding Path=Email, UpdateSourceTrigger=PropertyChanged}"  IsReadOnly="{Binding IsActivated}"/>
            </StackPanel>

            <StackPanel HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,10,0,0">
                <telerik:RadButton Focusable="False" Width="150" Margin="0,0,0,0"
                                   Content="{telerik:LocalizableResource Key=ProSettingsActivation}" Click="ActivateClick"
                                   Style="{StaticResource ActivateStyle}" />
                <telerik:RadButton Focusable="False" Width="150" Margin="10,0,0,0"
                                   Content="{telerik:LocalizableResource Key=ProSettingsPurchase}" Style="{StaticResource ActivateStyle}"
                                   Click="ButtonBase_OnClick" />
                <telerik:RadButton Focusable="False" HorizontalAlignment="Left" Margin="0,0,0,0"
                                   Style="{StaticResource ActivateProStyle}"
                                   Content="{telerik:LocalizableResource Key=ProSettingsDelete}" Click="DeleteLicClick" />
            </StackPanel>
            <StackPanel Margin="0,10,0,0" Style="{StaticResource LicenseBlockedStyle}" >
                <TextBlock  FontSize="15" FontWeight="DemiBold" Text="{Binding Path=BlockedText}" Margin="0,5,20,0" TextWrapping="Wrap"/>
            </StackPanel>

            <StackPanel Margin="0,10,0,0" Style="{StaticResource LicenseInfoStyle}" >
                <TextBlock  FontSize="15" FontWeight="Bold"
                           Text="{telerik:LocalizableResource Key=ProSettingsLicenseInfo}" />
                <StackPanel HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,5,0,0" Visibility="{Binding IsSetActivateDateStr, Converter={StaticResource BoolToVis}}">
                    <TextBlock Margin="0,0,0,0" FontSize="14"
                               Text="{telerik:LocalizableResource Key=ProSettingsLicenseActivateDate}" />
                    <TextBlock Margin="10,0,0,0" FontSize="14" Text="{Binding Path=ActivateDateStr}" />
                </StackPanel>
                <StackPanel HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBlock Margin="0,0,0,0" FontSize="14"
                               Text="{telerik:LocalizableResource Key=ProSettingsLicenseExpiryDate}" />
                    <TextBlock Margin="10,0,0,0" FontSize="14" Text="{Binding Path=ExpiryDateStr}" />
                </StackPanel>
                <StackPanel Style="{StaticResource Owner}" HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBlock Margin="0,0,0,0" FontSize="14"
                               Text="{telerik:LocalizableResource Key=ProSettingsLicenseUserName}" />
                    <TextBlock Margin="10,0,0,0" FontSize="14" Text="{Binding Path=UserName}" />
                </StackPanel>
                <StackPanel Style="{StaticResource IsActivateStyle}" HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBlock Margin="0,0,0,0" FontSize="14"
                               Text="{telerik:LocalizableResource Key=ProSettingsLicenseUsersCount}" />
                    <TextBlock Margin="10,0,0,0" FontSize="14" Text="{Binding Path=UserCount}" />
                </StackPanel>
                <!--<StackPanel Style="{StaticResource IsActivateStyle}" HorizontalAlignment="Left" Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBlock Margin="0,0,0,0" FontSize="14" telerik:LocalizableResource Key=.Uid="ProSettingsLicenseCountFreeSeats"
                               Text="{telerik:LocalizableResource Key=}" />
                    <TextBlock Margin="10,0,0,0" FontSize="14" Text="{Binding Path=CountFreeSeats}" />
                </StackPanel>-->
                <StackPanel>
                    <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Margin="0,20,0,0" Content="{telerik:LocalizableResource Key=ProSettingsGetData}" Click="LicenseInfoClick" />
                </StackPanel>
                
            </StackPanel>



        </StackPanel>
    </Grid>
</UserControl>

