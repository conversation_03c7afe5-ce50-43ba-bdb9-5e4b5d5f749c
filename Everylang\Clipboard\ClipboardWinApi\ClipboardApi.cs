﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading;
using Vanara.PInvoke;

namespace Everylang.App.Clipboard.ClipboardWinApi
{
    internal class ClipboardApi : IDisposable
    {
        internal ClipboardApi()
        {
            for (var i = 0; i < 5; i++)
            {
                if (User32.OpenClipboard())
                {
                    return;
                }
                Thread.Sleep(50);
            }
            ClipboardApiException.ThrowIfFailed(true, "OpenClipboard");
        }

        internal bool EmptyClipboard()
        {
            return User32.EmptyClipboard();
        }

        internal bool IsClipboardContainsRtf()
        {
            var formatsList = new List<ulong>();
            uint format = 0;
            while ((format = User32.EnumClipboardFormats(format)) != 0)
            {
                formatsList.Add(format);
            }
            if (formatsList.Contains(7) || formatsList.Contains(49161) || formatsList.Contains(49171) || formatsList.Contains(49311) || formatsList.Contains(50377))
            {
                return true;
            }
            return false;
        }


        internal BlockingCollection<ClipboardData> GetClipboard()
        {
            var clipData = new BlockingCollection<ClipboardData>();
            uint format = 0;
            while ((format = User32.EnumClipboardFormats(format)) != 0)
            {
                //                Trace.WriteLine("format " + Enum.Parse(typeof(ClipboardFormats), format.ToString()));
                if (format == (ulong)CLIPFORMAT.CF_UNICODETEXT ||
                    format == (ulong)CLIPFORMAT.CF_TEXT ||
                    format == (ulong)CLIPFORMAT.CF_LOCALE ||
                    format == (ulong)CLIPFORMAT.CF_OEMTEXT ||
                    format == 49161 ||
                    format == 49310 ||
                    format == 49171 ||
                    format == 49311 ||
                    format == 50377)
                {
                    byte[]? buffer = GetByteData(format);
                    if (buffer != null)
                    {
                        var cd = new ClipboardData(format, "", buffer);
                        clipData.Add(cd);
                    }
                }
            }
            return clipData;
        }

        internal BlockingCollection<ClipboardData> GetClipboardImages()
        {
            var clipData = new BlockingCollection<ClipboardData>();
            uint format = 0;
            while ((format = User32.EnumClipboardFormats(format)) != 0)
            {
                // Support image formats: CF_BITMAP, CF_DIB, CF_DIBV5
                if (format == (ulong)CLIPFORMAT.CF_BITMAP ||
                    format == (ulong)CLIPFORMAT.CF_DIB ||
                    format == (ulong)CLIPFORMAT.CF_DIBV5)
                {
                    byte[]? buffer = GetByteData(format);
                    if (buffer != null)
                    {
                        var cd = new ClipboardData(format, "", buffer);
                        clipData.Add(cd);
                    }
                }
            }
            return clipData;
        }

        private byte[]? GetByteData(uint format)
        {
            var memory = User32.GetClipboardData(format);
            if (memory == IntPtr.Zero)
            {
                return null;
            }
            var size = GetDataSize(memory);
            if (0 == size)
            {
                return null;
            }
            var data = new Byte[size];
            var ptr = Kernel32.GlobalLock(memory);
            Marshal.Copy(ptr, data, 0, data.Length);
            Kernel32.GlobalUnlock(memory);
            return data;
        }

        private ulong GetDataSize(IntPtr memory)
        {
            if (IntPtr.Zero == Kernel32.GlobalLock(memory))
            {
                // Not all handles returned by GetClipboardData are created with GlobalAlloc:
                // http://blogs.msdn.com/b/oldnewthing/archive/2007/10/26/5681471.aspx
                // https://msdn.microsoft.com/en-us/library/ms649014#_win32_Memory_and_the_Clipboard
                // Moreover, in case of CF_BITMAP GlobalFlags() and GlobalSize() just crash - that's why GlobalLock is in use
                return 0;
            }
            Kernel32.GlobalUnlock(memory);

            return Kernel32.GlobalSize(memory).Value;
        }


        internal bool SetClipboard(IEnumerable<ClipboardData>? clipData)
        {
            EmptyClipboard();
            using IEnumerator<ClipboardData> cData = clipData.GetEnumerator();
            while (cData.MoveNext())
            {
                ClipboardData cd = cData.Current;
                SetByteData(cd.Format, cd.Buffer);
            }
            return true;
        }

        private void SetByteData(uint format, byte[]? data)
        {
            if (data != null)
            {
                var ptr = Marshal.AllocHGlobal(data.Length);
                Marshal.Copy(data, 0, ptr, data.Length);
                var ptrClip = User32.SetClipboardData(format, hMem: ptr);
                ClipboardApiException.ThrowIfFailed(IntPtr.Zero == ptrClip, "SetClipboardData");
            }
        }

        internal void CloseClipboard()
        {
            User32.CloseClipboard();
        }

        public void Dispose()
        {
            CloseClipboard();
        }
    }
}
