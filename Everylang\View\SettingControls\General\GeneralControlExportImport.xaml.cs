﻿using System.Windows;

namespace Everylang.App.View.SettingControls.General
{
    /// <summary>
    /// Interaction logic for GeneralControlProxy.xaml
    /// </summary>
    internal partial class GeneralControlExportImport
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(GeneralControlExportImport));

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        internal GeneralControlExportImport()
        {
            InitializeComponent();
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }
    }
}
