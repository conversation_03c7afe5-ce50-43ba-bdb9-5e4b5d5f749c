﻿using Everylang.Common.LogManager;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

namespace Everylang.App.Utilities
{
    sealed class SystemCursor : IDisposable
    {
        private readonly Dictionary<User32.OCR, IntPtr> _originalCursorPointers;
        private Dictionary<User32.OCR, string>? _originalCursorPaths;

        private bool _disposed;


        internal SystemCursor()
        {
            _originalCursorPointers = new Dictionary<User32.OCR, IntPtr>();

            foreach (object value in Enum.GetValues(typeof(User32.OCR)))
            {
                _originalCursorPointers.Add((User32.OCR)value, IntPtr.Zero);
            }

            _disposed = false;
        }

        ~SystemCursor()
        {
            Dispose(false);
        }


        private void Change(IntPtr newCursor, User32.OCR targetCursorId)
        {
            if (newCursor == IntPtr.Zero)
            {
                throw new ArgumentNullException("newCursor");
            }
            ResourceId resourceId = Macros.MAKEINTRESOURCE((int)targetCursorId);
            if (_originalCursorPointers[targetCursorId] == IntPtr.Zero)
            {

                var originalCursor = User32.LoadCursor(HINSTANCE.NULL, resourceId);
                if (originalCursor != IntPtr.Zero)
                {
                    var originalCursorCopy = User32.CopyImage(originalCursor,
                        User32.LoadImageType.IMAGE_CURSOR, 0, 0, User32.CopyImageOptions.LR_COPYRETURNORG);
                    if (originalCursorCopy != IntPtr.Zero)
                    {
                        if (User32.SetSystemCursor(newCursor, targetCursorId))
                        {
                            _originalCursorPointers[targetCursorId] = originalCursorCopy.DangerousGetHandle();
                        }
                        else
                        {
                            throw new PInvokeException("Unable to set system cursor");
                        }
                    }
                    else
                    {
                        throw new PInvokeException("Unable to copy original cursor");
                    }
                }
                else
                {
                    throw new PInvokeException("Unable to load original cursor");
                }
            }
            else
            {
                if (!User32.SetSystemCursor(newCursor, targetCursorId))
                {
                    throw new PInvokeException("Unable to set system cursor");
                }
            }
        }

        internal void Copy(User32.OCR sourceCursorId, User32.OCR targetCursorId)
        {
            try
            {
                ResourceId sourceResourceId = Macros.MAKEINTRESOURCE((int)sourceCursorId);
                var sourceCursor = User32.LoadCursor(HINSTANCE.NULL, sourceResourceId);
                if (sourceCursor != IntPtr.Zero)
                {
                    var sourceCursorCopy = User32.CopyImage(sourceCursor,
                        User32.LoadImageType.IMAGE_CURSOR, 0, 0,
                        User32.CopyImageOptions.LR_COPYRETURNORG);
                    if (sourceCursorCopy != IntPtr.Zero)
                    {
                        Change(sourceCursorCopy.DangerousGetHandle(), targetCursorId);
                        User32.DestroyCursor(sourceCursorCopy.DangerousGetHandle());
                    }
                    else
                    {
                        throw new PInvokeException("Unable to copy source cursor");
                    }
                }
                else
                {
                    throw new PInvokeException("Unable to load source cursor");
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        private bool TryRestoreOne(User32.OCR cursorId)
        {
            var result = false;

            try
            {
                IntPtr cursorHandle = _originalCursorPointers[cursorId];
                if (cursorHandle != IntPtr.Zero)
                {
                    result = User32.SetSystemCursor(cursorHandle, cursorId);
                    User32.DestroyCursor(cursorHandle);
                    _originalCursorPointers[cursorId] = IntPtr.Zero;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return result;
        }

        internal bool TryRestoreAll()
        {
            var result = true;

            foreach (var cursorId in Enum.GetValues(typeof(User32.OCR)))
            {
                result &= TryRestoreOne((User32.OCR)cursorId);
            }

            return result;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    //dispose managed resources
                }

                //dispose unmanaged resources
                TryRestoreAll();

                _disposed = true;
            }
        }

        sealed class PInvokeException : Exception
        {
            internal PInvokeException(string message)
                : base(message, new Win32Exception(Marshal.GetLastWin32Error()))
            { }
        }
    }


}
