﻿<Popup x:Class="Everylang.App.View.Controls.SmartClick.SmartClickWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
        mc:Ignorable="d" 
        Height="300" Width="300" AllowsTransparency="True" Placement="Absolute" StaysOpen="True" Focusable="False"
        x:ClassModifier="internal"
        DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Popup.Resources>
        <ResourceDictionary>

            <telerik:RadRadialMenuItem Click="ButtonClickConverter" x:Key="UniConverter" ToolTipContent="{telerik:LocalizableResource Key=UniConverter}" CanUserSelect="False">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="Cached" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextConverter}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickTextCaseConverter" x:Key="UniCase" ToolTipContent="{telerik:LocalizableResource Key=UniCase}" CanUserSelect="False">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="FormatLetterCaseUpper" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniCase}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickLink" x:Key="UniLink" ToolTipContent="{telerik:LocalizableResource Key=UniLink}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="Link" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextLink}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickLinkTranslate" x:Key="UniLinkTranslate" ToolTipContent="{telerik:LocalizableResource Key=UniLinkTranslate}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="GoogleTranslate" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextLinkTranslate}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickLinkShorter" x:Key="UniLinkShorter" ToolTipContent="{telerik:LocalizableResource Key=UniLinkShorter}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="LinkVariant" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextLinkShorter}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickTranslate" x:Key="UniTranslate" ToolTipContent="{telerik:LocalizableResource Key=UniTranslate}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="Translate" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextTranslate}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSpellCheck" x:Key="UniSpellCheck" ToolTipContent="{telerik:LocalizableResource Key=UniSpellCheck}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="Spellcheck" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextSpellCheck}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickCopy" x:Key="UniCopy" ToolTipContent="{telerik:LocalizableResource Key=UniCopy}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="ContentCopy" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextCopy}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickPaste" x:Key="UniPaste" ToolTipContent="{telerik:LocalizableResource Key=UniPaste}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="ContentPaste" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextPaste}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickPasteUnf" x:Key="UniPasteUnf" ToolTipContent="{telerik:LocalizableResource Key=UniPasteUnf}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="FormatClear" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextPasteUnf1}" />
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextPasteUnf2}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSearch" x:Key="UniSearch" ToolTipContent="{telerik:LocalizableResource Key=UniSearch}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="SearchWeb" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextSearch}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickEmail" x:Key="UniEmail" ToolTipContent="{telerik:LocalizableResource Key=UniEmail}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="Email" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextEmail}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickAutochange" x:Key="UniAutochange" ToolTipContent="{telerik:LocalizableResource Key=UniAutochange}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="ContentDuplicate" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextAutochange}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickClipboardHistory" x:Key="UniClipboardHistory" ToolTipContent="{telerik:LocalizableResource Key=UniClipboardHistory}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="ClipboardText" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextClipboardHistory1}" />
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextClipboardHistory2}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickDiaryHistory" x:Key="UniDiaryHistory" ToolTipContent="{telerik:LocalizableResource Key=UniDiaryHistory}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="KeyboardVariant" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextDiaryHistory}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickOcr" x:Key="OcrHeader" ToolTipContent="{telerik:LocalizableResource Key=OcrHeader}">
                <telerik:RadRadialMenuItem.IconContent>
                    <wpf:MaterialIcon Width="20" Height="20" Kind="ImageSearch" />
                </telerik:RadRadialMenuItem.IconContent>
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="OCR" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickConvertExpressions" x:Key="UniConvertExpressions" ToolTipContent="{telerik:LocalizableResource Key=UniConvertExpressions}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextConvertExpressions}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickTranslit" x:Key="UniTranslit" ToolTipContent="{telerik:LocalizableResource Key=UniTranslit}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextTranslit}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickEnclose" x:Key="UniEnclose" ToolTipContent="{telerik:LocalizableResource Key=UniEnclose}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextEnclose}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSnakeCase" x:Key="UniSnakeCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsSnakeCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="snake_case"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickKebabCase" x:Key="UniKebabCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsKebabCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="kebab-case"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickPascalCase" x:Key="UniPascalCase" ToolTipContent="{telerik:LocalizableResource Key=ConverterSettingsPascalCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="PascalCase"  Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickCamelCase" x:Key="UniCamelCase" ToolTipContent="{telerik:LocalizableResource Key=UniCamelCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="camelCase" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickInvertCase" x:Key="UniInvertCase" ToolTipContent="{telerik:LocalizableResource Key=UniInvertCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextInvertCase}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickUpCase" x:Key="UniUpCase" ToolTipContent="{telerik:LocalizableResource Key=UniUpCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextUpCase}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickDownCase" x:Key="UniDownCase" ToolTipContent="{telerik:LocalizableResource Key=UniDownCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextDownCase}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickSearchAndReplace" x:Key="ConverterReplaceSelText" ToolTipContent="{telerik:LocalizableResource Key=ConverterReplaceSelText}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Vertical">
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextSearch}" />
                        <TextBlock Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickConvertFirstLetterToUp" x:Key="UniFirstLetterToUp" ToolTipContent="{telerik:LocalizableResource Key=UniFirstLetterUp}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextFirstLetterUp}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickConvertFirstLetterToDown" x:Key="UniFirstLetterToDown" ToolTipContent="{telerik:LocalizableResource Key=UniFirstLetterDown}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextFirstLetterDown}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <Style TargetType="telerik:RadialMenuButton" BasedOn="{StaticResource RadialMenuButtonStyle}">
                <Setter Property="ContentTemplate">
                    <Setter.Value>
                        <DataTemplate>
                            <wpf:MaterialIcon Kind="Adjust" />
                        </DataTemplate>
                    </Setter.Value>
                </Setter>
                <EventSetter Event="Click" Handler="EventSetter_OnHandler"></EventSetter>
            </Style>
        </ResourceDictionary>
    </Popup.Resources>
    <Grid>
        <telerik:RadRadialMenu x:Name="RadialMenuMu" VerticalAlignment="Center" HorizontalAlignment="Center" Navigated="RadialMenuMu_OnNavigated"
                               MinWidth="300" MinHeight="300"
                               InnerNavigationRadiusFactor="0.92"/>
    </Grid>
</Popup>































































































































































































































































































































































































































































































































