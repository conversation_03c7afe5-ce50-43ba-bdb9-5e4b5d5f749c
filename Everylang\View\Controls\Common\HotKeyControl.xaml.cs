﻿using Everylang.App.Shortcut;
using NHotkey;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Telerik.Windows.Controls;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;

namespace Everylang.App.View.Controls.Common
{
    /// <summary>
    /// Interaction logic for HotKeyWindow.xaml
    /// </summary>
    internal partial class HotKeyControl
    {
        internal static readonly RoutedEvent SaveEvent = EventManager.RegisterRoutedEvent("Save",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(HotKeyControl));

        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(HotKeyControl));

        internal event RoutedEventHandler Save
        {
            add { AddHandler(SaveEvent, value); }
            remove { RemoveHandler(SaveEvent, value); }
        }

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        internal static readonly DependencyProperty HeaderTextProperty = DependencyProperty.Register("HeaderText", typeof(string), typeof(HotKeyControl), new FrameworkPropertyMetadata(""));

        internal string HeaderText
        {
            get { return (string)GetValue(HeaderTextProperty); }
            set { SetValue(HeaderTextProperty, value); }
        }

        internal static readonly DependencyProperty HotkeyIsOnProperty = DependencyProperty.Register("HotkeyIsOn", typeof(bool), typeof(HotKeyControl), new FrameworkPropertyMetadata());

        internal bool HotkeyIsOn
        {
            get { return (bool)GetValue(HotkeyIsOnProperty); }
            set { SetValue(HotkeyIsOnProperty, value); }
        }

        internal static readonly DependencyProperty MouseIsOnProperty = DependencyProperty.Register("MouseIsOn", typeof(bool), typeof(HotKeyControl), new FrameworkPropertyMetadata());

        internal bool MouseIsOn
        {
            get { return (bool)GetValue(MouseIsOnProperty); }
            set { SetValue(MouseIsOnProperty, value); }
        }

        internal static readonly DependencyProperty IsChangingProperty = DependencyProperty.Register("IsChanging", typeof(bool), typeof(HotKeyControl), new FrameworkPropertyMetadata());

        internal bool IsChanging
        {
            get { return (bool)GetValue(IsChangingProperty); }
            set
            {
                SetValue(IsChangingProperty, value);
                IsNotChanging = !value;
            }
        }

        internal static readonly DependencyProperty IsNotChangingProperty = DependencyProperty.Register("IsNotChanging", typeof(bool), typeof(HotKeyControl), new FrameworkPropertyMetadata());

        internal bool IsNotChanging
        {
            get { return (bool)GetValue(IsNotChangingProperty); }
            set { SetValue(IsNotChangingProperty, value); }
        }

        private readonly string _shortcutEventName;
        private string _newShortcut;
        private readonly string _oldShortcut;
        private bool _isLoad;
        private readonly EventHandler<HotkeyEventArgs> _handler;

        internal string NewShortCut
        {
            get
            {
                if (IsChanging)
                {
                    return _oldShortcut;
                }
                if (!HotkeyIsOn)
                {
                    return "";
                }
                if (_newShortcut == "")
                {
                    return _oldShortcut;
                }
                return _newShortcut;
            }
        }

        internal HotKeyControl(string header, string shortcut, string shortcutEventName, EventHandler<HotkeyEventArgs> handler)
        {
            _isLoad = true;
            InitializeComponent();
            ShortcutManager.OpenedHotKeyControl = this;
            _shortcutEventName = shortcutEventName;
            _handler = handler;
            _newShortcut = shortcut;
            _oldShortcut = shortcut;
            HotKeyBoxMy.HotkeyTextBox.WatermarkContent = LocalizationManager.GetString("HotKeyWithoutShortcutNull");
            //HotKeyBoxMy.SetValue(TextBoxHelper.WatermarkProperty, LocalizationManager.GetString("HotKeyWithoutShortcutNull", "Text"));
            var freeKeys = DoubleKeyDownManager.GetFreeKeys(shortcutEventName);
            foreach (var freeKey in freeKeys)
            {
                ComboBoxDoubleClick.Items.Add(freeKey);

            }
            var freeMouseKey = MouseXKeyManager.GetFree(shortcutEventName);
            if (freeMouseKey.Count == 0)
            {
                MouseIsOn = false;
            }
            else
            {
                MouseIsOn = true;
                foreach (var freeKey in freeMouseKey)
                {
                    ComboBoxMouseXKey.Items.Add(freeKey);
                }
            }

            HeaderText = header;
            HotKeyBoxMy.HotKey = null;
            if (shortcut.StartsWith("double"))
            {
                HotkeyIsOn = true;
                RadioButtonHotkey.IsChecked = false;
                RadioButtonDoubleKeyDown.IsChecked = true;
                ComboBoxDoubleClick.SelectedItem = DoubleKeyDownManager.GetKeyData(shortcut.Replace("double", ""));
            }
            if (!shortcut.StartsWith("double") && (shortcut.StartsWith("shortcut") || shortcut != ""))
            {
                HotkeyIsOn = true;
                RadioButtonHotkey.IsChecked = true;
                RadioButtonDoubleKeyDown.IsChecked = false;
                ComboBoxDoubleClick.SelectedItem = null;
                HotKeyBoxMy.HotKey = ShortcutManager.GetHotKeyFromText(shortcut);
            }
            if (shortcut.Contains("mouse"))
            {
                HotkeyIsOn = true;
                CheckBoxMouseXKey.IsChecked = true;
                ComboBoxMouseXKey.SelectedItem = ShortcutManager.GetMouseKeyFromShortcut(shortcut);

            }
            if (shortcut == "")
            {
                RadioButtonHotkey.IsChecked = false;
                CheckBoxMouseXKey.IsChecked = false;
                RadioButtonDoubleKeyDown.IsChecked = false;
                ComboBoxDoubleClick.SelectedItem = null;
                HotkeyIsOn = false;
            }
            IsNotChanging = true;
            _isLoad = false;
        }

        private void SwitchOnOffHotkeyChecked(object sender, RoutedEventArgs e)
        {
            if (!HotkeyIsOn)
            {
                RadioButtonHotkey.IsChecked = false;
                RadioButtonDoubleKeyDown.IsChecked = false;
                CheckBoxMouseXKey.IsChecked = false;
                ComboBoxDoubleClick.SelectedItem = null;
                HotKeyBoxMy.HotKey = null;
                _newShortcut = "";
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                HotKeyBoxMy.HotkeyTextBox.WatermarkContent = LocalizationManager.GetString("HotKeyWithoutShortcutNull");
                SaveAll();
            }
        }

        private void ChangeShortcut(object sender, RoutedEventArgs e)
        {
            IsChanging = true;
            HotKeyBoxMy.HotKey = null;
            HotKeyBoxMy.MuGrid.Focus();
            HotKeyBoxMy.KeyUp += HotKeyBoxMyOnKeyUp;
            HotKeyBoxMy.HotkeyTextBox.WatermarkContent = LocalizationManager.GetString("HotKeyWithoutPressShortcut");
        }

        private void SaveShortcut(object? sender, RoutedEventArgs? e)
        {
            HotKeyBoxMy.KeyUp -= HotKeyBoxMyOnKeyUp;
            if (HotKeyBoxMy.HotKey == null)
            {
                HotKeyBoxMy.HotKey = ShortcutManager.GetHotKeyFromText(_oldShortcut);
            }
            else
            {
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                if (!ShortcutManager.RegisterShortcut(_shortcutEventName, HotKeyBoxMy.HotKey, _handler))
                {
                    HotKeyBoxMy.HotKey = ShortcutManager.GetHotKeyFromText(_oldShortcut);
                    _newShortcut = _oldShortcut;
                }
            }
            IsChanging = false;
            HotKeyBoxMy.HotkeyTextBox.WatermarkContent = LocalizationManager.GetString("HotKeyWithoutShortcutNull");
            SaveAll();
        }

        private void CancelShortcut(object sender, RoutedEventArgs e)
        {
            IsChanging = false;
            HotKeyBoxMy.HotKey = ShortcutManager.GetHotKeyFromText(_oldShortcut);
            HotKeyBoxMy.HotkeyTextBox.WatermarkContent = LocalizationManager.GetString("HotKeyWithoutShortcutNull");
        }

        private void HotKeyBoxMyOnKeyUp(object sender, KeyEventArgs keyEventArgs)
        {
            if (HotKeyBoxMy.HotKey != null)
            {
                if (HotKeyBoxMy.HotKey.ModifierKeys == ModifierKeys.Control &&
                    (HotKeyBoxMy.HotKey.Key == Key.C || HotKeyBoxMy.HotKey.Key == Key.Insert ||
                     HotKeyBoxMy.HotKey.Key == Key.V || HotKeyBoxMy.HotKey.Key == Key.X ||
                     HotKeyBoxMy.HotKey.Key == Key.A
                     || HotKeyBoxMy.HotKey.Key == Key.Z || HotKeyBoxMy.HotKey.Key == Key.Y ||
                     HotKeyBoxMy.HotKey.Key == Key.Escape) || IsShiftWithLetter()
                     || (HotKeyBoxMy.HotKey.ToString() != _oldShortcut && ShortcutManager.CheckAlreadyRegistered(HotKeyBoxMy.HotKey.ToString())))
                {

                    HotKeyBoxMy.HotKey = null;
                    SetNewShortcut("shortcut");
                }
                else
                {
                    SetNewShortcut("shortcut" + HotKeyBoxMy.HotKey);
                }
            }
        }

        private bool IsShiftWithLetter()
        {
            if (HotKeyBoxMy.HotKey != null && (HotKeyBoxMy.HotKey.Key == Key.RightShift || HotKeyBoxMy.HotKey.Key == Key.LeftShift))
            {
                if (HotKeyBoxMy.HotKey.Key >= Key.D0 && HotKeyBoxMy.HotKey.Key <= Key.Z)
                {
                    return true;
                }
            }
            return false;
        }

        private void ComboBoxDoubleClick_OnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoad)
            {
                return;
            }
            if (ComboBoxDoubleClick.SelectedItem != null)
            {
                SetNewShortcut("double" + ((KeyData)ComboBoxDoubleClick.SelectedItem).Code);
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
            }
            else
            {
                SetNewShortcut("double");
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
            }
            SaveAll();
        }

        private void ComboBoxMouseXKeyClick_OnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoad)
            {
                return;
            }
            if (ComboBoxMouseXKey.SelectedItem != null)
            {
                SetNewShortcut("mouse" + ComboBoxMouseXKey.SelectedItem);
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
            }
            else
            {
                SetNewShortcut("mouse");
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
            }
            SaveAll();
        }

        private void RadioButtonHotkey_OnChecked(object sender, RoutedEventArgs e)
        {
            if (_isLoad)
            {
                return;
            }
            UseHotkeyCheck(RadioButtonHotkey.IsChecked != null && (bool)RadioButtonHotkey.IsChecked);
            SaveAll();
        }

        private void RadioButtonDoubleKeyDown_OnChecked(object sender, RoutedEventArgs e)
        {
            if (IsChanging)
            {
                SaveShortcut(null, null);
            }
            if (_isLoad)
            {
                return;
            }
            UseDoubleClickCheck(RadioButtonDoubleKeyDown.IsChecked != null && (bool)RadioButtonDoubleKeyDown.IsChecked);
            SaveAll();
        }

        private void RadioButtonMouseXKey_OnChecked(object sender, RoutedEventArgs e)
        {
            if (IsChanging)
            {
                SaveShortcut(null, null);
            }
            if (_isLoad)
            {
                return;
            }

            if (CheckBoxMouseXKey.IsChecked != null && !((bool)CheckBoxMouseXKey.IsChecked))
            {
                SetNewShortcut("mouse");
                ShortcutManager.RemoveShortcut(_shortcutEventName);
                ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
                var shortcat = ShortcutManager.GetMouseKeyFromShortcut(_newShortcut);
                if (string.IsNullOrEmpty(shortcat))
                {
                    ComboBoxMouseXKey.SelectedItem = null;
                }
            }
            UseMouseXKeyCheck(RadioButtonDoubleKeyDown.IsChecked != null && (bool)RadioButtonDoubleKeyDown.IsChecked);
            SaveAll();
        }

        private void UseHotkeyCheck(bool value)
        {
            if (value)
            {
                if (HotKeyBoxMy.HotKey != null)
                {
                    SetNewShortcut("shortcut" + HotKeyBoxMy.HotKey);
                    ShortcutManager.RemoveShortcut(_shortcutEventName);
                    ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
                }
            }
        }

        private void UseDoubleClickCheck(bool value)
        {
            if (value)
            {
                if (ComboBoxDoubleClick.SelectedItem != null)
                {
                    SetNewShortcut("double" + ((KeyData)ComboBoxDoubleClick.SelectedItem).Code);
                    ShortcutManager.RemoveShortcut(_shortcutEventName);
                    ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
                }
            }
        }

        private void UseMouseXKeyCheck(bool value)
        {
            if (value)
            {
                if (ComboBoxMouseXKey.SelectedItem != null)
                {
                    SetNewShortcut("mouse" + ComboBoxMouseXKey.SelectedItem);
                    ShortcutManager.RemoveShortcut(_shortcutEventName);
                    ShortcutManager.RegisterShortcut(_shortcutEventName, _newShortcut, _handler);
                }
            }
        }

        private void SetNewShortcut(string newShortcut)
        {
            if (newShortcut == "mouse")
            {
                if (_newShortcut.Split('|').Length > 0)
                {
                    var hotkeys = _newShortcut.Split('|')[0];
                    _newShortcut = hotkeys;
                }
                else
                {
                    _newShortcut = "";
                }
                return;
            }

            if (newShortcut == "shortcut" || newShortcut == "double")
            {
                if (_newShortcut.Split('|').Length > 1)
                {
                    var mouseKey = _newShortcut.Split('|')[1];
                    _newShortcut = "|" + mouseKey;
                }
                else
                {
                    _newShortcut = "";
                }
                return;
            }
            if (newShortcut.Contains("mouse"))
            {
                var hotkeys = _newShortcut.Split('|')[0];
                _newShortcut = hotkeys + "|" + newShortcut;
            }
            else
            {
                if (_newShortcut.Contains("mouse"))
                {
                    var mouseKey = _newShortcut.Split('|')[1];
                    _newShortcut = newShortcut + "|" + mouseKey;
                }
                else
                {
                    _newShortcut = newShortcut;
                }
            }
        }

        private void SaveAll()
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(SaveEvent);
            RaiseEvent(newEventArgs);
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RaiseEvent(new RoutedEventArgs(HidePanelEvent));
        }
    }
}
