<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Zip</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Zip.BlockTransformBase">
            <summary>
            Base class for all block transformations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.CanReuseTransform">
            <summary>Gets a value indicating whether the current transform can be reused.</summary>
            <returns>true if the current transform can be reused; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.CanTransformMultipleBlocks">
            <summary>Gets a value indicating whether multiple blocks can be transformed.</summary>
            <returns>true if multiple blocks can be transformed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.Header">
            <summary>Gets transformation header (if required).</summary>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.Footer">
            <summary>Gets transformation footer (if required).</summary>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.InputBlockSize">
            <summary>Gets the input block size.</summary>
            <returns>The size of the input data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.BlockTransformBase.FixedInputBlockSize">
            <summary>
            Gets or sets value which indicates whether the transformation uses
            input buffer of the fixed size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.Dispose">
            <summary>
            Releases the resources used by the current instance of the ZipArchive class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.CreateHeader(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates transformation header to be written into the output stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.InitHeaderReading(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize reading of the transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.InitFooterReading(System.Int32)">
            <summary>
            Initialize reading of the transformation footer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.ProcessHeader">
            <summary>
            Process transformation header has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.ProcessFooter">
            <summary>
            Process transformation footer has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>Transforms the specified region of the input byte array and copies the resulting transform to the specified region of the output byte array.</summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.ValidateInputBufferParameters(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Validates parameters of the input buffer.
            </summary>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="validateBlockSize">Indicates whether buffer block size should be validated. Should be true for the TransformBlock and false for the TransformFinalBlock.</param>
            <param name="allowZeroCount">Indicates whether count can be zero.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.BlockTransformBase.ValidateParameters(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Boolean)">
            <summary>
            Validates parameters of the transform operation.
            </summary>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
            <param name="allowZeroCount">Indicates whether input count can be zero.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.Adler32">
            <summary>
            Implements Adler-32 checksum algorithm.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Adler32.Base">
            <summary>
            Base for modulo arithmetic (largest prime smaller than 65536).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Adler32.MaxIterations">
            <summary>
            Number of iterations we can safely do before applying the modulo.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Adler32.UpdateChecksum(System.UInt32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculate checksum for the specified region of the input byte array.
            </summary>
            <param name="checksum">Checksum to update.</param>
            <param name="buffer">The input for which to compute the checksum.</param>
            <param name="offset">The offset into the input byte array from which to begin using data.</param>
            <param name="length">The number of bytes in the input byte array to use as data.</param>
            <returns>Updated checksum.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.Crc32">
            <summary>
            Implements CRC-32 checksum algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Crc32.UpdateChecksum(System.UInt32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculate checksum for the specified region of the input byte array.
            </summary>
            <param name="checksum">Checksum to update.</param>
            <param name="buffer">The input for which to compute the checksum.</param>
            <param name="offset">The offset into the input byte array from which to begin using data.</param>
            <param name="length">The number of bytes in the input byte array to use as data.</param>
            <returns>Updated checksum.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.IChecksumAlgorithm">
            <summary>
            Interface which must be implemented by all implementations of the checksum algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IChecksumAlgorithm.UpdateChecksum(System.UInt32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculate checksum for the specified region of the input byte array.
            </summary>
            <param name="checksum">Checksum to update.</param>
            <param name="buffer">The input for which to compute the checksum.</param>
            <param name="offset">The offset into the input byte array from which to begin using data.</param>
            <param name="length">The number of bytes in the input byte array to use as data.</param>
            <returns>Updated checksum.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressionMethod">
            <summary>
            Specifies values that indicate compression method.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionMethod.Stored">
            <summary>
            The file is stored (no compression).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionMethod.Deflate">
            <summary>
            The file is Deflated.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionMethod.Lzma">
            <summary>
            The file is compressed using LZMA algorithm.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionMethod.Aes">
            <summary>
            The file is compressed using AE-x Encryption marker algorithm.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressedStream">
            <summary>
            Represents stream which allows read/write compressed information from/to given input stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.#ctor(System.IO.Stream,Telerik.Windows.Zip.StreamOperationMode,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initializes a new instance of the CompressedStream class.
            </summary>
            <param name="baseStream">The base input/output stream.</param>
            <param name="mode">Stream operational mode.</param>
            <param name="settings">Compression settings.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified mode is not allowed for the given stream.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.#ctor(System.IO.Stream,Telerik.Windows.Zip.StreamOperationMode,Telerik.Windows.Zip.CompressionSettings,System.Boolean,Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Initializes a new instance of the CompressedStream class.
            </summary>
            <param name="baseStream">The base input/output stream.</param>
            <param name="mode">Stream operational mode.</param>
            <param name="settings">Compression settings.</param>
            <param name="useCrc32">Indicates whether the CRC32 (true) or Adler32 (false) checksum algorithm will be used.</param>
            <param name="encryptionSettings">Encryption settings.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified mode is not allowed for the given stream.</exception>
        </member>
        <member name="E:Telerik.Windows.Zip.CompressedStream.ChecksumReady">
            <summary>
            Event occurs when calculation of the checksum for this stream is completed. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressedStream.Checksum">
            <summary>
            Gets checksum calculated for this stream starting from 
            the first read/write operation and up to the Flush call.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressedStream.CompressedSize">
            <summary>
            Gets the compressed size of the stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressedStream.ChecksumAlgorithm">
            <summary>
            Gets or sets the checksum algorithm will be used during compression-decompression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the 
            values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream. </param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many 
            bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Telerik.Windows.Zip.StreamOperationMode" /> associated with 
            current <see cref="T:Telerik.Windows.Zip.OperationStream" /> object does not match the underlying stream.  
            For example, this exception is thrown when using <see cref="F:Telerik.Windows.Zip.StreamOperationMode.Read" /> with an underlying stream that is write only.  </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than zero.-or- The <paramref name="count" /> parameter is less than zero. </exception>
            <exception cref="T:System.ArgumentException">The sum of the <paramref name="count" /> and <paramref name="offset" /> parameters is larger than the length of the buffer. </exception>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from buffer to the current stream. </param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream. </param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the Stream and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressedStream.Initialize(System.IO.Stream,Telerik.Windows.Zip.ICompressionAlgorithm,Telerik.Windows.Zip.IChecksumAlgorithm)">
            <summary>
            Initialize compressed stream.
            </summary>
            <param name="baseStream">The base input/output stream.</param>
            <param name="compressionAlgorithm">Compression algorithm.</param>
            <param name="checksumAlgorithm">Checksum algorithm.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressionSettings">
            <summary>
            Base class for the compression settings.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Zip.CompressionSettings.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressionSettings.Method">
            <summary>
            Gets or sets compression method.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionSettings.CopyFrom(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Copy settings from the given base settings.
            </summary>
            <param name="baseSettings">Base settings to copy from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionSettings.PrepareForZip(Telerik.Windows.Zip.CentralDirectoryHeader)">
            <summary>
            Prepare settings for usage in zip archive entries.
            </summary>
            <param name="header">Central directory header.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionSettings.OnPropertyChanged(System.String)">
            <summary>
            Called when property value is changed.
            </summary>
            <param name="propertyName">Property name.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressionTransformBase">
            <summary>
            Represents base class for all compression and decompression functionality.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionTransformBase.#ctor">
            <summary>
            Initializes a new instance of the CompressionTransformBase class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressionTransformBase.CanReuseTransform">
            <summary>Gets a value indicating whether the current transform can be reused.</summary>
            <returns>true if the current transform can be reused; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressionTransformBase.CanTransformMultipleBlocks">
            <summary>Gets a value indicating whether multiple blocks can be transformed.</summary>
            <returns>true if multiple blocks can be transformed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressionTransformBase.InputBlockSize">
            <summary>Gets the input block size.</summary>
            <returns>The size of the input data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.CompressionTransformBase.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionTransformBase.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            Transforms the specified region of the input byte array and copies
            the resulting transform to the specified region of the output byte array.
            </summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionTransformBase.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionTransformBase.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.CompressionTransformBase.ProcessTransform(System.Boolean)">
            <summary>
            Transforms current input buffer.
            </summary>
            <param name="finalBlock">The final block flag.</param>
            <returns>True when output still available.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressedStreamHeader">
            <summary>
            Specifies values for header type of the compressed stream.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressedStreamHeader.None">
            <summary>
            Compressed stream does not contain a header.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressedStreamHeader.ZLib">
            <summary>
            Compressed stream is formatted in accordance with RFC 1950
            (ZLIB Compressed Data Format Specification version 3.3).
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.CompressionLevel">
            <summary>
            The compression level to be used for compression of data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.NoCompression">
            <summary>
            The data will be simply stored,
            no compression should be performed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level0">
            <summary>
            Same as NoCompression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Fastest">
            <summary>
            The fastest but least effective compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level1">
            <summary>
            A synonym for Fastest.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level2">
            <summary>
            A little slower, but better, than level 1.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level3">
            <summary>
            A little slower, but better, than level 2.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level4">
            <summary>
            A little slower, but better, than level 3.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level5">
            <summary>
            A little slower than level 4, but with better compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Optimal">
            <summary>
            The default compression level with
            a good balance of speed and compression efficiency.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level6">
            <summary>
            A synonym for Optimal.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level7">
            <summary>
            Pretty good compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level8">
            <summary>
             Better compression than Level7.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Best">
            <summary>
            The best compression, where best means
            greatest reduction in size of the input data.
            This is also the slowest compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CompressionLevel.Level9">
            <summary>
            A synonym for Best compression level.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateAlgorithm">
            <summary>
            Class which implements Deflate compression algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateAlgorithm.CreateCompressor">
            <summary>
            Creates a compressor object. 
            </summary>
            <returns>A compressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateAlgorithm.CreateDecompressor">
            <summary>
            Creates a decompressor object.
            </summary>
            <returns>A decompressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateAlgorithm.Initialize(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize compression algorithm using given compression settings.
            </summary>
            <param name="settings">Compression settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateBlockState">
            <summary>
            Represents a state of current block.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateBlockState.NeedMore">
            <summary>
            Block is not completed, need more input or more output.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateBlockState.BlockDone">
            <summary>
            Block flush performed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateBlockState.FinishStarted">
            <summary>
            Finish started, need only more output at next deflate.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateBlockState.FinishDone">
            <summary>
            Finish done, accept no more input or output.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateCompressor">
            <summary>
            Compressor which implements Deflate compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateCompressor.WindowBitsDefault">
            <summary>
            The default number of window bits for the Deflate algorithm.
            15 is the maximum number of window bits for the Deflate algorithm (32K window).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.#ctor(Telerik.Windows.Zip.DeflateSettings)">
            <summary>
            Initializes a new instance of the DeflateCompressor class.
            </summary>
            <param name="settings">Deflate settings.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.CreateHeader(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates RFC 1950 (ZLIB Compressed Data Format Specification version 3.3) header
            to be written into the output stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.DownHeap(System.Int16[],System.Int32)">
            <summary>
            Restore the heap property by moving down the tree starting at specified node,
            exchanging a node with the smallest of its two sons if necessary, stopping
            when the heap property is re-established (each father smaller than its two sons).
            </summary>
            <param name="tree">The tree.</param>
            <param name="nodeIndex">Index of node.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.ProcessTransform(System.Boolean)">
            <summary>
            Transforms current input buffer.
            </summary>
            <param name="finalBlock">The final block flag.</param>
            <returns>True when still output available.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.ScanTree(System.Int16[],System.Int32)">
            <summary>
            Scan a literal or distance tree to determine the frequencies of the codes
            in the bit length tree.
            </summary>
            <param name="tree">The tree.</param>
            <param name="maxCode">Max code.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.BuildBitLengthTree">
            <summary>
            Construct the Huffman tree for the bit lengths.
            </summary>
            <returns>The index of the last bit length code to send.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.SendAllTrees(System.Int32,System.Int32,System.Int32)">
            <summary>
            Send the header for a block using dynamic Huffman trees: the counts,
            the lengths of the bit length codes, the literal tree and the distance tree.
            </summary>
            <param name="literalCodes">Length of literal codes.</param>
            <param name="distanceCodes">Length of distance codes.</param>
            <param name="bitLengthCodes">Length of bit length codes.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.SendTree(System.Int16[],System.Int32)">
            <summary>
            Send a literal or distance tree in compressed form,
            using the codes in bit length tree.
            </summary>
            <param name="tree">The tree.</param>
            <param name="maxCode">Max code.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.PutBytes(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Output a block of bytes on the stream.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="start">Start index.</param>
            <param name="length">Length.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.TreeTally(System.Int32,System.Int32)">
            <summary>
            Save the match info and tally the frequency counts.
            </summary>
            <param name="distance">Distance.</param>
            <param name="lengthOrChar">Length or unmatched char.</param>
            <returns>Return true if the current block must be flushed.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.SendCompressedBlock(System.Int16[],System.Int16[])">
            <summary>
            Send the block data compressed using the given Huffman trees.
            </summary>
            <param name="literalTree">Literal tree.</param>
            <param name="distanceTree">Distance tree.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.AlginOnByteBoundary">
            <summary>
            Flush the bit buffer and align the output on a byte boundary.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.CopyBlock(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Copy a stored block, storing first the length
            and its one's complement if requested.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="length">Length.</param>
            <param name="header">Should send the header.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.SendStoredBlock(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Send a stored block.
            </summary>
            <param name="offset">Offset in window.</param>
            <param name="length">Length.</param>
            <param name="lastBlock">The flag of last block.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.TreeFlushBlock(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Determine the best encoding for the current block: dynamic trees, static
            trees or store, and output the encoded block.
            </summary>
            <param name="offset">Offset in window.</param>
            <param name="length">Length.</param>
            <param name="lastBlock">The flag of last block.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.FillWindow">
            <summary>
            Fill the window if necessary.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.DeflateFast(System.Boolean)">
            <summary>
            Compress as much as possible from the input stream, return the current
            block state.
            This function does not perform lazy evaluation of matches and inserts
            new strings in the dictionary only for unmatched strings or for short
            matches. It is used only for the fast compression options.
            </summary>
            <param name="flush">Flush flag.</param>
            <returns>Returns the current block state.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.DeflateNone(System.Boolean)">
            <summary>
            Copy without compression as much as possible from the input buffer.
            </summary>
            <param name="flush">Flush flag.</param>
            <returns>Returns the current block state.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.DeflateSlow(System.Boolean)">
            <summary>
            Same as above, but achieves better compression. We use a lazy
            evaluation for matches: a match is finally adopted only if there is
            no better match at the next window position.
            </summary>
            <param name="flush">Flush flag.</param>
            <returns>Returns the current block state.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.InitializeTreeData">
            <summary>
            Initialize the tree data structures.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.SetConfiguration(System.Int32)">
            <summary>
            Sets configuration parameters by the compression level.
            </summary>
            <param name="level">Compression level.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.FlushPending">
            <summary>
            Flush as much pending output as possible.
            All deflate output goes through this function.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateCompressor.ReadBuffer(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read a new buffer from the current input stream, update
            total number of bytes read.  All deflate input goes through
            this function.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="start">Start position in buffer.</param>
            <param name="size">Size.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateConfiguration">
            <summary>
            Represents configuration of deflate algorithm.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateConfiguration.GoodLength">
            <summary>
            Use a faster search when the previous match is longer
            than this reduce lazy search above this match length.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateConfiguration.MaxLazy">
            <summary>
            Attempt to find a better match only when the current match is
            strictly smaller than this value. This mechanism is used only for
            compression levels >= 4.  For levels 1,2,3: MaxLazy is actually
            MaxInsertLength (See DeflateFast).
            Do not perform lazy search above this match length.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateConfiguration.NiceLength">
            <summary>
            Quit search above this match length.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateConfiguration.MaxChainLength">
            <summary>
            To speed up deflation, hash chains are never searched beyond this length.
            A higher limit improves compression ratio but degrades the speed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateConfiguration.Lookup(System.Int32)">
            <summary>
            Returns instance of Config class by the compression level.
            </summary>
            <param name="compressionLevel">Compression level.</param>
            <returns>Instance of Config class.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateConstants">
            <summary>
            Represents constants for deflate compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.NoCompression">
            <summary>
            The file is stored (no compression).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.HeaderDeflated">
            <summary>
            Z-lib header: the deflate compression method.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.MaxBitLengthBits">
            <summary>
            Bit length codes must not exceed MaxBitLengthBits bits.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.Repeat3To6">
            <summary>
            Repeat previous bit length 3-6 times (2 bits of repeat count).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.RepeatZero3To10">
            <summary>
            Repeat a zero length 3-10 times (3 bits of repeat count).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflateConstants.RepeatZero11To138">
            <summary>
            Repeat a zero length 11-138 times (7 bits of repeat count).
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateDecompressor">
            <summary>
            Decompressor which implements Deflate compression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateDecompressor.#ctor(Telerik.Windows.Zip.DeflateSettings)">
            <summary>
            Initializes a new instance of the DeflateDecompressor class.
            </summary>
            <param name="settings">Deflate settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateDecompressor.DecompressorState">
            <summary>
            Represents a state of decompressor process.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateDecompressor.BlockType">
            <summary>
            Represents a type of block in deflated data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateDecompressor.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateDecompressor.InitHeaderReading(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize reading of the transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateDecompressor.ProcessHeader">
            <summary>
            Process transformation header has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateDecompressor.ProcessTransform(System.Boolean)">
            <summary>
            Transforms current input buffer.
            </summary>
            <param name="finalBlock">The final block flag.</param>
            <returns>True when still output available.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateSettings">
            <summary>
            Compression settings of the Deflate method.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateSettings.#ctor">
            <summary>
            Initializes a new instance of the DeflateSettings class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateSettings.CompressionLevel">
            <summary>
            The compression level of deflate algorithm to be used for deflating by a CompressedStream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DeflateSettings.HeaderType">
            <summary>
            Gets or sets compression stream header type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateSettings.CopyFrom(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Copy settings from the given base settings.
            </summary>
            <param name="baseSettings">Base settings to copy from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateSettings.PrepareForZip(Telerik.Windows.Zip.CentralDirectoryHeader)">
            <summary>
            Prepare settings for usage in zip archive entries.
            </summary>
            <param name="header">Central directory header.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflateTransformBase">
            <summary>
            Represents base class for Deflate compression and decompression functionality.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DeflateTransformBase.#ctor(Telerik.Windows.Zip.DeflateSettings)">
            <summary>
            Initializes a new instance of the DeflateTransformBase class.
            </summary>
            <param name="settings">Deflate settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.InflateTree">
            <summary>
            Inflates data using a lookup table combined with a HuffmanTree.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InflateTree.#cctor">
            <summary>
            Initializes static members of the InflateTree class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InflateTree.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the InflateTree class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InflateTree.GetNextSymbol(Telerik.Windows.Zip.InputBitsBuffer)">
            <summary>
            Tries to get enough bits from input and try to decode them.
            </summary>
            <param name="input">Input buffer.</param>
            <returns>Next symbol or -1 when there is no enough bits in input.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.InflateTree.CalculateHuffmanCode">
            <summary>
            Calculate the huffman codes according to RFC 1951.
            </summary>
            <returns>Huffman codes.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.InputBitsBuffer">
            <summary>
            Represents input buffer for inflating data using Huffman coding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.#ctor">
            <summary>
            Initializes a new instance of the InputBitsBuffer class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.InputBitsBuffer.AvailableBits">
            <summary>
            Available bits in bit buffer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.InputBitsBuffer.AvailableBytes">
            <summary>
            Available bytes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.InputBitsBuffer.InputRequired">
            <summary>
            Is input required.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.CheckAvailable(System.Int32)">
            <summary>
            Checks available bits in the bit buffer.
            </summary>
            <param name="count">Count of bits.</param>
            <returns>True if available.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.GetBits(System.Int32)">
            <summary>
            Gets available bits from buffer.
            </summary>
            <param name="count">Count of required bits.</param>
            <returns>Bits data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read bytes to output buffer.
            </summary>
            <param name="output">Output buffer.</param>
            <param name="offset">Offset.</param>
            <param name="length">Length.</param>
            <returns>Count of bytes which are read.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.SetBuffer(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Set current working buffer.
            </summary>
            <param name="buffer">Bytes buffer.</param>
            <param name="offset">Offset.</param>
            <param name="length">Length.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.SkipBits(System.Int32)">
            <summary>
            Skips bits in bit buffer.
            </summary>
            <param name="count">Count of bits to skip.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.SkipToByteBoundary">
            <summary>
            Skips to the next byte boundary.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InputBitsBuffer.Get16Bits">
            <summary>
            Gets 16 or more bits into bit buffer.
            </summary>
            <returns>Bit buffer.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.OutputWindow">
            <summary>
            Represents output window for inflating data using Huffman coding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OutputWindow.#ctor">
            <summary>
            Initializes a new instance of the OutputWindow class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OutputWindow.AvailableBytes">
            <summary>
            Gets available bytes count.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OutputWindow.FreeBytes">
            <summary>
            Gets free bytes count.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OutputWindow.AddByte(System.Byte)">
            <summary>
            Adds a byte to output window.
            </summary>
            <param name="value">Byte.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.OutputWindow.Copy(System.Int32,System.Int32)">
            <summary>
            Copies bytes within output window.
            Moves backwards distance bytes and copy length bytes.
            </summary>
            <param name="length">Length.</param>
            <param name="distance">Distance.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.OutputWindow.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read bytes to output buffer.
            </summary>
            <param name="output">Output buffer.</param>
            <param name="offset">Offset.</param>
            <param name="length">Length.</param>
            <returns>Count of bytes which are read.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.OutputWindow.ReadInput(Telerik.Windows.Zip.InputBitsBuffer,System.Int32)">
            <summary>
            Reads bytes from input.
            </summary>
            <param name="input">InputBitsBuffer.</param>
            <param name="length">Length.</param>
            <returns>Count of read bytes.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.StaticTree">
            <summary>
            Represents Huffman static tree.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StaticTree.#cctor">
            <summary>
            Initializes static members of the StaticTree class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StaticTree.#ctor(System.Int16[],System.Int32[])">
            <summary>
            Initializes a new instance of the StaticTree class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StaticTree.TreeCodes">
            <summary>
            Static tree.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StaticTree.ExtraBits">
            <summary>
            Extra bits for each code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StaticTree.ExtraBase">
            <summary>
            Base index for extra bits.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StaticTree.Elements">
            <summary>
            Max number of elements in the tree.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StaticTree.MaxLength">
            <summary>
            Max bit length for the codes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.Tree">
            <summary>
            Deflates data using Huffman coding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Tree.BitReverse(System.Int32,System.Int32)">
            <summary>
            Reverse the first specified bits of a code,
            using straightforward code (a faster method would use a table).
            </summary>
            <param name="code">Value.</param>
            <param name="length">The length of bits to reverse.</param>
            <returns>Result of reverse.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.Tree.GetDistanceCode(System.Int32)">
            <summary>
            Map from a distance to a distance code.
            </summary>
            <remarks> 
            No side effects. DistanceCode[256] and DistanceCode[257] are never used.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Zip.Tree.BuildTree(Telerik.Windows.Zip.DeflateCompressor)">
            <summary>
            Construct one Huffman tree and assigns the code bit strings and lengths.
            Update the total bit length for the current block.
            </summary>
            <param name="manager">Deflate compressor.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.Tree.GenerateCodes(System.Int16[],System.Int32,System.Int16[])">
            <summary>
            Generate the codes for a given tree and bit counts (which need not be optimal).
            </summary>
            <param name="tree">The tree.</param>
            <param name="maxCode">Max code.</param>
            <param name="bitLengthCount">Bit length count.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.Tree.GenerateBitLengths(Telerik.Windows.Zip.DeflateCompressor)">
            <summary>
            Compute the optimal bit lengths for a tree and update the total bit length for the current block.
            </summary>
            <param name="manager">Deflate compressor.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.EncryptionStrength">
            <summary>
            Specifies values for a type of the encryption strength.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionStrength.Aes128">
            <summary>
            AES-128 encryption.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionStrength.Aes192">
            <summary>
            AES-192 encryption.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionStrength.Aes256">
            <summary>
            AES-256 encryption.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.ICompressionAlgorithm">
            <summary>
            Interface which must be implemented by all implementations of the compression algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ICompressionAlgorithm.CreateCompressor">
            <summary>
            Creates a compressor object. 
            </summary>
            <returns>A compressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ICompressionAlgorithm.CreateDecompressor">
            <summary>
            Creates a decompressor object.
            </summary>
            <returns>A decompressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ICompressionAlgorithm.Initialize(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize compression algorithm using given compression settings.
            </summary>
            <param name="settings">Compression settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaOptimizationData">
            <summary>
            The Optimization Data for LZMA match finder.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaAlgorithm">
            <summary>
            Class which implements Deflate compression algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaAlgorithm.CreateCompressor">
            <summary>
            Creates a compressor object. 
            </summary>
            <returns>A compressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaAlgorithm.CreateDecompressor">
            <summary>
            Creates a decompressor object.
            </summary>
            <returns>A decompressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaAlgorithm.Initialize(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize compression algorithm using given compression settings.
            </summary>
            <param name="settings">Compression settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaCompressor">
            <summary>
            Compressor which implements LZMA compression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaCompressor.#ctor(Telerik.Windows.Zip.LzmaSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.LzmaCompressor"/> class.
            </summary>
            <param name="settings">Settings.</param>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaCompressor.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaCompressor.CreateHeader(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates transformation header to be written into the output stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaCompressor.ProcessTransform(System.Boolean)">
            <summary>
            Transforms current input buffer.
            </summary>
            <param name="finalBlock">The final block flag.</param>
            <returns>True when output still available.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaCompressor.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources
            (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaDecompressor">
            <summary>
            Decompressor which implements LZMA decompression algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaDecompressor.#ctor(Telerik.Windows.Zip.LzmaSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.LzmaDecompressor"/> class.
            </summary>
            <param name="settings">Settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaDecompressor.LzmaDecompressorState">
            <summary>
            Represents a state of decompressor process.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaDecompressor.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaDecompressor.InitHeaderReading(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize reading of the transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaDecompressor.ProcessHeader">
            <summary>
            Process transformation header has been read.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaMatchFinderType">
            <summary>
            Specifies values for a type of the match finder for LZMA compression.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.LzmaMatchFinderType.BT2">
            <summary>
            The match finder uses two bytes for the hash.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.LzmaMatchFinderType.BT4">
            <summary>
            The match finder uses four bytes for the hash.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaSettings">
            <summary>
            Compression settings of the Deflate method.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.LzmaSettings"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.DictionarySize">
            <summary>
            Gets or sets dictionary size [0 - 27].
            Default value is 23 (8MB).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.PositionStateBits">
            <summary>
            Gets or sets number of position state bits for LZMA [0 - 4].
            Default value is 2.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.LiteralContextBits">
            <summary>
            Gets or sets number of literal context bits for LZMA [0 - 8].
            Default value is 3.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.LiteralPositionBits">
            <summary>
            Gets or sets number of literal position bits for LZMA [0 - 4].
            Default value is 3.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.FastBytes">
            <summary>
            Gets or sets number of fast bytes [5 - 273].
            Default value is 32.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.MatchFinderType">
            <summary>
            Gets or sets a type of the match finder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.StreamLength">
            <summary>
            Gets or sets length of the stream for compressing.
            Used for single compressed streams only (not for ZIP archives).
            Allows to avoid using the end of stream marker for compressed stream.
            If it is set to -1, then the marker will be used.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.InternalStreamLength">
            <summary>
            Gets or sets length of the stream for decompressing.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.LzmaSettings.UseZipHeader">
            <summary>
            Gets or sets a value which indicates whether
            the compression stream should use zip header type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaSettings.CopyFrom(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Copy settings from the given base settings.
            </summary>
            <param name="baseSettings">Base settings to copy from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaSettings.PrepareForZip(Telerik.Windows.Zip.CentralDirectoryHeader)">
            <summary>
            Prepare settings for usage in zip archive entries.
            </summary>
            <param name="header">Central directory header.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaState">
            <summary>
            Represents LZMA state for compressing and for decompressing.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaTransformBase">
            <summary>
            Represents base class for LZMA compression and decompression functionality.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LzmaTransformBase.#ctor(Telerik.Windows.Zip.LzmaSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.LzmaTransformBase"/> class.
            </summary>
            <param name="settings">Settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LzmaRangeEncoder">
            <summary>
            Represents the LZMA range encoder.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StoreAlgorithm">
            <summary>
            Class which implements Store (no compression) algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreAlgorithm.CreateCompressor">
            <summary>
            Creates a compressor object. 
            </summary>
            <returns>A compressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreAlgorithm.CreateDecompressor">
            <summary>
            Creates a decompressor object.
            </summary>
            <returns>A decompressor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreAlgorithm.Initialize(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize compression algorithm using given compression settings.
            </summary>
            <param name="settings">Compression settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.StoreCompressor">
            <summary>
            Compressor which implements Store compression.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StoreDecompressor">
            <summary>
            Decompressor which implements Store compression.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StoreSettings">
            <summary>
            Compression settings of the Store method.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreSettings.#ctor">
            <summary>
            Initializes a new instance of the StoreSettings class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StoreTransformBase">
            <summary>
            Base class for the Store (no compression) transformation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreTransformBase.#ctor">
            <summary>
            Initializes a new instance of the StoreTransformBase class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StoreTransformBase.CanReuseTransform">
            <summary>Gets a value indicating whether the current transform can be reused.</summary>
            <returns>true if the current transform can be reused; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.StoreTransformBase.CanTransformMultipleBlocks">
            <summary>Gets a value indicating whether multiple blocks can be transformed.</summary>
            <returns>true if multiple blocks can be transformed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.StoreTransformBase.InputBlockSize">
            <summary>Gets the input block size.</summary>
            <returns>The size of the input data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.StoreTransformBase.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreTransformBase.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>Transforms the specified region of the input byte array and copies the resulting transform to the specified region of the output byte array.</summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreTransformBase.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.StoreTransformBase.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DefaultPlatformManager">
            <summary>
            Platform independent manager.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DefaultPlatformManager.AltDirectorySeparatorChar">
            <summary>
            Gets a platform-specific alternate character used to separate directory levels in a path string that reflects a hierarchical file system organization.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DefaultPlatformManager.DefaultEncoding">
            <summary>
            Gets default encoding for this platform.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DefaultPlatformManager.DirectorySeparatorChar">
            <summary>
            Gets a platform-specific character used to separate directory levels in a path string that reflects a hierarchical file system organization.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DefaultPlatformManager.CreateTemporaryStream">
            <summary>
            Creates temporary stream.
            </summary>
            <returns>Stream will be used for temporary operations.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DefaultPlatformManager.DeleteTemporaryStream(System.IO.Stream)">
            <summary>
            Deletes temporary stream.
            </summary>
            <param name="stream">Stream to delete.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DefaultPlatformManager.GetCryptoProvider(Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Gets crypto provider initialized using given encryption settings.
            </summary>
            <param name="settings">Encryption settings.</param>
            <returns>Crypto provider.</returns>
            <exception cref="T:System.NotSupportedException">Specified crypto algorithm is not supported.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.DefaultPlatformManager.IsEncodingSupported(System.Text.Encoding)">
            <summary>
            Indicates whether specified encoding is supported for this platform.
            </summary>
            <param name="encoding">Encoding.</param>
            <returns>true if encoding is allowed in the ZIP file.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.AesCryptoProvider">
            <summary>
            Crypto provider which implements AES encryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.AesCryptoProvider.#ctor">
            <summary>
            Initializes a new instance of the AesCryptoProvider class with Aes256 encryption strength.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.AesCryptoProvider.CreateDecryptor">
            <summary>
            Creates an decryptor object. 
            </summary>
            <returns>A decryptor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.AesCryptoProvider.CreateEncryptor">
            <summary>
            Creates an encryptor object.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Zip.AesCryptoProvider.Initialize(Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Initialize crypto provider using given encryption settings.
            </summary>
            <param name="settings">Encryption settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.AesCryptoStream">
            <summary>
            AES Cryptographic stream. Allows encrypt or decrypt information from the given input stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.AesCryptoTransformBase.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.CryptoStream">
            <summary>
            Cryptographic stream. Allows encrypt or decrypt information from the given input stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CryptoStream.#ctor(System.IO.Stream,Telerik.Windows.Zip.StreamOperationMode,Telerik.Windows.Zip.ICryptoProvider,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initializes a new instance of the CryptoStream class.
            </summary>
            <param name="input">Input stream.</param>
            <param name="mode">Stream operational mode.</param>
            <param name="cryptoProvider">Crypto provider.</param>
            <param name="settings">Compression settings.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified mode is not allowed for the given stream.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.CryptoStream.Finalize">
            <summary>
            Allows an object to try to free resources and perform other cleanup operations before it is reclaimed by garbage collection. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CryptoStream.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the Stream and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DecryptionSettings">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.DecryptionSettings" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Zip.DecryptionSettings.PasswordRequired">
            <summary>
            Occurs when the password is required.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.EncryptionAlgorithm">
            <summary>
            Specifies values for a type of the encryption algorithm.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionAlgorithm.Unknown">
            <summary>
            Unknown encryption algorithm.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionAlgorithm.PKZIP">
            <summary>
            Traditional PKWARE encryption algorithm.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EncryptionAlgorithm.AES">
            <summary>
            Strong AES encryption algorithm.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.EncryptionSettings">
            <summary>
            Base class for the encryption settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.EncryptionSettingsBase">
            <summary>
            Base class for the encryption settings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.EncryptionSettingsBase" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.#ctor(Telerik.Windows.Zip.EncryptionAlgorithm,System.Nullable{Telerik.Windows.Zip.EncryptionStrength})">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.EncryptionSettingsBase" /> class.
            </summary>
            <param name="algorithm">The encryption algorithm.</param>
            <param name="encryptionStrength">The encryption strength.</param>
        </member>
        <member name="E:Telerik.Windows.Zip.EncryptionSettingsBase.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EncryptionSettingsBase.Algorithm">
            <summary>
            Gets name of the algorithm will be used for encryption/decryption.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EncryptionSettingsBase.FileTime">
            <summary>
            Gets or sets last modification file date and time.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EncryptionSettingsBase.EncryptionAlgorithm">
            <summary>
            Gets name of the algorithm will be used for encryption/decryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.CreatePkzipPasswordEncryptionSettings">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.PasswordEncryptionSettings" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.CreateAesPasswordEncryptionSettings">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.PasswordEncryptionSettings" /> class.
            The encryption strength is set to AES-256.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.CreateAesPasswordEncryptionSettings(Telerik.Windows.Zip.EncryptionStrength)">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.PasswordEncryptionSettings" /> class.
            </summary>
            <param name="encryptionStrength">The encryption strength.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.CreateDecryptionSettings">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.DecryptionSettings" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EncryptionSettingsBase.OnPropertyChanged(System.String)">
            <summary>
            Called when property value is changed.
            </summary>
            <param name="propertyName">Property name.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.ICryptoProvider">
            <summary>
            Interface which provides method to encrypt/decrypt data in the ZIP archive.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ICryptoProvider.CreateDecryptor">
            <summary>
            Creates an decryptor object. 
            </summary>
            <returns>A decryptor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ICryptoProvider.CreateEncryptor">
            <summary>
            Creates an encryptor object.
            </summary>
            <returns>An encryptor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ICryptoProvider.Initialize(Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Initialize crypto provider using given encryption settings.
            </summary>
            <param name="settings">Encryption settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DefaultCryptoProvider">
            <summary>
            Crypto provider which implements traditional PKWARE encryption.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.DefaultEncryptionSettings">
            <summary>
            Encryption settings for the default cryptographic provider (traditional PKWARE encryption).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DefaultEncryptionSettings.#ctor">
            <summary>
            Initializes a new instance of the DefaultEncryptionSettings class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.PasswordEncryptionSettings">
            <summary>
            Password encryption settings for the cryptographic provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PasswordEncryptionSettings.#ctor(Telerik.Windows.Zip.EncryptionAlgorithm,System.Nullable{Telerik.Windows.Zip.EncryptionStrength})">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.PasswordEncryptionSettings" /> class.
            </summary>
            <param name="algorithm">The encryption algorithm.</param>
            <param name="encryptionStrength">The encryption strength.</param>
        </member>
        <member name="P:Telerik.Windows.Zip.PasswordEncryptionSettings.Password">
            <summary>
            Gets or sets password will be used for encryption/decryption.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.PasswordRequiredEventArgs">
            <summary>
            Represents password needed event args.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PasswordRequiredEventArgs.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.PasswordRequiredEventArgs" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.PasswordRequiredEventArgs.Password">
            <summary>
            Gets or sets the password.
            </summary>
            <value>The password.</value>
        </member>
        <member name="T:Telerik.Windows.Zip.PkzipCryptoProvider">
            <summary>
            Crypto provider which implements traditional PKWARE encryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoProvider.CreateDecryptor">
            <summary>
            Creates an decryptor object. 
            </summary>
            <returns>A decryptor object.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoProvider.CreateEncryptor">
            <summary>
            Creates an encryptor object.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoProvider.Initialize(Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Initialize crypto provider using given encryption settings.
            </summary>
            <param name="settings">Encryption settings.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.PkzipCryptoTransformBase">
            <summary>
            Base class for the transformations which implements traditional PKWARE encryption/decryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.#ctor">
            <summary>
            Initializes a new instance of the DefaultCryptoTransformBase class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.PkzipCryptoTransformBase.CanReuseTransform">
            <summary>Gets a value indicating whether the current transform can be reused.</summary>
            <returns>true if the current transform can be reused; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.PkzipCryptoTransformBase.CanTransformMultipleBlocks">
            <summary>Gets a value indicating whether multiple blocks can be transformed.</summary>
            <returns>true if multiple blocks can be transformed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.PkzipCryptoTransformBase.InputBlockSize">
            <summary>Gets the input block size.</summary>
            <returns>The size of the input data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.PkzipCryptoTransformBase.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.PkzipCryptoTransformBase.EncodingByte">
            <summary>
            Gets encoding byte.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.CreateHeader(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates transformation header to be written into the output stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.InitHeaderReading(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize reading of the transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.ProcessHeader">
            <summary>
            Process transformation header has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the transform and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipCryptoTransformBase.UpdateKeys(System.Byte)">
            <summary>
            Update encryption keys.
            </summary>
            <param name="byteValue">Byte.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.PkzipDecryptor">
            <summary>
            Crypto transformation which implements traditional PKWARE decryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipDecryptor.#ctor">
            <summary>
            Initializes a new instance of the PkzipDecryptor class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipDecryptor.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>Transforms the specified region of the input byte array and copies the resulting transform to the specified region of the output byte array.</summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipDecryptor.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="T:Telerik.Windows.Zip.PkzipEncryptor">
            <summary>
            Crypto transformation which implements traditional PKWARE encryption.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipEncryptor.#ctor">
            <summary>
            Initializes a new instance of the DefaultEncryptor class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipEncryptor.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>Transforms the specified region of the input byte array and copies the resulting transform to the specified region of the output byte array.</summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.PkzipEncryptor.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="T:Telerik.Windows.Zip.IBlockTransform">
            <summary>
            Defines the basic operations of the cryptographic or compression transformations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.CanReuseTransform">
            <summary>Gets a value indicating whether the current transform can be reused.</summary>
            <returns>true if the current transform can be reused; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.CanTransformMultipleBlocks">
            <summary>Gets a value indicating whether multiple blocks can be transformed.</summary>
            <returns>true if multiple blocks can be transformed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.Header">
            <summary>Gets transformation header (if required).</summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.Footer">
            <summary>Gets transformation footer (if required).</summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.InputBlockSize">
            <summary>Gets the input block size.</summary>
            <returns>The size of the input data blocks in bytes.</returns>
        </member>
        <member name="P:Telerik.Windows.Zip.IBlockTransform.OutputBlockSize">
            <summary>Gets the output block size.</summary>
            <returns>The size of the output data blocks in bytes.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.CreateHeader(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates transformation header to be written into the output stream.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.InitHeaderReading(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initialize reading of the transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.InitFooterReading(System.Int32)">
            <summary>
            Initialize reading of the transformation footer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.ProcessHeader">
            <summary>
            Process transformation header has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.ProcessFooter">
            <summary>
            Process transformation footer has been read.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>Transforms the specified region of the input byte array and copies the resulting transform to the specified region of the output byte array.</summary>
            <returns>The number of bytes written.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the input byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the input byte array to use as data. </param>
            <param name="outputBuffer">The output to which to write the transform. </param>
            <param name="outputOffset">The offset into the output byte array from which to begin writing data. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.IBlockTransform.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>Transforms the specified region of the specified byte array.</summary>
            <returns>The computed transform.</returns>
            <param name="inputBuffer">The input for which to compute the transform. </param>
            <param name="inputOffset">The offset into the byte array from which to begin using data. </param>
            <param name="inputCount">The number of bytes in the byte array to use as data. </param>
        </member>
        <member name="T:Telerik.Windows.Zip.InvalidDataException">
            <summary>The exception that is thrown when a data stream is in an invalid format.</summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InvalidDataException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.InvalidDataException" /> class.</summary>
        </member>
        <member name="M:Telerik.Windows.Zip.InvalidDataException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.InvalidDataException" /> class with a specified error message.</summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.InvalidDataException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.InvalidDataException" /> class with a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.IPlatformManager">
            <summary>
            Interface which provides platform-specific operations. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IPlatformManager.AltDirectorySeparatorChar">
            <summary>
            Gets a platform-specific alternate character used to separate directory levels in a path string that reflects a hierarchical file system organization.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IPlatformManager.DefaultEncoding">
            <summary>
            Gets default encoding for this platform.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.IPlatformManager.DirectorySeparatorChar">
            <summary>
            Gets a platform-specific character used to separate directory levels in a path string that reflects a hierarchical file system organization.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.IPlatformManager.CreateTemporaryStream">
            <summary>
            Creates temporary stream.
            </summary>
            <returns>Stream will be used for temporary operations.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.IPlatformManager.DeleteTemporaryStream(System.IO.Stream)">
            <summary>
            Deletes temporary stream.
            </summary>
            <param name="stream">Stream to delete.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.IPlatformManager.GetCryptoProvider(Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Gets crypto provider initialized using given encryption settings.
            </summary>
            <param name="settings">Encryption settings.</param>
            <returns>Crypto provider.</returns>
            <exception cref="T:System.NotSupportedException">Specified crypto algorithm is not supported.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.IPlatformManager.GetCryptoStream(Telerik.Windows.Zip.EncryptionSettingsBase,System.IO.Stream,Telerik.Windows.Zip.StreamOperationMode,Telerik.Windows.Zip.ICryptoProvider,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Gets crypto stream initialized using given encryption settings.
            </summary>
            <param name="encryptionSettings">Encryption settings.</param>
            <param name="baseStream">Stream.</param>
            <param name="mode">Stream operation mode.</param>
            <param name="cryptoProvider">Crypto provider.</param>
            <param name="compressionSettings">Compression settings.</param>
            <returns>Crypto stream.</returns>
            <exception cref="T:System.NotSupportedException">Specified crypto algorithm is not supported.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.IPlatformManager.IsEncodingSupported(System.Text.Encoding)">
            <summary>
            Indicates whether specified encoding is supported for this platform.
            </summary>
            <param name="encoding"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Zip.ISpecData">
            <summary>
            Common interface for the data structures defined in the ZIP File Format Specification.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ISpecData.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>true if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ISpecData.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.OperationStream">
            <summary>
            Operational stream. Base class for cryptographic and compression streams.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.#ctor(System.IO.Stream,Telerik.Windows.Zip.StreamOperationMode,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Zip.OperationStream" /> class class.
            </summary>
            <param name="baseStream">The base input/output stream.</param>
            <param name="mode">Stream operational mode.</param>
            <param name="settings">The compression settings.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified mode is not allowed for the given stream.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Finalize">
            <summary>
            Allows an object to try to free resources and perform other cleanup operations before it is reclaimed by garbage collection. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.HasFlushedFinalBlock">
            <summary>Gets a value which indicates whether the final buffer block has been written/read to/from the underlying stream. </summary>
            <returns>true if the final block has been flushed or end of underlying stream is reached; otherwise, false. </returns>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.TotalPlainCount">
            <summary>
            Gets value which specify total plain bytes count (not-compressed and not-encrypted).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.TotalTransformedCount">
            <summary>
            Gets value which specify total transformed bytes count (compressed or encrypted).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.BaseStream">
            <summary>
            Gets input stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.Mode">
            <summary>
            Gets stream mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.IsDisposed">
            <summary>
            Gets or sets value which indicates whether this stream is disposed already.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.OperationStream.Transform">
            <summary>
            Gets or sets block transformation is used for read/write operations.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be written to the underlying device.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the 
            values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream. </param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many 
            bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
            <exception cref="T:System.NotSupportedException">The <see cref="T:Telerik.Windows.Zip.StreamOperationMode" /> associated with 
            current <see cref="T:Telerik.Windows.Zip.OperationStream" /> object does not match the underlying stream.  
            For example, this exception is thrown when using <see cref="F:Telerik.Windows.Zip.StreamOperationMode.Read" /> with an underlying stream that is write only.  </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than zero.-or- The <paramref name="count" /> parameter is less than zero. </exception>
            <exception cref="T:System.ArgumentException">The sum of the <paramref name="count" /> and <paramref name="offset" /> parameters is larger than the length of the buffer. </exception>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream. 
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type SeekOrigin indicating the reference point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes. </param>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from buffer to the current stream. </param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream. </param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.ValidateBufferParameters(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Validate read/write operation parameters.
            </summary>
            <param name="buffer">Operation buffer.</param>
            <param name="offset">Offset.</param>
            <param name="count">Count.</param>
            <param name="allowZeroCount">Indicates whether count can be zero.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.FlushFinalBlock">
            <summary>Updates the underlying data source or repository with the current state of the buffer, then clears the buffer.</summary>
            <exception cref="T:System.NotSupportedException">The current stream is not writable.-or- The final block has already been transformed. </exception>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.EnsureNotDisposed">
            <summary>
            Ensure that current stream is not disposed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the Stream and optionally releases the managed resources. 
            </summary>
            <param name="disposing">Value which indicates whether both managed and unmanaged resources (true) on only unmanaged resources (false) should be released.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.InitializeBuffers">
            <summary>
            Initialize internal buffers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.InitializeTransform">
            <summary>
            Initialize transformation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.ReadTransformationHeader">
            <summary>
            Read transformation header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.ReadTransformationFooter">
            <summary>
            Read transformation footer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.OperationStream.WriteTransformationHeader">
            <summary>
            Write transformation header.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.PlatformSettings">
            <summary>
            Static class which provides access to the platform-specific settings for all 
            parts of the ZIP library.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.PlatformSettings.Manager">
            <summary>
            Gets or sets platform manager.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StreamOperationMode">
            <summary>
            Operational mode of the cryptographic and compression streams.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.StreamOperationMode.Read">
            <summary>
            Read operation is allowed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.StreamOperationMode.Write">
            <summary>
            Write operation is allowed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.TransformationFooter">
            <summary>
            Represents footer of the transformation.
            The extra data succeeds the transformed data which provides
            some additional information about transformation (compression or encryption).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.TransformationFooter.#ctor">
            <summary>
            Initializes a new instance of the TransformationFooter class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationFooter.Buffer">
            <summary>
            Gets or sets buffer to store footer information.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationFooter.BytesToRead">
            <summary>
            Gets or sets number of byte to read.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationFooter.BytesToSkip">
            <summary>
            Gets or sets the actual encrypted file data to skip.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.TransformationHeader">
            <summary>
            Represents header of the transformation.
            The extra data precedes the transformed data which provides
            some additional information about transformation (compression or encryption).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.TransformationHeader.#ctor">
            <summary>
            Initializes a new instance of the TransformationHeader class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationHeader.Buffer">
            <summary>
            Gets or sets buffer to store header information.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationHeader.BytesToRead">
            <summary>
            Gets or sets number of byte to read.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationHeader.InitData">
            <summary>
            Gets initialization data of the header.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationHeader.Length">
            <summary>
            Gets length of the transformation header.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.TransformationHeader.CountHeaderInCompressedSize">
            <summary>
            Gets or sets the flag which indicates
            that the compressed size should include the header size.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.Utilities.StreamExtensions">
            <summary>
            Extensions for the <see cref="T:System.IO.Stream"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Utilities.StreamExtensions.ReadExactly(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads <paramref name="count"/> number of bytes from the current stream and advances the position within the stream.
            </summary>
            <param name="stream">
            The stream to read from.
            </param>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified byte array with the values
            between <paramref name="offset"/> and (<paramref name="offset"/> + <paramref name="count"/> - 1) replaced
            by the bytes read from the current stream.
            </param>
            <param name="offset">The byte offset in <paramref name="buffer"/> at which to begin storing the data read from the current stream.</param>
            <param name="count">The number of bytes to be read from the current stream.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="offset"/> is outside the bounds of <paramref name="buffer"/>.
            -or-
            <paramref name="count"/> is negative.
            -or-
            The range specified by the combination of <paramref name="offset"/> and <paramref name="count"/> exceeds the
            length of <paramref name="buffer"/>.
            </exception>
            <exception cref="T:System.IO.EndOfStreamException">
            The end of the stream is reached before reading <paramref name="count"/> number of bytes.
            </exception>
            <remarks>
            When <paramref name="count"/> is 0 (zero), this read operation will be completed without waiting for available data in the stream.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Zip.Zip64DataDescriptor">
            <summary>
            Represents data descriptor record described in the
            ZIP File Format Specification v6.3.3, #4.3.9.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Zip64DataDescriptor.Signature">
            <summary>
            Data descriptor header signature.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64DataDescriptor.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64DataDescriptor.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.Zip64DataDescriptorBase">
            <summary>
            Represents base fields of data descriptor record described in the
            ZIP File Format Specification v6.3.3, #4.3.9.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64DataDescriptorBase.Crc">
            <summary>
            Gets or sets crc-32.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64DataDescriptorBase.CompressedSize">
            <summary>
            Gets or sets compressed size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64DataDescriptorBase.UncompressedSize">
            <summary>
            Gets or sets uncompressed size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64DataDescriptorBase.ReadFields(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64DataDescriptorBase.ReadSize(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64DataDescriptorBase.WriteFields(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator">
            <summary>
            Represents Zip64 end of central directory locator described in the
            ZIP File Format Specification v6.3.3, #4.3.15.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.Signature">
            <summary>
            Zip64 end of central directory locator signature.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.StaticBlockLength">
            <summary>
            Size of the data block without signature and variable size fields.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.NumberOfTheDiskWithTheStartOfTheZip64EndOfCentralDirectory">
            <summary>
            Gets or sets number of the disk with the
            start of the zip64 end of
            central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.RelativeOffsetOfTheZip64EndOfCentralDirectoryRecord">
            <summary>
            Gets or sets relative offset of the zip64
            end of central directory record.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.NumberOfDisks">
            <summary>
            Gets or sets number of disks.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryLocator.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord">
            <summary>
            Represents Zip64 end of central directory record described in the
            ZIP File Format Specification v6.3.3, #4.3.14.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.Signature">
            <summary>
            Zip64 end of central directory record signature.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.SizeOfZip64EndOfCentralDirectoryRecord">
            <summary>
            Gets or sets size of zip64 end of central
            directory record.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.VersionMadeBy">
            <summary>
            Gets or sets byte which indicates the ZIP specification version
            supported by the software used to encode the file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.OsCompatibility">
            <summary>
            Gets or sets byte which indicates the compatibility
            of the file attribute information.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.VersionNeededToExtract">
            <summary>
            Gets or sets version needed to extract.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.NumberOfThisDisk">
            <summary>
            Gets or sets number of this disk.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.NumberOfTheDiskWithTheStartOfTheCentralDirectory">
            <summary>
            Gets or sets number of the disk with the start of the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.NumberOfEntriesInTheCentralDirectoryOnThisDisk">
            <summary>
            Gets or sets total number of entries in the central directory on this disk.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.NumberOfEntriesInTheCentralDirectory">
            <summary>
            Gets or sets total number of entries in the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.SizeOfCentralDirectory">
            <summary>
            Gets or sets size of the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.OffsetOfStartOfCentralDirectoryWithRespectToTheStartingDiskNumber">
            <summary>
            Gets or sets offset of start of central directory with respect to the starting disk number.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64EndOfCentralDirectoryRecord.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.ZipArchive">
            <summary>
            Represents a package of compressed files in the zip archive format. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.archiveMode">
            <summary>
            Value that describes the type of action the zip archive can perform on entries.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.archiveReader">
            <summary>
            Binary reader is used to read from working stream.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.archiveWriter">
            <summary>
            Binary writer is used to write to working stream.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.disposed">
            <summary>
            Track whether Dispose has been called. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.entryNameEncoding">
            <summary>
            Encoding of the entry name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.originalStream">
            <summary>
            Original archive stream. If this stream doesn't support seeking then 
            temporary working stream will be created.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.workingStream">
            <summary>
            Working archive stream. If original stream doesn't support seeking then 
            temporary working stream will be created.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.leaveStreamOpen">
            <summary>
            True to leave the stream open after the ZipArchive object is disposed; otherwise, false.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.centralDirectoryRead">
            <summary>
            Indicates whether the central directory have been read.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.endOfCentralDirectoryRecord">
            <summary>
            ZIP Archive End of Central Directory.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.zip64EndOfCentralDirectoryLocator">
            <summary>
            ZIP64 End of Central Directory Locator.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.zip64EndOfCentralDirectoryRecord">
            <summary>
            ZIP64 End of Central Directory Record.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchive.entries">
            <summary>
            ZIP entries.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the ZipArchive class from the specified stream.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.#ctor(System.IO.Stream,Telerik.Windows.Zip.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
            <summary>
            Initializes a new instance of the ZipArchive class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="mode">One of the enumeration values that indicates whether the zip archive is used to read, create, or update entries.</param>
            <param name="leaveOpen">True to leave the stream open after the ZipArchive object is disposed; otherwise, false.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.#ctor(System.IO.Stream,Telerik.Windows.Zip.ZipArchiveMode,System.Boolean,System.Text.Encoding,Telerik.Windows.Zip.CompressionSettings,Telerik.Windows.Zip.EncryptionSettingsBase)">
            <summary>
            Initializes a new instance of the ZipArchive class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="mode">One of the enumeration values that indicates whether the zip archive is used to read, create, or update entries.</param>
            <param name="leaveOpen">True to leave the stream open after the ZipArchive object is disposed; otherwise, false.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
            <param name="compressionSettings">Compression settings.</param>
            <param name="encryptionSettings">Encryption settings.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Finalize">
            <summary>
            Allows an object to try to free resources and perform other cleanup operations before it is reclaimed by garbage collection. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Zip.ZipArchive.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.Entries">
            <summary>
            Gets the collection of entries that are currently in the zip archive.
            </summary>
            <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
            <exception cref="T:Telerik.Windows.Zip.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.EntryNameEncoding">
            <summary>
            Gets entry name encoding.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.Mode">
            <summary>
            Gets a value that describes the type of action the zip archive can perform on entries.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.CompressionSettings">
            <summary>
            Gets compression settings.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.EncryptionSettings">
            <summary>
            Gets encryption settings.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.NumberOfThisDisk">
            <summary>
            Gets number of the disk.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.Reader">
            <summary>
            Gets reader for the working stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.Writer">
            <summary>
            Gets writer for the working stream.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchive.CentralDirectoryStart">
            <summary>
            Gets start of the central directory.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Read(System.IO.Stream)">
            <summary>
            Opens an existing archive and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Read(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Opens an existing archive and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Read(System.IO.Stream,System.Text.Encoding,Telerik.Windows.Zip.CompressionSettings,Telerik.Windows.Zip.DecryptionSettings)">
            <summary>
            Opens an existing archive and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
            <param name="compressionSettings">Compression settings.</param>
            <param name="decryptionSettings">Decryption settings.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Create(System.IO.Stream)">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Create(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Create(System.IO.Stream,System.Text.Encoding,Telerik.Windows.Zip.CompressionSettings,Telerik.Windows.Zip.EncryptionSettings)">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
            <param name="compressionSettings">Compression settings.</param>
            <param name="encryptionSettings">Encryption settings.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Update(System.IO.Stream)">
            <summary>
            Opens an existing archive for update and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Update(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Opens an existing archive for update and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Update(System.IO.Stream,System.Text.Encoding,Telerik.Windows.Zip.CompressionSettings,Telerik.Windows.Zip.DecryptionSettings)">
            <summary>
            Opens an existing archive for update and returns a new instance of the <see cref="T:Telerik.Windows.Zip.ZipArchive" /> class.
            </summary>
            <param name="stream">The stream that contains the archive to be read.</param>
            <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter 
            only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
            <param name="compressionSettings">Compression settings.</param>
            <param name="decryptionSettings">Decryption settings.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.CreateEntry(System.String)">
            <summary>
            Creates an empty entry that has the specified path and entry name in the zip archive.
            </summary>
            <param name="entryName">A path, relative to the root of the archive, that specifies the name of the entry to be created.</param>
            <returns>An empty entry in the zip archive.</returns>
            <exception cref="T:System.ArgumentException">The entry name is empty.</exception>
            <exception cref="T:System.ArgumentNullException">The entry name is null.</exception>
            <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.CreateEntry(System.String,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Creates an empty entry that has the specified path and entry name in the zip archive.
            </summary>
            <param name="entryName">A path, relative to the root of the archive, that specifies the name of the entry to be created.</param>
            <param name="settings">Compression settings.</param>
            <returns>An empty entry in the zip archive.</returns>
            <exception cref="T:System.ArgumentException">The entry name is empty.</exception>
            <exception cref="T:System.ArgumentNullException">The entry name is null.</exception>
            <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Dispose">
            <summary>
            Releases the resources used by the current instance of the ZipArchive class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.GetEntry(System.String)">
            <summary>
            Retrieves a wrapper for the specified entry in the zip archive.
            </summary>
            <param name="entryName">A path, relative to the root of the archive, that identifies the entry to retrieve.</param>
            <returns>A wrapper for the specified entry in the archive; null if the entry does not exist in the archive.</returns>
            <exception cref="T:System.ArgumentException">The entry name is empty.</exception>
            <exception cref="T:System.ArgumentNullException">The entry name is null.</exception>
            <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
            <exception cref="T:Telerik.Windows.Zip.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.DisposeStreams(System.Boolean)">
            <summary>
            Release the unmanaged resources used by the current instance of the ZipArchive class.
            </summary>
            <param name="closeStream">True to leave the stream open after the ZipArchive object is disposed; otherwise, false.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.Dispose(System.Boolean)">
            <summary>
            Called by the Dispose() and Finalize() methods to release the unmanaged 
            resources used by the current instance of the ZipArchive class, and optionally 
            finishes writing the archive and releases the managed resources.
            </summary>
            <param name="disposing">True to finish writing the archive and release unmanaged and managed resources; 
            false to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.DisposeStreams">
            <summary>
            Dispose streams.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchive.WriteArchive">
            <summary>
            Writes archive to the original stream.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.ZipArchiveEntry">
            <summary>
            Represents a compressed file within a zip archive.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchiveEntry.disposed">
            <summary>
            Track whether Dispose has been called. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.#ctor(Telerik.Windows.Zip.ZipArchive,Telerik.Windows.Zip.CentralDirectoryHeader)">
            <summary>
            Initializes a new instance of the ZipArchiveEntry class.
            </summary>
            <param name="archive">Zip archive.</param>
            <param name="header">Central directory header correspondent to this entry.</param>
        </member>
        <member name="E:Telerik.Windows.Zip.ZipArchiveEntry.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.Archive">
            <summary>
            The zip archive that the entry belongs to, or null if the entry has been deleted.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.CompressedLength">
            <summary>
            Gets compressed size of the entry in the zip archive.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.ExternalAttributes">
            <summary>
            Gets or sets external file attributes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.FullName">
            <summary>
            Gets the relative path of the entry in the zip archive.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.LastWriteTime">
            <summary>
            Gets or sets the last time the entry in the zip archive was changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.Length">
            <summary>
            Gets the uncompressed size of the entry in the zip archive.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.Name">
            <summary>
            Gets the file name of the entry in the zip archive.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.CompressionMethod">
            <summary>
            Gets or sets compression method.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.CompressedDataOffset">
            <summary>
            Gets or sets offset of the compressed data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.DiskStartNumber">
            <summary>
            Gets disk start number.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.LocalHeaderOffset">
            <summary>
            Gets or sets offset of the local header.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ZipArchiveEntry.UpdatableData">
            <summary>
            Gets temporary stream which contains uncompressed data for update.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.Delete">
            <summary>
            Deletes the entry from the zip archive.
            </summary>
            <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
            <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than Update.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.Dispose">
            <summary>
            Releases the resources used by the current instance of the ZipArchiveEntry class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.Open">
            <summary>
            Opens the entry from the zip archive.
            </summary>
            <returns>The stream that represents the contents of the entry.</returns>
            <remarks>The resulting stream depends on the zip archive mode.
            If zip archive mode is <see cref="F:Telerik.Windows.Zip.ZipArchiveMode.Create"/> then read-only stream without seeking support is returned (<see cref="T:Telerik.Windows.Zip.CompressedStream"/>).
            If zip archive mode is <see cref="F:Telerik.Windows.Zip.ZipArchiveMode.Read"/> then write-only stream without seeking support is returned (<see cref="T:Telerik.Windows.Zip.CompressedStream"/>).
            If zip archive mode is <see cref="F:Telerik.Windows.Zip.ZipArchiveMode.Update"/> then read/write stream which supports seeking is returned.
            </remarks>
            <exception cref="T:System.IO.IOException">The entry is already currently open for writing.
            -or-
            The entry has been deleted from the archive.
            -or-
            The archive for this entry was opened with the Create mode, and this entry has already been written to.</exception>
            <exception cref="T:System.NotSupportedException">The entry is either missing from the archive or is corrupt and cannot be read. 
            -or-
            The entry has been compressed by using a compression method that is not supported.</exception>
            <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.CheckIntegrity(System.String@)">
            <summary>
            Checks entry integrity.
            </summary>
            <param name="message">Message will be thrown if entry don't pass integrity check.</param>
            <returns>True - if entry is OK; false - otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.WriteCentralDirectoryHeader">
            <summary>
            Writes central directory header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipArchiveEntry.Dispose(System.Boolean)">
            <summary>
            Called by the Dispose() and Finalize() methods to release the unmanaged 
            resources used by the current instance of the ZipArchive class, and optionally 
            finishes writing the archive and releases the managed resources.
            </summary>
            <param name="disposing">True to finish writing the archive and release unmanaged and managed resources; 
            false to release only unmanaged resources.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.ZipArchiveMode">
            <summary>
            Specifies values for interacting with zip archive entries.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchiveMode.Create">
            <summary>
            Only creating new archive entries is permitted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchiveMode.Read">
            <summary>
            Only reading archive entries is permitted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ZipArchiveMode.Update">
            <summary>
            Both read and write operations are permitted for archive entries.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.ZipHelper">
            <summary>
            Provides common internal static methods.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.CopyStream(System.IO.Stream,System.IO.Stream,System.Int64)">
            <summary>
            Copy specified number of bytes from one stream to another.
            </summary>
            <param name="input">Input stream.</param>
            <param name="output">Output stream.</param>
            <param name="bytes">Number of bytes to copy.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.DateTimeToPacked(System.DateTime)">
            <summary>
            Converts .NET DateTime structure to the MS-DOS date-time.
            </summary>
            <param name="time">DateTime structure to convert.</param>
            <returns>Packed date-time.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.GetCompressionAlgorithm(Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Gets compression algorithm which corresponds to the given compression settings.
            </summary>
            <param name="settings">Compression settings to get algorithm for.</param>
            <returns>Compression algorithm.</returns>
            <exception cref="T:System.NotSupportedException">Compression method is not supported.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.GetCompressionSettings(Telerik.Windows.Zip.CompressionMethod,Telerik.Windows.Zip.CompressionSettings)">
            <summary>
            Gets compression settings for the specified compression method.
            </summary>
            <param name="method">Compression method to get settings for.</param>
            <param name="baseSettings">Base settings to copy parameters from.</param>
            <returns>Compression settings.</returns>
            <exception cref="T:System.NotSupportedException">Compression method is not supported.</exception>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.EndsWithDirChar(System.String)">
            <summary>
            Detect whether the given path string ends with directory separator char (i.e. given path represents directory).
            </summary>
            <param name="path">Path string.</param>
            <returns>True if path string ends with directory separator char; otherwise - false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.IsCompressionMethodSupported(Telerik.Windows.Zip.CompressionMethod)">
            <summary>
            Gets value which indicates whether specified compression method is supported.
            </summary>
            <param name="method">Compression method to check.</param>
            <returns>True - if compression method is supported; false - otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.PackedToDateTime(System.UInt32)">
            <summary>
            Converts MS-DOS date-time to the .NET DateTime structure.
            </summary>
            <param name="packedDateTime">Packed date-time to convert.</param>
            <returns>DataTime structure.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.ReadBytes(System.IO.Stream,System.Byte[],System.Int32)">
            <summary>
            Read specified number of bytes from the given stream to the buffer.
            </summary>
            <param name="stream">Stream to read data from.</param>
            <param name="buffer">Buffer to write data to.</param>
            <param name="bytesToRead">Number of bytes to read.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ZipHelper.SeekBackwardsToSignature(System.IO.Stream,System.UInt32)">
            <summary>
            Seek given stream backward to the data signature.
            </summary>
            <param name="stream">Stream to seek.</param>
            <param name="signatureToFind">Signature to find.</param>
            <returns>true if signature is found, otherwise false.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.CentralDirectoryHeader">
            <summary>
            Represents central directory header record described in the
            ZIP File Format Specification v6.3.3, #4.3.12.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CentralDirectoryHeader.Signature">
            <summary>
            Central directory header signature.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.CentralDirectoryHeader.StaticBlockLength">
            <summary>
            Size of the data block without signature and variable size fields.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.VersionMadeBy">
            <summary>
            Gets or sets byte which indicates the ZIP specification version
            supported by the software used to encode the file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.OsCompatibility">
            <summary>
            Gets or sets byte which indicates the compatibility
            of the file attribute information.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.DiskNumberStart">
            <summary>
            Gets or sets disk number start.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.InternalAttributes">
            <summary>
            Gets or sets internal file attributes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.ExternalAttributes">
            <summary>
            Gets or sets external file attributes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.LocalHeaderOffset">
            <summary>
            Gets or sets relative offset of local header.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.CentralDirectoryHeader.FileComment">
            <summary>
            Gets or sets file comment.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.CentralDirectoryHeader.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.CentralDirectoryHeader.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DataDescriptor">
            <summary>
            Represents data descriptor record described in the
            ZIP File Format Specification v6.3.3, #4.3.9.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DataDescriptor.Signature">
            <summary>
            Data descriptor header signature.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DataDescriptor.StaticBlockLength">
            <summary>
            Size of the data block without signature and variable size fields.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DataDescriptor.CompressedSizeZip64">
            <summary>
            Gets or sets compressed size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DataDescriptor.UncompressedSizeZip64">
            <summary>
            Gets or sets uncompressed size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptor.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptor.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptor.FromFileHeader(Telerik.Windows.Zip.FileHeaderBase)">
            <summary>
            Copy properties from the given file header to this object.
            </summary>
            <param name="fileHeader">File header to copy properties from.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DataDescriptorBase">
            <summary>
            Represents base fields of data descriptor record described in the
            ZIP File Format Specification v6.3.3, #4.3.9.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DataDescriptorBase.Crc">
            <summary>
            Gets or sets crc-32.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DataDescriptorBase.CompressedSize">
            <summary>
            Gets or sets compressed size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.DataDescriptorBase.UncompressedSize">
            <summary>
            Gets or sets uncompressed size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptorBase.ReadFields(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptorBase.ReadSize(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.DataDescriptorBase.WriteFields(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.DeflatingFlags">
            <summary>
            Represents general purpose bit flag for Methods 8 and 9 - Deflating 
            ZIP File Format Specification v6.3.3, #4.4.4.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflatingFlags.Normal">
            <summary>
            Bit 2  Bit 1
                0      0    Normal (-en) compression option was used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflatingFlags.Maximum">
            <summary>
            Bit 2  Bit 1
                0      1    Maximum (-exx/-ex) compression option was used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflatingFlags.Fast">
            <summary>
            Bit 2  Bit 1
                1      0    Fast (-ef) compression option was used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.DeflatingFlags.SuperFast">
            <summary>
            Bit 2  Bit 1
                1      1    Super Fast (-es) compression option was used.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.EndOfCentralDirectoryRecord">
            <summary>
            Represents end of central directory record described in the
            ZIP File Format Specification v6.3.3, #4.3.16.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.Signature">
            <summary>
            End of central directory signature.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.StaticBlockLength">
            <summary>
            Size of the data block without signature and variable size fields.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.NumberOfThisDisk">
            <summary>
            Gets or sets number of this disk.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.NumberOfTheDiskWithTheStartOfTheCentralDirectory">
            <summary>
            Gets or sets number of the disk with the start of the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.NumberOfEntriesInTheCentralDirectoryOnThisDisk">
            <summary>
            Gets or sets total number of entries in the central directory on this disk.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.NumberOfEntriesInTheCentralDirectory">
            <summary>
            Gets or sets total number of entries in the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.SizeOfCentralDirectory">
            <summary>
            Gets or sets size of the central directory.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.OffsetOfStartOfCentralDirectoryWithRespectToTheStartingDiskNumber">
            <summary>
            Gets or sets offset of start of central directory with respect to the starting disk number.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.ArchiveComment">
            <summary>
            Gets or sets .ZIP file comment.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>true if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.EndOfCentralDirectoryRecord.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.AesEncryptionExtraField">
            <summary>
            Represents base class for extra field described in the
            ZIP File Format Specification v6.3.3, #4.5.2.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.AesEncryptionExtraField.VendorVersion">
            <summary>
            Gets or sets vendor version for this record.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.AesEncryptionExtraField.Signature">
            <summary>
            Gets or sets signature (AE).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.AesEncryptionExtraField.KeyLength">
            <summary>
            Gets or sets bit length of encryption key.
            1 - 128-bit , 2 - 192-bit , 3 - 256-bit.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.AesEncryptionExtraField.Method">
            <summary>
            Gets or sets method.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.AesEncryptionExtraField.HeaderId">
            <summary>
            Gets extra field type (Header ID).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.AesEncryptionExtraField.GetBlock">
            <summary>
            Gets extra field data.
            </summary>
            <returns>Byte array of extra field data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.AesEncryptionExtraField.ParseBlock(System.Byte[])">
            <summary>
            Implements parsing of extra field data.
            </summary>
            <param name="extraData">Extra field data.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.ExtraFieldBase">
            <summary>
            Represents base class for extra field described in the
            ZIP File Format Specification v6.3.3, #4.5.2.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ExtraFieldBase.ExtraFieldType">
            <summary>
            Gets known extra field type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.ExtraFieldBase.HeaderId">
            <summary>
            Gets extra field type (Header ID).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.ExtraFieldBase.GetExtraFields(Telerik.Windows.Zip.FileHeaderInfoBase)">
            <summary>
            Gets extra field collection.
            </summary>
            <param name="headerInfo">The header info.</param>
            <returns>IEnumerable of ExtraFieldBase instances.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ExtraFieldBase.GetExtraFieldsData(System.Collections.Generic.IEnumerable{Telerik.Windows.Zip.ExtraFieldBase})">
            <summary>
            Gets extra field data.
            </summary>
            <param name="fields">Extra field collection.</param>
            <returns>Extra field data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.ExtraFieldBase.ParseBlock(System.Byte[])">
            <summary>
            Should implement parsing of extra field data.
            </summary>
            <param name="extraData">Extra field data.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.ExtraFieldBase.GetBlock">
            <summary>
            Gets extra field data.
            </summary>
            <returns>Byte array of extra field data.</returns>
        </member>
        <member name="T:Telerik.Windows.Zip.ExtraFieldType">
            <summary>
            Represents extra field type (Header ID) described in the
            ZIP File Format Specification v6.3.3, #4.5.2.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.Unknown">
            <summary>
            Unknown extra field type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.Zip64">
            <summary>
            Zip64 extra field type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.Ntfs">
            <summary>
            Ntfs extra field type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.StrongEncryption">
            <summary>
            StrongEncryption extra field type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.UnixTime">
            <summary>
            UnixTime extra field type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ExtraFieldType.AesEncryption">
            <summary>
            AesEncryption extra field type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.StrongEncryptionExtraField">
            <summary>
            Represents strong encryption extra field described in the
            ZIP File Format Specification v6.3.3, #4.5.12.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StrongEncryptionExtraField.Format">
            <summary>
            Gets or sets format definition for this record.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StrongEncryptionExtraField.AlgorithmId">
            <summary>
            Gets or sets encryption algorithm identifier.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StrongEncryptionExtraField.KeyLength">
            <summary>
            Gets or sets bit length of encryption key.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StrongEncryptionExtraField.Flags">
            <summary>
            Gets or sets processing flags.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.StrongEncryptionExtraField.HeaderId">
            <summary>
            Gets extra field type (Header ID).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.StrongEncryptionExtraField.GetBlock">
            <summary>
            Gets extra field data.
            </summary>
            <returns>Byte array of extra field data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.StrongEncryptionExtraField.ParseBlock(System.Byte[])">
            <summary>
            Implements parsing of extra field data.
            </summary>
            <param name="extraData">Extra field data.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.UnknownExtraField">
            <summary>
            Represents base class for extra field described in the
            ZIP File Format Specification v6.3.3, #4.5.2.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.UnknownExtraField.#ctor(System.UInt16)">
            <summary>
            Initializes a new instance of the UnknownExtraField class.
            </summary>
            <param name="fieldHeaderId">Header Id.</param>
        </member>
        <member name="P:Telerik.Windows.Zip.UnknownExtraField.HeaderId">
            <summary>
            Gets extra field type (Header ID).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.UnknownExtraField.ExtraFieldData">
            <summary>
            Gets or sets extra field data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.UnknownExtraField.GetBlock">
            <summary>
            Gets extra field data.
            </summary>
            <returns>Byte array of extra field data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.UnknownExtraField.ParseBlock(System.Byte[])">
            <summary>
            Implements parsing of extra field data.
            </summary>
            <param name="extraData">Extra field data.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.Zip64ExtraField">
            <summary>
            Represents Zip64 Extended Information Extra Field described in the
            ZIP File Format Specification v6.3.3, #4.5.3.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64ExtraField.OriginalSize">
            <summary>
            Gets or sets original uncompressed file size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64ExtraField.CompressedSize">
            <summary>
            Gets or sets size of compressed data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64ExtraField.RelativeHeaderOffset">
            <summary>
            Gets or sets offset of local header record.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64ExtraField.DiskStartNumber">
            <summary>
            Gets or sets number of the disk on which this file starts.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.Zip64ExtraField.HeaderId">
            <summary>
            Gets extra field type (Header ID).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64ExtraField.GetBlock">
            <summary>
            Gets extra field data.
            </summary>
            <returns>Byte array of extra field data.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.Zip64ExtraField.ParseBlock(System.Byte[])">
            <summary>
            Implements parsing of extra field data.
            </summary>
            <param name="extraData">Extra field data.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.FileHeaderBase">
            <summary>
            Represents file header base class for
            the local file header and central directory header
            which are described in the ZIP File Format Specification v6.3.3, #4.3.7 and #4.3.12.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.VersionNeededToExtract">
            <summary>
            Gets or sets version needed to extract.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.GeneralPurposeBitFlag">
            <summary>
            Gets or sets general purpose bit flag.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.CompressionMethod">
            <summary>
            Gets or sets compression method.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.FileTime">
            <summary>
            Gets or sets last modification file date and time.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.FileName">
            <summary>
            Gets or sets file name.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.ExtraFieldsData">
            <summary>
            Gets or sets extra fields data.
            </summary>
            <value>The extra fields data.</value>
        </member>
        <member name="P:Telerik.Windows.Zip.FileHeaderBase.ExtraFields">
            <summary>
            Gets or sets list of extra fields.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.FileHeaderBase.FromFileHeader(Telerik.Windows.Zip.FileHeaderBase)">
            <summary>
            Copy properties from the given file header to this object.
            </summary>
            <param name="fileHeader">File header to copy properties from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.FileHeaderBase.ReadFields(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.FileHeaderBase.ReadExtraData(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.FileHeaderBase.WriteFields(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.FileHeaderBase.WriteExtraData(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.GeneralPurposeBitFlag">
            <summary>
            Represents general purpose bit flag in the 
            ZIP File Format Specification v6.3.3, #4.4.4.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.FileIsEncrypted">
            <summary>
            Bit 0: If set, indicates that the file is encrypted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.ZeroLocalHeader">
            <summary>
            Bit 3: If this bit is set, the fields crc-32, compressed 
            size and uncompressed size are set to zero in the 
            local header.  The correct values are put in the 
            data descriptor immediately following the compressed
            data. 
            </summary>
            <remarks>
            Note: PKZIP version 2.04g for DOS only 
            recognizes this bit for method 8 compression, newer 
            versions of PKZIP recognize this bit for any 
            compression method.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.ReservedForEnhancedDeflating">
            <summary>
            Bit 4: Reserved for use with method 8, for enhanced
            deflating. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.CompressedPatchedData">
            <summary>
            Bit 5: If this bit is set, this indicates that the file is 
            compressed patched data.  
            </summary>
            <remarks>
            Note: Requires PKZIP version 2.70 or greater.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.StrongEncryption">
            <summary>
            Bit 6: Strong encryption.  If this bit is set, you MUST
            set the version needed to extract value to at least
            50 and you MUST also set bit 0.  If AES encryption
            is used, the version needed to extract value MUST 
            be at least 51. See the section describing the Strong
            Encryption Specification for details.  Refer to the 
            section in this document entitled "Incorporating PKWARE 
            Proprietary Technology into Your Product" for more 
            information.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.EncodingUtf8">
            <summary>
            Bit 11: Language encoding flag (EFS).  If this bit is set,
            the filename and comment fields for this file
            MUST be encoded using UTF-8 (see APPENDIX D).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.GeneralPurposeBitFlag.HideLocalHeader">
            <summary>
            Bit 13: Set when encrypting the Central Directory to indicate 
            selected data values in the Local Header are masked to
            hide their actual values.  See the section describing 
            the Strong Encryption Specification for details.  Refer
            to the section in this document entitled "Incorporating 
            PKWARE Proprietary Technology into Your Product" for 
            more information.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.ImplodingFlags">
            <summary>
            Represents general purpose bit flag for the Method 6 - Imploding 
            ZIP File Format Specification v6.3.3, #4.4.4.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ImplodingFlags.SlidingDictionary8K">
            <summary>
            For Method 6 - Imploding.
            Bit 1: If the compression method used was type 6,
            Imploding, then this bit, if set, indicates
            an 8K sliding dictionary was used.  If clear,
            then a 4K sliding dictionary was used.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.ImplodingFlags.ShannonFano3">
            <summary>
            For Method 6 - Imploding.
             Bit 2: If the compression method used was type 6,
             Imploding, then this bit, if set, indicates
             3 Shannon-Fano trees were used to encode the
             sliding dictionary output.  If clear, then 2
             Shannon-Fano trees were used.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.LocalFileHeader">
            <summary>
            Represents local file header record described in the
            ZIP File Format Specification v6.3.3, #4.3.7.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.LocalFileHeader.Signature">
            <summary>
            Local file header signature.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.LocalFileHeader.StaticBlockLength">
            <summary>
            Size of the data block without signature and variable size fields.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LocalFileHeader.#ctor">
            <summary>
            Initializes a new instance of the LocalFileHeader class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Zip.LocalFileHeader.#ctor(Telerik.Windows.Zip.FileHeaderBase)">
            <summary>
            Initializes a new instance of the LocalFileHeader class.
            </summary>
            <param name="fileHeader">File header to copy properties from.</param>
        </member>
        <member name="M:Telerik.Windows.Zip.LocalFileHeader.TryReadBlock(System.IO.BinaryReader)">
            <summary>
            Read data from the binary reader.
            </summary>
            <param name="reader">Binary reader to read data from.</param>
            <returns>True if success, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Windows.Zip.LocalFileHeader.WriteBlock(System.IO.BinaryWriter)">
            <summary>
            Write data to the binary writer.
            </summary>
            <param name="writer">Binary writer to write data to.</param>
        </member>
        <member name="T:Telerik.Windows.Zip.LZMAFlags">
            <summary>
            Represents general purpose bit flag for the Method 14 - LZMA 
            ZIP File Format Specification v6.3.3, #4.4.4.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Zip.LZMAFlags.UseEndOfStreamMarker">
            <summary>
            Bit 1: If the compression method used was type 14,
            LZMA, then this bit, if set, indicates
            an end-of-stream (EOS) marker is used to
            mark the end of the compressed data stream.
            If clear, then an EOS marker is not present
            and the compressed data size must be known
            to extract.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Zip.VersionNeededToExtract">
            <summary>
            Version needed to extract.
            </summary>
        </member>
    </members>
</doc>
