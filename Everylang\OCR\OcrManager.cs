﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.View.Controls.Ocr;
using NHotkey;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;

namespace Everylang.App.OCR
{
    internal class OcrManager
    {
        internal Dictionary<string, string> LangDic;

        internal List<string> LangsAll;

        internal List<string> LangsAllForRequest
        {
            get
            {
                if (_langsAllForRequest == null)
                {
                    _langsAllForRequest = new List<string>(SettingsManager.Settings.OcrLangsList);
                }
                return _langsAllForRequest;
            }
            set => _langsAllForRequest = value;
        }

        internal string DirTessdata;


        private List<string>? _langsAllForRequest;
        private static OcrManager? _instance;

        internal static OcrManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new OcrManager();

                }
                return _instance;
            }
        }

        internal OcrManager()
        {
            LangDic = new Dictionary<string, string>
            {


                { "Arabic", "ara" },
                { "Belarusian", "bel" },
                { "Czech", "ces" },
                { "Danish", "dan" },
                { "English", "eng" },
                { "Estonian", "est" },
                { "Finnish", "fin" },
                { "French", "fra" },
                { "German", "deu" },
                { "Greek", "grc" },
                { "Hebrew", "heb" },
                { "Italian", "ita" },
                { "Japanese", "jpn" },
                { "Kazakh", "kaz" },
                { "Korean", "kor" },
                { "Polish", "pol" },
                { "Portuguese", "por" },
                { "Russian", "rus" },
                { "Spanish", "spa" },
                { "Turkish", "tur" },
                { "Ukrainian", "ukr" },
                { "Afrikaans", "afr" },
                { "Albanian", "sqi" },
                { "Amharic", "amh" },
                { "Armenian", "hye" },
                { "Assamese", "asm" },
                { "Azerbaijani", "aze" },
                { "Basque", "eus" },
                { "Bengali", "ben" },
                { "Bosnian", "bos" },
                { "Breton", "bre" },
                { "Bulgarian", "bul" },
                { "Burmese", "mya" },
                { "Catalan; Valencian", "cat" },
                { "Cebuano", "ceb" },
                { "Central Khmer", "khm" },
                { "Cherokee", "chr" },
                { "Chinese - Simplified", "chi_sim" },
                { "Chinese - Traditional", "chi_tra" },
                { "Corsican", "cos" },
                { "Croatian", "hrv" },
                { "Dutch; Flemish", "nld" },
                { "Dzongkha", "dzo" },
                { "Esperanto", "epo" },
                { "Faroese", "fao" },
                { "Galician", "glg" },
                { "Georgian", "kat" },
                { "Gujarati", "guj" },
                { "Haitian; Haitian Creole", "hat" },
                { "Hindi", "hin" },
                { "Hungarian", "hun" },
                { "Icelandic", "isl" },
                { "Indonesian", "ind" },
                { "Inuktitut", "iku" },
                { "Irish", "gle" },
                { "Javanese", "jav" },
                { "Kannada", "kan" },
                { "Kirghiz; Kyrgyz", "kir" },
                { "Kurdish", "kur" },
                { "Lao", "lao" },
                { "Latin", "lat" },
                { "Latvian", "lav" },
                { "Lithuanian", "lit" },
                { "Luxembourgish", "ltz" },
                { "Malay", "msa" },
                { "Malayalam", "mal" },
                { "Maltese", "mlt" },
                { "Marathi", "mar" },
                { "Macedonian", "mkd" },
                { "Mongolian", "mon" },
                { "Maori", "mri" },
                { "Nepali", "nep" },
                { "Norwegian", "nor" },
                { "Occitan", "oci" },
                { "Oriya", "ori" },
                { "Panjabi; Punjabi", "pan" },
                { "Persian", "fas" },
                { "Pushto; Pashto", "pus" },
                { "Quechua", "que" },
                { "Romanian; Moldavian; Moldovan", "ron" },
                { "Sanskrit", "san" },
                { "Scottish Gaelic", "gla" },
                { "Serbian", "srp" },
                { "Sindhi", "snd" },
                { "Sinhala; Sinhalese", "sin" },
                { "Slovak", "slk" },
                { "Slovenian", "slv" },
                { "Swahili", "swa" },
                { "Swedish", "swe" },
                { "Syriac", "syr" },
                { "Tajik", "tgk" },
                { "Tamil", "tam" },
                { "Tatar", "tat" },
                { "Telugu", "tel" },
                { "Thai", "tha" },
                { "Tigrinya", "tir" },
                { "Tonga", "ton" },
                { "Uighur; Uyghur", "uig" },
                { "Urdu", "urd" },
                { "Western Frisian", "fry" }
            };
            LangsAll = new List<string>(LangDic.Keys.Select(x => x));
        }

        internal void Start()
        {
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.OcrShortcut), SettingsManager.Settings.OcrShortcut, PressedShortcutOcr);
            CopyLibs();
        }

        internal void Stop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.OcrShortcut));
        }

        internal void PressedShortcutOcr(object? sender, HotkeyEventArgs? hotkeyEventArgs)
        {
            ScreenshotTaker screenshot = new ScreenshotTaker();
            screenshot.GetImageAction += bitmap =>
            {
                ScreenShortMenuWindow screenShortMenuWindow = new ScreenShortMenuWindow();
                screenShortMenuWindow.ShowMenu(bitmap);
            };
            screenshot.OpenScreen();
        }

        private void CopyLibs()
        {
            var dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "EveryLang");
            dir = Path.Combine(dir, "OCR");
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            else
            {
                if (Directory.Exists(Path.Combine(dir, "Bin")))
                {
                    Directory.Delete(dir, true);
                    Directory.CreateDirectory(dir);
                }
            }

            if (!Directory.Exists(Path.Combine(dir, "tessdata"))) Directory.CreateDirectory(Path.Combine(dir, "tessdata"));

            DirTessdata = Path.Combine(dir, "tessdata");

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();
            var stream = assembly.GetManifestResourceStream(resourceNames.First(x => x.EndsWith("ocr.zip")));
            if (stream != null)
            {
                ExtractZipFromStream(stream, dir);
            }

            Tesseract.TesseractEnviornment.CustomSearchPath = dir;
        }

        internal static void ExtractZipFromStream(Stream zipStream, string extractPath)
        {
            // Создаем ZipArchive из потока
            using (ZipArchive archive = new ZipArchive(zipStream, ZipArchiveMode.Read))
            {
                foreach (var entry in archive.Entries)
                {
                    // Строим путь для распаковки
                    string filePath = Path.Combine(extractPath, entry.FullName);

                    // Если это директория, создаем ее
                    if (string.IsNullOrEmpty(entry.Name))
                    {
                        Directory.CreateDirectory(filePath);
                    }
                    else
                    {
                        // Создаем директорию, если ее нет
                        string directory = Path.GetDirectoryName(filePath);
                        if (!Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }

                        // Извлекаем файл
                        entry.Open().CopyTo(File.Create(filePath));
                    }
                }
            }
        }

    }
}
