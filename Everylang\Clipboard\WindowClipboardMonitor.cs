﻿using System;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;

namespace Everylang.App.Clipboard
{
    internal class WindowClipboardMonitor : IDisposable
    {
        internal event Action? ClipboardChanged;

        private HwndSource? _win32InteropSource;
        private IntPtr _windowInteropHandle;
        private bool _disposed;

        // See http://msdn.microsoft.com/en-us/library/ms649021%28v=vs.85%29.aspx
        const int ClipboardUpdateWindowMessageCode = 0x031D;

        internal WindowClipboardMonitor(Window clipboardWindow)
        {
            InitializeInteropSource(clipboardWindow);
            InitializeWindowInteropHandle(clipboardWindow);

            StartHandlingWin32Messages();
            AddListenerForClipboardOperationsMessages();
        }

        private void InitializeInteropSource(Window clipboardWindow)
        {
            var presentationSource = PresentationSource.FromVisual(clipboardWindow);
            _win32InteropSource = presentationSource as HwndSource;

            if (_win32InteropSource == null)
            {
                throw new ArgumentException(
                    $@"Window must be initialized before using the {nameof(WindowClipboardMonitor)}. Use the window's OnSourceInitialized() handler if possible, or a later point in the window lifecycle."
                    , nameof(clipboardWindow));
            }
        }

        private void InitializeWindowInteropHandle(Window clipboardWindow)
        {
            _windowInteropHandle = new WindowInteropHelper(clipboardWindow).Handle;
        }

        private void StartHandlingWin32Messages()
        {
            _win32InteropSource?.AddHook(Win32InteropMessageHandler);
        }

        private void StopHandlingWin32Messages()
        {
            _win32InteropSource?.RemoveHook(Win32InteropMessageHandler);
        }

        private void AddListenerForClipboardOperationsMessages()
        {
            User32.AddClipboardFormatListener(_windowInteropHandle);
        }

        private void RemoveListenerForClipboardOperationsMessages()
        {
            User32.RemoveClipboardFormatListener(_windowInteropHandle);
        }

        private IntPtr Win32InteropMessageHandler(IntPtr windowHandle, int messageCode, IntPtr wParam, IntPtr lParam, ref bool messageHandled)
        {
            if (messageCode == ClipboardUpdateWindowMessageCode)
            {
                OnClipboardChanged();

                messageHandled = true;
                return IntPtr.Zero;
            }

            return IntPtr.Zero;
        }

        private void OnClipboardChanged()
        {
            ClipboardChanged?.Invoke();
        }

        public void Dispose()
        {
            ReleaseResources();
            GC.SuppressFinalize(this);
        }

        protected virtual void ReleaseResources()
        {
            if (_disposed)
            {
                return;
            }
            else
            {
                _disposed = true;
            }

            RemoveListenerForClipboardOperationsMessages();
            StopHandlingWin32Messages();

            _win32InteropSource = null;
            _windowInteropHandle = IntPtr.Zero;
        }

        ~WindowClipboardMonitor()
        {
            ReleaseResources();
        }
    }
}
