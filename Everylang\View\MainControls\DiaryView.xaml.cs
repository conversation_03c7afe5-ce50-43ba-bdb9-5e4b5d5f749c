﻿using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.SettingsApp;
using Everylang.App.ViewModels;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Telerik.Windows.Controls;
using Telerik.Windows.Controls.GridView;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for DiaryView.xaml
    /// </summary>
    internal partial class DiaryView
    {
        internal static Action? CallbackEventHandlerHide;
        internal DiaryView()
        {
            InitializeComponent();
        }

        internal async void Show()
        {
            await Task.Delay(100);
            if (!string.IsNullOrEmpty(SettingsManager.Settings.DiaryPassword) && (DateTime.Now - VMContainer.Instance.DiaryViewModel.LastPasswordRequest).Minutes > 10)
            {
                GridPassword.Visibility = Visibility.Visible;
                lvDiary.Visibility = Visibility.Collapsed;
                PasswordBoxMu.Focus();
            }
            else
            {
                GridPassword.Visibility = Visibility.Collapsed;
                lvDiary.Visibility = Visibility.Visible;
            }
        }

        private void listBoxItem_DoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (lvDiary.SelectedItem != null)
                {
                    ClipboardOperations.SetText(((DiaryDataModel)lvDiary.SelectedItem).Text);
                }
                if (CallbackEventHandlerHide != null) CallbackEventHandlerHide();
            }
            catch
            {
                // ignore
            }
        }

        private void MenuItemCopyClick(object sender, RoutedEventArgs e)
        {
            var row = this.GridContextMenu.GetClickedElement<GridViewRow>();
            if (row == null) return;
            if (!VMContainer.Instance.DiaryViewModel.SelectedItems.Contains(row.DataContext))
            {
                VMContainer.Instance.DiaryViewModel.SelectedItems.Clear();
                VMContainer.Instance.DiaryViewModel.SelectedItems.Add((DiaryDataModel)row.DataContext);
            }
            if (VMContainer.Instance.DiaryViewModel.SelectedItems.Count > 0)
            {
                string text = "";
                foreach (var dataModel in VMContainer.Instance.DiaryViewModel.SelectedItems)
                {
                    text += dataModel.Text + Environment.NewLine;
                }
                ClipboardOperations.SetText(text.Trim());
            }
        }

        private void lvDiary_SelectionChanged(object sender, object e)
        {
            VMContainer.Instance.DiaryViewModel.SelectedItems.Clear();
            if (lvDiary.SelectedItems != null)
            {
                foreach (var item in lvDiary.SelectedItems)
                {
                    VMContainer.Instance.DiaryViewModel.SelectedItems.Add((DiaryDataModel)item);
                }
            }
            VMContainer.Instance.DiaryViewModel.OnPropertyChanged(nameof(VMContainer.Instance.DiaryViewModel.IsSelectedNotNull));
        }


        private void TextBoxBase_OnTextChanged(object sender, Telerik.Windows.Controls.AutoSuggestBox.TextChangedEventArgs e)
        {
            if (e.Reason == Telerik.Windows.Controls.AutoSuggestBox.TextChangeReason.UserInput)
            {
                string text = SearchTextBox.Text;
                var deleteCommand = RadGridViewCommands.SearchByText as RoutedUICommand;
                deleteCommand?.Execute(text, this.lvDiary);
            }
        }

        private bool _setItems;

        private void PasswordBoxMu_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            if (PasswordBoxMu.Password == SettingsManager.Settings.DiaryPassword)
            {
                VMContainer.Instance.DiaryViewModel.LastPasswordRequest = DateTime.Now;
                Show();
            }
        }

    }
}
