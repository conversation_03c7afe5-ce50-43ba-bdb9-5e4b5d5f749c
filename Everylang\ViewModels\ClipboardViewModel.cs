﻿using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels
{
    public class ClipboardViewModel : ViewModelBase
    {
        private string? _lastClipboardText;
        //internal bool IsIgnoreNewText { get; set; }
        internal bool IsBreakText { get; set; }

        public RadObservableCollection<ClipboardDataModel> AllClipboardItems { get; set; }

        internal List<ClipboardDataModel> SelectedItems { get; set; }


        public DelegateCommand DeleteSelectedCommand
        {
            get;
            private set;
        }

        public DelegateCommand ClearAllCommand
        {
            get;
            private set;
        }
        public ClipboardViewModel()
        {
            _lastClipboardText = "";
            DeleteSelectedCommand = new DelegateCommand(DeleteSelected);
            ClearAllCommand = new DelegateCommand(ClearAll);
            AllClipboardItems = new RadObservableCollection<ClipboardDataModel>();

            SelectedItems = new List<ClipboardDataModel>();
            ClipboardMonitorWorker.OnClipboardChange += AddNewClipboard;

            GetAllClipboardDataFromDbAsync();
        }

        private async void GetAllClipboardDataFromDbAsync()
        {
            await Task.Run(() =>
            {
                var collection = ClipboadManager.GetAllClipboardData();
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    AllClipboardItems.Clear();
                    AllClipboardItems.AddRange(collection);
                }));
            });
        }

        internal void AddNewClipboard(ClipboardDataObject dataObject)
        {
            if (_lastClipboardText != dataObject.TextPlain)
            {
                _lastClipboardText = dataObject.TextPlain;
                if (SettingsManager.Settings.ClipboardHistoryIsOn)
                {
                    AddNewClipboardDataAsync(dataObject, CheckActiveProcessFileName.GetActiveProcessName());
                }
            }
        }

        internal string? GetLastClipboardText()
        {
            return _lastClipboardText;
        }

        internal void SaveAllToDb()
        {
            ClipboadManager.SaveAll(AllClipboardItems);
        }

        private void ClearAll(object? o)
        {
            AllClipboardItems.Clear();
            ClipboadManager.ClearAllClipboardData();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }

        internal void DeleteSelected(object? o)
        {
            var listForRem = new List<ClipboardDataModel>();
            for (int i = 0; i < SelectedItems.Count; i++)
            {
                listForRem.Add(SelectedItems[i]);
            }
            ClipboadManager.DelClipboardData(listForRem);
            foreach (var item in listForRem)
            {
                AllClipboardItems.Remove(item);
            }
            SelectedItems.Clear();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsEnabled));
            }
        }

        public bool IsEnabled
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.ClipboardHistoryIsOn;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.ClipboardHistoryIsOn = value;
                base.OnPropertyChanged();
                if (SettingsManager.Settings.ClipboardHistoryIsOn)
                {
                    ClipboardMonitorWorker.Start();
                }
                else
                {
                    ClipboardMonitorWorker.Stop();
                }
                ClipboardHookManager.Instance.Restart();
            }
        }

        public bool IsSelectedNotNull
        {
            get
            {
                return SelectedItems.Count != 0;
            }
        }

        public bool IsNotNullAll
        {
            get { return AllClipboardItems.Count != 0; }
        }

        // private void GetAllClipboardDataFromDb()
        // {
        //     var collection = ClipboadManager.GetAllClipboardData();
        //     AllClipboardItems.Clear();
        //     AllClipboardItems.AddRange(collection);
        // }

        private async void AddNewClipboardDataAsync(ClipboardDataObject clipData, string? lastActiveProcessName)
        {
            if (IsEnabled)
            {
                if (CheckActiveProcessFileName.CheckClipboard())
                {
                    ClipboardDataModel clipboardDataModel = new ClipboardDataModel();
                    if (clipData.IsImage)
                    {
                        if (!CheckActiveProcessFileName.CheckClipboardImage())
                        {
                            return;
                        }

                        try
                        {
                            clipboardDataModel.ShortText = "Image " + DateTime.Now.ToString("F");
                            clipboardDataModel.ImageId = clipData.TextPlain;
                            clipboardDataModel.Application = Path.GetFileName(lastActiveProcessName);
                            clipboardDataModel.Text = clipboardDataModel.ShortText;
                            clipboardDataModel.DateTime = DateTime.Now;
                            var filename = Path.GetTempFileName();
                            clipData.ImageData?.Save(filename);
                            var clipboardDataModelCopyList = AllClipboardItems.Where(x =>
                                x.ImageId != null && x.ImageId.Equals(clipboardDataModel.ImageId)).ToList();
                            var oldFavorite = clipboardDataModelCopyList.FirstOrDefault()?.IsFavorite;
                            if (clipboardDataModelCopyList.Count > 0)
                            {
                                foreach (ClipboardDataModel clipboardDataModelCopy in clipboardDataModelCopyList)
                                {
                                    AllClipboardItems.Remove(clipboardDataModelCopy);
                                    ClipboadManager.DelClipboardData(new List<ClipboardDataModel>()
                                        { clipboardDataModelCopy });
                                }
                            }

                            clipboardDataModel.IsFavorite = oldFavorite ?? false;
                            ClipboadManager.AddClipboardImage(filename, clipboardDataModel.ImageId);
                            AllClipboardItems.Insert(0, clipboardDataModel);
                            ClipboadManager.AddClipboardData(clipboardDataModel);
                            File.Delete(filename);
                        }
                        catch
                        {
                            // ignored
                        }

                    }
                    else
                    {
                        try
                        {
                            clipboardDataModel.ShortText = clipData.TextPlain != null && clipData.TextPlain.Length > 150
                                ? clipData.TextPlain.Substring(0, 150) + "......"
                                : clipData.TextPlain;
                            clipboardDataModel.ShortText =
                                clipboardDataModel.ShortText?.Replace(Environment.NewLine, " ");
                            clipboardDataModel.Application = Path.GetFileName(lastActiveProcessName);
                            clipboardDataModel.DateTime = DateTime.Now;
                            clipboardDataModel.Text = clipData.TextPlain;
                            if (clipData.IsHtml)
                            {
                                clipboardDataModel.Html = clipData.TextData;
                            }

                            if (clipData.IsRtf)
                            {
                                clipboardDataModel.Rtf = clipData.TextData;
                            }

                            if (clipData.IsFiles)
                            {
                                clipboardDataModel.Files = clipData.TextData;
                            }


                            if (!IsBreakText)
                            {
                                var clipboardDataModelCopyList = AllClipboardItems
                                    .Where(x => x.Text != null && x.Text.Equals(clipData.TextPlain)).ToList();
                                var oldFavorite = clipboardDataModelCopyList.FirstOrDefault()?.IsFavorite;
                                if (clipboardDataModelCopyList.Count > 0)
                                {
                                    foreach (ClipboardDataModel clipboardDataModelCopy in clipboardDataModelCopyList)
                                    {
                                        AllClipboardItems.Remove(clipboardDataModelCopy);
                                        ClipboadManager.DelClipboardData(new List<ClipboardDataModel>()
                                            { clipboardDataModelCopy });
                                    }
                                }
                                clipboardDataModel.IsFavorite = oldFavorite ?? false;
                            }

                            await AddNewClipboardDataAsync(clipboardDataModel);
                        }
                        catch
                        {
                            // ignored
                        }

                    }


                }
            }
        }

        private Task AddNewClipboardDataAsync(ClipboardDataModel clipboardDataModel)
        {
            return Task.Run(async () =>
            {
                try
                {
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                    {
                        AllClipboardItems.Insert(0, clipboardDataModel);
                    }));
                    ClipboadManager.AddClipboardData(clipboardDataModel);
                    ClipboardKeyHookManager.CallCounter = -1;

                    if (AllClipboardItems.Count > SettingsManager.Settings.ClipboardMaxClipboardItems)
                    {
                        var listForRem = AllClipboardItems.Where(x => !x.IsFavorite).Skip(SettingsManager.Settings.ClipboardMaxClipboardItems).ToList();
                        ClipboadManager.DelClipboardData(listForRem);
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        {
                            AllClipboardItems.RemoveRange(listForRem);
                        }));
                    }

                    base.OnPropertyChanged(nameof(IsSelectedNotNull));
                    base.OnPropertyChanged(nameof(IsNotNullAll));
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        }

        internal void BreakInter()
        {
            IsBreakText = true;
            if (SelectedItems.Count != 0)
            {
                List<ClipboardDataModel> clipboardDataModels = new List<ClipboardDataModel>(SelectedItems);
                foreach (var clipboardDataModel in clipboardDataModels)
                {
                    var text = NormalizeLineBreaks(clipboardDataModel.Text);
                    string?[] textArr = text.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
                    if (textArr.Length > 0)
                    {
                        for (int i = textArr.Length - 1; i >= 0; i--)
                        {
                            var s = textArr[i];
                            if (s?.Trim() != "" && s?.Trim() != " ")
                            {
                                ClipboardDataObject clipData = new ClipboardDataObject
                                {
                                    IsPlainText = true,
                                    TextData = s,
                                    TextPlain = s
                                };
                                AddNewClipboardDataAsync(clipData, clipboardDataModel.Application);
                                Thread.Sleep(30);
                            }
                        }
                    }
                }
            }
            IsBreakText = false;
        }

        internal void BreakSpace()
        {
            IsBreakText = true;
            if (SelectedItems.Count != 0)
            {
                List<ClipboardDataModel> clipboardDataModels = new List<ClipboardDataModel>(SelectedItems);
                foreach (var clipboardDataModel in clipboardDataModels)
                {
                    string[]? textArr = clipboardDataModel.Text?.Split(' ');
                    if (textArr != null && textArr.Length > 0)
                    {
                        for (int i = textArr.Length - 1; i >= 0; i--)
                        {
                            var s = textArr[i].Trim();
                            if (s != "" && s != " ")
                            {
                                ClipboardDataObject clipData = new ClipboardDataObject();
                                clipData.IsPlainText = true;
                                clipData.TextData = s;
                                clipData.TextPlain = s;
                                AddNewClipboardDataAsync(clipData, clipboardDataModel.Application);
                                Thread.Sleep(30);
                            }

                        }
                    }
                }
            }
            IsBreakText = false;
        }

        static string NormalizeLineBreaks(string? input)
        {
            // Allow 10% as a rough guess of how much the string may grow.
            // If we're wrong we'll either waste space or have extra copies -
            // it will still work
            if (input != null)
            {
                StringBuilder builder = new StringBuilder((int)(input.Length * 1.1));

                bool lastWasCr = false;

                foreach (char c in input)
                {
                    if (lastWasCr)
                    {
                        lastWasCr = false;
                        if (c == '\n')
                        {
                            continue; // Already written \r\n
                        }
                    }
                    switch (c)
                    {
                        case '\r':
                            builder.Append("\r\n");
                            lastWasCr = true;
                            break;
                        case '\n':
                            builder.Append("\r\n");
                            break;
                        default:
                            builder.Append(c);
                            break;
                    }
                }
                return builder.ToString();
            }

            return "";
        }

    }
}
