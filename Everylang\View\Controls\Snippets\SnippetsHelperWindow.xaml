﻿<telerik:RadWindow x:Class="Everylang.App.View.Controls.Snippets.SnippetsHelperWindow"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:snippets="clr-namespace:Everylang.App.View.Controls.Snippets"
                   mc:Ignorable="d"
                   Header="{telerik:LocalizableResource Key=AutochangeEditor}"
                   PreviewKeyDown="SnippetsHelperWindow_OnPreviewKeyDown"
                   SizeToContent="True"
                   Width="600"
                   HideMaximizeButton="True" HideMinimizeButton="True"
                   x:Name="SnippetsHelper"
                   x:ClassModifier="internal"
                   DataContext="{Binding ElementName=SnippetsHelper, Path=AutochangeDataModel}">
    <telerik:RadWindow.Style>
        <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="snippets:SnippetsHelperWindow" />
    </telerik:RadWindow.Style>
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid Grid.ColumnSpan="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <TextBlock
                FontSize="14"
                Text="{telerik:LocalizableResource Key=AutochangeHelperDesc}"
                VerticalAlignment="Center" />
            <TextBox
                Grid.Column="1"
                Margin="10,0,0,0"
                Text="{Binding Path=ShortText, UpdateSourceTrigger=PropertyChanged}" />
        </Grid>
        <StackPanel Grid.Row="1" Grid.Column="0" Margin="10">
            <TextBlock
                FontSize="14"
                Margin="0,0,0,0"
                Text="{telerik:LocalizableResource Key=AutochangeHelperToText}" />
            <TextBox
                AcceptsReturn="True"
                Height="160"
                HorizontalScrollBarVisibility="Auto"
                Margin="0,7,0,0"
                Name="TextBoxTo"
                Text="{Binding Path=Text, UpdateSourceTrigger=PropertyChanged}"
                TextWrapping="Wrap"
                VerticalContentAlignment="Top"
                VerticalScrollBarVisibility="Auto" />
            <TextBlock
                FontSize="14"
                Margin="0,10,0,0"
                Text="{telerik:LocalizableResource Key=AutochangeHelperFromText}" />
            <TextBox
                KeyDown="TextBox_OnKeyDown"
                Margin="0,7,0,0"
                Name="TextBox"
                Text="{Binding Path=FromText, UpdateSourceTrigger=PropertyChanged}"
                TextAlignment="Left" />
        </StackPanel>
        <StackPanel
            Grid.Column="1"
            Grid.Row="1"
            Margin="0,10,10,10">
            <TextBlock
                FontSize="14"
                Margin="0,0,0,0"
                Text="{telerik:LocalizableResource Key=AutochangeHelperLangListDesc}" />
            <telerik:RadComboBox
                BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                Focusable="False"
                Margin="0,7,0,0"
                x:Name="ComboLangList" />
            <CheckBox
                Content="{telerik:LocalizableResource Key=AutochangeHelperSaveCursorPosition}"
                Focusable="False"
                IsChecked="{Binding Path=IsSetCursorPosition}"
                Margin="0,2,0,0" />
            <CheckBox
                Content="{telerik:LocalizableResource Key=AutochangeHelperChangeAtOnce}"
                Focusable="False"
                IsChecked="{Binding Path=IsChangeAtOnce}"
                Margin="0,0,0,0" />
            <TextBlock
                FontSize="14"
                Margin="0,9,0,0"
                Text="{telerik:LocalizableResource Key=AutochangeHelperTags}" />
            <TextBox Margin="0,7,0,0" Text="{Binding Path=Tags, UpdateSourceTrigger=PropertyChanged}" />
            <Grid Margin="0,7,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="65*" />
                    <ColumnDefinition Width="35*" />
                </Grid.ColumnDefinitions>
                <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" Focusable="False" x:Name="ComboTrueListOfTags" />
                <telerik:RadButton
                    Click="AddTagClick"
                    Content="{telerik:LocalizableResource Key=AutochangeAddNew}"
                    Focusable="False"
                    Grid.Column="1"
                    Margin="1,0,0,0" />
            </Grid>
            <StackPanel
                HorizontalAlignment="Right"
                Margin="0,5,0,0"
                Orientation="Horizontal">
                <telerik:RadButton
                    Click="SaveCloseButton_OnClick"
                    Content="{telerik:LocalizableResource Key=Save}"
                    Focusable="False"
                    Margin="0,0,0,0"
                    Width="100" />
                <telerik:RadButton
                    Click="Close"
                    Content="{telerik:LocalizableResource Key=Cancel}"
                    Focusable="False"
                    Margin="3,0,0,0"
                    Width="100" />
            </StackPanel>
        </StackPanel>
    </Grid>
</telerik:RadWindow>