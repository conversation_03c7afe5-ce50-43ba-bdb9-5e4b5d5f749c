﻿using Everylang.Note.Callbacks;
using Everylang.Note.Helpers;
using Everylang.Note.NoteDataStore;
using Everylang.Note.SettingsApp;
using MaterialDesignColors;
using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Telerik.Windows.Controls;
using Vanara.PInvoke;
using MenuItem = System.Windows.Controls.MenuItem;
using MouseButtonState = System.Windows.Input.MouseButtonState;
using MouseEventArgs = System.Windows.Input.MouseEventArgs;
using Size = System.Windows.Size;

namespace Everylang.Note
{
    /// <summary>
    /// Interaction logic for WindowNote.xaml
    /// </summary>
    public partial class WindowNote
    {
        private string? _color;
        private double _widthLast;
        private double _heightLast;
        private object? _owner;

        public static readonly DependencyProperty IsStayOnTopProperty =
            DependencyProperty.Register("IsStayOnTop",
                typeof(<PERSON><PERSON><PERSON>),
                typeof(WindowNote),
                new FrameworkPropertyMetadata(false,
                FrameworkPropertyMetadataOptions.None
              ));

        public bool IsStayOnTop
        {
            get { return (bool)GetValue(IsStayOnTopProperty); }
            set
            {
                Topmost = value;
                if (value)
                {
                    if (Owner != null) _owner = Owner;
                    Owner = null;
                    Left--;
                    Left++;
                }
                else
                {
                    if (_owner != null) Owner = (Window)_owner;
                }

                SetValue(IsStayOnTopProperty, value);
            }
        }

        public static readonly DependencyProperty TransparencyForNotesProperty =
            DependencyProperty.Register("TransparencyForNotesP",
                typeof(double),
                typeof(WindowNote),
                new FrameworkPropertyMetadata());

        public double TransparencyForNotesP
        {
            get { return (double)GetValue(TransparencyForNotesProperty); }
            set { SetValue(TransparencyForNotesProperty, value); }
        }

        public static readonly DependencyProperty IsCheckListProperty =
            DependencyProperty.Register("IsCheckList",
                typeof(bool),
                typeof(WindowNote),
                new FrameworkPropertyMetadata());

        public bool IsCheckList
        {
            get { return (bool)GetValue(IsCheckListProperty); }
            set { SetValue(IsCheckListProperty, value); }
        }

        public WindowNote(NoteDataModel noteDataModel)
        {
            InitializeComponent();
            noteControl.SetData(noteDataModel);
            noteControl.SizeChanged += NoteControlOnSizeChanged;
            DataContext = this;
            TransparencyForNotesP = Convert.ToDouble(SettingsMiminoteManager.AppSettings.TransparencyForNotes) / 100.0;
            CallBack.MiminoteCallBackTransparencyForNotesChange +=
                OnMiminoteCallBackTransparencyForNotesChange;
        }

        private void NoteControlOnSizeChanged(Size size)
        {
            if (size.Width != 0)
                this.Width = size.Width;
            if (size.Height != 0)
                this.Height = size.Height;
        }

        private void OnMiminoteCallBackTransparencyForNotesChange()
        {
            TransparencyForNotesP = Convert.ToDouble(SettingsMiminoteManager.AppSettings.TransparencyForNotes) / 100.0;
        }

        private void NoteFormOnLoaded(object sender, RoutedEventArgs e)
        {
            ContextMenuCreator();
            LoadData();
        }

        #region Context Menu

        private void ContextMenuCreator()
        {
            var colorItem = new MenuItem();
            colorItem.Header = LocalizationManager.GetString("NoteColor");
            foreach (var swatch in new SwatchesProvider().Swatches.ToList())
            {
                var item = new MenuItem();
                item.Header = swatch.Name.Replace(swatch.Name[0].ToString(), swatch.Name[0].ToString().ToUpper());
                item.Background = new SolidColorBrush(swatch.PrimaryHues.ToList()[1].Color);
                item.Click += MenuChangeColor;
                item.MouseEnter += MenuChangeColorPreview;
                item.MouseLeave += MenuChangeColorBack;
                colorItem.Items.Add(item);
            }
            var addNewItem = new MenuItem();
            addNewItem.Header = LocalizationManager.GetString("NotesAddNew");
            addNewItem.Click += AddNew;

            var settingsItem = new MenuItem();
            settingsItem.Header = LocalizationManager.GetString("Settings");
            settingsItem.Click += OpenSettings;
            var listNotesItem = new MenuItem();
            listNotesItem.Header = LocalizationManager.GetString("NotesList");
            listNotesItem.Click += OpenNotesList;
            var closeItem = new MenuItem();
            closeItem.Header = LocalizationManager.GetString("NotesToArchive");
            closeItem.Click += ToArchiveNote;

            contextMenuButtonMenu.Items.Add(addNewItem);
            contextMenuButtonMenu.Items.Add(listNotesItem);
            contextMenuButtonMenu.Items.Add(colorItem);
            contextMenuButtonMenu.Items.Add(settingsItem);
            contextMenuButtonMenu.Items.Add(closeItem);
        }

        private void ToArchiveNote(object sender, RoutedEventArgs e)
        {
            noteControl.ToArchiveNote();
        }

        private void OpenSettings(object sender, RoutedEventArgs e)
        {
            CallBack.OnMiminoteCallBackOpenSettings();
        }

        private void OpenNotesList(object sender, RoutedEventArgs e)
        {
            CallBack.OnMiminoteCallBackOpenNotesList();
        }

        private string? _lastLocorPreview = "";

        private void MenuChangeColorPreview(object sender, RoutedEventArgs e)
        {
            _lastLocorPreview = _color;
            var item = (MenuItem)sender;
            if (item.Header != null) SetColor(item.Header.ToString());
        }

        private void MenuChangeColorBack(object sender, RoutedEventArgs e)
        {
            if (_color != _lastLocorPreview)
            {
                SetColor(_lastLocorPreview);
                _lastLocorPreview = _color;
            }
        }

        private void MenuChangeColor(object sender, RoutedEventArgs e)
        {
            var item = (MenuItem)sender;
            SetColor(item.Header.ToString());
            _lastLocorPreview = _color;
        }

        #endregion


        private void LoadData()
        {
            noteControl.LoadNoteData();
            if (Left > SystemParameters.VirtualScreenWidth)
            {
                Left = SystemParameters.VirtualScreenWidth - Width - 30;
            }
            if (Top > SystemParameters.VirtualScreenHeight)
            {
                Top = SystemParameters.VirtualScreenHeight - Height - 30;
            }

            if (Top < 0)
            {
                Top = 0;
            }

            if (!VisibleOnScreen())
            {
                Left = 30;
                Top = 30;
            }
            _heightLast = Height;

            IsCheckList = noteControl.NoteDataModelObj.IsCheckList;

            SetColor(noteControl.NoteDataModelObj.Color);
            textBoxTitle.Text = noteControl.NoteDataModelObj.NoteName;

            Activate();
        }

        private bool VisibleOnScreen()
        {
            foreach (Screen screen in Screen.AllScreens)
            {
                if (screen.Bounds.Contains(new Rectangle((int)Left, (int)this.Top, (int)this.Width, (int)this.Height)))
                {
                    return true;
                }
            }

            return false;
        }

        private void BorderMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                if (Width < 950.0 || Height < 600.0)
                {
                    _widthLast = Width;
                    _heightLast = Height;
                    Width = 950;
                    Height = 600;

                }
                else if (Math.Abs(Width - 950.0) < 0.1 && Math.Abs(Height - 600.0) < 0.1)
                {
                    Width = _widthLast;
                    Height = _heightLast;
                }
            }
        }

        private void BorderMain_OnMouseMove(object sender, MouseEventArgs e)
        {
            SetVisibilityButtons(true);
        }

        private void BorderMain_OnMouseLeave(object sender, MouseEventArgs e)
        {

            if (!IsActive)
            {
                SetVisibilityButtons(false);
            }
        }

        private void SetColor(string? colorName)
        {
            if (!string.IsNullOrEmpty(colorName))
            {
                new PaletteHelperLocal().ReplacePrimaryColor(colorName, Resources);
                new PaletteHelperLocal().ReplacePrimaryColor(colorName, noteControl.Resources);
                _color = colorName;
                noteControl.ColorNote = _color;
            }
            else
            {
                if (_color == "")
                {
                    new PaletteHelperLocal().ReplacePrimaryColor(new SwatchesProvider().Swatches.ToList()[3], Resources);
                    new PaletteHelperLocal().ReplacePrimaryColor(new SwatchesProvider().Swatches.ToList()[3], noteControl.Resources);
                }
                else
                {
                    var swatches = new SwatchesProvider().Swatches.ToList();
                    int index = swatches.FindIndex(x => x.Name == _color) + 1;
                    if (index >= swatches.Count)
                    {
                        index = 0;
                    }
                    var swatch = swatches[index];
                    new PaletteHelperLocal().ReplacePrimaryColor(swatch, Resources);
                    new PaletteHelperLocal().ReplacePrimaryColor(swatch, noteControl.Resources);
                    _color = swatch.Name;
                }
            }
            noteControl.ColorNote = _color;
        }

        private void Close(object sender, RoutedEventArgs e)
        {
            noteControl.Close();
            // if (_noteDataModel != null) CallBack.OnMiminoteCallBackCloseNote(_noteDataModel);
        }

        private void AddNew(object sender, RoutedEventArgs e)
        {
            if (noteControl.NoteDataModelObj != null)
            {
                noteControl.NoteDataModelObj.IsCallNewNote = true;
                noteControl.Save((int)Left, (int)Top, (int)Width, (int)Height, textBoxTitle.Text);
                CallBack.OnMiminoteCallBackAddNewNote();
                noteControl.NoteDataModelObj.IsCallNewNote = false;
            }
        }

        private void OnDeactivated(object sender, EventArgs e)
        {
            SetVisibilityButtons(false);
            if (IsVisible)
            {
                noteControl.Save((int)Left, (int)Top, (int)Width, (int)Height, textBoxTitle.Text);
            }
        }

        private void SetVisibilityButtons(bool visible)
        {
            var visibility = visible ? Visibility.Visible : Visibility.Collapsed;
            buttonAdd.Visibility = visibility;
            buttonClose.Visibility = visibility;
            if (visible)
            {
                TransparencyForNotesP = 1;
            }
            else
            {
                TransparencyForNotesP = Convert.ToDouble(SettingsMiminoteManager.AppSettings.TransparencyForNotes) / 100.0;
            }

            buttonMenu.Visibility = visibility;
            buttonPin.Visibility = visibility;
            buttonToList.Visibility = visibility;
        }

        private void OnActivated(object sender, EventArgs e)
        {
            SetVisibilityButtons(true);
        }

        private void ButtonPin_OnClick(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = !IsStayOnTop;
        }

        private void ToListOrNote(object sender, RoutedEventArgs e)
        {
            noteControl.ToListOrNote();
            IsCheckList = noteControl.NoteDataModelObj.IsCheckList;
        }


        public const uint WM_NCLBUTTONDOWN = 0xA1;
        public const nint HTCAPTION = 0x2;

        private void TextBoxTitle_OnMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                User32.ReleaseCapture();
                User32.SendMessage(new WindowInteropHelper(this).Handle, WM_NCLBUTTONDOWN, HTCAPTION, MakeLParam(System.Windows.Forms.Cursor.Position.X, System.Windows.Forms.Cursor.Position.Y));
            }
        }

        private nint MakeLParam(int loWord, int hiWord)
        {
            return (hiWord << 16) | (loWord & 0xffff);
        }

        private async void TextBoxTitle_OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            await Task.Delay(100);
            if (Width < 950.0 || Height < 600.0)
            {
                _widthLast = Width;
                _heightLast = Height;
                Width = 950;
                Height = 600;

            }
            else if (Math.Abs(Width - 950.0) < 0.1 && Math.Abs(Height - 600.0) < 0.1)
            {
                Width = _widthLast;
                Height = _heightLast;
            }
            textBoxTitle.CaretIndex = textBoxTitle.Text.Length;
        }

        private void TextBoxTitle_OnMouseUp(object sender, MouseButtonEventArgs e)
        {
            textBoxTitle.SelectionLength = 0;
        }

        private void ButtonMenu_Click(object sender, RoutedEventArgs e)
        {
            contextMenuButtonMenu.Visibility = Visibility.Visible;
            contextMenuButtonMenu.IsOpen = true;
        }

    }



}
