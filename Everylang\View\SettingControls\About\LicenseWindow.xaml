﻿<telerik:RadWindow x:Class="Everylang.App.View.SettingControls.About.LicenseWindow"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
                   xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                   xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                   xmlns:richTextBoxEx1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
                   xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
                   xmlns:about="clr-namespace:Everylang.App.View.SettingControls.About"
                   WindowStartupLocation="CenterOwner" Width="500" Height="600" Header="{telerik:LocalizableResource Key=AboutSettingsLicense}"
                   mc:Ignorable="d" CanMove="True" HideMaximizeButton="True" HideMinimizeButton="True"
                   x:ClassModifier="internal"
                   DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <telerik:RadWindow.Style>
        <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="about:LicenseWindow" />
    </telerik:RadWindow.Style>
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <richTextBoxEx1:RichTextBoxEx Margin="5" Grid.Row="0" x:Name="RadRichTextBox" BorderBrush="{x:Null}" VerticalScrollBarVisibility="Visible" BorderThickness="0" IsReadOnly="True"  />
        <Button Focusable="False" Grid.Row="1" HorizontalAlignment="Stretch" VerticalAlignment="Bottom" Content="{telerik:LocalizableResource Key=ButtonClose}" Click="CloseClick"/>
    </Grid>
</telerik:RadWindow>
