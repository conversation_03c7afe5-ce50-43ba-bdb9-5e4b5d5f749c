<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Close" xml:space="preserve">
    <value>Zamknąć</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Korekta</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Suma</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Automatyczny</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Tło:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Kolor obramowania:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Grubość obramowania:</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Zmiana rozmiaru płótna</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Rozmiar płótna</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Przycinać</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Tekst obrazkowy</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Twój tekst</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modyfikacja</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Plama</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Jasność</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Kontrast</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Zmiana odcienia</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Odwróć kolory</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Nasycenie</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Wyostrzyć</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Odwróć poziomo</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Odwróć w pionie</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Rozmiar czcionki</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Wysokość:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Pozycja pozioma</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Wyrównanie obrazu</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Podgląd obrazu</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Rozmiar obrazu</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Otwarte</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Opcje</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Zachowaj oryginalne proporcje</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Promień:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Powrót</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Rozmiar względny</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Zmień rozmiar</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Obróć o 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Obróć o 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Obróć o 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Zakręt</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Zaokrąglone rogi</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Ratować</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Tekst</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Kolor tekstu</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Nie można otworzyć pliku.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Nie można otworzyć pliku. Może to być blokowane przez inną aplikację.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Konwertować</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Nie udało się zapisać pliku.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Anulować</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Ten format pliku nie jest obsługiwany.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Pozycja pionowa</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Szerokość:</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Rysować</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Kolor pędzla:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Rozmiar pędzla:</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Postać</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Kolor obramowania</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Grubość granicy</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Formularz napełniania rur</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Zablokuj proporcje</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Postać</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Wypełnienie kształtu</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>biały</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Elipsa</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Harmonogram</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Prostokąt</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Nastawić</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Zresetuj wszystko</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Anulować</value>
  </data>
</root>