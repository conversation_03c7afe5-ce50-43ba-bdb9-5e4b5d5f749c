﻿using FlaUI.UIA3;
using System;

namespace Everylang.App.Utilities.Automation.UIA3;

internal class AutomationUi
{
    private static UIA3Automation? _uia3Automation;

    internal static UIA3Automation Uia3Automation
    {
        get
        {
            if (_uia3Automation == null)
            {
                try
                {
                    _uia3Automation = new UIA3Automation();
                }
                catch (System.Runtime.InteropServices.COMException)
                {
                }
            }
            return _uia3Automation ?? throw new InvalidOperationException("Failed to create Uia3Automation instance");
        }
    }
}