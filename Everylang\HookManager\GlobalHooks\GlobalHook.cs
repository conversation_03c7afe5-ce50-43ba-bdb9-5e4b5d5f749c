﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

namespace Everylang.App.HookManager.GlobalHooks
{
    internal static class GlobalHook
    {
        //Keyboard Hook necessary variables
        static User32.HookProc? _keyHookProc; //methods subscribed to this delegate will be called when a keyboard event happens
        static User32.SafeHHOOK _keyHookWindowsHandle = new(IntPtr.Zero); //Int pointer to the keyboard hook

        //Mouse Hook necessary variables
        static User32.HookProc? _mouseHookProc; //methods subscribed to this delegate will be called when a Mouse event happens
        static User32.SafeHHOOK _mouseHookWindowsHandle = new(IntPtr.Zero); //Int pointer to the Mouse hook

        //This constructor will handle the loading of the User32 Library which is the one that manages system hooks.
        static GlobalHook()
        {
            var user32LibraryHandle = Kernel32.LoadLibrary("User32");
            if (user32LibraryHandle == IntPtr.Zero)
            {
                int errorCode = Marshal.GetLastWin32Error();
                throw new Win32Exception(errorCode,
                    $"Failed to load library 'User32.dll'. Error {errorCode}: {new Win32Exception(Marshal.GetLastWin32Error()).Message}.");
            }
        }

        //This handles the creation of Keyboard Hook
        internal static User32.SafeHHOOK? CreateKeyboardHook(User32.HookProc? hookCb)
        {
            //We must not have more than one Keyboard hook for this instance of the class so we check if there's already one. If there is, we throw an error.
            if (_keyHookWindowsHandle != IntPtr.Zero)
                throw new Exception("There's already a keyboard hook instantiated! No need to create another one.");

            _keyHookProc =
                hookCb; // we must keep alive _hookProc, because GC is not aware about SetWindowsHookEx behaviour.

            if (_keyHookProc != null)
                _keyHookWindowsHandle = User32.SetWindowsHookEx(User32.HookType.WH_KEYBOARD_LL, _keyHookProc);

            if (_keyHookWindowsHandle != null && _keyHookWindowsHandle == IntPtr.Zero)
            {
                int errorCode = Marshal.GetLastWin32Error();
                throw new Win32Exception(errorCode,
                    $"Failed to adjust keyboard hooks for '{Process.GetCurrentProcess().ProcessName}'. Error {errorCode}: {new Win32Exception(Marshal.GetLastWin32Error()).Message}.");
            }

            return _keyHookWindowsHandle;
        }

        //This handles the creation of Mouse Hook
        internal static User32.SafeHHOOK? CreateMouseHook(User32.HookProc? hookCb)
        {
            //We must not have more than one Keyboard hook for this instance of the class so we check if there's already one. If there is, we throw an error.
            if (_mouseHookWindowsHandle != IntPtr.Zero)
                throw new Exception("There's already a mouse hook instantiated! No need to create another one.");

            _mouseHookProc =
                hookCb; // we must keep alive _hookProc, because GC is not aware about SetWindowsHookEx behaviour.

            if (_mouseHookProc != null)
                _mouseHookWindowsHandle =
                    User32.SetWindowsHookEx(User32.HookType.WH_MOUSE_LL, _mouseHookProc);
            if (_mouseHookWindowsHandle != null && _mouseHookWindowsHandle == IntPtr.Zero)
            {
                int errorCode = Marshal.GetLastWin32Error();
                throw new Win32Exception(errorCode,
                    $"Failed to adjust mouse hooks for '{Process.GetCurrentProcess().ProcessName}'. Error {errorCode}: {new Win32Exception(Marshal.GetLastWin32Error()).Message}.");
            }

            return _mouseHookWindowsHandle;
        }

        internal static void Dispose()
        {
            DisposeMouseHook();
            DisposeKeyHook();
        }

        internal static void DisposeMouseHook()
        {
            if (_mouseHookWindowsHandle != IntPtr.Zero)
            {
                User32.UnhookWindowsHookEx(_mouseHookWindowsHandle);
                _mouseHookWindowsHandle = new(IntPtr.Zero);
                _mouseHookProc = null;
            }
        }

        internal static void DisposeKeyHook()
        {
            if (_keyHookWindowsHandle != IntPtr.Zero)
            {
                User32.UnhookWindowsHookEx(_keyHookWindowsHandle);
                _keyHookWindowsHandle = new(IntPtr.Zero);
                _keyHookProc = null;
            }
        }
    }
}
