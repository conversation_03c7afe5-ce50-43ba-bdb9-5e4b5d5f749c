﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Appearance
{
    internal partial class AppearanceControl
    {
        internal AppearanceControl()
        {
            InitializeComponent();
            SetFunctionOrder();
        }

        private void SetFunctionOrder()
        {
            var fList = SettingsManager.Settings.FunctionOrder.Split().ToList();
            var objects = new Dictionary<int, object>();
            foreach (var item in LvFunctionOrder.Items)
            {
                objects.Add(fList.IndexOf(((RadListBoxItem)item).Tag.ToString() ?? string.Empty), item);
            }
            LvFunctionOrder.Items.Clear();
            var orderedList = objects.OrderBy(x => x.Key);
            foreach (var keyValuePair in orderedList)
            {
                LvFunctionOrder.Items.Add(keyValuePair.Value);
            }
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private async void LvFunctionOrder_OnDrop(object sender, DragEventArgs e)
        {
            await Task.Delay(200);
            var asd = "";
            for (var i = 0; i < LvFunctionOrder.Items.Count; i++)
            {
                asd += ((RadListBoxItem)LvFunctionOrder.Items[i]).Tag + " ";
            }

            SettingsManager.Settings.FunctionOrder = asd.Trim();
            GlobalEventsApp.OnEventFunctionOrder();
        }
    }
}