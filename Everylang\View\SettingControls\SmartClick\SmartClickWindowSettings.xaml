﻿<UserControl x:Class="Everylang.App.View.SettingControls.SmartClick.SmartClickWindowSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0"
                                      x:Name="PageTransitionControl" Margin="0" />

        <StackPanel Margin="20,10,0,0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold"
                           Text="{telerik:LocalizableResource Key=UniversalWindowSettingsHeader}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold"
                           Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>
            <telerik:RadToggleSwitchButton
                Margin="0,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                CheckedContent="{telerik:LocalizableResource Key=UniversalWindowSettingsUniversalWindowIsOn}"
                ContentPosition="Right"
                FontSize="14"
                Focusable="False"
                FontWeight="DemiBold"
                IsChecked="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                UncheckedContent="{telerik:LocalizableResource Key=UniversalWindowSettingsUniversalWindowIsOff}" />
            <CheckBox Focusable="False" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                      MinHeight="0" Margin="0,0,0,0" FontSize="14"
                      IsChecked="{Binding Path=UniversalWindowSettingsViewModel.ShowOnDoubleMiddle}">
                <TextBlock FontSize="14"
                           Text="{telerik:LocalizableResource Key=UniversalWindowSettingsShowOnDoubleMiddle}"
                           TextWrapping="Wrap" />
            </CheckBox>
            <CheckBox Focusable="False" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                      MinHeight="0" Margin="0,0,0,0" FontSize="14"
                      IsChecked="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressLeftAndRightMouseButtons}">
                <TextBlock FontSize="14"
                           Text="{telerik:LocalizableResource Key=UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons}"
                           TextWrapping="Wrap" />
            </CheckBox>
            <CheckBox Focusable="False" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                      MinHeight="0" Margin="0,0,0,0" FontSize="14"
                      IsChecked="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressHotKeys}">
                <TextBlock FontSize="14"
                           Text="{telerik:LocalizableResource Key=UniversalWindowSettingsShowOnPressHotKeys}"
                           TextWrapping="Wrap" />
            </CheckBox>
            <StackPanel Margin="0,8,0,0"
                        IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SmartClickShortcutSettingsHeader}"
                           IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressHotKeys}" />
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0"
                            IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressHotKeys}">
                    <TextBox IsReadOnly="True" Focusable="False" Background="Transparent" HorizontalAlignment="Left"
                             Width="350" Text="{Binding Path=UniversalWindowSettingsViewModel.Shortcut}" ToolTip="{Binding Path=UniversalWindowSettingsViewModel.Shortcut}"/>
                    <telerik:RadButton Margin="5,0,0,0" Focusable="False" Click="ShortcutClick"
                                       HorizontalAlignment="Left" Padding="5,0,5,0"
                                       Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>
            </StackPanel>

            <TextBlock Margin="0,10,0,5" FontSize="14"
                       Text="{telerik:LocalizableResource Key=UniversalWindowSettingsSearchServices}" />
            <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                                 IsEnabled="{Binding UniversalWindowSettingsViewModel.UniversalWindowIsOn }"
                                 Focusable="False" Width="200"
                                 ItemsSource="{Binding Path=UniversalWindowSettingsViewModel.SearchServices}"
                                 SelectedItem="{Binding Path=UniversalWindowSettingsViewModel.SearchService}"
                                 HorizontalAlignment="Left" />

            <telerik:RadButton Focusable="False"
                               IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                               Margin="0,10,0,0" Click="SmartClickCheckItemsClick" HorizontalAlignment="Left"
                               Content="{telerik:LocalizableResource Key=UniversalWindowSettingsItemsCheck}" />

            <CheckBox Focusable="False" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}"
                      Margin="0,0,0,0" FontSize="14"
                      IsChecked="{Binding Path=UniversalWindowSettingsViewModel.SmartClickMiniIsOn}">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=UniversalWindowSettingsShowMiniOn}"
                           TextWrapping="Wrap" />
            </CheckBox>
            <TextBlock Margin="0, 5, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=SmartClickMiniSize}" />
            <telerik:RadSlider Width="250" Focusable="False"
                               IsEnabled="{Binding UniversalWindowSettingsViewModel.UniversalWindowIsOn }"
                               Margin="0, 3, 0, 0"
                               Value="{Binding Path=UniversalWindowSettingsViewModel.SmartClickMiniSizeIcon}"
                               AutoToolTipPlacement="BottomRight" HorizontalAlignment="Left" Minimum="10" Maximum="35" />

            <TextBlock Margin="0, 5, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=SmartClickMiniPos}" />
            <StackPanel Margin="0,3,0,0" Orientation="Horizontal"
                        IsEnabled="{Binding UniversalWindowSettingsViewModel.UniversalWindowIsOn }">
                <TextBlock FontSize="14" Text="X- " HorizontalAlignment="Left" VerticalAlignment="Center" />
                <TextBox Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"

                         Text="{Binding UniversalWindowSettingsViewModel.SmartClickMiniPosX,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
                <TextBlock Margin="10, 0, 0, 0" FontSize="14" Text="Y- " HorizontalAlignment="Left"
                           VerticalAlignment="Center" />
                <TextBox Width="50" HorizontalAlignment="Left" VerticalAlignment="Center"

                         Text="{Binding UniversalWindowSettingsViewModel.SmartClickMiniPosY,
                                                    ValidatesOnDataErrors=True,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    NotifyOnValidationError=True}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>