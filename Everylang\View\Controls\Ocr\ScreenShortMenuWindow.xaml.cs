﻿using Everylang.App.Callback;
using Everylang.App.Translator;
using Everylang.App.Utilities;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using Telerik.Windows.Controls;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using PlacementMode = System.Windows.Controls.Primitives.PlacementMode;

namespace Everylang.App.View.Controls.Ocr
{
    /// <summary>
    /// Interaction logic for ScreenShortMenuWindow.xaml
    /// </summary>
    internal partial class ScreenShortMenuWindow
    {
        private Bitmap? _image;
        private RadContextMenu _contextMenu;
        internal ScreenShortMenuWindow()
        {
            InitializeComponent();
        }

        internal void ShowMenu(Bitmap? image)
        {

            _image = image;
            _contextMenu = new RadContextMenu();
            _contextMenu.Margin = new Thickness(3);
            _contextMenu.Closed += (_, _) =>
            {
                CloseThis();
            };

            RadMenuItem itemPastButton = new RadMenuItem();
            itemPastButton.Header = LocalizationManager.GetString("TranslateButton");
            itemPastButton.PreviewMouseDown += TranslateImage;
            _contextMenu.Items.Add(itemPastButton);

            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("OcrCopyImage");
            itemCopy.PreviewMouseDown += MenuItemCopyClick;
            _contextMenu.Items.Add(itemCopy);

            RadMenuItem itemCopyText = new RadMenuItem();
            itemCopyText.Header = LocalizationManager.GetString("OcrCopyText");
            itemCopyText.PreviewMouseDown += MenuItemCopyTextClick;
            _contextMenu.Items.Add(itemCopyText);

            RadMenuItem itemCopyToMain = new RadMenuItem();
            itemCopyToMain.Header = LocalizationManager.GetString("OcrEditImage");
            itemCopyToMain.PreviewMouseDown += MenuItemToMainWindow;
            _contextMenu.Items.Add(itemCopyToMain);

            RadMenuItem itemCopyToSnippets = new RadMenuItem();
            itemCopyToSnippets.Header = LocalizationManager.GetString("Save");
            itemCopyToSnippets.PreviewMouseDown += MenuItemSave;
            _contextMenu.Items.Add(itemCopyToSnippets);

            RadMenuItem itemClose = new RadMenuItem();
            itemClose.Header = LocalizationManager.GetString("ButtonClose");
            itemClose.PreviewMouseDown += (sender, args) => CloseThis();
            _contextMenu.Items.Add(itemClose);
            _contextMenu.Placement = PlacementMode.Relative;

            var mousePos = MousePosition.GetMousePoint();
            _contextMenu.StaysOpen = false;
            _contextMenu.IsOpen = true;
            _contextMenu.HorizontalOffset = mousePos.X;
            _contextMenu.VerticalOffset = mousePos.Y;
        }

        private void MenuItemToMainWindow(object sender, MouseButtonEventArgs e)
        {
            if (_image != null) GlobalEventsApp.OnEventOcrMainWindow(_image);
            CloseThis();
        }

        private void MenuItemSave(object sender, MouseButtonEventArgs e)
        {
            _contextMenu.IsOpen = false;
            SaveFileDialog dialog = new SaveFileDialog();
            dialog.Filter = "Image files (*.png)|*.png|All files (*.*)|*.*";
            dialog.FilterIndex = 2;
            dialog.RestoreDirectory = true;
            dialog.FileName = "Screenshot " + DateTime.Now.ToString("dd-MM-yy hh-mm-ss") + ".png";
            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                var fileName = dialog.FileName;
                if (!dialog.FileName.EndsWith(".png"))
                {
                    fileName += ".png";
                }

                _image?.Save(fileName, ImageFormat.Png);
            }

            CloseThis();
        }

        private async void MenuItemCopyClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                System.Windows.Forms.Clipboard.Clear();
                if (_image != null) System.Windows.Forms.Clipboard.SetImage(_image);
                await Task.Delay(100);
            }
            catch { }
            CloseThis();
        }

        private void MenuItemCopyTextClick(object sender, MouseButtonEventArgs e)
        {
            CloseThis();
            var asd = new OcrWaitWindow();
            asd.Show(_image);
        }

        private void TranslateImage(object sender, MouseButtonEventArgs e)
        {
            CloseThis();
            if (_image != null) TranslateManager.Instance.ShowTranslationFromImage(_image);
        }

        private void CloseThis()
        {
            IsOpen = false;
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyStates == Keyboard.GetKeyStates(Key.Escape)) CloseThis();
        }
    }
}
