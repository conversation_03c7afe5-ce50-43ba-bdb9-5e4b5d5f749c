﻿using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using Everylang.App.ViewModels.SettingsModel;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Switcher
{
    internal partial class SwitcherControl
    {

        private SelectLangForSwitchViewModel? _model;
        internal SwitcherControl()
        {
            InitializeComponent();
            PreparationLangForSwitch();
        }

        private void PreparationLangForSwitch()
        {
            _model = new SelectLangForSwitchViewModel(SettingsManager.Settings.SwitcherNotTrueListOfLang, false);
            ComboTrueListOfLang.ItemsSource = _model.Items;
            ComboLangAndKeysForSwitchList.ItemsSource = _model.ItemsLangAndKeysForSwitch;
            if (_model != null && _model.SelectedItems != null)
            {
                foreach (var modelSelectedItem in _model.SelectedItems)
                {
                    ComboTrueListOfLang.SelectedItems.Add(modelSelectedItem);
                }
            }

            if (_model?.SelectedItemsLangAndKeysForSwitch != null)
            {
                foreach (var keyValuePair in _model.SelectedItemsLangAndKeysForSwitch)
                {
                    ComboLangAndKeysForSwitchList.SelectedItems.Add(keyValuePair);
                }
            }

            ComboTrueListOfLang.SelectionChanged += SelectedChanged;
            ComboLangAndKeysForSwitchList.SelectionChanged += SelectedChangedLangAndKeysForSwitchList;
        }

        private void SelectedChangedLangAndKeysForSwitchList(object sender,
            SelectionChangedEventArgs selectionChangedEventArgs)
        {
            var settings = SettingsManager.Settings.SwitcherLangAndKeysForSwitch;
            SettingsManager.Settings.SwitcherLangAndKeysForSwitch = "";
            var listSettings = settings.Split(';');
            KeyValuePair<string, object> selected = new KeyValuePair<string, object?>();
            if (_model != null)
            {
                _model.SelectedItemsLangAndKeysForSwitch = new Dictionary<string, object?>();
                foreach (KeyValuePair<string, object> selectedItem in ComboLangAndKeysForSwitchList.SelectedItems)
                {
                    _model.SelectedItemsLangAndKeysForSwitch.Add(selectedItem.Key, selectedItem.Value);
                }

                foreach (var o in _model.SelectedItemsLangAndKeysForSwitch)
                {
                    if (!listSettings.Contains(o.Value?.ToString()))
                    {
                        selected = o;
                        break;
                    }
                }

                if (selected.Value == null)
                {
                    return;
                }

                var selectedItems = new Dictionary<string, object?>(_model.SelectedItemsLangAndKeysForSwitch);
                foreach (var keyValuePair in selectedItems)
                {
                    if (selected.Value != null && keyValuePair.Key != selected.Key && (selected.Value.ToString()!.Split('+')[0].Equals(keyValuePair.Value?.ToString()?.Split('+')[0]) ||
                            selected.Value.ToString()!.Split('+')[1].Equals(keyValuePair.Value?.ToString()?.Split('+')[1])))
                    {
                        _model.SelectedItemsLangAndKeysForSwitch.Remove(keyValuePair.Key);
                    }
                }

                if (selected.Value?.ToString()?.Split('+')[1] == "rctrl" &&
                    SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 1)
                {
                    VMContainer.Instance.SwitcherSettingsViewModel.CurrentSwitchOnKey =
                        LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart");
                }

                if (selected.Value?.ToString()?.Split('+')[1] == "lctrl" &&
                    SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 2)
                {
                    VMContainer.Instance.SwitcherSettingsViewModel.CurrentSwitchOnKey =
                        LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart");
                }

                if (selected.Value?.ToString()?.Split('+')[1] == "rshift" &&
                    SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 3)
                {
                    VMContainer.Instance.SwitcherSettingsViewModel.CurrentSwitchOnKey =
                        LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart");
                }

                if (selected.Value?.ToString()?.Split('+')[1] == "lshift" &&
                    SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 4)
                {
                    VMContainer.Instance.SwitcherSettingsViewModel.CurrentSwitchOnKey =
                        LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart");
                }

                foreach (var o in _model.SelectedItemsLangAndKeysForSwitch)
                {
                    SettingsManager.Settings.SwitcherLangAndKeysForSwitch += o.Value + ";";
                }
            }
        }

        private void CurrentSwitchOnKeySelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_model?.SelectedItemsLangAndKeysForSwitch != null)
            {
                if (_model != null)
                {
                    var selectedItems = new Dictionary<string, object?>(_model.SelectedItemsLangAndKeysForSwitch);
                    foreach (var keyValuePair in selectedItems)
                    {
                        if (keyValuePair.Value != null && (keyValuePair.Value.ToString()!.Split('+')[1].Equals("rctrl") && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 1))
                        {
                            if (_model.SelectedItemsLangAndKeysForSwitch.ContainsKey(keyValuePair.Key))
                                _model.SelectedItemsLangAndKeysForSwitch.Remove(keyValuePair.Key);
                        }
                        if (keyValuePair.Value != null && (keyValuePair.Value.ToString()!.Split('+')[1].Equals("lctrl") && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 2))
                        {
                            if (_model.SelectedItemsLangAndKeysForSwitch.ContainsKey(keyValuePair.Key))
                                _model.SelectedItemsLangAndKeysForSwitch.Remove(keyValuePair.Key);
                        }
                        if (keyValuePair.Value != null && (keyValuePair.Value.ToString()!.Split('+')[1].Equals("rshift") && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 3))
                        {
                            if (_model.SelectedItemsLangAndKeysForSwitch.ContainsKey(keyValuePair.Key))
                                _model.SelectedItemsLangAndKeysForSwitch.Remove(keyValuePair.Key);
                        }
                        if (keyValuePair.Value != null && (keyValuePair.Value.ToString()!.Split('+')[1].Equals("lshift") && SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 4))
                        {
                            if (_model.SelectedItemsLangAndKeysForSwitch.ContainsKey(keyValuePair.Key))
                                _model.SelectedItemsLangAndKeysForSwitch.Remove(keyValuePair.Key);
                        }
                    }
                }
            }

        }

        private void SelectedChanged(object sender, SelectionChangedEventArgs selectionChangedEventArgs)
        {
            SettingsManager.Settings.SwitcherNotTrueListOfLang = "";
            if (_model != null)
            {
                _model.SelectedItems = new Dictionary<string, object?>();
                foreach (KeyValuePair<string, object?> selectedItem in ComboTrueListOfLang.SelectedItems)
                {
                    _model.SelectedItems.Add(selectedItem.Key, selectedItem.Value);
                }

                if (_model.Items != null)
                    foreach (var trueLang in _model.Items)
                    {
                        if (_model.SelectedItems.Count > 0 && !_model.SelectedItems.ContainsKey(trueLang.Key))
                        {
                            SettingsManager.Settings.SwitcherNotTrueListOfLang += trueLang.Value + ";";
                        }
                    }
            }
        }

        private void KeyboardShortcutsSwitchClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsKeyboardShortcutsSwitch"), SettingsManager.Settings.SwitcherSwitchTextLangShortcut, nameof(SettingsManager.Settings.SwitcherSwitchTextLangShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSwitchLangForWord);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }



        private void SwitchTextLangForAllLineShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsIsOnInsert"), SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut, nameof(SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSwitchLangForAllLine);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchTextShortcutSelectedClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsKeyboardShortcutsSwitchSelected"), SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut, nameof(SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSelectedText);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.ShortcutSelected = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SoundClick(object sender, RoutedEventArgs e)
        {
            SoundsForm? form = new SoundsForm("langswitch");
            form.HeaderText = LocalizationManager.GetString("SwitcherSettingsSoundEdit");
            PageTransitionControl.Content = form;
            form.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                form = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }


    }
}
