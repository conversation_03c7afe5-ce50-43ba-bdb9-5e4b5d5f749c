﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Common
{
    /// <summary>
    /// Interaction logic for SoundsForm.xaml
    /// </summary>
    internal partial class SoundsForm
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
           RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(SoundsForm));

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        internal static readonly DependencyProperty HeaderTextProperty = DependencyProperty.Register("HeaderText", typeof(string), typeof(SoundsForm), new FrameworkPropertyMetadata(""));

        internal string HeaderText
        {
            get { return (string)GetValue(HeaderTextProperty); }
            set { SetValue(HeaderTextProperty, value); }
        }

        private Dictionary<string, string> _allSoundsDic;
        private string _sourceType;


        internal SoundsForm(string type)
        {
            InitializeComponent();
            _sourceType = type;
            _allSoundsDic = SoundManager.AllSounds;
            List<string> allSounds = new List<string>();
            foreach (KeyValuePair<string, string> keyValuePair in _allSoundsDic)
            {
                allSounds.Add(keyValuePair.Key);
            }
            RadCarouselSounds.ItemsSource = allSounds;
            SetData();

        }

        private async void SetData()
        {
            await Task.Delay(100);

            if (_sourceType == "spellcheck")
            {
                if (!string.IsNullOrEmpty(SettingsManager.Settings.SoundForSpellCheck))
                {
                    RadCarouselSounds.BringDataItemIntoView(SettingsManager.Settings.SoundForSpellCheck);
                }
                SliderVolume.Value = SettingsManager.Settings.SoundVolumeForSpellCheck;
            }
            if (_sourceType == "langswitch")
            {
                if (!string.IsNullOrEmpty(SettingsManager.Settings.SoundForLangSwitch))
                {
                    RadCarouselSounds.BringDataItemIntoView(SettingsManager.Settings.SoundForLangSwitch);
                }
                SliderVolume.Value = SettingsManager.Settings.SoundVolumeForLangSwitch;
            }
            if (_sourceType == "clipboard")
            {
                if (!string.IsNullOrEmpty(SettingsManager.Settings.SoundForClipboard))
                {
                    RadCarouselSounds.BringDataItemIntoView(SettingsManager.Settings.SoundForClipboard);
                }
                SliderVolume.Value = SettingsManager.Settings.SoundVolumeForClipboard;
            }
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }


        private void ButtonPlaySound(object sender, RoutedEventArgs e)
        {
            if (GetSelectedListBoxItem(sender) is CarouselItem ob)
            {
                SoundManager.PlaySound(_allSoundsDic[(string)ob.Content], (float)(SliderVolume.Value / 100));
            }
        }

        private object? GetSelectedListBoxItem(object sender)
        {
            try
            {
                if (RadCarouselSounds != null)
                {
                    var item = (DependencyObject)sender;
                    while (VisualTreeHelper.GetParent(item) != null && !(item is CarouselItem))
                    {
                        item = VisualTreeHelper.GetParent(item);
                    }
                    if (!(item is CarouselItem))
                    {
                        return null;
                    }
                    return item;
                }
            }
            catch
            {
                return null;
            }
            return null;
        }

        private void SliderVolume_OnValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_sourceType == "spellcheck")
            {
                SettingsManager.Settings.SoundVolumeForSpellCheck = (int)SliderVolume.Value;
            }
            if (_sourceType == "langswitch")
            {
                SettingsManager.Settings.SoundVolumeForLangSwitch = (int)SliderVolume.Value;
            }
            if (_sourceType == "clipboard")
            {
                SettingsManager.Settings.SoundVolumeForClipboard = (int)SliderVolume.Value;
            }
        }

        private void RadCarouselSoundsOnSelectionChanged(object sender, SelectionChangeEventArgs e)
        {
            if (RadCarouselSounds.SelectedItem != null)
            {
                if (_sourceType == "spellcheck")
                {

                    SettingsManager.Settings.SoundForSpellCheck = (string)RadCarouselSounds.SelectedItem;
                }
                if (_sourceType == "langswitch")
                {

                    SettingsManager.Settings.SoundForLangSwitch = (string)RadCarouselSounds.SelectedItem;
                }
                if (_sourceType == "clipboard")
                {

                    SettingsManager.Settings.SoundForClipboard = (string)RadCarouselSounds.SelectedItem;
                }
            }
        }
    }
}
