﻿using CaseConverter;
using Everylang.App.Clipboard;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Converter;
using Everylang.Common.Utilities;
using NickBuhro.Translit;

namespace Everylang.App.Converter
{
    class ConverterVarious
    {

        internal static void RunReplaceInSelTextWindow()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (text?.Trim().Replace("\r\n", "") != "")
            {
                ReplaceInSelTextWindow replaceInSelTextWindow = new ReplaceInSelTextWindow(text);
                replaceInSelTextWindow.Show();
            }
        }

        // добавить языки
        internal static void ConvertTransliteration()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                var lang = Language.Unknown;
                var detectedLang = KeyboardLayoutMethods.DetectLang(text);

                lang = detectedLang switch
                {
                    "ru" => Language.Russian,
                    "be" => Language.Belorussian,
                    "bg" => Language.Bulgarian,
                    "uk" => Language.Ukrainian,
                    _ => lang
                };

                var resultText = "";
                if (lang != Language.Unknown)
                {
                    resultText = Transliteration.CyrillicToLatin(text, lang);
                }
                else
                {
                    foreach (var cultureInfo in KeyboardLayoutMethods.GetInputLangs())
                    {
                        if (cultureInfo.TwoLetterISOLanguageName == "ru")
                        {
                            resultText = Transliteration.LatinToCyrillic(text, Language.Russian);
                            break;
                        }
                        if (cultureInfo.TwoLetterISOLanguageName == "be")
                        {
                            resultText = Transliteration.LatinToCyrillic(text, Language.Belorussian);
                            break;
                        }
                        if (cultureInfo.TwoLetterISOLanguageName == "bg")
                        {
                            resultText = Transliteration.LatinToCyrillic(text, Language.Bulgarian);
                            break;
                        }
                        if (cultureInfo.TwoLetterISOLanguageName == "uk")
                        {
                            resultText = Transliteration.LatinToCyrillic(text, Language.Ukrainian);
                            break;
                        }
                    }
                }
                SendText.SendStringByPaste(resultText, false);
            }
        }

        internal static void ConvertEncloseTextQuotationMarks()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                TextFrameWindow textFrameWindow = new TextFrameWindow(text);
                textFrameWindow.IsOpen = true;
            }
        }

        internal static void ConvertCamelCase()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                SendText.SendStringByPaste(text.ToCamelCase(), false);
            }
        }

        internal static void ConvertPascalCase()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                SendText.SendStringByPaste(text.ToPascalCase(), false);
            }
        }

        internal static void ConvertKebabCase()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                SendText.SendStringByPaste(text.ToKebabCase(), false);
            }
        }

        internal static void ConvertSnakeCase()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                SendText.SendStringByPaste(text.ToSnakeCase(), false);
            }
        }
    }
}
