﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Everylang.Common.LogManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;

namespace Everylang.App.Translator.Bing
{
    class BingTranslator
    {
        private static CachedObject<BingCredentials> _cachedCredentials;
        private RequestSettings? _requestSettings;

        internal WebResultTranslator? Translate(RequestSettings requestSettings)
        {
            _requestSettings = requestSettings;
            var sourceText = requestSettings.SourceTextTrimmed;
            var languageFromCurrent = requestSettings.LanguageFromCurrent.Abbreviation;
            var languageToCurrent = requestSettings.LanguageToCurrent.Abbreviation;
            var result = Translate(sourceText, languageFromCurrent, languageToCurrent);
            return result;
        }

        private WebResultTranslator? Translate(string sourceText, string? languageFromCurrent, string? languageToCurrent)
        {
            try
            {
                var credentials = GetOrUpdateCredentialsAsync();
                if (credentials == null)
                {
                    return new WebResultTranslator() { WithError = true, ErrorText = "" };
                }
                if (languageFromCurrent == "auto")
                {
                    languageFromCurrent = "auto-detect";
                }
                if (languageFromCurrent == languageToCurrent)
                {
                    languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                }

                var url = $"https://www.bing.com/ttranslatev3?isVertical=1&IG={TranslateCommonSettings.BingAppId}&IID=translator.5026";
                string postData = $"&text={Uri.EscapeDataString(sourceText)}&" +
                                  $"fromLang={languageFromCurrent}&" +
                                  $"to={languageToCurrent}&" +
                                  $"token={credentials.Token}&" +
                                  $"key={credentials.Key.ToString()}";


                var netLib = new NetLibTranslator(url, postData, "https://www.bing.com/translator/");
                var webResult = netLib.StartPostWebRequestBing();
                if (!webResult.WithError)
                {
                    if (webResult.ResultText != null)
                    {
                        webResult.FromLang = languageFromCurrent;
                        if (languageFromCurrent == "auto-detect")
                        {
                            webResult.FromLang = GetDetectedLanguage(webResult.ResultText);
                            if (webResult.FromLang == languageToCurrent)
                            {
                                if (webResult.FromLang == languageToCurrent)
                                {
                                    languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                                }
                                if (webResult.FromLang == languageToCurrent)
                                {
                                    languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                                }

                                return Translate(sourceText, webResult.FromLang, languageToCurrent);
                            }
                        }
                        webResult.ResultText = ProcessingOfTheRequest(webResult.ResultText);
                        webResult = LookUp(sourceText, webResult, languageToCurrent);
                        if (string.IsNullOrEmpty(webResult.ResultText))
                        {
                            return new WebResultTranslator() { WithError = true, ErrorText = "" };
                        }
                    }
                }
                webResult.ToLang = languageToCurrent;
                return webResult;
            }
            catch (Exception e)
            {
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }
        }

        private WebResultTranslator? LookUp(string sourceText, WebResultTranslator? webResult, string? languageToCurrent)
        {
            try
            {
                var credentials = GetOrUpdateCredentialsAsync();
                if (credentials == null)
                {
                    webResult.ResultText = "";
                    return webResult;
                }

                string url =
                    $"https://www.bing.com/tlookupv3?isVertical=1&&IG={TranslateCommonSettings.BingAppId}&IID=translator.5023.6";
                string postData = $"&text={Uri.EscapeDataString(sourceText)}&" +
                                  $"from={webResult.FromLang}&" +
                                  $"to={languageToCurrent}&" +
                                  $"token={credentials.Token}&" +
                                  $"key={credentials.Key.ToString()}";
                var netLib = new NetLibTranslator(url, postData, "https://www.bing.com/translator/");
                var webResultInner = netLib.StartPostWebRequestBing();
                if (!webResultInner.WithError)
                {
                    if (!string.IsNullOrEmpty(webResultInner.ResultText))
                    {
                        webResult.ResultTextWithNonChar = _requestSettings?.StartNonCharList + webResult.ResultText +
                                                   _requestSettings?.EndNonCharList;
                        webResult.ResultText = ProcessingLookUp(webResultInner.ResultText, webResult.ResultText);
                    }
                }

            }
            catch (Exception e)
            {
                Logger.LogTo.Debug(e, e.Message);
            }
            return webResult;
        }



        private string? ProcessingLookUp(string? resultText, string? translatedText)
        {
            var result = translatedText;
            try
            {
                if (!string.IsNullOrEmpty(resultText))
                {
                    JArray jsonArray = JArray.Parse(resultText);
                    if (jsonArray.Count > 0)
                    {
                        JObject? json = jsonArray.First as JObject;

                        var traslateResultStructList = new List<TraslateResultStruct>();
                        if (json != null && json.TryGetValue("translations", out var translationsToken))
                        {
                            foreach (var translationToken in translationsToken)
                            {
                                var pos = (translationToken.SelectToken("posTag") ?? throw new InvalidOperationException("BingTranslator: PosTag not found")).Value<string>()?.ToLower();
                                var traslateResultStruct = traslateResultStructList.FirstOrDefault(x => x.Pos == pos);
                                if (traslateResultStruct == null)
                                {
                                    traslateResultStruct = new TraslateResultStruct
                                    {
                                        Pos = pos,
                                        Terms = new List<TermsStruct>()
                                    };
                                    traslateResultStructList.Add(traslateResultStruct);
                                }

                                var termsStruct = new TermsStruct();
                                termsStruct.ReverseTranslation = new();
                                termsStruct.Word = (translationToken.SelectToken("displayTarget") ?? throw new InvalidOperationException("BingTranslator: DisplayTarget not found")).Value<string>();
                                foreach (var jToken in translationToken["backTranslations"]!)
                                {
                                    termsStruct.ReverseTranslation.Add(jToken["displayText"]?.Value<string>() ?? throw new InvalidOperationException("BingTranslator: DisplayText not found"));
                                }

                                traslateResultStruct.Terms?.Add(termsStruct);
                            }

                        }

                        foreach (var traslateResultStruct in traslateResultStructList)
                        {
                            result = "<bold>" + result + "\n" + "\n";
                            result += "\n<underline>" + traslateResultStruct.Pos + ":\n";
                            if (traslateResultStruct.Terms != null)
                            {
                                foreach (var item in traslateResultStruct.Terms)
                                {
                                    result += "      " + item.Word + " (";
                                    for (int index = 0; index < item.ReverseTranslation?.Count; index++)
                                    {
                                        var text = item.ReverseTranslation[index];
                                        if (index + 1 == item.ReverseTranslation.Count)
                                        {
                                            result += text;
                                        }
                                        else
                                        {
                                            result += text + ", ";
                                        }
                                    }

                                    result += ")\n";
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Debug(e, e.Message);
            }
            return result;
        }
        private class TraslateResultStruct
        {
            internal List<TermsStruct>? Terms;
            internal string? Pos;
        }

        private class TermsStruct
        {
            internal List<string>? ReverseTranslation;
            internal string? Word;
        }

        private string? ProcessingOfTheRequest(string? resultText)
        {
            try
            {
                if (resultText != null)
                {
                    JArray jsonArray = JArray.Parse(resultText);
                    if (jsonArray.Count > 0)
                    {
                        if (jsonArray.First is JObject json && json.TryGetValue("translations", out var jToken))
                        {
                            return (jToken.First?["text"] ?? throw new InvalidOperationException("BingTranslator: Text not found")).Value<string>();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Debug(e, e.Message);
            }
            return "";
        }

        private string? GetDetectedLanguage(string? resultText)
        {
            try
            {
                if (resultText != null)
                {
                    JArray jsonArray = JArray.Parse(resultText);
                    if (jsonArray.Count > 0)
                    {
                        if (jsonArray.First is JObject json && json.TryGetValue("detectedLanguage", out var jToken))
                        {
                            return (jToken["language"] ?? throw new InvalidOperationException("BingTranslator: Language not found")).Value<string>();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Debug(e, e.Message);
            }
            return "";
        }

        private string? DownloadBingAppId(string uri)
        {
            try
            {
                var netLib = new NetLibTranslator(uri);
                WebResultTranslator webResult = netLib.StartGetWebRequestWithCookies();
                if (!webResult.WithError)
                {
                    if (webResult.ResultText != null)
                        return webResult.ResultText;

                }

            }
            catch (Exception e)
            {
                Trace.Write(e.Message);
            }
            return "";
        }

        private void ProcessingOfTheRequestBingAppId(string? resultText)
        {
            try
            {
                string pattern = "IG:\"(.*?)\"";
                var rgx = new Regex(pattern, RegexOptions.IgnoreCase);
                if (resultText != null)
                {
                    MatchCollection matches = rgx.Matches(resultText);
                    if (matches.Count > 0)
                    {
                        if (matches[0].Groups.Count == 2)
                        {
                            string result = matches[0].Groups[1].Value;
                            TranslateCommonSettings.BingAppId = result;
                            TranslateCommonSettings.BingAppIdLastTime = DateTime.Now;
                        }

                    }
                }
            }
            catch (Exception e)
            {
                Trace.Write(e.Message);
            }
        }

        private BingCredentials? GetOrUpdateCredentialsAsync()
        {
            if (_cachedCredentials.Value != null && !_cachedCredentials.IsExpired())
            {
                return _cachedCredentials.Value;
            }
            string? content = DownloadBingAppId("https://www.bing.com/translator");
            ProcessingOfTheRequestBingAppId(content);
            string pattern = @"var\s+params_AbusePreventionHelper\s*=\s*\[(\d+),""([^""]+)"",(\d+)\]";
            Match match = Regex.Match(content, pattern);

            if (match.Success)
            {
                long timestamp = long.Parse(match.Groups[1].Value);
                string token = match.Groups[2].Value;

                var credentials = new BingCredentials(timestamp, token, Guid.NewGuid());

                _cachedCredentials = new CachedObject<BingCredentials>(credentials, DateTimeOffset.FromUnixTimeMilliseconds(timestamp + 3600000));
            }
            else
            {
                return null;
            }
            return _cachedCredentials.Value;
        }

        class BingCredentials
        {
            internal BingCredentials(long key, string token, Guid impressionGuid)
            {
                Key = key;
                Token = token;
                ImpressionGuid = impressionGuid;
            }

            internal long Key { get; }
            internal string Token { get; }
            internal Guid ImpressionGuid { get; }
        }

        readonly struct CachedObject<T>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="CachedObject{T}"/> structure with a specified value and no expiration date.
            /// </summary>
            /// <param name="value">The value.</param>
            internal CachedObject(T value)
            {
                Value = value;
                ExpirationDate = DateTimeOffset.MaxValue;
            }

            /// <summary>
            /// Initializes a new instance of the <see cref="CachedObject{T}"/> structure with a specified value and expiration date.
            /// </summary>
            /// <param name="value">The value.</param>
            /// <param name="expirationDate">The date this object will expire.</param>
            internal CachedObject(T value, DateTimeOffset expirationDate) : this(value)
            {
                ExpirationDate = expirationDate;
            }

            /// <summary>
            /// Gets the cached object.
            /// </summary>
            internal T? Value { get; }

            /// <summary>
            /// Gets the date this object will expire.
            /// </summary>
            internal DateTimeOffset ExpirationDate { get; }

            /// <summary>
            /// Returns whether this object has expired.
            /// </summary>
            /// <returns><see langword="true"/> if the object has expired, otherwise <see langword="false"/>.</returns>
            internal bool IsExpired() => DateTimeOffset.UtcNow > ExpirationDate;

            /// <inheritdoc/>
            public override string ToString() => $"Value: {Value}, Expired: {IsExpired()}";
        }

    }


}

