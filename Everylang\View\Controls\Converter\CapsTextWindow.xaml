﻿<Popup x:Class="Everylang.App.View.Controls.Converter.CapsTextWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
        mc:Ignorable="d" 
        Height="300" Width="300" AllowsTransparency="True" Placement="Absolute" StaysOpen="True" Focusable="False"
        x:ClassModifier="internal"
        DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Popup.Resources>
        <ResourceDictionary>

            
            <telerik:RadRadialMenuItem Click="ButtonClickInvertCase" x:Key="UniInvertCase" ToolTipContent="{telerik:LocalizableResource Key=UniInvertCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextInvertCase}" TextWrapping="Wrap" Margin="5,0,0,0"/>
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickUpCase" x:Key="UniUpCase" ToolTipContent="{telerik:LocalizableResource Key=UniUpCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextUpCase}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickDownCase" x:Key="UniDownCase" ToolTipContent="{telerik:LocalizableResource Key=UniDownCase}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextDownCase}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickConvertFirstLetterToUp" x:Key="UniFirstLetterToUp" ToolTipContent="{telerik:LocalizableResource Key=UniFirstLetterUp}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextFirstLetterUp}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>
            <telerik:RadRadialMenuItem Click="ButtonClickConvertFirstLetterToDown" x:Key="UniFirstLetterToDown" ToolTipContent="{telerik:LocalizableResource Key=UniFirstLetterDown}">
                <telerik:RadRadialMenuItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontSize="13" Name="TextBlockNumber" VerticalAlignment="Center" Visibility="Collapsed" Margin="0,0,5,0"/>
                        <TextBlock FontSize="13" TextAlignment="Center" Text="{telerik:LocalizableResource Key=UniTextFirstLetterDown}" TextWrapping="Wrap" Margin="5,0,0,0" />
                    </StackPanel>
                </telerik:RadRadialMenuItem.Header>
            </telerik:RadRadialMenuItem>

            <Style TargetType="telerik:RadialMenuButton" BasedOn="{StaticResource RadialMenuButtonStyle}">
                <Setter Property="ContentTemplate">
                    <Setter.Value>
                        <DataTemplate>
                            <wpf:MaterialIcon Kind="Adjust" />
                        </DataTemplate>
                    </Setter.Value>
                </Setter>
                <EventSetter Event="Click" Handler="EventSetter_OnHandler"></EventSetter>
            </Style>
        </ResourceDictionary>
    </Popup.Resources>
    <Grid>
        <telerik:RadRadialMenu x:Name="RadialMenuMu" VerticalAlignment="Center" HorizontalAlignment="Center" Navigated="RadialMenuMu_OnNavigated"
                               MinWidth="300" MinHeight="300"
                               InnerNavigationRadiusFactor="0.92"/>
    </Grid>
</Popup>































































































































































































































































































































































































































































































































