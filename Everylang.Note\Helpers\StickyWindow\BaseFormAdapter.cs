﻿using System;
using System.Drawing;
using Vanara.PInvoke;

namespace Everylang.Note.Helpers.StickyWindow
{
    internal abstract class BaseFormAdapter
    {
        private bool _isExtendedFrameMarginInitialized;
        private Rectangle _extendedFrameMargin;

        internal abstract IntPtr Handle { get; }
        protected abstract Rectangle InternalBounds { get; set; }
        internal abstract Size MaximumSize { get; set; }
        internal abstract Size MinimumSize { get; set; }
        internal abstract bool Capture { get; set; }
        internal abstract void Activate();
        internal abstract Point PointToScreen(Point point);

        private void InitExtendedFrameMargin()
        {
            if (_isExtendedFrameMarginInitialized)
            {
                return;
            }

            //Win32.RECT rect;
            if (DwmApi.DwmGetWindowAttribute(Handle, DwmApi.DWMWINDOWATTRIBUTE.DWMWA_EXTENDED_FRAME_BOUNDS, out RECT rect).Succeeded)
            {
                var originalFormBounds = InternalBounds;
                _extendedFrameMargin = new Rectangle(
                    -(originalFormBounds.Left - rect.Left),
                    -(originalFormBounds.Top - rect.Top),
                    -(originalFormBounds.Width - (rect.Right - rect.Left)),
                    -(originalFormBounds.Height - (rect.Bottom - rect.Top)));
            }
            else
            {
                _extendedFrameMargin = Rectangle.Empty;
            }
            _isExtendedFrameMarginInitialized = true;
        }

        internal Rectangle Bounds
        {
            get
            {
                InitExtendedFrameMargin();

                var bounds = InternalBounds;
                bounds.X += _extendedFrameMargin.Left;
                bounds.Y += _extendedFrameMargin.Top;
                bounds.Width += _extendedFrameMargin.Width;
                bounds.Height += _extendedFrameMargin.Height;

                return bounds;
            }
            set
            {
                InitExtendedFrameMargin();

                var bounds = value;
                bounds.X -= _extendedFrameMargin.Left;
                bounds.Y -= _extendedFrameMargin.Top;
                bounds.Width -= _extendedFrameMargin.Width;
                bounds.Height -= _extendedFrameMargin.Height;
                InternalBounds = bounds;
            }
        }
    }
}