﻿<Application x:Class="Everylang.App.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:helpers="clr-namespace:Everylang.App.View.Helpers">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/System.Windows.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/Telerik.Windows.Controls.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/Telerik.Windows.Controls.Input.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/Telerik.Windows.Controls.Navigation.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/Telerik.Windows.Controls.GridView.xaml" />
                <ResourceDictionary Source="/Telerik.Windows.Themes.Windows11;component/Themes/Telerik.Windows.Controls.ImageEditor.xaml"/>
                <telerik:Windows11ResourceDictionary />
            </ResourceDictionary.MergedDictionaries>
            <helpers:BoolToVisibilityConverter x:Key="BoolToVisCollapsed" />
            <telerik:BooleanToVisibilityConverter x:Key="BoolToVis" />
            <telerik:InvertedBooleanToVisibilityConverter x:Key="BoolToVisInvert" />
            <telerik:InvertedBooleanConverter x:Key="InvertedBooleanConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
