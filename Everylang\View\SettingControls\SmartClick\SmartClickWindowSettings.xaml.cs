﻿using Everylang.App.SettingsApp;
using Everylang.App.SmartClick;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.SmartClick
{
    /// <summary>
    /// Interaction logic for UniversalWindowSettings.xaml
    /// </summary>
    internal partial class SmartClickWindowSettings
    {
        internal SmartClickWindowSettings()
        {
            InitializeComponent();
        }

        private void ShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SmartClickShortcutSettingsHeader"), SettingsManager.Settings.SmartClickShortcut, nameof(SettingsManager.Settings.SmartClickShortcut), SmartClickManager.Instance.PressedShortcutSmartClick);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.UniversalWindowSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void SmartClickCheckItemsClick(object sender, RoutedEventArgs e)
        {
            SmartClickItemsChecker? smartClickItemsChecker = new SmartClickItemsChecker();
            PageTransitionControl.Content = smartClickItemsChecker;
            smartClickItemsChecker.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                smartClickItemsChecker = null;
            };
        }


    }
}
