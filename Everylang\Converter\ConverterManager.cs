﻿using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Converter;
using Everylang.App.ViewModels;
using NHotkey;
using System;

namespace Everylang.App.Converter
{
    class ConverterManager : IDisposable
    {

        private static ConverterManager? _instance;

        internal static ConverterManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ConverterManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            if (VMContainer.Instance.ConverterSettingsViewModel.jgebhdhs)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsInvert), SettingsManager.Settings.ConverterShortcutCapsInvert, PressedSwitcherSelectedCaps);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsUp), SettingsManager.Settings.ConverterShortcutCapsUp, PressedSwitcherSelectedCapsUp);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsDown), SettingsManager.Settings.ConverterShortcutCapsDown, PressedSwitcherSelectedCapsDown);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterExpresionShortcut), SettingsManager.Settings.ConverterExpresionShortcut, PressedConverterExpresion);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterTransliterationShortcut), SettingsManager.Settings.ConverterTransliterationShortcut, PressedConvertTransliteration);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut), SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut, PressedConvertEncloseTextQuotationMarks);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutCamelCase), SettingsManager.Settings.ConverterShortcutCamelCase, PressedCamelCase);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutSnakeCase), SettingsManager.Settings.ConverterShortcutSnakeCase, PressedSnakeCase);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutKebabCase), SettingsManager.Settings.ConverterShortcutKebabCase, PressedKebabCase);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutPascalCase), SettingsManager.Settings.ConverterShortcutPascalCase, PressedPascalCase);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutReplaceSelText), SettingsManager.Settings.ConverterShortcutReplaceSelText, PressedReplaceSelText);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterFirstLetterToDown), SettingsManager.Settings.ConverterFirstLetterToDown, PressedSwitcherFirstLetterToDown);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterFirstLetterToUp), SettingsManager.Settings.ConverterFirstLetterToUp, PressedSwitcherFirstLetterToUp);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterOpenWindowShortcut), SettingsManager.Settings.ConverterOpenWindowShortcut, PressedConverterOpenWindow);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsOpenWindow), SettingsManager.Settings.ConverterShortcutCapsOpenWindow, PressedSwitcherOpenWindowCaps);
            }


        }


        internal void PressedConvertEncloseTextQuotationMarks(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertEncloseTextQuotationMarks();
        }

        internal void PressedConvertTransliteration(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertTransliteration();
        }

        internal void PressedSwitcherOpenWindowCaps(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            string? selectedText = ClipboardOperations.GetSelectionText();

            if (selectedText?.Trim().Replace("\r\n", "") != "")
            {
                var capsTextWindow = new CapsTextWindow();
                capsTextWindow.Closed += (_, _) =>
                {
                    capsTextWindow = null;
                };
                capsTextWindow.SourceText = selectedText;
                capsTextWindow.ShowWindow();
            }
        }

        internal void PressedConverterOpenWindow(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            string? selectedText = ClipboardOperations.GetSelectionText();

            if (selectedText?.Trim().Replace("\r\n", "") != "")
            {
                var converterWindow = new ConverterWindow();
                converterWindow.Closed += (_, _) =>
                {
                    converterWindow = null;
                };
                converterWindow.SourceText = selectedText;
                converterWindow.ShowWindow();
            }
        }

        internal void PressedConverterExpresion(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterExpresion.ConvertExpresion();
        }

        internal void PressedSwitcherSelectedCaps(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterCaps.ConvertCaps();
        }

        internal void PressedSwitcherSelectedCapsUp(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterCaps.ConvertCapsUp();
        }

        internal void PressedSwitcherSelectedCapsDown(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterCaps.ConvertCapsDown();
        }

        internal void PressedSwitcherFirstLetterToDown(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (ConverterCaps.CallIsRunConvertFirstLetterToDown) return;
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterCaps.ConvertFirstLetterToDown();
        }

        internal void PressedSwitcherFirstLetterToUp(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            if (ConverterCaps.CallIsRunConvertFirstLetterToUp) return;
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterCaps.ConvertFirstLetterToUp();
        }

        internal void PressedCamelCase(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertCamelCase();
        }

        internal void PressedPascalCase(object? sender, HotkeyEventArgs e)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertPascalCase();
        }

        internal void PressedKebabCase(object? sender, HotkeyEventArgs e)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertKebabCase();
        }

        internal void PressedSnakeCase(object? sender, HotkeyEventArgs e)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.ConvertSnakeCase();
        }

        internal void PressedReplaceSelText(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckConverter()) return;
            ConverterVarious.RunReplaceInSelTextWindow();
        }




        internal void Stop()
        {
            ConverterStop();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        private void ConverterStop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterExpresionShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsDown));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsUp));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCapsInvert));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterTransliterationShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutCamelCase));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterShortcutReplaceSelText));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterFirstLetterToDown));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterFirstLetterToUp));
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize((object)this);
        }


    }
}
