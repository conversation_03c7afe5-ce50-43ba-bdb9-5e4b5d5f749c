﻿using Everylang.App.Main;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using Ookii.Dialogs.Wpf;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.General
{
    /// <summary>
    /// Interaction logic for GeneralControl.xaml
    /// </summary>
    internal partial class GeneralControl
    {
        internal GeneralControl()
        {
            InitializeComponent();
        }

        private void OpenProxySettings(object sender, RoutedEventArgs e)
        {
            GeneralControlProxy? controlProxy = new GeneralControlProxy();
            PageTransitionControl.Content = controlProxy;
            controlProxy.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                controlProxy = null;
            };
        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("OpenMainWindowShortcut"), SettingsManager.Settings.OpenMainWindowShortcut, nameof(SettingsManager.Settings.OpenMainWindowShortcut), AppHookManager.Instance.PressedMain);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.GeneralSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void StopWorkingShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("StopWorkingShortcut"), SettingsManager.Settings.StopWorkingShortcut, nameof(SettingsManager.Settings.StopWorkingShortcut), AppHookManager.Instance.StopWorking);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.GeneralSettingsViewModel.StopWorkingShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void EditDataFilePathClick(object sender, RoutedEventArgs e)
        {
            var browserDialog = new VistaFolderBrowserDialog();
            browserDialog.SelectedPath = VMContainer.Instance.GeneralSettingsViewModel.DataFilePath;
            var dialResult = browserDialog.ShowDialog();
            if (dialResult != null && dialResult.Value)
            {
                VMContainer.Instance.GeneralSettingsViewModel.DataFilePath = browserDialog.SelectedPath;
            }
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }


    }
}
