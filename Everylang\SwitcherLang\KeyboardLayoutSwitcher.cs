using Everylang.App.HookManager;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Everylang.Common.Utilities;
using Microsoft.Win32;
using System;
using System.Runtime.InteropServices;
using Vanara.PInvoke;
using WindowsInput;

namespace Everylang.App.SwitcherLang
{
    internal static class KeyboardLayoutSwitcher
    {
        internal static void SwitchLayout()
        {
            KeyboardLayoutCommon.CheckKeyboardLayuots();
            SwitchToNext();
        }

        internal static void SwitchLayoutToLang(IntPtr code)
        {
            KeyboardLayoutCommon.CheckKeyboardLayuots();
            SwitchToLayout(code);
        }

        internal static void SwitchLayoutToLangForAutoSwitch(IntPtr code)
        {
            KeyboardLayoutCommon.CheckKeyboardLayuots();
            SwitchToLayout(code);
        }

        internal static bool SwitchToNext()
        {
            int index = KeyboardLayoutCommon.LayoutHandleList.IndexOf(KeyboardLayoutCommon.CurrentKeyboardLayout) + 1;
            if (index == KeyboardLayoutCommon.LayoutHandleList.Count) index = 0;

            var nextKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.LayoutHandleList[index]);
            var currentKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.CurrentKeyboardLayout);
            var nameNext = currentKeyboardLayoutName.ToUpper() + "\u2192" + nextKeyboardLayoutName.ToUpper();
            if (SettingsManager.Settings.SwitcherNotTrueListOfLang.Contains(nameNext))
            {
                index += 1;
                if (index == KeyboardLayoutCommon.LayoutHandleList.Count) index = 0;
                nextKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.LayoutHandleList[index]);
                if (nextKeyboardLayoutName == currentKeyboardLayoutName)
                {
                    return false;
                }
            }

            if (index == KeyboardLayoutCommon.LayoutHandleList.IndexOf(KeyboardLayoutCommon.CurrentKeyboardLayout))
            {
                return false;
            }

            SwitchToLayout(KeyboardLayoutCommon.LayoutHandleList[index]);

            return true;
        }

        private static bool _switcherSountOff;

        internal static void SwitchToLayout(IntPtr code, bool switcherSountOff = false)
        {
            if (KeyboardLayoutCommon.CurrentKeyboardLayout.Equals(code))
            {
                return;
            }

            _switcherSountOff = switcherSountOff;
            KeyboardLayoutManager.Instance.IsProcessing = true;
            CommonHookListener.IsKeyEnabled = false;
            try
            {
                if (SettingsManager.Settings.SwitcherSwitchMethod == 1)
                {
                    SwitchByCommand(code);
                }
                else
                {
                    _codeForSwitch = code;
                    int indexCurent = KeyboardLayoutCommon.LayoutHandleList.IndexOf(KeyboardLayoutCommon.CurrentKeyboardLayout);
                    int count = 0;
                    while (KeyboardLayoutCommon.LayoutHandleList[indexCurent] != code)
                    {
                        count++;
                        indexCurent++;
                        if (indexCurent == KeyboardLayoutCommon.LayoutHandleList.Count)
                        {
                            indexCurent = 0;
                        }
                    }
                    SwitchLayoutForWindows(count);
                }
            }
            catch (COMException)
            {
            }
            catch
            {
                // Ignore
            }
            KeyboardLayoutWorker.IsLayoutChanging = false;
            KeyboardLayoutManager.Instance.IsProcessing = false;
            CommonHookListener.IsKeyEnabled = true;
        }

        private static IntPtr _codeForSwitch;

        private static void SwitchLayoutForWindows(int count)
        {
            string? keyRes = "";
            using (RegistryKey reg = Registry.CurrentUser.CreateSubKey("Keyboard Layout\\Toggle"))
            {
                var key = reg.GetValue("Hotkey");
                if (key != null)
                {
                    keyRes = key.ToString();
                }
                else
                {
                    key = reg.GetValue("Language Hotkey");
                    if (key != null)
                    {
                        keyRes = key.ToString();
                    }
                }

                if (key == null)
                {
                    keyRes = "1";
                }
            }

            if (keyRes != null) Switch(keyRes, count);
        }

        private static void Switch(string? key, int count)
        {


            if (key == "" || key == "3")
            {
                SwitchByCommand(_codeForSwitch);
                return;
            }

            KeyboardState.ReleaseAllKeys(true);
            var sim = new InputSimulator();

            for (int i = 0; i < count; i++)
            {
                sim.Keyboard.ModifiedKeyStroke(VirtualKeyCode.LWIN, VirtualKeyCode.SPACE).Sleep(50);
                //sim.Keyboard.KeyDown(VirtualKeyCode.LMENU).Sleep(10);
                //sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT).Sleep(10);
                //sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT).Sleep(10);
                //sim.Keyboard.KeyUp(VirtualKeyCode.LMENU).Sleep(10);
            }


            //if (key == "1")
            //{
            //    for (int i = 0; i < count; i++)
            //    {
            //        sim.Keyboard.ModifiedKeyStroke(VirtualKeyCode.LMENU, VirtualKeyCode.LSHIFT).Sleep(30);
            //        //sim.Keyboard.KeyDown(VirtualKeyCode.LMENU).Sleep(10);
            //        //sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT).Sleep(10);
            //        //sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT).Sleep(10);
            //        //sim.Keyboard.KeyUp(VirtualKeyCode.LMENU).Sleep(10);
            //    }
            //}

            //if (key == "2")
            //{
            //    for (int i = 0; i < count; i++)
            //    {
            //        sim.Keyboard.ModifiedKeyStroke(VirtualKeyCode.LCONTROL, VirtualKeyCode.LSHIFT).Sleep(30);
            //        // sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL).Sleep(10);
            //        // sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT).Sleep(10);
            //        // sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT).Sleep(10);
            //        // sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL).Sleep(10);
            //    }

            //}

            //if (key == "4")
            //{
            //    for (int i = 0; i < count; i++)
            //    {
            //        sim.Keyboard.KeyPress(VirtualKeyCode.OEM_3).Sleep(10);
            //    }
            //}

            if (SettingsManager.Settings.SwitcherSountIsOn && !_switcherSountOff)
            {
                SoundManager.PlayForSwitch();
            }
        }

        private static void SwitchByCommand(IntPtr code)
        {
            uint WM_INPUTLANGCHANGEREQUEST = 0x0050;
            // ReSharper disable once InconsistentNaming
            IntPtr INPUTLANGCHANGE_SYSCHARSET = new IntPtr(0x0001);
            User32.PostMessage(ForegroundWindow.GetForeground(), WM_INPUTLANGCHANGEREQUEST,
                wParam: INPUTLANGCHANGE_SYSCHARSET, lParam: code);
            if (SettingsManager.Settings.SwitcherSountIsOn && !_switcherSountOff)
            {
                SoundManager.PlayForSwitch();
            }
        }
    }

}