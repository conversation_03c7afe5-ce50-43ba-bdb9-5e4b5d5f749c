﻿<telerik:RadWindow
    CanClose="{Binding GeneralSettingsViewModel.CanClose}"
    CornerRadius="5"
    Height="550"
    HideMaximizeButton="True"
    Loaded="RadWindow_Loaded"
    MinHeight="550"
    MinWidth="900"
    PreviewClosed="MainWindow_OnPreviewClosed"
    RestoreMinimizedLocation="True"
    Width="900"
    WindowStartupLocation="CenterScreen"
    helpers:WindowEffectsHelper.BackdropMaterial="Mica"
    navigation:RadWindowInteropHelper.AllowTransparency="True"
    navigation:RadWindowInteropHelper.ShowInTaskbar="True"
    navigation:RadWindowInteropHelper.Title="{Binding Path=MainWindowViewModel.TitleName, Mode=TwoWay}"
    x:Class="Everylang.App.View.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:helpers="clr-namespace:Telerik.Windows.Controls.Theming.Helpers;assembly=Telerik.Windows.Controls"
    xmlns:navigation="clr-namespace:Telerik.Windows.Controls.Navigation;assembly=Telerik.Windows.Controls.Navigation"
    xmlns:tb="clr-namespace:H.NotifyIcon;assembly=H.NotifyIcon.Wpf"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialIcons="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:view1="clr-namespace:Everylang.App.View"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <telerik:RadWindow.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="view1:MainWindow" />
        </ResourceDictionary>
    </telerik:RadWindow.Resources>
    <telerik:RadWindow.Header>

        <Grid Margin="0,3,0,0">
            <TextBlock Text="{Binding Path=MainWindowViewModel.TitleName, Mode=TwoWay}" VerticalAlignment="Center" />
            <TextBlock
                HorizontalAlignment="Right"
                Text="{Binding Path=MainWindowViewModel.CurrentService, Mode=TwoWay}"
                VerticalAlignment="Center" />
        </Grid>

    </telerik:RadWindow.Header>
    <Grid>
        <tb:TaskbarIcon
            ToolTipText="{telerik:LocalizableResource Key=StatusButtonCapsLockIsOn}"
            Visibility="Hidden" 
            Id="6CE3B7CD-C4EE-423D-871F-4ABC55B203A4"
            x:Name="NotifyIconCapsLock" />
        <tb:TaskbarIcon
            Visibility="Hidden" 
            Id="ACF98767-CC25-48E2-A09B-1895ACAA1119"
            x:Name="NotifyIconNumLock" />
        <tb:TaskbarIcon
            Id="FE67ABDC-D92E-47F9-82C5-60583CACCFB9"
            MenuActivation="RightClick"
            ToolTipText="{Binding MainWindowViewModel.TitleName}"
            TrayMouseDoubleClick="NotifyIcon_OnTrayIconMouseDoubleClick"
            Visibility="Visible"
            x:Name="NotifyIconMain">
            <tb:TaskbarIcon.ContextMenu>
                <ContextMenu>
                    <ContextMenu.Template>
                        <ControlTemplate>
                            <telerik:RadContextMenu>
                                <telerik:RadMenuItem Click="SettingsTrayOnClick" Header="{telerik:LocalizableResource Key=Settings}" />
                                <telerik:RadMenuItem IsSeparator="True" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=IsLangInfoWindowShowForMouse}"
                                    IsCheckable="True"
                                    IsChecked="{Binding LangFlagSettingsViewModel.IsLangInfoWindowShowForMouse, Mode=TwoWay}" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=IsLangInfoWindowShowForCaret}"
                                    IsCheckable="True"
                                    IsChecked="{Binding LangFlagSettingsViewModel.IsLangInfoWindowShowForCaret, Mode=TwoWay}" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=IsLangInfoInTray}"
                                    IsCheckable="True"
                                    IsChecked="{Binding LangFlagSettingsViewModel.IsLangInfoInTray, Mode=TwoWay}" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=IsLangInfoWindowShowLargeWindow}"
                                    IsCheckable="True"
                                    IsChecked="{Binding LangFlagSettingsViewModel.IsLangInfoWindowShowLargeWindow, Mode=TwoWay}" />
                                <telerik:RadMenuItem IsSeparator="True" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=TransSettingsTranslationIsAlways}"
                                    IsCheckable="True"
                                    IsChecked="{Binding TranslationSettingsViewModel.TranslationIsAlways, Mode=TwoWay}" />
                                <telerik:RadMenuItem IsSeparator="True" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=SwitcherTab}"
                                    IsCheckable="True"
                                    IsChecked="{Binding SwitcherSettingsViewModel.SwitcherIsOn, Mode=TwoWay}" />
                                <telerik:RadMenuItem
                                    Header="{telerik:LocalizableResource Key=AutoSwitcherSettingsHeader}"
                                    IsCheckable="True"
                                    IsChecked="{Binding AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch, Mode=TwoWay}" />
                                <telerik:RadMenuItem IsSeparator="True" />
                                <telerik:RadMenuItem
                                    Click="DisableApp"
                                    Header="{telerik:LocalizableResource Key=WorkingOff}"
                                    Visibility="{Binding MainWindowViewModel.IsStopWorking, Converter={StaticResource BoolToVisInvert}}" />
                                <telerik:RadMenuItem
                                    Click="EnableApp"
                                    Header="{telerik:LocalizableResource Key=WorkingOn}"
                                    Visibility="{Binding MainWindowViewModel.IsStopWorking, Converter={StaticResource BoolToVis}}" />
                                <telerik:RadMenuItem Click="ExitTrayOnClick" Header="{telerik:LocalizableResource Key=Exit}" />
                            </telerik:RadContextMenu>

                        </ControlTemplate>
                    </ContextMenu.Template>
                </ContextMenu>
            </tb:TaskbarIcon.ContextMenu>
        </tb:TaskbarIcon>

        <telerik:RadNavigationView
            AutoChangeDisplayMode="False"
            BorderThickness="1"
            Name="MuNavigationView"
            PaneHeaderHeight="5"
            PaneToggleButtonVisibility="Collapsed"
            SelectionChanged="NavigationViewOnSelectionChanged">
            <telerik:RadNavigationView.Items>
                <telerik:RadNavigationViewItem Tag="0" ToolTip="{telerik:LocalizableResource Key=TranslationTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="Translate"
                            Width="20"
                            Height="20"
                            VerticalAlignment="Center" />
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                <telerik:RadNavigationViewItem Tag="1" ToolTip="{telerik:LocalizableResource Key=ClipboardTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="ClipboardOutline"
                            VerticalAlignment="Center" 
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                <telerik:RadNavigationViewItem Tag="2" ToolTip="{telerik:LocalizableResource Key=DiareTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="NotebookOutline"
                            VerticalAlignment="Center"
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                <telerik:RadNavigationViewItem Tag="3" ToolTip="{telerik:LocalizableResource Key=NoteTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="NoteMultiple"
                            VerticalAlignment="Center" 
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                <telerik:RadNavigationViewItem Tag="4" ToolTip="{telerik:LocalizableResource Key=AutochangeTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="StickerTextOutline"
                            VerticalAlignment="Center" 
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                <telerik:RadNavigationViewItem Tag="5" ToolTip="{telerik:LocalizableResource Key=OcrTab}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="ImageSearchOutline"
                            VerticalAlignment="Center" 
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
                

            </telerik:RadNavigationView.Items>
            <telerik:RadNavigationView.PaneFooter>
                <telerik:RadNavigationViewItem Click="ShowSettingsOnClick" ToolTip="{telerik:LocalizableResource Key=Settings}">
                    <telerik:RadNavigationViewItem.Icon>
                        <materialIcons:MaterialIcon 
                            Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                            HorizontalAlignment="Center"
                            Kind="Cog"
                            VerticalAlignment="Center"
                            Width="20"
                            Height="20"/>
                    </telerik:RadNavigationViewItem.Icon>
                </telerik:RadNavigationViewItem>
            </telerik:RadNavigationView.PaneFooter>
        </telerik:RadNavigationView>
    </Grid>
</telerik:RadWindow>
