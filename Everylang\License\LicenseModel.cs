﻿using System;

namespace Everylang.App.License;

internal class LicenseModel
{
    internal string License { get; set; } = "";

    internal string Email { get; set; } = "";

    internal int UsersCount { get; set; }

    internal string? UserName { get; set; } = "";

    internal bool IsEnterprise { get; set; }

    internal string MachineName { get; set; } = "";

    internal bool IsBlockedByEarlyActivation { get; set; }

    internal bool IsBlockedByMonthActivateLimit { get; set; }

    internal bool LicIsBlocked { get; set; }

    internal bool LicIsTrial { get; set; }

    internal bool IsConnectOk { get; set; }

    internal string ErrorConnect { get; set; } = "";

    internal string ErrorParce { get; set; } = "";

    internal string ErrorLic { get; set; } = "";

    internal bool IsReactivated { get; set; }

    internal string LastSeat { get; set; } = "";

    internal DateTime LicExpiryDate { get; set; }

    internal DateTime ActivateDate { get; set; }

}