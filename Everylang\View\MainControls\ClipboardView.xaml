﻿<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Everylang.App.View.MainControls.ClipboardView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    xmlns:dataModel="clr-namespace:Everylang.App.Data.DataModel"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}" IsEnabled="{Binding Path=ClipboardViewModel.jgebhdhs}"
          >
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200*" />
        </Grid.RowDefinitions>
        <WrapPanel
            Grid.Row="0"
            HorizontalAlignment="Left"
            Margin="5"
            Orientation="Horizontal"
            VerticalAlignment="Top">
            <telerik:RadAutoSuggestBox
                ClearButtonVisibility="Auto"
                Name="SearchTextBox"
                WatermarkContent="{telerik:LocalizableResource Key=SearchHelperText}"
                TextChanged="TextBoxBase_OnTextChanged"
                Width="250" />
            <Button
                Command="{Binding Path=ClipboardViewModel.DeleteSelectedCommand}"
                Content="{telerik:LocalizableResource Key=HistoryDelSelected}"
                IsEnabled="{Binding ClipboardViewModel.IsSelectedNotNull}"
                Margin="5,0,0,0"
                ToolTip="{telerik:LocalizableResource Key=HistoryDelSelected}" />
            <Button
                Command="{Binding Path=ClipboardViewModel.ClearAllCommand}"
                Content="{telerik:LocalizableResource Key=HistoryClear}"
                IsEnabled="{Binding ClipboardViewModel.IsNotNullAll}"
                Margin="5,0,0,0"
                ToolTip="{telerik:LocalizableResource Key=HistoryClear}" />
            <telerik:RadToggleButton Checked="ToggleButtonFavorite_OnChecked"
                                     Unchecked="ToggleButtonFavorite_OnChecked"
                                     Content="{telerik:LocalizableResource Key=Favorite}"
                                     IsEnabled="{Binding ClipboardViewModel.IsNotNullAll}"
                                     Margin="5,0,0,0"
                                     Focusable="False"
                                     ToolTip="{telerik:LocalizableResource Key=ClipboardFavorite}"/>


        </WrapPanel>
        <telerik:RadToggleSwitchButton
            CheckedContent="{telerik:LocalizableResource Key=ClipboardOn}"
            ContentPosition="Left"
            Grid.Row="0"
            HorizontalAlignment="Right"
            IsChecked="{Binding Path=ClipboardViewModel.IsEnabled}"
            Margin="0,0,10,0"
            UncheckedContent="{telerik:LocalizableResource Key=ClipboardOff}"
            VerticalAlignment="Center" />

        <telerik:RadGridView
            AutoExpandGroups="True"
            AutoGenerateColumns="False"
            CanUserSortGroups="False"
            ClipboardCopyMode="None"
            Focusable="False"
            FrozenColumnsSplitterVisibility="Hidden"
            Grid.Row="1"
            GroupRenderMode="Flat"
            IsPropertyChangedAggregationEnabled="False"
            IsSynchronizedWithCurrentItem="False"
            IsReadOnly="True"
            ItemsSource="{Binding Path=ClipboardViewModel.AllClipboardItems}"
            Margin="5,0,5,5"
            MouseDoubleClick="listBoxItem_DoubleClick"
            RowIndicatorVisibility="Collapsed"
            SelectionChanged="lvClipboard_SelectionChanged"
            SelectionMode="Extended"
            IsLocalizationLanguageRespected="False"
            SelectionUnit="FullRow"
            ShowGroupPanel="False"
            TextSearch.TextPath="ShortText"
            x:Name="lvClipboard">
            <telerik:RadContextMenu.ContextMenu>
                <telerik:RadContextMenu Opened="GridContextMenu_Opened" x:Name="GridContextMenu" />
            </telerik:RadContextMenu.ContextMenu>
            <telerik:RadGridView.Columns>
                <telerik:GridViewDataColumn Width="0" DataMemberBinding="{Binding IsFavorite}" IsVisible="False"/>
                <telerik:GridViewDataColumn
                    Header="{telerik:LocalizableResource Key=DiaryHeaderText}"
                    IsFilterable="False"
                    Width="500*">
                    <telerik:GridViewDataColumn.CellTemplate>
                        <DataTemplate DataType="{x:Type dataModel:ClipboardDataModel}">
                            <StackPanel Orientation="Horizontal">
                                <Path Fill="Gold" Height="12" Width="12" Data="M9.5,3 L11.5,8 L16.5,8 L12.5,11.5 L14.5,16.5 L9.5,13 L4.5,16.5 L6.5,11.5 L2.5,8 L7.5,8 Z" Stretch="Fill" Visibility="{Binding IsFavorite, Converter={StaticResource BoolToVis}}" Margin="0,0,5,0" />
                            <TextBlock>
                                <TextBlock.Text>                                    
                                    <Binding Path="ShortText" IsAsync="True" />
                                </TextBlock.Text>
                            </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </telerik:GridViewDataColumn.CellTemplate>
                    <telerik:GridViewColumn.ToolTipTemplate>
                        <DataTemplate DataType="{x:Type dataModel:ClipboardDataModel}">
                            <StackPanel>
                                <TextBlock Text="{Binding TextPrev, IsAsync=True}" />
                                <Image Margin="0,0,0,2" Source="{Binding ImagePrev}" />
                            </StackPanel>
                        </DataTemplate>
                    </telerik:GridViewColumn.ToolTipTemplate>
                </telerik:GridViewDataColumn>
                <telerik:GridViewDataColumn
                    DataMemberBinding="{Binding Application, IsAsync=True}"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderApp}"
                    Width="140" />
                <telerik:GridViewDataColumn
                    FilterMemberPath="DateTimeDate"
                    GroupMemberPath="DateTimeDate"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderDate}"
                    SortMemberPath="DateTime"
                    Width="Auto">
                    <telerik:GridViewDataColumn.CellTemplate>
                        <DataTemplate DataType="{x:Type dataModel:ClipboardDataModel}">
                            <TextBlock>
                                <TextBlock.Text>
                                    <Binding Path="DateTime" StringFormat="{}{0:g}" IsAsync="True" />
                                </TextBlock.Text>
                            </TextBlock>
                        </DataTemplate>
                    </telerik:GridViewDataColumn.CellTemplate>
                </telerik:GridViewDataColumn>

                <telerik:GridViewDataColumn
                    FilterMemberPath="Format"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderFormat}"
                    SortMemberPath="Format"
                    Width="70">
                    <telerik:GridViewDataColumn.CellTemplate>
                        <DataTemplate DataType="{x:Type dataModel:ClipboardDataModel}">
                            <telerik:RadHyperlinkButton
                                Margin="0"
                                Padding="5,0,5,0"
                                Click="ButtonClickFormat"
                                Content="{Binding Path=Format}"
                                Foreground="{telerik:Windows11Resource ResourceKey=IconBrush}"
                                HorizontalAlignment="Center"
                                VisitedForeground="{telerik:Windows11Resource ResourceKey=IconBrush}" />
                        </DataTemplate>
                    </telerik:GridViewDataColumn.CellTemplate>
                </telerik:GridViewDataColumn>
            </telerik:RadGridView.Columns>
        </telerik:RadGridView>
    </Grid>
</UserControl>