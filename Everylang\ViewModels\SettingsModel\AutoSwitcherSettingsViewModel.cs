﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using System.Linq;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class AutoSwitcherSettingsViewModel : ViewModelBase
    {
        public RadObservableCollection<AutoSwitchRuleDataModel> AutoSwitchRuleDataModels { get; set; }

        public AutoSwitcherSettingsViewModel()
        {
            AutoSwitchRuleDataModels = new RadObservableCollection<AutoSwitchRuleDataModel>();
            AutoSwitchRuleDataModels.AddRange(AutoSwitchRuleManager.GetAllAutoSwitchRules());

            base.OnPropertyChanged(nameof(AutoSwitchRuleDataModels));
        }



        internal void AddDataModel(AutoSwitchRuleDataModel dataModel)
        {
            if (AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text == dataModel.Text) == null)
            {
                AutoSwitchRuleManager.AddAutoSwitchRule(dataModel);
                AutoSwitchRuleDataModels.Add(dataModel);
                base.OnPropertyChanged(nameof(AutoSwitchRuleDataModels));
            }
        }

        internal void SaveAllToDb()
        {
            AutoSwitchRuleManager.SaveAllData(AutoSwitchRuleDataModels);
        }

        internal void RemoveDataModel(AutoSwitchRuleDataModel dataModel)
        {
            if (AutoSwitchRuleDataModels.Contains(dataModel))
            {
                AutoSwitchRuleManager.DelAutoSwitchRule(dataModel);
                AutoSwitchRuleDataModels.Remove(dataModel);
                base.OnPropertyChanged(nameof(AutoSwitchRuleDataModels));
            }
        }

        internal void UpdateDataModel(AutoSwitchRuleDataModel dataModel)
        {
            AutoSwitchRuleManager.UpdateData(dataModel);
            base.OnPropertyChanged(nameof(AutoSwitchRuleDataModels));
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsEnabledAutoSwitch));
            }
        }

        public bool IsEnabledAutoSwitch
        {

            get
            {
                return jgebhdhs && SettingsManager.Settings.AutoSwitcherIsOn;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.AutoSwitcherIsOn = value;
                SettingsManager.Settings.AutoSwitcherAddRule = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsOnAddingRule));
            }
        }

        public bool IsOnTwoUpperCaseLetters
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherFixTwoUpperCaseLettersInStart;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherFixTwoUpperCaseLettersInStart = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsOnFixWrongUpperCase
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherFixWrongUpperCase;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherFixWrongUpperCase = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsSwitchOneLetter
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherIsSwitchOneLetter;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherIsSwitchOneLetter = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsOnByEnter
        {

            get
            {
                return SettingsManager.Settings.AutoSwitcherSwitchTextLangAfterPressEnter;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherSwitchTextLangAfterPressEnter = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsOnUpperCaseNotSwitch
        {

            get
            {
                return SettingsManager.Settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsOnAddingRule
        {

            get
            {
                return SettingsManager.Settings.AutoSwitcherAddRule;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherAddRule = value;
                base.OnPropertyChanged();
            }
        }

        public bool AutoSwitcherShowAcceptWindow
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherShowAcceptWindow;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherShowAcceptWindow = value;
                base.OnPropertyChanged();
            }
        }

        public bool AutoSwitcherDisableAutoSwitchAfterManualSwitch
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch = value;
                base.OnPropertyChanged();
            }
        }

        public bool AutoSwitcherOnlyAfterSeparator
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherOnlyAfterSeparator;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherOnlyAfterSeparator = value;
                base.OnPropertyChanged();
            }
        }

        public bool AutoSwitcherAfterPause
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherAfterPause;
            }
            set
            {
                SettingsManager.Settings.AutoSwitcherAfterPause = value;
                base.OnPropertyChanged();
            }
        }


        public int AutoSwitcherCountCheckRule
        {
            get
            {
                return SettingsManager.Settings.AutoSwitcherCountCheckRule;
            }
            set
            {
                foreach (var autoSwitchRuleDataModel in AutoSwitchRuleDataModels)
                {
                    if (autoSwitchRuleDataModel.ManualSwitchCount == SettingsManager.Settings.AutoSwitcherCountCheckRule)
                    {
                        autoSwitchRuleDataModel.ManualSwitchCount = value;
                    }
                }
                SettingsManager.Settings.AutoSwitcherCountCheckRule = value;
                base.OnPropertyChanged();
            }
        }
    }
}
