﻿using System;
using System.ComponentModel;

namespace Everylang.App.HookManager.GlobalHooks
{

    /// <summary>
    /// This enumarates the sides on your keyboard where you can find the modifier keys. (Left and Right)
    /// </summary>
    internal enum ModifierKeySide
    {
        /// <summary>
        /// The left instance of the modifier key on the Keyboard.
        /// </summary>
        Left,
        /// <summary>
        /// The right instance of the modifier key on the Keyboard.
        /// </summary>
        Right,
        None
    }

    /// <summary>
    /// Creates a low level hook which detects events from the Keyboard even when the application doesn't have the focus.
    /// </summary>
    internal class GlobalKeyHook
    {
        //static GlobalKeyHook singleton; //This will allow us to check if there's already an instance of this class created.

        /// <summary>
        /// This event will be called when the user presses down a key.
        /// </summary>
        internal event EventHandler<GlobalKeyEventArgs>? OnKeyDown;

        /// <summary>
        /// This event will be called when the user lets go of a key.
        /// </summary>
        internal event EventHandler<GlobalKeyEventArgs>? OnKeyUp;


        private readonly InternalGlobalKeyHook _globalKeyboardHook; //A variable pertaining to an instance of the InternalGlobalKeyHook which handles low level stuff for us.

        //Keep track of the modifiers key that are being pressed as well as the location of that modifier key.
        ModifierKeySide _shift = ModifierKeySide.None;
        ModifierKeySide _alt = ModifierKeySide.None;
        ModifierKeySide _ctrl = ModifierKeySide.None;

        //List<VirtualKeycodes> currentKeysPressed = new List<VirtualKeycodes>(); //This will store all the keys that are currently being pressed by the user. This will prevent the event OnKeyDown from being continually called.

        internal GlobalKeyHook()
        {
            _globalKeyboardHook = new InternalGlobalKeyHook(); //Instantiate GlobalKeyboardHook
            _globalKeyboardHook.KeyboardPressed += OnKeyPressed; //Subscribe OnKeyPressed method to KeyboardPressed event
        }

        //This is the method that is subscribed to our KeyboardPressed event found in the internal GlobalKeyboardHook class.
        private void OnKeyPressed(object? sender, GlobalKeyboardHookEventArgs e)
        {
            if (e.KeyboardState == InternalGlobalKeyHook.KeyboardState.KeyDown || e.KeyboardState == InternalGlobalKeyHook.KeyboardState.SysKeyDown) //When the user presses down a key.
            {
                HandleModifierKeyDown(e);
                e.Handled = KeyDown(e); //We fire the KeyDown event which will also return a bool value (Handled var). If variable handled is set to true, the succeeding events handling a KeyDown will not be called.
            }
            else if (e.KeyboardState == InternalGlobalKeyHook.KeyboardState.KeyUp ||
                     e.KeyboardState == InternalGlobalKeyHook.KeyboardState.SysKeyUp
            ) //When the user lets go of a key.
            {
                HandleModifierKeyUp(e);
                e.Handled = KeyUp(e);
            }
        }

        /// <summary>
        /// Checks if key pressed is a modifier key. If it is, we set the values of shift, alt and ctrl accordingly. We also return the bool value of true if the key pressed is a modifier and vice versa.
        /// </summary>
        /// <param name="e">Argument passed by the KeyboardPressed event found in the internal GlobalKeyboardHook class.</param>
        /// <returns>Returns true if the key that was pressed is a modifier key. False if otherwise.</returns>
        void HandleModifierKeyDown(GlobalKeyboardHookEventArgs e)
        {
            switch (e.KeyboardData.VirtualCode) //Check which Modifier Key was pressed, set the appropriate variable to the location of the modifier key that was pressed and return true. Return False if the key wasn't a modifier key.
            {
                case VirtualKeycodes.LeftShift:
                    _shift = ModifierKeySide.Left;
                    return;
                case VirtualKeycodes.LeftAlt:
                    _alt = ModifierKeySide.Left;
                    return;
                case VirtualKeycodes.LeftCtrl:
                    _ctrl = ModifierKeySide.Left;
                    return;
                case VirtualKeycodes.RightCtrl:
                    _ctrl = ModifierKeySide.Right;
                    return;
                case VirtualKeycodes.RightShift:
                    _shift = ModifierKeySide.Right;
                    return;
                case VirtualKeycodes.RightAlt:
                    _alt = ModifierKeySide.Right;
                    return;
                default:
                    return;
            }
        }

        /// <summary>
        /// Checks if the key that was let go is a modifier key. If it is, we set the values of shift, alt and ctrl accordingly. We also return the bool value of true if the key that was let go is a modifier and vice versa.
        /// </summary>
        /// <param name="e">Argument passed by the KeyboardPressed event found in the internal GlobalKeyboardHook class.</param>
        /// <returns>Returns true if the key that was let go is a modifier key. If otherwise, false.</returns>
        void HandleModifierKeyUp(GlobalKeyboardHookEventArgs e)
        {
            switch (e.KeyboardData.VirtualCode) //Check which Modifier Key was let go, set the appropriate variable back to not pressed state (none) and return true. Return False if the key wasn't a modifier key.
            {
                case VirtualKeycodes.LeftShift:
                    _shift = ModifierKeySide.None;
                    return;
                case VirtualKeycodes.LeftAlt:
                    _alt = ModifierKeySide.None;
                    return;
                case VirtualKeycodes.LeftCtrl:
                    _ctrl = ModifierKeySide.None;
                    return;
                case VirtualKeycodes.RightCtrl:
                    _ctrl = ModifierKeySide.None;
                    return;
                case VirtualKeycodes.RightShift:
                    _shift = ModifierKeySide.None;
                    return;
                case VirtualKeycodes.RightAlt:
                    _alt = ModifierKeySide.None;
                    return;
                default:
                    return;
            }
        }

        /// <summary>
        /// This method will call the OnKeyDown event exposed from the library and pass on the information about the key that was pressed.
        /// </summary>
        /// <param name="e">Argument passed by the KeyboardPressed event found in the internal GlobalKeyboardHook class.</param>
        /// <returns>Returns true if the event was handled and false if not.</returns>
        bool KeyDown(GlobalKeyboardHookEventArgs e)
        {

            GlobalKeyEventArgs globalKeyEventArgs = new GlobalKeyEventArgs(e, _alt, _ctrl, _shift); //Store the information passed to this method by the _OnKeyPressed method. This variable will be passed to the methods subscribed to the OnKeyDown event.

            EventHandler<GlobalKeyEventArgs>? handler = OnKeyDown;
            if (handler == null) //If there's nothing subscribed to the OnKeyDown event, we exit from the method.
                return false;
            handler.Invoke(this, globalKeyEventArgs); //Call the OnKeyDown event and pass on the globalKeyEventArgs as an argument
            return globalKeyEventArgs.Handled; //Now, we return the bool value of the handled variable back to the OnKeyPressed method.
        }

        /// <summary>
        /// This method will call the OnKeyUp event exposed from the library and pass on the information about the key that was pressed.
        /// </summary>
        /// <param name="e">Argument passed by the KeyboardPressed event found in the internal GlobalKeyboardHook class.</param>
        /// <returns>Returns true if the event was handled and false if not.</returns>
        bool KeyUp(GlobalKeyboardHookEventArgs e)
        {

            GlobalKeyEventArgs globalKeyEventArgs = new GlobalKeyEventArgs(e, _alt, _ctrl, _shift);

            EventHandler<GlobalKeyEventArgs>? handler = OnKeyUp;
            if (handler == null)
                return false;
            handler.Invoke(this, globalKeyEventArgs);
            return globalKeyEventArgs.Handled;
        }

    }

    /// <summary>
    /// Contains the information about the keyboard event that has happened. (e.g. Key that was pressed; Which modifier keys were pressed)
    /// </summary>
    internal class GlobalKeyEventArgs : HandledEventArgs
    {
        //Key Modifiers information

        /// <summary>
        /// Value equates to which Alt Modifier Key was pressed. If neither was pressed, value will be set to none.
        /// </summary>
        internal ModifierKeySide Alt { get; private set; }
        /// <summary>
        /// Value equates to which Ctrl Modifier Key was pressed. If neither was pressed, value will be set to none.
        /// </summary>
        internal ModifierKeySide Control { get; private set; }
        /// <summary>
        /// Value equates to which Shift Modifier Key was pressed. If neither was pressed, value will be set to none.
        /// </summary>
        internal ModifierKeySide Shift { get; private set; }

        /// <summary>
        /// Returns the key that was pressed (OnKeyDown), being pressed (OnKeyPressed) or that was let go (OnKeyUp).
        /// </summary>
        internal VirtualKeycodes KeyCode { get; set; }

        /// <summary>
        /// Returns the hardware keycode of the key that was pressed (OnKeyDown), being pressed (OnKeyPressed) or that was let go (OnKeyUp).
        /// </summary>
        internal int HardwareScanCode { get; private set; }

        /// <summary>
        /// Returns true if the key that was pressed (OnKeyDown), being pressed (OnKeyPressed) or that was let go (OnKeyUp) was a Modifier key (Ctrl, Alt, Shift).
        /// </summary>
        internal bool IsModifierKey { get; private set; }

        public override string ToString()
        {
            return "IsModifierKey=" + IsModifierKey + " KeyCode=" + KeyCode + " hardwareScanCode" +
                   HardwareScanCode;
        }

        /// <summary>
        /// The resulting readable character from the Keyboard event. (e.g. Left Shift + 1 = !)
        /// </summary>
        //internal string CharResult { get; private set; }


        //Handle the creation of an instance of this class. Set the values of the properties.
        internal GlobalKeyEventArgs(GlobalKeyboardHookEventArgs e, ModifierKeySide alt, ModifierKeySide ctrl, ModifierKeySide shift)
        {
            Alt = alt;
            Control = ctrl;
            Shift = shift;
            KeyCode = e.KeyboardData.VirtualCode;
            HardwareScanCode = e.KeyboardData.HardwareScanCode;

            //Set the IsModifierKey variable by checking if the KeyCode is a modifier key.
            IsModifierKey = (KeyCode == VirtualKeycodes.LeftShift || KeyCode == VirtualKeycodes.LeftCtrl || KeyCode == VirtualKeycodes.LeftAlt
                    || KeyCode == VirtualKeycodes.RightAlt || KeyCode == VirtualKeycodes.RightCtrl || KeyCode == VirtualKeycodes.RightShift);


        }

        internal GlobalKeyEventArgs(VirtualKeycodes keyCode, int hardScanCode, bool isModifierKey, ModifierKeySide alt, ModifierKeySide ctrl, ModifierKeySide shift)
        {
            Alt = alt;
            Control = ctrl;
            Shift = shift;
            KeyCode = keyCode;
            HardwareScanCode = hardScanCode;
            IsModifierKey = isModifierKey;
        }
    }



}