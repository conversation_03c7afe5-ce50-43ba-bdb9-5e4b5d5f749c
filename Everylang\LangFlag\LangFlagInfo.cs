﻿using Everylang.App.Callback;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.LangInfo;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using Vanara.PInvoke;
using Brush = System.Windows.Media.Brush;
using MousePosition = Everylang.App.Utilities.MousePosition;
using Point = System.Windows.Point;
using Timer = System.Timers.Timer;


namespace Everylang.App.LangFlag
{
    internal class LangFlagInfo : IDisposable
    {
        private bool _timerStarted;
        private bool _nowCaret;
        private bool _nowCaretCheck;
        private bool _nowLangInfoLargeWindowUpdate;
        private bool _nowWheel;
        private bool _nowMouseDown;
        private bool _nowClosed;
        private bool _langFlagShowIconsOld;
        private bool _currentTrayIconIsNative;
        private string? _lastPos;
        private DateTime _lastCaretPosTime;

        private LangInfoWindow? _langInfoWindow;
        private LangInfoLargeWindow? _langInfoLargeWindow;
        private Timer? _timerForUpdateKeyboardLayoutName;
        private readonly Dictionary<string, Brush> _inputLangList;


        internal LangFlagInfo()
        {
            _nowClosed = false;
            _nowCaretCheck = true;
            _nowLangInfoLargeWindowUpdate = false;
            _inputLangList = new Dictionary<string, Brush>();
            _langFlagShowIconsOld = SettingsManager.Settings.LangFlagShowIcons;
            var converter = new BrushConverter();
            List<Brush?> brushes = new List<Brush?>
            {
                converter.ConvertFromString("#007BFF") as Brush,
                converter.ConvertFromString("#DC3545") as Brush,
                converter.ConvertFromString("#28A745") as Brush,
                converter.ConvertFromString("#FFC107") as Brush,
                converter.ConvertFromString("#17A2B8") as Brush,
                converter.ConvertFromString("#00695c") as Brush,
                converter.ConvertFromString("#795548") as Brush,
                converter.ConvertFromString("#6a1b9a") as Brush,
                converter.ConvertFromString("#c62828") as Brush,
            };

            for (var i = 0; i < InputLanguage.InstalledInputLanguages.Count; i++)
            {
                InputLanguage lang = InputLanguage.InstalledInputLanguages[i];
                if (!_inputLangList.ContainsKey(lang.Culture.TwoLetterISOLanguageName.ToUpper()) && brushes.Count > i)
                    _inputLangList.Add(lang.Culture.TwoLetterISOLanguageName.ToUpper(), brushes[i] ?? Brushes.Crimson);
            }

            StartUpdateKeyboardLayoutName();
            HookCallBackMouseMove.CallbackEventHandler += HookManagerMouseMove;
            HookCallBackMouseWheel.CallbackEventHandler += HookManagerMouseWheel;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            HookCallBackMouseUp.CallbackEventHandler += HookManagerMouseUp;
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
        }

        private void HookManagerKeyDown(GlobalKeyEventArgs obj)
        {
            _nowCaretCheck = true;
            _nowLangInfoLargeWindowUpdate = true;
        }

        bool _isGetingInfoForCaret;
        private Point _lastCarretLocation;
        private async void ShowLangInfoForCaret()
        {
            try
            {
                if (_nowClosed)
                {
                    return;
                }

                if (!SettingsManager.Settings.LangFlagShowForCaret) return;
                if (_isGetingInfoForCaret)
                {
                    return;
                }

                if (!_nowCaretCheck)
                {
                    await System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                        (ThreadStart)delegate { ShowLangInfoForCaret((int)_lastCarretLocation.X, (int)_lastCarretLocation.Y); });
                    return;
                }

                _isGetingInfoForCaret = true;
                var location = CarretPosition.GetPosition();
                _lastCarretLocation = location;
                if (Math.Abs(location.X - location.Y) > 0.0001)
                {
                    await System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                        (ThreadStart)delegate { ShowLangInfoForCaret((int)location.X, (int)location.Y); });

                }
                else
                {
                    await System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                        (ThreadStart)HideLangInfoForCaret);

                }
            }
            catch
            {
                // ignored
            }

            _nowCaretCheck = false;
            _isGetingInfoForCaret = false;
        }

        private void StartUpdateKeyboardLayoutName()
        {
            _timerForUpdateKeyboardLayoutName = new Timer(500);
            _timerForUpdateKeyboardLayoutName.Elapsed += (_, _) =>
            {
                if (!_timerStarted) return;
                System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new ThreadStart(
                    () =>
                    {
                        ShowCaps();
                        ShowNumLock();
                    }));

                ShowLangInfoForCaret();
                Thread.Sleep(50);
                System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new ThreadStart(
                    () =>
                    {
                        ShowLangInfoForMouse();
                        ShowLangInfoForLarge();
                    }));

            };
            GlobalEventsApp.EventKeyboardLayoutChangedForLangFlag += UpdateLangInfoWindow;
        }

        private bool _isCapsLockPressed;

        private void ShowCaps()
        {
            if (SettingsManager.Settings.LangFlagIsIndicateCapsLockState)
            {
                if (Control.IsKeyLocked(Keys.CapsLock))
                {
                    if (!_isCapsLockPressed)
                    {
                        _isCapsLockPressed = true;
                        if (_langInfoWindow != null) _langInfoWindow.CapsLockButtonOn.Visibility = Visibility.Visible;
                        if (_langInfoLargeWindow != null) _langInfoLargeWindow.CapsLockButtonOn.Visibility = Visibility.Visible;
                        GlobalEventsApp.OnEventCapsLock(StatusCapsLockButton.CapsLockOn);
                    }
                }
                else if (_isCapsLockPressed)
                {
                    _isCapsLockPressed = false;
                    if (_langInfoWindow != null) _langInfoWindow.CapsLockButtonOn.Visibility = Visibility.Collapsed;
                    if (_langInfoLargeWindow != null) _langInfoLargeWindow.CapsLockButtonOn.Visibility = Visibility.Collapsed;
                    GlobalEventsApp.OnEventCapsLock(StatusCapsLockButton.CapsLockOff);
                }
            }
            else
            {
                if (_isCapsLockPressed)
                {
                    _isCapsLockPressed = false;
                    if (_langInfoWindow != null) _langInfoWindow.CapsLockButtonOn.Visibility = Visibility.Collapsed;
                    if (_langInfoLargeWindow != null) _langInfoLargeWindow.CapsLockButtonOn.Visibility = Visibility.Collapsed;
                    GlobalEventsApp.OnEventCapsLock(StatusCapsLockButton.CapsLockOff);
                }
            }
        }

        private bool _isNumLockPressed;
        private bool _isNumLockShowed;

        private void ShowNumLock()
        {
            if (SettingsManager.Settings.LangFlagIsIndicateNumLockState)
            {

                if (Control.IsKeyLocked(Keys.NumLock))
                {
                    if (!_isNumLockShowed || !_isNumLockPressed)
                    {
                        _isNumLockPressed = true;
                        if (_langInfoLargeWindow != null)
                        {
                            _langInfoLargeWindow.NumLockButtonOn.Visibility = Visibility.Visible;
                            _langInfoLargeWindow.NumLockButtonOff.Visibility = Visibility.Collapsed;
                        }
                        GlobalEventsApp.OnEventNumLock(StatusNumLockButton.NumLockStateOn);
                    }
                }
                else if (!_isNumLockShowed || _isNumLockPressed)
                {
                    _isNumLockPressed = false;
                    if (_langInfoLargeWindow != null)
                    {
                        _langInfoLargeWindow.NumLockButtonOn.Visibility = Visibility.Collapsed;
                        _langInfoLargeWindow.NumLockButtonOff.Visibility = Visibility.Visible;
                    }
                    GlobalEventsApp.OnEventNumLock(StatusNumLockButton.NumLockStateOff);
                }
                _isNumLockShowed = true;
            }
            else
            {
                if (_isNumLockShowed)
                {
                    _isNumLockShowed = false;
                    if (_langInfoLargeWindow != null)
                    {
                        _langInfoLargeWindow.NumLockButtonOn.Visibility = Visibility.Collapsed;
                        _langInfoLargeWindow.NumLockButtonOff.Visibility = Visibility.Collapsed;
                    }
                    GlobalEventsApp.OnEventNumLock(StatusNumLockButton.NumLockOff);
                }
            }
        }

        private void HookManagerMouseWheel(GlobalMouseEventArgs globalMouseEventArgs)
        {
            _nowWheel = true;
            if (!_nowCaret && !_nowClosed && SettingsManager.Settings.LangFlagShowForMouse && _langInfoWindow != null)
            {
                var pos = MousePosition.GetMousePoint();
                try
                {
                    _langInfoWindow.HorizontalOffset = pos.X + SettingsManager.Settings.LangFlagPosMouseX;
                    _langInfoWindow.VerticalOffset = pos.Y + SettingsManager.Settings.LangFlagPosMouseY;
                }
                catch (COMException)
                {

                }
                catch
                {
                    // ignored
                }
                HideLangInfoForCaret();
            }
        }

        private void HookManagerMouseMove(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (_nowMouseDown)
            {
                _nowWheel = true;
                HideLangInfoForCaret();
            }
            if (!_nowCaret && !_nowClosed && SettingsManager.Settings.LangFlagShowForMouse && _langInfoWindow != null)
            {
                var pt = MousePosition.GetMousePoint();
                try
                {
                    _langInfoWindow.HorizontalOffset = pt.X + SettingsManager.Settings.LangFlagPosMouseX;
                    _langInfoWindow.VerticalOffset = pt.Y + SettingsManager.Settings.LangFlagPosMouseY;
                }
                catch (COMException)
                {

                }
                catch
                {
                    // ignored
                }
            }

        }


        private void HookManagerMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            _nowMouseDown = true;
        }

        private void HookManagerMouseUp(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }

            _nowLangInfoLargeWindowUpdate = true;
            _nowCaretCheck = true;
            _nowMouseDown = false;
            _nowWheel = false;
        }


        private void UpdateLangInfoWindow(IntPtr lang)
        {
            try
            {
                if (_nowClosed)
                {
                    return;
                }

                var langName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(lang).ToUpper();
                var englishNameCurrentKeyboardLayout = KeyboardLayoutMethods.GetEnglishNameCurrentKeyboardLayout(lang).ToLower();
                if (SettingsManager.Settings.LangFlagShowInTray)
                {
                    try
                    {
                        if (SettingsManager.Settings.LangFlagShowIcons)
                        {
                            var icon = LangInfoManager.GetFlagIcon(englishNameCurrentKeyboardLayout);
                            if (icon != null) GlobalEventsApp.OnEventLangLayoutChanged(icon);
                        }
                        else
                        {
                            var icon = LangInfoManager.GetTwoLetterIcon(englishNameCurrentKeyboardLayout);
                            if (icon != null) GlobalEventsApp.OnEventLangLayoutChanged(icon);
                        }

                    }
                    catch
                    {
                        // ignored
                    }
                    _currentTrayIconIsNative = false;
                }
                else if (!_currentTrayIconIsNative)
                {
                    GlobalEventsApp.OnEventLangLayoutChanged(null);
                    _currentTrayIconIsNative = true;
                }

                if (SettingsManager.Settings.LangFlagShowForMouse || SettingsManager.Settings.LangFlagShowForCaret)
                {
                    if (SettingsManager.Settings.LangFlagShowIcons)
                    {
                        SetImageInWindow(langName, englishNameCurrentKeyboardLayout);
                    }
                    else
                    {
                        if (_langInfoWindow != null)
                        {
                            _langInfoWindow.textBlock.Text = langName;
                            _langInfoWindow.textBlock.Foreground = _inputLangList[langName];
                            _langInfoWindow.textBlock.Opacity =
                                Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
                            _langInfoWindow.textBlock.Visibility = Visibility.Visible;
                            _langInfoWindow.image.Visibility = Visibility.Hidden;
                        }
                    }
                }
                if (SettingsManager.Settings.LangFlagShowLargeWindow)
                {
                    if (_langInfoLargeWindow != null)
                    {
                        if (SettingsManager.Settings.LangFlagShowIcons)
                        {
                            SetImageInWindow(langName, englishNameCurrentKeyboardLayout);
                        }
                        else
                        {
                            _langInfoLargeWindow.textBlock.Text = langName;
                            _langInfoLargeWindow.textBlock.Foreground = _inputLangList[langName];
                            _langInfoLargeWindow.textBlock.Opacity =
                                Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
                            _langInfoLargeWindow.textBlock.Visibility = Visibility.Visible;
                            _langInfoLargeWindow.image.Visibility = Visibility.Hidden;
                        }
                    }
                }
                LangInfoManager.OldLang = langName;
                _langFlagShowIconsOld = SettingsManager.Settings.LangFlagShowIcons;
            }
            catch
            {
                // ignored
            }
        }

        private void ShowLangInfoForLarge()
        {
            if (SettingsManager.Settings.LangFlagShowLargeWindow && _langInfoLargeWindow != null)
            {
                if (SettingsManager.Settings.IsHideIndicateInFullScreenApp && GlobalLangChangeHook.IsFullScreen)
                {
                    _langInfoLargeWindow.IsOpen = false;
                }
                else
                {
                    if (_langInfoLargeWindow.IsOpen && _nowLangInfoLargeWindowUpdate)
                    {
                        _langInfoLargeWindow.IsOpen = false;
                        _langInfoLargeWindow.IsOpen = true;
                        _nowLangInfoLargeWindowUpdate = false;
                    }

                    if (!_langInfoLargeWindow.IsOpen)
                    {
                        _langInfoLargeWindow.IsOpen = true;
                    }
                }
            }
        }


        private IntPtr _keyboardLayoutCurrentForMouse;
        private void ShowLangInfoForMouse()
        {
            try
            {
                if (!_nowCaret && !_nowClosed)
                {
                    if (MousePosition.IsTextCursor && SettingsManager.Settings.LangFlagShowForMouse)
                    {
                        if (!CheckActiveProcessFileName.CheckLayoutFlagForMouse() || (SettingsManager.Settings.IsHideIndicateInFullScreenApp && GlobalLangChangeHook.IsFullScreen))
                        {
                            if (_langInfoWindow != null) _langInfoWindow.IsOpen = false;
                            return;
                        }

                        if (_langInfoWindow != null)
                        {
                            var point = MousePosition.GetMousePoint();
                            var hwnd = User32.WindowFromPoint(new POINT((int)point.X, (int)point.Y));
                            var keyboardLayout = User32.GetKeyboardLayout(User32.GetWindowThreadProcessId(hwnd, out _)).DangerousGetHandle();
                            _langInfoWindow.IsOpen = true;
                            if (_keyboardLayoutCurrentForMouse != keyboardLayout)
                            {
                                var lang = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(keyboardLayout)
                                    .ToUpper();
                                _keyboardLayoutCurrentForMouse = keyboardLayout;
                                if (SettingsManager.Settings.LangFlagShowIcons)
                                {
                                    SetImageInWindow(lang,
                                        KeyboardLayoutMethods.GetEnglishNameCurrentKeyboardLayout(keyboardLayout));
                                }
                                else
                                {
                                    _langInfoWindow.textBlock.Opacity =
                                        Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
                                    _langInfoWindow.textBlock.Visibility = Visibility.Visible;
                                    _langInfoWindow.image.Visibility = Visibility.Hidden;
                                }

                                LangInfoManager.OldLang = lang;
                            }
                        }
                    }
                    else
                    {
                        if (_langInfoWindow != null) _langInfoWindow.IsOpen = false;
                    }
                }
            }
            catch (COMException)
            {

            }
            catch
            {
                // ignored
            }
        }



        private void ShowLangInfoForCaret(int left, int top)
        {
            try
            {
                if (!CheckActiveProcessFileName.CheckLayoutFlagForCaret() || (SettingsManager.Settings.IsHideIndicateInFullScreenApp && GlobalLangChangeHook.IsFullScreen))
                {
                    _nowCaret = true;
                    if (_langInfoWindow != null) _langInfoWindow.IsOpen = false;
                    return;
                }
                if (_nowClosed) return;
                if (_nowWheel && _lastPos == left + " " + top)
                {
                    HideLangInfoForCaret();
                    return;
                }
                if (_lastPos == left + " " + top && (DateTime.Now - _lastCaretPosTime).TotalSeconds > 60)
                {
                    if (_langInfoWindow != null) _langInfoWindow.IsOpen = false;
                    _nowCaret = true;
                    return;
                }
                if (_lastPos != left + " " + top)
                {
                    _lastCaretPosTime = DateTime.Now;
                }
                _lastPos = left + " " + top;
                VMContainer.Instance.LangFlagSettingsViewModel.FontSizeForCarret = true;
                if (_langInfoWindow != null)
                {
                    _langInfoWindow.IsOpen = true;
                    var pos = MousePosition.GetMousePoint(new Point(left, top));
                    _langInfoWindow.HorizontalOffset = pos.X + SettingsManager.Settings.LangFlagPosCarretX;
                    _langInfoWindow.VerticalOffset = pos.Y + SettingsManager.Settings.LangFlagPosCarretY;
                }

                _nowCaret = true;
            }
            catch (COMException)
            {

            }
            catch
            {
                // ignored
            }
        }

        private void HideLangInfoForCaret()
        {
            try
            {
                if (_nowClosed) return;
                VMContainer.Instance.LangFlagSettingsViewModel.FontSizeForCarret = false;
                _nowCaret = false;
            }
            catch (COMException)
            {

            }
            catch
            {
                // ignored
            }
        }

        internal void Start()
        {
            if (!SettingsManager.LicIsActivated)
            {
                return;
            }
            if (SettingsManager.Settings.LangFlagShowLargeWindow)
            {
                _langInfoLargeWindow = new LangInfoLargeWindow();

                if (Math.Abs(SettingsManager.Settings.LangInfoLargeWindowPosX) < 0.001 || Math.Abs(SettingsManager.Settings.LangInfoLargeWindowPosY) < 0.001)
                {
                    _langInfoLargeWindow.IsOpen = true;
                    var centre = WindowLocation.GetReallyCenterToScreen();
                    _langInfoLargeWindow.HorizontalOffset = centre.X;
                    _langInfoLargeWindow.VerticalOffset = centre.Y;
                }
                else
                {
                    _langInfoLargeWindow.IsOpen = true;
                    _langInfoLargeWindow.HorizontalOffset = SettingsManager.Settings.LangInfoLargeWindowPosX;
                    _langInfoLargeWindow.VerticalOffset = SettingsManager.Settings.LangInfoLargeWindowPosY;
                }
            }

            _langInfoWindow = new LangInfoWindow();
            _nowClosed = false;
            LangInfoManager.OldLang = "";
            UpdateLangInfoWindow(KeyboardLayoutCommon.CurrentKeyboardLayout);
            if (!_timerStarted)
            {
                _timerForUpdateKeyboardLayoutName?.Start();
                _timerStarted = true;
            }
        }

        internal void Stop()
        {
            if (_langInfoWindow != null) _langInfoWindow.IsOpen = false;
            _langInfoWindow = null;
            if (_timerStarted)
            {
                _timerForUpdateKeyboardLayoutName?.Stop();
                _timerForUpdateKeyboardLayoutName?.Close();
                _timerStarted = false;
            }
            _nowCaret = false;
            _nowWheel = false;
            _langFlagShowIconsOld = false;
            _currentTrayIconIsNative = false;
            _lastPos = "";
            _nowClosed = true;
            if (_langInfoLargeWindow != null)
            {
                _langInfoLargeWindow.IsOpen = false;
                _langInfoLargeWindow = null;
            }
        }

        internal void Restart()
        {
            if (!_nowClosed)
            {
                Stop();
            }
            Start();
        }

        internal void Kill()
        {
            _nowClosed = true;
            Stop();

        }

        private void SetImageInWindow(string langIsoName, string langEnglishName)
        {
            if (LangInfoManager.OldLang == langIsoName && _langFlagShowIconsOld == SettingsManager.Settings.LangFlagShowIcons)
            {
                return;
            }
            var assembly = Assembly.GetExecutingAssembly();
            var manifestResourceNames = assembly.GetManifestResourceNames();

            using (var file = assembly.GetManifestResourceStream(manifestResourceNames.FirstOrDefault(x => x.Contains($"FlagsImages.{langEnglishName.ToLower()}.png")) ?? string.Empty))
            {
                if (file != null)
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.StreamSource = file;
                    bitmap.EndInit();
                    bitmap.Freeze();
                    if (SettingsManager.Settings.LangFlagShowForMouse || SettingsManager.Settings.LangFlagShowForCaret)
                    {
                        if (_langInfoWindow != null)
                        {
                            _langInfoWindow.textBlock.Visibility = Visibility.Hidden;
                            _langInfoWindow.image.Visibility = Visibility.Visible;
                            _langInfoWindow.image.Source = bitmap;
                            _langInfoWindow.image.Opacity =
                                Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
                            _langInfoWindow.UpdateLayout();
                        }
                    }

                    if (SettingsManager.Settings.LangFlagShowLargeWindow && _langInfoLargeWindow != null)
                    {
                        _langInfoLargeWindow.textBlock.Visibility = Visibility.Hidden;
                        _langInfoLargeWindow.image.Visibility = Visibility.Visible;
                        _langInfoLargeWindow.image.Source = bitmap;
                        _langInfoLargeWindow.image.Opacity = Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
                        _langInfoLargeWindow.UpdateLayout();
                    }
                }
                else
                {
                    if (SettingsManager.Settings.LangFlagShowForMouse || SettingsManager.Settings.LangFlagShowForCaret)
                    {
                        if (_langInfoWindow != null)
                        {
                            _langInfoWindow.textBlock.Visibility = Visibility.Visible;
                            _langInfoWindow.image.Visibility = Visibility.Hidden;
                        }
                    }
                    if (SettingsManager.Settings.LangFlagShowLargeWindow && _langInfoLargeWindow != null)
                    {
                        _langInfoLargeWindow.textBlock.Visibility = Visibility.Visible;
                        _langInfoLargeWindow.image.Visibility = Visibility.Hidden;
                    }
                }
            }
            GC.Collect();
        }





        public void Dispose()
        {
            Kill();
        }
    }
}
