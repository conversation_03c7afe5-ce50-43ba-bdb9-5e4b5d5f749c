﻿using Everylang.App.SettingsApp;
using System;
using System.Drawing;

namespace Everylang.App.OCR
{
    class ScreenshotTaker
    {
        internal event Action<Bitmap>? GetImageAction;

        private static bool _isShow;

        internal void OpenScreen()
        {
            if (!SettingsManager.LicIsActivated)
            {
                return;
            }
            if (_isShow)
            {
                return;
            }
            _isShow = true;

            var screenshotTool = new OcrTool.ScreenshotTool();
            screenshotTool.BitmapRegionAction += BitmapRegionAction;
            screenshotTool.ScreenshotWindowClosedAction += () =>
            {
                _isShow = false;
            };
            screenshotTool.Capture();
        }

        private void BitmapRegionAction(Bitmap? bitmap)
        {
            if (bitmap != null) GetImageAction?.Invoke(bitmap);
        }
    }
}
