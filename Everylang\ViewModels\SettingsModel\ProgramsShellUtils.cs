﻿using Everylang.App.Utilities;
using Everylang.Common.LogManager;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;

namespace Everylang.App.ViewModels.SettingsModel
{
    internal class ProgramsShellUtils
    {
        internal static IEnumerable<string> GetDirectoryFiles(string rootPath, string patternMatch, SearchOption searchOption)
        {
            IEnumerable<string?> foundFiles = Enumerable.Empty<string>();

            if (searchOption == SearchOption.AllDirectories)
            {
                try
                {
                    IEnumerable<string> subDirs = Directory.EnumerateDirectories(rootPath);
                    foreach (string dir in subDirs)
                    {
                        foundFiles = foundFiles.Concat(GetDirectoryFiles(dir, patternMatch, searchOption)); // Add files in subdirectories recursively to the list
                    }
                }
                catch (UnauthorizedAccessException) { }
                catch (PathTooLongException) { }
            }

            try
            {
                foundFiles = foundFiles.Concat(Directory.EnumerateFiles(rootPath, patternMatch)); // Add files from the current directory
            }
            catch (UnauthorizedAccessException) { }

            return foundFiles;
        }

        internal static Task<List<ProgramInfo>> GetAllStartedProgramsAsync()
        {
            return Task.Run(async () =>
            {
                var programsList = new List<ProgramInfo>();
                var withoutIconProgramsList = new List<ProgramInfo>();
                try
                {
                    var processes = new List<Process>(Process.GetProcesses());
                    foreach (var process in processes)
                    {
                        try
                        {
                            var program = CheckActiveProcessFileName.InnerGetActiveProcessName((uint)process.Id);
                            if (string.IsNullOrEmpty(program) || program.Trim() == "?" || program.StartsWith("?"))
                            {
                                continue;
                            }

                            if (program.ToLower() != @"c:\windows\explorer.exe" &&
                                programsList.All(x => x.Name != program) &&
                                withoutIconProgramsList.All(x => x.Name != program))
                            {
                                try
                                {
                                    var programException = new ProgramInfo();
                                    programException.Name = program;
                                    await Application.Current.Dispatcher.BeginInvoke(() =>
                                    {
                                        programException.Icon = Microsoft.WindowsAPICodePack.Shell.ShellFile.FromFilePath(program).Thumbnail?.BitmapSource;
                                    });

                                    if (programException.Icon == null)
                                    {
                                        withoutIconProgramsList.Add(programException);
                                    }
                                    else
                                    {
                                        programsList.Add(programException);
                                    }
                                }
                                catch
                                {
                                    // Ignored
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            Logger.LogTo.Error(e, e.Message);
                        }
                    }

                    programsList = programsList.OrderBy(x => x.Name).ToList();
                    withoutIconProgramsList = withoutIconProgramsList.OrderBy(x => x.Name).ToList();
                    foreach (var programException in withoutIconProgramsList)
                    {
                        programsList.Add(programException);
                    }
                }
                catch
                {
                    // Ignored
                }
                return programsList;
            });
        }
    }


    public class ProgramInfo
    {
        public string? Name { get; set; }
        public ImageSource? Icon { get; set; }
    }
}
