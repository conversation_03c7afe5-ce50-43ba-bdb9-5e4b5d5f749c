﻿using Everylang.Note.NoteDataStore;
using System;

namespace Everylang.Note.Callbacks
{
    public class CallBack
    {
        // public static event Action MiminoteCallBackShowAll;
        public static event Action MiminoteCallBackAddNewNote;
        public static event Action<NoteDataModel> MiminoteCallBackCloseNote;
        public static event Action<NoteDataModel> MiminoteCallBackToArchiveNote;
        public static event Action MiminoteCallBackOpenNotesList;
        public static event Action MiminoteCallBackOpenSettings;
        public static event Action MiminoteCallBackTransparencyForNotesChange;

        // public static void OnMiminoteCallBackShowAll()
        // {
        //     MiminoteCallBackShowAll?.Invoke();
        // }

        public static void OnMiminoteCallBackAddNewNote()
        {
            MiminoteCallBackAddNewNote?.Invoke();
        }

        public static void OnMiminoteCallBackCloseNote(NoteDataModel obj)
        {
            MiminoteCallBackCloseNote?.Invoke(obj);
        }

        public static void OnMiminoteCallBackToArchiveNote(NoteDataModel obj)
        {
            MiminoteCallBackToArchiveNote?.Invoke(obj);
        }

        public static void OnMiminoteCallBackOpenNotesList()
        {
            MiminoteCallBackOpenNotesList?.Invoke();
        }

        public static void OnMiminoteCallBackOpenSettings()
        {
            MiminoteCallBackOpenSettings?.Invoke();
        }

        public static void OnMiminoteCallBackTransparencyForNotesChange()
        {
            MiminoteCallBackTransparencyForNotesChange?.Invoke();
        }
    }
}
