﻿using Everylang.App.Clipboard;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.Common.Utilities;
using System.Threading;
using WindowsInput;

namespace Everylang.App.Converter
{
    class ConverterCaps
    {
        internal static void ConvertCapsDown()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                var resultText = "";
                foreach (var t in text)
                {
                    if (!char.IsLetter(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsWhiteSpace(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsLower(t))
                    {
                        resultText += t;
                    }
                    if (char.IsUpper(t))
                    {
                        resultText += char.ToLower(t);
                    }
                }
                SendText.SendStringByPaste(resultText, false);
            }
        }

        internal static void ConvertCapsUp()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                var resultText = "";
                foreach (var t in text)
                {
                    if (!char.IsLetter(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsWhiteSpace(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsUpper(t))
                    {
                        resultText += t;
                    }
                    if (char.IsLower(t))
                    {
                        resultText += char.ToUpper(t);
                    }
                }
                SendText.SendStringByPaste(resultText, false);
            }
        }

        internal static void ConvertCaps()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                var resultText = "";
                foreach (var t in text)
                {
                    if (!char.IsLetter(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsWhiteSpace(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsUpper(t))
                    {
                        resultText += char.ToLower(t);
                    }
                    if (char.IsLower(t))
                    {
                        resultText += char.ToUpper(t);
                    }
                }
                SendText.SendStringByPaste(resultText, false);
            }
        }

        internal static bool CallIsRunConvertFirstLetterToDown;

        internal static void ConvertFirstLetterToDown()
        {
            CallIsRunConvertFirstLetterToDown = true;
            KeyboardState.ReleaseAllKeys();
            Thread.Sleep(50);
            var sim = new InputSimulator();
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.LEFT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT);
            sim.Keyboard.KeyPress(VirtualKeyCode.RIGHT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT);
            Thread.Sleep(50);
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                var resultText = "";
                foreach (var t in text)
                {
                    if (!char.IsLetter(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsWhiteSpace(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsLower(t))
                    {
                        resultText += t;
                    }
                    if (char.IsUpper(t))
                    {
                        resultText += char.ToLower(t);
                    }
                }
                sim.Keyboard.TextEntry(resultText);
                Thread.Sleep(50);
            }
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.RIGHT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            CallIsRunConvertFirstLetterToDown = false;
        }

        internal static bool CallIsRunConvertFirstLetterToUp;
        internal static void ConvertFirstLetterToUp()
        {
            CallIsRunConvertFirstLetterToUp = true;
            KeyboardState.ReleaseAllKeys();
            Thread.Sleep(50);
            var sim = new InputSimulator();
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.LEFT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT);
            sim.Keyboard.KeyPress(VirtualKeyCode.RIGHT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT);
            Thread.Sleep(50);
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                var resultText = "";
                foreach (var t in text)
                {
                    if (!char.IsLetter(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsWhiteSpace(t))
                    {
                        resultText += t;
                        continue;
                    }
                    if (char.IsUpper(t))
                    {
                        resultText += t;
                    }
                    if (char.IsLower(t))
                    {
                        resultText += char.ToUpper(t);
                    }
                }
                sim.Keyboard.TextEntry(resultText);
                Thread.Sleep(50);
            }
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.RIGHT);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            CallIsRunConvertFirstLetterToUp = false;
        }
    }
}
