﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.SmartClick;
using System;
using System.Collections.ObjectModel;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class UniversalWindowSettingsViewModel : ViewModelBase
    {
        public ObservableCollection<string> SearchServices { get; private set; }

        public UniversalWindowSettingsViewModel()
        {
            SearchServices = new ObservableCollection<string> { "Google", "Yandex", "Bing", "DuckDuckGo" };
        }

        public string SearchService
        {
            get => SearchServices[SettingsManager.Settings.SmartClickSearchService];
            set => SettingsManager.Settings.SmartClickSearchService = SearchServices.IndexOf(value);
        }

        public bool ShowOnDoubleMiddle
        {
            get
            {
                return SettingsManager.Settings.SmartClickShowOnDoubleMiddle;
            }
            set
            {
                SettingsManager.Settings.SmartClickShowOnDoubleMiddle = value;
                base.OnPropertyChanged();
            }
        }

        public bool ShowOnPressLeftAndRightMouseButtons
        {
            get
            {
                return SettingsManager.Settings.SmartClickShowOnPressLeftAndRightMouseButtons;
            }
            set
            {
                SettingsManager.Settings.SmartClickShowOnPressLeftAndRightMouseButtons = value;
                base.OnPropertyChanged();
            }
        }

        public bool ShowOnPressHotKeys
        {
            get
            {
                return SettingsManager.Settings.SmartClickShowOnPressHotKeys;
            }
            set
            {
                SettingsManager.Settings.SmartClickShowOnPressHotKeys = value;
                SmartClickManager.Instance.RestartWatcher();
                base.OnPropertyChanged();
            }
        }

        public string Shortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SmartClickShortcut);
            }
            set
            {
                SettingsManager.Settings.SmartClickShortcut = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(UniversalWindowIsOn));
            }
        }

        public bool UniversalWindowIsOn
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.SmartClickIsOn;
            }
            set
            {
                SettingsManager.Settings.SmartClickIsOn = value;
                if (value)
                {
                    SmartClickManager.Instance.StartWatcher();
                }
                else
                {
                    SmartClickManager.Instance.StopWatcher();
                }
                base.OnPropertyChanged();
            }
        }

        public bool SmartClickMiniIsOn
        {
            get
            {
                return SettingsManager.Settings.SmartClickMiniIsOn;
            }
            set
            {
                SettingsManager.Settings.SmartClickMiniIsOn = value;
                base.OnPropertyChanged();
            }
        }

        public double SmartClickMiniPosY
        {
            get
            {
                return SettingsManager.Settings.SmartClickMiniPosY;
            }
            set
            {
                SettingsManager.Settings.SmartClickMiniPosY = value;
                base.OnPropertyChanged();
            }
        }

        public double SmartClickMiniPosX
        {
            get
            {
                return SettingsManager.Settings.SmartClickMiniPosX;
            }
            set
            {
                SettingsManager.Settings.SmartClickMiniPosX = value;
                base.OnPropertyChanged();
            }
        }

        public int SmartClickMiniSizeIcon
        {
            get
            {
                return SettingsManager.Settings.SmartClickMiniSizeIcon;
            }
            set
            {
                SettingsManager.Settings.SmartClickMiniSizeIcon = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(SmartClickMiniSizeCommon));
                base.OnPropertyChanged(nameof(SmartClickMiniSizeAll));
            }
        }

        public double SmartClickMiniSizeCommon => Convert.ToDouble(SmartClickMiniSizeIcon) + 6;

        public double SmartClickMiniSizeAll => Convert.ToDouble(SmartClickMiniSizeIcon) + 3;
    }
}
