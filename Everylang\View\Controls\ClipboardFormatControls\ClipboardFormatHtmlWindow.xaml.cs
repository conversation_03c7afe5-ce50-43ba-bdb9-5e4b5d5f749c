﻿using Everylang.App.Utilities;
using System;
using System.Diagnostics;
using System.Threading;
using System.Windows;
using System.Windows.Navigation;
using System.Windows.Threading;

namespace Everylang.App.View.Controls.ClipboardFormatControls
{
    /// <summary>
    /// Interaction logic for ClipboardFormatHtmlWindow.xaml
    /// </summary>
    internal partial class ClipboardFormatHtmlWindow
    {
        internal static readonly DependencyProperty LinkTextProperty =
            DependencyProperty.Register("LinkText",
                typeof(string),
                typeof(ClipboardFormatHtmlWindow),
                new FrameworkPropertyMetadata(""));

        internal string LinkText
        {
            get { return (string)GetValue(LinkTextProperty); }
            set { SetValue(LinkTextProperty, value); }
        }

        internal ClipboardFormatHtmlWindow(string? text)
        {
            InitializeComponent();
            if (!string.IsNullOrEmpty(text))
            {
                string link = "";
                string html = "";
                var indexHtml = text.IndexOf("<html>", StringComparison.CurrentCulture);
                if (indexHtml != -1)
                {
                    var textForDel = text.Substring(0, indexHtml);
                    var indexUrl = textForDel.IndexOf("SourceURL:", StringComparison.CurrentCulture);
                    if (indexUrl != -1)
                    {
                        var textUrl = text.Substring(0, indexUrl);
                        var newText = textForDel.Replace(textUrl, "");
                        link = newText.Replace("SourceURL:", "").Trim();
                    }
                    html = text.Replace(textForDel, "");
                }
                if (!string.IsNullOrEmpty(link))
                {
                    LinkText = link.Length > 60 ? link.Substring(0, 60) + "......" : link;
                    hyperlink.NavigateUri = new Uri(link);
                }
                if (!string.IsNullOrEmpty(html))
                {
                    html = @"<head><meta http-equiv='Content-Type' content='text/html;charset=UTF-8'></head>" + Environment.NewLine + html;
                    ThemeVariation variation = AccentColorSet.Variation;
                    if (variation == ThemeVariation.Dark)
                    {
                        webBrowser.NavigateToString(html.Replace("<body", "<body bgcolor=#000000"));
                    }
                    if (variation == ThemeVariation.Light)
                    {
                        webBrowser.NavigateToString(html.Replace("<body", "<body bgcolor=#FFFFFF"));
                    }
                }
                if (string.IsNullOrEmpty(link) && string.IsNullOrEmpty(html))
                {
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
                    Application curApp = Application.Current;
                    if (curApp.MainWindow != null) curApp.MainWindow.Activate();
                }

            }

        }

        private void me_Deactivated(object sender, EventArgs e)
        {
            if (IsVisible)
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
            Application curApp = Application.Current;
            if (curApp.MainWindow != null) curApp.MainWindow.Activate();
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            Process.Start(new ProcessStartInfo(e.Uri.AbsoluteUri));
            e.Handled = true;
        }

        private void webBrowser_Loaded(object sender, RoutedEventArgs e)
        {
            if (Application.Current.MainWindow != null)
            {
                var pos = WindowLocation.GetAbsolutePosition(Application.Current.MainWindow);
                this.Left = (Application.Current.MainWindow.ActualWidth - this.ActualWidth) / 2 + pos.X;
                this.Top = (Application.Current.MainWindow.ActualHeight - this.ActualHeight) / 2 + pos.Y;
            }

            if (this.Top < 1)
            {
                this.Top = 5;
            }
        }
    }
}
