﻿using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace Everylang.App.View.Controls.Snippets
{
    /// <summary>
    /// Interaction logic for SnippetsMiniWindow.xaml
    /// </summary>
    internal partial class SnippetsMiniWindow : Window
    {
        internal SnippetsMiniWindow(string text)
        {
            InitializeComponent();
            TextBlock.Text = text;
        }

        static async Task PutTaskDelay()
        {
            await Task.Delay(2000);
        }

        internal async void ShowHide()
        {
            this.Width = TextBlock.ActualWidth + 5;
            this.Height = TextBlock.ActualHeight + 3;
            if (WindowStartupLocation == WindowStartupLocation.CenterScreen)
            {
                Left -= Width / 2;
            }
            await PutTaskDelay();
            await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);

        }
    }
}
