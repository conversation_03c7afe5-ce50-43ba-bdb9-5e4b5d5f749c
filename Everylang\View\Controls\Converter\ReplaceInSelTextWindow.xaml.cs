﻿using Everylang.App.Clipboard;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Common.RichTextBoxEx;
using Everylang.Common.Utilities;
using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Converter
{
    /// <summary>
    /// Interaction logic for ReplaceInSelTextWindow.xaml
    /// </summary>
    internal partial class ReplaceInSelTextWindow
    {
        private readonly string? _text;

        internal ReplaceInSelTextWindow(string? text)
        {
            InitializeComponent();
            ForegroundWindow.StoreForegroundWindow();
            _text = text;
        }

        private void ReplaceInSelTextWindow_OnLoaded(object sender, RoutedEventArgs e)
        {
            RichTextBoxMu.Text = _text;
        }


        private void RichTextBoxTextChanged(object sender, TextChangedEventArgs e)
        {
            if (RichTextBoxMu.Text == "")
            {
                return;
            }

            var formattedText = RichTextBoxMu.Document.GetFormattedText(this);
            if (formattedText != null && formattedText.WidthIncludingTrailingWhitespace + 20 < MaxWidth)
            {
                this.Width = formattedText.WidthIncludingTrailingWhitespace + 20;
                this.Height = formattedText.Height + 100;
            }
            else
            {
                this.Width += 250;
                UpdateLayout();
                this.Height += (this.RichTextBoxMu.ExtentHeight - this.RichTextBoxMu.ViewportHeight) + 10;
                UpdateLayout();
                if (this.RichTextBoxMu.ExtentHeight > this.RichTextBoxMu.ViewportHeight)
                {
                    this.Width = MaxWidth;
                    UpdateLayout();
                    this.Height += (this.RichTextBoxMu.ExtentHeight - this.RichTextBoxMu.ViewportHeight) + 10;
                }
            }
        }


        private void ReplaceClick(object sender, RoutedEventArgs e)
        {
            if (RichTextBoxMu.Text != null)
            {
                var newText = Regex.Replace(RichTextBoxMu.Text, TextBoxFind.Text.Replace("\\", "\\\\"), TextBoxReplace.Text);
                RichTextBoxMu.Text = newText;
            }

            FindText(TextBoxReplace.Text, true);
        }

        private void FindText(string textForFind, bool isReplace)
        {
            TextRange textRange = new TextRange(RichTextBoxMu.Document.ContentStart, RichTextBoxMu.Document.ContentEnd);
            textRange.ClearAllProperties();
            //get the richtextbox text
            string textBoxText = textRange.Text;
            //get search text
            string searchText = textForFind;
            if (string.IsNullOrWhiteSpace(textBoxText) || string.IsNullOrWhiteSpace(searchText))
            {
                return;
            }
            //using regex to get the search count
            //this will include search word even it is part of another word
            //say we are searching "hi" in "hi, how are you Mahi?" --> match count will be 2 (hi in 'Mahi' also)
            Regex regex = new Regex(searchText.Replace("\\", "\\\\"));
            int countMatchFound = Regex.Matches(textBoxText, regex.ToString()).Count;
            for (TextPointer? startPointer = RichTextBoxMu.Document.ContentStart;
                startPointer != null && startPointer.CompareTo(RichTextBoxMu.Document.ContentEnd) <= 0;
                startPointer = startPointer?.GetNextContextPosition(LogicalDirection.Forward))
            {
                //check if end of text
                if (startPointer.CompareTo(RichTextBoxMu.Document.ContentEnd) == 0)
                {
                    break;
                }
                //get the adjacent string
                string parsedString = startPointer.GetTextInRun(LogicalDirection.Forward);
                //check if the search string present here
                int indexOfParseString = parsedString.IndexOf(searchText, StringComparison.Ordinal);
                if (indexOfParseString >= 0) //present
                {
                    //setting up the pointer here at this matched index
                    startPointer = startPointer.GetPositionAtOffset(indexOfParseString);
                    if (startPointer != null)
                    {
                        //next pointer will be the length of the search string
                        TextPointer nextPointer = startPointer.GetPositionAtOffset(searchText.Length)!;
                        //create the text range
                        TextRange searchedTextRange = new TextRange(startPointer, nextPointer);
                        //color up 
                        searchedTextRange.ApplyPropertyValue(TextElement.BackgroundProperty, new SolidColorBrush(Colors.CornflowerBlue));
                        //add other setting property
                    }
                }
            }
            //update the label text with count
            if (countMatchFound > 0)
            {
                if (isReplace)
                {
                    TextBlockStatus.Text = LocalizationManager.GetString("TotalMatchFoundReplace") + " " + countMatchFound;
                }
                else
                {
                    TextBlockStatus.Text = LocalizationManager.GetString("TotalMatchFound") + " " + countMatchFound;
                }

            }
            else
            {
                TextBlockStatus.Text = LocalizationManager.GetString("NoMatchFound");
            }
        }

        private void CloseClick(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void PasteClick(object sender, RoutedEventArgs e)
        {
            Close();
            ForegroundWindow.RestoreForegroundWindow();
            SendText.SendStringByPaste(RichTextBoxMu.Text, false);
        }

        private void CopyClick(object sender, RoutedEventArgs e)
        {
            ClipboardOperations.SetText(RichTextBoxMu.Text);
        }

        private void CancelClick(object sender, RoutedEventArgs routedEventArgs)
        {
            RichTextBoxMu.Text = _text;
        }

        private void TextBoxFind_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            FindText(TextBoxFind.Text, false);
        }


    }
}
