﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Translation
{
    /// <summary>
    /// Interaction logic for TranslationControl.xaml
    /// </summary>
    public partial class TranslationControl
    {
        public TranslationControl()
        {
            InitializeComponent();
        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("TransSettingsKeyboardShortcuts"), SettingsManager.Settings.TranslateShowMiniFormShortcut, nameof(SettingsManager.Settings.TranslateShowMiniFormShortcut), TranslateManager.Instance.PressedFloating);

            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.TranslationSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }
    }
}
