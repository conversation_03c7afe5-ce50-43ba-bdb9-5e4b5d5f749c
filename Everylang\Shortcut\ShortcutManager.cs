﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Common;
using NHotkey;
using NHotkey.Wpf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Input;
using Telerik.Windows.Controls;

namespace Everylang.App.Shortcut
{
    internal static class ShortcutManager
    {
        static ShortcutManager()
        {
            _handlerDataList = new List<HandlerData>();
            HotkeyManager.HotkeyAlreadyRegistered += HotkeyManagerHotkeyAlreadyRegistered;
            DoubleKeyDownManager.Start();
            MouseXKeyManager.Start();
        }

        private static string _name;
        private static string _shortcut;
        private static List<HandlerData> _handlerDataList;
        internal static HotKeyControl OpenedHotKeyControl;
        private static bool _unRegistered;

        internal static void UnRegisterAll()
        {
            foreach (var handlerData in _handlerDataList)
            {
                if (nameof(SettingsManager.Settings.StopWorkingShortcut) != handlerData.NameEvent)
                {
                    HotkeyManager.Current.Remove(handlerData.NameEvent);
                    DoubleKeyDownManager.Remove(handlerData.NameEvent);
                    MouseXKeyManager.Remove(handlerData.NameEvent);
                }

            }

            _unRegistered = true;
        }

        internal static void RegisterAll()
        {
            if (_unRegistered)
            {
                var handlerDataList = new List<HandlerData>(_handlerDataList);
                _handlerDataList = new List<HandlerData>();
                foreach (var handlerData in handlerDataList)
                {
                    RegisterShortcut(handlerData.NameEvent, handlerData.Shortcut, handlerData.Handler);
                }

                _unRegistered = false;
            }

        }

        private static async void GlobalEventHandler(object sender, HotkeyEventArgs hotkeyEventArgs)
        {
            if (!CheckActiveProcessFileName.CheckHotKeys())
            {
                return;
            }
            await Task.Delay(30);
            var eventHandler = _handlerDataList.FirstOrDefault(x => x.NameEvent == hotkeyEventArgs.Name);
            if (eventHandler != null)
            {
                eventHandler.Handler.Invoke(sender, hotkeyEventArgs);
            }
        }

        private static void HotkeyManagerHotkeyAlreadyRegistered(object sender, HotkeyAlreadyRegisteredEventArgs e)
        {
            RemoveShortcut(_name);
            GlobalEventsApp.OnEventErrorRegisterHotkey(_shortcut);
        }

        internal static bool CheckAlreadyRegistered(string shortcut)
        {
            return _handlerDataList.FirstOrDefault(x =>
                       x.Shortcut.Equals(shortcut, StringComparison.InvariantCultureIgnoreCase)) != null;
        }

        internal static bool RegisterAutoGeneratedShortcutHotkey(string name, string shortcut, EventHandler<HotkeyEventArgs> handler)
        {
            _name = name;
            _shortcut = shortcut;
            var keys = ParseShortcut(shortcut);
            try
            {
                _handlerDataList.Add(new HandlerData() { Handler = handler, NameEvent = name, Shortcut = keys.ToString() });
                HotkeyManager.Current.AddOrReplace(name, keys.Key, keys.ModifierKeys, GlobalEventHandler);
            }
            catch (Exception e)
            {
                return false;
            }
            return true;
        }

        internal static bool RegisterShortcut(string name, string shortcut, EventHandler<HotkeyEventArgs> handler)
        {
            if (string.IsNullOrEmpty(shortcut))
            {
                return true;
            }
            if (shortcut.Contains("mouse"))
            {
                _handlerDataList.Add(new HandlerData() { Handler = new EventHandler<HotkeyEventArgs>(handler), NameEvent = name, Shortcut = shortcut });
                MouseXKeyManager.AddNew(name, shortcut, handler);
            }
            if (shortcut.StartsWith("double"))
            {
                _handlerDataList.Add(new HandlerData() { Handler = new EventHandler<HotkeyEventArgs>(handler), NameEvent = name, Shortcut = shortcut });
                DoubleKeyDownManager.AddNew(name, shortcut, handler);
            }
            else
            {
                _name = name;
                shortcut = GetKeysFromShortcut(shortcut);
                if (string.IsNullOrEmpty(shortcut))
                {
                    return true;
                }
                _shortcut = shortcut;
                var keys = ParseShortcut(shortcut);
                try
                {

                    var handler1 = new EventHandler<HotkeyEventArgs>(handler);
                    _handlerDataList.Add(new HandlerData() { Handler = handler1, NameEvent = name, Shortcut = keys.ToString() });
                    HotkeyManager.Current.AddOrReplace(name, keys.Key, keys.ModifierKeys, GlobalEventHandler);
                }
                catch (Exception e)
                {
                    return false;
                }
                return true;
            }
            return true;
        }

        internal static bool RegisterShortcut(string name, HotKey? shortcut, EventHandler<HotkeyEventArgs> handler)
        {
            _name = name;
            try
            {
                _handlerDataList.Add(new HandlerData() { Handler = handler, NameEvent = name, Shortcut = shortcut.ToString() });
                HotkeyManager.Current.AddOrReplace(name, shortcut.Key, shortcut.ModifierKeys, GlobalEventHandler);
            }
            catch (Exception e)
            {
                return false;
            }
            return true;
        }

        internal static void RemoveShortcut(string name)
        {
            if (_handlerDataList.FirstOrDefault(x => x.NameEvent == name) != null)
            {
                _handlerDataList.RemoveAll(x => x.NameEvent == name);
                HotkeyManager.Current.Remove(name);
                DoubleKeyDownManager.Remove(name);
                MouseXKeyManager.Remove(name);
            }

        }

        private static HotKey? ParseShortcut(string text)
        {
            try
            {
                text = text.Split('|')[0];
                if (string.IsNullOrEmpty(text))
                {
                    return null;
                }
                text = text.Replace("double", "").Replace("shortcut", "").Trim();
                ModifierKeys mods = ModifierKeys.None;
                if (!text.Contains("+"))
                {
                    text = GetKeyFromChar(text);
                    return new HotKey((Key)Enum.Parse(typeof(Key), text.Replace(" ", ""), true));
                }
                var rx = new Regex(
                    @"(?:(?:(?<win>win|windows)|(?<ctrl>ctrl|control|ctl)|(?<alt>alt)|(?<shift>shift))\s*(?:[\|+-])\s*)+(?<key>.*)",
                    RegexOptions.IgnoreCase);

                Match m = rx.Match(text);
                if (m.Groups["win"].Success)
                    mods |= ModifierKeys.Windows;
                if (m.Groups["ctrl"].Success)
                    mods |= ModifierKeys.Control;
                if (m.Groups["alt"].Success)
                    mods |= ModifierKeys.Alt;
                if (m.Groups["shift"].Success)
                    mods |= ModifierKeys.Shift;
                var tKey = GetKeyFromChar(m.Groups["key"].Value).Replace(" ", "").Replace("ScrollLock", "Scroll");
                return new HotKey((Key)Enum.Parse(typeof(Key), tKey, true), mods);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return new HotKey(Key.None);
        }

        private static string GetKeyFromChar(string text)
        {
            if (text.Length == 1)
            {
                text = KeyboardLayoutMethods.ConvertCharToVirtualKey(text[0]).ToString().Replace(", Shift", "");
            }

            text = text.Replace("Break", "Pause");
            text = text.Replace("Num Lock", "NumLock");
            text = text.Replace("~", "OemTilde");
            text = text.Replace("`", "OemTilde");
            if (text.Length == 1)
            {
                text = text.Replace("0", "D0");
                text = text.Replace("1", "D1");
                text = text.Replace("2", "D2");
                text = text.Replace("3", "D3");
                text = text.Replace("4", "D4");
                text = text.Replace("5", "D5");
                text = text.Replace("6", "D6");
                text = text.Replace("7", "D7");
                text = text.Replace("8", "D8");
                text = text.Replace("9", "D9");
            }
            return text;
        }

        internal static HotKey? GetHotKeyFromText(string keys)
        {
            return ParseShortcut(keys);
        }

        internal static string GetCharFromKey(string keys)
        {
            if (string.IsNullOrEmpty(keys))
            {
                return LocalizationManager.GetString("HotKeyIsOff");
            }
            var mouseKey = "";
            if (keys.Contains("mouse"))
            {
                mouseKey = GetMouseKeyFromShortcut(keys);
            }
            if (keys.StartsWith("double"))
            {
                if (string.IsNullOrEmpty(mouseKey))
                {
                    return LocalizationManager.GetString("HotKeyDoubleKeyDown") + " " + DoubleKeyDownManager.GetKeyData(keys).Name;
                }
                else
                {
                    return LocalizationManager.GetString("HotKeyDoubleKeyDown") + " " + DoubleKeyDownManager.GetKeyData(keys).Name +
                           " | " + LocalizationManager.GetString("HotKeyUseMouseXKey") + " " + mouseKey;
                }

            }
            var hotKey = ParseShortcut(GetKeysFromShortcut(keys));
            if (hotKey != null)
            {
                if (string.IsNullOrEmpty(mouseKey))
                {
                    return LocalizationManager.GetString("HotKeyShortcut") + " " + hotKey.ToString().Replace("`", "~");
                }
                else
                {
                    return LocalizationManager.GetString("HotKeyShortcut") + " " + hotKey.ToString().Replace("`", "~") +
                           " | " + LocalizationManager.GetString("HotKeyUseMouseXKey") + " " + mouseKey;
                }

            }
            if (keys.Contains("mouse"))
            {
                return LocalizationManager.GetString("HotKeyUseMouseXKey") + " " + mouseKey;
            }
            return "";
        }

        internal static string GetKeysFromShortcut(string shortcut)
        {
            if (shortcut.Contains("mouse"))
            {
                shortcut = shortcut.Split('|')[0];
            }
            return shortcut.Replace("double", "").Replace("shortcut", "").Trim();
        }

        internal static string GetMouseKeyFromShortcut(string shortcut)
        {
            if (shortcut.Contains("mouse"))
            {
                if (shortcut.Split('|').Length > 1) return shortcut.Split('|')[1].Replace("mouse", "").Trim();
            }
            return "";
        }

        class HandlerData
        {
            internal string NameEvent { get; set; }
            internal string Shortcut { get; set; }

            internal EventHandler<HotkeyEventArgs> Handler { get; set; }

        }
    }
}