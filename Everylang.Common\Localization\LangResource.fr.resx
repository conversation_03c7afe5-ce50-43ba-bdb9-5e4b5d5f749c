<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>À propos du programme</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Nouvelle version disponible, veuillez redémarrer l'application</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Clair</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Période d'essai activée pendant 40 jours, toutes fonctionnalités incluses</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Copie</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Copier le HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Copier Rtf</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Cliquez sur le numéro pour insérer du texte</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Diviser le texte au caractère de nouvelle ligne</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Diviser le texte par espace</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Sortie</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Geler la fenêtre</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>De la langue :</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Afficher l'historique</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Effondrement</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Développer</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Insérer</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Ouvrez la fenêtre principale avec traduction</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Ouvrez le site Web du service de traduction</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Raccourci clavier pour ouvrir la fenêtre principale</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Ouvrir la liste dans la fenêtre principale</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Raccourci clavier pour désactiver toutes les fonctionnalités</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Désactivez le programme</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Activer le programme</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>Le programme est désactivé</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Ne ferme pas</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>En latin</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Langue:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Traduire</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Une erreur s'est produite pendant le processus de traduction, veuillez réessayer ou sélectionner un autre service de traduction</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Écouter</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Mise à jour</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>PARAMÈTRES</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>HISTOIRE</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>Presse-papiers</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>AGENDA</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>EXTRAITS</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Activer PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Histoire</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Presse-papiers</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Raccourcis clavier</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Impossible d'enregistrer la combinaison de touches</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Mise à jour disponible, redémarrez l'application</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>L'application est en cours d'exécution et réduite</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>L'application a été mise à jour, la liste des modifications est sur le site</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Pour que le programme fonctionne correctement avec toutes les applications, vous devez l'exécuter en tant qu'administrateur</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Une erreur s'est produite et l'application sera terminée. Veuillez envoyer un texte avec des erreurs à <EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Votre ordinateur dispose de plusieurs configurations de clavier installées pour certaines langues, ce qui peut nuire au bon fonctionnement de la fonction de changement de configuration.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Fonte</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Trouver...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(version PRO)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Changement</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tous</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Tous les programmes</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Uniquement en version PRO</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>Le programme est réduit dans la barre d'état</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Avant d'utiliser le programme, lisez la documentation</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Onglet - Rechercher.  Echap - Annuler et effacer</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronom</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Adjectif</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verbe</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Adverbe</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Prétexte</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Union</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Interjection</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Communion</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Verbe auxiliaire</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Mot d'introduction</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Vérification orthographique</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Aucune erreur</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Langue non prise en charge</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Une erreur s'est produite</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Le texte est trop long</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Possibilités :</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Aucune option</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Vérification terminée</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Sauter</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Tout ignorer</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Remplacer</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Remplacer tout</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Insérer du texte</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Copie</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Réinitialiser les paramètres</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Appuyez sur la combinaison de touches</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>russe</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>italien</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ukrainien</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>À PROPOS DU PROGRAMME</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Contrat de licence</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Un assistant universel pour travailler avec du texte dans différentes langues</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Version:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Une nouvelle version du programme est disponible</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Mise à jour</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>RETOUR</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Écrivez vos commentaires et/ou questions</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Réinitialiser tous les paramètres du programme</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Ouvrir la fenêtre de bienvenue</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Réinitialiser tous les paramètres et données du programme ?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Formulaire de contact</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>Le programme s'exécute en tant qu'administrateur</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>Le programme ne s'exécute pas en tant qu'administrateur</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>COMPOSANTS</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Changer la mise en page</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Changer la disposition du dernier mot</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Utiliser la pause</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Changer la disposition du texte sélectionné</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Sélection d'une méthode de changement de disposition</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Émulation de touches pour changer de disposition</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Exécuter une commande Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Conserver le texte sélectionné après avoir changé de mise en page</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Changement de disposition activé</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Utilisez Ctrl+(numéro) pour passer à une langue spécifique</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Utilisez la touche Maj en double-cliquant</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Passer du début de la ligne</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Utilisez le double-clic sur le bouton ScrollLock</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Changer la disposition par bouton</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Paramètres système</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Ctrl droit</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Ctrl gauche</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Maj droite</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Maj gauche</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Ctrl droite ou gauche</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Maj droite ou gauche</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Ctrl droit ou CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Pour activer ou désactiver la fonction CapsLock, appuyez simultanément sur les touches Shift droite et gauche.</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Son de changement de disposition</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Définir le son pour changer de mise en page</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Liste des langues vers lesquelles la mise en page basculera</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Configuration des touches pour passer à une langue spécifique</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Désactiver le changement de disposition automatique ?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Activer le changement de disposition automatique ?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Changement de disposition automatique</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Commutation automatique activée</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Corriger deux lettres majuscules au début d'un mot</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Correction d'une pression accidentelle sur CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Ne changez pas si toutes les lettres d'un mot sont en majuscules</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Corrigez la mise en page après avoir appuyé sur la touche Entrée</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Ajouter des mots d'une lettre aux règles</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Ajouter automatiquement des règles de commutation</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Ne corrigez pas la mise en page si elle a déjà été modifiée manuellement</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Changer de disposition seulement après avoir saisi un mot entier</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Changer de disposition après avoir arrêté de taper</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Supprimer toutes les règles de commutation automatique</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Texte</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Toutes les mises en page</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Ajouter des règles seulement après confirmation</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Changer</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Ne changez pas</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Candidat</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Règles de commutation automatique</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Liste des langues pour lesquelles la commutation automatique fonctionnera</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Liste des règles de commutation automatique</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Afficher les candidats</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Ouvrir la liste des règles de commutation automatique</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Nombre de commutateurs manuels de la disposition des mots à inclure dans les règles</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Ajouter un mot aux règles de commutation automatique ? Entrez - OUI</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Langue de saisie actuelle sur le pointeur de la souris</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Langue de saisie actuelle dans le curseur de texte</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Fenêtre d'indicateur de langue séparée</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Langue actuelle dans la barre d'état système</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Fonctionnalités avancées</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Afficher le drapeau du pays</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Afficher le nom de la langue</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Transparence des indicateurs</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Augmenter la taille de l'indicateur en pourcentage</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Indication de la langue actuelle sur le clavier</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Position de l'indicateur sur le pointeur de la souris</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Masquer l'indicateur dans les programmes exécutés en plein écran</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Afficher l'état de CapsLock</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock activé</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Position de l'indicateur dans le curseur de texte</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Vérification orthographique</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Vérification orthographique activée</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Vérifier l'orthographe lors de la saisie</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Son de vérification orthographique lors de la saisie</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Utilisez des numéros pour un remplacement rapide</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Paramètres sonores pour la vérification orthographique</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Raccourci clavier pour vérifier l'orthographe du texte sélectionné</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Vérifier l'orthographe du texte sélectionné</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Fermez la fenêtre s'il n'y a pas d'erreur après 3 secondes</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Gestionnaire du presse-papiers</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Collez le texte sans formatage et collez le chemin du fichier copié</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Gestionnaire de presse-papiers activé</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Ouvrir l'historique du presse-papiers à l'aide du raccourci clavier</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Coller séquentiellement le texte de l'historique du presse-papiers pour la fenêtre actuelle</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Coller séquentiellement du texte à partir de l'historique du presse-papiers</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Coller le texte par Ctrl+Shift+(numéro) - numéro 1, 2, 3, 4, 5, 6, 7, 8, 9 - index de l'entrée dans l'historique du presse-papiers</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Enregistrez le chemin d'accès au fichier copié dans l'historique du presse-papiers</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Enregistrer l'historique du tampon d'image</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Lors du collage du texte de l'historique, remplacez la valeur actuelle dans le presse-papiers</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Taille de l'historique du Presse-papiers</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Changer le son du presse-papiers</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Convertisseur de texte</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Convertir en fonction de la disposition actuelle du clavier</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Convertir des nombres et des dates en chaînes, évaluer des expressions</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Configurer les raccourcis clavier</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Inverser la casse du texte sélectionné</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Convertir le texte sélectionné en majuscule</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Texte sélectionné en minuscules</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Minuscule le premier caractère du mot sous le curseur</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Mettre en majuscule le premier caractère du mot sous le curseur</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Translittérer le texte sélectionné</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Convertir le texte en style camelCase</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Rechercher et remplacer du texte dans le texte sélectionné</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Encadrer le texte sélectionné avec des symboles (exemple)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Gauche</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Droite</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>exemple de texte</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Fermer ÉCHAP</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Remplacer le texte ENTRÉE</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Traduction</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Langue par défaut vers laquelle traduire</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Langue par défaut à partir de laquelle traduire</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Langue principale pour la traduction</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Service de traduction</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Raccourci clavier pour traduire le texte sélectionné</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Appuyez sur la combinaison de touches</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Double-cliquez sur le bouton Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Traduire lors de la sélection de texte avec la souris</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Traduction incluse</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Paramètres généraux</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Langue de l'interface du programme</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Redémarrer l'application pour changer la langue ?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Divers</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Réduire dans le bac</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Exécuter depuis Windows</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Exécuter avec les droits d'administrateur</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Vérifier les mises à jour</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Mise à jour vers la version bêta</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Sujet</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>De jour comme de nuit</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Styles de conception</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Dossier pour enregistrer les paramètres du programme</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Utiliser les paramètres du proxy système</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Entrez l'adresse du serveur</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Entrez le port</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Entrez votre nom d'utilisateur</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Entrez votre mot de passe</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Enregistrer les paramètres du proxy</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Paramètres du proxy</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Erreur de paramètres du serveur proxy, modifier les paramètres</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Utilisez un thème sombre le soir</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Horaires du soir à partir de</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Horaires du soir jusqu'à</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Désactivez toutes les fonctions des programmes exécutés en mode plein écran</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Paramètres d'importation</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Paramètres d'exportation</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Paramètres de base</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Traducteur</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Vérification orthographique</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Indicateur de mise en page</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Fonctionnalités PRO</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Presse-papiers</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Convertisseur de texte</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Changer de disposition</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Commutateur automatique</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Programmes d'exception</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Dispositions par défaut</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>À propos du programme</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Licence</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Extraits</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Traduire</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Copie</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Vérifier l'orthographe</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Recherche d'appel</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Ouvrir le lien dans le navigateur</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Traduisez le site en utilisant le lien</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Générer un lien court</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Créer un message électronique</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Insérer du texte</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Coller du texte sans formatage</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Ouvrir l'historique du presse-papiers</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Ouvrir l'historique des traductions</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Journal ouvert</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Changer la casse du texte sélectionné</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Ouvrir la liste des extraits</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Convertisseur de texte</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Texte sélectionné en minuscules</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Convertir le texte sélectionné en majuscule</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Inverser la casse du texte sélectionné</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Encadrer le texte sélectionné avec des symboles</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Translittérer le texte sélectionné</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Convertir des nombres et des dates en chaînes, évaluer des expressions</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Convertisseur</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Traduction</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Copie</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Orthographe</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Recherche</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Lien</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Traduction de sites Web</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL plus courte</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Insérer</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Insertion sans format.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>sans format.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Historique du tampon</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>tampon</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Extraits</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Inscrivez-vous</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Inscrivez-vous</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Inverser le registre</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Encadrement du texte</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Translitération</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Tapis. expressions</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Commencer:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Fin:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Clair</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Compris</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEXTE</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>APPLICATION</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Programmes d'exception</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Ajouter un fichier exe</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Ajouter un dossier</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Ajouter par titre de fenêtre</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Indicateur de mise en page</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Changer de disposition</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Extraits</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Raccourcis clavier</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Changement de langue automatique</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Journal de saisie de texte</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Convertisseur et casse de texte</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Historique du presse-papiers</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Enregistrer des images</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Cliquez sur le programme souhaité</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Marquez les fonctions qui fonctionneront pour le programme</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Ajout à partir de la liste des programmes</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Ajouter avec le curseur</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Langages du programme</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Langue par défaut pour les programmes sélectionnés</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Activation des fonctions PRO</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Code d'activation</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Essayez-le</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO activé</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Période d'essai de 40 jours</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Informations sur la licence</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Date d'activation :</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Durée de validité de la licence :</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>E-mail:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Propriétaire:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Informations sur la licence</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Nombre de places :</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Nombre de réactivations disponibles :</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Places disponibles :</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Type de licence :</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO non activé</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO activé pour la période d'essai</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Acheter</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Entrez l'e-mail</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Les informations de votre licence ont été envoyées à :</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Supprimer la licence</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Entrez le code et l'e-mail</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Activation terminée avec succès</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Activation terminée avec erreur</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Activation terminée avec erreur, votre licence est bloquée</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>Le programme a été activé sur le nouveau lieu de travail, mais a été désactivé sur l'ordinateur</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Activation terminée avec erreur, vérifiez votre connexion Internet</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Activation terminée avec une erreur, un email incorrect a peut-être été saisi</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>La période d'essai a expiré</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>La licence a expiré</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>L'activation n'est pas possible en raison du dépassement de la limite de réactivation, veuillez acheter des sièges supplémentaires pour cette licence</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>L'activation n'est pas possible en raison de la réactivation de cette licence sur un autre ordinateur</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Votre code d'activation a été mis à jour</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Licence perpétuelle</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Vous avez dépassé la limite du nombre de postes pour la licence, toutes les fonctionnalités PRO seront désactivées</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>La version PRO a expiré, une nouvelle licence peut être achetée sur le site EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>La version PRO expire bientôt</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>La limite de réactivation de la licence pour les 30 derniers jours a été dépassée, toutes les fonctionnalités PRO seront désactivées</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Votre licence est bloquée, toutes les fonctionnalités PRO seront désactivées</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick activé</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Sélectionnez un service de recherche pour SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Vérifiez les fonctionnalités qui seront disponibles</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Ouvrir en appuyant sur le bouton gauche puis droit de la souris</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Ouvrir en double-cliquant sur le bouton central de la souris</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Utilisez les raccourcis clavier pour ouvrir</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Raccourcis clavier pour ouvrir la fenêtre SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Afficher la fenêtre d'aide après la sélection de texte</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Taille de la fenêtre</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Position de la fenêtre par rapport au pointeur de la souris</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Journal ouvert</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Journal inclus</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Mot de passe du journal</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Entrez votre ancien mot de passe de journal</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Nouveau mot de passe enregistré</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Réinitialisation du mot de passe</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Nombre d'entrées dans l'agenda</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>Le mot de passe saisi est incorrect, réinitialiser le mot de passe actuel ? Dans ce cas, toutes les données de l'agenda seront supprimées</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Gardez des phrases d'un seul mot</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Texte à remplacer (facultatif) :</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Texte de l'extrait :</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>Vers quelle langue dois-je changer la mise en page ?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Ne changez pas</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Balises (séparées par des espaces) :</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Balises</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Description:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Maintenir la position du curseur</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Remplacer lors de la saisie</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Extraits</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Nouvel extrait</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Supprimer tous les extraits comportant cette balise ?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Extraits activés</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Modifier des extraits</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Que remplacer</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>Par quoi remplacer</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Changement</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Remplacer lors de la saisie dans une autre mise en page</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Correspondre à la casse des lettres</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Afficher un indice lors de la saisie</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Trier par fréquence d'utilisation</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Remplacer par :</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Extraits activés</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Ouvrir la liste des extraits à insérer</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Ajouter du texte en surbrillance aux extraits</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Touche de tabulation</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Touche Entrée</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tabulation ou Entrée</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Espace</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Espace ou Entrée</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Raccourcis clavier</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Double pression sur une touche</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Cliquer sur un bouton de la souris</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Raccourcis clavier activés</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Absent</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Appuyez sur les touches de raccourci</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Combinaison</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Appuyez deux fois</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Cliquer sur un bouton de la souris</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Sélectionnez une touche sur laquelle appuyer deux fois</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Sélectionnez un bouton de souris supplémentaire</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Raccourcis clavier désactivés</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Ctrl gauche ou droite</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Ctrl gauche</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Ctrl droit</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Maj gauche ou droite</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Maj gauche</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Maj droite</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Alt gauche</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Alt droite</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Son activé</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Sélectionnez un son</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Avant d'utiliser le programme, lisez la documentation</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Explorez les fonctionnalités du programme</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Regardez la vidéo de présentation</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Site Web du programme</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Acheter une licence</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Reconnaissance de texte</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Raccourci clavier pour démarrer l'OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Sélectionnez les langues par défaut</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Sélectionnez les langues</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>L'utilisation des langues européennes et asiatiques n'est pas prise en charge</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Langues européennes :</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Langues asiatiques :</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Sélectionnez la zone d'écran</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Ouvrir l'image</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Reconnaître</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Reconnaître le code-barres</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Sélectionnez les langues</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Sélectionnez une zone sur l'écran ou téléchargez un fichier pour la reconnaissance</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Reconnaissance de texte</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Veuillez patienter, le module est en cours de chargement</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Cliquez pour télécharger le module</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Texte copié</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Texte non reconnu</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Copier l'image</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Copier le texte</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Remplacer par :</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Recherche:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Nombre total de correspondances trouvées :</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Aucune correspondance trouvée !</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Total des remplacements effectués :</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATE</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TAPER</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Effacer le filtre</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Sélectionner des colonnes</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filtre</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>ET</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Contient</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Ne contient pas</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Se termine par</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Contenu dans</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Vide</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Égal</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Plus que</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Supérieur ou égal à</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Moins que</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Inférieur ou égal à</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Non contenu dans</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>pas vide</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>pas égal</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>N'est pas nul</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Nul</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Sensible aux majuscules et minuscules</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Ou</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Tout sélectionner</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Afficher les lignes avec une valeur qui</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Commence par</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Faites glisser un en-tête de colonne et déposez-le ici pour regrouper par cette colonne</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>En-tête de groupe</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Regroupé par :</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Recherche en texte intégral</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Le journal est désactivé</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Journal inclus</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>Le gestionnaire de presse-papiers est désactivé</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Compris</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Changement de mise en page désactivé</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>Le changement de disposition automatique est désactivé</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Changement de disposition automatique activé</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Cliquez ici pour ajouter un nouvel élément</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Copier le texte traduit</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Tout effacer</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Ouvrir dans le navigateur</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Fermer le programme ?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Langues proposées</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Sélectionnez vos langues préférées</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Stocker l'historique des traductions</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Effacer l’historique des traductions</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Fonte</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Fonctions</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Chargement</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Changer la taille du canevas</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Correction</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Somme</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Arrière-plan:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Couleur de la bordure :</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Épaisseur de la bordure :</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Taille de la toile</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>blanc</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Garniture</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Texte de l'image</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Votre texte</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Dessiner</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Couleur du pinceau :</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Taille du pinceau :</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Se brouiller</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Luminosité</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Contraste</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Changer la teinte</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Inverser les couleurs</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Saturation</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Aiguiser</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modification</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Retourner horizontalement</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Retourner verticalement</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Taille de la police</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Hauteur:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Position horizontale</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Alignement des images</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Aperçu de l'image</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Taille de l'image</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Ouvrir</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Possibilités</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Conserver les proportions d'origine</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Rayon:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Taille relative</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Redimensionner</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Rotation à 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Rotation à 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Rotation à 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Tourner</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Coins arrondis</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Chiffre</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Couleur de la bordure</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Épaisseur de la bordure</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Formulaire de remplissage de tuyaux</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Verrouiller les proportions</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Chiffre</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Remplir une forme</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Texte</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Couleur du texte</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Le fichier ne peut pas être ouvert.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Le fichier ne peut pas être ouvert. Cela peut être bloqué par une autre application.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Convertir</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Échec de l'enregistrement du fichier.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Ce format de fichier n'est pas pris en charge.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Position verticale</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Largeur:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Tout réinitialiser</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Depuis le presse-papiers</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Modifier l'image</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Coller l'entrée d'émulation</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Éditeur d'extraits</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Texte de remplacement :</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Le module de reconnaissance de texte n'est pas chargé</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Apparence</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Ordre des fonctions</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Afficher uniquement les langues sélectionnées</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Afficher tout</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Redémarrer le programme pour changer de thème ?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Pour insérer du texte, appuyez sur la touche</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Afficher les notes</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>Vers les archives</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Ajouter une remarque</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Liste des notes</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Couleur des notes</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Convertir en note</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Convertir en liste de tâches</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Coller comme texte normal</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Coller sous forme de texte</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Archives des notes</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Restaurer</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Famille de police et taille des notes</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Transparence des notes inactives</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick désactivé</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Journal désactivé</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Extraits désactivés</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Afficher le bouton de fermeture</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Délai d'attente pour l'émulation de frappe</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Texte reconnu</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>La mise à jour n'est pas terminée, veuillez la mettre à jour manuellement.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Erreur de mise à jour EveryLang</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Tagué</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Compris</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Entrez le texte à rechercher</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Des archives</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>Dans les archives</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Disposition du clavier</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Casse du texte</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Afficher l'état de NumLock</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock est désactivé</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>Verrouillage numérique activé</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Ouvrir une fenêtre avec les fonctions du convertisseur</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Mettre la première lettre en majuscule</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Tout d'abord</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Première lettre minuscule</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Premier essai</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Ouvrir une fenêtre avec des fonctions de changement de cas</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Convertir le texte en style Snake_case</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Conversion du texte en style kebab-case</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Conversion de texte en style PascalCase</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Cas de texte</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Cliquez pour télécharger</value>
  </data>
  <data name="AutochangeSortingByAlphabet" xml:space="preserve">
    <value>Trier par alphabet</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Favoris</value>
  </data>
  <data name="RemoveFavorite" xml:space="preserve">
    <value>Supprimer des favoris</value>
  </data>
  <data name="AddFavorite" xml:space="preserve">
    <value>Ajouter aux favoris</value>
  </data>
  <data name="ClipboardFavorite" xml:space="preserve">
    <value>Favoris du presse-papiers</value>
  </data>
</root>