﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        public List<string> AppThemes { get; set; }

        public DelegateCommand SpellCheckCommand
        {
            get;
            private set;
        }

        public MainWindowViewModel()
        {
            SpellCheckCommand = new DelegateCommand(SpellCheck);
            GlobalEventsApp.EventUpdateAvailable += UpdateAvailableHandler;
            GlobalEventsApp.EventPro += (_) =>
            {
                base.OnPropertyChanged(nameof(IsTrue));
                base.OnPropertyChanged(nameof(TitleName));
                CheckFuck();
            };
            GlobalEventsApp.EventProStart += CheckFuck;
            AppThemes = new List<string>() { "System", "Dark", "Light" };
            SetTheme();
            SystemEvents.UserPreferenceChanged += SystemEvents_UserPreferenceChanged;
        }

        private void SystemEvents_UserPreferenceChanged(object sender, UserPreferenceChangedEventArgs userPreferenceChangedEventArgs)
        {
            if (userPreferenceChangedEventArgs.Category == UserPreferenceCategory.Color)
            {
                if (SettingsManager.Settings.AppUIThemeNew == "System")
                {
                    SetTheme();
                }
            }
        }

        private static async void CheckFuck()
        {
            try
            {
                if (File.Exists(@"C:\Windows\System32\drivers\etc\hosts"))
                {
                    bool isFuck = false;
                    List<string?> list = new List<string?>();
                    var fs = new FileStream(@"C:\Windows\System32\drivers\etc\hosts", FileMode.Open, FileAccess.Read,
                        FileShare.Read);
                    using (var sr = new StreamReader(fs))
                    {
                        while (await sr.ReadLineAsync() is { } lineOfText)
                        {
                            if (lineOfText.Contains("data.everylang.net") || lineOfText.Contains("************"))
                            {
                                isFuck = true;
                            }
                            else
                            {
                                list.Add(lineOfText);
                            }
                        }
                    }

                    if (isFuck && list.Count > 0)
                    {
                        try
                        {
                            File.WriteAllLines(@"C:\Windows\System32\drivers\etc\hosts", list!);
                        }
                        catch (Exception)
                        {
                            SettingsManager.Settings.LicEvaluateCode = "";
                            SettingsManager.Settings.LicCode = "";
                            SettingsManager.Settings.LicIsActive = false;
                            SettingsManager.Settings.LicIsEvaluate = false;
                            Application.Current.Shutdown(0);
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        private void SetTheme()
        {

            if (string.IsNullOrEmpty(SettingsManager.Settings.AppUIThemeNew))
            {
                SettingsManager.Settings.AppUIThemeNew = "System";
            }
            ThemeVariation variation = ThemeVariation.Light;
            if (SettingsManager.Settings.AppUIThemeNew == "System")
            {
                variation = AccentColorSet.Variation;
            }
            else if (SettingsManager.Settings.AppUIThemeNew == "Dark")
            {
                variation = ThemeVariation.Dark;
            }
            if (variation == ThemeVariation.Dark)
            {
                SettingsManager.Settings.AppUIThemeCurrent = "dark";
                Windows11Palette.LoadPreset(Windows11Palette.ColorVariation.Dark);
                Windows11Palette.Palette.PrimaryForegroundColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.SecondaryForegroundColor = (Color)ColorConverter.ConvertFromString("#C8FFFFFF");
                Windows11Palette.Palette.TertiaryForegroundColor = (Color)ColorConverter.ConvertFromString("#8BFFFFFF");
                Windows11Palette.Palette.DisabledForegroundColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.AccentControlForegroundColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.IconColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.IconSecondaryColor = (Color)ColorConverter.ConvertFromString("#C8FFFFFF");
                Windows11Palette.Palette.PrimaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#0FFFFFFF");
                Windows11Palette.Palette.PrimarySolidBackgroundColor = (Color)ColorConverter.ConvertFromString("#FF1E1E1E");
                Windows11Palette.Palette.SecondaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#FF1C1C1C");
                Windows11Palette.Palette.TertiaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#FF282828");
                Windows11Palette.Palette.TertiarySmokeBackgroundColor = (Color)ColorConverter.ConvertFromString("#4D000000");
                Windows11Palette.Palette.SubtleColor = (Color)ColorConverter.ConvertFromString("#0FFFFFFF");
                Windows11Palette.Palette.SubtleSecondaryColor = (Color)ColorConverter.ConvertFromString("#0BFFFFFF");
                Windows11Palette.Palette.AlternativeColor = (Color)ColorConverter.ConvertFromString("#FF202020");
                Windows11Palette.Palette.OverlayColor = (Color)ColorConverter.ConvertFromString("#FF2D2D2D");
                Windows11Palette.Palette.PrimaryBorderColor = (Color)ColorConverter.ConvertFromString("#12FFFFFF");
                Windows11Palette.Palette.PrimarySolidBorderColor = (Color)ColorConverter.ConvertFromString("#FF2C2C2C");
                Windows11Palette.Palette.SecondaryBorderColor = (Color)ColorConverter.ConvertFromString("#18FFFFFF");
                Windows11Palette.Palette.TertiaryBorderColor = (Color)ColorConverter.ConvertFromString("#FF262626");
                Windows11Palette.Palette.ButtonBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#17FFFFFF");
                Windows11Palette.Palette.ButtonBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#11FFFFFF");
                Windows11Palette.Palette.InputBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#14FFFFFF");
                Windows11Palette.Palette.InputBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#8AFFFFFF");
                Windows11Palette.Palette.AccentControlBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#14FFFFFF");
                Windows11Palette.Palette.AccentControlBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#23000000");
                Windows11Palette.Palette.FocusColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.FocusInnerColor = (Color)ColorConverter.ConvertFromString("#B3000000");
                Windows11Palette.Palette.MouseOverBackgroundColor = (Color)ColorConverter.ConvertFromString("#15FFFFFF");
                Windows11Palette.Palette.MouseOverBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#14FFFFFF");
                Windows11Palette.Palette.MouseOverBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#8AFFFFFF");
                Windows11Palette.Palette.PressedBackgroundColor = (Color)ColorConverter.ConvertFromString("#08FFFFFF");
                Windows11Palette.Palette.SelectedColor = (Color)ColorConverter.ConvertFromString("#0FFFFFFF");
                Windows11Palette.Palette.SelectedMouseOverColor = (Color)ColorConverter.ConvertFromString("#0BFFFFFF");
                Windows11Palette.Palette.SelectedUnfocusedColor = (Color)ColorConverter.ConvertFromString("#FF404040");
                Windows11Palette.Palette.StrokeColor = (Color)ColorConverter.ConvertFromString("#FF313131");
                Windows11Palette.Palette.ReadOnlyBackgroundColor = (Color)ColorConverter.ConvertFromString("#08FFFFFF");
                Windows11Palette.Palette.ReadOnlyBorderColor = (Color)ColorConverter.ConvertFromString("#FF1C1C1C");
                Windows11Palette.Palette.DisabledBackgroundColor = (Color)ColorConverter.ConvertFromString("#0BFFFFFF");
                Windows11Palette.Palette.DisabledBorderColor = (Color)ColorConverter.ConvertFromString("#12FFFFFF");
                Windows11Palette.Palette.ValidationColor = (Color)ColorConverter.ConvertFromString("#FFFF99A4");
                Windows11Palette.Palette.AccentColor = (Color)ColorConverter.ConvertFromString("#FF60CDFF");
                Windows11Palette.Palette.AccentMouseOverColor = (Color)ColorConverter.ConvertFromString("#E660CDFF");
                Windows11Palette.Palette.AccentPressedColor = (Color)ColorConverter.ConvertFromString("#CC60CDFF");
                Windows11Palette.Palette.AccentSelectedColor = (Color)ColorConverter.ConvertFromString("#FF0078D4");
                Windows11Palette.Palette.DisabledOpacity = 0.7;
                Windows11Palette.Palette.ReadOnlyOpacity = 0.8;
            }
            if (variation == ThemeVariation.Light)
            {
                SettingsManager.Settings.AppUIThemeCurrent = "light";
                Windows11Palette.LoadPreset(Windows11Palette.ColorVariation.Light);
                Windows11Palette.Palette.PrimaryForegroundColor = (Color)ColorConverter.ConvertFromString("#E4000000");
                Windows11Palette.Palette.SecondaryForegroundColor = (Color)ColorConverter.ConvertFromString("#BF000000");
                Windows11Palette.Palette.TertiaryForegroundColor = (Color)ColorConverter.ConvertFromString("#72000000");
                Windows11Palette.Palette.DisabledForegroundColor = (Color)ColorConverter.ConvertFromString("#FF000000");
                Windows11Palette.Palette.AccentControlForegroundColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.IconColor = (Color)ColorConverter.ConvertFromString("#E4000000");
                Windows11Palette.Palette.IconSecondaryColor = (Color)ColorConverter.ConvertFromString("#9B000000");
                Windows11Palette.Palette.PrimaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#B3FFFFFF");
                Windows11Palette.Palette.PrimarySolidBackgroundColor = (Color)ColorConverter.ConvertFromString("#FFFFFFFF");
                Windows11Palette.Palette.SecondaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#FFEEEEEE");
                Windows11Palette.Palette.TertiaryBackgroundColor = (Color)ColorConverter.ConvertFromString("#FFF9F9F9");
                Windows11Palette.Palette.TertiarySmokeBackgroundColor = (Color)ColorConverter.ConvertFromString("#4D000000");
                Windows11Palette.Palette.SubtleColor = (Color)ColorConverter.ConvertFromString("#0A000000");
                Windows11Palette.Palette.SubtleSecondaryColor = (Color)ColorConverter.ConvertFromString("#06000000");
                Windows11Palette.Palette.AlternativeColor = (Color)ColorConverter.ConvertFromString("#FFDCE2E6");
                Windows11Palette.Palette.OverlayColor = (Color)ColorConverter.ConvertFromString("#FFE1E5E6");
                Windows11Palette.Palette.PrimaryBorderColor = (Color)ColorConverter.ConvertFromString("#0F000000");
                Windows11Palette.Palette.PrimarySolidBorderColor = (Color)ColorConverter.ConvertFromString("#FFECECEC");
                Windows11Palette.Palette.SecondaryBorderColor = (Color)ColorConverter.ConvertFromString("#29000000");
                Windows11Palette.Palette.TertiaryBorderColor = (Color)ColorConverter.ConvertFromString("#FFEBEBEB");
                Windows11Palette.Palette.ButtonBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#0E000000");
                Windows11Palette.Palette.ButtonBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#29000000");
                Windows11Palette.Palette.InputBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#0F000000");
                Windows11Palette.Palette.InputBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#72000000");
                Windows11Palette.Palette.AccentControlBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#14FFFFFF");
                Windows11Palette.Palette.AccentControlBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#66000000");
                Windows11Palette.Palette.FocusColor = (Color)ColorConverter.ConvertFromString("#E4000000");
                Windows11Palette.Palette.FocusInnerColor = (Color)ColorConverter.ConvertFromString("#B3FFFFFF");
                Windows11Palette.Palette.MouseOverBackgroundColor = (Color)ColorConverter.ConvertFromString("#B3F9F9F9");
                Windows11Palette.Palette.MouseOverBorderGradientStop1Color = (Color)ColorConverter.ConvertFromString("#0F000000");
                Windows11Palette.Palette.MouseOverBorderGradientStop2Color = (Color)ColorConverter.ConvertFromString("#71000000");
                Windows11Palette.Palette.PressedBackgroundColor = (Color)ColorConverter.ConvertFromString("#4DF9F9F9");
                Windows11Palette.Palette.SelectedColor = (Color)ColorConverter.ConvertFromString("#0A000000");
                Windows11Palette.Palette.SelectedMouseOverColor = (Color)ColorConverter.ConvertFromString("#06000000");
                Windows11Palette.Palette.SelectedUnfocusedColor = (Color)ColorConverter.ConvertFromString("#FFD9D9D9");
                Windows11Palette.Palette.StrokeColor = (Color)ColorConverter.ConvertFromString("#FFC4C4C4");
                Windows11Palette.Palette.ReadOnlyBackgroundColor = (Color)ColorConverter.ConvertFromString("#E6F6F6F6");
                Windows11Palette.Palette.ReadOnlyBorderColor = (Color)ColorConverter.ConvertFromString("#FFEBEBEB");
                Windows11Palette.Palette.DisabledBackgroundColor = (Color)ColorConverter.ConvertFromString("#4DF9F9F9");
                Windows11Palette.Palette.DisabledBorderColor = (Color)ColorConverter.ConvertFromString("#0F000000");
                Windows11Palette.Palette.ValidationColor = (Color)ColorConverter.ConvertFromString("#FFC42B1C");
                Windows11Palette.Palette.AccentColor = (Color)ColorConverter.ConvertFromString("#FF005FB8");
                Windows11Palette.Palette.AccentMouseOverColor = (Color)ColorConverter.ConvertFromString("#E6005FB8");
                Windows11Palette.Palette.AccentPressedColor = (Color)ColorConverter.ConvertFromString("#CC005FB8");
                Windows11Palette.Palette.AccentSelectedColor = (Color)ColorConverter.ConvertFromString("#FF0078D4");
                Windows11Palette.Palette.DisabledOpacity = 0.7;
                Windows11Palette.Palette.ReadOnlyOpacity = 0.8;
            }
            Windows11Palette.Palette.FontSizeS = AppFontSize - 2;
            Windows11Palette.Palette.FontSize = AppFontSize;
            Windows11Palette.Palette.FontSizeM = AppFontSize + 2;
            Windows11Palette.Palette.FontSizeL = AppFontSize + 4;
            Windows11Palette.Palette.FontSizeXL = AppFontSize + 12;
            Windows11Palette.Palette.FontFamily = new FontFamily(AppFont);

        }

        public string AppFont
        {
            get { return SettingsManager.Settings.AppFont; }
            set
            {
                SettingsManager.Settings.AppFont = value;
                SetTheme();
            }
        }

        public int AppFontSize
        {
            get { return SettingsManager.Settings.AppFontSize; }
            set
            {
                SettingsManager.Settings.AppFontSize = value;
                SetTheme();
            }
        }

        public bool IsStopWorking
        {
            get { return SettingsManager.IsStopWorkingAll; }
        }

        public List<string> Fonts
        {
            get
            {
                InstalledFontCollection installedFontCollection = new InstalledFontCollection();
                return installedFontCollection.Families.Select(x => x.Name).ToList();
            }
        }

        public string SelectedAppTheme
        {
            get
            {
                return SettingsManager.Settings.AppUIThemeNew;
            }
            set
            {
                SettingsManager.Settings.AppUIThemeNew = value;
                IsRestart();
            }
        }

        private void IsRestart()
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("GeneralSettingsThemeProgramRestart"),
                Closed = (_, args) =>
                {
                    if (args.DialogResult == true)
                    {
                        SettingsManager.SaveSettings();
                        GlobalEventsApp.OnEventRestart(false);
                    }
                }
            });
        }


        private void SpellCheck(object obj)
        {
            if (VMContainer.Instance.TranslationMainViewModel.SourceText != "")
            {
                GlobalEventsApp.OnEventSpellCheckForMain(VMContainer.Instance.TranslationMainViewModel.SourceText, true);
            }
        }

        private void UpdateAvailableHandler(string varsion, bool isBeta)
        {
            TitleName = "EveryLang  (" + LocalizationManager.GetString("AboutSettingsUpdateAvailable") + ")";
        }

        #region Variables

        internal bool IsTrue => SettingsManager.LicIsActivated;

        internal bool IsName => !IsTrue;

        private string _titleName = "";
        private string _currentService;

        public string TitleName
        {
            get
            {
                if (_titleName == "")
                {
                    if (IsTrue)
                    {
                        if (SettingsManager.Settings.LicIsEvaluate)
                        {
                            return "EveryLang TRIAL";
                        }

                        if (SettingsManager.LicenseModel != null && !string.IsNullOrEmpty(SettingsManager.LicenseModel.UserName) && !SettingsManager.LicenseModel.UserName.Contains("?"))
                        {
                            return "EveryLang PRO";
                        }
                        return "EveryLang PRO";
                    }
                    else
                    {
                        return "EveryLang FREE";
                    }

                }
                else
                {
                    return _titleName;
                }
            }
            set
            {
                _titleName = value;
                base.OnPropertyChanged(nameof(TitleName));
            }
        }


        public string CurrentService
        {
            get { return _currentService; }
            set
            {
                _currentService = value;
                base.OnPropertyChanged(nameof(CurrentService));
            }
        }

        #endregion

    }

}
