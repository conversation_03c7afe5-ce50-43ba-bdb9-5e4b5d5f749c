﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Everylang.App.License.LicenseCore;

class Secure
{
    private static readonly byte[] Salt = Encoding.ASCII.GetBytes("o6896642kbM9c5");

    // ReS<PERSON>per disable once InconsistentNaming
    internal static string EncryptStringAES(string plainText, string sharedSecret)
    {

        string outStr = "";
        if (string.IsNullOrEmpty(plainText))
            return outStr;
        if (string.IsNullOrEmpty(sharedSecret))
            return outStr;

#pragma warning disable SYSLIB0022
        RijndaelManaged? aesAlg = null; // RijndaelManaged object used to encrypt the data.
#pragma warning restore SYSLIB0022


        try
        {

#pragma warning disable SYSLIB0041
            Rfc2898DeriveBytes key = new Rfc2898DeriveBytes(sharedSecret, Salt);
#pragma warning restore SYSLIB0041

#pragma warning disable SYSLIB0022
            aesAlg = new RijndaelManaged();
#pragma warning restore SYSLIB0022

            aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);

            ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

            using MemoryStream msEncrypt = new MemoryStream();
            msEncrypt.Write(BitConverter.GetBytes(aesAlg.IV.Length), 0, sizeof(int));
            msEncrypt.Write(aesAlg.IV, 0, aesAlg.IV.Length);

            using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
            {
                using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                {
                    swEncrypt.Write(plainText);
                }
            }
            outStr = Convert.ToBase64String(msEncrypt.ToArray());
        }
        catch
        {
            // ignored
        }
        finally
        {
            if (aesAlg != null) aesAlg.Clear();
        }
        return outStr;
    }

    // ReSharper disable once InconsistentNaming
    internal static string DecryptStringAES(string cipherText, string sharedSecret)
    {

        string plaintext = "";
        if (string.IsNullOrEmpty(cipherText))
            return plaintext;
        if (string.IsNullOrEmpty(sharedSecret))
            return plaintext;

#pragma warning disable SYSLIB0022
        RijndaelManaged? aesAlg = null;
#pragma warning restore SYSLIB0022

        try
        {

#pragma warning disable SYSLIB0041
            Rfc2898DeriveBytes key = new Rfc2898DeriveBytes(sharedSecret, Salt);
#pragma warning restore SYSLIB0041

            byte[] bytes = Convert.FromBase64String(cipherText);

            using (MemoryStream msDecrypt = new MemoryStream(bytes))
            {

#pragma warning disable SYSLIB0022
                aesAlg = new RijndaelManaged();
#pragma warning restore SYSLIB0022

                aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);

                aesAlg.IV = ReadByteArray(msDecrypt);

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using StreamReader srDecrypt = new StreamReader(csDecrypt);
                plaintext = srDecrypt.ReadToEnd();
            }
        }
        catch
        {
            // ignored
        }
        finally
        {
            if (aesAlg != null) aesAlg.Clear();
        }
        return plaintext;
    }

    private static byte[] ReadByteArray(Stream s)
    {
        byte[] rawLength = new byte[sizeof(int)];
        if (s.Read(rawLength, 0, rawLength.Length) != rawLength.Length)
        {
            throw new SystemException("Stream did not contain properly formatted byte array");
        }

        byte[] buffer = new byte[BitConverter.ToInt32(rawLength, 0)];

        if (s.Read(buffer, 0, buffer.Length) != buffer.Length)
        {
            throw new SystemException("Did not read byte array properly");
        }

        return buffer;

    }

}