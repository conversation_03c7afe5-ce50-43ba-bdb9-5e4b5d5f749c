<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.PivotFieldList</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer.#ctor(Telerik.Windows.Controls.FieldList.FieldBox)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer.GetAutomationControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.#ctor(Telerik.Windows.Controls.FieldList.FieldBoxItem)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.GetAutomationControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.FieldBoxItemAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.InlineFieldBoxAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.InlineFieldBoxAutomationPeer.#ctor(Telerik.Windows.Controls.FieldList.FieldBox)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.InlineFieldBoxAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.InlineFieldBoxItemAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.InlineFieldBoxItemAutomationPeer.#ctor(Telerik.Windows.Controls.FieldList.FieldBoxItem)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.InlineFieldBoxItemAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.#ctor(Telerik.Windows.Controls.RadPivotFieldList)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.GetAutomationControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPivotFieldListAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Pivot.Editors.BoolEditorConverter">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Pivot.Editors.BoolEditorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Pivot.Editors.BoolEditorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Pivot.Editors.EnumEditorConverter">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Pivot.Editors.EnumEditorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Pivot.Editors.EnumEditorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel">
            <summary>
            A view-model used by <see cref="T:Telerik.Windows.Controls.FieldList.Editing.OperatorValueFilterControl" />.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.AvailableConditions">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption"/>s available for selection in <see cref="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.SelectedCondition"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.SelectedCondition">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.From">
            <summary>
            Gets or sets a lower bound for various conditions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.To">
            <summary>
            Gets or sets an upper bound for various conditions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.Than">
            <summary>
            Gets or sets a base point for various conditions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.IgnoreCase">
            <summary>
            Gets or sets a value that indicates if string case should be ignored in the produced filter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.FilterDialogs.OperatorValueFilterViewModel.DistinctValues">
            <summary>
            Gets the distinct values used by this control.
            </summary>
            <value>
            The distinct values.
            </value>
        </member>
        <member name="T:Telerik.Windows.Controls.Pivot.Editors.ValueDisplayNamePair">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.ValueDisplayNamePair.Value">
            <summary>
            Gets the value.
            </summary>
            <value>
            The value.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Pivot.Editors.ValueDisplayNamePair.DisplayName">
            <summary>
            Gets the display name.
            </summary>
            <value>
            The display name.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Pivot.Editors.ValueDisplayNamePair.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption">
            <summary>
            Represents an available condition option. 
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ConditionOption.DisplayName">
            <summary>
            Gets the display friendly name of the condition option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ConditionOption.Condition">
            <summary>
            Gets the condition used for this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.BooleanToAscendingSortOrderConverter">
            <summary>
            Represents a converter that converts <see cref="T:System.Boolean"/> values to and from <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToAscendingSortOrderConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:System.Boolean"/> value to a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:System.Boolean"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:Telerik.Pivot.Core.SortOrder.Ascending"/> if value is true; otherwise, <see cref="F:Telerik.Pivot.Core.SortOrder.Descending"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToAscendingSortOrderConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value to a <see cref="T:System.Boolean"/> value.
            </summary>
            <param name="value">A <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>true if value is <see cref="F:Telerik.Pivot.Core.SortOrder.Ascending"/>; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.BooleanToDescendingSortOrderConverter">
            <summary>
            Represents a converter that converts <see cref="T:System.Boolean"/> values to and from <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToDescendingSortOrderConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:System.Boolean"/> value to a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:System.Boolean"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:Telerik.Pivot.Core.SortOrder.Descending"/> if value is true; otherwise, <see cref="F:Telerik.Pivot.Core.SortOrder.Ascending"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToDescendingSortOrderConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value to a <see cref="T:System.Boolean"/> value.
            </summary>
            <param name="value">A <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>true if value is <see cref="F:Telerik.Pivot.Core.SortOrder.Descending"/>; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.BooleanToSortOrderConverter">
            <summary>
            Represents a converter that converts <see cref="T:System.Boolean"/> values to and from <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToSortOrderConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:System.Boolean"/> value to a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:System.Boolean"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">The parameter matching the current enum value.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>true if value matches the parameter and false otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.BooleanToSortOrderConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value to a <see cref="T:System.Boolean"/> value.
            </summary>
            <param name="value">A <see cref="T:Telerik.Pivot.Core.SortOrder"/> enumeration value.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">The parameter matching the current enum value.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>true if value matches the parameter and false otherwise.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.NamedToStringConverter">
            <summary>
            A converter that converts an object to string using the <see cref="P:Telerik.Pivot.Core.INamed.DisplayName"/> if the object is <see cref="T:Telerik.Pivot.Core.INamed"/> or the <see cref="M:System.Object.ToString"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.NamedToStringConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            If the <paramref name="value"/> implements <see cref="T:Telerik.Pivot.Core.INamed"/> returns the <see cref="P:Telerik.Pivot.Core.INamed.DisplayName"/>. Else returns the <paramref name="value"/> <see cref="M:System.Object.ToString"/>.
            </summary>
            <param name="value">The value.</param>
            <param name="targetType">The targetType - this parameter is ignored.</param>
            <param name="parameter">Converter parameter - this parameter is ignored.</param>
            <param name="culture">Culture - this parameter is ignored.</param>
            <returns>The DisplayName or the ToString representation of the <paramref name="value"/></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.NamedToStringConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            <see cref="M:System.Windows.Data.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> method is not supported.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterTypeToVisibilityConverter">
            <summary>
            A converter that returns <see cref="F:System.Windows.Visibility.Visible"/> if the parameter is one of the top ten filters represented by "Items", "Percent" and "Sum" and the value type matches semantically the string.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterTypeToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterTypeToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter">
            <summary>
            Represents a converter that compares a value to a type and returns a Boolean.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.ComparisonType">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.ComparisonType"/> to compare.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.ComparisonType"/> to Boolean.
            </summary>
            <param name="value">The type to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>True if <paramref name="value"/> inherits <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.ComparisonType"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.TypeToBooleanConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            This method is not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This method is not implemented.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.FilterCommands">
            <summary>
            Provides a standard set of <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> related commands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.FilterCommands.ItemsSetFilter">
            <summary>
            Gets the value that represents the Items Set Filter command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl">
            <summary>
            Control that works with <see cref="T:Telerik.Pivot.Core.Filtering.ItemsFilterCondition"/>.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel">
            <summary>
            A view-model used by <see cref="T:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControl" />.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.IsWorking">
            <summary>
            Gets a value indicating whether this instance is waiting for an async operation.
            </summary>
            <value>
            <c>true</c> if this instance is waiting for an async operation; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.OtherConditionViewModel">
            <summary>
            Gets the other filtering condition.
            </summary>
            <value>
            The other filtering condition.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.Selection">
            <summary>
            Gets the SelectionCollection used to create a <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition"/> for the <see cref="T:Telerik.Windows.Controls.FieldList.Filter"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.SelectionView">
            <summary>
            Gets the filtered SelectionCollection view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.SearchText">
            <summary>
            Gets or sets SearchText and notifies for changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotItemsFilteringControlViewModel.TryExecuteOnDispatcher(System.Action)">
            <summary>
            Executed the action on the dispatcher if dispatcher is available.
            </summary>
            <param name="action">The action.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.OperatorValueFilterControl">
            <summary>
            Control that works with different types of filter conditions.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.OperatorValueFilterControl.#ctor">
            <summary>
            Initializes a new instance of the OperatorValueFilterControl class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.OperatorValueFilterControl.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.OperatorValueFilterControl.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel"/> that encapsulates the editing of Step property for the DoubleGroups.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel">
            <summary>
            A view-model that encapsulates the editing of the size of the generated <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>s.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.SelectedStep">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.IDoubleGroupDescription.Step"/> of current <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.Error">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.Item(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.CanExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DoubleGroupStepViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.Top10SelectionType.Top">
            <summary>
            Identifies the items in the beginning of a sorted list.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.Top10SelectionType.Bottom">
            <summary>
            Identifies the items at the bottom of a sorted list.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel">
            <summary>
            Provides mechanisms to observe the <see cref="T:Telerik.Pivot.Core.IDataProvider"/> context and its <see cref="T:Telerik.Pivot.Core.IPivotResults"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.DataProvider">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.IDataProvider"/> currently available in the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.DetachFromServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.OnDataProviderResultsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotResultsViewModel.ExecuteRefresh(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel">
            <summary>
            Provides mechanisms to observe the <see cref="T:Telerik.Pivot.Core.IPivotSettings"/> context. Disables the dialog if the current <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context"/> is not part of an <see cref="T:Telerik.Pivot.Core.IPivotSettings"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.PivotSettings">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.IPivotSettings"/> retrieved from the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.TryExecuteOnDispatcher(System.Action)">
            <summary>
            Executed the action on the dispatcher if dispatcher is available.
            </summary>
            <param name="action">The action.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.DetachFromServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.OnPivotSettingsChanged">
            <summary>
            Handles the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.PivotSettingsViewModel.PivotSettings"/> changed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ISelectable">
            <summary>
            An interface for the selection of an item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ISelectable.IsSelected">
            <summary>
            Gets the item this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ISelectable"/> is responsible for.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ISelectable.Item">
            <summary>
            Gets or sets a value that indicates if the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ISelectable.Item"/> is selected or not.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel"/> that encapsulates the editing of a set filter for a <see cref="T:Telerik.Windows.Controls.FieldList.Filter"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterDialog.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel">
            <summary>
            A view-model that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Filter"/>'s Description.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel.DetachFromServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ItemsSetFilterViewModel.OnDataProviderResultsChanged">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.Selectable">
            <summary>
            A wrapper over an object that exposes properties for selection of that item.
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.FieldList.Editing.ISelectable"/>
            <see cref="T:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection"/>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Editing.Selectable.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Selectable.Item">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Selectable.IsSelected">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Selectable.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Selectable.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Selectable.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Selectable.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.SelectAll">
            <summary>
            A special case of <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ISelectable"/> that is used to select or deselect all other items in a <see cref="T:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection"/>.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Editing.SelectAll.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SelectAll.Item">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SelectAll.IsSelected">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SelectAll.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SelectAll.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection">
            <summary>
            A class that implements the logic for item selection and creation of a <see cref="T:Telerik.Pivot.Core.Filtering.Condition"/> for filtering.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection"/> class.
            All items are selected by default.
            </summary>
            <param name="source">The items available for selection.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SelectionCollection.IsAllSelected">
            <summary>
            Gets or sets whether all items should be selected and pass the produced SetCondition filter or unselected and filtered.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.DifferenceFromCalculation">
            <summary>
            Difference from calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DifferenceFromCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.IndexCalculation">
            <summary>
            Index calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.IndexCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ItemBasedCalculation">
            <summary>
            A base class for item based calculation options.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ItemBasedCalculation.Previous">
            <summary>
            Gets an object that represents a special base item for selection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ItemBasedCalculation.Next">
            <summary>
            Gets an object that represents a special base item for selection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.NoCalculation">
            <summary>
            No calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.NoCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentDifferenceFromCalculation">
            <summary>
            Percent difference from calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentDifferenceFromCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentOfCalculation">
            <summary>
            Percent of calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentOfCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentOfColumnTotalCalculation">
            <summary>
            Percent of column total calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentOfColumnTotalCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentOfGrandTotalCalculation">
            <summary>
            Percent of grand total calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentOfGrandTotalCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentOfRowTotalCalculation">
            <summary>
            Percent of row total calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentOfRowTotalCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.PercentRunningTotalInCalculation">
            <summary>
            Percent running total in calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.PercentRunningTotalInCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.RankLargestToSmallestCalculation">
            <summary>
            Rank largest to smallest calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.RankLargestToSmallestCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.RankSmallestToLargestCalculation">
            <summary>
            Rank smallest to largest calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.RankSmallestToLargestCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.RunningTotalCalculation">
            <summary>
            A base class for running total calculation options.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.RunningTotalInCalculation">
            <summary>
            Running total in calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.RunningTotalInCalculation.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.SimpleCalculation">
            <summary>
            A base class for simple calculation options.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ConfirmationFrame">
            <summary>
            A <see cref="T:System.Windows.Controls.ContentControl"/> used to render common OK and Cancel buttons around its content.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ConfirmationFrame.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ComparisonToVisibleConverter">
            <summary>
            Represents a converter that converts <see cref="T:Telerik.Pivot.Core.Filtering.Comparison"/> values to <see cref="T:System.Windows.Visibility"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ComparisonToVisibleConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Pivot.Core.Filtering.Comparison"/> value to a <see cref="T:System.Windows.Visibility"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:Telerik.Pivot.Core.Filtering.Comparison"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:System.Windows.Visibility.Visible"/> if value is <see cref="T:Telerik.Pivot.Core.Filtering.Comparison"/> or <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/>; if <paramref name="value"/> is null or other type, <see cref="F:System.Windows.Visibility.Collapsed"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ComparisonToVisibleConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            This method is not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This parameter is not used.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.IntervalComparisonToVisibilityConverter">
            <summary>
            Represents a converter that converts <see cref="T:Telerik.Pivot.Core.Filtering.IntervalComparison"/> values to <see cref="T:System.Windows.Visibility"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.IntervalComparisonToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Pivot.Core.Filtering.Comparison"/> value to a <see cref="T:System.Windows.Visibility"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:Telerik.Pivot.Core.Filtering.IntervalComparison"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:System.Windows.Visibility.Visible"/> if value is <see cref="T:Telerik.Pivot.Core.Filtering.IntervalComparison"/>; if <paramref name="value"/> is null or other type, <see cref="F:System.Windows.Visibility.Collapsed"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.IntervalComparisonToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This parameter is not used.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.NullToVisibilityConverter">
            <summary>
            Represents a converter that converts <see cref="T:System.Object"/> values to <see cref="T:System.Windows.Visibility"/> enumeration values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.NullToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts an <see cref="T:System.Object"/> value to a <see cref="T:System.Windows.Visibility"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:System.Windows.Visibility.Visible"/> if <paramref name="value"/> is not null; If <paramref name="value"/> is null, <see cref="F:System.Windows.Visibility.Collapsed"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.NullToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            This method is not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This parameter is not used.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.StringToBooleanConverter">
            <summary>
            Represents a converter that compares the string presentation of an object to the converter parameter and returns a Boolean.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringToBooleanConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts an <see cref="T:System.Object"/> to string and compares it value to the <paramref name="parameter"/> value. Returns to a <see cref="T:System.Windows.Visibility"/> enumeration value.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> value to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">The string the value must equals.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:System.Windows.Visibility.Visible"/> if <paramref name="value"/> to string equals <paramref name="parameter"/>; otherwise, <see cref="F:System.Windows.Visibility.Collapsed"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringToBooleanConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            This method is not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This method is not implemented.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter">
            <summary>
            Represents a converter that compares a value to a type and returns a <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Visibility"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.ComparisonType">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.ComparisonType"/> to compare.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Visibility">
            <summary>
            Gets or sets the visibility option.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.ComparisonType"/> to <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Visibility"/>.
            </summary>
            <param name="value">The type to convert.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns><see cref="F:System.Windows.Visibility.Visible"/> if <paramref name="value"/> inherits <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.ComparisonType"/> and <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Visibility"/> is <see cref="P:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.Visibility"/>; otherwise, <see cref="F:System.Windows.Visibility.Collapsed"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            This method is not implemented.
            </summary>
            <param name="value">This parameter is not used.</param>
            <param name="targetType">This parameter is not used.</param>
            <param name="parameter">This parameter is not used.</param>
            <param name="culture">This parameter is not used.</param>
            <returns>This method is not implemented.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.TypeVisibility">
            <summary>
            Gets visibility options for <see cref="T:Telerik.Windows.Controls.FieldList.Editing.TypeToVisibilityConverter"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.TypeVisibility.VisibleIfOfType">
            <summary>
            The converter return <see cref="F:System.Windows.Visibility.Visible"/> if value inherits the comparison type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.TypeVisibility.CollapsedIfOfType">
            <summary>
            The converter return <see cref="F:System.Windows.Visibility.Collapsed"/> if value inherits the comparison type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.LabelCommands">
            <summary>
            Provides a standard set of <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> related commands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.MoreSortingOptions">
            <summary>
            Gets the value that represents the More Sorting Options command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.ValueFilter">
            <summary>
            Gets the value that represents the Value Filter command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.LabelFilter">
            <summary>
            Gets the value that represents the Label Filter command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.Top10Filter">
            <summary>
            Gets the value that represents the Top10 Filter command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.ClearFilter">
            <summary>
            Gets the value that represents the Clear Filter command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.SetDoubleGroupStep">
            <summary>
            Gets the value that represents the Set Double Group Step command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption">
            <summary>
            Represents an available option for a group comparer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption.Description">
            <summary>
            Gets the description this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/> compares on.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption.GroupComparer">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption.GroupComparer"/> for this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption.ComparerDescription">
            <summary>
            Gets the value that will be shown in the UI for the current <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.LabelFilterDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel"/> that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Filtering.LabelGroupFilter"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterDialog.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel">
            <summary>
            A view-model that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Filtering.LabelGroupFilter"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel.DetachFromServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.LabelFilterViewModel.OnDataProviderResultsChanged">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel"/> that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.SortOrder"/> and <see cref="T:Telerik.Pivot.Core.GroupComparer"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel">
            <summary>
            A view-model that encapsulates the editing of a <see cref="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SortOrder"/> and <see cref="T:Telerik.Pivot.Core.GroupComparer"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.AvailableComparers">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/> available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SelectedAscendingComparer"/> and <see cref="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SelectedDescendingComparer"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SelectedAscendingComparer">
            <summary>
            Gets or sets the selected ascending <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SelectedDescendingComparer">
            <summary>
            Gets or sets the selected descending <see cref="T:Telerik.Windows.Controls.FieldList.Editing.GroupComparerOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SortOrder">
            <summary>
            Gets or sets the selected <see cref="P:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.SortOrder"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.OnPivotSettingsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.SortingOptionsViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel"/> that encapsulates the editing of Top10 typed filters for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType">
            <summary>
            Lists the types for Top10 filter.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Items">
            <summary>
            Indicates a <see cref="T:Telerik.Pivot.Core.Filtering.GroupsCountFilter" />.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Percent">
            <summary>
            Indicates a <see cref="T:Telerik.Pivot.Core.Filtering.GroupsPercentFilter" />.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Sum">
            <summary>
            Indicates a <see cref="T:Telerik.Pivot.Core.Filtering.GroupsSumFilter" />.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel">
            <summary>
            A view-model that encapsulates the editing of Top10 typed filters for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.AvailableSelections">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.SortedListSelection"/>s available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedSelection"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedSelection">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Pivot.Core.SortedListSelection"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.AvailableFilterTypes">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType"/>s available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedFilterType"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedFilterType">
            <summary>
            Gets or sets the available <see cref="T:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.AvailableValueSources">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/> available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedValueSource"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.SelectedValueSource">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.Items">
            <summary>
            Gets or sets the number of items to pass in a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Items"/> filter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.Percent">
            <summary>
            Gets or sets the percent of total value to be aggregated by the passing groups in <see cref="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Percent"/> filter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.Sum">
            <summary>
            Gets or sets the percent of total value to be aggregated by the passing groups in <see cref="F:Telerik.Windows.Controls.FieldList.Editing.Top10FilterType.Percent"/> filter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.OnPivotSettingsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.CanExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.Top10FilterViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueFilterDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel"/> that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Filtering.ValueGroupFilter"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel">
            <summary>
            A view-model that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Filtering.ValueGroupFilter"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.AvailableValueSources">
            <summary>
            Gets a list with <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>s available for selection in <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.SelectedValueSource">
            <summary>
            Gets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.AvailableConditions">
            <summary>
            Gets a list with <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption"/>s available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.SelectedCondition"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.SelectedCondition">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ConditionOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.From">
            <summary>
            Gets or sets a lower bound for various conditions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.To">
            <summary>
            Gets or sets an upper bound for various conditions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.Than">
            <summary>
            Gets or sets a base point for various conditions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.OnPivotSettingsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.CanExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueFilterViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption">
            <summary>
            Represents an available option for a value source.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption.DisplayName">
            <summary>
            Gets a display friendly name for this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption.AggregateIndex">
            <summary>
            Gets the aggregate index associated with this <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSourceOption"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueCommands">
            <summary>
            Provides a standard set of <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> related commands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.MoreSummarizationOptions">
            <summary>
            Gets the value that represents the More Summarization Options command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.MoreTotalFormatOptions">
            <summary>
            Gets the value that represents the More TotalFormat Options command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.StringFormats">
            <summary>
            Gets the value that represents the String Formats command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetSumAggregate">
            <summary>
            Gets the value that represents the Set Sum Aggregate command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetCountAggregate">
            <summary>
            Gets the value that represents the Set Count Aggregate command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetAverageAggregate">
            <summary>
            Gets the value that represents the Set Average Aggregate command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetIndexTotalFormat">
            <summary>
            Gets the value that represents the Set Index TotalFormat command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetPercentOfGrandTotalFormat">
            <summary>
            Gets the value that represents the Set Percent Of Grand Total Format command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.ClearTotalFormat">
            <summary>
            Gets the value that represents the Clear Total Format command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.CalculationOption">
            <summary>
            An available calculation option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.CalculationOption.DisplayName">
            <summary>
            Gets the display friendly name for the option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.CalculationOption.RequiresBaseLabel">
            <summary>
            Gets a value that indicates if this calculation option requires a base label selection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.CalculationOption.RequiresBaseItem">
            <summary>
            Gets a value that indicates if this calculation option requires a base item selection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.CalculationType">
            <summary>
            Enumerates possible additional calculations applied as <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/>s.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.NoCalculation">
            <summary>
            Represents no <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfGrandTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfGrandTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfColumnTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfColumnTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfRowTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfRowTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOf">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOf"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentRowTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentRowTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentColumnTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentColumnTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentTotal">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentOfParentTotal"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.DifferenceFrom">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.DifferenceFrom"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentDifferenceFrom">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentDifferenceFrom"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RunningTotalIn">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RunningTotalIn"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentRunningTotalIn">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.PercentRunningTotalIn"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RankSmallestToLargest">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RankSmallestToLargest"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RankLargestToSmallest">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.RankLargestToSmallest"/> TotalFormat.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.Index">
            <summary>
            Represents a <see cref="F:Telerik.Windows.Controls.FieldList.Editing.CalculationType.Index"/> TotalFormat.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel">
            <summary>
            A base class for the various pivot field list editing dialogs view models.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Completed">
            <summary>
            Invoked when the user completed and confirmed the changes on this dialog.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Canceled">
            <summary>
            Invoked when the user canceled any changes done through this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.IsWorking">
            <summary>
            Gets a value that indicates if the dialog is waiting on results.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.RequiresRefresh">
            <summary>
            Gets a value that indicates if the editing requires recent <see cref="T:Telerik.Pivot.Core.IPivotResults"/> data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.IsAvailable">
            <summary>
            Gets a value that indicates if the editing is possible.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Ok">
            <summary>
            Gets the Ok <see cref="T:System.Windows.Input.ICommand"/> for this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Cancel">
            <summary>
            Gets the Cancel <see cref="T:System.Windows.Input.ICommand"/> for this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Refresh">
            <summary>
            Gets the Refresh <see cref="T:System.Windows.Input.ICommand"/> for this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context">
            <summary>
            Gets or sets the context <see cref="T:System.IServiceProvider"/> in which this dialog operates.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.DetachFromServices">
            <summary>
            Detach the previous services.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.RetrieveServices(System.IServiceProvider)">
            <summary>
            Retrieve the services from the <paramref name="serviceProvider"/>. You could use the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context"/> if you do not want the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.IsAvailable"/> to track your requests.
            </summary>
            <param name="serviceProvider">
            The <see cref="T:System.IServiceProvider"/> to retrieve services from.
            This is a slim wrapper around the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Context"/> that would keep track of if all requested services were available.
            If a requested service was unavailable the <see cref="T:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel"/>'s <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.IsAvailable"/> would be set to false.
            </param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.AttachToServices">
            <summary>
            Attach to the new services.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.CanExecuteOk(System.Object)">
            <summary>
            Checks if the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Ok"/> <see cref="T:System.Windows.Input.ICommand"/> can be executed.
            </summary>
            <param name="parameter">The parameters.</param>
            <returns>True if <see cref="T:System.Windows.Input.ICommand"/> can be executed. Otherwise - false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.ExecuteOk(System.Object)">
            <summary>
            Handles the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Ok"/> execution.
            </summary>
            <param name="parameter"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.ExecuteRefresh(System.Object)">
            <summary>
            Handles the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Refresh"/> execution.
            </summary>
            <param name="parameter"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.DialogViewModel.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.NamedObject">
            <summary>
            A base class for simple named token classes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.NamedObject.DisplayName">
            <summary>
            Gets the display-friendly name of this object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.NamedObject.ToString">
            <summary>
            Gets the name from <see cref="P:Telerik.Windows.Controls.FieldList.Editing.NamedObject.DisplayName"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption">
            <summary>
            Identifies an option for string format selection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption.StringFormat">
            <summary>
            Gets the string format represented by this option.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption.Description">
            <summary>
            Gets the string description for this option.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.StringFormatsDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel"/> that encapsulates the editing of a string format for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatsDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel">
            <summary>
            A view-model that encapsulates the editing of a string format for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.AvailableFormatOptions">
            <summary>
            Gets a list with <see cref="T:Telerik.Windows.Controls.FieldList.Editing.StringFormatOption"/> available for <see cref="P:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.SelectedFormat"/> selection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.SelectedFormat">
            <summary>
            Gets or sets the selected string format.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.StringFormatsViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel"/> that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel">
            <summary>
            A view-model that encapsulates the editing of a <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.AvailableCalculationOptions">
            <summary>
            Gets a list of <see cref="T:Telerik.Windows.Controls.FieldList.Editing.CalculationOption"/>s available for selection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedCalculationOption">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Editing.CalculationOption"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.RequiresGroupDescription">
            <summary>
            Gets a value that indicates if the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedCalculationOption"/> requires selection of base label.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.RequiresGroupName">
            <summary>
            Gets a value that indicates if the <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedCalculationOption"/> requires selection of base item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.AvailableGroupDescriptions">
            <summary>
            Gets a list of the <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> items available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedGroupDescription"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedGroupDescription">
            <summary>
            Gets or sets the selected <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> used in the setup of some group filters.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.AvailableGroupNames">
            <summary>
            Gets a lists of <see cref="T:System.Object"/> items available for selection in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedGroupName"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.SelectedGroupName">
            <summary>
            Gets or sets the available base item used in the setup of some group filters.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.OnDataProviderResultsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.OnPivotSettingsChanged">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.CanExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueCalculationsViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationDialog">
            <summary>
            A view on <see cref="T:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel"/> that encapsulates the editing of an aggregate function for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel">
            <summary>
            A view-model that encapsulates the editing of an aggregate function for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s Description.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.AvailableAggregateFunctions">
            <summary>
            Gets a list of objects with available items to select for <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.SelectedAggregateFunction"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.SelectedAggregateFunction">
            <summary>
            Gets or sets an object used to identify an aggregate function. Available options in <see cref="P:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.AvailableAggregateFunctions"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.RetrieveServices(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.AttachToServices">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ValueSummarizationViewModel.ExecuteOk(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editing.ShowFieldListDialog">
            <summary>
            A view that allows for adding new fields to the inline field list.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editing.ShowFieldListDialog.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.AttachedBehavior">
            <summary>
            Encapsulates mechanisms to build behaviors that are attached to object using attached properties.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.AttachedBehavior.Reattach(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Detaches the previous behavior of the <paramref name="element"/> and attaches the next one. The signature is suitable for dependency property changed callback.
            </summary>
            <param name="element"></param>
            <param name="args"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.AttachedBehavior.Detach(System.Windows.DependencyObject)">
            <summary>
            Removes this behavior from the <paramref name="element"/>.
            </summary>
            <param name="element"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.AttachedBehavior.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches this behavior to the <paramref name="element"/>.
            </summary>
            <param name="element"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.AttachedBehavior.RequestElementType``1(System.Object)">
            <summary>
            Gets the <paramref name="element"/> converted to <typeparamref name="THost"/>.
            Throws appropriate exception if the element is not of the right type.
            Use this method in the Attach and Detach methods instead of safe casts.
            </summary>
            <typeparam name="THost">The target type the <paramref name="element"/> should be converted to.</typeparam>
            <param name="element">The element to convert.</param>
            <returns>The element of type <typeparamref name="THost"/>.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior">
            <summary>
            A class that implements an attached behavior that handles <see cref="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents.RequestContextMenu"/> events, providing a context menu.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.BehaviorProperty">
            <summary>
            Identifies the Behavior attached dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.HideSubTotalsMenuItem">
            <summary>
            Gets or sets value indicating whether to hide the Show SubTotals menu item from the ContextMenu.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.GetBehavior(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the Behavior attached property for the <paramref name="obj"/>.
            </summary>
            <param name="obj">The object.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.SetBehavior(System.Windows.DependencyObject,Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior)">
            <summary>
            Sets the Behavior attached property for the <paramref name="obj"/> to <paramref name="value"/>.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListContextMenuBehavior.CreateContextMenu(System.Object)">
            <summary>
            Provide a context menu that would be displayed for the provided <paramref name="dataContext"/>.
            </summary>
            <param name="dataContext">The DataContext.</param>
            <returns>A RadContextMenu instance.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior">
            <summary>
            <see cref="T:Telerik.Windows.Controls.FieldList.AttachedBehavior"/> that delegates assignment of the context menu for the host element to one of its parents.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior.BehaviorProperty">
            <summary>
            Identifies the Behavior attached dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior.GetBehavior(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the Behavior attached property for the <paramref name="obj"/>.
            </summary>
            <param name="obj">The object.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior.SetBehavior(System.Windows.DependencyObject,Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior)">
            <summary>
            Sets the Behavior attached property for the <paramref name="obj"/> to <paramref name="value"/>.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RoutedContextMenuBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs">
            <summary>
            Provides data for the <see cref="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents.RequestContextMenu"/> routed event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs.#ctor(System.Windows.RoutedEvent,System.Windows.FrameworkElement)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event identifier for this instance of the <see cref="T:System.Windows.RoutedEventArgs"/> class.</param>
            <param name="contextMenuHost">The element that will use the context menu.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,System.Windows.FrameworkElement)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event identifier for this instance of the <see cref="T:System.Windows.RoutedEventArgs"/> class.</param>
            <param name="source">An alternate source that will be reported when the event is handled. This pre-populates the System.Windows.RoutedEventArgs.Source property.</param>
            <param name="contextMenuHost">The element that will use the context menu.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs.#ctor(System.Windows.FrameworkElement)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs"/> class for the <see cref="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents.RequestContextMenu"/> routed event.
            </summary>
            <param name="contextMenuHost">The element that will use the context menu.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs.ContextMenuHost">
            <summary>
            Gets the element that will use the context menu.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ContextMenuRequestEventArgs.ContextMenu">
            <summary>
            Gets or sets the assigned context menu.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents">
            <summary>
            Routed events used to delegate the assignment of a context menu to parent elements.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents.RequestContextMenu">
            <summary>
            A routed event that is thrown by items that offer to display context menu but delegate the menu assignment to its parents.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DialogInfo">
            <summary>
            Encloses information for a dialog settings and content.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogInfo.Content">
            <summary>
            Gets or sets the desired content UI of the requested dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogInfo.ViewModel">
            <summary>
            Gets or sets the desired <see cref="P:Telerik.Windows.Controls.FieldList.DialogInfo.ViewModel"/> for the requested dialog.
            <see cref="T:Telerik.Windows.Controls.FieldList.IDialogViewModel"/> implementations support simple communication with the dialog host.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogInfo.Header">
            <summary>
            Gets or sets the header string for the dialog host.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogInfo.DesiredWidth">
            <summary>
            Gets or sets the desired content area width to be available in the dialog host.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogInfo.DesiredHeight">
            <summary>
            Gets or sets the desired content area height to be available in the dialog host.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs">
            <summary>
            Contains information associated with a dialog request.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs.#ctor(Telerik.Windows.Controls.FieldList.DialogInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs"/> class.
            </summary>
            <param name="dialogInfo">The <see cref="P:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs.DialogInfo"/> value.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs.DialogInfo">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs.DialogInfo"/> that contains information about the requested dialog.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior">
            <summary>
            <see cref="T:Telerik.Windows.Controls.FieldList.AttachedBehavior"/> that implements a mechanism to handle <see cref="T:Telerik.Windows.Controls.FieldList.DialogHostingRequestEventArgs"/> events and open the requested UI in a <see cref="T:Telerik.Windows.Controls.RadWindow"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.BehaviorProperty">
            <summary>
            Identifies the Behavior attached dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.GetBehavior(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the Behavior attached property for the <paramref name="obj"/>.
            </summary>
            <param name="obj">The object.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.SetBehavior(System.Windows.DependencyObject,Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior)">
            <summary>
            Sets the Behavior attached property for the <paramref name="obj"/> to <paramref name="value"/>.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.WindowDialogHostingBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.IDialogViewModel">
            <summary>
            An interface that defines some basic mechanisms for communication between a view model and the hosting dialog UI.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.IDialogViewModel.Completed">
            <summary>
            Invoked when the used completed and confirmed the changes on this dialog.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.IDialogViewModel.Canceled">
            <summary>
            Invoked when the used canceled any changes done through this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.IDialogViewModel.Ok">
            <summary>
            Gets the Ok <see cref="T:System.Windows.Input.ICommand"/> for this dialog.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.IDialogViewModel.Cancel">
            <summary>
            Gets the Cancel <see cref="T:System.Windows.Input.ICommand"/> for this dialog.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.RoutedDialogEvents">
            <summary>
            Routed events used to delegate the hosting of dialogs to parent elements.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.RoutedDialogEvents.RequestDialog">
            <summary>
            A routed event that is thrown by items that offer to display context menu but delegate the menu assignment to its parents.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DragDropBehavior">
            <summary>
            Base class for drag and drop attached behaviors.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DragDropBehavior.BehaviorProperty">
            <summary>
            Identifies the Behavior attached dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DragDropBehavior.GetBehavior(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the Behavior attached property for the <paramref name="obj"/>.
            </summary>
            <param name="obj">The object.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DragDropBehavior.SetBehavior(System.Windows.DependencyObject,Telerik.Windows.Controls.FieldList.DragDropBehavior)">
            <summary>
            Sets the Behavior attached property for the <paramref name="obj"/> to <paramref name="value"/>.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldBoxDragDropBehavior">
            <summary>
            Encapsulates the drag and drop handling for all 4 <see cref="T:Telerik.Windows.Controls.FieldList.FieldBox"/> controls in the <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBoxDragDropBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBoxDragDropBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldsTreeDragDropBehavior">
            <summary>
            Encapsulates the drag and drop handling for the <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/>'s tree with available fields.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldsTreeDragDropBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldsTreeDragDropBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs">
            <summary>
            Provides data for the <see cref="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.DisplayDropIndication"/> and <see cref="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.ClearDropIndication"/> routed events.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.#ctor(System.Windows.RoutedEvent,System.Windows.FrameworkElement,Telerik.Windows.Controls.FieldList.DropPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event identifier for this instance of the <see cref="T:System.Windows.RoutedEventArgs"/> class.</param>
            <param name="element">The Element an indication is requested for.</param>
            <param name="position">The position relative to the <paramref name="element"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,System.Windows.FrameworkElement,Telerik.Windows.Controls.FieldList.DropPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event identifier for this instance of the <see cref="T:System.Windows.RoutedEventArgs"/> class.</param>
            <param name="source">An alternate source that will be reported when the event is handled. This pre-populates the System.Windows.RoutedEventArgs.Source property.</param>
            <param name="element">The Element an indication is requested for.</param>
            <param name="position">The position relative to the <paramref name="element"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.Element">
            <summary>
            Gets the element on which a drop occurred and indicator is requested for or removed for.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.Position">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.DropPosition"/> of the drag and drop operation relative to the <see cref="P:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.Element"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.ClearIndication(System.Windows.FrameworkElement)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> that encloses a <see cref="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.ClearDropIndication"/> event.
            </summary>
            <param name="element">The element on which indication is no longer required.</param>
            <returns>An instance of <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> object.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs.DropIndication(System.Windows.FrameworkElement,Telerik.Windows.Controls.FieldList.DropPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> that encloses a <see cref="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.DisplayDropIndication"/> event.
            </summary>
            <param name="element">The element on which indication is requested.</param>
            <param name="position">The drop position relative to the <paramref name="element"/>.</param>
            <returns>An instance of <see cref="T:Telerik.Windows.Controls.FieldList.DropIndicationEventArgs"/> object.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DropIndicationEvents">
            <summary>
            Routed events used to delegate the drawing of a drop indication to parent elements.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.DisplayDropIndication">
            <summary>
            A routed event that is thrown by drag and drop behaviors or manual handling when a visual indication for a drop location is required but should be delegated to parents up the visual tree.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropIndicationEvents.ClearDropIndication">
            <summary>
            A routed event that is thrown by drag and drop behaviors or manual handling when a visual indication for a drop location is no longer required but was previously delegated to parents up the visual tree.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DialogEditor">
            <summary>
            An <see cref="T:Telerik.Windows.Controls.FieldList.Editor"/> that will try to open a dialog using a host request trough <see cref="F:Telerik.Windows.Controls.FieldList.RoutedDialogEvents.RequestDialog"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogEditor.#ctor(System.Windows.Input.ICommand)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DialogEditor"/> class.
            </summary>
            <param name="command">The command.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogEditor.CanExecute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogEditor.OnDialogCanExecute(System.Object)">
            <summary>
            Checks if the editor can handle the editing of the parameter.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>True if the parameter could be edited, false - otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DialogEditor.OnDialogExecuted(System.Object)">
            <summary>
            Define the <see cref="T:Telerik.Windows.Controls.FieldList.DialogInfo"/> properties that should be used to open an editing UI for the <paramref name="parameter"/>.
            </summary>
            <param name="parameter">The object to edit.</param>
            <returns>The DialogInfo for which a dialog host will be requested.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DoubleGroupStepEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.SetDoubleGroupStep"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DoubleGroupStepEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.DoubleGroupStepEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DoubleGroupStepEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DoubleGroupStepEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Editor">
            <summary>
            A custom form of a <see cref="T:System.Windows.Input.CommandBinding"/> used to define the editors for the various options in the <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editor.#ctor(System.Windows.Input.ICommand)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.Editor"/> class.
            </summary>
            <param name="command">Command triggering the editing.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Editor.Command">
            <summary>
            Gets the <see cref="T:System.Windows.Input.ICommand"/> triggering the editing.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editor.Execute(System.Object,System.Object)">
            <summary>
            Handles editing execution. Changes properties of the <see cref="P:System.Windows.Input.ExecutedRoutedEventArgs.Parameter"/>.
            </summary>
            <param name="sender">The sender.</param>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Editor.CanExecute(System.Object,System.Object)">
            <summary>
            Determines if the editing can occur.
            </summary>
            <param name="sender">The sender.</param>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.EditorsBehavior">
            <summary>
            <see cref="T:Telerik.Windows.Controls.FieldList.AttachedBehavior"/> that implements a mechanism to handle editing commands for the various <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/> items.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.EditorsBehavior.BehaviorProperty">
            <summary>
            Identifies the Behavior attached dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.EditorsBehavior.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.EditorsBehavior"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.EditorsBehavior.Editors">
            <summary>
            Gets the collection of the <see cref="T:Telerik.Windows.Controls.FieldList.Editor"/> objects that handle editing commands for the behavior.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.EditorsBehavior.GetBehavior(System.Windows.DependencyObject)">
            <summary>
            Gets the value of the Behavior attached property for the <paramref name="obj"/>.
            </summary>
            <param name="obj">The object.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.EditorsBehavior.SetBehavior(System.Windows.DependencyObject,Telerik.Windows.Controls.FieldList.EditorsBehavior)">
            <summary>
            Sets the Behavior attached property for the <paramref name="obj"/> to <paramref name="value"/>.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.EditorsBehavior.Detach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.EditorsBehavior.Attach(System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.EditorsCollection">
            <summary>
            A collection of <see cref="T:Telerik.Windows.Controls.FieldList.Editor"/> objects.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ItemsSetFilterEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Filter"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.FilterCommands.ItemsSetFilter"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ItemsSetFilterEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ItemsSetFilterEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ItemsSetFilterEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ItemsSetFilterEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.LabelFilterEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.LabelFilter"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.LabelFilterEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.LabelFilterEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.LabelFilterEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.LabelFilterEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ClearTotalFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.ClearTotalFormat"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearTotalFormatEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ClearTotalFormatEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearTotalFormatEditor.CanExecute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearTotalFormatEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetAggregateEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object. Used to set an aggregate by aggregate function name.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetAggregateEditor.#ctor(System.Windows.Input.ICommand,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetAggregateEditor"/> class.
            </summary>
            <param name="command">The command.</param>
            <param name="aggregateFunctionName">The name of the aggregate function to set.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.SetAggregateEditor.AggregateFunctionName">
            <summary>
            Gets the name of the aggregate function this <see cref="T:Telerik.Windows.Controls.FieldList.Editor"/> sets to the edited <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s aggregate description.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetAggregateEditor.CanExecute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetAggregateEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ClearFilterEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.ClearFilter"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearFilterEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ClearFilterEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearFilterEditor.CanExecute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ClearFilterEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetAverageAggregateEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetAverageAggregate"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetAverageAggregateEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetAverageAggregateEditor"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetCountAggregateEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetCountAggregate"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetCountAggregateEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetCountAggregateEditor"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetIndexTotalFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetIndexTotalFormat"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetIndexTotalFormatEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetIndexTotalFormatEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetIndexTotalFormatEditor.CreateTotalFormat">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetPercentOfGrandTotalFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetPercentOfGrandTotalFormat"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetPercentOfGrandTotalFormatEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetPercentOfGrandTotalFormatEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetPercentOfGrandTotalFormatEditor.CreateTotalFormat">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetSumAggregateEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.SetSumAggregate"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetSumAggregateEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetSumAggregateEditor"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor.#ctor(System.Windows.Input.ICommand)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor"/> class.
            </summary>
            <param name="command">The command.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor.CreateTotalFormat">
            <summary>
            Creates a new <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> to be applied to the <see cref="T:Telerik.Windows.Controls.FieldList.Value"/>'s <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SetTotalFormatEditor.CanExecute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SortingEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.MoreSummarizationOptions"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SortingEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SortingEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SortingEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SortingEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.StringFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.StringFormats"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.StringFormatEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.StringFormatEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.StringFormatEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.StringFormatEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SummarizationEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.MoreSummarizationOptions"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SummarizationEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SummarizationEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SummarizationEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SummarizationEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Top10FilterEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.Top10Filter"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Top10FilterEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.Top10FilterEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Top10FilterEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Top10FilterEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.TotalFormatEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Value"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.ValueCommands.MoreTotalFormatOptions"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.TotalFormatEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.TotalFormatEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.TotalFormatEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.TotalFormatEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ValueFilterEditor">
            <summary>
            A class that handles the editing for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.Editing.LabelCommands.ValueFilter"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValueFilterEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ValueFilterEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValueFilterEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValueFilterEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DropIndicator">
            <summary>
            A Control that provides a basic visual indication for the drop location of a drag and drop operation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.DropIndicator.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DropPosition">
            <summary>
            Enumerates the possible drop positions relative to an item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropPosition.Over">
            <summary>
            Drop over or 'in' the item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropPosition.Left">
            <summary>
            Drop to the left of the item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropPosition.Top">
            <summary>
            Drop to the top of the item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropPosition.Right">
            <summary>
            Drop to the right of the item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.DropPosition.Bottom">
            <summary>
            Drop to the bottom of the item.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldContentTemplateSelector">
            <summary>
            A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> that selects special <see cref="T:System.Windows.DataTemplate"/> for the <see cref="T:Telerik.Windows.Controls.FieldList.ValuesPositionField"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldContentTemplateSelector.Default">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used by default.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldContentTemplateSelector.ValuesPosition">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> that will be used for the <see cref="T:Telerik.Windows.Controls.FieldList.ValuesPositionField"/> item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldContentTemplateSelector.SelectTemplate(System.Object,System.Windows.DependencyObject)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter">
            <summary>
            An <see cref="T:System.Windows.Data.IValueConverter"/> that converts a <see cref="T:Telerik.Windows.Controls.FieldList.FieldDragAction"/> to <see cref="T:System.Uri"/>. Generally used to display icons.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.ValueSource">
            <summary>
            Gets or set an <see cref="T:System.Uri"/> that the <see cref="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> would return for <see cref="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToValues"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.RowSource">
            <summary>
            Gets or set an <see cref="T:System.Uri"/> that the <see cref="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> would return for <see cref="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToRows"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.ColumnSource">
            <summary>
            Gets or set an <see cref="T:System.Uri"/> that the <see cref="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> would return for <see cref="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToColumns"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.FilterSource">
            <summary>
            Gets or set an <see cref="T:System.Uri"/> that the <see cref="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> would return for <see cref="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToFilters"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.RemoveSource">
            <summary>
            Gets or set an <see cref="T:System.Uri"/> that the <see cref="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)"/> would return for <see cref="F:Telerik.Windows.Controls.FieldList.FieldDragAction.Remove"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a <see cref="T:Telerik.Windows.Controls.FieldList.FieldDragAction"/> to <see cref="T:System.Uri"/> based on
            <see cref="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.ValueSource"/>, <see cref="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.RowSource"/>, <see cref="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.ColumnSource"/>, <see cref="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.FilterSource"/>, <see cref="P:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.RemoveSource"/>.
            </summary>
            <param name="value">The value to convert. Expected <see cref="T:Telerik.Windows.Controls.FieldList.FieldDragAction"/>.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>An <see cref="T:System.Uri"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldDragActionToSourceConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented. Throws <see cref="T:System.NotImplementedException"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldDragVisual">
            <summary>
            A <see cref="T:System.Windows.Controls.ContentControl"/> used to display a visual feedback during drag and drop of <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldDragVisual.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.FieldDragVisual"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldBox">
            <summary>
            An <see cref="T:System.Windows.Controls.ItemsControl"/> that displays items actively participating in pivot grouping. Supports Drag and provides visual feedback on drag operations.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldBox.HeaderProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.FieldBox.Header"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.FieldBox"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBox.Header">
            <summary>
            Gets or sets a value used as content for the header of this control. This is a DependencyProperty.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBox.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBox.IsItemItsOwnContainerOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBox.GetContainerForItemOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBox.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldBoxItem">
            <summary>
            A <see cref="T:System.Windows.Controls.ContentControl"/> used as item in the <see cref="T:Telerik.Windows.Controls.FieldList.FieldBox"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBoxItem.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldAdapter">
            <summary>
            Describes the hierarchical structure of the <see cref="T:Telerik.Windows.Controls.FieldList.ICompositeField"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldAdapter.GetItems(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldAdapter.GetItemAt(System.Object,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector">
            <summary>
            A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> that selects a DataTemplate based on a <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>'s <see cref="P:Telerik.Windows.Controls.FieldList.IField.FieldInfo"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.DimensionRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Dimension"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.FolderRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Folder"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.ValuesRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Measure"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.KpiRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Kpi"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.OtherRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Other"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.SelectableRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Selectable"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.NoneRoleTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> used for the <see cref="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.None"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldBoxItemRolePresenterTemplateSelector.SelectTemplate(System.Object,System.Windows.DependencyObject)">
            <summary>
            Selects a DataTemplate based on a <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>'s <see cref="P:Telerik.Windows.Controls.FieldList.IField.FieldInfo"/>.
            </summary>
            <param name="item">The item for which a template is selected. Expected to be <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>.</param>
            <param name="container">The container.</param>
            <returns>The selected <see cref="T:System.Windows.DataTemplate"/>.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldListViewModel">
            <summary>
            A class used as ViewModel for setting pivot grouping.
            </summary>
            <summary>
            A class used as ViewModel for setting pivot grouping.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.UpdateCommand">
            <summary>
            Gets an <see cref="T:System.Windows.Input.ICommand"/> that could be executed to force an update.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListViewModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.DataProvider">
            <summary>
            Gets or sets the underlying <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.DeferUpdates">
            <summary>
            Gets or sets a property that indicates if changes to the grouping settings would trigger computations immediately when invalidated or through the <see cref="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.UpdateCommand"/> execute. Based on the <see cref="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.DataProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.AggregatesLevel">
            <summary>
            Gets or sets the position where groups for the aggregates should be placed based on the <see cref="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.DataProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.AggregatesPosition">
            <summary>
            Gets or sets a value indicating where the aggregate groups should be positioned based on the <see cref="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.DataProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.Fields">
            <summary>
            Gets a <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> that holds a hierarchy of all <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> that may participate in pivot grouping.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.Filters">
            <summary>
            Gets a <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> that holds all <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>s that are used for filtering.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.ColumnLabels">
            <summary>
            Gets a <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> that holds all <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>s that are used for column labels.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.RowLabels">
            <summary>
            Gets a <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> that holds all <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>s that are used for row labels.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.Values">
            <summary>
            Gets a <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> that holds all <see cref="T:Telerik.Windows.Controls.FieldList.IField"/>s that are used for aggregation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.NoColumnAndDataDescriptions">
            <summary>
            Gets a boolean value indication whether the column and value descriptions are empty.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldListViewModel.NoColumnRowAndDataDescriptions">
            <summary>
            Gets a boolean value indication whether the column, row and value descriptions are empty.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ChildFilter">
            <summary>
            A class that identifies <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> items that are used for filtering and has been added to <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ReportFilters"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ChildLabel">
            <summary>
            A class that identifies <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> items that are used for grouping and has been added to <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.RowLabels"/> or <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ColumnLabels"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.DescriptionWrapper">
            <summary>
            A base class for the data items presenting the descriptions in the field list view model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DescriptionWrapper.ParentList">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DescriptionWrapper.Description">
            <summary>
            Gets the underlying <see cref="T:Telerik.Pivot.Core.DescriptionBase"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.DescriptionWrapper.FieldInfo">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> associated with the <see cref="P:Telerik.Windows.Controls.FieldList.DescriptionWrapper.Description"/>.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.DescriptionWrapper.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ValuesPositionDescription">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.IDescriptionBase"/> that identifies the Values position.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValuesPositionDescription.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValuesPositionDescription.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValuesPositionDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ValuesPositionDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.RemoveEditor">
            <summary>
            A class that handles the removal of a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.Remove"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RemoveEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.RemoveEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.RemoveEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ReloadDataEditor">
            <summary>
            A class that handles the data reloading raised by <see cref="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.ReloadData"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ReloadDataEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ReloadDataEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ReloadDataEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.SortEditor">
            <summary>
            A class that handles the sorting for a <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> object raised by <see cref="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.Sort"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SortEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.SortEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.SortEditor.Execute(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ShowFieldListViewModel">
            <summary>
            A view-model that encapsulates the inline editing of fields.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ShowFieldListViewModel.FieldListViewModel">
            <summary>
            Gets the view model of the inline field list.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ShowFieldListEditor">
            <summary>
            A class that handles the editing for the inline fields raised by <see cref="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.ShowFieldList"/> command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ShowFieldListEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.ShowFieldListEditor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ShowFieldListEditor.OnDialogCanExecute(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.ShowFieldListEditor.OnDialogExecuted(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.InlineFieldListContextMenuBehavior">
            <summary>
            A class that implements an attached behavior that handles <see cref="F:Telerik.Windows.Controls.FieldList.RoutedContextMenuEvents.RequestContextMenu"/> events, providing a context menu for elements part of the inline field list.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldListContextMenuBehavior.CreateContextMenu(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands">
            <summary>
            Provides a standard set of <see cref="T:Telerik.Windows.Controls.FieldList.InlineFieldBox"/> related commands.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.Sort">
            <summary>
            Gets the value that represents the Sort command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.Remove">
            <summary>
            Gets the value that represents the Remove command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.ReloadData">
            <summary>
            Gets the value that represents the Reload Data command.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxCommands.ShowFieldList">
            <summary>
            Gets the value that represents the Show Inline Field List command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldListBehavior">
            <summary>
            Allows for extending the functionality of a RadPivotGrid to provide inline grouping/filtering/sorting options.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldListBehavior.ShowInlineProperty">
            <summary>
            Represents the ShowInline attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldListBehavior.IsPivotTemplateAppliedProperty">
            <summary>
            Represents the PivotTemplateApplied attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListBehavior.GetShowInline(System.Windows.DependencyObject)">
            <summary>
            Gets a boolean value indicating whether the inline field list is enabled.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldListBehavior.SetShowInline(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets a boolean value indicating whether the inline field list is enabled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.InlineFieldBox">
            <summary>
            An <see cref="T:System.Windows.Controls.ItemsControl"/> that displays items actively participating in the inline pivot grouping. Supports Drag and provides visual feedback on drag operations.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBox.EmptyTextProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.EmptyText"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnAndDataDescriptionsInViewModelProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnAndDataDescriptionsInViewModel"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnRowAndDataDescriptionsInViewModelProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnRowAndDataDescriptionsInViewModel"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.InlineFieldBox"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.EmptyText">
            <summary>
            Gets or sets the text displayed when Items collection is empty.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnAndDataDescriptionsInViewModel">
            <summary>
            Gets a boolean value indicating whether the Column and data descriptions are empty.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBox.NoColumnRowAndDataDescriptionsInViewModel">
            <summary>
            Gets a boolean value indicating whether the Column, Row and data descriptions are empty.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.IsItemItsOwnContainerOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.GetContainerForItemOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBox.ChangeVisualState(System.Boolean,System.String)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">True to use a VisualTransition to transition between states; otherwise, false.</param>
            <param name="stateName">The name of the state to transition into.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem">
            <summary>
            A <see cref="T:System.Windows.Controls.ContentControl"/> used as item in the <see cref="T:Telerik.Windows.Controls.FieldList.InlineFieldBox"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.SortOrderProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.SortOrder"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.GroupFilterProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.GroupFilter"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.IsValuesPositionFieldProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.IsValuesPositionField"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.SortOrder">
            <summary>
            Gets or sets a value indicating the sort order for descriptions, which can be sorted.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.GroupFilter">
            <summary>
            Gets or sets a group filter for descriptions, which can be filtered.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.IsValuesPositionField">
            <summary>
            Gets a boolean value indicating whether the DataContext of this item is a ValuesPositionDescription.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event.
            This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.InlineFieldBoxItem.ChangeVisualState(System.Boolean,System.String)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">True to use a VisualTransition to transition between states; otherwise, false.</param>
            <param name="stateName">The name of the state to transition into.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldDragAction">
            <summary>
            Enumerates the possible outcomes of an <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> drag action.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.None">
            <summary>
            Nothing would happen.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.Remove">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> will be removed from its holding collection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToRows">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> will be moved to the <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.RowLabels"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToColumns">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> will be moved to the <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ColumnLabels"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToFilters">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> will be moved to the <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ReportFilters"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldDragAction.MoveToValues">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> will be moved to the <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.Values"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldCollection">
            <summary>
            An read only observable collection of <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldCollection.Parent">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel"/> this collection is part of.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldCollection.FieldCollectionType">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Controls.FieldList.FieldCollection.FieldCollectionType"/> that describes what role has this collection in the parent <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldCollectionType">
            <summary>
            Enumerates the roles <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> can take in the <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.Fields">
            <summary>
            Identifies a collection with all possible fields.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ReportFilters">
            <summary>
            Identifies a collection with the report filter fields.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.RowLabels">
            <summary>
            Identifies a collection with the row label fields.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ColumnLabels">
            <summary>
            Identifies a collection with the column label fields.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.Values">
            <summary>
            Identifies a collection with the value fields.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.FieldPayload">
            <summary>
            A <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> payload used in drag operations between the <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/> areas.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldPayload.#ctor(Telerik.Windows.Controls.FieldList.IField)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.FieldPayload"/> class.
            </summary>
            <param name="field"></param>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldPayload.DraggedField">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> this payload carries.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldPayload.RemoveFromSource">
            <summary>
            Gets or sets a value that indicates if the <see cref="P:Telerik.Windows.Controls.FieldList.FieldPayload.DraggedField"/> should be removed upon drag completion.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldPayload.DragAction">
            <summary>
            Gets the an <see cref="T:Telerik.Windows.Controls.FieldList.FieldDragAction"/> that identifies the expected outcome if drop occurs with the current settings.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.FieldPayload.AllowedEffects">
            <summary>
            Gets the allowed <see cref="T:System.Windows.DragDropEffects"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldPayload.SetDestination(Telerik.Windows.Controls.FieldList.FieldCollection,System.Int32)">
            <summary>
            Set the potential destination for the <see cref="P:Telerik.Windows.Controls.FieldList.FieldPayload.DraggedField"/>.
            </summary>
            <param name="destinationCollection">The destination <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/>.</param>
            <param name="index">The destination index.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldPayload.SetDestination(Telerik.Windows.Controls.FieldList.FieldCollection)">
            <summary>
            Set the potential destination for the <see cref="P:Telerik.Windows.Controls.FieldList.FieldPayload.DraggedField"/>.
            </summary>
            <param name="destinationCollection">The destination <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldPayload.OnDrop">
            <summary>
            Call to notify the drag and drop did drop. Call this method once to apply the <see cref="P:Telerik.Windows.Controls.FieldList.FieldPayload.DragAction"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.FieldPayload.OnDropComplete">
            <summary>
            Call to notify the drag and drop did drop complete. Call this method once to apply the <see cref="P:Telerik.Windows.Controls.FieldList.FieldPayload.DragAction"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Field">
            <summary>
            An implementation of <see cref="T:Telerik.Windows.Controls.FieldList.ICompositeField"/>. Represents a node in <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> hierarchy.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.FieldList.Field.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.Children">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.HasChildren">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.ParentList">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.DisplayName">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.IsUsed">
            <summary>
            Gets or sets a value that indicates if this item participates in the pivot grouping.
            When set to true its <see cref="P:Telerik.Windows.Controls.FieldList.Field.FieldInfo"/> will be used to create the appropriate entries in a <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.FieldInfo">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Field.Role">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Field.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Field.OnPropertyChanged(System.String)">
            <summary>
            Notifies that the property with name <paramref name="propertyName"/> has changed.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ICompositeField">
            <summary>
            An interface describing a hierarchical item that can participate in pivot grouping. It may have nested <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> or <see cref="T:Telerik.Windows.Controls.FieldList.ICompositeField"/> items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ICompositeField.Children">
            <summary>
            Gets the nested <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> elements.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ICompositeField.HasChildren">
            <summary>
            Gets a value that indicates if this <see cref="T:Telerik.Windows.Controls.FieldList.ICompositeField"/> has children.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ICompositeField.Role">
            <summary>
            Gets the role of this node.
            </summary>
            <value>The role.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.IField">
            <summary>
            An interface describing an item that can participate in pivot grouping.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.IField.FieldInfo">
            <summary>
            Gets the field info associated with this instance.
            </summary>
            <value>The field info.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.IField.ParentList">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.FieldCollection"/> this <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> belongs to.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Label">
            <summary>
            A class that identifies <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> items that are used for grouping and has been added to <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.RowLabels"/> or <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ColumnLabels"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.FieldList.Label.#ctor(Telerik.Pivot.Core.DescriptionBase,Telerik.Windows.Controls.FieldList.FieldCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.FieldList.Label"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Label.Children">
            <summary>
            Gets or sets the children collection of this instance.
            </summary>
            <value>The children.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Filter">
            <summary>
            A class that identifies <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> items that are used for filtering and has been added to <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ReportFilters"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Filter.Children">
            <summary>
            Gets or sets the children collection of this instance.
            </summary>
            <value>The children.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.Value">
            <summary>
            A class that identifies <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> item, that is used to place groups for aggregate values, and has been added to <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.RowLabels"/> or <see cref="F:Telerik.Windows.Controls.FieldList.FieldCollectionType.ColumnLabels"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.Value.Children">
            <summary>
            Gets the children collection of this instance.
            </summary>
            <value>The children.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.FieldList.ValuesPositionField">
            <summary>
            A class used to present a <see cref="T:Telerik.Windows.Controls.FieldList.IField"/> where the values from aggregates will be presented.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.FieldList.ValuesPositionField.Children">
            <summary>
            Gets the children collection of this instance.
            </summary>
            <value>The children.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.EnumToBooleanConverter">
            <summary>
            Represents converter, which converts <see cref="T:System.Enum"/> types to and from a
            boolean value using the given parameter.
            </summary>
            <remarks>
            The <see cref="T:System.Enum"/> can be with <see cref="T:System.FlagsAttribute"/>. Characters: ',' or ';' 
            can be used to split multiple values passed in the given parameter.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.EnumToBooleanConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.EnumToBooleanConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.EnumToVisibilityConverter">
            <summary>
            Represents converter, which converts <see cref="T:System.Enum"/> types to and from a
            boolean value using the given parameter.
            </summary>
            <remarks>
            The <see cref="T:System.Enum"/> can be with <see cref="T:System.FlagsAttribute"/>. Characters: ',' or ';' 
            can be used to split multiple values passed in the given parameter.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.EnumToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.EnumToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Not implemented.
            </summary>
            <param name="value"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.RadPivotFieldList">
            <summary>
            A <see cref="T:System.Windows.Controls.Control"/> that is used to setup a pivot grouping.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPivotFieldList.DataProviderProperty">
            <summary>
            Identifies the DataProvider DependencyProperty.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPivotFieldList.IsBusyProperty">
            <summary>
            Identifies the IsBusy read-only dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPivotFieldList.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadPivotFieldList"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPivotFieldList.ViewModel">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.FieldList.FieldListViewModel"/> associated with this control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPivotFieldList.DataProvider">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.RadPivotFieldList.DataProvider"/> associated with this control. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPivotFieldList.IsBusy">
            <summary>
            Gets a value indicating whether this instance is processing or loading data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPivotFieldList.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.RadPivotFieldList.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPivotFieldList.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
    </members>
</doc>
