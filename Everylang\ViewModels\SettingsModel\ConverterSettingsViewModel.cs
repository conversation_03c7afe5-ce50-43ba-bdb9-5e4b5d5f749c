﻿using Everylang.App.Converter;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class ConverterSettingsViewModel : ViewModelBase
    {

        private bool _isPro;

        public bool jgebhdhs
        {
            get { return _isPro; }
            set
            {
                _isPro = value;
                if (!value)
                {
                    ConverterManager.Instance.Stop();
                }
                else
                {
                    ConverterManager.Instance.Start();
                }
                base.OnPropertyChanged();
            }
        }

        public string ShortcutOpenWindow
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterOpenWindowShortcut);
            }
            set
            {
                SettingsManager.Settings.ConverterOpenWindowShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutExpresion
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterExpresionShortcut);
            }
            set
            {
                SettingsManager.Settings.ConverterExpresionShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutTransliteration
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterTransliterationShortcut);
            }
            set
            {
                SettingsManager.Settings.ConverterTransliterationShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutEncloseTextQuotationMarks
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut);
            }
            set
            {
                SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutCamelCase
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutCamelCase);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutCamelCase = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutPascalCase
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutPascalCase);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutPascalCase = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutKebabCase
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutKebabCase);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutKebabCase = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutSnakeCase
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutSnakeCase);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutSnakeCase = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutReplaceSelText
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutReplaceSelText);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutReplaceSelText = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutOpenWindowCaps
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutCapsOpenWindow);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutCapsOpenWindow = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutSelectedCaps
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutCapsInvert);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutCapsInvert = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutSelectedCapsUp
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutCapsUp);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutCapsUp = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutSelectedCapsDown
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterShortcutCapsDown);
            }
            set
            {
                SettingsManager.Settings.ConverterShortcutCapsDown = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutFirstLetterToDown
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterFirstLetterToDown);
            }
            set
            {
                SettingsManager.Settings.ConverterFirstLetterToDown = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutFirstLetterToUp
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.ConverterFirstLetterToUp);
            }
            set
            {
                SettingsManager.Settings.ConverterFirstLetterToUp = value;
                base.OnPropertyChanged();
            }
        }

    }
}
