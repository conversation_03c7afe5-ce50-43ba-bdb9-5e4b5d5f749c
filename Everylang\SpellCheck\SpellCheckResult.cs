﻿using Everylang.App.Utilities.NetRequest;
using System.Collections.Generic;
using System.Windows.Documents;
using System.Windows.Media;

namespace Everylang.App.SpellCheck
{
    internal class SpellCheckResult : WebResult
    {
        internal SpellCheckResult()
        {
            SpellCheckDictionary = new List<SpellCheckDictionary>();
            ResultTextList = new List<string>();
        }

        internal List<SpellCheckDictionary> SpellCheckDictionary;

        internal List<string> ResultTextList;

        internal string SourceText;

        internal bool IsOk;
    }

    internal class SpellCheckDictionary
    {
        internal SpellCheckDictionary()
        {
            Suggestions = new List<string>();
            ChilDictionaries = new List<SpellCheckDictionary>();
        }

        internal TextRange TextRange { get; set; }

        internal int Pos { get; set; }

        internal int Len { get; set; }

        internal string Key { get; set; }

        internal string ResultKey { get; set; }

        internal List<SpellCheckDictionary> ChilDictionaries;

        internal bool Ok { get; set; }


        internal List<string> Suggestions;

        internal void SetResultText(bool child)
        {
            if (ResultKey != null)
            {
                TextRange.Text = ResultKey;
                if (child && ChilDictionaries.Count > 0)
                {
                    foreach (var spellCheckDictionary in ChilDictionaries)
                    {
                        if (!spellCheckDictionary.Ok)
                        {
                            spellCheckDictionary.TextRange.Text = ResultKey;
                        }
                    }
                }
            }
        }

        internal void SetStatus(bool status, bool child)
        {
            Ok = status;
            if (child && ChilDictionaries.Count > 0)
            {
                foreach (var spellCheckDictionary in ChilDictionaries)
                {
                    spellCheckDictionary.Ok = status;
                }
            }
        }

        internal void Undo()
        {
            TextRange.Text = Key;
            Ok = false;
        }

        internal void SetColorToKey()
        {
            TextRange.ApplyPropertyValue(TextElement.ForegroundProperty, new SolidColorBrush(Colors.Red));
            if (ChilDictionaries.Count > 0)
            {
                foreach (var spellCheckDictionary in ChilDictionaries)
                {
                    spellCheckDictionary.TextRange.ApplyPropertyValue(TextElement.ForegroundProperty, new SolidColorBrush(Colors.Salmon));
                }
            }
        }
    }
}
