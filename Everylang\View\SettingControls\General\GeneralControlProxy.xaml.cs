﻿using Everylang.App.ViewModels;
using System.Windows;

namespace Everylang.App.View.SettingControls.General
{
    /// <summary>
    /// Interaction logic for GeneralControlProxy.xaml
    /// </summary>
    internal partial class GeneralControlProxy
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(GeneralControlProxy));

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        internal GeneralControlProxy()
        {
            InitializeComponent();
            txtPassword.Password = VMContainer.Instance.GeneralSettingsViewModel.ProxyPassword;
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }

    }
}
