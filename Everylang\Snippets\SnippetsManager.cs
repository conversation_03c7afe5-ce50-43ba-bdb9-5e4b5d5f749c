﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.Common.Utilities;
using NHotkey;
using System;

namespace Everylang.App.Snippets
{
    class SnippetsManager : IDisposable
    {
        private bool _isHotKeySnippetsView;
        private static SnippetsManager? _instance;

        internal static SnippetsManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SnippetsManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            StartSnippetsHistory();
        }


        private void StartSnippetsHistory()
        {
            if (SettingsManager.Settings.SnippetsIsOn && !_isHotKeySnippetsView)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SnippetsShowAllShortcut), SettingsManager.Settings.SnippetsShowAllShortcut, PressedSnippetsView);
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SnippetsAddNewShortcut), SettingsManager.Settings.SnippetsAddNewShortcut, PressedSnippetsAddNew);
                _isHotKeySnippetsView = true;
            }
        }

        internal void PressedSnippetsView(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            GlobalEventsApp.OnEventSnippetsView();
        }

        internal void PressedSnippetsAddNew(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            ForegroundWindow.StoreForegroundWindow();
            string? text = ClipboardOperations.GetSelectionText();
            if (!string.IsNullOrEmpty(text))
            {
                GlobalEventsApp.OnEventAddNewSnippets(text);
            }
        }


        internal void Stop()
        {
            SnippetsStop();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        internal void SnippetsStop()
        {
            _isHotKeySnippetsView = false;
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SnippetsShowAllShortcut));
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SnippetsAddNewShortcut));
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
