﻿using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.IO;
using System.Windows;

namespace Everylang.App.Data
{
    internal static class DataBaseManager
    {
        internal static LiteDatabase LiteDb = null!;

        private const string Password = "fm33f9h34f348rl9j";

        internal static string? DbPath { get; set; }

        internal static void Init()
        {
            LiteDb = new LiteDatabase(DbConStr);
            LiteDb.Rebuild(new LiteDB.Engine.RebuildOptions() { Password = Password });
            if (DbPath != null) DeleteBackupDbFile(DbPath);
        }

        internal static void DeleteAllData()
        {
            try
            {
                LiteDb.Dispose();
                if (DbPath != null) File.Delete(DbPath);
                LiteDb = new LiteDatabase(DbConStr);
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static ConnectionString DbConStr
        {
            get
            {
                if (DbPath == null) return null!;
                var connString = new ConnectionString
                {
                    Filename = DbPath,
                    Connection = ConnectionType.Direct,
                    Password = Password,
                    Upgrade = true
                };
                return connString;
            }
        }

        private static void DeleteBackupDbFile(string fileName)
        {
            string backupFilename = Path.Combine(Path.GetDirectoryName(fileName), Path.GetFileNameWithoutExtension(fileName) + "-backup" + Path.GetExtension(fileName));
            if (File.Exists(backupFilename))
            {
                try
                {
                    File.Delete(backupFilename);
                }
                catch (Exception e)
                {
                    MessageBox.Show($"Error deleting backup file: {e.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        internal static void Dispose()
        {
            LiteDb.Dispose();
        }
    }
}
