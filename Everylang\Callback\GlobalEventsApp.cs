﻿using Everylang.App.LangFlag;
using Everylang.App.SpellCheck;
using System;
using System.Drawing;

namespace Everylang.App.Callback
{
    internal class GlobalEventsApp
    {
        internal static event Action<string>? EventAddDiary;
        internal static event Action<string>? EventAddNewSnippets;
        internal static event Action? EventAddNewSnippetsUpdate;
        internal static event Action<string>? EventAddNewHistory;
        internal static event Action? EventSnippetsView;
        internal static event Action? EventUpdateAction;
        internal static event Action? EventClipboardView;
        internal static event Action? EventDiaryView;
        internal static event Action? EventDown;
        internal static event Action<string>? EventErrorRegisterHotkey;
        internal static event Action<string>? EventGoToMainWindowTranslate;
        internal static event Action<IntPtr>? EventKeyboardLayoutChanged;
        internal static event Action<IntPtr>? EventKeyboardLayoutChangedForLangFlag;
        internal static event Action<Icon?>? EventLangLayoutChanged;
        internal static event Action<StatusCapsLockButton>? EventCapsLock;
        internal static event Action<StatusNumLockButton>? EventNumLock;
        internal static event Action? EventMinimizeToTray;
        internal static event Action<Bitmap>? EventOcrMainWindow;
        internal static event Action? EventOpenSettingsDiary;
        internal static event Action? EventOpenSettingsClipboard;
        internal static event Action? EventOpenSettingsTranslation;
        internal static event Action? EventOpenSnippetsList;
        internal static event Action? EventOpenSnippetsSettings;
        internal static event Action? EventOpenListDiary;
        internal static event Action? EventOpenListClipboard;
        internal static event Action<string>? EventPro;
        internal static event Action<string>? EventProSendEarlyActive;
        internal static event Action? EventProExp;
        internal static event Action? EventProLastDay;
        internal static event Action? EventProStartEv;
        internal static event Action? EventProStart;
        internal static event Action<object, string, string>? EventPropPro;
        internal static event Action<bool>? EventRestart;
        internal static event Action? EventStopWorking;
        internal static event Action<SpellCheckResult>? EventSpellCheck;
        internal static event Action<string, bool>? EventSpellCheckForMain;
        internal static event Action? EventStart;
        internal static event Action? EventTranslationHistoryView;
        internal static event Action<string>? EventUniWindowTranslate;
        internal static event Action<string, bool>? EventUniWindowSpellCheck;
        internal static event Action<string, bool>? EventUpdateAvailable;
        internal static event Action? EventFunctionOrder;
        internal static event Action? EventOnlyFavoriteLangForTranslate;
        internal static event Action<string>? SpellCheckReplaceAction;
        internal static event Action<string>? SwitchAcceptAction;

        internal static void OnEventAddDiary(string obj)
        {
            EventAddDiary?.Invoke(obj);
        }

        internal static void OnEventAddNewSnippets(string obj)
        {
            EventAddNewSnippets?.Invoke(obj);
        }

        internal static void OnEventAddNewSnippetsUpdate()
        {
            EventAddNewSnippetsUpdate?.Invoke();
        }


        internal static void OnEventAddNewHistory(string obj)
        {
            EventAddNewHistory?.Invoke(obj);
        }

        internal static void OnEventSnippetsView()
        {
            EventSnippetsView?.Invoke();
        }

        internal static void OnEventUpdateAction()
        {
            EventUpdateAction?.Invoke();
        }

        internal static void OnEventClipboardView()
        {
            EventClipboardView?.Invoke();
        }

        internal static void OnEventDiaryView()
        {
            EventDiaryView?.Invoke();
        }

        internal static void OnEventDown()
        {
            EventDown?.Invoke();
        }

        internal static void OnEventErrorRegisterHotkey(string obj)
        {
            EventErrorRegisterHotkey?.Invoke(obj);
        }

        internal static void OnEventGoToMainWindowTranslate(string obj)
        {
            EventGoToMainWindowTranslate?.Invoke(obj);
        }

        internal static void OnEventKeyboardLayoutChanged(IntPtr obj)
        {
            EventKeyboardLayoutChanged?.Invoke(obj);
        }

        internal static void OnEventKeyboardLayoutChangedForLangFlag(IntPtr obj)
        {
            EventKeyboardLayoutChangedForLangFlag?.Invoke(obj);
        }

        internal static void OnEventLangLayoutChanged(Icon? obj)
        {
            EventLangLayoutChanged?.Invoke(obj);
        }

        internal static void OnEventCapsLock(StatusCapsLockButton obj)
        {
            EventCapsLock?.Invoke(obj);
        }

        internal static void OnEventNumLock(StatusNumLockButton obj)
        {
            EventNumLock?.Invoke(obj);
        }

        internal static void OnEventMinimizeToTray()
        {
            EventMinimizeToTray?.Invoke();
        }

        internal static void OnEventOcrMainWindow(Bitmap obj)
        {
            EventOcrMainWindow?.Invoke(obj);
        }

        internal static void OnEventOpenSettingsDiary()
        {
            EventOpenSettingsDiary?.Invoke();
        }

        internal static void OnEventOpenSettingsClipboard()
        {
            EventOpenSettingsClipboard?.Invoke();
        }

        internal static void OnEventOpenSettingsTranslation()
        {
            EventOpenSettingsTranslation?.Invoke();
        }

        internal static void OnEventOpenSnippetsList()
        {
            EventOpenSnippetsList?.Invoke();
        }

        internal static void OnEventOpenSnippetsSettings()
        {
            EventOpenSnippetsSettings?.Invoke();
        }

        internal static void OnEventOpenListDiary()
        {
            EventOpenListDiary?.Invoke();
        }

        internal static void OnEventOpenListClipboard()
        {
            EventOpenListClipboard?.Invoke();
        }

        internal static void OnEventPro(string obj)
        {
            EventPro?.Invoke(obj);
        }

        internal static void OnEventProSendEarlyActive(string obj)
        {
            EventProSendEarlyActive?.Invoke(obj);
        }

        internal static void OnEventProExp()
        {
            EventProExp?.Invoke();
        }

        internal static void OnEventProLastDay()
        {
            EventProLastDay?.Invoke();
        }

        internal static void OnEventProStartEv()
        {
            EventProStartEv?.Invoke();
        }

        internal static void OnEventProStart()
        {
            EventProStart?.Invoke();
        }

        internal static void OnEventPropPro(object arg1, string arg2, string arg3)
        {
            EventPropPro?.Invoke(arg1, arg2, arg3);
        }

        internal static void OnEventRestart(bool obj)
        {
            EventRestart?.Invoke(obj);
        }

        internal static void OnEventStopWorking()
        {
            EventStopWorking?.Invoke();
        }

        internal static void OnEventSpellCheck(SpellCheckResult obj)
        {
            EventSpellCheck?.Invoke(obj);
        }

        internal static void OnEventSpellCheckForMain(string arg1, bool arg2)
        {
            EventSpellCheckForMain?.Invoke(arg1, arg2);
        }

        internal static void OnEventStart()
        {
            EventStart?.Invoke();
        }

        internal static void OnEventTranslationHistoryView()
        {
            EventTranslationHistoryView?.Invoke();
        }

        internal static void OnEventUniWindowTranslate(string obj)
        {
            EventUniWindowTranslate?.Invoke(obj);
        }

        internal static void OnEventUniWindowSpellCheck(string arg1, bool arg2)
        {
            EventUniWindowSpellCheck?.Invoke(arg1, arg2);
        }

        internal static void OnEventUpdateAvailable(string version)
        {
            EventUpdateAvailable?.Invoke(version, false);
        }

        internal static void OnEventUpdateBetaAvailable(string version)
        {
            EventUpdateAvailable?.Invoke(version, true);
        }

        internal static void OnEventFunctionOrder()
        {
            EventFunctionOrder?.Invoke();
        }

        internal static void OnEventOnlyFavoriteLangForTranslate()
        {
            EventOnlyFavoriteLangForTranslate?.Invoke();
        }

        internal static void OnSpellCheckReplaceAction(string obj)
        {
            SpellCheckReplaceAction?.Invoke(obj);
        }

        internal static void OnSwitchAcceptAction(string obj)
        {
            SwitchAcceptAction?.Invoke(obj);
        }
    }
}
