﻿using Everylang.App.Callback;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels
{
    public class HistoryViewModel : ViewModelBase
    {
        public RadObservableCollection<HistoryTranslationModel> AllHistoryItems { get; set; }
        internal List<HistoryTranslationModel> SelectedItems { get; set; }

        internal DelegateCommand DeleteSelectedCommand
        {
            get;
            private set;
        }

        public DelegateCommand ClearAllCommand
        {
            get;
            private set;
        }
        public HistoryViewModel()
        {
            DeleteSelectedCommand = new DelegateCommand(DeleteSelected);
            ClearAllCommand = new DelegateCommand(ClearAll);
            AllHistoryItems = new RadObservableCollection<HistoryTranslationModel>();
            SelectedItems = new List<HistoryTranslationModel>();
            GlobalEventsApp.EventAddNewHistory += AddNewHistory;
            GetAllHistoryFromDb();
        }

        private void ClearAll(object o)
        {
            AllHistoryItems.Clear();
            TranslationHistoryManager.ClearHistoryTranslation();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }

        internal void DeleteSelected(object o)
        {
            var listForRem = new List<HistoryTranslationModel>();
            for (int i = 0; i < SelectedItems.Count; i++)
            {
                listForRem.Add(SelectedItems[i]);
            }
            TranslationHistoryManager.DelHistoryTranslation(listForRem);
            foreach (var item in listForRem)
            {
                AllHistoryItems.Remove(item);
            }
            SelectedItems.Clear();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }


        public bool IsEnabled
        {
            get
            {
                return SettingsManager.Settings.TranslateHistoryIsOn;
            }
            set
            {

                SettingsManager.Settings.TranslateHistoryIsOn = value;
                base.OnPropertyChanged();
                if (value)
                {
                    GetAllHistoryFromDb();
                }
            }

        }

        internal bool IsSelectedNotNull
        {
            get { return SelectedItems.Count != 0; }
        }

        internal bool IsNotNullAll
        {
            get { return AllHistoryItems.Count != 0; }
        }

        private void GetAllHistoryFromDb()
        {
            var collection = TranslationHistoryManager.GetAllHistory();
            AllHistoryItems.Clear();
            AllHistoryItems.AddRange(collection.OrderBy(x => x.DateTime).Reverse());
        }

        private async void AddNewHistory(string text)
        {
            if (IsEnabled)
            {
                if (!string.IsNullOrEmpty(text))
                {
                    var shortText = text.Length > 150 ? text.Substring(0, 150) + "......" : text;
                    shortText = shortText.Replace(Environment.NewLine, " ");
                    await AddHistory(new HistoryTranslationModel() { DateText = DateTime.Now.Date.ToString("D"), Text = text, ShortText = shortText, Application = "", DateTime = DateTime.Now });
                }
            }
        }

        private async Task AddHistory(HistoryTranslationModel? historyTranslationModel)
        {
            await Task.Run(async () =>
            {
                try
                {
                    var copyAllHistoryItems = new List<HistoryTranslationModel>(AllHistoryItems);
                    var dataModelEmpy = copyAllHistoryItems.FirstOrDefault(x => x.ShortText == null || x.Text == null || x.DateText == null);
                    if (dataModelEmpy != null)
                    {
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        {
                            AllHistoryItems.Remove(dataModelEmpy);
                        }));
                    }

                    var historyDataModelOld = copyAllHistoryItems.FirstOrDefault(x => String.Equals(x.Text?.Trim(), historyTranslationModel?.Text?.Trim(), StringComparison.CurrentCultureIgnoreCase));
                    if (historyDataModelOld != null)
                    {
                        TranslationHistoryManager.DelHistoryTranslation(new List<HistoryTranslationModel>() { historyDataModelOld });
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        {
                            AllHistoryItems.Remove(historyDataModelOld);
                        }));

                    }

                    if (historyTranslationModel?.ShortText == null) return;
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                    {
                        AllHistoryItems.Insert(0, historyTranslationModel);
                    }));

                    TranslationHistoryManager.AddHistoryTranslation(historyTranslationModel);

                    if (AllHistoryItems.Count > 50)
                    {
                        var listForRem = AllHistoryItems.Skip(50).ToList();
                        TranslationHistoryManager.DelHistoryTranslation(listForRem);
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        {
                            AllHistoryItems.RemoveRange(listForRem);
                        }));
                    }
                    base.OnPropertyChanged(nameof(IsSelectedNotNull));
                    base.OnPropertyChanged(nameof(IsNotNullAll));
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        }
    }
}
