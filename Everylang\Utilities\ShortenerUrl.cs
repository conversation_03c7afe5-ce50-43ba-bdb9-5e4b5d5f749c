﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using System;
using System.Net;
using System.Threading.Tasks;

namespace Everylang.App.Utilities
{
    class ShortenerUrl
    {
        internal static Task<string> Shorten(string urlLong)
        {
            return Task.Run(() =>
            {
                string tinyUrl = "";
                System.Uri address = new System.Uri("http://tinyurl.com/api-create.php?url=" + urlLong);
                try
                {
                    WebClient client = new WebClient();
                    client.Proxy = NetLib.GetProxy();
                    tinyUrl = client.DownloadString(address);
                }
                catch (Exception e)
                { }

                return tinyUrl;
            });

        }

        internal static string ShortenExpand(string url)
        {
            try
            {
                url = url.Replace("http:", "https:");
                HttpWebRequest request = (HttpWebRequest)(WebRequest.Create(url));
                request.UserAgent = SettingsManager.UserAgent;
                request.AllowAutoRedirect = false;
                request.Proxy = NetLib.GetProxy();
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    return response.Headers["Location"];
                }
            }
            catch (Exception e)
            {

            }
            return url;
        }

    }
}
