﻿using Everylang.App.Data.DataModel;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using Vanara.PInvoke;
using User32 = Vanara.PInvoke.User32;

namespace Everylang.App.Utilities
{
    internal static class CheckActiveProcessFileName
    {
        internal static bool CheckDiary()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnDiary)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckConverter()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnConverter)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckClipboard()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnClipboard)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckClipboardImage()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnClipboardImage)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckAutoSwitch()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnAutoSwitch)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckLayoutFlagForMouse()
        {

            var dataModel = GetModelExc(true);
            if (dataModel != null)
            {
                if (!dataModel.IsOnLayoutFlag)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckLayoutFlagForCaret()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnLayoutFlag)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckLayoutSwitcher()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnLayoutSwitcher)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckSpellCheckWhileTyping()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnSpellCheckWhileTyping)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckLayoutSwitcherForHotkey()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnLayoutSwitcher)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckLayoutSmartClick()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnSmartClick)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckAutochange()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnAutochange)
                {
                    return false;
                }
            }
            return true;
        }

        internal static bool CheckHotKeys()
        {
            var dataModel = GetModelExc();
            if (dataModel != null)
            {
                if (!dataModel.IsOnHotKeys)
                {
                    return false;
                }
            }
            return true;
        }

        internal static ProgramsSetLayoutDataModel? GetModelCurrentApp()
        {
            if (VMContainer.Instance.ProgramsSetLayoutViewModel.ProgramsSetLayoutList == null || VMContainer.Instance.ProgramsSetLayoutViewModel.ProgramsSetLayoutList.Count == 0)
            {
                return null;
            }
            var processName = GetActiveProcessName();
            var title = GetActiveProcessName(false, true);
            ProgramsSetLayoutDataModel? dataModel = null;
            if (title != "")
            {
                dataModel = VMContainer.Instance.ProgramsSetLayoutViewModel.ProgramsSetLayoutList.Where(x => !string.IsNullOrEmpty(x?.Title)).FirstOrDefault(x => x?.Title != null && title != null && title.Contains(x.Title));
            }
            if (dataModel == null)
            {
                dataModel = VMContainer.Instance.ProgramsSetLayoutViewModel.ProgramsSetLayoutList.Where(x => !string.IsNullOrEmpty(x?.Program)).FirstOrDefault(x => x?.Program != null && processName != null && processName.Contains(x.Program));
            }
            return dataModel;
        }

        private static ProgramsExceptionsDataModel? GetModelExc(bool windowFromPoint = false)
        {
            if (VMContainer.Instance.ProgramsExceptionsViewModel.ProgramsExceptionsList.Count == 0)
            {
                return null;
            }
            var processName = GetActiveProcessName(windowFromPoint);
            var title = GetActiveProcessName(windowFromPoint, true);
            ProgramsExceptionsDataModel? dataModel = null;
            if (title != "")
            {
                dataModel = VMContainer.Instance.ProgramsExceptionsViewModel.ProgramsExceptionsList.Where(x => !string.IsNullOrEmpty(x?.Title)).FirstOrDefault(x => x?.Title != null && title != null && title.Contains(x.Title));
            }
            if (processName != null && dataModel == null)
            {
                dataModel = VMContainer.Instance.ProgramsExceptionsViewModel.ProgramsExceptionsList.Where(x => !string.IsNullOrEmpty(x?.Program)).FirstOrDefault(x => x?.Program != null && processName.Contains(x.Program));
            }
            return dataModel;
        }

        private static string? _lastProcessName = "";
        private static IntPtr _hwnd;

        internal static string? GetActiveProcessName(bool windowFromPoint = false, bool windowTitle = false)
        {
            try
            {
                IntPtr hwnd;
                if (windowFromPoint)
                {
                    var point = MousePosition.GetMousePoint();
                    hwnd = User32.WindowFromPoint(new Vanara.PInvoke.POINT((int)point.X, (int)point.Y)).DangerousGetHandle();
                }
                else
                {
                    hwnd = User32.GetForegroundWindow().DangerousGetHandle();
                }

                if (_hwnd == hwnd && !windowTitle)
                {
                    return _lastProcessName;
                }

                _hwnd = hwnd;
                if (windowTitle)
                {
                    User32.GetWindowThreadProcessId(hwnd, out var id);
                    StringBuilder stringBuilder = new StringBuilder(256);
                    User32.GetWindowText(new MainWindowFinder().FindMainWindow((int)id),
                        stringBuilder, stringBuilder.Capacity);
                    return stringBuilder.ToString();
                }

                _lastProcessName = InnerGetActiveProcessName(hwnd);
                return _lastProcessName;
            }
            catch
            {
                // ignore
            }
            return "";

        }

        internal static string InnerGetActiveProcessName(IntPtr hwnd)
        {
            try
            {
                User32.GetWindowThreadProcessId(hwnd, out var id);
                var foregroundProcess = GetProcessName(id);
                if (foregroundProcess.Contains("ApplicationFrameHost"))
                {
                    foregroundProcess = GetRealProcess(hwnd);
                }
                return foregroundProcess;
            }
            catch
            {
                // ignore
            }

            return "";
        }

        internal static string InnerGetActiveProcessName(uint id)
        {
            var foregroundProcess = GetProcessName(id);
            if (foregroundProcess.Contains("ApplicationFrameHost"))
            {
                foregroundProcess = GetRealProcess(Process.GetProcessById((int)id).MainWindowHandle);
            }
            return foregroundProcess;
        }

        [DllImport("psapi.dll")]
        static extern uint GetModuleFileNameEx(IntPtr hProcess, IntPtr hModule, [Out] StringBuilder lpBaseName, [In][MarshalAs(UnmanagedType.U4)] int nSize);

        internal static string GetProcessName(uint pid)
        {
            string result = "";

            try
            {
                uint PROCESS_QUERY_LIMITED_INFORMATION = 0x1000;
                var processHandle = Kernel32.OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, false, pid);

                if (processHandle == IntPtr.Zero)
                {
                    return "";
                }

                const int lengthSb = 4000;
                var sb = new StringBuilder(lengthSb);

                if (GetModuleFileNameEx(processHandle.DangerousGetHandle(), IntPtr.Zero, sb, lengthSb) > 0)
                {
                    result = sb.ToString();
                }

                processHandle.Close();
            }
            catch
            {
                // ignore
            }

            return result;
        }


        private static string GetRealProcess(IntPtr hwndOwner)
        {
            List<IntPtr> childHandles = new List<IntPtr>();

            bool LpEnumFunc(HWND hwnd, IntPtr _)
            {
                childHandles.Add(hwnd.DangerousGetHandle());
                return true;
            }

            User32.EnumChildWindows(hwndOwner, LpEnumFunc, new IntPtr(0));
            foreach (var childHandle in childHandles)
            {
                User32.GetWindowThreadProcessId(childHandle, out var id);
                var foregroundProcess = GetProcessName(id);
                if (foregroundProcess != "" && !foregroundProcess.Contains("ApplicationFrameHost"))
                {
                    return foregroundProcess;
                }
            }
            return "";
        }

        internal class MainWindowFinder
        {
            private IntPtr _bestHandle;
            private int _processId;
            private User32.EnumWindowsProc EnumWindowsProc => EnumWindowsCallback;

            private bool EnumWindowsCallback(HWND hwnd, IntPtr lparam)
            {
                User32.GetWindowThreadProcessId(hwnd, out var processId);
                if (processId != this._processId || !this.IsMainWindow(hwnd.DangerousGetHandle()))
                    return true;
                _bestHandle = hwnd.DangerousGetHandle();
                return false;
            }

            internal IntPtr FindMainWindow(int processId)
            {
                _bestHandle = (IntPtr)0;
                _processId = processId;
                User32.EnumWindows(EnumWindowsProc, IntPtr.Zero);
                GC.KeepAlive(EnumWindowsProc);
                return _bestHandle;
            }

            private bool IsMainWindow(IntPtr handle)
            {
                return !(User32.GetWindow(handle, 4) != (IntPtr)0) && User32.IsWindowVisible(handle);
            }

        }
    }
}
