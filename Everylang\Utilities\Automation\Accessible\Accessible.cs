﻿// (c) Copyright Microsoft Corporation.
// This source is subject to the Microsoft Permissive License.
// See http://www.microsoft.com/opensource/licenses.mspx#Ms-PL.
// All other rights reserved.

using Accessibility;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using Vanara.PInvoke;

namespace Everylang.App.Utilities.Automation.Accessible
{
    // this class is a wrapper for the IAccessible interface and some MSAA APIs
    // It insulates the rest of the code from having to deal with the child Id vs a 
    // real IAccessible usage.  This is easy to get wrong.  An IAccessible is really 
    // the IAccessible interface plus the child id.
    internal class Accessible
    {
        private readonly IAccessible? _accessible;
        private readonly int _childId;
        // ReSharper disable once InconsistentNaming
        private const int S_OK = 0;
        // ReSharper disable once InconsistentNaming
        private const int CHILD_SELF = 0x0;
        // ReSharper disable once InconsistentNaming
        private static readonly Guid IID_IAccessible = new Guid(0x618736e0, 0x3c3d, 0x11cf, 0x81, 0x0c, 0x00, 0xaa, 0x00, 0x38, 0x9b, 0x71);


        internal Accessible(IAccessible? accessible, int childId)
        {
            System.Diagnostics.Debug.Assert(accessible != null);

            _accessible = accessible;
            _childId = childId;
        }

        internal static HRESULT FromPoint(Point pt, out Accessible? accessible)
        {
            accessible = null;
            var hr = Oleacc.AccessibleObjectFromPoint(new POINT(pt.X, pt.Y), out var acc, out var childId);

            if (hr == 0)
            {
                if (!(childId is int))
                {
                    throw new VariantNotIntException(childId);
                }
                accessible = new Accessible(acc, (int)childId);
            }
            return hr;
        }

        internal static HRESULT FromWindow(IntPtr hwnd, out Accessible? accessible)
        {
            accessible = null;
            var hr = Oleacc.AccessibleObjectFromWindow(hwnd, 0, in IID_IAccessible, out var obj);

            if (obj is IAccessible acc)
            {
                accessible = new Accessible(acc, 0);
            }
            return hr;
        }

        internal static HRESULT FromWindow(IntPtr hwnd, out Accessible? accessible, uint objId)
        {
            accessible = null;
            var hr = Oleacc.AccessibleObjectFromWindow(hwnd, objId, in IID_IAccessible, out var obj);

            if (obj is IAccessible acc)
            {
                accessible = new Accessible(acc, 0);
            }

            return hr;
        }

        internal static HRESULT FromEvent(IntPtr hwnd, uint idObject, uint idChild, out Accessible? accessible)
        {
            accessible = null;
            var hr = Oleacc.AccessibleObjectFromEvent(hwnd, idObject, idChild, out var acc, out var childId);

            accessible = new Accessible(acc, (int)childId);

            return hr;
        }

        internal IAccessible? IAccessible => _accessible;

        internal int ChildId => _childId;

        internal Accessible? Parent
        {
            get
            {
                object? parent;

                if (_childId == CHILD_SELF)
                {
                    if (_accessible?.accParent is not IAccessible parentIAccessible)
                    {
                        parent = null;
                    }
                    else
                    {
                        parent = new Accessible(parentIAccessible, CHILD_SELF);
                    }
                }
                else
                {
                    parent = new Accessible(_accessible, CHILD_SELF);
                }

                return parent as Accessible;
            }
        }

        internal HRESULT Children(out Accessible[]? accessible)
        {
            accessible = null;
            if (_childId != CHILD_SELF)
            {
                accessible = new Accessible[] { };
                return S_OK;
            }

            if (_accessible != null)
            {
                int childrenCount = _accessible.accChildCount;
                if (childrenCount < 0)
                {
                    throw new ChildCountInvalidException(childrenCount);
                }

                object[] children = new object[childrenCount];

                var hr = Oleacc.AccessibleChildren(_accessible, 0, childrenCount, children, out childrenCount);

                if (hr == S_OK)
                {
                    accessible = new Accessible[childrenCount];
                    int i = 0;
                    foreach (object? child in children)
                    {
                        if (child is IAccessible)
                        {
                            accessible[i++] = new Accessible((IAccessible)child, CHILD_SELF);
                        }
                        else if (child is int)
                        {
                            accessible[i++] = new Accessible(_accessible, (int)child);
                        }
                    }

                    // null children don't occur very often but if they do it stops us from going on
                    // So keep track of them so we can reallocate the array if necessary
                    if (childrenCount != i)
                    {
                        // if we had some null chilren create a smaller array to send the 
                        // children back in.
                        Accessible[] accessibleNew = new Accessible[i];
                        Array.Copy(accessible, accessibleNew, i);
                        accessible = accessibleNew;
                    }
                }

                return hr;
            }

            return 0;
        }

        internal string? Name
        {
            get
            {
                string? name = _accessible?.accName[_childId];

                //This could be returning a valid empty string or null
                return name;
            }
        }

        internal int Role
        {
            get
            {
                object? role = _accessible?.accRole[_childId];

                if (!(role is int))
                {
                    throw new VariantNotIntException(role);
                }

                return (int)role;
            }
        }

        internal int State
        {
            get
            {
                object? state = _accessible?.accState[_childId];

                if (!(state is int))
                {
                    throw new VariantNotIntException(state);
                }

                return (int)state;
            }
        }

        internal string Value
        {
            get
            {
                string? value = _accessible?.accValue[_childId];

                // Both null and empty string are ok just force it to be
                // an empty string to make comparing these easier
                if (value == null)
                {
                    value = "";
                }

                return value;
            }
        }

        internal Rectangle Location
        {
            get
            {
                if (_accessible != null)
                {
                    _accessible.accLocation(out var left, out var top, out var width, out var height, _childId);
                    Rectangle location = new Rectangle(left, top, width, height);
                    return location;
                }
                return new Rectangle(0, 0, 0, 0);

            }
        }

        internal string? KeyboardShortcut => _accessible?.accKeyboardShortcut[_childId];

        internal void Select(int flag)
        {
            _accessible?.accSelect(flag, _childId);
        }

        internal IntPtr Hwnd
        {
            get
            {
                HWND hwnd = IntPtr.Zero;
                if (_accessible != null)
                {
                    Oleacc.WindowFromAccessibleObject(_accessible, out hwnd);
                }
                return (IntPtr)hwnd;
            }
        }

        //
        // Compares an MsaaElement with an Accessible based on name, value, child count and location.
        //
        internal bool Compare(Accessible? acc)
        {
            // the goal is to abort as quickly as possible if we can, since
            // this method will be called many times when searching the tree,
            // and we want to minimize the amount of time used on non-matches
            if (acc == null)
            {
                return false;
            }

            Rectangle loc;
            try
            {
                loc = acc.Location;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    loc = Rectangle.Empty;
                }
                else
                {
                    throw;
                }
            }
            Rectangle thisLoc;
            try
            {
                thisLoc = Location;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    thisLoc = Rectangle.Empty;
                }
                else
                {
                    throw;
                }
            }
            if (!thisLoc.Equals(loc))
            {
                return false;
            }


            string? name;
            try
            {
                name = acc.Name;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    name = "";
                }
                else
                {
                    throw;
                }
            }
            string? thisName;
            try
            {
                thisName = Name;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    thisName = "";
                }
                else
                {
                    throw;
                }
            }
            if (thisName != name)
            {
                return false;
            }

            int role;
            try
            {
                role = acc.Role;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    role = 0;
                }
                else
                {
                    throw;
                }
            }
            int thisRole;
            try
            {
                thisRole = Role;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    thisRole = 0;
                }
                else
                {
                    throw;
                }
            }
            if (thisRole != role)
            {
                return false;
            }

            string? value;
            try
            {
                value = acc.Value;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    value = "";
                }
                else
                {
                    throw;
                }
            }
            string? thisValue;
            try
            {
                thisValue = Value;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    thisValue = "";
                }
                else
                {
                    throw;
                }
            }
            if (thisValue != value)
            {
                return false;
            }

            return true;
        }

        // Create a sting that has the list of the name of the parents to the root
        internal string CreateParentChain()
        {
            List<string> chain = new List<string?>();
            Accessible? acc = Parent;
            const int sanityCheckLimit = 100;
            int loopCount = 0;

            while (acc != null)
            {
                loopCount++;
                string? name;
                try
                {
                    name = Name;
                }
                catch (Exception e)
                {
                    if (IsExpectedException(e))
                    {
                        name = "";
                    }
                    else
                    {
                        throw;
                    }
                }

                if (!string.IsNullOrEmpty(name))
                {
                    chain.Add(name);
                }

                try
                {
                    acc = Parent;
                }
                catch (Exception e)
                {
                    if (IsExpectedException(e))
                    {
                        acc = null;
                    }
                    else
                    {
                        throw;
                    }
                }

                if (loopCount > sanityCheckLimit)
                {
                    break;
                }
            }

            chain.Reverse();

            return string.Join(".", chain.ToArray());
        }

        public override string ToString()
        {
            string? name;
            try
            {
                name = Name;
                if (name == null)
                    name = "";
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    name = "";
                }
                else
                {
                    throw;
                }
            }

            int role;
            try
            {
                role = Role;
            }
            catch (Exception e)
            {
                if (IsExpectedException(e))
                {
                    role = 0;
                }
                else
                {
                    throw;
                }
            }

            StringBuilder roleText = new StringBuilder(255);
            Oleacc.GetRoleText((Oleacc.AccessibilityRole)role, roleText, (uint)roleText.Capacity);

            return $"[IAccessible.ChildId({_childId}).Role({roleText}).Name({name})]";
        }

        // these are the exceptions that we expect the caller will determine if is ok or not.
        internal static bool IsExpectedException(Exception e)
        {
            if (e is NotImplementedException ||
                e is COMException ||
                e is VariantNotIntException ||
                e is ArgumentException ||
                e is InvalidCastException ||
                e is UnauthorizedAccessException)
            {
                return true;
            }

            return false;
        }
    }


    internal class VariantNotIntException : Exception
    {
        internal VariantNotIntException(object? variant)
        {
            VariantType = variant == null ? "[NULL]" : variant.GetType().ToString();
        }

        internal string VariantType { get; }
    }

    internal class ChildCountInvalidException : Exception
    {
        internal ChildCountInvalidException(int childCount)
        {
            ChildCount = childCount;
        }

        internal int ChildCount { get; }
    }

}
