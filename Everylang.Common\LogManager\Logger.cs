﻿using Serilog;
using Serilog.Events;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace Everylang.Common.LogManager
{
    public static class Logger
    {
        public static void InitLogger(string logPath)
        {
            LogEventLevel level = LogEventLevel.Information;
#if DEBUG
            level = LogEventLevel.Debug;
#endif

            var outputTemplate = "{Timestamp:dd-MM-yyyy HH:mm:ss} {Level} [{ClassName} -> {MethodName}()]: {SourceContext} {Message} {NewLine}{Exception}";
            Log.Logger = new LoggerConfiguration()
#if DEBUG
                .MinimumLevel.Debug()
#endif
                .WriteTo.File(Path.Combine(logPath, "Everylang_Log_.txt"), level, rollingInterval: RollingInterval.Day, outputTemplate: outputTemplate)
                .WriteTo.Console(restrictedToMinimumLevel: level, outputTemplate: outputTemplate)
                .Enrich.FromLogContext()
                .CreateLogger();
        }

        public static ILogger LogTo => Log.Logger.Log();
    }

    public static class LoggerExtensions
    {
        public static ILogger Log(this ILogger logger)
        {
            var stackFrame = FindStackFrame();
            var methodBase = stackFrame?.GetMethod();
            return logger
                .ForContext("MethodName", methodBase?.Name)
                .ForContext("ClassName", methodBase?.ReflectedType?.Name);
            //.ForContext("LineNumber", stackFrame!.GetFileLineNumber()
            //);
        }

        private static StackFrame? FindStackFrame()
        {
            var stackTrace = new StackTrace();
            for (var i = 0; i < stackTrace.GetFrames().Length; i++)
            {
                var methodBase = stackTrace.GetFrame(i)?.GetMethod();
                var name = MethodBase.GetCurrentMethod()?.Name;
                if (methodBase != null && !methodBase.Name.Equals("Log") && !methodBase.Name.Equals("get_LogTo") && !methodBase.Name.Equals(name))
                    return new StackFrame(i, true);
            }
            return null;
        }
    }
}
