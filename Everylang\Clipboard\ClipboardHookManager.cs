﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using NHotkey;
using System;
using System.Collections.Generic;

namespace Everylang.App.Clipboard
{
    class ClipboardHookManager : IDisposable
    {
        private List<string>? _hotKeysPasteByIndexList;
        private bool _isHotKeyClipboardPaste;
        private bool _isHotKeyClipboardView;
        private bool _isHotKeyClipboardRoundPaste;
        private bool _isHotKeyClipboardPasteByIndex;

        internal event Action? KeyCombinationPressedClipboardPasteWithoutFormatting;
        internal event Action? KeyCombinationPressedClipboardView;
        internal event Action? KeyCombinationPressedClipboardRoundPaste;
        internal event Action<int>? KeyCombinationPressedClipboardPasteByIndex;

        private static ClipboardHookManager? _instance;

        internal static ClipboardHookManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ClipboardHookManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            if (SettingsManager.Settings.ClipboardHistoryIsOn)
            {
                StartClipboardPaste();
                StartClipboardHistory();
                StartClipboardRoundPaste();
                StartClipboardPasteByIndex();
            }
        }

        private void StartClipboardPaste()
        {
            if (SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcutIsOn && !_isHotKeyClipboardPaste)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut), SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut, PressedClipboardPasteWithoutFormatting);
                _isHotKeyClipboardPaste = true;
            }
        }
        private void StartClipboardHistory()
        {
            if (SettingsManager.Settings.ClipboardHistoryIsOn && !_isHotKeyClipboardView)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ClipboardShowHistoryShortcut), SettingsManager.Settings.ClipboardShowHistoryShortcut, PressedClipboardView);
                _isHotKeyClipboardView = true;
            }
        }

        private void StartClipboardRoundPaste()
        {
            if (SettingsManager.Settings.ClipboardPasteRoundIsOn && !_isHotKeyClipboardRoundPaste)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.ClipboardPasteRoundShortcut), SettingsManager.Settings.ClipboardPasteRoundShortcut, PressedClipboardRoundPaste);
                _isHotKeyClipboardRoundPaste = true;
            }
        }

        private void StartClipboardPasteByIndex()
        {
            if (SettingsManager.Settings.ClipboardPasteByCtrlPlusIndexIsOn && !_isHotKeyClipboardPasteByIndex)
            {
                _hotKeysPasteByIndexList = new List<string>();
                for (int i = 1; i < 10; i++)
                {
                    _hotKeysPasteByIndexList.Add("HotKeyClipboardPasteByIndexList" + i);
                    ShortcutManager.RegisterAutoGeneratedShortcutHotkey("HotKeyClipboardPasteByIndexList" + i, "Ctrl+Shift+" + i, PressedClipboardPasteByIndex);
                }
                _isHotKeyClipboardPasteByIndex = true;
            }
        }

        internal void PressedClipboardRoundPaste(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedClipboardRoundPaste;
            if (handler != null) handler();
        }

        internal void PressedClipboardView(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedClipboardView;
            if (handler != null) handler();
        }

        internal void PressedClipboardPasteWithoutFormatting(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            var handler = KeyCombinationPressedClipboardPasteWithoutFormatting;
            if (handler != null) handler();
        }

        internal void PressedClipboardPasteByIndex(object? obj, HotkeyEventArgs e)
        {
            e.Handled = true;
            var handler = KeyCombinationPressedClipboardPasteByIndex;
            if (handler != null)
                if (_hotKeysPasteByIndexList != null)
                    handler(_hotKeysPasteByIndexList.IndexOf(e.Name));
        }

        internal void Stop()
        {
            ClipboardStopPastWithoutFormatting();
            ClipboardStopHistory();
            ClipboardStopRoundPast();
            ClipboardStopPasteByIndex();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        internal void ClipboardStopPasteByIndex()
        {

            if (_hotKeysPasteByIndexList != null)
            {
                _isHotKeyClipboardPasteByIndex = false;
                foreach (var hotKey in _hotKeysPasteByIndexList)
                {
                    ShortcutManager.RemoveShortcut(hotKey);
                }
            }
        }

        internal void ClipboardStopPastWithoutFormatting()
        {
            _isHotKeyClipboardPaste = false;
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut));
        }

        internal void ClipboardStopRoundPast()
        {
            _isHotKeyClipboardRoundPaste = false;
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ClipboardPasteRoundShortcut));
        }

        internal void ClipboardStopHistory()
        {
            _isHotKeyClipboardView = false;
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ClipboardShowHistoryShortcut));
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
