﻿using System.ComponentModel;
using System.Runtime.CompilerServices;
using Telerik.Windows.Controls;

namespace Everylang.App.Translator
{
    public class Language : INotifyPropertyChanged
    {
        public string SelectedNameFrom
        {
            get { return LocalizationManager.GetString("FromLang") + " " + _name; }
            set { }

        }

        public string SelectedNameTo
        {
            get { return LocalizationManager.GetString("ToLang") + " " + _name; }
            set { }
        }

        private string? _name;

        public string? Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public bool IsFavorite { get; set; }
        internal string? Abbreviation { get; set; }

        internal static Language AddLanguage(string name, string abbreviation)
        {
            return new Language
            {
                Name = name,
                Abbreviation = abbreviation,
            };
        }

        public override string? ToString()
        {
            return this.Name;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
