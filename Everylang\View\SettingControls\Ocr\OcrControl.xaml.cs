﻿using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Ocr
{
    /// <summary>
    /// Interaction logic for SpellcheckingControl.xaml
    /// </summary>
    internal partial class OcrControl
    {

        internal OcrControl()
        {
            InitializeComponent();
            LoadLanguages();
        }

        internal void LoadLanguages()
        {

            foreach (var s in SettingsManager.Settings.OcrLangsList)
            {
                var lang = VMContainer.Instance.OcrViewModel.Languages.FirstOrDefault(x => x.Item.Name == s);
                if (lang != null)
                {
                    lang.IsChecked = true;
                }
            }
        }

        internal void ApplyLanguages()
        {
            var langs = new List<string>();
            for (var i = 0; i < VMContainer.Instance.OcrViewModel.Languages.Count; i++)
            {
                if (VMContainer.Instance.OcrViewModel.Languages[i].IsChecked)
                {
                    var itemName = VMContainer.Instance.OcrViewModel.Languages[i].Item.Name;
                    langs.Add(itemName);

                }
            }

            SettingsManager.Settings.OcrLangsList = langs;
        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("OcrKeyboardShortcuts"), SettingsManager.Settings.OcrShortcut, nameof(SettingsManager.Settings.OcrShortcut), OcrManager.Instance.PressedShortcutOcr);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.OcrViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void ToggleButton_OnChecked(object sender, RoutedEventArgs e)
        {
            ApplyLanguages();
        }

    }
}
