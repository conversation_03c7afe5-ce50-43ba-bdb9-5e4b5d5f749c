﻿using Everylang.App.Callback;
using Everylang.App.Data;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.LangFlag;
using Everylang.App.License;
using Everylang.App.License.LicenseCore;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using Everylang.App.View.SettingControls.ProSettings;
using Everylang.App.ViewModels;
using Everylang.Common.LogManager;
using Everylang.Note.SettingsApp;
using Microsoft.Win32;
using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using Secure = Everylang.App.License.LicenseCore.Secure;

namespace Everylang.App.SettingsApp
{
    internal static class SettingsManager
    {
        internal static string UserAgent = @"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
        internal static string LicStatus { get; set; } = "";
        internal static bool IsOneStart { get; set; }
        internal static bool IsStopWorkingAll { get; set; }
        internal static bool IsClipboardSoundOff { get; set; }
        internal static SettingsDataModel Settings { get; private set; } = null!;
        internal static LicenseModel? LicenseModel { get; set; }
        internal static bool DbError { get; set; }


        private static string _appDataPath = "";
        private static string _dbName = "eldata3.db";
        private static bool _appDataPathNotExist;
        private static bool? _isAdmin;
        private static string? _machineId;
        private static bool _isStopWorking;

        internal static bool Init()
        {
            var localPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            _appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "EveryLang");
            var logPath = Path.Combine(_appDataPath, "Log");
            var dataPath = DataFilePath;
            if (!Directory.Exists(dataPath))
            {
                _appDataPathNotExist = true;
                if (dataPath != null) Directory.CreateDirectory(dataPath);
            }

            if (dataPath != null)
            {
                if (!Directory.Exists(logPath))
                {
                    Directory.CreateDirectory(logPath);
                }
                if (!File.Exists(Path.Combine(dataPath, _dbName)))
                {
                    if (localPath != null && File.Exists(Path.Combine(localPath, _dbName)))
                    {
                        DataFilePath = localPath;
                    }
                }
                SystemEvents.PowerModeChanged += SystemEvents_PowerModeChanged;
                GlobalEventsApp.EventStart += CallbackEventHandler;
                DataBaseManager.DbPath = Path.Combine(_appDataPath, _dbName);
                SettingsMiminoteManager.UserDbFolderPath = _appDataPath;

                Logger.InitLogger(logPath);
                //Logger.LogTo.Information("Start application");
            }

            return LoadSettings();
        }



        private static bool LoadSettings()
        {
            try
            {
                DataBaseManager.Init();
                Settings = new SettingsDataModel();
                IsWindows7 = GetIsWindows7();
                IsOneStart = false;
                if (!File.Exists(Path.Combine(_appDataPath, _dbName)))
                {
                    IsOneStart = true;
                }
                SettingsDataManager.GetSettings(Settings);
                LicenseModel = new LicenseModel
                {
                    Email = Settings.LicEmail
                };
                if (string.IsNullOrEmpty(Settings.LicCode))
                {
                    LicenseModel.License = Settings.LicEvaluateCode;
                }
                else
                {
                    LicenseModel.License = Settings.LicCode;
                }
                Settings.DontSave = false;
                if (Settings.HashFromSite != null && Settings.HashFromSite.Contains("_"))
                {
                    Settings.HashFromSite = "";
                    Settings.LastVersionForCheckHash = "";
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
                return false;
            }
            return true;
        }

        internal static string? DataFilePath
        {
            get
            {
                var path = _appDataPath;
                RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
                string? value = reg.GetValue("DataFilePath") as string;
                if (!string.IsNullOrEmpty(value))
                {
                    path = value;
                    _appDataPath = path;
                }
                reg.Close();
                return path;
            }
            set
            {
                try
                {
                    if (value != null && value != _appDataPath)
                    {
                        var eldata3Path = Path.Combine(value, _dbName);
                        if (!File.Exists(eldata3Path))
                        {
                            CopyDataFiles(_appDataPath, value);
                        }
                    }
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            }
        }

        private static void CopyDataFiles(string sourceFolder, string targetFolder)
        {
            DataBaseManager.Dispose();
            SettingsMiminoteManager.Dispose();
            File.Copy(Path.Combine(sourceFolder, _dbName), Path.Combine(targetFolder, _dbName));
            File.Delete(Path.Combine(sourceFolder, _dbName));
            File.Copy(Path.Combine(sourceFolder, "everynote.db"), Path.Combine(targetFolder, "everynote.db"));
            File.Delete(Path.Combine(sourceFolder, "everynote.db"));
            RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
            if (reg != null)
            {
                reg.SetValue("DataFilePath", targetFolder);
                reg.Close();
            }
            _appDataPath = targetFolder;
            DataBaseManager.DbPath = Path.Combine(_appDataPath, _dbName);
            DataBaseManager.Init();


        }

        private static void SystemEvents_PowerModeChanged(object sender, PowerModeChangedEventArgs e)
        {
            if (e.Mode == PowerModes.Suspend) IsStopWorking = true;
            else if (e.Mode == PowerModes.Resume) IsStopWorking = false;
        }

        private static async void CallbackEventHandler()
        {
            await Task.Delay(1000);
            ProStatusStrings.Instance.AutoStart();
        }

        internal static bool IsStopWorking
        {
            get
            {
                if (IsStopWorkingAll)
                {
                    return true;
                }
                return _isStopWorking;
            }

            set
            {
                if (value == _isStopWorking)
                {
                    return;
                }
                if (!value)
                {
                    ShortcutManager.RegisterAll();
                    LangInfoManager.Start();
                }
                else
                {
                    LangInfoManager.Stop();
                    ShortcutManager.UnRegisterAll();
                }
                _isStopWorking = value;
            }
        }

        internal static string? MachineId
        {
            get
            {
                if (string.IsNullOrEmpty(_machineId))
                {
                    _machineId = HardwareId.GetId();
                }
                return _machineId;
            }
        }

        internal static bool LicIsActivated => LicenseModel != null && !string.IsNullOrEmpty(LicenseModel.License) && !string.IsNullOrEmpty(LicStatus) && Secure.DecryptStringAES(LicStatus, LicenseModel.License).ToLower().Contains("hol");

        internal static void SaveSettings()
        {
            SettingsDataManager.SaveSettings(Settings);
        }

        internal static bool IsWindows7 { get; private set; }
        private static bool GetIsWindows7()
        {
            try
            {
                var reg = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");

                if (reg != null)
                {
                    string? productName = reg.GetValue("ProductName") as string;

                    return productName != null && productName.StartsWith("Windows 7");
                }
            }
            catch
            {
                // ignore
            }
            return false;
        }

        internal static bool IsAdministrator()
        {
            if (_isAdmin == null)
            {
                _isAdmin = Administrator.IsAdministrator();
            }
            return _isAdmin.Value;
        }

        internal static void DeleteAllSettings()
        {
            try
            {
                var eldata3Path = Path.Combine(_appDataPath, _dbName);
                if (File.Exists(eldata3Path))
                {
                    File.Copy(Path.Combine(_appDataPath, _dbName), Path.Combine(_appDataPath, "eldata3 " + DateTime.Now.ToString("dd-MM-yy HH.mm") + ".db"));
                }
                if (Directory.Exists(Path.Combine(_appDataPath, "OCR")))
                {
                    try
                    {
                        Directory.Delete(Path.Combine(_appDataPath, "OCR"));
                    }
                    catch { }
                }
                Registry.CurrentUser.DeleteSubKeyTree("Software\\EveryLang\\");
                DataBaseManager.DeleteAllData();
                SettingsDataManager.CreateSettingsOnlyLic(Settings);
                VMContainer.Instance.SnippetsViewModel.SaveAllToDb();
                VMContainer.Instance.AutoSwitcherSettingsViewModel.SaveAllToDb();
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }


}
