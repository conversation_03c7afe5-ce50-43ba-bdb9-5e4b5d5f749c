﻿<Popup
    AllowsTransparency="True"
    Focusable="False"
    Placement="Absolute"
    StaysOpen="True"
    x:Class="Everylang.App.View.Controls.LangInfo.LangInfoWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">



    <StackPanel Margin="1" Orientation="Horizontal">
        <Grid VerticalAlignment="Center">
            <TextBlock
                FontSize="{Binding LangFlagSettingsViewModel.FontSizeLangInfo}"
                FontWeight="Bold"
                HorizontalAlignment="Center"
                Name="textBlock"
                Opacity="{Binding LangFlagSettingsViewModel.OpacityIcon, Mode=TwoWay}"
                VerticalAlignment="Center" />
            <Image
                Name="image"
                Opacity="{Binding LangFlagSettingsViewModel.OpacityIcon, Mode=TwoWay}"
                RenderOptions.BitmapScalingMode="HighQuality">
                <Image.LayoutTransform>
                    <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSize}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSize}" />
                </Image.LayoutTransform>
            </Image>
        </Grid>
        <Image
            Margin="3,0,0,0"
            Name="CapsLockButtonOn"
            RenderOptions.BitmapScalingMode="HighQuality"
            Source="/EveryLang;component/Resources/capslock_on.png"
            VerticalAlignment="Center"
            Visibility="Collapsed">
            <Image.LayoutTransform>
                <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSizeCaps}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSizeCaps}" />
            </Image.LayoutTransform>
        </Image>
    </StackPanel>

</Popup>
