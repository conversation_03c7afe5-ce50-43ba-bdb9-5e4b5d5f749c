﻿// using Everylang.App.Shortcut;
// using Everylang.Note.Callbacks;
// using Everylang.Note.SettingsApp;
// using System.Windows.Media;
// using Telerik.Windows.Controls;
//
// namespace Everylang.App.ViewModels.SettingsModel
// {
//     public class SettingsMiminoteViewModel : ViewModelBase
//     {
//         public DelegateCommand ShowAllCommand
//         {
//             get;
//             private set;
//         }
//
//         internal SettingsMiminoteViewModel()
//         {
//             ShowAllCommand = new DelegateCommand(ShowAll);
//         }
//
//         private void ShowAll(object obj)
//         {
//             CallBack.OnMiminoteCallBackShowAll();
//         }
//
//         private bool _isPro;
//
//         public bool jgebhdhs
//         {
//             get => _isPro;
//             set
//             {
//                 _isPro = value;
//                 base.OnPropertyChanged();
//                 base.OnPropertyChanged(nameof(IsOnNotes));
//             }
//         }
//
//         public bool IsOnNotes
//         {
//             get { return jgebhdhs && SettingsMiminoteManager.AppSettings.IsOnNotes; }
//             set
//             {
//                 SettingsMiminoteManager.AppSettings.IsOnNotes = value;
//             }
//         }
//
//         public bool AddNewShortcutIsEnabled
//         {
//             get { return SettingsMiminoteManager.AppSettings.AddNewShortcutIsEnabled; }
//             set
//             {
//                 SettingsMiminoteManager.AppSettings.AddNewShortcutIsEnabled = value;
//                 OnPropertyChanged();
//             }
//         }
//
//         public string AddNewShortcut
//         {
//             get { return ShortcutManager.GetCharFromKey(SettingsMiminoteManager.AppSettings.AddNewShortcut); }
//             set
//             {
//                 SettingsMiminoteManager.AppSettings.AddNewShortcut = value;
//                 OnPropertyChanged();
//             }
//         }
//
//         public string SizeFontDefaultText
//         {
//             get => SettingsMiminoteManager.AppSettings.SizeFontDefault.ToString();
//             set
//             {
//                 if (!string.IsNullOrEmpty(value))
//                 {
//                     if (Double.TryParse(value, out var size))
//                     {
//                         SettingsMiminoteManager.AppSettings.SizeFontDefault = size;
//                         OnPropertyChanged();
//                     }
//                 }
//             }
//         }
//
//         public FontFamily FontFamilyDefault
//         {
//             get => new FontFamily(SettingsMiminoteManager.AppSettings.FontDefault);
//             set
//             {
//                 SettingsMiminoteManager.AppSettings.FontDefault = value.ToString();
//                 OnPropertyChanged();
//             }
//         }
//
//         public int TransparencyForNotes
//         {
//             get
//             {
//                 return SettingsMiminoteManager.AppSettings.TransparencyForNotes;
//             }
//             set
//             {
//                 SettingsMiminoteManager.AppSettings.TransparencyForNotes = value;
//                 CallBack.OnMiminoteCallBackTransparencyForNotesChange();
//                 OnPropertyChanged();
//             }
//         }
//     }
// }
