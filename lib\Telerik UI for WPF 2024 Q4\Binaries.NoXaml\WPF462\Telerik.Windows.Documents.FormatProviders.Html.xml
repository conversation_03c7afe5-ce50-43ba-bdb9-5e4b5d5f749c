<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.FormatProviders.Html</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider">
            <summary>
            Represents a wrapper of <see cref="T:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider"/> that can be used in data binding scenarios.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.DataProviderBase" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider"/> class.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.HtmlProperty">
            <summary>
            The HTML property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.Html">
            <summary>
            Gets or sets the current document as HTML
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.Bind(Telerik.Windows.Controls.RadRichTextBox)">
            <summary>
            Binds the specified <see cref="T:Telerik.Windows.Controls.RadRichTextBox"/> to the current instance.
            </summary>
            <param name="box">The <see cref="T:Telerik.Windows.Controls.RadRichTextBox"/>.</param>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.SourceProperty">
            <summary>
            The source property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.SetSource(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the source.
            </summary>
            <param name="dependencyObject">The dependency object the source should be set to.</param>
            <param name="sourceValue">The source value.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.GetSource(System.Windows.DependencyObject)">
            <summary>
            Gets the source.
            </summary>
            <param name="dependencyObject">The dependency object the source should be obtained from.</param>
            <returns>The source.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlDataProvider.GetAttachedDataProvider(System.Windows.DependencyObject)">
            <summary>
            Gets the attached data provider.
            </summary>
            <param name="dependencyObject">The dependency object the data provider should be obtained from.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider">
            <summary>
            Represents a format provider that can import and export HTML documents from/to <see cref="T:Telerik.Windows.Documents.Model.RadDocument"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.Name">
            <summary>
            Gets the name of the specific format provider.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.FilesDescription">
            <summary>
            Gets the description of the supported file formats.
            </summary>
            <value>
            The files description.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.SupportedExtensions">
            <summary>
            Gets the extensions supported by this format provider.
            </summary>
            <value>
            The supported extensions.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.ImportSettings">
            <summary>
            Gets or sets the settings which will be used while importing a document.
            </summary>
            <value>
            The import settings.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.ExportSettings">
            <summary>
            Gets or sets the settings which will be used while exporting a document.
            </summary>
            <value>
            The export settings.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.CanImport">
            <summary>
            Gets a value indicating whether this instance can import.
            </summary>
            <value>
            <c>true</c> if this instance can import; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.CanExport">
            <summary>
            Gets a value indicating whether this instance can export.
            </summary>
            <value>
            <c>true</c> if this instance can export; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.Export(Telerik.Windows.Documents.Model.RadDocument,System.IO.Stream)">
            <summary>
            Exports the specified <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The <see cref="T:System.IO.Stream" /> the document should be saved into.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.Import(System.IO.Stream)">
            <summary>
            Imports the specified <see cref="T:System.IO.Stream" /> into a <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="input">The <see cref="T:System.IO.Stream" /> containing the document data.</param>
            <returns>
            The generated <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.Import(System.String)">
            <summary>
            Imports the specified <see cref="T:System.String"/> into a <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="input">The HTML string.</param>
            <returns>
            The generated <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.HtmlFormatProvider.Export(Telerik.Windows.Documents.Model.RadDocument)">
            <summary>
            Exports the specified <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance to a <see cref="T:System.String"/>.
            </summary>
            <param name="document">The document which should be exported.</param>
            <returns>A <see cref="T:System.String"/> containing the HTML document.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Import.HtmlDocumentImporter">
            <summary>
            Defines members for importing HTML documents. This class is intended for internal usage.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Import.HtmlDocumentImporter.#ctor(Telerik.Windows.Documents.FormatProviders.Html.HtmlImportSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Html.Import.HtmlDocumentImporter"/> class.
            </summary>
            <param name="importSettings">The import settings.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Import.HtmlDocumentImporter.Import(System.IO.Stream)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>A <see cref="T:Telerik.Windows.Documents.Model.RadDocument"/> instance.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.DeclarationOrigin">
            <summary>
            Defines different types of origin of styles.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.DeclarationOrigin.DefaultStylesheet">
            <summary>
            The default stylesheet.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.DeclarationOrigin.UserStylesheet">
            <summary>
            The user stylesheet.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.DeclarationOrigin.InlineStyle">
            <summary>
            The inline style.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1">
            <summary>
            An IDictionary{string, T} containing lowercase keys only.
            </summary>
            <typeparam name="T">The type of the values inside the dictionary.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Add(System.String,`0)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</param>
            <returns>
              <see langword="true" /> if the <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the key; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Remove(System.String)">
            <summary>
            Removes the element with the specified key from the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
            <param name="key">The key of the element to remove.</param>
            <returns>
              <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.  This method also returns <see langword="false" /> if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.TryGetValue(System.String,`0@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
            <returns>
              <see langword="true" /> if the object that implements <see cref="T:System.Collections.Generic.IDictionary`2" /> contains an element with the specified key; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Values">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Item(System.String)">
            <summary>
            Gets or sets the <typeparamref name="T"/> with the specified key.
            </summary>
            <value>
            The <typeparamref name="T"/>.
            </value>
            <param name="key">The key.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.CopyTo(System.Collections.Generic.KeyValuePair{System.String,`0}[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.LowerCaseDictionary`1.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.IDeclarationContainer">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.IDeclarationContainer.Declarations">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.IRuleSetContainer">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.IRuleSetContainer.RuleSets">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Attribute">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Attribute.Operand">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Attribute.Operator">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Attribute.Value">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Attribute.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.Equals">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.InList">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.Hyphenated">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.EndsWith">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.BeginsWith">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.AttributeOperator.Contains">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Combinator">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Combinator.ChildOf">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Combinator.PrecededImmediatelyBy">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Combinator.PrecededBy">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.CssDocument.Directives">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.CssDocument.RuleSets">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.CssDocument.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Declaration">
            <summary>property ( name: stuff; )</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Declaration.Name">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Declaration.Important">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Declaration.Expression">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Declaration.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Type">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Name">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Expression">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Mediums">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Directives">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.RuleSets">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.Declarations">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Directive.ToString(System.Int32)">
            <summary></summary>
            <param name="nesting"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Media">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Import">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Charset">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Page">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.FontFace">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Namespace">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.DirectiveType.Other">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Expression">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Expression.PropertyValues">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Expression.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Function">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Function.Name">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Function.Expression">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Function.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.all">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.aural">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.braille">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.embossed">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.handheld">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.print">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.projection">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.screen">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.tty">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Medium.tv">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue">
            <summary>part of a property's value</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Seperator">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.SeperatorChar">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Sign">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.SignChar">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Type">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Value">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Unit">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.Function">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValue.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.Number">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.Function">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.String">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.Url">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.Unicode">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.PropertyValueType.Hex">
            <summary></summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.RuleSet">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.RuleSet.Selectors">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.RuleSet.Declarations">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.RuleSet.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.RuleSet.ToString(System.Int32)">
            <summary></summary>
            <param name="nesting"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Selector">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.Selector.SelectorParts">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.Selector.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Combinator">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.ElementName">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.ID">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Class">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Pseudo">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Attribute">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Function">
            <summary></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.Child">
            <summary></summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.CSS.SelectorPart.ToString">
            <summary></summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.None">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.Percent">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.EM">
            <summary>the font size of the element (or, to the parent element's font size if set on the 'font-size' property)</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.EX">
            <summary>the x-height of the element's font</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.PX">
            <summary>viewing device</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.GD">
            <summary>the grid defined by 'layout-grid' described in the CSS3 Text module [CSS3TEXT]</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.REM">
            <summary>the font size of the root element</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.VW">
            <summary>the viewport's width</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.VH">
            <summary>the viewport's height</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.VM">
            <summary>the viewport's height or width, whichever is smaller of the two</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.CH">
            <summary>The width of the "0" (ZERO, U+0030) glyph found in the font for the font size used to render. If the "0" glyph is not found in the font, the average character width may be used. How is the "average character width" found?</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.MM">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.CM">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.IN">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.PT">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.PC">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.DEG">
            <summary>degrees</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.GRAD">
            <summary>grads</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.RAD">
            <summary>radians</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.TURN">
            <summary>turns</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.MS">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.S">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.Hz">
            <summary></summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.CSS.Unit.kHz">
            <summary></summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException" -->
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.IndexSize">
            <summary>If index or size is negative, or greater than the allowed value</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.DomStringSize">
            <summary>If the specified range of text does not fit into a DOMString</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.HierarchyRequest">
            <summary>If any node is inserted somewhere it doesn't belong</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.WrongDocument">
            <summary>If a node is used in a different document than the one that created it 
            (that doesn't support it)</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.InvalidCharacter">
            <summary>If an invalid or illegal character is specified, such as in a name. See 
            production 2 in the XML specification for the definition of a legal 
            character, and production 5 for the definition of a legal name 
            character.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.NoDataAllowed">
            <summary>If data is specified for a node which does not support data</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.NoModificationAllowed">
            <summary>If an attempt is made to modify an object where modifications are not 
            allowed</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.NotFound">
            <summary>If an attempt is made to reference a node in a context where it does 
            not exist</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.NotSupported">
            <summary>If the implementation does not support the requested type of object or 
            operation.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.InUseAttribute">
            <summary>If an attempt is made to add an attribute that is already in use 
            elsewhere</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.InvalidState">
            <summary>If an attempt is made to use an object that is not, or is no longer, 
            usable.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.Syntax">
            <summary>If an invalid or illegal string is specified.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.InvalidModification">
            <summary>If an attempt is made to modify the type of the underlying object.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.Namespace">
            <summary>If an attempt is made to create or change an object in a way which is 
            incorrect with regard to namespaces.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.InvalidAccess">
            <summary>If a parameter or an operation is not supported by the underlying 
            object.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.#ctor">
            <summary>
            Default constuctor.  Used only for serialisation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.#ctor(System.Int16,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="code">The code.</param>
            <param name="message">The message</param>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException.Code">
            <summary>
            The error code.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr.Name">
            <summary>Returns the name of this attribute.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr.Specified" -->
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr.Value" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr.OwnerElement">
            <summary>The <code>Element</code> node this attribute is attached to or 
            <code>null</code> if this attribute is not in use.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICdataSection" -->
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.Data">
            <summary> The character data of the node that implements this interface. The DOM 
            implementation may not put arbitrary limits on the amount of data 
            that may be stored in a <code>CharacterData</code> node. However, 
            implementation limits may mean that the entirety of a node's data may 
            not fit into a single <code>DOMString</code>. In such cases, the user 
            may call <code>substringData</code> to retrieve the data in 
            appropriately sized pieces.
            </summary>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised when the node is readonly.
            </exception>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            DOMSTRING_SIZE_ERR: Raised when it would return more characters than 
            fit in a <code>DOMString</code> variable on the implementation 
            platform.
            
            </exception>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.Length">
            <summary> The number of 16-bit units that are available through <code>data</code> 
            and the <code>substringData</code> method below. This may have the 
            value zero, i.e., <code>CharacterData</code> nodes may be empty.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.SubstringData(System.Int32,System.Int32)">
            <summary>Extracts a range of data from the node.</summary>
            <param name="offsetStart">offset of substring to extract.</param>
            <param name="countThe">number of 16-bit units to extract.</param>
            <returns> The specified substring. If the sum of <code>offset</code> and 
            <code>count</code> exceeds the <code>length</code>, then all 16-bit 
            units to the end of the data are returned.
            </returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INDEX_SIZE_ERR: Raised if the specified <code>offset</code> is 
            negative or greater than the number of 16-bit units in 
            <code>data</code>, or if the specified <code>count</code> is 
            negative.
            <br />DOMSTRING_SIZE_ERR: Raised if the specified range of text does 
            not fit into a <code>DOMString</code>.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.AppendData(System.String)">
            <summary> Append the string to the end of the character data of the node. Upon 
            success, <code>data</code> provides access to the concatenation of 
            <code>data</code> and the <code>DOMString</code> specified.
            </summary>
            <param name="argThe"><code>DOMString</code> to append.</param>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.InsertData(System.Int32,System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.DeleteData(System.Int32,System.Int32)">
            <summary> Remove a range of 16-bit units from the node. Upon success, 
            <code>data</code> and <code>length</code> reflect the change.
            </summary>
            <param name="offsetThe">offset from which to start removing.</param>
            <param name="countThe">number of 16-bit units to delete. If the sum of 
            <code>offset</code> and <code>count</code> exceeds 
            <code>length</code> then all 16-bit units from <code>offset</code> 
            to the end of the data are deleted.</param>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INDEX_SIZE_ERR: Raised if the specified <code>offset</code> is 
            negative or greater than the number of 16-bit units in 
            <code>data</code>, or if the specified <code>count</code> is 
            negative.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.ICharacterData.ReplaceData(System.Int32,System.Int32,System.String)">
            <summary> Replace the characters starting at the specified 16-bit unit offset 
            with the specified string.
            </summary>
            <param name="offsetThe">offset from which to start replacing.</param>
            <param name="countThe">number of 16-bit units to replace. If the sum of 
            <code>offset</code> and <code>count</code> exceeds 
            <code>length</code>, then all 16-bit units to the end of the data 
            are replaced; (i.e., the effect is the same as a <code>remove</code>
            method call with the same range, followed by an <code>append</code>
            method invocation).
            </param>
            <param name="argThe"><code>DOMString</code> with which the range must be 
            replaced.</param>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INDEX_SIZE_ERR: Raised if the specified <code>offset</code> is 
            negative or greater than the number of 16-bit units in 
            <code>data</code>, or if the specified <code>count</code> is 
            negative.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IComment" -->
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.Doctype">
            <summary> The Document Type Declaration (see <code>DocumentType</code>) 
            associated with this document. For HTML documents as well as XML 
            documents without a document type declaration this returns 
            <code>null</code>. The DOM Level 2 does not support editing the 
            Document Type Declaration. <code>docType</code> cannot be altered in 
            any way, including through the use of methods inherited from the 
            <code>Node</code> interface, such as <code>insertNode</code> or 
            <code>removeNode</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.Implementation">
            <summary> The <code>DOMImplementation</code> object that handles this document. A 
            DOM application may use objects from multiple implementations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.DocumentElement">
            <summary> This is a convenience attribute that allows direct access to the child 
            node that is the root element of the document. For HTML documents, 
            this is the element with the tagName "HTML".
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateElement(System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateDocumentFragment">
            <summary>Creates an empty <code>DocumentFragment</code> object.</summary>
            <returns> A new <code>DocumentFragment</code>.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateTextNode(System.String)">
            <summary>Creates a <code>Text</code> node given the specified string.</summary>
            <param name="dataThe">data for the node.</param>
            <returns> The new <code>Text</code> object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateComment(System.String)">
            <summary>Creates a <code>Comment</code> node given the specified string.</summary>
            <param name="dataThe">data for the node.</param>
            <returns> The new <code>Comment</code> object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateCdataSection(System.String)">
            <summary>Creates a <code>CDATASection</code> node whose value is the specified 
            string.</summary>
            <param name="dataThe">data for the <code>CDATASection</code> contents.</param>
            <returns> The new <code>CDATASection</code> object.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">NOT_SUPPORTED_ERR: Raised if this document is an HTML document.</exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateProcessingInstruction(System.String,System.String)">
            <summary>Creates a <code>ProcessingInstruction</code> node given the specified 
            name and data strings.</summary>
            <param name="targetThe">target part of the processing instruction.</param>
            <param name="dataThe">data for the node.</param>
            <returns> The new <code>ProcessingInstruction</code> object.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INVALID_CHARACTER_ERR: Raised if the specified target contains an 
            illegal character.
            <br />NOT_SUPPORTED_ERR: Raised if this document is an HTML document.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateAttribute(System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateEntityReference(System.String)">
            <summary> Creates an <code>EntityReference</code> object. In addition, if the 
            referenced entity is known, the child list of the 
            <code>EntityReference</code> node is made the same as that of the 
            corresponding <code>Entity</code> node.If any descendant of the 
            <code>Entity</code> node has an unbound namespace prefix, the 
            corresponding descendant of the created <code>EntityReference</code> 
            node is also unbound; (its <code>namespaceURI</code> is 
            <code>null</code>). The DOM Level 2 does not support any mechanism to 
            resolve namespace prefixes.
            </summary>
            <param name="nameThe">name of the entity to reference. </param>
            <returns> The new <code>EntityReference</code> object.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INVALID_CHARACTER_ERR: Raised if the specified name contains an 
            illegal character.
            <br />NOT_SUPPORTED_ERR: Raised if this document is an HTML document.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.GetElementsByTagName(System.String)">
            <summary>Returns a <code>NodeList</code> of all the <code>Elements</code> with a 
            given tag name in the order in which they are encountered in a 
            preorder traversal of the <code>Document</code> tree. 
            </summary>
            <param name="tagnameThe">name of the tag to match on. The special value "*" 
            matches all tags.</param>
            <returns>A new <code>NodeList</code> object containing all the matched 
            <code>Elements</code>.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.ImportNode(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode,System.Boolean)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateElementNS(System.String,System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.CreateAttributeNS(System.String,System.String)">
            <summary> Creates an attribute of the given qualified name and namespace URI. 
            HTML-only DOM implementations do not need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the attribute to create.
            </param>
            <param name="qualifiedNameThe">qualified name of the attribute to instantiate.
            </param>
            <returns> A new <code>Attr</code> object with the following attributes:
            AttributeValue<code>Node.nodeName</code>qualifiedName
            <code>Node.namespaceURI</code><code>namespaceURI</code>
            <code>Node.prefix</code>prefix, extracted from 
            <code>qualifiedName</code>, or <code>null</code> if there is no 
            prefix<code>Node.localName</code>local name, extracted from 
            <code>qualifiedName</code><code>Attr.name</code>
            <code>qualifiedName</code><code>Node.nodeValue</code>the empty 
            string
            </returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INVALID_CHARACTER_ERR: Raised if the specified qualified name 
            contains an illegal character.
            <br />NAMESPACE_ERR: Raised if the <code>qualifiedName</code> is 
            malformed, if the <code>qualifiedName</code> has a prefix and the 
            <code>namespaceURI</code> is <code>null</code>, if the 
            <code>qualifiedName</code> has a prefix that is "xml" and the 
            <code>namespaceURI</code> is different from "
            http://www.w3.org/XML/1998/namespace", or if the 
            <code>qualifiedName</code> is "xmlns" and the 
            <code>namespaceURI</code> is different from "
            http://www.w3.org/2000/xmlns/".
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.GetElementsByTagNameNS(System.String,System.String)">
            <summary> Returns a <code>NodeList</code> of all the <code>Elements</code> with a 
            given local name and namespace URI in the order in which they are 
            encountered in a preorder traversal of the <code>Document</code> tree.
            </summary>
            <param name="namespaceURIThe">namespace URI of the elements to match on. The 
            special value "*" matches all namespaces.
            </param>
            <param name="localNameThe">local name of the elements to match on. The 
            special value "*" matches all local names.
            </param>
            <returns> A new <code>NodeList</code> object containing all the matched 
            <code>Elements</code>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocument.GetElementById(System.String)">
            <summary> Returns the <code>Element</code> whose <code>ID</code> is given by 
            <code>elementId</code>. If no such element exists, returns 
            <code>null</code>. Behavior is not defined if more than one element 
            has this <code>ID</code>. The DOM implementation must have 
            information that says which attributes are of type ID. Attributes 
            with the name "ID" are not of type ID unless so defined. 
            Implementations that do not know whether attributes are of type ID or 
            not are expected to return <code>null</code>.
            </summary>
            <param name="elementIdThe">unique <code>id</code> value for an element.</param>
            <returns>The matching element.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentFragment" -->
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.Name">
            <summary>The name of DTD; i.e., the name immediately following the 
            <code>DOCTYPE</code> keyword.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.Entities" -->
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.Notations" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.PublicId">
            <summary> The public identifier of the external subset.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.SystemId">
            <summary> The system identifier of the external subset.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType.InternalSubset">
            <summary> The internal subset as a string.The actual content returned depends on 
            how much information is available to the implementation. This may 
            vary depending on various parameters, including the XML processor 
            used to build the document.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDomImplementation" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDomImplementation.HasFeature(System.String,System.String)">
            <summary> Test if the DOM implementation implements a specific feature.
            </summary>
            <param name="featureThe">name of the feature to test (case-insensitive). The 
            values used by DOM features are defined throughout the DOM Level 2 
            specifications and listed in the  section. The name must be an XML 
            name. To avoid possible conflicts, as a convention, names referring 
            to features defined outside the DOM specification should be made 
            unique by reversing the name of the Internet domain name of the 
            person (or the organization that the person belongs to) who defines 
            the feature, component by component, and using this as a prefix. 
            For instance, the W3C SVG Working Group defines the feature 
            "TidyNet.svg".
            </param>
            <param name="versionThis">is the version number of the feature to test. In 
            Level 2, the string can be either "2.0" or "1.0". If the version is 
            not specified, supporting any version of the feature causes the 
            method to return <code>true</code>.
            </param>
            <returns> <code>true</code> if the feature is implemented in the 
            specified version, <code>false</code> otherwise.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDomImplementation.CreateDocumentType(System.String,System.String,System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDomImplementation.CreateDocument(System.String,System.String,Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IDocumentType)">
            <summary> Creates an XML <code>Document</code> object of the specified type with 
            its document element. HTML-only DOM implementations do not need to 
            implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the document element to create.</param>
            <param name="qualifiedNameThe">qualified name of the document element to be created.</param>
            <param name="doctypeThe">type of document to be created or <code>null</code>.
            When <code>doctype</code> is not <code>null</code>, its 
            <code>Node.ownerDocument</code> attribute is set to the document 
            being created.
            </param>
            <returns>A new <code>Document</code> object.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INVALID_CHARACTER_ERR: Raised if the specified qualified name 
            contains an illegal character.
            <br />NAMESPACE_ERR: Raised if the <code>qualifiedName</code> is 
            malformed, if the <code>qualifiedName</code> has a prefix and the 
            <code>namespaceURI</code> is <code>null</code>, or if the 
            <code>qualifiedName</code> has a prefix that is "xml" and the 
            <code>namespaceURI</code> is different from "
            http://www.w3.org/XML/1998/namespace" .
            <br />WRONG_DOCUMENT_ERR: Raised if <code>doctype</code> has already 
            been used with a different document or was created from a different 
            implementation.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.TagName">
            <summary> The name of the element. For example, in: 
            <pre> &lt;elementExample 
            id="demo"&gt; ... &lt;/elementExample&gt; , </pre>
            <code>tagName</code> has 
            the value <code>"elementExample"</code>. Note that this is 
            case-preserving in XML, as are all of the operations of the DOM. The 
            HTML DOM returns the <code>tagName</code> of an HTML element in the 
            canonical uppercase form, regardless of the case in the source HTML 
            document. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetAttribute(System.String)">
            <summary> Retrieves an attribute value by name.</summary>
            <param name="nameThe">name of the attribute to retrieve.</param>
            <returns> The <code>Attr</code> value as a string, or the empty string 
            if that attribute does not have a specified or default value.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.SetAttribute(System.String,System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.RemoveAttribute(System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetAttributeNode(System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.SetAttributeNode(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.RemoveAttributeNode(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr)">
            <summary> Removes the specified attribute node. If the removed <code>Attr</code> 
            has a default value it is immediately replaced. The replacing 
            attribute has the same namespace URI and local name, as well as the 
            original prefix, when applicable.
            </summary>
            <param name="oldAttrThe"><code>Attr</code> node to remove from the attribute 
            list.
            </param>
            <returns>The <code>Attr</code> node that was removed.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            <br />NOT_FOUND_ERR: Raised if <code>oldAttr</code> is not an attribute 
            of the element.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetElementsByTagName(System.String)">
            <summary> Returns a <code>NodeList</code> of all descendant <code>Elements</code> 
            with a given tag name, in the order in which they are encountered in 
            a preorder traversal of this <code>Element</code> tree.
            </summary>
            <param name="nameThe">name of the tag to match on. The special value "*" 
            matches all tags.
            </param>
            <returns> A list of matching <code>Element</code> nodes.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetAttributeNS(System.String,System.String)">
            <summary> Retrieves an attribute value by local name and namespace URI. HTML-only 
            DOM implementations do not need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the attribute to retrieve.</param>
            <param name="localNameThe">local name of the attribute to retrieve.</param>
            <returns> The <code>Attr</code> value as a string, or the empty string 
            if that attribute does not have a specified or default value.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.SetAttributeNS(System.String,System.String,System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.RemoveAttributeNS(System.String,System.String)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetAttributeNodeNS(System.String,System.String)">
            <summary> Retrieves an <code>Attr</code> node by local name and namespace URI. 
            HTML-only DOM implementations do not need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the attribute to retrieve.</param>
            <param name="localNameThe">local name of the attribute to retrieve.</param>
            <returns> The <code>Attr</code> node with the specified attribute local 
            name and namespace URI or <code>null</code> if there is no such 
            attribute.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.SetAttributeNodeNS(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.GetElementsByTagNameNS(System.String,System.String)">
            <summary> Returns a <code>NodeList</code> of all the descendant 
            <code>Elements</code> with a given local name and namespace URI in 
            the order in which they are encountered in a preorder traversal of 
            this <code>Element</code> tree.
            <br />HTML-only DOM implementations do not need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the elements to match on. The 
            special value "*" matches all namespaces.
            </param>
            <param name="localNameThe">local name of the elements to match on. The 
            special value "*" matches all local names.
            </param>
            <returns>A new <code>NodeList</code> object containing all the matched 
            <code>Elements</code>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.HasAttribute(System.String)">
            <summary> Returns <code>true</code> when an attribute with a given name is 
            specified on this element or has a default value, <code>false</code> 
            otherwise.
            </summary>
            <param name="nameThe">name of the attribute to look for.</param>
            <returns> <code>true</code> if an attribute with the given name is 
            specified on this element or has a default value, <code>false</code>
            otherwise.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IElement.HasAttributeNS(System.String,System.String)">
            <summary> Returns <code>true</code> when an attribute with a given local name and 
            namespace URI is specified on this element or has a default value, 
            <code>false</code> otherwise. HTML-only DOM implementations do not 
            need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the attribute to look for.</param>
            <param name="localNameThe">local name of the attribute to look for.</param>
            <returns> <code>true</code> if an attribute with the given local name 
            and namespace URI is specified or has a default value on this 
            element, <code>false</code> otherwise.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IEntityReference" -->
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.Length">
            <summary> The number of nodes in this map. The range of valid child node indices 
            is <code>0</code> to <code>length-1</code> inclusive. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.GetNamedItem(System.String)">
            <summary> Retrieves a node specified by name.</summary>
            <param name="nameThe"><code>nodeName</code> of a node to retrieve.</param>
            <returns> A <code>Node</code> (of any type) with the specified 
            <code>nodeName</code>, or <code>null</code> if it does not identify 
            any node in this map.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.SetNamedItem(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.RemoveNamedItem(System.String)">
            <summary> Removes a node specified by name. When this map contains the attributes 
            attached to an element, if the removed attribute is known to have a 
            default value, an attribute immediately appears containing the 
            default value as well as the corresponding namespace URI, local name, 
            and prefix when applicable.
            </summary>
            <param name="nameThe"><code>nodeName</code> of the node to remove.</param>
            <returns> The node removed from this map if a node with such a name 
            exists.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NOT_FOUND_ERR: Raised if there is no node named <code>name</code> in 
            this map.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this map is readonly.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.Item(System.Int32)">
            <summary> Returns the <code>index</code>th item in the map. If <code>index</code> 
            is greater than or equal to the number of nodes in this map, this 
            returns <code>null</code>.
            </summary>
            <param name="indexIndex">into this map.</param>
            <returns> The node at the <code>index</code>th position in the map, or 
            <code>null</code> if that is not a valid index.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.GetNamedItemNS(System.String,System.String)">
            <summary> Retrieves a node specified by local name and namespace URI. HTML-only 
            DOM implementations do not need to implement this method.
            </summary>
            <param name="namespaceURIThe">namespace URI of the node to retrieve.</param>
            <param name="localNameThe">local name of the node to retrieve.</param>
            <returns> A <code>Node</code> (of any type) with the specified local 
            name and namespace URI, or <code>null</code> if they do not 
            identify any node in this map.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.SetNamedItemNS(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)" -->
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INamedNodeMap.RemoveNamedItemNS(System.String,System.String)" -->
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.NodeName">
            <summary> The name of this node, depending on its type; see the table above.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.NodeValue">
            <summary> The value of this node, depending on its type; see the table above. 
            When it is defined to be <code>null</code>, setting it has no effect.
            </summary>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised when the node is readonly.
            </exception>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            DOMSTRING_SIZE_ERR: Raised when it would return more characters than 
            fit in a <code>DOMString</code> variable on the implementation 
            platform.
            </exception>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.NodeType">
            <summary> A code representing the type of the underlying object, as defined above.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.ParentNode">
            <summary> The parent of this node. All nodes, except <code>Attr</code>, 
            <code>Document</code>, <code>DocumentFragment</code>, 
            <code>Entity</code>, and <code>Notation</code> may have a parent. 
            However, if a node has just been created and not yet added to the 
            tree, or if it has been removed from the tree, this is 
            <code>null</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.ChildNodes">
            <summary> A <code>NodeList</code> that contains all children of this node. If 
            there are no children, this is a <code>NodeList</code> containing no 
            nodes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.FirstChild">
            <summary> The first child of this node. If there is no such node, this returns 
            <code>null</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.LastChild">
            <summary> The last child of this node. If there is no such node, this returns 
            <code>null</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.PreviousSibling">
            <summary> The node immediately preceding this node. If there is no such node, 
            this returns <code>null</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.NextSibling">
            <summary> The node immediately following this node. If there is no such node, 
            this returns <code>null</code>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.Attributes">
            <summary> A <code>NamedNodeMap</code> containing the attributes of this node (if 
            it is an <code>Element</code>) or <code>null</code> otherwise. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.OwnerDocument">
            <summary> The <code>Document</code> object associated with this node. This is 
            also the <code>Document</code> object used to create new nodes. When 
            this node is a <code>Document</code> or a <code>DocumentType</code> 
            which is not used with any <code>Document</code> yet, this is 
            <code>null</code>.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.NamespaceURI" -->
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.Prefix" -->
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.LocalName" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.InsertBefore(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode,Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)">
            <summary> Inserts the node <code>newChild</code> before the existing child node 
            <code>refChild</code>. If <code>refChild</code> is <code>null</code>, 
            insert <code>newChild</code> at the end of the list of children.
            <br />If <code>newChild</code> is a <code>DocumentFragment</code> object, 
            all of its children are inserted, in the same order, before 
            <code>refChild</code>. If the <code>newChild</code> is already in the 
            tree, it is first removed.
            </summary>
            <param name="newChildThe">node to insert.</param>
            <param name="refChildThe">reference node, i.e., the node before which the new 
            node must be inserted.</param>
            <returns>The node being inserted.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            HIERARCHY_REQUEST_ERR: Raised if this node is of a type that does not 
            allow children of the type of the <code>newChild</code> node, or if 
            the node to insert is one of this node's ancestors.
            <br />WRONG_DOCUMENT_ERR: Raised if <code>newChild</code> was created 
            from a different document than the one that created this node.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly or 
            if the parent of the node being inserted is readonly.
            <br />NOT_FOUND_ERR: Raised if <code>refChild</code> is not a child of 
            this node.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.ReplaceChild(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode,Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.RemoveChild(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)">
            <summary> Removes the child node indicated by <code>oldChild</code> from the list 
            of children, and returns it.
            </summary>
            <param name="oldChildThe">node being removed.</param>
            <returns>The node removed.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            <br />NOT_FOUND_ERR: Raised if <code>oldChild</code> is not a child of 
            this node.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.AppendChild(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)">
            <summary> Adds the node <code>newChild</code> to the end of the list of children 
            of this node. If the <code>newChild</code> is already in the tree, it 
            is first removed.
            </summary>
            <param name="newChildThe">node to add.If it is a <code>DocumentFragment</code>
            object, the entire contents of the document fragment are moved 
            into the child list of this node
            </param>
            <returns>The node added.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            HIERARCHY_REQUEST_ERR: Raised if this node is of a type that does not 
            allow children of the type of the <code>newChild</code> node, or if 
            the node to append is one of this node's ancestors.
            <br />WRONG_DOCUMENT_ERR: Raised if <code>newChild</code> was created 
            from a different document than the one that created this node.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.HasChildNodes">
            <summary> Returns whether this node has any children.</summary>
            <returns>  <code>true</code> if this node has any children, 
            <code>false</code> otherwise.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.CloneNode(System.Boolean)" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.Normalize">
            <summary> Puts all <code>Text</code> nodes in the full depth of the sub-tree 
            underneath this <code>Node</code>, including attribute nodes, into a 
            "normal" form where only structure (e.g., elements, comments, 
            processing instructions, CDATA sections, and entity references) 
            separates <code>Text</code> nodes, i.e., there are neither adjacent 
            <code>Text</code> nodes nor empty <code>Text</code> nodes. This can 
            be used to ensure that the DOM view of a document is the same as if 
            it were saved and re-loaded, and is useful when operations (such as 
            XPointer  lookups) that depend on a particular document tree 
            structure are to be used.In cases where the document contains 
            <code>CDATASections</code>, the normalize operation alone may not be 
            sufficient, since XPointers do not differentiate between 
            <code>Text</code> nodes and <code>CDATASection</code> nodes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.IsSupported(System.String,System.String)">
            <summary> Tests whether the DOM implementation implements a specific feature and 
            that feature is supported by this node.
            </summary>
            <param name="featureThe">name of the feature to test. This is the same name 
            which can be passed to the method <code>hasFeature</code> on 
            <code>DOMImplementation</code>.
            </param>
            <param name="versionThis">is the version number of the feature to test. In 
            Level 2, version 1, this is the string "2.0". If the version is not 
            specified, supporting any version of the feature will cause the 
            method to return <code>true</code>.
            </param>
            <returns> Returns <code>true</code> if the specified feature is 
            supported on this node, <code>false</code> otherwise.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode.HasAttributes">
            <summary> Returns whether this node (if it is an element) has any attributes.</summary>
            <returns> <code>true</code> if this node has any attributes, 
            <code>false</code> otherwise.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INodeList" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INodeList.Length">
            <summary> The number of nodes in the list. The range of valid child node indices 
            is 0 to <code>length-1</code> inclusive. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INodeList.Item(System.Int32)">
            <summary> Returns the <code>index</code>th item in the collection. If 
            <code>index</code> is greater than or equal to the number of nodes in 
            the list, this returns <code>null</code>.
            </summary>
            <param name="indexIndex">into the collection.</param>
            <returns> The node at the <code>index</code>th position in the 
            <code>NodeList</code>, or <code>null</code> if that is not a valid 
            index.
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IProcessingInstruction" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IProcessingInstruction.Target">
            <summary> The target of this processing instruction. XML defines this as being 
            the first token following the markup that begins the processing 
            instruction.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IProcessingInstruction.Data">
            <summary> The content of this processing instruction. This is from the first non 
            white space character after the target to the character immediately 
            preceding the <code>?&gt;</code>.
            </summary>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            NO_MODIFICATION_ALLOWED_ERR: Raised when the node is readonly.
            </exception>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IText" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IText.SplitText(System.Int32)">
            <summary> Breaks this node into two nodes at the specified <code>offset</code>, 
            keeping both in the tree as siblings. After being split, this node 
            will contain all the content up to the <code>offset</code> point. A 
            new node of the same type, which contains all the content at and 
            after the <code>offset</code> point, is returned. If the original 
            node had a parent node, the new node is inserted as the next sibling 
            of the original node. When the <code>offset</code> is equal to the 
            length of this node, the new node has no data.
            </summary>
            <param name="offsetThe">16-bit unit offset at which to split, starting from 
            <code>0</code>.
            </param>
            <returns> The new node, of the same type as this node.</returns>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException">
            INDEX_SIZE_ERR: Raised if the specified offset is negative or greater 
            than the number of 16-bit units in <code>data</code>.
            <br />NO_MODIFICATION_ALLOWED_ERR: Raised if this node is readonly.
            </exception>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.ELEMENT_NODE">
            <summary> The node is an <code>Element</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.ATTRIBUTE_NODE">
            <summary> The node is an <code>Attr</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.TEXT_NODE">
            <summary> The node is a <code>Text</code> node.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.CDATA_SECTION_NODE">
            <summary> The node is a <code>CDATASection</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.ENTITY_REFERENCE_NODE">
            <summary> The node is an <code>EntityReference</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.ENTITY_NODE">
            <summary> The node is an <code>Entity</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.PROCESSING_INSTRUCTION_NODE">
            <summary> The node is a <code>ProcessingInstruction</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.COMMENT_NODE">
            <summary> The node is a <code>Comment</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.DOCUMENT_NODE">
            <summary> The node is a <code>Document</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.DOCUMENT_TYPE_NODE">
            <summary> The node is a <code>DocumentType</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.DOCUMENT_FRAGMENT_NODE">
            <summary> The node is a <code>DocumentFragment</code>.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.NodeType.NOTATION_NODE">
            <summary> The node is a <code>Notation</code>.</summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AlignAttrCheck">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AnchorCheckTableCheckAttribs">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AreaCheckTableCheckAttribs">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AttrCheckImpl">
            <summary>
            Check attribute values implementations
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Attribute">
            <summary>
            HTML attribute
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AttributeTable">
            <summary>
            HTML attribute hash table
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.AttVal">
            <summary>
            Attribute/Value linked list node
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.BoolAttrCheck">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CaptionCheckTableCheckAttribs">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding">
            <summary>
            Character encoding
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.Raw">
            <summary>
            Raw
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.ASCII">
            <summary>
            Ascii
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.Latin1">
            <summary>
            Latin-1
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.UTF8">
            <summary>
            UTF-8
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.ISO2022">
            <summary>
            ISO-2022
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CharEncoding.MacroMan">
            <summary>
            Mac
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.CheckAttribsImpl">
            <summary>
            Check HTML attributes implementation
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Clean">
            <summary>
            Clean up misuse of presentation markup
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks>
            Filters from other formats such as Microsoft Word
            often make excessive use of presentation markup such
            as font tags, B, I, and the align attribute. By applying
            a set of production rules, it is straight forward to
            transform this to use CSS.
            
            Some rules replace some of the children of an element by
            style properties on the element, e.g.
            
            &lt;p&gt;&lt;b&gt;...&lt;/b&gt;&lt;/p&gt; -&gt; &lt;p style="font-weight: bold"&gt;...&lt;/p&gt;
            
            Such rules are applied to the element's content and then 
            to the element itself until none of the rules more apply.
            Having applied all the rules to an element, it will have
            a style attribute with one or more properties. 
            
            Other rules strip the element they apply to, replacing
            it by style properties on the contents, e.g.
            
            &lt;dir&gt;&lt;li&gt;&lt;p&gt;...&lt;/li&gt;&lt;/dir&gt; -&gt; &lt;p style="margin-left 1em"&gt;...
            
            These rules are applied to an element before processing
            its content and replace the current element by the first
            element in the exposed content.
            
            After applying both sets of rules, you can replace the
            style attribute by a class value and style rule in the
            document head. To support this, an association of styles
            and class names is built.
            
            A naive approach is to rely on string matching to test
            when two property lists are the same. A better approach
            would be to first sort the properties before matching.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ClsStreamInImpl">
            <summary>
            .NET native Input Stream Implementation
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ContentModel">
            <summary>
            Content Model enum.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dict">
            <summary>
            Tag dictionary node
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType">
            <summary>
            DOCTYPE enumeration.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType.Omit">
            <summary>
            Omit / omitted
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType.Auto">
            <summary>
            Automatic
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType.Strict">
            <summary>
            Strict
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType.Loose">
            <summary>
            Loose
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DocType.User">
            <summary>
            User-defined
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrImpl">
            <summary>
            DomAttrImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrImpl.Value" -->
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrImpl.OwnerElement">
            <summary> DOM2 - not implemented.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrMapImpl">
            <summary>
            DomAttrMapImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrMapImpl.GetNamedItemNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrMapImpl.SetNamedItemNS(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode)">
            <summary> DOM2 - not implemented. </summary>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException"></exception>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomAttrMapImpl.RemoveNamedItemNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
            <exception cref="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.DomException"></exception>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomCdataSectionImpl">
            <summary>
            DomCdataSectionImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <author>Gary L Peskin &lt;<EMAIL>&gt;</author>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomCharacterDataImpl">
            <summary>
            DomCharacterDataImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomCommentImpl">
            <summary>
            DomCommentImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl">
            <summary>
            DomDocumentImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl.ImportNode(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.INode,System.Boolean)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl.CreateAttributeNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl.CreateElementNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl.GetElementsByTagNameNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentImpl.GetElementById(System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentTypeImpl">
            <summary>
            DomDocumentTypeImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentTypeImpl.PublicId">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentTypeImpl.SystemId">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomDocumentTypeImpl.InternalSubset">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl">
            <summary>
            DOMElementImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.SetAttributeNS(System.String,System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.RemoveAttributeNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.GetAttributeNodeNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.SetAttributeNodeNS(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Dom.IAttr)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.GetElementsByTagNameNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.HasAttribute(System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DOMElementImpl.HasAttributeNS(System.String,System.String)">
            <summary> DOM2 - not implemented. </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl">
            <summary>
            DomNodeImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.NamespaceURI">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.Prefix">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.LocalName">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.Normalize">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.Supports(System.String,System.String)">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeImpl.IsSupported(System.String,System.String)">
            <summary> DOM2 - not implemented.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeListByTagNameImpl" -->
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomNodeListImpl">
            <summary>
            DomNodeListImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks> <p>The items in the <code>NodeList</code> are accessible via an integral 
            index, starting from 0. 
            </p>
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomProcessingInstructionImpl">
            <summary>
            DomProcessingInstructionImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.DomTextImpl">
            <summary>
            DomTextImpl
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Entity">
            <summary>
            HTML ISO entity
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.EntityTable">
            <summary>
            Entity hash table
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HrCheckTableCheckAttribs">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlCheckAttribs">
            <summary>
            Check attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser" -->
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser.ParseInternal(System.IO.Stream,System.IO.Stream,Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection)">
            <summary> Parses InputStream in and returns the root Node.
            If out is non-null, pretty prints to OutputStream out.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser.ParseInternal(System.IO.Stream,System.String,System.IO.Stream,Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection)">
            <summary> Internal routine that actually does the parsing.  The caller
            can pass either an InputStream or file name.  If both are passed,
            the file name is preferred.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser.ParseDom(System.IO.Stream,System.IO.Stream,Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection)">
            <summary> Parses InputStream in and returns a DOM Document node.
            If out is non-null, pretty prints to OutputStream out.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser.ParseDom(System.IO.Stream,Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection)">
            <summary>
            Parses InputStream in and returns a DOM Document node.        
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParser.CreateEmptyDocument">
            <summary> Creates an empty DOM Document.</summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessage">
            <summary>
            A message from HtmlParser.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection">
            <summary>
            Collection of TidyMessages
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection.#ctor">
            <summary>
            Public default constructor
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection.Add(Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessage)">
            <summary>
            Adds a message.
            </summary>
            <param name="message">The message to add.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection.Errors">
            <summary> Errors - the number of errors that occurred in the most
            recent parse operation
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserMessageCollection.Warnings">
            <summary> Warnings - the number of warnings that occurred in the most
            recent parse operation
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions">
            <summary>
            HtmlParser options.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.#ctor">
            <summary>
            Default constructor.
            </summary>
            <param name="configuration"></param>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.Spaces">
            <summary>Default indentation</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapLen">
            <summary>Default wrap margin</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.CharEncoding">
            <summary>Character Encoding</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.TabSize">
            <summary>Tab size</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.IndentContent">
            <summary>Indent content of appropriate tags</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.SmartIndent">
            <summary>Does text/block level content affect indentation</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.HideEndTags">
            <summary>Suppress optional end tags</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.XmlTags">
            <summary>Treat input as XML</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.XmlOut">
            <summary>Create output as XML</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.Xhtml">
            <summary>Output XHTML</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.RawOut">
            <summary>Avoid mapping values > 127 to entities</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.UpperCaseTags">
            <summary>Output tags in upper not lower case</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.UpperCaseAttrs">
            <summary>Output attributes in upper not lower case</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.MakeClean">
            <summary>Remove presentational clutter</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.BreakBeforeBR">
            <summary>O/p newline before &lt;br&gt; or not?</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.BurstSlides">
            <summary>Create slides on each h2 element</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.NumEntities">
            <summary>Use numeric entities</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.QuoteMarks">
            <summary>Output " marks as &amp;quot;</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.QuoteNbsp">
            <summary>Output non-breaking space as entity</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.QuoteAmpersand">
            <summary>Output naked ampersand as &amp;</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapAttributeValues">
            <summary>Wrap within attribute values</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapScriptlets">
            <summary>Wrap within JavaScript string literals</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapSection">
            <summary>Wrap within &lt;![ ... ]&gt; section tags</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.AltText">
            <summary>Default text for alt attribute</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.Slidestyle">
            <summary>Style sheet for slides</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.XmlPi">
            <summary>Add &lt;?xml?&gt; for XML docs</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.DropFontTags">
            <summary>Discard presentation tags</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.DropEmptyParas">
            <summary>Discard empty p elements</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.FixComments">
            <summary>Fix comments with adjacent hyphens</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapAsp">
            <summary>Wrap within ASP pseudo elements</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapJste">
            <summary>Wrap within JSTE pseudo elements</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.WrapPhp">
            <summary>Wrap within PHP pseudo elements</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.FixBackslash">
            <summary>Fix URLs by replacing \ with /</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.IndentAttributes">
            <summary>Newline+indent before each attribute</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.LogicalEmphasis">
            <summary>Replace i by em and b by strong</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.XmlPIs">
            <summary>If set to true PIs must end with ?></summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.EncloseText">
            <summary>If true text at body is wrapped in &lt;p&gt;'s</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.EncloseBlockText">
            <summary>If true text in blocks is wrapped in &lt;p&gt;'s</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.Word2000">
            <summary>Draconian cleaning for Word2000</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.XmlSpace">
            <summary>If set to yes adds xml:space attr as needed</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.LiteralAttribs">
            <summary>If true attributes may use newlines</summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.DocType">
            <summary>
            The DOCTYPE
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlParserOptions.DocTypeStr">
            <summary> DocType - user specified doctype
            omit | auto | strict | loose | <i>fpi</i>
            where the <i>fpi</i> is a string similar to
            &quot;-//ACME//DTD HTML 3.14159//EN&quot;
            Note: for <i>fpi</i> include the double-quotes in the string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.HtmlVersion">
            <summary>
            Version of HTML.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks>
            If the document uses just HTML 2.0 tags and attributes described it as HTML 2.0
            Similarly for HTML 3.2 and the 3 flavors of HTML 4.0. If there are proprietary
            tags and attributes then describe it as HTML Proprietary. If it includes the
            xml-lang or xmlns attributes but is otherwise HTML 2.0, 3.2 or 4.0 then describe
            it as one of the flavors of Voyager (strict, loose or frameset).
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.IAttrCheck">
            <summary>
            Check attribute values
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ICheckAttribs">
            <summary>
            Check HTML attributes
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.IdAttrCheck">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ImgCheckTableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.InlineStack">
            <summary>
            Inline stack node
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.IParser">
            <summary>
            HTML Parser
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Lexer">
            <summary>
            Lexer for html parser
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks>
            Given a file stream fp it returns a sequence of tokens.
            
            GetToken(fp) gets the next token
            UngetToken(fp) provides one level undo
            
            The tags include an attribute list:
            
            - linked list of attribute/value nodes
            - each node has 2 null-terminated strings.
            - entities are replaced in attribute values
            
            white space is compacted if not in preformatted mode
            If not in preformatted mode then leading white space
            is discarded and subsequent white space sequences
            compacted to single space chars.
            
            If XmlTags is no then Tag names are folded to upper
            case and attribute names to lower case.
            
            Not yet done:
            -   Doctype subset and marked sections
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.LinkCheckTableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MapCheckTableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MessageLevel">
            <summary>
            Level of messaging.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MessageLevel.Info">
            <summary>
            Informational only.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MessageLevel.Warning">
            <summary>
            Warning.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MessageLevel.Error">
            <summary>
            Error.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MutableBoolean">
            <summary>
            Mutable Boolean
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MutableInteger">
            <summary>
            Mutable Integer
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.MutableObject">
            <summary>
            Mutable Object
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.NameAttrCheck">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Node">
            <summary>
            Node
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks>
            Used for elements and text nodes element name is null for text nodes
            start and end are offsets into lexbuf which contains the textual content of
            all elements in the parse tree.
            
            parent and content allow traversal of the parse tree in any direction.
            attributes are represented as a linked list of AttVal nodes which hold the
            strings for attribute/value pairs.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ParserImpl">
            <summary>
            HTML Parser implementation
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ParserImpl.XMLPreserveWhiteSpace(Telerik.Windows.Documents.FormatProviders.Html.Parsing.Node,Telerik.Windows.Documents.FormatProviders.Html.Parsing.TagTable)">
            <summary>  Indicates whether or not whitespace should be preserved for this element.
            If an <code>xml:space</code> attribute is found, then if the attribute value is
            <code>preserve</code>, returns <code>true</code>.  For any other value, returns
            <code>false</code>.  If an <code>xml:space</code> attribute was <em>not</em>
            found, then the following element names result in a return value of <code>true:
            pre, script, style,</code> and <code>xsl:text</code>.  Finally, if a
            <code>TagTable</code> was passed in and the element appears as the "pre" element
            in the <code>TagTable</code>, then <code>true</code> will be returned.
            Otherwise, <code>false</code> is returned.
            </summary>
            <param name="element">The <code>Node</code> to test to see if whitespace should be
            preserved.
            </param>
            <param name="tt">The <code>TagTable</code> to test for the <code>getNodePre()</code>
            function.  This may be <code>null</code>, in which case this test
            is bypassed.
            </param>
            <returns> <code>true</code> or <code>false</code>, as explained above.
            
            </returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.PPrint">
            <summary>
            Pretty print parse tree
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
            <remarks>Block-level and unknown elements are printed on
            new lines and their contents indented 2 spaces
            Inline elements are printed inline.
            Inline content is wrapped on spaces (except in
            attribute values or preformatted text, after
            start tags and before end tags
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Report">
            <summary>
            Error/informational message reporter.
            
            You should only need to edit the file TidyMessages.properties
            to localize HTML tidy.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ScriptAttrCheck">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ScriptCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.StreamIn">
            <summary>
            Input Stream
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.StreamInImpl">
            <summary>
            Input Stream Implementation
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.Style">
            <summary>
            Linked list of class names and styles
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.StyleCheckTableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.StyleProp">
            <summary>
            Linked list of style properties
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.TableCellCheckTableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.TableCheckAttribs">
            <summary>
            Check attributes.
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.TagTable">
            <summary>
            Tag dictionary node hash table
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.UrlAttrCheck">
            <summary>
            Check attribute values
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Html.Parsing.ValignAttrCheck">
            <summary>
            Check attribute values
            
            (c) 1998-2000 (W3C) MIT, INRIA, Keio University
            See HtmlParser.cs for the copyright notice.
            Derived from <a href="http://www.w3.org/People/Raggett/tidy">
            HTML HtmlParser Release 4 Aug 2000</a>
            
            </summary>
            <author>Dave Raggett &lt;<EMAIL>&gt;</author>
            <author>Andy Quick &lt;<EMAIL>&gt; (translation to Java)</author>
            <author>Seth Yates &lt;<EMAIL>&gt; (translation to C#)</author>
            <version>1.0, 1999/05/22</version>
            <version>1.0.1, 1999/05/29</version>
            <version>1.1, 1999/06/18 Java Bean</version>
            <version>1.2, 1999/07/10 HtmlParser Release 7 Jul 1999</version>
            <version>1.3, 1999/07/30 HtmlParser Release 26 Jul 1999</version>
            <version>1.4, 1999/09/04 DOM support</version>
            <version>1.5, 1999/10/23 HtmlParser Release 27 Sep 1999</version>
            <version>1.6, 1999/11/01 HtmlParser Release 22 Oct 1999</version>
            <version>1.7, 1999/12/06 HtmlParser Release 30 Nov 1999</version>
            <version>1.8, 2000/01/22 HtmlParser Release 13 Jan 2000</version>
            <version>1.9, 2000/06/03 HtmlParser Release 30 Apr 2000</version>
            <version>1.10, 2000/07/22 HtmlParser Release 8 Jul 2000</version>
            <version>1.11, 2000/08/16 HtmlParser Release 4 Aug 2000</version>
        </member>
    </members>
</doc>
