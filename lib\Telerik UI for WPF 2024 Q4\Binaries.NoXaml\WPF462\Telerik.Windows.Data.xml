<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Data</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Controls.DataControl">
            <summary>
            Represents a control that provides common functionality for all Telerik WPF controls that represent data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.ItemsSourceProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DataControl.ItemsSource"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.Items">
            <summary>
            Items is the collection of data that is used to generate the content
            of this control.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.DataControl"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnItemsPropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Called when this.Items has a property changed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.ComponentModel.PropertyChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnItemsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Called when this.Items collection changes.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.ItemsSource">
            <summary>
            Gets or sets a collection that is used to generate the content of the control.
            </summary>
            <value>The collection that is used to generate the content of the control. The default is null.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.TableDefinition">
            <summary>
            Gets the TableDefinition object for the current record collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.Rebind">
            <summary>
            Rebinds the grid.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.CreateTableDefinition">
            <summary>
            Creates the specific table definition for the <see cref="T:Telerik.Windows.Controls.DataControl"/>.
            </summary>
            <returns>The newly created <see cref="P:Telerik.Windows.Controls.DataControl.TableDefinition"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnItemsSourceChanged(System.Object,System.Object)">
            <summary>
            Called when ItemsSource property has changed.
            </summary>
            <param name="oldValue">The old value.</param>
            <param name="newValue">The new value.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.DataControl.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnPropertyChanged(System.String)">
            <summary>
            Called when a property changes.
            </summary>
            <param name="propertyName">Name of the property.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.CurrentItem">
            <summary>
            Gets the data item bound to the row that contains the current cell.
            </summary>
            <value>The data item bound to the row that contains the current cell.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.CurrentItemProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DataControl.CurrentItem" /> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnCurrentItemChanged">
            <summary>
            Called when the current item of the <see cref="T:Telerik.Windows.Controls.DataControl"/> is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.SelectedItem">
            <summary>
            Gets or sets the data item corresponding to the selected row.
            </summary>
            <value>The data item corresponding to the selected row.</value>
            <remarks>
            If the SelectionMode property is set to Extended and multiple rows are selected, use the SelectedItems property to retrieve all selected items.
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.SelectedItemProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DataControl.SelectedItem" /> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnSelectedItemChanged(System.Object,System.Object)">
            <summary>
            Called when the selected item of the <see cref="T:Telerik.Windows.Controls.DataControl"/> is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.CanUserSelect">
            <summary>
            Gets or sets a value indicating whether the user can select rows.
            </summary>
            <value>
            	<c>true</c> if user can select; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.CanUserSelectProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DataControl.CanUserSelect"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnCanUserSelectChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when property CanUserSelect changes.
            </summary>
            <param name="oldValue">Old property value.</param>
            <param name="newValue">New property value.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.IsSynchronizedWithCurrentItem">
            <summary>
            Gets or sets a value that indicates whether <see cref="T:Telerik.Windows.Controls.DataControl"/> should keep the SelectedItem synchronized with its CurrentItem property.
            </summary>
            <value>True if the SelectedItem is always synchronized with the current item; false if the SelectedItem is never synchronized; null if the SelectedItem is synchronized with the current item only if a CollectionView is used.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.IsSynchronizedWithCurrentItemProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.DataControl.IsSynchronizedWithCurrentItem"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.ShouldHandleSelection">
            <summary>
            Gets a value indicating whether this instance handles selection events and participates in the selection mechanism.
            </summary>
            <value>
            	<c>true</c> if handles selection; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="E:Telerik.Windows.Controls.DataControl.SelectionChanged">
            <summary>
            Occurs when the selected items have changed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.SelectionChangedEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.DataControl.SelectionChanged"/> routed event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DataControl.SelectionChanging">
            <summary>
            Occurs when the selected items are about to change.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DataControl.SelectionChangingEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.DataControl.SelectionChanging"/> routed event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DataControl.SelectedItems">
            <summary>
            Gets a collection that contains the data items corresponding to the selected rows.
            </summary>
            <value>A collection of the data items corresponding to the selected rows.</value>
            <remarks>If the SelectionMode property is set to Single, the SelectedItems list will contain only the SelectedItem property value.</remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.RaiseSelectionChangedEvent(Telerik.Windows.Controls.SelectionChangeEventArgs)">
            <summary>
            Raises the selection changed event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SelectionChangeEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.OnSelectionChanging(Telerik.Windows.Controls.SelectionChangingEventArgs)">
            <summary>
            Raises the <see cref="F:Telerik.Windows.Controls.DataControl.SelectionChangingEvent"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SelectionChangingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.SetIsSelected(System.Object,System.Boolean)">
            <summary>
            Sets the selection state for a container that wraps a given data item.
            </summary>
            <param name="item">The data item.</param>
            <param name="isSelected">If set to <c>true</c> container is selected.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.SetIsCurrent(System.Object,System.Boolean)">
            <summary>
            Sets the current state for a container that wraps a given data item.
            </summary>
            <param name="item">The data item.</param>
            <param name="isCurrent">If set to <c>true</c> container is current.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DataControl.InitializeSelection">
            <summary>
            Initializes the selection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.IRowItem">
            <summary>
            Supports row-like UI elements.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.IRowItem.DataContext">
            <summary>
            Gets or sets the data context.
            </summary>
            <value>The data context.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.IRowItem.RowIndicatorVisibility">
            <summary>
            Gets or sets the row indicator visibility.
            </summary>
            <value>The row indicator visibility.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.IRowItem.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadRowItem">
            <summary>
            This class is a base class for all UI Rows.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.Item">
            <summary>
                The item that the row represents. This item is an entry in the list of items from the GridViewDataControl.
                From this item, cells are generated for each column in the GridViewDataControl.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.ItemProperty">
            <summary>
                The DependencyProperty for the Item property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnItemChanged(System.Object,System.Object)">
            <summary>
                Called when the value of the Item property changes.
            </summary>
            <param name="oldItem">The old value of Item.</param>
            <param name="newItem">The new value of Item.</param>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.IsAlternatingProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadRowItem.IsAlternating"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.IsCurrentProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadRowItem.IsCurrent"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.IsSelectedProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadRowItem.IsSelected"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.SelectedEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadRowItem.Selected"/> routed event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadRowItem.Selected">
            <summary>
            Occurs when <see cref="P:Telerik.Windows.Controls.RadRowItem.IsSelected"/> property becomes true.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.UnselectedEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadRowItem.Unselected"/> routed event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadRowItem.Unselected">
            <summary>
            Occurs when <see cref="P:Telerik.Windows.Controls.RadRowItem.IsSelected"/> property becomes false.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.IsCurrentChangedEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadRowItem.IsCurrentChanged"/> routed event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadRowItem.IsCurrentChanged">
            <summary>
            Occurs when <see cref="P:Telerik.Windows.Controls.RadRowItem.IsCurrent"/> property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.IsAlternating">
            <summary>
            Gets or sets a value indicating whether this row is alternating.
            </summary>
            <value>
            	<c>True</c> if this row is alternating; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.RowIndicatorVisibility">
            <summary>
            Gets or sets the row indicator visibility.
            </summary>
            <value>The row indicator visibility.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadRowItem.RowIndicatorVisibilityProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadRowItem.RowIndicatorVisibility"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.ActionOnLostFocus">
            <summary>
            Gets or sets the action on lost focus.
            </summary>
            <value>The action on lost focus.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.IsSelected">
            <summary>
            Gets or sets a value indicating whether the <see cref="T:Telerik.Windows.Controls.RadRowItem"/> is selected.
            </summary>
            <value>
            	<c>True</c> if this instance is selected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadRowItem.IsCurrent">
            <summary>
            Gets or sets a value indicating whether the <see cref="T:Telerik.Windows.Controls.RadRowItem"/> is current.
            </summary>
            <value>
            	<c>True</c> if this instance is current; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.CreateIndentItem">
            <summary>
            Creates the item that fills in the indentation.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.IsSelectable">
            <summary>
            Determines whether this instance is selectable.
            </summary>
            <returns>
            	<c>true</c> if this instance is selectable; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.SelectionPropertyChanged(System.Object,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Called when the selection was changed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="rowSelectionArgs">The <see cref="T:System.Windows.DependencyPropertyChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnSelected(Telerik.Windows.RadRoutedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.RadRowItem.Selected"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnUnselected(Telerik.Windows.RadRoutedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Controls.RadRowItem.Unselected"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnIsCurrentChanged(System.Boolean)">
            <summary>
            Called when the value of the IsCurrent property is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnIsAlternatingChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when IsAlternating property changes.
            </summary>
            <param name="oldValue">New value.</param>
            <param name="newValue">Old value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnMouseEnter(System.Windows.Input.MouseEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.RadRowItem.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.RowItemMouseEventArgs">
            <summary>
            Provides data for the MouseUp, MouseDown, and MouseMove events for the RadRowItem object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RowItemMouseEventArgs.FromMouseEvent(System.Windows.Input.MouseEventArgs,System.Windows.UIElement)">
            <summary>
            Creates the RowItemMouseEventArgs instance.
            </summary>
            <param name="args">The <see cref="T:System.Windows.Input.MouseEventArgs"/> instance containing the event data.</param>
            <param name="targetElement">The target element.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Windows.Controls.RowItemMouseEventArgs.Position">
            <summary>
            Gets or sets the position.
            </summary>
            <value>The position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RowItemMouseEventArgs.LeftButton">
            <summary>
            Gets or sets the left button.
            </summary>
            <value>The left button.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RowItemMouseEventArgs.RightButton">
            <summary>
            Gets or sets the right button.
            </summary>
            <value>The right button.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SelectionChangeEventArgs">
            <summary>
            Initializes a new instance of the SelectionChangeEventArgs class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SelectionChangeEventArgs.RemovedItems">
            <summary>
            Gets a list that contains the items that were unselected.
            <value>Items that were unselected.</value>
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SelectionChangeEventArgs.AddedItems">
            <summary>
            Gets a list that contains the items that were selected.
            <value>Items that were selected</value>
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SelectionChangeEventArgs.#ctor(System.Collections.Generic.IList{System.Object},System.Collections.Generic.IList{System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SelectionChangeEventArgs"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SelectionChangingEventArgs">
            <summary>
            Initializes a new instance of the SelectionChangingEventArgs class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SelectionChangingEventArgs.RemovedItems">
            <summary>
            Gets a list that contains the items that are being unselected.
            <value>Items that are being unselected</value>
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SelectionChangingEventArgs.AddedItems">
            <summary>
            Gets a list that contains the items that are being selected.
            <value>Items that are being selected</value>
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SelectionChangingEventArgs.IsCancelable">
            <summary>
            Gets a value that indicates whether the event is cancelable.
            </summary>
            <value>
            	<c>true</c> if this instance is cancelable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.SelectionChangingEventArgs.#ctor(System.Collections.Generic.IList{System.Object},System.Collections.Generic.IList{System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SelectionChangingEventArgs"/> class.
            </summary>
            <param name="addedItems">The items that were selected.</param>
            <param name="removedItems">The items that were unselected.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SelectionChangingEventArgs.#ctor(System.Collections.Generic.IList{System.Object},System.Collections.Generic.IList{System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SelectionChangingEventArgs"/> class.
            </summary>
            <param name="addedItems">The items that were selected.</param>
            <param name="removedItems">The items that were unselected.</param>
            <param name="isCancelable">If set to <c>true</c> [is cancelable].</param>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateFunctionCollection">
            <summary>
            Represents a collection of <see cref="T:Telerik.Windows.Data.AggregateFunction"/> items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunctionCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Data.AggregateFunction"/> with the specified function name.
            </summary>
            <value>
            First <see cref="T:Telerik.Windows.Data.AggregateFunction"/> with the specified function name 
            if any, otherwise null.
            </value>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateResultCollection">
            <summary>
            Represents a collection of <see cref="T:Telerik.Windows.Data.AggregateResult"/> items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResultCollection.Item(System.String)">
            <summary>
            Gets the first <see cref="T:Telerik.Windows.Data.AggregateResult"/> which
            <see cref="P:Telerik.Windows.Data.AggregateResult.FunctionName"/> is equal to <paramref name="functionName"/>.
            </summary>
            <value>
            The <see cref="T:Telerik.Windows.Data.AggregateResult"/> for the specified function if any, otherwise null.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResultCollection.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.CollectionChangingEventArgs">
            <summary>
            Represents event data for CollectionChanging event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionChangingEventArgs.#ctor(System.ComponentModel.CollectionChangeAction,System.Int32,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.CollectionChangingEventArgs"/> class.
            </summary>
            <param name="action">The action.</param>
            <param name="index">The index.</param>
            <param name="item">The item.</param>
        </member>
        <member name="P:Telerik.Windows.Data.CollectionChangingEventArgs.Cancel">
            <summary>
            Set this to true to cancel the changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionChangingEventArgs.#ctor(System.ComponentModel.CollectionChangeAction)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.CollectionChangingEventArgs"/> class.
            </summary>
            <param name="action">The action.</param>
        </member>
        <member name="P:Telerik.Windows.Data.CollectionChangingEventArgs.Item">
            <summary>
            Gets or sets the item.
            </summary>
            <value>The item.</value>
        </member>
        <member name="P:Telerik.Windows.Data.CollectionChangingEventArgs.Index">
            <summary>
            Gets or sets the index.
            </summary>
            <value>The index.</value>
        </member>
        <member name="P:Telerik.Windows.Data.CollectionChangingEventArgs.Action">
            <summary>
            Gets or sets the collection change action.
            </summary>
            <value>The action.</value>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Equalize(System.Collections.IList,System.Collections.IList,System.Collections.IEqualityComparer)">
            <summary>
            Combines the left and right into a new list and
            makes left and right to be the same as the new list.
            </summary>
            <param name="left">The left list.</param>
            <param name="right">The right list.</param>
            <param name="comparer">The item equality comparer.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Equalize(System.Collections.IList,System.Collections.IList)">
            <summary>
            Combines the left and right into a new list and
            makes left and right to be the same as the new list.
            </summary>
            <param name="left">The left list.</param>
            <param name="right">The right list.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Mirror(System.Collections.IList,System.Collections.IEnumerable,System.Collections.IEqualityComparer)">
            <summary>
            Makes the target collection a mirror copy of the source, so that they both contain the same items.
            </summary>
            <param name="target">The target collection.</param>
            <param name="source">The source enumerable.</param>
            <param name="comparer">The item equality comparer.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Mirror(System.Collections.IList,System.Collections.IEnumerable)">
            <summary>
            Makes the target collection a mirror copy of the source, so that they both contain the same items.
            </summary>
            <param name="target">The target collection.</param>
            <param name="source">The source enumerable.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Synchronize(System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Collections.IEnumerable,System.Collections.IList)">
            <summary>
            Synchronizes two source and target based on the information
            stored in the e parameter.
            </summary>
            <param name="e">The arguments for synchronization.</param>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Synchronize(System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Collections.IEnumerable,System.Collections.IList,System.Func{System.Object,System.Object})">
            <summary>
            Synchronizes two source and target based on the information
            stored in the e parameter. This method uses Converter function to convert items stored in argument parameter.
            </summary>
            <param name="e">The arguments for synchronization.</param>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
            <param name="itemConverter">Function that converts items from argument collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Synchronize(System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Collections.IEnumerable,System.Collections.IList,System.Func{System.Object,System.Object},System.Collections.IEqualityComparer)">
            <summary>
            Synchronizes two source and target based on the information
            stored in the e parameter. This method uses Converter function to convert items stored in argument parameter.
            </summary>
            <param name="e">The arguments for synchronization.</param>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
            <param name="itemConverter">Function that converts items from argument collection.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.FindEqualElement(System.Collections.IEnumerable,System.Object,System.Collections.IEqualityComparer)">
            <summary>
            Search for the input element in the collection using itemComparer.
            </summary>
            <param name="collection">The collection to search in.</param>
            <param name="element">Searched element.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
            <returns>Element if found, otherwise null.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.FindEqualElements(System.Collections.IEnumerable,System.Object,System.Collections.IEqualityComparer)">
            <summary>
            Search for the input element in the collection using itemComparer.
            </summary>
            <param name="collection">The collection to search in.</param>
            <param name="element">Searched element.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
            <returns>Elements if found, otherwise empty.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Insert(System.Collections.IList,System.Object,System.Int32)">
            <summary>
            Inserts newItem in target at the specified index. If the index is
            invalid then it simply adds it to target.
            </summary>
            <param name="target">The list to insert in.</param>
            <param name="newItem">The item to insert.</param>
            <param name="index">The index at which the item will be inserted.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Insert(System.Collections.IList,System.Collections.IEnumerable,System.Int32,System.Collections.IEqualityComparer)">
            <summary>
            Inserts newItems in target at the starting from the specified index. 
            If the index is invalid then it simply adds them to target.
            </summary>
            <param name="target">The list to insert in.</param>
            <param name="newItems">The items to insert.</param>
            <param name="startingIndex">The starting index.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Remove(System.Collections.IList,System.Collections.IEnumerable,System.Collections.IEqualityComparer)">
            <summary>
            Removes items from target.
            </summary>
            <param name="target">The target from which to remove.</param>
            <param name="items">The items to remove.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Replace(System.Collections.IList,System.Object,System.Object,System.Collections.IEqualityComparer)">
            <summary>
            Replaces oldItem with newItem in target. If target does not contain
            oldItem the it simply adds newItem to target.
            </summary>
            <param name="target">The target to replace in.</param>
            <param name="newItem">The new item.</param>
            <param name="oldItem">The old item.</param>
            <param name="itemComparer">IEqualityComparer used to compare items.</param>
            <remarks>
            Replace is kind of tricky when the two collections are different.
            Imagine that source is [0, 1] and target is [1, 0] and we have
            replaced the 0 from the source with 2. The source has become [2, 1]
            We will receive:
                 target =            [1, 0]
                 newItems =          {2}
                 newStartingIndex =  0 => this is base on the source collection!!!
                 oldItems =          {0}       
            Now what should we do? Replace target[newStartingIndex] with 3. NO!
            If we do this the target will become [3, 0] and that is wrong.
            We have to at least try to locate the 0 in the target and replace it
            with the 3.
            If we cannot find it I think that we should do nothing! Replace should
            replace an existing item and if it is not there, then do nothing.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Reset(System.Collections.IEnumerable,System.Collections.IList)">
            <summary>
            Makes target equal to source.
            </summary>
            <param name="source">Source collection.</param>
            <param name="target">Target collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Reset(System.Collections.IEnumerable,System.Collections.IList,System.Func{System.Object,System.Object})">
            <summary>
            Makes target equal to source.
            </summary>
            <param name="source">Source collection.</param>
            <param name="target">Target collection.</param>
            <param name="itemConverter">Function that converts items from argument collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.Move(System.Collections.IList,System.Object,System.Int32)">
            <summary>
            Moves item to newIndex in target if it is present in target.
            Otherwise does nothing.
            </summary>
            <param name="target">The target to move in.</param>
            <param name="item">The item to move.</param>
            <param name="newIndex">The index to move the item to.</param>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionHelper.EnsureAllHaveOneElement(System.Collections.IEnumerable[])">
            <summary>
            Raises an exception if one of the enumerables does not have
            exactly one element.
            </summary>
            <param name="enumerables">The enumerables to check.</param>
        </member>
        <member name="T:Telerik.Windows.Data.DataItemCollection">
            <summary>
            DataItemCollection.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.CollectionChanged">
            <summary>
            Occurs when the collection changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.RaiseCollectionViewPropertyChanged">
            <summary>
            This is thrown in order to handle the incorrect behavior of the DomainDataSourceView
            The DomainDataSourceView is reporting PageIndex of -1 even when its PageSize is 
            greater than 0. This breaks all pagers that are listening for PropertyChanged events.
            A pager cannot move to page -1 when its page size is greater than zero.
            We have to trick the pagers!
            
            In other words, this is a simulation of the Source property of the 
            pager changing. When the Source property of a pager changes, it knows to 
            invalidate its PageIndex to -1 and everything is fine afterwards because
            it is "restarted". Here the underlying source collection is actually 
            changing, i.e. from InnerCollectionView to DomainDataSourceView,
            but the pager does not know this because its Source property is bound to us,
            i.e. "this" and for the pager the Source property has not changed. Therefore 
            we have to trick the pager to update its PageIndex to -1 as though its Source 
            has changed.
            
            When this event is caught by RadDataPager, it will change its PageIndex to -1
            without triggering the property changed callback.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.EqualizeDescriptors">
            <summary>
            Copies all local descriptors to the CollectionView and then updates the 
            local collection with all descriptors from the CollectionView. In fact
            this synchronizes the two collections.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsGrouped">
            <summary>
            Gets a value indicating whether this instance is grouped.
            </summary>
            <value>
            	<c>true</c> if this instance is grouped; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.DataItemCollection"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Add(System.Object)">
            <summary>
            Adds an item to the <see cref="T:System.Collections.IList"/>.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to add to the <see cref="T:System.Collections.IList"/>.</param>
            <returns>
            The position into which the new element was inserted.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            -or-
            The <see cref="T:System.Collections.IList"/> has a fixed size.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.IList"/>.
            </summary>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:System.Collections.IList"/> contains a specific value.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to locate in the <see cref="T:System.Collections.IList"/>.</param>
            <returns>
            True if the <see cref="T:System.Object"/> is found in the <see cref="T:System.Collections.IList"/>; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.IndexOf(System.Object)">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.IList"/>.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to locate in the <see cref="T:System.Collections.IList"/>.</param>
            <returns>
            The index of <paramref name="value"/> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Insert(System.Int32,System.Object)">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.IList"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="value"/> should be inserted.</param>
            <param name="value">The <see cref="T:System.Object"/> to insert into the <see cref="T:System.Collections.IList"/>.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.IList"/>.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            -or-
            The <see cref="T:System.Collections.IList"/> has a fixed size.
            </exception>
            <exception cref="T:System.NullReferenceException">
            	<paramref name="value"/> is null reference in the <see cref="T:System.Collections.IList"/>.
            </exception>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.System#Collections#IList#IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IList"/> has a fixed size.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IList"/> has a fixed size; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.System#Collections#IList#IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IList"/> is read-only.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IList"/> is read-only; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.Item(System.Int32)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> at the specified index.
            </summary>
            <value></value>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is less than zero.
            </exception>
            <exception cref="T:System.ArgumentException">
            	<paramref name="array"/> is multidimensional.
            -or-
            <paramref name="index"/> is equal to or greater than the length of <paramref name="array"/>.
            -or-
            The number of elements in the source <see cref="T:System.Collections.ICollection"/> is greater than the available space from <paramref name="index"/> to the end of the destination <paramref name="array"/>.
            </exception>
            <exception cref="T:System.ArgumentException">
            The type of the source <see cref="T:System.Collections.ICollection"/> cannot be cast automatically to the type of the destination <paramref name="array"/>.
            </exception>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>
            The number of elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.ItemType">
            <summary>
            Gets or sets the type used for all internal data engine operations.
            </summary>
            <value>The type of the item.</value>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.ItemProperties">
            <summary>
            Gets a collection of objects that describes the properties of the items in the collection.
            </summary>
            <returns>
            A collection of objects that describes the properties of the items in the collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.ItemPropertiesByPropertyName">
            <summary>
            Gets a dictionary of objects that describes the properties of the items in the collection by property name.
            </summary>
            <returns>
            A dictionary of objects that describes the properties of the items in the collection by property name.
            </returns>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.GroupCollectionChanged">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.BindToInnerView">
            <summary>
            Binds our *Descriptor collections to our internal QCV's *Descriptor collections.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.BindDescriptorsToDescriptions">
            <summary>
            Binds our own *Descriptor collections to our *Description collections.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.BindCollections(System.Collections.Specialized.INotifyCollectionChanged,System.Collections.Specialized.INotifyCollectionChanged,System.Collections.IEqualityComparer,System.Boolean)">
            <summary>
            Binds two collections together using the ObservableCollectionManager and an equality comparer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.BindCollections(System.Collections.Specialized.INotifyCollectionChanged,System.Collections.Specialized.INotifyCollectionChanged,System.Collections.IEqualityComparer,System.Func{System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Boolean},System.Func{System.Collections.Specialized.NotifyCollectionChangedEventArgs,System.Boolean},System.Boolean)">
            <summary>
            Binds two collections together using the ObservableCollectionManager and an equality comparer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.DescriptorsSynchronizationMode">
            <summary>
            Gets or sets a value that controls how this DataItemCollection synchronizes its FilterDescriptors, GroupDescriptor and SortDescriptors,
            as well as GroupDescription and SortDescriptions (if applicable), with its source.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.SourceCollectionView">
            <summary>
            Gets the SourceCollection as ICollectionView.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.SourcePagedCollectionView">
            <summary>
            Gets the SourceCollection as IPagedCollectionView.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.AddNewItem(System.Object)">
            <summary>
            Adds the specified object to the collection.
            </summary>
            <param name="newItem">The object to add to the collection.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.AddNew">
            <summary>
            Adds a new item to the collection.
            </summary>
            <returns>
            The new item that is added to the collection.
            </returns>
            <exception cref="T:System.InvalidOperationException"><see cref="P:Telerik.Windows.Data.DataItemCollection.CanAddNew"/> is false.</exception>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanAddNew">
            <summary>
            Gets a value that indicates whether a new item can be added to the collection.
            </summary>
            <returns>
            true if <see cref="P:Telerik.Windows.Data.DataItemCollection.SourceCollection"/> is not <see cref="P:System.Collections.IList.IsFixedSize"/> and 
            collection element has a default constructor; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanAddNewItem">
            <summary>
            Gets a value that indicates whether a specified object can be added to the collection.
            </summary>
            <value>
            	<c>true</c> if a specified object can be added to the collection; otherwise <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanCancelEdit">
            <summary>
            Gets a value that indicates whether the editing of an item can be canceled.
            </summary>
            <returns>
            true if <see cref="P:Telerik.Windows.Data.DataItemCollection.CurrentEditItem"/> implements <see cref="T:System.ComponentModel.IEditableObject"/>; 
            otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanRemove">
            <summary>
            Gets a value that indicates whether an item can be removed from the collection.
            </summary>
            <returns>
            true if an item can be removed from the collection; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.CancelEdit">
            <summary>
            Ends the edit transaction and discards any pending changes to the item.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            CancelEdit is not supported for the current edit item. 
            Only items implementing <see cref="T:System.ComponentModel.IEditableObject"/> are supported.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.CancelNew">
            <summary>
            Ends the add transaction and discards the pending new item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.CommitEdit">
            <summary>
            Ends the edit transaction and saves the pending changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.CommitNew">
            <summary>
            Ends the add transaction and saves the pending new item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CurrentAddItem">
            <summary>
            Gets the item that is being added during the current add transaction.
            </summary>
            <returns>
            The item that is being added if <see cref="P:Telerik.Windows.Data.DataItemCollection.IsAddingNew"/> is true; otherwise, null.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CurrentEditItem">
            <summary>
            Gets the item in the collection that is being edited.
            </summary>
            <returns>
            The item in the collection that is being edited 
            if <see cref="P:Telerik.Windows.Data.DataItemCollection.IsEditingItem"/> is true; otherwise, null.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.EditItem(System.Object)">
            <summary>
            Begins an edit transaction of the specified item.
            </summary>
            <param name="item">The item to edit.</param>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsAddingNew">
            <summary>
            Gets a value that indicates whether an add transaction is in progress.
            </summary>
            <returns>
            true if an add transaction is in progress; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsEditingItem">
            <summary>
            Gets a value that indicates whether an edit transaction is in progress.
            </summary>
            <returns>
            true if an edit transaction is in progress; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.NewItemPlaceholderPosition">
            <summary>
            Gets or sets the position of the new item placeholder in the collection.
            </summary>
            <returns>
            One of the enumeration values that specifies the position of the new item placeholder in the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Remove(System.Object)">
            <summary>
            Removes the specified item from the collection.
            </summary>
            <param name="item">The item to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the item at the specified position from the collection.
            </summary>
            <param name="index">The position of the item to remove.</param>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.CurrentChanged">
            <summary>
            When implementing this interface, raise this event after the current item has been changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.CurrentChanging">
            <summary>
            When implementing this interface, raise this event before changing the current item. Event handler can cancel this event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanFilter">
            <summary>
            Gets a value that indicates whether this view supports filtering via the <see cref="P:System.ComponentModel.ICollectionView.Filter"/> property.
            </summary>
            <value></value>
            <returns>true if this view support filtering; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanGroup">
            <summary>
            Gets a value that indicates whether this view supports grouping via the <see cref="P:System.ComponentModel.ICollectionView.GroupDescriptions"/> property.
            </summary>
            <value></value>
            <returns>true if this view supports grouping; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanSort">
            <summary>
            Gets a value that indicates whether this view supports sorting via the <see cref="P:System.ComponentModel.ICollectionView.SortDescriptions"/> property.
            </summary>
            <value></value>
            <returns>true if this view supports sorting; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.Culture">
            <summary>
            Gets or sets the cultural info for any operations of the view that may differ by culture, such as sorting.
            </summary>
            <returns>
            The culture to use during sorting.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the <see cref="E:PropertyChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CurrentItem">
            <summary>
            Gets the current item in the view.
            </summary>
            <value></value>
            <returns>
            The current item of the view or null if there is no current item.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CurrentPosition">
            <summary>
            Gets the ordinal position of the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> within the view.
            </summary>
            <value></value>
            <returns>
            The ordinal position of the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> within the view.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.DeferRefresh">
            <summary>
            Enters a defer cycle that you can use to merge changes to the view and delay automatic refresh.
            </summary>
            <returns>
            An <see cref="T:System.IDisposable"/> object that you can use to dispose of the calling object.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.EndDefer">
            <summary>
            Ends the defer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.Filter">
            <summary>
            Gets or sets a callback used to determine if an item is suitable for inclusion in the view.
            </summary>
            <value></value>
            <returns>
            A method used to determine if an item is suitable for inclusion in the view.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.GroupDescriptions">
            <summary>
            Gets a collection of <see cref="T:System.ComponentModel.GroupDescription"/> objects that describe how the items in the collection are grouped in the view.
            </summary>
            <value></value>
            <returns>
            A collection of <see cref="T:System.ComponentModel.GroupDescription"/> objects that describe how the items in the collection are grouped in the view.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.Groups">
            <summary>
            Gets the top-level groups.
            </summary>
            <value></value>
            <returns>
            A read-only collection of the top-level groups or null if there are no groups.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsCurrentAfterLast">
            <summary>
            Gets a value that indicates whether the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> of the view is beyond the end of the collection.
            </summary>
            <value></value>
            <returns>
            Returns true if the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> of the view is beyond the end of the collection; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsCurrentBeforeFirst">
            <summary>
            Gets a value that indicates whether the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> of the view is beyond the beginning of the collection.
            </summary>
            <value></value>
            <returns>
            Returns true if the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> of the view is beyond the beginning of the collection; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsEmpty">
            <summary>
            Returns a value that indicates whether the resulting view is empty.
            </summary>
            <value></value>
            <returns>true if the resulting view is empty; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentTo(System.Object)">
            <summary>
            Sets the specified item to be the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> in the view.
            </summary>
            <param name="item">The item to set as the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/>.</param>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentToFirst">
            <summary>
            Sets the first item in the view as the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/>.
            </summary>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentToLast">
            <summary>
            Sets the last item in the view as the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/>.
            </summary>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentToNext">
            <summary>
            Sets the item after the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> in the view as the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/>.
            </summary>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentToPosition(System.Int32)">
            <summary>
            Sets the item at the specified index to be the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> in the view.
            </summary>
            <param name="position">The index to set the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> to.</param>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveCurrentToPrevious">
            <summary>
            Sets the item before the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> in the view as the <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/>.
            </summary>
            <returns>
            True if the resulting <see cref="P:System.ComponentModel.ICollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.OnCurrentChanging(System.ComponentModel.CurrentChangingEventArgs)">
            <summary>
            Invokes the CurrentChanging event.
            </summary>
            <param name="e">Arguments that carry the information of the event.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.OnCurrentChanged(System.EventArgs)">
            <summary>
            Invokes CurrentChanged event.
            </summary>
            <param name="e">Arguments that carry data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.Refresh">
            <summary>
            Recreates the view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.SortDescriptions">
            <summary>
            Gets a collection of <see cref="T:System.ComponentModel.SortDescription"/> objects that describe how the items in the collection are sorted in the view.
            </summary>
            <value></value>
            <returns>
            A collection of <see cref="T:System.ComponentModel.SortDescription"/> objects that describe how the items in the collection are sorted in the view.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.SourceCollection">
            <summary>
            Returns the underlying collection.
            </summary>
            <value></value>
            <returns>
            An <see cref="T:System.Collections.IEnumerable"/> object that is the underlying collection.
            </returns>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.PageChanged">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.DataItemCollection.PageIndex" /> has changed.</summary>
        </member>
        <member name="E:Telerik.Windows.Data.DataItemCollection.PageChanging">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.DataItemCollection.PageIndex" /> is changing.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveToFirstPage">
            <summary>Sets the first page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveToLastPage">
            <summary>Sets the last page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveToNextPage">
            <summary>Moves to the page after the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveToPage(System.Int32)">
            <summary>Requests a page move to the page at the specified index.</summary>
            <returns>true if the move was successfully initiated; otherwise, false.</returns>
            <param name="pageIndex">The index of the page to move to.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DataItemCollection.MoveToPreviousPage">
            <summary>Moves to the page before the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.CanChangePage">
            <summary>
            Gets a value that indicates whether the <see cref="P:Telerik.Windows.Data.DataItemCollection.PageIndex" /> value can change.
            </summary>
            <returns>
            true if the <see cref="P:Telerik.Windows.Data.DataItemCollection.PageIndex" /> value can change; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.IsPageChanging">
            <summary>Gets a value that indicates whether a page index change is in process.</summary>
            <returns>true if the page index is changing; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.ItemCount">
            <summary>
            Gets the minimum number of items known to be in the source collection.
            </summary>
            <returns>
            The minimum number of items known to be in the source collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.PageIndex">
            <summary>Gets the zero-based index of the current page.</summary>
            <returns>The zero-based index of the current page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.PageSize">
            <summary>Gets or sets the number of items to display on a page.</summary>
            <returns>The number of items to display on a page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.DataItemCollection.TotalItemCount">
            <summary>
            Gets the total number of items in the source collection, or -1 if the total number is unknown.
            </summary>
            <returns>
            The total number of items in the source collection, or -1 if the total number is unknown.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.DependencyObjectCollection`1">
            <summary>
            Collection class for propagating inheritance context to child elements in WPF.
            </summary>
            <typeparam name="T">The type of elements in the collection.</typeparam>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Expand">
            <summary>
            Expand.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Collapse">
            <summary>
            Collapse.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Add">
            <summary>
            Add.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Remove">
            <summary>
            Remove.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Reset">
            <summary>
            Reset.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyCollectionAction.Replace">
            <summary>
            Replace.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.KeyedCollection.NullValue">
            <summary>
            Specifies a static value that is used by KeyedCollection to handle scenarios
            when QueryableCollectionViewGroup.Key is null.
            </summary>
            <value>A fake null value.</value>
        </member>
        <member name="T:Telerik.Windows.Data.KeyedCollection.KeyedCollectionNullValue">
            <summary>
            Thread-safe singleton implementation for specifying a 
            static value that is used by KeyedCollection rather 
            than null to indicate that key is null.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.KeyedCollection.KeyedCollectionNullValue.Instance">
            <summary>
            Gets or sets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Telerik.Windows.Data.KeyedCollection.KeyedCollectionNullValue.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Data.KeyedCollection.KeyedCollectionNullValue" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.KeyedCollection.KeyedCollectionNullValue.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.QueryableCollectionViewSource">
            <summary>
             Describes a <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableCollectionViewSource" /> class.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionViewSource.ViewPropertyKey">
            <summary>
            The key needed to define a read-only property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionViewSource.ViewProperty">
            <summary>
            The DependencyProperty for the View property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionViewSource.SourceProperty">
            <summary>
            The DependencyProperty for the Source property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewSource.CollectionView">
            <summary>
            Gets or sets a Source QCV collection that enables data operations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewSource.Source">
            <summary>
                Source is the underlying collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewSource.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewSource.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewSource.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.BindCollections(System.Collections.Specialized.INotifyCollectionChanged,System.Collections.Specialized.INotifyCollectionChanged,System.Collections.IEqualityComparer,System.Boolean)">
            <summary>
            Binds two collections together using the ObservableCollectionManager and an equality comparer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.EqualizeDescriptors">
            <summary>
            Copies all local descriptors to the CollectionView and then updates the 
            local collection with all descriptors from the CollectionView. In fact
            this synchronizes the two collections.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.System#ComponentModel#ISupportInitialize#BeginInit">
            <summary>
            Signals the object that initialization is starting.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.System#ComponentModel#ISupportInitialize#EndInit">
            <summary>
            Signals the object that initialization is completed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing,
            or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewSource.Dispose(System.Boolean)">
            <summary>
            Unsubscribes form collection changed events.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:Telerik.Windows.Data.INotifyItemChanged">
            <summary>
            Allows access to a non-generic version of the ItemChanged event for <see cref="T:Telerik.Windows.Data.ObservableItemCollection`1"/>.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.INotifyItemChanged.ItemChanged">
            <summary>
            Raised when some collection item's property is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ObservableCollectionManager.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.ObservableCollectionManager.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.CollectionViewQueryableProxy.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.HierarchicalChildCollectionView">
            <summary>
            CollectionView used in the RadTreeListView.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalChildCollectionView.HierarchyDescriptors">
            <inheritdoc />
            <remarks>
            Gets the root view hierarchy descriptors.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalChildCollectionView.SortDescriptors">
            <inheritdoc />
            <remarks>
            Gets the sort descriptors used for sorting operations.
            If this view is a child one, its root view sort descriptors are returned.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalChildCollectionView.GroupDescriptors">
            <inheritdoc />
            <remarks>
            Gets the group descriptors used for grouping operations.
            If this view is a child one, its root view group descriptors are returned.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalChildCollectionView.FilterDescriptors">
            <inheritdoc />
            <remarks>
            Gets the filter descriptors used for filter operations.
            If this view is a child one, its root view filter descriptors are returned.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalChildCollectionView.ParentView">
            <summary>
            Gets the parent view for this view.
            </summary>
            <value>
            The parent view for this view, if the current view is a child view, otherwise <c>null</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalChildCollectionView.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.EditableCollectionViewExtensions">
            <summary>
            Extend IEditableCollectionView by adding a method used to determine if the underlying data source is fixed size.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.HierarchicalCollectionView">
            <summary>
            CollectionView used in the RadTreeListView.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalCollectionView.HierarchyDescriptors">
            <inheritdoc />
        </member>
        <member name="F:Telerik.Windows.Data.HierarchicalCollectionView.ExpandCollapseOperation.Expand">
            <summary>
            Expand.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.HierarchicalCollectionView.ExpandCollapseOperation.Collapse">
            <summary>
            Collapse.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.HierarchicalCollectionView.ExpandCollapseOperation.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionView.Remove(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.InnerCollectionView"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.InnerCollectionView.SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>
            An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.Add(System.Object)">
            <summary>
            Adds an item to the <see cref="T:System.Collections.IList"/>.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to add to the <see cref="T:System.Collections.IList"/>.</param>
            <returns>
            The position into which the new element was inserted.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            -or-
            The <see cref="T:System.Collections.IList"/> has a fixed size.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.IList"/>.
            </summary>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.Insert(System.Int32,System.Object)">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.IList"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="value"/> should be inserted.</param>
            <param name="value">The <see cref="T:System.Object"/> to insert into the <see cref="T:System.Collections.IList"/>.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.IList"/>.
            </exception>
            <exception cref="T:System.NotSupportedException">
            The <see cref="T:System.Collections.IList"/> is read-only.
            -or-
            The <see cref="T:System.Collections.IList"/> has a fixed size.
            </exception>
            <exception cref="T:System.NullReferenceException">
            	<paramref name="value"/> is null reference in the <see cref="T:System.Collections.IList"/>.
            </exception>
        </member>
        <member name="P:Telerik.Windows.Data.InnerCollectionView.System#Collections#IList#IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IList"/> has a fixed size.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IList"/> has a fixed size; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.InnerCollectionView.System#Collections#IList#IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IList"/> is read-only.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IList"/> is read-only; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.Remove(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.RemoveAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.InnerCollectionView.Item(System.Int32)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> at the specified index.
            </summary>
            <value></value>
        </member>
        <member name="M:Telerik.Windows.Data.InnerCollectionView.CreateView">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.IQueryableCollectionView">
            <summary>
            Represents a view for grouping, sorting and filtering a data collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IQueryableCollectionView.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IQueryableCollectionView.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IQueryableCollectionView.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="T:Telerik.Windows.Data.ISuspendNotifications">
            <summary>
            Represents a interface for suspend notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ISuspendNotifications.SuspendNotifications">
            <summary>
            Suspends the notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ISuspendNotifications.ResumeNotifications">
            <summary>
            Resumes the notifications.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.ISuspendNotifications.NotificationsSuspended">
            <summary>
            Gets or sets a value indicating whether change notifications are suspended.
            </summary>
            <value>
            	<c>True</c> if notifications are suspended, otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableCollectionView">
            <summary>
            Represents a view for grouping, sorting, filtering and paging data collection 
            using LINQ based query operators.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanAddNew">
            <summary>
            Gets a value that indicates whether a new item can be added to the collection.
            </summary>
            <returns>
            true if <see cref="P:Telerik.Windows.Data.QueryableCollectionView.SourceCollection"/> is not <see cref="P:System.Collections.IList.IsFixedSize"/> or Add method is available and 
            collection element has a default constructor; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ShouldRespectIEditableObject">
            <summary>
            Gets or set a value that indicates whether IEditableObject methods are automatically invoked by QueryableCollectionView.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanAddNewItem">
            <summary>
            Gets a value that indicates whether a specified object can be added to the collection.
            </summary>
            <value>
            	<c>true</c> if a specified object can be added to the collection; otherwise <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrentAddItem">
            <summary>
            Gets the item that is being added during the current add transaction.
            </summary>
            <returns>
            The item that is being added if <see cref="P:Telerik.Windows.Data.QueryableCollectionView.IsAddingNew"/> is true; otherwise, null.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanRemove">
            <summary>
            Gets a value that indicates whether an item can be removed from the collection.
            </summary>
            <returns>
            true if an item can be removed from the collection; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanCancelEdit">
            <summary>
            Gets a value that indicates whether the editing of an item can be canceled.
            </summary>
            <returns>
            true if <see cref="P:Telerik.Windows.Data.QueryableCollectionView.CurrentEditItem"/> implements <see cref="T:System.ComponentModel.IEditableObject"/>; 
            otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrentEditItem">
            <summary>
            Gets the item in the collection that is being edited.
            </summary>
            <returns>
            The item in the collection that is being edited 
            if <see cref="P:Telerik.Windows.Data.QueryableCollectionView.IsEditingItem"/> is true; otherwise, null.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsAddingNew">
            <summary>
            Gets a value that indicates whether an add transaction is in progress.
            </summary>
            <returns>
            true if an add transaction is in progress; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsEditingItem">
            <summary>
            Gets a value that indicates whether an edit transaction is in progress.
            </summary>
            <returns>
            true if an edit transaction is in progress; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.NewItemPlaceholderPosition">
            <summary>
            Gets or sets the position of the new item placeholder in the collection.
            </summary>
            <returns>
            One of the enumeration values that specifies the position of the new item placeholder in the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.AddNew">
            <summary>
            Adds a new item to the collection.
            </summary>
            <returns>
            The new item that is added to the collection.
            </returns>
            <exception cref="T:System.InvalidOperationException"><see cref="P:Telerik.Windows.Data.QueryableCollectionView.CanAddNew"/> is false.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.AddNewItem(System.Object)">
            <summary>
            Adds the specified object to the collection.
            </summary>
            <param name="newItem">The object to add to the collection.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.AddNew(System.Object)">
            <summary>
            Adds the new item to the collection.
            </summary>
            <param name="newItem">The new item that will be added to the collection.</param>
            <exception cref="T:System.InvalidOperationException"><see cref="P:Telerik.Windows.Data.QueryableCollectionView.CanAddNew"/> is false.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CommitNew">
            <summary>
            Ends the add transaction and saves the pending new item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CancelNew">
            <summary>
            Ends the add transaction and discards the pending new item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.EditItem(System.Object)">
            <summary>
            Begins an edit transaction of the specified item.
            </summary>
            <param name="item">The item to edit.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CommitEdit">
            <summary>
            Ends the edit transaction and saves the pending changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CancelEdit">
            <summary>
            Ends the edit transaction and discards any pending changes to the item.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            CancelEdit is not supported for the current edit item. 
            Only items implementing <see cref="T:System.ComponentModel.IEditableObject"/> are supported.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Remove(System.Object)">
            <summary>
            Removes the specified item from the collection.
            </summary>
            <param name="item">The item to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RemoveAt(System.Int32)">
            <summary>
            Removes the item at the specified position from the collection.
            </summary>
            <param name="index">The position of the item to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.ConstructNewItem">
            <summary>
            Constructs a new item.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RaisePageChanged">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.PageChanged"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CompletePageMove(System.Int32)">
            <summary>
            Completes the page move.
            </summary>
            <param name="newPageIndex">The index of the new page.</param>
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.PageChanged">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex" /> has changed.</summary>
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.PageChanging">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex" /> is changing.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToFirstPage">
            <summary>Sets the first page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToLastPage">
            <summary>Sets the last page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToNextPage">
            <summary>Moves to the page after the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToPage(System.Int32)">
            <summary>Requests a page move to the page at the specified index.</summary>
            <returns>true if the move was successfully initiated; otherwise, false.</returns>
            <param name="pageIndex">The index of the page to move to.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToPageCore(System.Int32)">
            <summary>Requests a page move to the page at the specified index.</summary>
            <returns>true if the move was successfully initiated; otherwise, false.</returns>
            <param name="index">The index of the page to move to.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveToPreviousPage">
            <summary>Moves to the page before the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanChangePage">
            <summary>
            Gets a value that indicates whether the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex" /> value can change.
            </summary>
            <returns>
            true if the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex" /> value can change; otherwise, false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsPageChanging">
            <summary>Gets a value that indicates whether a page index change is in process.</summary>
            <returns>true if the page index is changing; otherwise, false.</returns>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.pageIndexField">
            <summary>
            This is called pIndex instead of pageIndex because the parameter of
            the MoveToPage method is called pageIndex.
            If the field and the parameter have the same name -> CA error.
            If I change the name of the parameter -> another CA error.
            So I changed the name of the field.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.PageIndex">
            <summary>Gets the zero-based index of the current page.</summary>
            <returns>The zero-based index of the current page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.PageSize">
            <summary>Gets or sets the number of items to display on a page.</summary>
            <returns>The number of items to display on a page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ShouldRefreshOrDeferOnPageSizeChange">
            <summary>
            Gets a value indicating whether this instance should RefreshOrDefer when PageSize changes.
            </summary>
            <value>
            	<c>true</c> if this instance should RefreshOrDefer when PageSize changes; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsPaged">
            <summary>
            Gets a value indicating whether this instance has pages or not.
            </summary>
            <value>
            	<c>true</c> if this instance has PageSize greater than 0; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.Count">
            <summary>
            Gets the number of records in the view after grouping, filtering, sorting, and paging.
            </summary>
            <value>The number of records in the view after grouping, filtering, sorting, and paging.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.InternalCount">
            <summary>
            Protected accessor to private count.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.TotalItemCount">
            <summary>
            Gets the total number of items in the source collection, or -1 if the total number is unknown.
            </summary>
            <returns>
            The total number of items in the source collection, or -1 if the total number is unknown.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ItemCount">
            <summary>
            Gets the minimum number of items known to be in the source collection.
            </summary>
            <returns>
            The minimum number of items known to be in the source collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.UpdateItemCount">
            <summary>
            Called when the count of the items should be updated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.GetEffectiveItemCount">
            <summary>
            Gets the count of items depending on the page and/or grouped state.
            </summary>
            <returns>
            The count of items.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.GetPagingDeterminativeItemCount">
            <summary>
            Gets the paging determinative item count.
            </summary>
            <returns>The paging determinative item count.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.InvalidatePagingDeterminativeItemCount">
            <summary>
            Invalidates the paging determinative item count.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.DeferRefresh">
            <summary>
            Enters a defer cycle that you can use to merge changes to the view and delay automatic refresh.
            </summary>
            <returns>
            An <see cref="T:System.IDisposable"/> object that you can use to dispose of the calling object. 
            </returns>
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.CollectionChanged">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.CurrentChanged">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.CurrentChanging">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrentPosition">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsCurrentAfterLast">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsCurrentBeforeFirst">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrentItem">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentToFirst">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentToLast">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentToNext">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentToPosition(System.Int32)">
            <summary>
            Sets the item at the specified index to be the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.CurrentItem"/> in the view.
            </summary>
            <param name="position">The index to set the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.CurrentItem"/> to.</param>
            <returns>
            True if the resulting <see cref="P:Telerik.Windows.Data.QueryableCollectionView.CurrentItem"/> is an item within the view; otherwise, false.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><c>position</c> is out of range.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.MoveCurrentToPrevious">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnCurrentChanged(System.EventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.CurrentChanged"/> event.
            </summary>
            <param name="args">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnCurrentChanging(System.ComponentModel.CurrentChangingEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.CurrentChanging"/> event.
            </summary>
            <param name="args">The <see cref="T:System.ComponentModel.CurrentChangingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnCurrentChanging">
            <summary>
            Raise a non-cancelable CurrentChanging event
            This is called when CurrentItem is affected by a CollectionChange (Remove or Refresh).
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.SetCurrentPosition(System.Int32)">
            <summary>
            Sets the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.CurrentPosition"/> to the given <paramref name="newPosition"/> .
            </summary>
            <param name="newPosition">The new position.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.SetCurrent(System.Object,System.Int32)">
            <summary>
            Sets the CurrentItem and CurrentPosition.
            </summary>
            <param name="newItem">The new current item.</param>
            <param name="newPosition">The new current position.</param>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.PendingCurrencyRefresh">
            <summary>
            Used to hold "old" currency info when DataLoadMode is Asynchronous
            and we need to refresh the currency after the load is complete.
            This field is not supposed to be used directly from your code.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo">
            <summary>
            Used to hold "old" currency info when DataLoadMode is Asynchronous
            and we need to refresh the currency after the load is complete.
            This class is not supposed to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo.OldIsCurrentAfterLast">
            <summary>
            OldIsCurrentAfterLast.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo.OldIsCurrentBeforeFirst">
            <summary>
            OldIsCurrentBeforeFirst.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo.OldCurrentPosition">
            <summary>
            OldCurrentPosition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo.OldCurrentItem">
            <summary>
            OldCurrentItem.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo.#ctor(System.Boolean,System.Boolean,System.Int32,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo"/> class.
            </summary>
            <param name="oldIsCurrentAfterLast">The old IsCurrentAfterLast.</param>
            <param name="oldIsCurrentBeforeFirst">The old IsCurrentBeforeFirst.</param>
            <param name="oldCurrentPosition">The old current position.</param>
            <param name="oldCurrentItem">The old current item.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/> class.
            </summary>
            <param name="source">The source collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.#ctor(System.Collections.IEnumerable,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/> class.
            </summary>
            <param name="sourceCollection">The source collection.</param>
            <param name="itemType">Type which will be used for all operations.</param>
            <exception cref="T:System.ArgumentNullException"><c>sourceCollection</c> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.InitializeCurrentItem">
            <summary>
            Called when the current item should be initialized.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.NeedsRefresh">
            <summary>
            Gets a value that indicates whether this view needs to be refreshed.
            </summary>
            <returns>true if the view needs to be refreshed; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.SourceCollection">
            <summary>
            Returns the underlying collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerable"/> object that is the underlying collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.QueryableSourceCollection">
            <summary>
            Returns the queryable collection, constructed from <see cref="P:Telerik.Windows.Data.QueryableCollectionView.SourceCollection"/>.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable"/> object that is constructed from the underlying collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanFilter">
            <summary>
            Gets a value that indicates whether this view supports filtering via the 
            <see cref="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors"/> property.
            </summary>
            <returns>For a default instance of <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/>, this
            property always returns true.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanGroup">
            <summary>
            Gets a value that indicates whether this view supports grouping via the 
            <see cref="P:Telerik.Windows.Data.QueryableCollectionView.GroupDescriptors"/>
            property.
            </summary>
            <returns>For a default instance of <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/>, this
            property always returns true.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.CanSort">
            <summary>
            Gets a value that indicates whether this view supports sorting via the 
            <see cref="P:Telerik.Windows.Data.QueryableCollectionView.SortDescriptors"/>
            property.
            </summary>
            <returns>For a default instance of <see cref="T:Telerik.Windows.Data.QueryableCollectionView"/>, this
            property always returns true.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.Culture">
            <summary>
            Gets or sets the cultural info for any operations of the view that may differ by culture, such as sorting.
            </summary>
            <returns>
            The culture to use during sorting.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.Groups">
            <summary>
            Gets the top-level groups.
            </summary>
            <returns>
            A read-only collection of the top-level groups or null if there are no groups.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.Item(System.Int32)">
            <summary>
            This indexer is not supposed to be used directly from your code.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsEmpty">
            <summary>
            Returns a value that indicates whether the resulting view is empty.
            </summary>
            <returns>
            true if the resulting view is empty; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Contains(System.Object)">
            <summary>
            Returns a value that indicates whether a given item belongs to this collection view.
            </summary>
            <returns>
            True if the item belongs to this collection view; otherwise, false.
            </returns>
            <param name="item">The object to check.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Refresh">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnRefresh">
            <summary>
            Invoked when the instance should be refreshed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.SelectDescriptors">
            <summary>
            Gets the selection descriptors used for selection operations.
            </summary>
            <value>The selection descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ItemType">
            <summary>
            Gets or sets the type used for all internal data engine operations.
            </summary>
            <value>The type of the item.</value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.InvalidatePagingAndRefresh">
            <summary>
            Invalidates the paging and refreshes the view.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnSortDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.ISortDescriptor})">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.SortDescriptors"/> item changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnSortDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.SortDescriptors"/> collection changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnGroupDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.IGroupDescriptor})">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.GroupDescriptors"/> item changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnGroupDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.roupDescriptors"/> collection changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnFilterDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.IFilterDescriptor})">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors"/> item changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnFilterDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors"/> collection changes.
            </summary>
            <param name="sender">Current instance of the QueryableCollectionView collection.</param>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnFilterDescriptorsChanged">
            <summary>
            Called when anything in the filter descriptors changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnFilterDescriptorsLogicalOperatorChanged">
            <summary>
            Invoked when the <see cref="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors"/> logical operator changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CreateView">
            <summary>
            Returns <see cref="T:System.Linq.IQueryable"/> with applied filtering, sorting, grouping and paging.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.ApplySelectDescriptors(System.Linq.IQueryable)">
            <summary>
            Applies <seealso cref="T:Telerik.Windows.Data.SelectDescriptor"/>s over the specified queryable.
            </summary>
            <param name="queryable">The queryable.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Sort(System.Linq.IQueryable)">
            <summary>
            Sorts the specified queryable.
            </summary>
            <param name="queryable"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.InternalList">
            <summary>
            Gets the list created from resulting query 
            after applying filtering, sorting, grouping and paging.
            </summary>
            <value>The internal list for the current view.</value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.SetInternalList(System.Collections.IList)">
            <summary>
            Called when internal list should be set.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.GetInternalList">
            <summary>
            Called when internal list is required.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.InitializeInternalList(System.Linq.IQueryable)">
            <summary>
            Called when internal list should be initialized.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.CreateInternalList">
            <summary>
            Called when internal list needs to be created.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.PopulateInternalList(System.Linq.IQueryable)">
            <summary>
            Called when internal list should be populated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnInternalListCreated">
            <summary>
            Called when internal list is created.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.PropertyChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.CollectionChanged"/> event.
            </summary>
            <param name="args">The <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsLoading">
            <summary>
            Gets a value that indicates whether this view is loading data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RefreshOverrideCore">
            <summary>
            Re-create the view over the associated IList.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.UpdatePageIndex">
            <summary>
            We might need to move back to the last valid page
            since dramatic changes may have occurred. For example
            if you delete some items, the page count is reduced
            and we need to move back to the last valid page index.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RefreshOverride">
            <summary>
            Re-create the view over the associated IList.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.InitializeCurrencyOnRefresh(Telerik.Windows.Data.QueryableCollectionView.CurrencyRefreshInfo)">
            <summary>
            Initializes the currency on refresh.
            </summary>
            <param name="currencyRefreshInfo">The currency refresh info.</param>
        </member>
        <member name="E:Telerik.Windows.Data.QueryableCollectionView.GroupCollectionChanged">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RefreshGroupsOnItemAction(System.Object,Telerik.Windows.Data.ItemAction)">
            <summary>
            Refreshes the groups according to the item and the respective action.
            </summary>
            <param name="item">The item.</param>
            <param name="action">The action.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RefreshOnItemAction(System.Object,Telerik.Windows.Data.ItemAction)">
            <summary>
            Refreshes the view according to the item and the respective action.
            </summary>
            <param name="item">The item.</param>
            <param name="action">The action.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.RefreshOnItemActionWithoutPaging(System.Object,Telerik.Windows.Data.ItemAction)">
            <summary>
            Refreshes the view according to the item and the respective action when there is no paging involved.
            </summary>
            <param name="item">The item.</param>
            <param name="action">The action.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.OnPropertyChanged(System.String)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.QueryableCollectionView.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">Name of the property.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.AreArgumentsInvalid(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Detects invalid event arguments produced by the System.Windows.Data.CompositeCollection 
            when adding an item to one of its CollectionContainers. 
            For more info see Support Ticket 657921.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.UpdateTotalItemCount">
            <summary>
            Called when the count of all items should be updated.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsGrouped">
            <summary>
            Gets a value indicating whether this instance is grouped.
            </summary>
            <value>
            	<c>true</c> if this instance is grouped; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.CachedIsEmpty">
            <summary>
            CachedIsEmpty.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsCurrentAfterLast">
            <summary>
            IsCurrentAfterLast.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsCurrentBeforeFirst">
            <summary>
            IsCurrentBeforeFirst.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsDataInGroupOrder">
            <summary>
            IsDataInGroupOrder.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsDataSorted">
            <summary>
            IsDataSorted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsMoveToPageDeferred">
            <summary>
            IsMoveToPageDeferred.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsPageChanging">
            <summary>
            IsPageChanging.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.IsUpdatePageSizeDeferred">
            <summary>
            IsUpdatePageSizeDeferred.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.NeedsRefresh">
            <summary>
            NeedsRefresh.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.QueryableCollectionView.CollectionViewFlags.ShouldProcessCollectionChanged">
            <summary>
            ShouldProcessCollectionChanged.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.IndexOf(System.Object)">
            <summary>
            Returns the zero-based index at which the specified item is located.
            </summary>
            <returns>The index at which the specified item is located, or –1 if the item is unknown.</returns>
            <param name="item">The item to locate.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.GetItemAt(System.Int32)">
            <summary>
            Retrieves the item at the specified zero-based index in the view.
            </summary>
            <returns>The item at the specified zero-based index in the view.</returns>
            <param name="index">The zero-based index of the item to retrieve.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is less than 0 or greater than <see cref="P:Telerik.Windows.Data.QueryableCollectionView.ItemCount"/>. 
            </exception>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ShouldInitializeGroupProxy">
            <summary>
            Gets a value indicating whether should initialize group proxy.
            </summary>
            <value>
            	<c>true</c> if should initialize group proxy; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing,
            or resetting unmanaged resources.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.IsDisposed">
            <summary>
            Used for unit tests only.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionView.Dispose(System.Boolean)">
            <summary>
            Unsubscribes form collection changed events.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.Filter">
            <summary>
            Gets or sets a callback used to determine if an item is suitable for inclusion in the view.
            </summary>
            <returns>
            A method used to determine if an item is suitable for inclusion in the view.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Setting Filter property is not supported. Use <see cref="P:Telerik.Windows.Data.QueryableCollectionView.FilterDescriptors"/> property instead.
            </exception>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.System#ComponentModel#ICollectionView#SortDescriptions">
            <summary>
            Gets a collection of <see cref="T:System.ComponentModel.SortDescription" /> objects that describe how the items 
            in the collection are sorted in the view.
            </summary>
            <returns>
            A collection of <see cref="T:System.ComponentModel.SortDescription" /> objects that describe how the 
            items in the collection are sorted in the view.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.System#ComponentModel#ICollectionView#GroupDescriptions">
            <summary>
            Gets a collection of <see cref="T:System.ComponentModel.GroupDescription" /> objects that describe 
            how the items in the collection are grouped in the view.
            </summary>
            <returns>
            A collection of <see cref="T:System.ComponentModel.GroupDescription" /> objects that describe how 
            the items in the collection are grouped in the view.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.SortDescriptions">
            <summary>
            Gets the underlying <see cref="P:System.ComponentModel.ICollectionView.SortDescriptions"/> collection.
            </summary>
            <value>The <see cref="P:System.ComponentModel.ICollectionView.SortDescriptions"/> for this view.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.GroupDescriptions">
            <summary>
            Gets the underlying <see cref="P:System.ComponentModel.ICollectionView.GroupDescriptions"/> collection.
            </summary>
            <value>The <see cref="P:System.ComponentModel.ICollectionView.GroupDescriptions"/> for this view.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ItemProperties">
            <summary>
            Gets a collection of objects that describes the properties of the items in the collection.
            </summary>
            <returns>
            A collection of objects that describes the properties of the items in the collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionView.ItemPropertiesByPropertyName">
            <summary>
            Gets a dictionary of objects that describes the properties of the items in the collection by property name.
            </summary>
            <returns>
            A dictionary of objects that describes the properties of the items in the collection by property name.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.ItemAction">
            <summary>
            Represent an item action.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ItemAction.Add">
            <summary>
            Add.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ItemAction.Remove">
            <summary>
            Remove.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ItemAction.Edit">
            <summary>
            Edit.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableExtensions">
            <summary>
            Holds extension methods for <see cref="T:System.Linq.IQueryable"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Sort(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.ISortDescriptor})">
            <summary>
            Sorts the elements of a sequence using the specified sort descriptors.
            </summary>
            <param name="source">A sequence of values to sort.</param>
            <param name="sortDescriptors">The sort descriptors used for sorting.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted according to a <paramref name="sortDescriptors"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Sort(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.SortDescriptor})">
            <summary>
            Sorts the elements of a sequence using the specified sort descriptors.
            </summary>
            <param name="source">A sequence of values to sort.</param>
            <param name="sortDescriptors">The sort descriptors used for sorting.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted according to a <paramref name="sortDescriptors"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Page(System.Linq.IQueryable,System.Int32,System.Int32)">
            <summary>
            Pages through the elements of a sequence until the specified 
            <paramref name="pageIndex"/> using <paramref name="pageSize"/>.
            </summary>
            <param name="source">A sequence of values to page.</param>
            <param name="pageIndex">Index of the page.</param>
            <param name="pageSize">Size of the page.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are at the specified <paramref name="pageIndex"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Select(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Projects each element of a sequence into a new form.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are the result of invoking a 
            projection selector on each element of <paramref name="source" />.
            </returns>
            <param name="source"> A sequence of values to project. </param>
            <param name="selector"> A projection function to apply to each element. </param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Groups the elements of a sequence according to a specified key selector function.
            </summary>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> whose elements to group.</param>
            <param name="keySelector"> A function to extract the key for each element.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable"/> with <see cref="T:System.Linq.IGrouping`2"/> items, 
            whose elements contains a sequence of objects and a key.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Sorts the elements of a sequence in ascending order according to a key.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted according to a key.
            </returns>
            <param name="source">
            A sequence of values to order.
            </param>
            <param name="keySelector">
            A function to extract a key from an element.
            </param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.OrderByDescending(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Sorts the elements of a sequence in descending order according to a key.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted in descending order according to a key.
            </returns>
            <param name="source">
            A sequence of values to order.
            </param>
            <param name="keySelector">
            A function to extract a key from an element.
            </param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression,System.Nullable{System.ComponentModel.ListSortDirection})">
            <summary>
            Calls <see cref="M:Telerik.Windows.Data.QueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)"/> 
            or <see cref="M:Telerik.Windows.Data.QueryableExtensions.OrderByDescending(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)"/> depending on the <paramref name="sortDirection"/>.
            </summary>
            <param name="source">The source.</param>
            <param name="keySelector">The key selector.</param>
            <param name="sortDirection">The sort direction.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted according to a key.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.GroupBy(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.IGroupDescriptor})">
            <summary>
            Groups the elements of a sequence according to a specified <paramref name="groupDescriptors"/>.
            </summary>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> whose elements to group. </param>
            <param name="groupDescriptors">The group descriptors used for grouping.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable"/> with <see cref="T:Telerik.Windows.Data.IGroup"/> items, 
            whose elements contains a sequence of objects and a key.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Aggregate(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.AggregateFunction})">
            <summary>
            Calculates the results of given aggregates functions on a sequence of elements.
            </summary>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> whose elements will 
            be used for aggregate calculation.</param>
            <param name="aggregateFunctions">The aggregate functions.</param>
            <returns>Collection of <see cref="T:Telerik.Windows.Data.AggregateResult"/>s calculated for each function.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Aggregate(System.Linq.IQueryable,Telerik.Windows.Data.AggregateFunction)">
            <summary>
            Calculates the results of a given aggregate function on a sequence of elements.
            </summary>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> whose elements will 
            be used for aggregate calculation.</param>
            <param name="aggregateFunction">The aggregate function.</param>
            <returns>Collection of <see cref="T:Telerik.Windows.Data.AggregateResult"/>s calculated for the function.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Where(System.Linq.IQueryable,System.Linq.Expressions.Expression)">
            <summary> 
            Filters a sequence of values based on a predicate. 
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that satisfy the condition specified by <paramref name="predicate" />.
            </returns>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> to filter.</param>
            <param name="predicate"> A function to test each element for a condition.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Where(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.IFilterDescriptor})">
            <summary> 
            Filters a sequence of values based on a collection of <see cref="T:Telerik.Windows.Data.IFilterDescriptor"/>. 
            </summary>
            <param name="source">The source.</param>
            <param name="filterDescriptors">The filter descriptors.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that satisfy the conditions specified by each filter descriptor in <paramref name="filterDescriptors" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Where(System.Linq.IQueryable,Telerik.Windows.Data.CompositeFilterDescriptorCollection)">
            <summary> 
            Filters a sequence of values based on a <see cref="T:Telerik.Windows.Data.CompositeFilterDescriptorCollection"/>. 
            </summary>
            <param name="source">The source.</param>
            <param name="filterDescriptors">The composite filter descriptor collection.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that satisfy the conditions specified by the <paramref name="filterDescriptors" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Select(System.Linq.IQueryable,Telerik.Windows.Data.SelectDescriptorCollection)">
            <summary> 
            Selects a sequence of objects based on a <see cref="T:Telerik.Windows.Data.SelectDescriptorCollection"/>. 
            </summary>
            <param name="source">The source.</param>
            <param name="selectDescriptors">The select descriptor collection.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that will be projected according to the specified <paramref name="selectDescriptors" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Select(System.Linq.IQueryable,System.Collections.Generic.IEnumerable{Telerik.Windows.Data.ISelectDescriptor})">
            <summary> 
            Selects a sequence of objects based on a IEnumerable of SelectDescriptor. 
            </summary>
            <param name="source">The source.</param>
            <param name="selectDescriptors">The select descriptor enumerable.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that will be projected according to the specified <paramref name="selectDescriptors" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Take(System.Linq.IQueryable,System.Int32)">
            <summary>
            Returns a specified number of contiguous elements from the start of a sequence.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains the specified number 
            of elements from the start of <paramref name="source" />.
            </returns>
            <param name="source"> The sequence to return elements from.</param>
            <param name="count"> The number of elements to return. </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="source" /> is null. </exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Skip(System.Linq.IQueryable,System.Int32)">
            <summary>
            Bypasses a specified number of elements in a sequence 
            and then returns the remaining elements.
            </summary>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements that occur 
            after the specified index in the input sequence.
            </returns>
            <param name="source">
            An <see cref="T:System.Linq.IQueryable" /> to return elements from.
            </param>
            <param name="count">
            The number of elements to skip before returning the remaining elements.
            </param>
            <exception cref="T:System.ArgumentNullException"> <paramref name="source" /> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Count(System.Linq.IQueryable)">
            <summary> Returns the number of elements in a sequence.</summary>
            <returns> The number of elements in the input sequence.</returns>
            <param name="source">
            The <see cref="T:System.Linq.IQueryable" /> that contains the elements to be counted.
            </param>
            <exception cref="T:System.ArgumentNullException"> <paramref name="source" /> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.First(System.Linq.IQueryable)">
            <summary>
            Returns the firsts item in a sequence.
            </summary>
            <param name="source">
            The <see cref="T:System.Linq.IQueryable" /> that contains the elements to be counted.
            </param>
            <returns>First element.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.ElementAt(System.Linq.IQueryable,System.Int32)">
            <summary> Returns the element at a specified index in a sequence.</summary>
            <returns> The element at the specified position in <paramref name="source" />.</returns>
            <param name="source"> An <see cref="T:System.Linq.IQueryable" /> to return an element from.</param>
            <param name="index"> The zero-based index of the element to retrieve.</param>
            <exception cref="T:System.ArgumentNullException"> <paramref name="source" /> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"> <paramref name="index" /> is less than zero.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.ToIList(System.Linq.IQueryable)">
            <summary>
            Creates a <see cref="T:System.Collections.Generic.IList`1" /> from an <see cref="T:System.Linq.IQueryable" /> where T is <see cref="P:System.Linq.IQueryable.ElementType"/>.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.List`1" /> that contains elements from the input sequence.
            </returns>
            <param name="source">
            The <see cref="T:System.Linq.IQueryable" /> to create a <see cref="T:System.Collections.Generic.List`1" /> from.
            </param>
            <exception cref="T:System.ArgumentNullException"> 
            <paramref name="source" /> is null.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.ToIList``1(System.Linq.IQueryable{``0})">
            <summary>
            Creates a <see cref="T:System.Collections.Generic.IList`1" /> from an <see cref="T:System.Linq.IQueryable" /> where T is <see cref="P:System.Linq.IQueryable.ElementType"/>.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.List`1" /> that contains elements from the input sequence.
            </returns>
            <param name="source">
            The <see cref="T:System.Linq.IQueryable" /> to create a <see cref="T:System.Collections.Generic.List`1" /> from.
            </param>
            <exception cref="T:System.ArgumentNullException"> 
            <paramref name="source" /> is null.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Where(System.Linq.IQueryable,Telerik.Windows.Data.FilterDescriptorCollection)">
            <summary> 
            Filters a sequence of values based on a collection of <see cref="T:Telerik.Windows.Data.IFilterDescriptor"/>. 
            </summary>
            <param name="source">The source.</param>
            <param name="filterDescriptors">The filter descriptors.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> that contains elements from the input sequence 
            that satisfy the conditions specified by each filter descriptor in <paramref name="filterDescriptors" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Sort(System.Linq.IQueryable,Telerik.Windows.Data.SortDescriptorCollection)">
            <summary>
            Sorts the elements of a sequence using the specified sort descriptors.
            </summary>
            <param name="source">A sequence of values to sort.</param>
            <param name="sortDescriptors">The sort descriptors used for sorting.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable" /> whose elements are sorted according to a <paramref name="sortDescriptors"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Except(System.Linq.IQueryable,System.Linq.IQueryable)">
            <summary>
            Produces the set difference of two sequences by using the default equality comparer to compare values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableExtensions.Union(System.Linq.IQueryable,System.Linq.IQueryable)">
            <summary>
            Produces the set union of two sequences by using the default equality comparer.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.SynchronizationMode">
            <summary>
            Describes the direction of data flow in a collection synchronization.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.SynchronizationMode.TwoWay">
            <summary>
            Both collections are synchronized.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.SynchronizationMode.OneWayToSource">
            <summary>
            Only changes from the target to the source are synchronized.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.SynchronizationMode.None">
            <summary>
            Synchronization is disabled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.VirtualQueryableCollectionView`1">
            <summary>
            Represents a view for grouping, sorting, filtering and paging data collection virtually.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView`1.#ctor">
            <summary>
            Initializes a new instance of the VirtualQueryableCollectionView class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.VirtualQueryableCollectionView">
            <summary>
            Represents a view for grouping, sorting, filtering and paging data collection virtually.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.VirtualQueryableCollectionView.ItemsLoading">
            <summary>
            Occurs when the collection is about to load items.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.VirtualQueryableCollectionView.ItemsLoaded">
            <summary>
            Occurs when the items are loaded.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.#ctor">
            <summary>
            Initializes a new instance of the VirtualQueryableCollectionView class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.VirtualQueryableCollectionView"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.VirtualQueryableCollectionView"/> class.
            </summary>
            <param name="source">The source collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.#ctor(System.Collections.IEnumerable,System.Type)">
            <summary>
            Initializes a new instance of the VirtualQueryableCollectionView class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.IndexOf(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.GetItemAt(System.Int32)">
            <inheritdoc />
            <remarks>
            If the item at this index is not loaded will raise ItemsLoading event. 
            The returned item will be null or temporary dynamic item until the real item is loaded.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.Load(System.Int32,System.Collections.IEnumerable)">
            <summary>
            Loads new data in the view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.IsRequestingItems">
            <summary>
            Gets a value that indicates whether this view is loading items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.VirtualItemCount">
            <summary>Gets or sets the total number of all items.</summary>
            <returns>The total number of all items.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.LoadSize">
            <summary>Gets or sets the total number of items to retrieve.</summary>
            <returns>The total number of items to retrieve.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.OnRefresh">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.ResetItems">
            <summary>
            Reset all items to default value (null) in the collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.ResetItems(System.Int32,System.Int32)">
            <summary>
            Reset number of items starting from given index to default value (null) in the collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.Remove(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.RemoveAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.AddNew">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.CancelNew">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.CanAddNew">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.CanRemove">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.InternalList">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.InvalidatePagingAndRefresh">
            <summary>
            Invalidates the paging and refreshes the view.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.CreateView">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.QueryableSourceCollection">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.ItemCount">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.InternalCount">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.Add(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.Insert(System.Int32,System.Object)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.IsFixedSize">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.System#Collections#IList#Item(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.CopyTo(System.Array,System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.IsSynchronized">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.SyncRoot">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.VirtualQueryableCollectionView.Clear">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.ShouldEnumeratorLoadItems">
            <summary>
            Gets or sets a value that indicates whether items that are not loaded yet should get loaded while the collection's enumerator is traversed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionView.System#Collections#ICollection#Count">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadedEventArgs">
            <summary>
            Represents event data for ItemsLoaded event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadedEventArgs.StartIndex">
            <summary>
            Gets the start index.
            </summary>
            <value>The start index.</value>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadedEventArgs.Items">
            <summary>
            Gets the loaded items.
            </summary>
            <value>The loaded items.</value>
        </member>
        <member name="T:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadingEventArgs">
            <summary>
            Represents event data for ItemsLoading event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadingEventArgs.StartIndex">
            <summary>
            Gets the start index.
            </summary>
            <value>The start index.</value>
        </member>
        <member name="P:Telerik.Windows.Data.VirtualQueryableCollectionViewItemsLoadingEventArgs.ItemCount">
            <summary>
            Gets the items count.
            </summary>
            <value>The items count.</value>
        </member>
        <member name="T:Telerik.Windows.Data.RemoveOnlyCollection`1">
            <summary>
            Provides the base class for a generic collection that only allows removing items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RemoveOnlyCollection`1.#ctor(System.Collections.Generic.IList{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.RemoveOnlyCollection`1"/> 
            class as a wrapper for the specified list.
            </summary>
            <param name="list">The list that is wrapped by the new collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.RemoveOnlyCollection`1.InsertItem(System.Int32,`0)">
            <inheritdoc />
            <remarks>
            This implementation always throws NotSupportedException.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.RemoveOnlyCollection`1.SetItem(System.Int32,`0)">
            <inheritdoc />
            <remarks>
            This implementation always throws NotSupportedException.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Data.EnumDataSource">
            <summary>
            Provides a collection of view models suitable for combo-box binding.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.EnumDataSource.CollectionChanged">
            <summary>
            Occurs when the collection changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.EnumDataSource.EnumType">
            <summary>
            Gets or sets the type of the enum.
            </summary>
            <value>The type of the enumeration.</value>
        </member>
        <member name="M:Telerik.Windows.Data.EnumDataSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.EnumDataSource"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.EnumDataSource.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.EnumDataSource.FromType``1">
            <summary>
            Returns a collection of EnumMemberViewModel's based on the supplied enumeration type.
            You can use this method's return value as the ItemsSource of a combo-box.
            </summary>
            <typeparam name="TEnum">The enumeration type.</typeparam>
            <returns>A collection of EnumMemberViewModel's based on the supplied enumeration type.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.EnumDataSource.FromType(System.Type)">
            <summary>
            Returns a collection of EnumMemberViewModel's based on the supplied enumeration type.
            You can use this method's return value as the ItemsSource of a combo-box.
            </summary>
            <param name="enumType">The enumeration type.</param>
            <returns>A collection of EnumMemberViewModel's based on the supplied enumeration type.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.EnumMemberViewModel">
            <summary>
            Holds information about an Enum member.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.EnumMemberViewModel.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumMemberViewModel.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumMemberViewModel.Description">
            <summary>
            Returns the Description of the DescriptionAttribute, if present.
            </summary>
            <value>The Description of the DescriptionAttribute, if present.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumMemberViewModel.DisplayName">
            <summary>
            Returns the first of the following properties that is not null:
            - DisplayShortName.
            - Description.
            - Name.
            </summary>
            <value>The display name.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumMemberViewModel.DisplayShortName">
            <summary>
            Returns the ShortName of the DisplayAttribute, if present.
            </summary>
            <value>The ShortName of the DisplayAttribute, if present.</value>
        </member>
        <member name="M:Telerik.Windows.Data.EnumMemberViewModel.#ctor(System.Object,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.EnumMemberViewModel"/> class.
            </summary>
            <param name="value">The value.</param>
            <param name="name">The name.</param>
            <param name="description">The description.</param>
        </member>
        <member name="M:Telerik.Windows.Data.EnumMemberViewModel.#ctor(System.Object,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.EnumMemberViewModel"/> class.
            </summary>
            <param name="value">The value.</param>
            <param name="name">The name.</param>
            <param name="description">The description.</param>
            <param name="displayShortName">The short name.</param>
        </member>
        <member name="M:Telerik.Windows.Data.EnumMemberViewModel.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FakePropertyDescriptor"/> class.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="P:Telerik.Windows.Data.FakePropertyDescriptor.Name">
            <summary>
            Gets the name of the member.
            </summary>
            <value></value>
            <returns>
            The name of the member.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.FakePropertyDescriptor.ComponentType">
            <summary>
            When overridden in a derived class, gets the type of the component this property is bound to.
            </summary>
            <value></value>
            <returns>A <see cref="T:System.Type"/> that represents the type of component this property is bound to. When the <see cref="M:System.ComponentModel.PropertyDescriptor.GetValue(System.Object)"/> or <see cref="M:System.ComponentModel.PropertyDescriptor.SetValue(System.Object,System.Object)"/> methods are invoked, the object specified might be an instance of this type.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.FakePropertyDescriptor.IsReadOnly">
            <summary>
            When overridden in a derived class, gets a value indicating whether this property is read-only.
            </summary>
            <value></value>
            <returns>true if the property is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.FakePropertyDescriptor.PropertyType">
            <summary>
            When overridden in a derived class, gets the type of the property.
            </summary>
            <value></value>
            <returns>A <see cref="T:System.Type"/> that represents the type of the property.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.CanResetValue(System.Object)">
            <summary>
            When overridden in a derived class, returns whether resetting an object changes its value.
            </summary>
            <param name="component">The component to test for reset capability.</param>
            <returns>
            True if resetting the component changes its value; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.GetValue(System.Object)">
            <summary>
            When overridden in a derived class, gets the current value of the property on a component.
            </summary>
            <param name="component">The component with the property for which to retrieve the value.</param>
            <returns>
            The value of a property for a given component.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.ResetValue(System.Object)">
            <summary>
            When overridden in a derived class, resets the value for this property of the component to the default value.
            </summary>
            <param name="component">The component with the property value that is to be reset to the default value.</param>
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.SetValue(System.Object,System.Object)">
            <summary>
            When overridden in a derived class, sets the value of the component to a different value.
            </summary>
            <param name="component">The component with the property value that is to be set.</param>
            <param name="value">The new value.</param>
        </member>
        <member name="M:Telerik.Windows.Data.FakePropertyDescriptor.ShouldSerializeValue(System.Object)">
            <summary>
            When overridden in a derived class, determines a value indicating whether the value of this property needs to be persisted.
            </summary>
            <param name="component">The component with the property to be examined for persistence.</param>
            <returns>
            True if the property should be persisted; otherwise, false.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.DataFieldDescriptorExtensions">
            <summary>
            DataFieldDescriptorExtensions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataFieldDescriptorExtensions.GetDataMemberName(Telerik.Windows.Data.IDataFieldDescriptor)">
            <summary>
            Gets the data member name.
            </summary>
            <param name="descriptor">IDataFieldDescriptor.</param>
            <returns>The data member name.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataFieldDescriptorExtensions.CreateConvertedAndFormattedValueFunc(Telerik.Windows.Data.IDataFieldDescriptor)">
            <summary>
            Returns a function that converts and formats a value according to a column settings.
            </summary>
            <param name="fieldDescriptor">The IDataFieldDescriptor.</param>
            <returns>A function that converts and formats a value according to a column settings.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataFieldDescriptorExtensions.CreateConvertedAndFormattedValueFunc(Telerik.Windows.Data.IDataFieldDescriptor,System.Func{System.Object,System.Object})">
            <summary>
            Returns a function that converts and formats a value according to a column settings.
            </summary>
            <param name="fieldDescriptor">The IDataFieldDescriptor.</param>
            <param name="func">The func.</param>
            <returns>A function that converts and formats a value according to a column settings.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.DescriptorBase">
            <summary>
            Base class for all descriptors used for 
            handling the logic for property changed notifications.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.DescriptorBase.PropertyChanged">
            <summary>
            Occurs when a property changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DescriptorBase.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.DescriptorBase.PropertyChanged"/> event.
            </summary>
            <param name="args">The <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DescriptorBase.OnPropertyChanged(System.String)">
            <summary>
            Calls <see cref="M:Telerik.Windows.Data.DescriptorBase.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)"/>
            creating a new instance of <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> with given <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">Name of the property that is changed.</param>
        </member>
        <member name="P:Telerik.Windows.Data.DescriptorBase.NotificationsSuspended">
            <summary>
            Gets or sets a value indicating whether change notifications are suspended.
            </summary>
            <value>
            	<c>True</c> if notifications are suspended, otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.DescriptorBase.SuspendNotifications">
            <summary>
            Suspends the notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DescriptorBase.ResumeNotifications">
            <summary>
            Resumes the notifications.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.DynamicClass">
            <summary>Dynamic class.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.DynamicClass.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.DynamicProperty">
            <summary>Dynamic property.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.DynamicProperty.#ctor(System.String,System.Type)">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Data.DynamicProperty" /> class.</summary>
        </member>
        <member name="P:Telerik.Windows.Data.DynamicProperty.Name">
            <summary>Dynamic property name.</summary>
        </member>
        <member name="P:Telerik.Windows.Data.DynamicProperty.Type">
            <summary>Dynamic property type.</summary>
        </member>
        <member name="T:Telerik.Windows.Data.CompositeFilterDescriptor">
            <summary>
            Represents a filtering descriptor which serves as a container for one or more child filtering descriptors.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.CompositeFilterDescriptor.LogicalOperator">
            <summary>
            Gets or sets the logical operator used for composing of <see cref="P:Telerik.Windows.Data.CompositeFilterDescriptor.FilterDescriptors"/>.
            </summary>
            <value>The logical operator used for composition.</value>
        </member>
        <member name="P:Telerik.Windows.Data.CompositeFilterDescriptor.FilterDescriptors">
            <summary>
            Gets or sets the filter descriptors that will be used for composition.
            </summary>
            <value>The filter descriptors used for composition.</value>
        </member>
        <member name="M:Telerik.Windows.Data.CompositeFilterDescriptor.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.CompositeFilterDescriptor.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.CompositeFilterDescriptorCollection">
            <summary>
            Represents collection of <see cref="T:Telerik.Windows.Data.IFilterDescriptor"/> object composed together by a logical operator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.CompositeFilterDescriptorCollection.LogicalOperator">
            <summary>
            Gets or sets the logical operator.
            </summary>
            <value>The logical operator.</value>
        </member>
        <member name="P:Telerik.Windows.Data.CompositeFilterDescriptorCollection.Telerik#Windows#Data#ICompositeFilterDescriptor#FilterDescriptors">
            <summary>
            Gets filter descriptors that will be used for composition.
            </summary>
            <value>The filter descriptors used for composition.</value>
        </member>
        <member name="P:Telerik.Windows.Data.CompositeFilterDescriptorCollection.FilterDescriptors">
            <summary>
            Gets filter descriptors that will be used for composition.
            </summary>
            <value>The filter descriptors used for composition.</value>
        </member>
        <member name="M:Telerik.Windows.Data.CompositeFilterDescriptorCollection.CreateFilterExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a predicate filter expression used for collection filtering.
            </summary>
            <param name="instance">The instance expression, which will be used for filtering.</param>
            <returns>A predicate filter expression.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.CompositeFilterDescriptorCollection.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.LambdaFilterDescriptor.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.FilterDescriptor`1">
            <summary>
            Allows filtering by a lambda expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.FilterDescriptor`1.FilteringExpression">
            <summary>
            Gets or sets the filter expression.
            </summary>
            <value>The filter expression.</value>
        </member>
        <member name="F:Telerik.Windows.Data.FilterDescriptor`1.FilteringExpressionProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.FilterDescriptor`1.FilteringExpression"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor`1.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.GenericEnumerable`1">
            <summary>
            This type is used internally by the data binding infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.GenericEnumerable`1.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.GenericEnumerable`1"/> class.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="T:Telerik.Windows.Data.FilterDescriptor">
            <summary>
            Represents a filter descriptor of the form Member-Operator-Value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FilterDescriptor"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.#ctor(System.String,Telerik.Windows.Data.FilterOperator,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FilterDescriptor"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="filterOperator">The filter operator.</param>
            <param name="filterValue">The filter value.</param>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.#ctor(System.String,Telerik.Windows.Data.FilterOperator,System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FilterDescriptor"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="filterOperator">The filter operator.</param>
            <param name="filterValue">The filter value.</param>
            <param name="caseSensitive">If set to <c>true</c> indicates that this filter descriptor will be case sensitive.</param>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.#ctor(System.String,Telerik.Windows.Data.FilterOperator,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FilterDescriptor"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="filterOperator">The filter operator.</param>
            <param name="filterValue">The filter value.</param>
            <param name="cultureInfo">The culture of the filter descriptor.</param>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.#ctor(System.String,Telerik.Windows.Data.FilterOperator,System.Object,System.Boolean,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FilterDescriptor"/> class.
            </summary>
            <param name="member">The member.</param>
            <param name="filterOperator">The filter operator.</param>
            <param name="filterValue">The filter value.</param>
            <param name="caseSensitive">If set to <c>true</c> indicates that this filter descriptor will be case sensitive.</param>
            <param name="memberType">The Type of the member.</param>
        </member>
        <member name="F:Telerik.Windows.Data.FilterDescriptor.MemberProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.FilterDescriptor.Member" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.FilterDescriptor.Member">
            <summary>
            Gets or sets the member name which will be used for filtering.
            </summary>
            <value>The member that will be used for filtering.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FilterDescriptor.MemberType">
            <summary>
            Gets or sets the type of the member that is used for filtering.
            Set this property if the member type cannot be resolved automatically.
            Such cases are: items with ICustomTypeDescriptor, XmlNode or DataRow.
            Changing this property does not raise PropertyChanged event.
            </summary>
            <value>The type of the member used for filtering.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FilterDescriptor.IsSearchWithAccentEnabled">
            <summary>
            Specifies if search with accent is enable.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
            <remarks>
            Creates a predicate filter expression.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptor.ToString">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.FilterDescriptor.IsActive">
            <inheritdoc />
            <remarks>
            A filter is considered active if its Value is different from OperatorValueFilterDescriptorBase.UnsetValue.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Data.FilterDescriptorBase">
            <summary>
            Base class for all <see cref="T:Telerik.Windows.Data.IFilterDescriptor"/> used for 
            handling the logic for property changed notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptorBase.CreateFilterExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a filter expression by delegating its creation to 
            <see cref="M:Telerik.Windows.Data.FilterDescriptorBase.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)"/>, if 
            <paramref name="instance"/> is <see cref="T:System.Linq.Expressions.ParameterExpression"/>, otherwise throws <see cref="T:System.ArgumentException"/>
            </summary>
            <param name="instance">The instance expression, which will be used for filtering.</param>
            <returns>A predicate filter expression.</returns>
            <exception cref="T:System.ArgumentException">Parameter should be of type <see cref="T:System.Linq.Expressions.ParameterExpression"/></exception>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptorBase.CreateFilterExpression(System.Linq.Expressions.ParameterExpression)">
            <summary>
            Creates a predicate filter expression used for collection filtering.
            </summary>
            <param name="parameterExpression">The parameter expression, which will be used for filtering.</param>
            <returns>A predicate filter expression.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.FilterDescriptorCollection">
            <summary>
            Represents collection of <see cref="T:Telerik.Windows.Data.IFilterDescriptor"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterDescriptorCollection.InsertItem(System.Int32,Telerik.Windows.Data.IFilterDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.FilterCompositionLogicalOperator">
            <summary>
            Logical operator used for filter descriptor composition.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterCompositionLogicalOperator.And">
            <summary>
            Combines filters with logical AND.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterCompositionLogicalOperator.Or">
            <summary>
            Combines filters with logical OR.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.ICompositeFilterDescriptor">
            <summary>
            Represents a composite filtering abstraction which has a collection of 
            filter descriptors combined together by a logical operator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.ICompositeFilterDescriptor.LogicalOperator">
            <summary>
            Gets or sets the logical operator.
            </summary>
            <value>The logical operator.</value>
        </member>
        <member name="P:Telerik.Windows.Data.ICompositeFilterDescriptor.FilterDescriptors">
            <summary>
            Gets filter descriptors that will be used for composition.
            </summary>
            <value>The filter descriptors used for composition.</value>
        </member>
        <member name="T:Telerik.Windows.Data.IFilterDescriptor">
            <summary>
            Represents a filtering abstraction that knows how to create predicate filtering expression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.IFilterDescriptor.CreateFilterExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a predicate filter expression used for collection filtering.
            </summary>
            <param name="instance">The instance expression, which will be used for filtering.</param>
            <returns>A predicate filter expression.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.OperatorValueFilterDescriptorBase">
            <summary>
            Represents the base class for all filter descriptors that have an operator and a value.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.Operator" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.Operator">
            <summary>
            Gets or sets the filter operator.
            </summary>
            <value>The filter operator.</value>
        </member>
        <member name="F:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.ValueProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.Value" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.Value">
            <summary>
            Gets or sets the target filter value.
            </summary>
            <value>The filter value.</value>
        </member>
        <member name="F:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.IsCaseSensitiveProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.IsCaseSensitive" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.IsCaseSensitive">
            <summary>
            Gets or sets a value indicating whether this filter descriptor is case sensitive.
            </summary>
            <value><strong>true</strong> if the filter descriptor is case sensitive; otherwise, 
            <strong>false</strong>. The default value is <strong>true</strong>.</value>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.IsActive">
            <summary>
            Gets a value indicating whether this filter is active. A filter is considered active if
            its Value is different from OperatorValueFilterDescriptorBase.UnsetValue.
            </summary>
            <value>A value indicating whether this filter is active.</value>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.CultureInfo">
            <summary>
            Gets or sets the culture of the filter descriptor.    
            </summary>
            <value>The culture of the filter descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.UnsetValue">
            <summary>
            Specifies a static value that is used by the filtering system 
            rather than null to indicate that a OperatorValueFilterDescriptorBase.Value is 
            not set and thus the filter is inactive.
            </summary>
            <value>An unset value.</value>
            <remarks>
            Assign this value to a OperatorValueFilterDescriptorBase.Value if you want to 
            mark it as inactive.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorValueFilterDescriptorBaseUnsetValue">
            <summary>
            Thread-safe singleton implementation for specifying a 
            static value that is used by FilterDescriptors rather 
            than null to indicate that FilterDescriptor.Value is not set.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorValueFilterDescriptorBaseUnsetValue.Instance">
            <summary>
            Gets or sets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorValueFilterDescriptorBaseUnsetValue.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorValueFilterDescriptorBaseUnsetValue" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.OperatorValueFilterDescriptorBase.OperatorValueFilterDescriptorBaseUnsetValue.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.AggregatedGroupDescriptorBase">
            <summary>
            Serves as a base class for group descriptors with aggregate functions. Holds <see cref="P:Telerik.Windows.Data.AggregatedGroupDescriptorBase.AggregateFunctions"/> 
            that will be used to aggregate the results from the descriptor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.AggregatedGroupDescriptorBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.AggregatedGroupDescriptorBase"/> class.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.AggregatedGroupDescriptorBase.AggregateFunctionsPropertyKey">
            <summary>
            Identifies the AggregateFunctions readonly dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.AggregatedGroupDescriptorBase.AggregateFunctionsProperty">
            <summary>
            Identifies the AggregateFunctions readonly dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AggregatedGroupDescriptorBase.AggregateFunctions">
            <summary>
            Gets the aggregate functions collection used when grouping is executed.
            This is a dependency property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateFunction`2">
            <summary>
            Represents a class that allows a custom lambda expression to be executed over a sequence of items.
            </summary>
            <typeparam name="TElement">The type of the elements in the sequence.</typeparam>
            <typeparam name="TResult">The type of the function result.</typeparam>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunction`2.AggregationExpression">
            <summary>
            Gets or sets the aggregation expression.
            </summary>
            <value>The aggregation expression.</value>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunction`2.CreateAggregateExpression(System.Linq.Expressions.Expression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.AggregateResultMergeAction">
            <summary>
            Represents an aggregate result merge action.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.AggregateResultMergeAction.Add">
            <summary>
            Add.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.AggregateResultMergeAction.Remove">
            <summary>
            Remove.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.Addition">
            <summary>
            Addition.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.Subtraction">
            <summary>
            Subtraction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.Multiplication">
            <summary>
            Multiplication.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.Division">
            <summary>
            Division.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.IsGreaterThan">
            <summary>
            IsGreaterThan.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ArithmeticFunctionsCache.ArithmeticOperation.IsLessThan">
            <summary>
            IsLessThan.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.EnumerableAggregateFunction">
            <summary>
            Represents an <see cref="T:Telerik.Windows.Data.AggregateFunction"/> that uses aggregate extension 
            methods provided in <see cref="T:System.Linq.Enumerable"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableAggregateFunction.CreateAggregateExpression(System.Linq.Expressions.Expression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.EnumerableAggregateFunctionBase">
            <summary>
            Base class for all aggregate functions that will use extension 
            methods in <see cref="T:System.Linq.Enumerable"/> for aggregation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.EnumerableAggregateFunctionBase.AggregateMethodName">
            <summary>
            Gets the name of the aggregate method on the <see cref="P:Telerik.Windows.Data.EnumerableAggregateFunctionBase.ExtensionMethodsType"/>
            that will be used for aggregation.
            </summary>
            <value>The name of the aggregate method that will be used.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumerableAggregateFunctionBase.ExtensionMethodsType">
            <summary>
            Gets the type of the extension methods that holds the extension methods for
            aggregation. For example <see cref="T:System.Linq.Enumerable"/> or <see cref="T:System.Linq.Queryable"/>.
            </summary>
            <value>
            The type of that holds the extension methods. The default value is <see cref="T:System.Linq.Enumerable"/>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableAggregateFunctionBase.GenerateFunctionName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.EnumerableSelectorAggregateFunction">
            <summary>
            Represents an <see cref="T:Telerik.Windows.Data.AggregateFunction"/> that uses aggregate extension 
            methods provided in <see cref="T:System.Linq.Enumerable"/> using <see cref="P:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.SourceField"/>
            as a member selector.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.SourceField">
            <summary>
            Gets or sets the name of the field, of the item from the set of items, which value is used as the argument of the aggregate function.
            </summary>
            <value>The name of the field to get the argument value from.</value>
        </member>
        <member name="P:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.SourceFieldType">
            <summary>
            Gets or sets the type of the member that is used as the argument of the aggregate function.
            Set this property if the member type cannot be resolved automatically.
            Such cases are: items with ICustomTypeDescriptor, XmlNode or DataRow.
            </summary>
            <value>The type of the member used as the argument of the aggregate function.</value>
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.GenerateFunctionName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.CreateAggregateExpression(System.Linq.Expressions.Expression)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableSelectorAggregateFunction.GetAggregationValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.GroupDescriptor`3">
            <summary>
            Allows grouping by a lambda expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptor`3.GroupingExpression">
            <summary>
            Gets or sets the grouping predicate.
            </summary>
            <value>The grouping predicate.</value>
        </member>
        <member name="F:Telerik.Windows.Data.GroupDescriptor`3.GroupingExpressionProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.GroupDescriptor`3.GroupingExpression"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptor`3.GroupSortingExpression">
            <summary>
            Gets or sets the grouping and sorting predicate.
            </summary>
            <value>The grouping and sorting predicate.</value>
        </member>
        <member name="F:Telerik.Windows.Data.GroupDescriptor`3.GroupSortingExpressionProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.GroupDescriptor`3.GroupSortingExpression"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptor`3.CreateGroupKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptor`3.CreateGroupSortExpression(System.Linq.Expressions.Expression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.GroupDescriptorBase">
            <summary>
            Serves as a base class for group descriptors. Holds <see cref="P:Telerik.Windows.Data.GroupDescriptorBase.SortDirection"/> 
            that will be used to sort the groups created from the descriptor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptorBase.CreateGroupKeyExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a group expression by delegating its creation to 
            <see cref="M:Telerik.Windows.Data.GroupDescriptorBase.CreateGroupKeyExpression(System.Linq.Expressions.ParameterExpression)"/>, if 
            <paramref name="itemExpression"/> is <see cref="T:System.Linq.Expressions.ParameterExpression"/>, 
            otherwise throws <see cref="T:System.ArgumentException"/>
            </summary>
            <param name="itemExpression">
            The instance expression, which will be used for grouping.
            </param>
            <returns>
            Expression that creates group key for the given item.
            </returns>
            <exception cref="T:System.ArgumentException">Parameter should be of type <see cref="T:System.Linq.Expressions.ParameterExpression"/></exception>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptorBase.CreateGroupKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <summary>
            Creates a group expression that returns 
            the grouping key for each item in a collection.
            </summary>
            <param name="parameterExpression">
            The parameter expression, which will be used for grouping.
            </param>
            <returns>
            Expression that creates group key for the given item.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptorBase.CreateGroupSortExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates sorting key expression that sorts the groups 
            created from this descriptor using the group's key.
            </summary>
            <param name="groupingExpression">The grouping expression, which represents the grouped items
            created from the <see cref="M:Telerik.Windows.Data.GroupDescriptorBase.CreateGroupKeyExpression(System.Linq.Expressions.Expression)"/>.</param>
            <returns>
            Expression that represents the sort criteria for each group.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptorBase.SortDirection">
            <summary>
            Gets or sets the sort direction for this descriptor. If the value is null
            no sorting will be applied.
            </summary>
            <value>The sort direction. The default value is null.</value>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptorBase.CycleSortDirection">
            <summary>
            Changes the <see cref="T:Telerik.Windows.Data.SortDescriptor"/> to the next logical value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptorBase.ToGroupDescription">
            <summary>
            Converts this GroupDescriptor to a GroupDescription implementation.
            </summary>
            <returns>A GroupDescription implementation.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptorBase.DisplayContent">
            <summary>
            Gets or sets the content which will be used to visually represent this descriptor.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.GroupDescriptorBase.DisplayContentProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.GroupDescriptorBase.DisplayContent" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.GroupInfo.GroupKeyFunction">
            <summary>
            Returns a function that accept a data item and returns its group key,
            for example if you supply a Player it will return his Country.
            </summary>
            <value>The group key function.</value>
        </member>
        <member name="T:Telerik.Windows.Data.GroupingImpl`2">
            <summary>
            Helper class used as IGrouping implementation.
            </summary>
            <typeparam name="TGroupKey">The type of the group key.</typeparam>
            <typeparam name="TItem">The type of the item.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Data.GroupingImpl`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.GroupingImpl`2.#ctor(System.Object,System.Collections.IEnumerable,System.Int32,System.Boolean,Telerik.Windows.Data.AggregateResultCollection,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.GroupingImpl`2"/> class.
            </summary>
            <param name="key">The group key.</param>
            <param name="originalItems">The original items.</param>
            <param name="originalItemCount">The original item count.</param>
            <param name="hasSubgroups">If set to <c>true</c> This group has child groups.</param>
            <param name="aggregateResults">The aggregate results.</param>
            <param name="parentGroup">The parent group.</param>
        </member>
        <member name="T:Telerik.Windows.Data.IAggregateFunctionsProvider">
            <summary>
            Defines property for collection of <see cref="T:Telerik.Windows.Data.AggregateFunction"/>.
            Used by the expression data engine to create aggregates for a given group.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IAggregateFunctionsProvider.AggregateFunctions">
            <summary>
            Gets the aggregate functions used when grouping is executed.
            </summary>
            <value>The aggregate functions that will be used in grouping.</value>
        </member>
        <member name="T:Telerik.Windows.Data.IGroupDescriptor">
            <summary>
            Represents a grouping abstraction that knows how to 
            create group key and group sort expressions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.IGroupDescriptor.CreateGroupKeyExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a group expression that returns 
            the grouping key for each item in a collection.
            </summary>
            <param name="itemExpression">
            Expression representing an item in a collection.
            </param>
            <returns>
            Expression that creates group key for the given item.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.IGroupDescriptor.CreateGroupSortExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates the group order by expression that sorts 
            the groups created from this descriptor.
            </summary>
            <param name="groupingExpression">
            The grouping expression, which represents the grouped items 
            created from the <see cref="M:Telerik.Windows.Data.IGroupDescriptor.CreateGroupKeyExpression(System.Linq.Expressions.Expression)"/>.
            </param>
            <returns>
            Expression that represents the sort criteria for each group.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.IGroupDescriptor.SortDirection">
            <summary>
            Gets or sets the sort direction for this descriptor. If the value is <see langword="null"/>
            no sorting will be applied.
            </summary>
            <value>The sort direction. The default value is <see langword="null"/>.</value>
        </member>
        <member name="T:Telerik.Windows.Data.NotifyGroupCollectionChangedAction">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.Add">
            <summary>
            Denotes that one or more items were added to the collection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.Remove">
            <summary>
            Denotes that one or more items were removed from the collection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.Replace">
            <summary>
            Denotes that one or more items were replaced in the collection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.Move">
            <summary>
            Denotes that one or more items were moved within the collection.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.Reset">
            <summary>
            Denotes that the content of the collection changed dramatically.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.NotifyGroupCollectionChangedAction.GroupChange">
            <summary>
            Denotes that the content of a group is changed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs">
            <summary>
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.AffectedGroup">
            <summary>
            Gets the group which is affected by any CollectionChanged operation like Add, Remove or Replace.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.Action">
            <summary>
            Gets the action that caused the event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.NewItems">
            <summary>
            Gets the list of new items involved in the change.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.NewStartingIndex">
            <summary>
            Gets the index at which the change occurred.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.OldItems">
            <summary>
            Gets the list of items affected by a Replace, Remove, or Move action.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.NotifyGroupCollectionChangedEventArgs.OldStartingIndex">
            <summary>
            Gets the index at which a Move, Remove, or Replace action occurred.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptionExtensions.AsGroupDescriptor(System.ComponentModel.GroupDescription)">
            <summary>
            Converts items of type GroupDescriptions to GroupDescriptor. Currently works only with PropertyGroupDescriptions.
            </summary>
            <param name="groupDescription">Description to be converted.</param>
            <returns>The converted group descriptor.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateFunctionsGroup">
            <summary>
            Represents group with aggregate functions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunctionsGroup.AggregateFunctionsProjection">
            <summary>
            Gets or sets the aggregate functions projection for this group. 
            This projection is used to generate aggregate functions results for this group.
            </summary>
            <value>The aggregate functions projection.</value>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunctionsGroup.GetAggregateResults(System.Collections.Generic.IEnumerable{Telerik.Windows.Data.AggregateFunction})">
            <summary>
            Gets the aggregate results generated for the given aggregate functions.
            </summary>
            <value>The aggregate results for the provided aggregate functions.</value>
            <exception cref="T:System.ArgumentNullException"><c>functions</c> is null.</exception>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableCollectionViewGroup">
            <summary>
            Represents a wrapper over an AggregateFunctionsGroup that allows
            adding and removing of child items/groups.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.Key">
            <summary>
            Gets the key for this group.
            </summary>
            <value>The key for this group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.Items">
            <summary>
            Gets the immediate items contained in this group.
            </summary>
            <value></value>
            <returns>
            A read-only collection of the immediate items in this group. 
            This is either a collection of subgroups or a collection of items 
            if this group does not have any subgroups.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.EnsureItems">
            <summary>
            Method used to lazy-load the original items. The original items will be
            enumerator only after someone requests them, for example when the user expands
            a group. Once they've been enumerated, we will never need them again.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.Subgroups">
            <summary>
            Gets the subgroups, if <see cref="P:Telerik.Windows.Data.QueryableCollectionViewGroup.HasSubgroups"/> is true, otherwise empty collection.
            </summary>
            <value>The subgroups.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.IsBottomLevel">
            <summary>
            Gets a value that indicates whether this group has any subgroups.
            </summary>
            <value></value>
            <returns>true if this group is at the bottom level and does not have any subgroups; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.HasSubgroups">
            <summary>
            Gets a value indicating whether this instance has sub groups.
            </summary>
            <value>
            	<c>true</c> if this instance has sub groups; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.ParentGroupInternal">
            <summary>
            Gets the parent group object. 
            This property should be used for navigation purposes like walk through the group tree.
            </summary>
            <value>The parent group object.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.ParentGroup">
            <summary>
            Gets the parent group.
            </summary>
            <value>The parent group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.AggregateResults">
            <summary>
            Gets the aggregate results.
            </summary>
            <value>The aggregate results.</value>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroup.RootGroup">
            <summary>
            Gets the root group.
            </summary>
            <value>The root group.</value>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.#ctor(System.Object,System.Collections.IEnumerable,System.Int32,System.Boolean,Telerik.Windows.Data.AggregateResultCollection,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableCollectionViewGroup"/> class.
            </summary>
            <param name="key">The group key.</param>
            <param name="originalItems">The original items.</param>
            <param name="originalItemCount">The original item count.</param>
            <param name="hasSubgroups">If set to <c>true</c> This group has child groups.</param>
            <param name="aggregateResults">The aggregate results.</param>
            <param name="parentGroup">The parent group.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.Load">
            <summary>
            Loads all items for the group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableCollectionViewGroup.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.QueryableCollectionViewGroupRoot">
            <summary>
            This class is used as an entry point for all actions related to grouping (like add, remove, edit).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroupRoot.IsBottomLevel">
            <inheritdoc />
            <remarks>
            Returns false.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Data.QueryableCollectionViewGroupRoot.RootGroup">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.HierarchyDescriptor`2">
            <summary>
            Describes the hierarchy relation in the terms of member access expression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchyDescriptor`2.#ctor(System.Func{`0,System.Collections.Generic.IEnumerable{`1}})">
            <summary>
            Initializes a new instance of the HierarchyDescriptor class.
            </summary>
            <param name="hierarchySelector">The hierarchy descriptor expression.</param>
        </member>
        <member name="T:Telerik.Windows.Data.HierarchyDescriptor">
            <summary>
            Describes the hierarchy relation in the terms of member access expression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchyDescriptor.#ctor(System.Linq.Expressions.Expression{System.Func{System.Object,System.Collections.IEnumerable}})">
            <summary>
            Initializes a new instance of the HierarchyDescriptor class.
            </summary>
            <param name="hierarchySelector">The hierarchy descriptor expression.</param>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchyDescriptor.HierachySelector">
            <summary>
            Gets the hierarchy selector expression that was used to create the descriptor.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.HierarchyDescriptorCollection">
            <summary>
            A collection that contains hierarchy descriptors for the HierarchyDescriptor.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Data.Hierarchy.IHierarchyDescriptor">
            <summary>
            Describes the hierarchy relation of the data in the HierarchyCollectionView.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Data.Hierarchy.IHierarchyDescriptor.CreateHierarchySelectorExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a projection function that will be used as a hierarchy selector.
            </summary>
            <param name="instance">The instance expression, which will be used for filtering.</param>
            <returns>A predicate filter expression.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.ISelectDescriptor">
            <summary>
            Represents a type projection abstraction that knows how to create predicate selection expression.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ISelectDescriptor.CreateSelectExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a predicate selection expression used for collection selection.
            </summary>
            <param name="instance">The instance expression, which will be used for selection.</param>
            <returns>A predicate selection expression.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.ISelectDescriptor.ProjectedMemberName">
            <summary>
            Gets the name of the projected member.
            </summary>
            <value>The name of the projected member.</value>
        </member>
        <member name="P:Telerik.Windows.Data.ISelectDescriptor.ProjectedMemberType">
            <summary>
            Gets the type of the projected member.
            </summary>
            <value>The type of the projected member.</value>
        </member>
        <member name="T:Telerik.Windows.Data.SelectDescriptor">
            <summary>
            Represents declarative selection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.SelectDescriptor.SourceMemberName">
            <summary>
            Gets or sets the name of the source member.
            </summary>
            <value>The name of the source member.</value>
        </member>
        <member name="P:Telerik.Windows.Data.SelectDescriptor.ProjectedMemberType">
            <summary>
            Gets or sets the type of the projected member.
            </summary>
            <value>The type of the projected member.</value>
        </member>
        <member name="P:Telerik.Windows.Data.SelectDescriptor.ProjectedMemberName">
            <summary>
            Gets or sets the name of the projected member.
            </summary>
            <value>The name of the projected member.</value>
        </member>
        <member name="M:Telerik.Windows.Data.SelectDescriptor.CreateSelectExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a predicate expression used for collection selection.
            </summary>
            <param name="instance">The instance expression, which will be used for selection.</param>
            <returns>A selection expression.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.SelectDescriptorCollection">
            <summary>
            Represents collection of <see cref="T:Telerik.Windows.Data.ISelectDescriptor"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.SelectDescriptorCollection.ProjectedType">
            <summary>
            Gets or sets the projected type.
            </summary>
            <value>The projected type.</value>
        </member>
        <member name="M:Telerik.Windows.Data.SelectDescriptorCollection.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.ISortDescriptor">
            <summary>
            Represents a sorting abstraction that knows how to create sort key expressions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ISortDescriptor.CreateSortKeyExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a sort expression that returns 
            the sorting key for each item in a collection.
            </summary>
            <param name="itemExpression">
            Expression representing an item in a collection.
            </param>
            <returns>
            Expression that creates sort key for the given item.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.ISortDescriptor.SortDirection">
            <summary>
            Gets or sets the sort direction for this descriptor.
            </summary>
            <value>The sort direction.</value>
        </member>
        <member name="T:Telerik.Windows.Data.SortDescriptor`2">
            <summary>
            Allows sorting by a lambda expression.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.SortDescriptor`2.SortingExpression">
            <summary>
            Gets or sets the sorting predicate.
            </summary>
            <value>The sorting predicate.</value>
        </member>
        <member name="F:Telerik.Windows.Data.SortDescriptor`2.SortingExpressionProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.SortDescriptor`2.SortingExpression"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.SortDescriptor`2.CreateSortKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.SortDescriptorBase">
            <summary>
            Serves as a base class for sort descriptors.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.SortDescriptorBase.CreateSortKeyExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates a sort expression that returns
            the sorting key for each item in a collection.
            </summary>
            <param name="itemExpression">Expression representing an item in a collection.</param>
            <returns>
            Expression that creates sort key for the given item.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.SortDescriptorBase.CreateSortKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <summary>
            Creates a sort expression that returns 
            the sorting key for each item in a collection.
            </summary>
            <param name="parameterExpression">
            The parameter expression, which will be used for sorting.
            </param>
            <returns>
            Expression that creates a sort key for the given item.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Data.SortDescriptorBase.SortDirection">
            <summary>
            Gets or sets the sort direction for this descriptor.
            </summary>
            <value>The sort direction.</value>
        </member>
        <member name="F:Telerik.Windows.Data.SortDescriptorBase.SortDirectionProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.SortDescriptorBase.SortDirection"/> Dependency Property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.StringWithAccentInsensitiveFilterOperatorExpressionBuilder">
            <summary>
            Currently implemented methods works for the Search As You Type. For filtering functionality, all methods from StringFilterOperatorExpressionBuilder need to be overridden and modified to use CompareInfo with CompareOptions.IgnoreNonSpace.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.CustomTypeProviderExtensions">
            <summary>ICustomTypeProvider extensions.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeProviderExtensions.Property``1(System.Reflection.ICustomTypeProvider,System.String)">
            <summary>
            Gets the value of a property on the given custom type provider.
            </summary>
            <param name="typeProvider">The type provider, which property will be accessed.</param>
            <param name="propertyName">Name of the property.</param>
            <returns>The value of the given property for the given custom type provider.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeProviderExtensions.Property``1(System.Object,System.String)">
            <summary>
            Gets the value of a property on the given component.
            </summary>
            <param name="component">The component, which property will be accessed.</param>
            <param name="propertyName">Name of the property.</param>
            <returns>The value of the given property for the given component.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeProviderExtensions.GetPropertyValueRecursive``1(System.Object,System.Collections.Generic.Stack{System.String})">
            <summary>
            Gets the property value for a component by recursively drilling a property names stack, 
            i.e. Company -> Department -> Employees -> Count and so on.
            </summary>
            <param name="componentInstance">The component.</param>
            <param name="propertyNamesStack">The stack containing the property names.</param>
            <returns>The property value.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.Expressions.ExpressionBuilderOptions.LiftMemberAccessToNull">
            <summary>
            Gets or sets a value indicating whether member access expression used
            by this builder should be lifted to null. The default value is true.
            </summary>
            <value>
            	<c>true</c> if member access should be lifted to null; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.OperatorValueFilterDescriptorExpressionBuilderBase">
            <summary>
            Base class for all builders that build expression based on an operator and a value.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.StringFilterOperatorExpressionBuilder.ShouldGenerateToLowerCall(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
            <summary>
            If we have an equality comparison operator and either of the operands is null/string.Empty
            we don't need to call ToLower.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.SelectDescriptorCollectionExpressionBuilder.#ctor(System.Linq.Expressions.ParameterExpression,Telerik.Windows.Data.SelectDescriptorCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.Expressions.SelectDescriptorCollectionExpressionBuilder"/> class.
            </summary>
            <param name="parameterExpression">The parameter expression.</param>
            <param name="selectDescriptors">The select descriptors.</param>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.SelectDescriptorCollectionExpressionBuilder.CreateBodyExpression">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder.#ctor(System.Linq.Expressions.ParameterExpression,Telerik.Windows.Data.ISelectDescriptor,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder"/> class.
            </summary>
            <param name="parameterExpression">The parameter expression.</param>
            <param name="descriptor">The descriptor.</param>
            <param name="projectedType">Type of the projected.</param>
        </member>
        <member name="P:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder.Descriptor">
            <summary>
            Gets the descriptor.
            </summary>
            <value>The descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder.ProjectedMember">
            <summary>
            Gets the projected member.
            </summary>
            <value>The projected member.</value>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder.CreateSelectMemberBinding">
            <summary>
            Creates the select member binding.
            </summary>
            <returns>The member binding.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.SelectDescriptorExpressionBuilder.CreateBodyExpression">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.CustomTypeDescriptorExtensions">
            <summary>CustomTypeDescriptor extensions.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeDescriptorExtensions.Property``1(System.ComponentModel.ICustomTypeDescriptor,System.String)">
            <summary>
            Gets the value of a property on the given custom type descriptor.
            </summary>
            <param name="typeDescriptor">The type descriptor, which property will be accessed.</param>
            <param name="propertyName">Name of the property.</param>
            <returns>The value of the given property for the given custom type descriptor.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeDescriptorExtensions.Property``1(System.Object,System.String)">
            <summary>
            Gets the value of a property on the given component via call to TypeDescriptor.GetProperties.
            </summary>
            <param name="component">The component, which property will be accessed.</param>
            <param name="propertyName">Name of the property.</param>
            <returns>The value of the given property for the given component.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.CustomTypeDescriptorExtensions.GetPropertyValueRecursive``1(System.Object,System.Collections.Generic.Stack{System.String})">
            <summary>
            Gets the property value for a component by recursively drilling a property names stack, 
            i.e. Company -> Department -> Employees -> Count and so on.
            </summary>
            <param name="componentInstance">The component.</param>
            <param name="propertyNamesStack">The stack containing the property names.</param>
            <returns>The property value.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.FilterDescriptorExpressionBuilder">
            <summary>
             Builds filtering expression for the FilterDescriptor class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Expressions.XmlNodeExtensions">
            <summary>
            Holds extension methods for <see cref="T:System.Xml.XmlNode"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Expressions.XmlNodeExtensions.ChildElementInnerText(System.Xml.XmlNode,System.String)">
            <summary>
            Returns child element InnerText.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.ExpressionTypeConverter">
            <summary>
            Converts a string to a LINQ node expression containing the parsed string.
            Uses Telerik Expression Parser to parse the string to an ExpressionNode (AST) object.
            If there is a parse error returns null.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ExpressionTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
            <remarks>
            True if <paramref name="sourceType" /> is a <see cref="T:System.String" /> type; otherwise, false.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.ExpressionTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.CollectionExtensions">
            <summary>
            Holds extension methods for generic ICollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionExtensions.AddRange``1(System.Collections.Generic.ICollection{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Adds the elements of the specified collection to the end of a generic ICollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.CollectionExtensions.RemoveItems``1(System.Collections.Generic.ICollection{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Removes the elements of the specified collection from a generic ICollection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.FilterCompositionLogicalOperatorConverter">
            <summary>
            Converts <see cref="T:Telerik.Windows.Data.FilterCompositionLogicalOperator"/> to <see cref="T:System.String"/> using 
            localization infrastructure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterCompositionLogicalOperatorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            Localized string for given filter operator.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.FilterCompositionLogicalOperatorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.FilterOperatorConverter">
            <summary>
            Converts <see cref="T:Telerik.Windows.Data.FilterOperator"/> to <see cref="T:System.String"/> using 
            localization infrastructure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FilterOperatorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            Localized string for given filter operator.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.FilterOperatorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.HierarchicalCollectionViewBase">
            <summary>
            Serves as a base class to all hierarchical views.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalCollectionViewBase.ShouldInitializeGroupProxy">
            <inheritdoc />
            <remarks>
            Always false for HierarchicalCollectionViewBase.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.GetItemAt(System.Int32)">
            <summary>
            Retrieves the item at the specified zero-based index in the view.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalCollectionViewBase.HierarchyDescriptors">
            <summary>
            Gets the hierarchy descriptors used for hierarchy construction. 
            If this view is a child one, its root view hierarchy descriptors are returned.
            </summary>
            <value>The hierarchy descriptors.</value>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.CreateView">
            <inheritdoc />
            <remarks>
            Overrides the CreateView method and returns a IQueryable view
            specific to the hierarchy collection view.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.OnFilterDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.OnFilterDescriptorsItemChanged(System.Object,Telerik.Windows.Data.ItemChangedEventArgs{Telerik.Windows.Data.IFilterDescriptor})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.RefreshOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.OnSortDescriptorsCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalCollectionViewBase.InternalCount">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.HierarchicalCollectionViewBase.IsGrouped">
            <inheritdoc />
            <remarks>
            Returns false. Grouping is not supported.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.InitializeInternalList(System.Linq.IQueryable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.CreateInternalList">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.PopulateInternalList(System.Linq.IQueryable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.RefreshOnItemAction(System.Object,Telerik.Windows.Data.ItemAction)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.HierarchicalCollectionViewBase.OnFilterDescriptorsChanged">
            <summary>
            Called when anything in the filter descriptors changes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.ItemPropertyInfoExtensions">
            <summary>
            Holds extension methods for <see cref="T:System.ComponentModel.ItemPropertyInfo"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.ItemPropertyInfoOrderComparer">
            <summary>
            Represents a ItemPropertyInfo comparison operation that uses the DisplayAttribute[Order].
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ItemPropertyInfoOrderComparer.DefaultColumnDisplayOrder">
            <summary>
            The default order to use for columns when there is no DisplayAttribute.Order
            value available for the property.
            </summary>
            <remarks>
            The value of 10,000 comes from the DataAnnotations spec, allowing
            some properties to be ordered at the beginning and some at the end.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.ItemPropertyInfoOrderComparer.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Data.ItemPropertyInfoOrderComparer" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ItemPropertyInfoOrderComparer.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Telerik.Windows.Data.ItemPropertyInfoOrderComparer" /> class from being created.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.ItemPropertyInfoOrderComparer.Instance">
            <summary>
            Gets or sets the instance.
            </summary>
            <value>The instance.</value>
        </member>
        <member name="M:Telerik.Windows.Data.ItemPropertyInfoOrderComparer.Compare(System.ComponentModel.ItemPropertyInfo,System.ComponentModel.ItemPropertyInfo)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            <returns>
            Value Condition Less than zero<paramref name="x"/> is less than <paramref name="y"/>.Zero<paramref name="x"/> equals <paramref name="y"/>.Greater than zero<paramref name="x"/> is greater than <paramref name="y"/>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.GroupDescriptorCollection">
            <summary>
            Represents a collection of <see cref="T:Telerik.Windows.Data.GroupDescriptor"/> objects.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.IPagedCollectionView">
            <summary>Provides paging functionality for a collection view.</summary>
        </member>
        <member name="E:Telerik.Windows.Data.IPagedCollectionView.PageChanged">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.IPagedCollectionView.PageIndex" /> has changed.</summary>
        </member>
        <member name="E:Telerik.Windows.Data.IPagedCollectionView.PageChanging">
            <summary>Occurs when the <see cref="P:Telerik.Windows.Data.IPagedCollectionView.PageIndex" /> is changing.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.IPagedCollectionView.MoveToFirstPage">
            <summary>Sets the first page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.IPagedCollectionView.MoveToLastPage">
            <summary>Sets the last page as the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.IPagedCollectionView.MoveToNextPage">
            <summary>Moves to the page after the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.IPagedCollectionView.MoveToPage(System.Int32)">
            <summary>Requests a page move to the page at the specified index.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
            <param name="pageIndex">The index of the page to move to.</param>
        </member>
        <member name="M:Telerik.Windows.Data.IPagedCollectionView.MoveToPreviousPage">
            <summary>Moves to the page before the current page.</summary>
            <returns>true if the operation was successful; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.CanChangePage">
            <summary>Gets a value that indicates whether the <see cref="P:Telerik.Windows.Data.IPagedCollectionView.PageIndex" /> value is allowed to change.</summary>
            <returns>true if the <see cref="P:Telerik.Windows.Data.IPagedCollectionView.PageIndex" /> value is allowed to change; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.IsPageChanging">
            <summary>Gets a value that indicates whether a page index change is in process.</summary>
            <returns>true if the page index is changing; otherwise, false.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.ItemCount">
            <summary>Gets the minimum number of items known to be in the source collection.</summary>
            <returns>The minimum number of items known to be in the source collection.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.PageIndex">
            <summary>Gets the zero-based index of the current page.</summary>
            <returns>The zero-based index of the current page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.PageSize">
            <summary>Gets or sets the number of items to display on a page.</summary>
            <returns>The number of items to display on a page.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.IPagedCollectionView.TotalItemCount">
            <summary>Gets the total number of items in the source collection.</summary>
            <returns>The total number of items in the source collection, or -1 if the total number is unknown.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.PageChangingEventArgs">
            <summary>Provides data for notifications when the page index is changing.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.PageChangingEventArgs.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Data.PageChangingEventArgs" /> class.</summary>
            <param name="newPageIndex">The index of the requested page.</param>
        </member>
        <member name="P:Telerik.Windows.Data.PageChangingEventArgs.NewPageIndex">
            <summary>Gets the index of the requested page.</summary>
            <returns>The index of the requested page.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.SortDescriptor">
            <summary>
            Represents declarative sorting.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.SortDescriptor.Member">
            <summary>
            Gets or sets the member name that will be used for sorting.
            </summary>
            <value>The member name that will be used for sorting.</value>
        </member>
        <member name="F:Telerik.Windows.Data.SortDescriptor.MemberProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.SortDescriptor.Member"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.SortDescriptor.CreateSortKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.SortDescriptor.Equals(Telerik.Windows.Data.SortDescriptor)">
            <summary>
            Checks whether this SortDescriptor is equal to another.
            </summary>
            <param name="other">The SortDescriptor to check equality against.</param>
        </member>
        <member name="T:Telerik.Windows.Data.SortDescriptorCollection">
            <summary>
            Represents collection of <see cref="T:Telerik.Windows.Data.SortDescriptor"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.FilterOperator">
            <summary>
            Operator used in <see cref="T:Telerik.Windows.Data.FilterDescriptor"/>
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsLessThan">
            <summary>
            Left operand must be smaller than the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsLessThanOrEqualTo">
            <summary>
            Left operand must be smaller than or equal to the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsEqualTo">
            <summary>
            Left operand must be equal to the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsNotEqualTo">
            <summary>
            Left operand must be different from the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsGreaterThanOrEqualTo">
            <summary>
            Left operand must be larger than the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsGreaterThan">
            <summary>
            Left operand must be larger than or equal to the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.StartsWith">
            <summary>
            Left operand must start with the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.EndsWith">
            <summary>
            Left operand must end with the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.Contains">
            <summary>
            Left operand must contain the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.DoesNotContain">
            <summary>
            Left operand must not contain the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsContainedIn">
            <summary>
            Left operand must be contained in the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsNotContainedIn">
            <summary>
            Left operand must not be contained in the right one.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsNull">
            <summary>
            Operand is null.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsNotNull">
            <summary>
            Operand is not null.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsEmpty">
            <summary>
            Operand is empty.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.FilterOperator.IsNotEmpty">
            <summary>
            Operand is not empty.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Group">
            <summary>
            Represents an item that is created after grouping.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Group.HasSubgroups">
            <summary>
            Gets a value indicating whether this instance has any sub groups.
            </summary>
            <value>
            	<c>true</c> if this instance has sub groups; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.Group.ItemCount">
            <summary>
            Gets the number of items in this group.
            </summary>
            <value>The items count.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Group.Subgroups">
            <summary>
            Gets the subgroups, if <see cref="P:Telerik.Windows.Data.Group.HasSubgroups"/> is true, otherwise empty collection.
            </summary>
            <value>The subgroups.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Group.Items">
            <summary>
            Gets the items in this groups.
            </summary>
            <value>The items in this group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Group.Key">
            <summary>
            Gets the key for this group.
            </summary>
            <value>The key for this group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Group.ParentGroup">
            <summary>
            Gets the parent group.
            </summary>
            <value>The parent group.</value>
        </member>
        <member name="M:Telerik.Windows.Data.Group.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.Group.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.Group.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.GroupDescriptor">
            <summary>
            Represents group descriptor, which groups by item's <see cref="P:Telerik.Windows.Data.GroupDescriptor.Member"/>
            and sorts the groups by their <see cref="P:System.Linq.IGrouping`2.Key"/>s.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.GroupDescriptor.MemberProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Data.GroupDescriptor.Member" /> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptor.Member">
            <summary>
            Gets or sets the member name which will be used for grouping.
            </summary>
            <returns>The member name that will be used for grouping.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.GroupDescriptor.MemberType">
            <summary>
            Gets or sets the type of the member that is used for grouping.
            Set this property if the member type cannot be resolved automatically.
            Such cases are: items with ICustomTypeDescriptor, XmlNode or DataRow.
            Changing this property will not raise 
            <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event.
            </summary>
            <value>The type of the member used for grouping.</value>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptor.ToGroupDescription">
            <inheritdoc />
            <remarks>
            Converts this GroupDescriptor to a GroupDescription implementation.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptor.Equals(Telerik.Windows.Data.GroupDescriptor)">
            <summary>
            Determines whether the specified <paramref name="other"/> descriptor 
            is equal to the current one.
            </summary>
            <param name="other">The other group descriptor.</param>
            <returns>
            True if all members of the current descriptor are 
            equal to the ones of <paramref name="other"/>, otherwise false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.GroupDescriptor.CreateGroupKeyExpression(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.IGroup">
            <summary>
            Represents an item that is created after grouping.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.Key">
            <summary>
            Gets the key for this group.
            </summary>
            <value>The key for this group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.Items">
            <summary>
            Gets the items in this groups.
            </summary>
            <value>The items in this group.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.HasSubgroups">
            <summary>
            Gets a value indicating whether this instance has sub groups.
            </summary>
            <value>
            	<c>true</c> if this instance has sub groups; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.ItemCount">
            <summary>
            Gets the <see cref="P:Telerik.Windows.Data.IGroup.Items"/> count.
            </summary>
            <value>The <see cref="P:Telerik.Windows.Data.IGroup.Items"/> count.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.Subgroups">
            <summary>
            Gets the subgroups, if <see cref="P:Telerik.Windows.Data.IGroup.HasSubgroups"/> is true, otherwise empty collection.
            </summary>
            <value>The subgroups.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IGroup.ParentGroup">
            <summary>
            Gets the parent group.
            </summary>
            <value>The parent group.</value>
        </member>
        <member name="T:Telerik.Windows.Data.ItemChangedEventArgs`1">
            <summary>
            Contains data about the item's property that has been changed.
            </summary>
            <typeparam name="T">Type of the changed item.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Data.ItemChangedEventArgs`1.#ctor(`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.ItemChangedEventArgs`1"/> class.
            </summary>
            <param name="item">The item that has been changed.</param>
            <param name="propertyName">Name of the property that have been changed.</param>
        </member>
        <member name="P:Telerik.Windows.Data.ItemChangedEventArgs`1.Item">
            <summary>
            Gets the item that has been changed.
            </summary>
            <value>The item that has been changed.</value>
        </member>
        <member name="T:Telerik.Windows.Data.ItemChangedEventArgs">
            <summary>
            Contains data about the item's property that has been changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ItemChangedEventArgs.#ctor(System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.ItemChangedEventArgs"/> class.
            </summary>
            <param name="item">The item.</param>
            <param name="propertyName">Name of the property.</param>
        </member>
        <member name="P:Telerik.Windows.Data.ItemChangedEventArgs.Item">
            <summary>
            Gets the item that has been changed.
            </summary>
            <value>The item that has been changed.</value>
        </member>
        <member name="P:Telerik.Windows.Data.ItemChangedEventArgs.PropertyName">
            <summary>
            Gets the name of the property that has been changed.
            </summary>
            <value>The name of the property that has been changed.</value>
        </member>
        <member name="T:Telerik.Windows.Data.ObjectDataBinder">
            <summary>
            Get or sets values of objects by using WPF data binding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ObjectDataBinder.GetValue(System.Object,System.String)">
            <summary>
            Gets the value from the specified binding source, at the specified property path.
            </summary>
            <param name="bindingSource">The binding source to get the value from.</param>
            <param name="propertyPath">The path to the property of the binding source containing the value to get.</param>
            <returns>The value from the specified binding source, at the specified property path.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.ObjectDataBinder.SetValue(System.Object,System.String,System.Object)">
            <summary>
            Sets the value at the specified property path of the specified binding source.
            </summary>
            <param name="bindingSource">The binding source to set the value in.</param>
            <param name="propertyPath">The path to the property of the binding source containing the value to set.</param>
            <param name="value">The new value to set.</param>
            <returns>A value indicating if the value was changed, that is, if the new and the old value are the same.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.ObjectDataBinder.ValueSetter">
            <summary>
            Serves to set a property value by using WPF data binding. 
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.ObservableItemCollection`1">
            <summary>
            <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/> that also raises notifications when
            one of its items property is changed. The items in the collection should implement
            <see cref="T:System.ComponentModel.INotifyPropertyChanged"/>. 
            </summary>
            <typeparam name="T">Type of the items in the collection. It should implement <see cref="T:System.ComponentModel.INotifyPropertyChanged"/>.</typeparam>
        </member>
        <member name="E:Telerik.Windows.Data.ObservableItemCollection`1.ItemChanged">
            <summary>
            Raised when some collection item's property is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.ObservableItemCollection`1.Telerik#Windows#Data#INotifyItemChanged#ItemChanged">
            <summary>
            Raised when some collection item's property is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.ObservableItemCollection`1.OnItemChanged(Telerik.Windows.Data.ItemChangedEventArgs{`0})">
            <summary>
            Raises the <see cref="E:Telerik.Windows.Data.ObservableItemCollection`1.ItemChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.Windows.Data.ItemChangedEventArgs`1"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.ObservableItemCollection`1.ClearItems">
            <inheritdoc />
            <remarks>
            Unsubscribes from all items <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> events.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.ObservableItemCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.FuncExtensions">
            <summary>
            Holds extension methods for delegates.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FuncExtensions.ToUntypedFunc``2(System.Func{``0,``1})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <typeparam name="T">The type of the parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function for the given <paramref name="func"/></returns>
        </member>
        <member name="M:Telerik.Windows.Data.FuncExtensions.ToUntypedTwoParameterFunc``3(System.Func{``0,``1,``2})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function for the given <paramref name="func"/></returns>
        </member>
        <member name="M:Telerik.Windows.Data.FuncExtensions.ToUntypedBooleanFunc``1(System.Func{``0,System.Boolean})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <param name="func">The func.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.FuncExtensions.ToTypedResultFunc``2(System.Func{``0,``1})">
            <summary>
            Converts the given function to an untyped one that has a strongly-typed return value.
            </summary>
            <typeparam name="T">The type of the parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function with a strongly-typed return value for the given <paramref name="func"/></returns>
        </member>
        <member name="T:Telerik.Windows.Data.SimplePropertyDescriptor">
            <summary>Represents an abstract class that provides properties for objects that do not have properties.</summary>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.#ctor(System.Type,System.String,System.Type)">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Data.SimplePropertyDescriptor" /> class.</summary>
            <param name="componentType">A <see cref="T:System.Type"></see> that represents the type of component to which this property descriptor binds. </param>
            <param name="propertyType">A <see cref="T:System.Type"></see> that represents the data type for this property. </param>
            <param name="name">The name of the property. </param>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.#ctor(System.Type,System.String,System.Type,System.Attribute[])">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Data.SimplePropertyDescriptor" /> class.</summary>
            <param name="componentType">A <see cref="T:System.Type"></see> that represents the type of component to which this property descriptor binds. </param>
            <param name="propertyType">A <see cref="T:System.Type"></see> that represents the data type for this property. </param>
            <param name="name">The name of the property. </param>
            <param name="attributes">An <see cref="T:System.Attribute"></see> array with the attributes to associate with the property. </param>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.CanResetValue(System.Object)">
            <summary>Returns whether resetting the component changes the value of the component.</summary>
            <returns>true if resetting the component changes the value of the component; otherwise, false.</returns>
            <param name="component">The component to test for reset capability. </param>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.ResetValue(System.Object)">
            <summary>Resets the value for this property of the component.</summary>
            <param name="component">The component with the property value to be reset. </param>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.ShouldSerializeValue(System.Object)">
            <summary>Returns whether the value of this property can persist.</summary>
            <returns>true if the value of the property can persist; otherwise, false.</returns>
            <param name="component">The component with the property that is to be examined for persistence. </param>
        </member>
        <member name="P:Telerik.Windows.Data.SimplePropertyDescriptor.ComponentType">
            <summary>Gets the type of component to which this property description binds.</summary>
            <returns>A <see cref="T:System.Type"></see> that represents the type of component to which this property binds.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.SimplePropertyDescriptor.IsReadOnly">
            <summary>Gets a value indicating whether this property is read-only.</summary>
            <returns>true if the property is read-only; false if the property is read/write.</returns>
        </member>
        <member name="P:Telerik.Windows.Data.SimplePropertyDescriptor.PropertyType">
            <summary>Gets the type of the property.</summary>
            <returns>A <see cref="T:System.Type"></see> that represents the type of the property.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.GetValue(System.Object)">
            <summary>When overridden in a derived class, gets the current value of the property on a component.</summary>
            <returns>The value of a property for a given component.</returns>
            <param name="component">The component with the property for which to retrieve the value. </param>
        </member>
        <member name="M:Telerik.Windows.Data.SimplePropertyDescriptor.SetValue(System.Object,System.Object)">
            <summary>When overridden in a derived class, sets the value of the component to a different value.</summary>
            <param name="component">The component with the property value that is to be set. </param>
            <param name="value">The new value. </param>
        </member>
        <member name="T:Telerik.Windows.Data.Selection.ISelectorInternal">
            <summary>
            Represents an entity that can select items.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.Selection.ItemSelectionHandler">
            <summary>
            This class supports Selection infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Selection.ItemSelectionHandler.SetSelectionState(System.Collections.Generic.IList{System.Object},System.Boolean)">
            <summary>
            Sets the selection state of the specified items.
            </summary>
            <param name="items">The items.</param>
            <param name="isSelected">If set to <c>true</c> items are marked as selected.</param>
        </member>
        <member name="T:Telerik.Windows.Data.Selection.SelectionModificationOptions">
            <summary>
            Define how selection will be modified.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Selection.SelectionModificationOptions.Extend">
            <summary>
            Gets or sets a value indicating whether selection should be extended.
            </summary>
            <value><c>true</c> if should extend; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Selection.SelectionModificationOptions.MinimallyModify">
            <summary>
            Gets or sets a value indicating whether selection should be minimally modified.
            </summary>
            <value><c>true</c> if should minimally modify; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Telerik.Windows.Data.Selection.SelectionModificationOptions.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.Selection.SelectionModificationOptions.Equals(Telerik.Windows.Data.Selection.SelectionModificationOptions)">
            <summary>
            Equalises the specified other option.
            </summary>
            <param name="otherOption">The other option.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.Selection.SelectionModificationOptions.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.Selection.SelectionModificationOptions.op_Equality(Telerik.Windows.Data.Selection.SelectionModificationOptions,Telerik.Windows.Data.Selection.SelectionModificationOptions)">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="firstOptions">The first options.</param>
            <param name="secondOptions">The second options.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.Selection.SelectionModificationOptions.op_Inequality(Telerik.Windows.Data.Selection.SelectionModificationOptions,Telerik.Windows.Data.Selection.SelectionModificationOptions)">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="firstOptions">The first options.</param>
            <param name="secondOptions">The second options.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="T:Telerik.Windows.Data.StringToObjectConverter">
            <summary>
            IValueConverter that converts strings to objects with the specified in the ConverterParameter type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.StringToObjectConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.StringToObjectConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>
            A converted value. If the method returns null, the valid null value is used.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.TraversalDirection">
            <summary>
            Specifies the direction of tree traversal.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.TraversalDirection.Up">
            <summary>
            Traversing should go up.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.TraversalDirection.Down">
            <summary>
            Traversing should go down.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.PartialTrustAssemblyName">
            <summary>
            Represents a full name of an assembly which can also be obtained in partial trust environments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.PartialTrustAssemblyName.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.PartialTrustAssemblyName"/> class.
            </summary>
            <param name="assemblyFullName">The full name of the assembly to get the name components from.</param>
        </member>
        <member name="P:Telerik.Windows.Data.PartialTrustAssemblyName.Name">
            <summary>
            Gets or sets the simple name of the assembly. This is usually, but not necessarily, 
            the file name of the manifest file of the assembly, minus its extension.
            </summary>
            <value>A string that is the simple name of the assembly..</value>
        </member>
        <member name="P:Telerik.Windows.Data.PartialTrustAssemblyName.Version">
            <summary>
            Gets or sets the major, minor, build, and revision numbers of the assembly.
            </summary>
            <value>A System.Version object representing the major, minor, build, and revision numbers of the assembly.</value>
        </member>
        <member name="P:Telerik.Windows.Data.PartialTrustAssemblyName.CultureInfo">
            <summary>
            Gets or sets the culture supported by the assembly.
            </summary>
            <value>A System.Globalization.CultureInfo object representing the culture supported by the assembly.</value>
        </member>
        <member name="P:Telerik.Windows.Data.PartialTrustAssemblyName.PublicKeyToken">
            <summary>
            Gets the public key token, which is the last 8 bytes of the SHA-1 hash of the public key 
            under which the application or assembly is signed.
            </summary>
            <value>An string containing the public key token.</value>
        </member>
        <member name="T:Telerik.Windows.Data.RelationBase">
            <summary>
            The abstract base class for all relations.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.RelationBase.Name">
            <summary>
            Gets or sets the name of the relation.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Data.RelationBase.IsSelfReference">
            <summary>
            Gets or sets a value indicating whether this instance is self-reference.
            Self-reference relations are used when the child data contains the same objects
            as the master.
            </summary>
            <value>
            	<c>True</c> if this relation is a self-reference one; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.RelationBase.GetDataSource(System.Object,Telerik.Windows.Data.DataItemCollection)">
            <summary>
            Gets the child data source.
            </summary>
            <param name="context">The data context.</param>
            <param name="parentItems">The parent items collection.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Data.DataTableExtensions">
            <summary>
            Provides extension methods which facilitate working with DataTables.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.DataTableExtensions.GetPropertyDescriptorCollection(System.Data.DataTable)">
            <summary>
            Gets the collection of property descriptors representing the columns of the DataTable.
            </summary>
            <param name="table">The table.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataTableExtensions.GetColumnValue(System.Data.DataRowView,System.Data.DataColumn)">
            <summary>
            Gets the value of the cell located in the specified row view and the specified column.
            </summary>
            <param name="rowView">The row view in which the cell is located.</param>
            <param name="column">The column in which the cell is located.</param>
            <returns>The value of the found cell.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.DataTableExtensions.SetColumnValue(System.Data.DataRowView,System.Data.DataColumn,System.Object)">
            <summary>
            Sets the value of the cell located in the specified row view and the specified column.
            </summary>
            <param name="rowView">The row view in which the cell is located.</param>
            <param name="column">The column in which the cell is located.</param>
            <param name="value">The value to set as the new value of the cell.</param>
        </member>
        <member name="M:Telerik.Windows.Data.DataTableExtensions.IsSqlType(System.Data.DataColumn)">
            <summary>
            Determines whether the type corresponding to the specified column is an SQL type.
            </summary>
            <param name="column">The column which type is checked if it is an SQL type.</param>
            <returns>
            	<c>true</c> if the type corresponding to the specified column is an SQL type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.EnumerableDataRowCollection">
            <summary>
            Helper class that mimics EnumerableRowCollection, but exposes the source
            DataTable as public property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.EnumerableDataRowCollection.DataTable">
            <summary>
            Gets the source data table.
            </summary>
            <value>The source data table.</value>
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableDataRowCollection.#ctor(System.Data.DataTable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.EnumerableDataRowCollection"/> class.
            </summary>
            <param name="dataTable">The source data table.</param>
        </member>
        <member name="M:Telerik.Windows.Data.EnumerableDataRowCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.FieldDescriptor">
            <summary>
            Describes a field that wraps a data element from a user data source. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.DataMemberBinding">
            <summary>
            Gets or sets the binding which points to the data member to display in the cells of the <see cref="T:Telerik.Windows.Data.FieldDescriptor"/>.
            </summary>
            <value>The display member binding.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.UniqueName">
            <summary>
            Gets or sets the unique name of the field descriptor.
            </summary>
            <value>The unique name of the field descriptor..</value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.IsReadOnly">
            <summary>
            Gets or sets a value indicating whether the descriptor is read-only.
            </summary>
            <value>
            	<c>True</c> if the descriptor is read-only; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.DataFormatString">
            <summary>
            Gets or sets the string that formats the data contained in the fields being described.
            </summary>
            <value>
            The string that formats the data contained in the fields being described.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.ItemType">
            <summary>
            Gets the type of the item that the field belongs to.
            </summary>
            <value>The type of the item.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.DataType">
            <summary>
            Gets or sets the data type of the fields being described.
            </summary>
            <value>The data type of the fields being described.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.IsDataBound">
            <summary>
            Gets or sets a value indicating whether this instance is data bound.
            </summary>
            <value>
            	<c>True</c> if this instance is data bound; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.IsAutoGenerated">
            <summary>
            Gets or sets a value indicating whether the descriptor is automatically generated.
            </summary>
            <value>
            	<c>True</c> if the descriptor is automatically generated; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptor.IsVisible">
            <summary>
            Gets or sets a value indicating whether the descriptor is visible in a user interface.
            </summary>
            <value>
            	<c>True</c> if the descriptor is visible in a user interface; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.FieldDescriptor.CreateField">
            <summary>
            Creates a field described by the descriptor.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.FieldDescriptor.CanSort">
            <summary>
            Determines whether the data represented by the field descriptor can be sorted.
            </summary>
            <returns>
            	<c>true</c> if the data represented by the field descriptor can be sorted; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.FieldDescriptor.CanGroup">
            <summary>
            Determines whether the data represented by the field descriptor can be sorted.
            </summary>
            <returns>
            	<c>true</c> if the data represented by the field descriptor can be sorted; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.FieldDescriptorNamePairCollection">
            <summary>
            This collection contains pairs of field descriptor names.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateFunction">
            <summary>
            Represents the basic class that supports creating functions that provide statistical information about a set of items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunction.Caption">
            <summary>
            Gets or sets the informative message to display as an illustration of the aggregate function.
            </summary>
            <value>The caption to display as an illustration of the aggregate function.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunction.FunctionName">
            <summary>
            Gets or sets the name of the aggregate function, which appears as a property of the group record on which records the function works.
            </summary>
            <value>The name of the function as visible from the group record.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateFunction.ResultFormatString">
            <summary>
            Gets or sets a string that is used to format the result value.
            </summary>
            <value>The format string.</value>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunction.CreateAggregateExpression(System.Linq.Expressions.Expression)">
            <summary>
            Creates the aggregate expression that is used for constructing expression 
            tree that will calculate the aggregate result.
            </summary>
            <param name="enumerableExpression">The grouping expression.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <summary>
            Merges the specified item into the aggregate result according to the specified action.
            </summary>
            <param name="item">The item.</param>
            <param name="mergeAction">The action.</param>
            <param name="originalAggregateResult">The original aggregate result.</param>
            <param name="group">The group which the item is being added to or removed from.</param>
            <returns>The new value of the aggregate result.</returns>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunction.GenerateFunctionName">
            <summary>
            Generates default name for this function using this type's name.
            </summary>
            <returns>
            Function name generated with the following pattern: 
            {<see cref="M:System.Object.GetType"/>.<see cref="P:System.Reflection.MemberInfo.Name"/>}_{<see cref="M:System.Object.GetHashCode"/>}.
            </returns>
        </member>
        <member name="E:Telerik.Windows.Data.AggregateFunction.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateFunction.OnPropertyChanged(System.String)">
            <summary>
            Raise PropertyChanged Event.
            </summary>
            <param name="propertyName">The property name.</param>
        </member>
        <member name="T:Telerik.Windows.Data.AggregateResult">
            <summary>
            Represents a result returned by an aggregate function.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.AggregateResult.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResult.#ctor(System.Object,System.Int32,Telerik.Windows.Data.AggregateFunction)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.AggregateResult"/> class.
            </summary>
            <param name="value">The value of the result.</param>
            <param name="itemCount">The number of arguments used for the calculation of the result.</param>
            <param name="aggregateFunction">Function that generated the result.</param>
            <exception cref="T:System.ArgumentNullException"><c>aggregateFunction</c> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResult.#ctor(Telerik.Windows.Data.AggregateFunction)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.AggregateResult"/> class.
            </summary>
            <param name="function"><see cref="T:Telerik.Windows.Data.AggregateFunction"/> that generated the result.</param>
            <exception cref="T:System.ArgumentNullException"><c>function</c> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResult.#ctor(System.Object,Telerik.Windows.Data.AggregateFunction)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.AggregateResult"/> class.
            </summary>
            <param name="value">The value of the result.</param>
            <param name="function"><see cref="T:Telerik.Windows.Data.AggregateFunction"/> that generated the result.</param>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResult.Value">
            <summary>
            Gets or sets the value of the result.
            </summary>
            <value>The value of the result.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResult.FormattedValue">
            <summary>
            Gets the formatted value of the result.
            </summary>
            <value>The formatted value of the result.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResult.ItemCount">
            <summary>
            Gets or sets the number of arguments used for the calculation of the result.
            </summary>
            <value>The number of arguments used for the calculation of the result.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResult.Caption">
            <summary>
            Gets or sets the text which serves as a caption for the result in a user interface..
            </summary>
            <value>The text which serves as a caption for the result in a user interface.</value>
        </member>
        <member name="P:Telerik.Windows.Data.AggregateResult.FunctionName">
            <summary>
            Gets the name of the function.
            </summary>
            <value>The name of the function.</value>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResult.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.AggregateResult.OnPropertyChanged(System.String)">
            <summary>
            Called when a property has changed.
            </summary>
            <param name="propertyName">Name of the property.</param>
        </member>
        <member name="T:Telerik.Windows.Data.AverageFunction">
            <summary>
            Represents a function that returns the arithmetic mean of a set of arguments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.AverageFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.AverageFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.AverageFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Average method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.AverageFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.CountFunction">
            <summary>
            Represents a function that returns the number of items in a set of items, including nested sets.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.CountFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.CountFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.CountFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Count method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.CountFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.FirstFunction">
            <summary>
            Represents a function that returns the first item from a set of items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FirstFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FirstFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.FirstFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the First method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.FirstFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.LastFunction">
            <summary>
            Represents a function that returns the last item from a set of items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.LastFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.LastFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.LastFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Last method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.LastFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.MaxFunction">
            <summary>
            Represents a function that returns the greatest item from a set of items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.MaxFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.MaxFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.MaxFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Max method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.MaxFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.MinFunction">
            <summary>
            Represents a function that returns the least item from a set of items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.MinFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.MinFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.MinFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Min method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.MinFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.FieldDescriptorNamePair">
            <summary>
            This class represents a mapping between two fields names.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FieldDescriptorNamePair.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FieldDescriptorNamePair"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.FieldDescriptorNamePair.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.FieldDescriptorNamePair"/> class.
            </summary>
            <param name="parentDescriptorFieldName">Name of the parent descriptor field.</param>
            <param name="childFieldDescriptorName">Name of the child field descriptor.</param>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptorNamePair.ParentFieldDescriptorName">
            <summary>
            Gets or sets the name of the parent field descriptor.
            This property points to the field descriptor from the parent data source in a relation.
            </summary>
            <value>The name of the parent field descriptor.</value>
        </member>
        <member name="P:Telerik.Windows.Data.FieldDescriptorNamePair.ChildFieldDescriptorName">
            <summary>
            Gets or sets the name of the child field descriptor.
            This property points to the field descriptor from the child data source in a relation.
            </summary>
            <value>The name of the child field descriptor.</value>
        </member>
        <member name="T:Telerik.Windows.Data.SumFunction">
            <summary>
            Represents a function that returns the sum of all items from a set of items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.SumFunction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.SumFunction"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.SumFunction.AggregateMethodName">
            <inheritdoc />
            <remarks>
            Gets the the Sum method name.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.SumFunction.Merge(System.Object,Telerik.Windows.Data.AggregateResultMergeAction,Telerik.Windows.Data.AggregateResult,Telerik.Windows.Data.QueryableCollectionViewGroup)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.IHierarchyFilter">
            <summary>
            Exposes methods that filter child collection in hierarchy mode.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.IHierarchyFilter.FilteredCollection">
            <summary>
            Filters the collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.IHierarchyFilter.Initialize(System.Collections.IEnumerable,System.ComponentModel.PropertyDescriptorCollection,System.Collections.Generic.IList{System.Object},System.Collections.Generic.IList{Telerik.Windows.Data.FieldDescriptorNamePair},System.Boolean)">
            <summary>
            Initializes the specified data.
            </summary>
            <param name="data">The data.</param>
            <param name="properties">The properties.</param>
            <param name="masterRecordValues">The master record values.</param>
            <param name="relationFieldNames">The relation field names.</param>
            <param name="isSelfReference">Identifies whether this is a self reference hierarchy.</param>
        </member>
        <member name="T:Telerik.Windows.Data.IRelation">
            <summary>
            This interface is used to connect parent and child table definitions.
            It provides a means for a child table to obtain its data items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IRelation.Name">
            <summary>
            Gets or sets the name of the relation.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IRelation.IsSelfReference">
            <summary>
            Gets or sets a value indicating whether this instance is self-reference.
            Self-reference relations are used when the child data contains the same objects
            as the master.
            </summary>
            <value>
            	<c>True</c> if this relation is a self-reference one; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Windows.Data.PropertyRelation">
            <summary>
            This class represents a relation between a parent object and a collection of child objects that are
            accessible via a property on the master object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.PropertyRelation.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.PropertyRelation"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.PropertyRelation.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.PropertyRelation"/> class.
            </summary>
            <param name="parentPropertyName">Name of the parent property.</param>
        </member>
        <member name="P:Telerik.Windows.Data.PropertyRelation.ParentPropertyName">
            <summary>
            Gets or sets the name of the property on the parent object.
            For example if the parent object is of type Customer and it has an Orders property that contains
            a collection of Order objects, then this property should be set to "Orders".
            </summary>
            <value>The name of the parent property.</value>
        </member>
        <member name="M:Telerik.Windows.Data.PropertyRelation.GetDataSource(System.Object,Telerik.Windows.Data.DataItemCollection)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Data.TableDefinitionCreatedEventArgs">
            <summary>
            Provides data for the event that is fired when a table definition is created.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinitionCreatedEventArgs.#ctor(Telerik.Windows.Data.TableDefinition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.TableDefinitionCreatedEventArgs"/> class.
            </summary>
            <param name="tableDefinition">The table definition which is created.</param>
        </member>
        <member name="P:Telerik.Windows.Data.TableDefinitionCreatedEventArgs.TableDefinition">
            <summary>
            Gets the table definition that is created.
            </summary>
            <value>The table definition that is created.</value>
        </member>
        <member name="T:Telerik.Windows.Data.TableRelation">
            <summary>
            This class represents a relation between two tables. It is similar to relations modeled with
            foreign keys in relational databases.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.TableRelation.FieldNames">
            <summary>
            Gets the field names.
            This collection contains mappings of columns from the master table to columns in the child table.
            </summary>
            <value>The field names.</value>
        </member>
        <member name="M:Telerik.Windows.Data.TableRelation.GetDataSource(System.Object,Telerik.Windows.Data.DataItemCollection)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.DependencyPropertyExtensions.RegisterReadOnly(System.String,System.Type,System.Type,System.Windows.PropertyMetadata)">
            <summary>
            Registers a read-only dependency property with the specified property name, property type, owner type, and property metadata.
            </summary>
            <param name="name">
            The name of the dependency property to register.
            </param>
            <param name="propertyType">
            The type of the property.
            </param>
            <param name="ownerType">
            The owner type that is registering the dependency property.
            </param>
            <param name="metadata">
            Property metadata for the dependency property.
            </param>
            <returns>
            A dependency property identifier that should be used to set the value of a public static readonly field in your class. That identifier is then used to reference the dependency property later, for operations such as setting its value programmatically or obtaining metadata.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.ChangeOperation">
            <summary>
            Represents the different possible operations performed on a set of items.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Inserted">
            <summary>
            Indicates insert operation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Removed">
            <summary>
            Indicates remove operation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Set">
            <summary>
            Indicates that an item is set.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Clearing">
            <summary>
            Indicates that the items will be cleared.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Cleared">
            <summary>
            Indicates that the items are cleared.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Sorting">
            <summary>
            Indicates that the items will be sorted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Data.ChangeOperation.Sorted">
            <summary>
            Indicates that the items are sorted.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.IFieldDescriptor">
            <summary>
            Supports classes that describe fields.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IFieldDescriptor.UniqueName">
            <summary>
            Gets or sets the unique name of the field descriptor.
            </summary>
            <value>The name of the unique.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IFieldDescriptor.IsReadOnly">
            <summary>
            Gets or sets a value indicating whether the descriptor is read-only.
            </summary>
            <value>
            	<c>True</c> if the descriptor is read-only; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.IFieldDescriptor.CreateField">
            <summary>
            Creates a field described by the descriptor.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Data.IDataFieldDescriptor">
            <summary>
            Supports classes that describe fields representing data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.ItemType">
            <summary>
            Gets the type of the item that the field belongs to.
            </summary>
            <value>The type of the item.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.DataType">
            <summary>
            Gets or sets the data type of the fields being described.
            </summary>
            <value>The data type of the fields being described.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.IsAutoGenerated">
            <summary>
            Gets or sets a value indicating whether the descriptor is automatically generated.
            </summary>
            <value>
            	<c>True</c> if the descriptor is automatically generated; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.DataFormatString">
            <summary>
            Gets or sets the string that formats the data contained in the fields being described.
            </summary>
            <value>The string that formats the data contained in the fields being described.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.DataMemberBinding">
            <summary>
            Gets or sets the binding which points to the data member to display in the cells of the <see cref="T:Telerik.Windows.Data.IDataFieldDescriptor"/>.
            </summary>
            <value>The display member binding.</value>
        </member>
        <member name="P:Telerik.Windows.Data.IDataFieldDescriptor.IsVisible">
            <summary>
            Gets or sets a value indicating whether the descriptor is visible in a user interface.
            </summary>
            <value>
            	<c>True</c> if the descriptor is visible in a user interface; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Data.IDataFieldDescriptor.CanSort">
            <summary>
            Determines whether the data represented by the field descriptor can be sorted.
            </summary>
            <returns>
            	<c>True</c> if the data represented by the field descriptor can be sorted; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Data.IDataFieldDescriptor.CanGroup">
            <summary>
            Determines whether the data represented by the field descriptor can be grouped.
            </summary>
            <returns>
            	<c>True</c> if the data represented by the field descriptor can be grouped; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.XmlNodePropertyDescriptor">
            <summary>
            Describes the elements, attributes and other properties of an XML node.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.#ctor(System.Xml.XmlNode)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.XmlNodePropertyDescriptor"/> class.
            </summary>
            <param name="node">The node which properties are described.</param>
        </member>
        <member name="P:Telerik.Windows.Data.XmlNodePropertyDescriptor.ComponentType">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.XmlNodePropertyDescriptor.DisplayName">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.XmlNodePropertyDescriptor.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.XmlNodePropertyDescriptor.PropertyType">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Data.XmlNodePropertyDescriptor.SchemaInfo">
            <summary>
            Gets the schema information of the XML document in which the described node is located.
            </summary>
            <value>The schema info of the XML document in which the described node is located.</value>
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.CanResetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.GetValue(System.Object)">
            <inheritdoc />
            <remarks>
            Gets the current value of the property on a component (an XML node).
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.ResetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.ShouldSerializeValue(System.Object)">
            <inheritdoc />
            <remarks>
            Returns false.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.XmlNodePropertyDescriptor.SetValue(System.Object,System.Object)">
            <inheritdoc />
            <remarks>
            Sets the value of the component (the XML node) to the specified value.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Data.Pair`2">
            <summary>
            Represents an ordered pair of objects.
            </summary>
            <typeparam name="TFirst">The type of the first element of the pair.</typeparam>
            <typeparam name="TSecond">The type of the second element of the pair.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Data.Pair`2.#ctor">
            <summary>
            Initializes a new instance of the Pair class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.Pair`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the Pair class.
            </summary>
            <param name="first">The first element of the pair.</param>
            <param name="second">The second element of the pair.</param>
        </member>
        <member name="P:Telerik.Windows.Data.Pair`2.First">
            <summary>
            Gets or sets the first element of the pair.
            </summary>
            <value>The first element of the pair.</value>
        </member>
        <member name="P:Telerik.Windows.Data.Pair`2.Second">
            <summary>
            Gets or sets the second element of the pair.
            </summary>
            <value>The second element of the pair.</value>
        </member>
        <member name="T:Telerik.Windows.Data.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.RadObservableCollection`1">
            <summary>
            Represents an <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/> that has ability to suspend
            change notification events.
            </summary>
            <typeparam name="T">The type of the items in the collection.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.RadObservableCollection`1"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.RadObservableCollection`1" /> class.
            </summary>
            <param name="shouldResetOnResumeNotifications">Indicates whether RadObservableCollection will raise CollectionChanged 
            event with Reset action, when notifications are resumed.</param>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.RadObservableCollection`1"/> class.
            </summary>
            <param name="collection">The collection from which the elements are copied.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="collection"/> parameter cannot be null.
            </exception>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.RadObservableCollection`1" /> class.
            </summary>
            <param name="shouldResetOnResumeNotifications">Indicates whether RadObservableCollection will raise CollectionChanged 
            event with Reset action, when notifications are resumed.</param>
             /// <param name="collection">The collection from which the elements are copied.</param>
            <exception cref="T:System.ArgumentNullException">
            The <paramref name="collection"/> parameter cannot be null.
            </exception>
        </member>
        <member name="E:Telerik.Windows.Data.RadObservableCollection`1.CollectionChanging">
            <summary>
            Occurs when collection is changing.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.RadObservableCollection`1.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.RadObservableCollection`1.IsDirty">
            <summary>
            Gets or sets a value indicating whether change to the collection is made when
            its notifications are suspended.
            </summary>
            <value><c>true</c> if this instance is has been changed while notifications are
            suspended; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Data.RadObservableCollection`1.ShouldResetOnResumeNotifications">
            <summary>
            Get a value that indicates whether RadObservableCollection 
            would raise CollectionChanged event with Reset action, when a bulk add/remove operation takes place.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Adds the elements of the specified collection to the end of the <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            </summary>
            <param name="items">The items that will be added.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="items"/> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.InsertRange(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Inserts the elements of the specified collection at the specified index.
            </summary>
            <param name="items">The items that will be added.</param>
            <param name="index">The start index.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="items"/> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Removes the elements from the specified collection.
            </summary>
            <param name="items">The items that will be removed.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="items"/> is null.</exception>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.Reset">
            <summary>
            Raises <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged"/> with 
            <see cref="F:System.Collections.Specialized.NotifyCollectionChangedAction.Reset"/> changed action.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
            <remarks>
            Raises the <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged"/> event when
            notifications are not suspended.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <inheritdoc />
            <remarks>
            Raises the <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event when
            notifications are not suspended.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.OnCollectionChanging(Telerik.Windows.Data.CollectionChangingEventArgs)">
            <summary>
            Raises the <see cref="E:CollectionChanging"/> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.Windows.Data.CollectionChangingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.InsertItem(System.Int32,`0)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.RemoveItem(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.ClearItems">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.SuspendNotifications">
            <summary>
            Suspends the notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.ResumeNotifications">
            <summary>
            Resumes the notifications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.RadObservableCollection`1.RaiseCollectionChangedOnResume">
            <summary>
            Raises the CollectionChanged in accordance to the value of ShouldResetOnResumeNotifications and the presence of modified items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.RadObservableCollection`1.NotificationsSuspended">
            <summary>
            Gets a value indicating whether change notifications are suspended.
            </summary>
            <value>
            	<c>True</c> if notifications are suspended, otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Data.RadObservableCollection`1.AddedItems">
            <summary>
            Gets the added items between suspend and resume.
            </summary>
            <value>The added items.</value>
        </member>
        <member name="P:Telerik.Windows.Data.RadObservableCollection`1.RemovedItems">
            <summary>
            Gets the removed items between suspend and resume.
            </summary>
            <value>The removed items.</value>
        </member>
        <member name="T:Telerik.Windows.Data.Strings">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.AppearanceCategory">
            <summary>
              Looks up a localized string similar to Appearance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.BehaviorCategory">
            <summary>
              Looks up a localized string similar to Behavior.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.BrushesCategory">
            <summary>
              Looks up a localized string similar to Brushes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.CommonCategory">
            <summary>
              Looks up a localized string similar to Common Properties.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.ContentCategory">
            <summary>
              Looks up a localized string similar to Content.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.FakeProperty">
            <summary>
              Looks up a localized string similar to Value.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.ItemsSourceInUse">
             <summary>
               Looks up a localized string similar to 
            Simultaneous use of Items and ItemsSource is not allowed.
             </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.LayoutCategory">
            <summary>
              Looks up a localized string similar to Layout.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.MiscCategory">
            <summary>
              Looks up a localized string similar to Misc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.ObservableCollectionReentrancyNotAllowed">
            <summary>
              Looks up a localized string similar to ObservableCollection reentrancy not allowed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.RepeatedGroupDescriptionNotAllowed">
            <summary>
              Looks up a localized string similar to Repeated group description not allowed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.TextCategory">
            <summary>
              Looks up a localized string similar to Text.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.Strings.TransformCategory">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Data.TableDefinition">
            <summary>
            The TableDefinition class defines the data model of a tabular data source. 
            It contains information about the field descriptors(columns) and hierarchy settings.
            This is an abstract class that is used by the data binding logic. Controls such as RadGridView
            inherit and provide their specific implementations.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.TableDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinition.CopyPropertiesFrom(Telerik.Windows.Data.TableDefinition)">
            <summary>
            Copy properties from the source <see cref="T:Telerik.Windows.Data.TableDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinition.CopyChildTableDefinitionsFrom(Telerik.Windows.Data.TableDefinition)">
            <summary>
            Copy child table definitions from the source <see cref="T:Telerik.Windows.Data.TableDefinition"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Data.TableDefinition.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Data.TableDefinition.DataSource">
            <summary>
            Gets or sets the data source for the current table definition.
            </summary>
            <value>The data source.</value>
        </member>
        <member name="P:Telerik.Windows.Data.TableDefinition.Relation">
            <summary>
            Gets or sets the relation.
            Relations are used in hierarchy scenarios to obtain data from the parent data item.
            </summary>
            <value>The relation.</value>
        </member>
        <member name="P:Telerik.Windows.Data.TableDefinition.ChildTableDefinitions">
            <summary>
            Gets the child table definitions.
            This collection contains the child table definitions that define the hierarchy structure.
            </summary>
            <value>The child table definitions.</value>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinition.CreateChildTableDefinition">
            <summary>
            Creates a child table definition of the correct inherited type.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Data.TableDefinitionCollection">
            <summary>
            Represents a collection of table definitions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinitionCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.TableDefinitionCollection"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinitionCollection.#ctor(Telerik.Windows.Data.TableDefinition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.TableDefinitionCollection"/> class.
            </summary>
            <param name="owner">The owner of the definitions in the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Data.TableDefinitionCollection.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Expressions.ExpressionLocation">
            <summary>
            Represents a position within a string representation of an expression.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionLocation.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionLocation"/> class.
            </summary>
            <param name="line">The zero-based number of the line number.</param>
            <param name="column">The zero-based number of the column.</param>
        </member>
        <member name="P:Telerik.Expressions.ExpressionLocation.Line">
            <summary>
            Gets or sets the zero-based line number.
            </summary>
        </member>
        <member name="P:Telerik.Expressions.ExpressionLocation.Column">
            <summary>
            Gets or sets the zero-based column number.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionLocation.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Telerik.Expressions.ExpressionParserError">
            <summary>
            Represents an error that occurs when an expression is parsed.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserError.#ctor(Telerik.Expressions.ExpressionLocation,Telerik.Expressions.ExpressionLocation,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserError"/> class.
            </summary>
            <param name="start">The start location of the error.</param>
            <param name="end">The end location of the error.</param>
            <param name="message">The error message.</param>
        </member>
        <member name="P:Telerik.Expressions.ExpressionParserError.Start">
            <summary>
            Gets the start location of the error message.
            </summary>
        </member>
        <member name="P:Telerik.Expressions.ExpressionParserError.End">
            <summary>
            Gets the end location of the error message.
            </summary>
        </member>
        <member name="P:Telerik.Expressions.ExpressionParserError.Message">
            <summary>
            Gets the error message.
            </summary>
        </member>
        <member name="T:Telerik.Expressions.ExpressionParserException">
            <summary>
            Represents an error that occurs during parsing of an expression.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.#ctor(System.Collections.Generic.IEnumerable{Telerik.Expressions.ExpressionParserError})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserException"/> class.
            </summary>
            <param name="errors"></param>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.ExpressionParserException"/> class.
            </summary>
            <param name="info">The System.Runtime.Serialization.SerializationInfo that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The System.Runtime.Serialization.StreamingContext that contains contextual information about the source or destination.</param>
        </member>
        <member name="P:Telerik.Expressions.ExpressionParserException.Errors">
            <summary>
            Gets the errors that occurred while attempting to parse an expression.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ExpressionParserException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Telerik.Expressions.ExpressionFunctionContext">
            <summary>
            Provides the context for custom functions in the ExpressionParser.
            </summary>
        </member>
        <member name="P:Telerik.Expressions.ExpressionFunctionContext.Context">
            <summary>
            Gets or sets the default expression context class, which will be used for determining the expression functions.
            </summary>
        </member>
        <member name="T:Telerik.Expressions.ExpressionParser">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.OperandType.None">
            <summary>
            No operation.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.OperandType.Scalar">
            <summary>
            Scalar.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.OperandType.Expression">
            <summary>
            Expression.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.NodeType.NoOp">
            <summary>
            No operation node.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.NodeType.UnaryOp">
            <summary>
            Unary operation node.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.NodeType.BinaryOp">
            <summary>
            Binary operation node.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionParser.NodeType.Parentheses">
            <summary>
            A node with an expression in parentheses.
            </summary>
        </member>
        <member name="T:Telerik.Expressions.RadExpressionParser">
            <summary>
            An expression parser that parses a string into an lambda expression.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Expressions.RadExpressionParser"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Expressions.RadExpressionParser.Item">
            <summary>
            The parameter to be used if the expression contains such e.g. "() => parameter.ExampleProperty + 11".
            </summary>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.ParseAsync(System.String)">
            <summary>
            Run a parse operation and returns the resulting lambda expression. 
            </summary>
            <param name="expression">The string expression to be parsed.</param>
            <exception cref="T:Telerik.Expressions.ExpressionParserException">ExpressionParserException.</exception>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.ParseAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Run a parse operation and returns the resulting lambda expression. 
            </summary>
            <param name="expression">The string expression to be parsed.</param>
            <param name="cancellationToken">The cancellation token that will be assigned to the new task.</param>
            <exception cref="T:Telerik.Expressions.ExpressionParserException">ExpressionParserException.</exception>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.Parse(System.String)">
            <summary>
            Converts the string representation of an expression to its <see cref="T:System.Linq.Expressions.LambdaExpression"/> equivalent.
            </summary>
            <param name="expression">The string expression to be parsed.</param>
            <exception cref="T:Telerik.Expressions.ExpressionParserException">ExpressionParserException.</exception>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.TryParse(System.String,System.Linq.Expressions.LambdaExpression@)">
            <summary>
            Converts the string representation of an expression to its <see cref="T:System.Linq.Expressions.LambdaExpression"/> equivalent. 
            A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="expression">A string containing an expression to convert.</param>
            <param name="lambda">When this method returns, contains the <see cref="T:System.Linq.Expressions.LambdaExpression"/> equivalent of the s parameter, 
            if the conversion succeeded, or null if the conversion failed. The conversion fails when the supplied expression contains
            invalid operators or operands;any value originally supplied in result will be overwritten.</param>
            <returns>true if expression was converted successfully; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.RadExpressionParser.Dispose(System.Boolean)">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:Telerik.Expressions.ClrExpressionStringMethods">
            <summary>
            Contains all the string operations available in RadExpressionEditor.
            </summary>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.ConvertToString(System.Object)">
            <summary>
            Returns a string representation of an object.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Substring(System.String,System.Int32,System.Int32)">
            <summary>
            Retrieves a value from a string. The value starts at a specified character position and has a specified length.
            </summary>
            <param name="text"></param>
            <param name="startIndex"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Concat(System.String,System.String)">
            <summary>
            Concatenates two specified instances of System.String.
            </summary>
            <param name="value1"></param>
            <param name="value2"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Format(System.String,System.Object)">
            <summary>
            Replaces the format item in a specified System.String with the text equivalent of the value of a specified System.Object instance.
            </summary>
            <param name="formatValue"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Trim(System.String)">
            <summary>
            Removes all occurrences of white space characters from the beginning and end of the string.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Len(System.String)">
            <summary>
            Gets the number of characters in a string.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.IndexOf(System.String,System.String)">
            <summary>
            Reports the index of the first occurrence of the specified string in this instance.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.LastIndexOf(System.String,System.String)">
            <summary>
            Reports the index position of the last occurrence of a specified string within this instance.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Insert(System.String,System.Int32,System.String)">
            <summary>
            Inserts String2 into String1 at the position specified by StartPosition.
            </summary>
            <param name="text"></param>
            <param name="startPosition"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Lower(System.String)">
            <summary>
            Returns the String in lowercase.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Upper(System.String)">
            <summary>
            Returns String in uppercase.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.LowerInvariant(System.String)">
            <summary>
            Returns a copy of this System.String object converted to lowercase using the casing rules of the invariant culture.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.UpperInvariant(System.String)">
            <summary>
            Returns a copy of this System.String object converted to uppercase using the casing rules of the invariant culture.
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.PadLeft(System.String,System.Int32)">
            <summary>
            Left-aligns characters in the defined string, padding its left side with white space characters up to a specified total length.
            </summary>
            <param name="text"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.PadRight(System.String,System.Int32)">
            <summary>
            Right-aligns characters in the defined string, padding its left side with white space characters up to a specified total length.
            </summary>
            <param name="text"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Remove(System.String,System.Int32,System.Int32)">
            <summary>
            Deletes a specified number of characters from this instance, beginning at a specified position.
            </summary>
            <param name="text"></param>
            <param name="startPosition"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Replace(System.String,System.String,System.String)">
            <summary>
            Returns a copy of String1, in which value2 has been replaced with String3.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <param name="newValue"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.StartsWith(System.String,System.String)">
            <summary>
            Determines whether the beginning of this string instance matches the specified string.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.EndsWith(System.String,System.String)">
            <summary>
            Determines whether the end of this string instance matches the specified string.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Expressions.ClrExpressionStringMethods.Contains(System.String,System.String)">
            <summary>
            Returns a value indicating whether the specified System.String object occurs within this string.
            </summary>
            <param name="text"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Add">
            <summary>
            Add.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.And">
            <summary>
            And.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Divide">
            <summary>
            Divide.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Equal">
            <summary>
            Equal.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.GreaterThan">
            <summary>
            GreaterThan.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.GreaterThanOrEqual">
            <summary>
            GreaterThanOrEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.LessThan">
            <summary>
            LessThan.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.LessThanOrEqual">
            <summary>
            LessThanOrEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Modulo">
            <summary>
            Modulo.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Multiply">
            <summary>
            Multiply.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Negate">
            <summary>
            Negate.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Not">
            <summary>
            Not.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.NotEqual">
            <summary>
            NotEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Or">
            <summary>
            Or.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Subtract">
            <summary>
            Subtract.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.Procedure">
            <summary>
            Procedure.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.None">
            <summary>
            No operation.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperator.UnaryPlus">
            <summary>
            Unary plus.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Additive">
            <summary>
            Additive.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Equality">
            <summary>
            Equality.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Logical">
            <summary>
            Logical.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Multiplicative">
            <summary>
            Multiplicative.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Relational">
            <summary>
            Relational.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Unary">
            <summary>
            Unary.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.Procedure">
            <summary>
            Procedure.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeOperatorCategory.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Binary">
            <summary>
            Binary.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Constant">
            <summary>
            Constant.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Function">
            <summary>
            Function.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Index">
            <summary>
            Index.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Member">
            <summary>
            Member.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.ExpressionNodeType.Unary">
            <summary>
            Unary.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Paren">
            <summary>
            Paren.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.CloseParen">
            <summary>
            CloseParen.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Bracket">
            <summary>
            Bracket.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.CloseBracket">
            <summary>
            CloseBracket.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Comma">
            <summary>
            Comma.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Eof">
            <summary>
            Eof.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Null">
            <summary>
            Null.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.True">
            <summary>
            True.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.False">
            <summary>
            False.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.And">
            <summary>
            And.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Or">
            <summary>
            Or.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Not">
            <summary>
            Not.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Dot">
            <summary>
            Dot.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Plus">
            <summary>
            Plus.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Minus">
            <summary>
            Minus.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Percent">
            <summary>
            Percent.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Multiply">
            <summary>
            Multiply.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Divide">
            <summary>
            Divide.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Equal">
            <summary>
            Equal.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.NotEqual">
            <summary>
            NotEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.GreaterThan">
            <summary>
            GreaterThan.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.GreaterThanOrEqual">
            <summary>
            GreaterThanOrEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.LessThan">
            <summary>
            LessThan.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.LessThanOrEqual">
            <summary>
            LessThanOrEqual.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Literal">
            <summary>
            Literal.
            </summary>
        </member>
        <member name="F:Telerik.Expressions.TokenType.Identifier">
            <summary>
            Identifier.
            </summary>
        </member>
    </members>
</doc>
