﻿<Window x:Class="Everylang.App.View.Controls.SpellCheck.SpellCheckWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:richTextBoxEx1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
        xmlns:formatters="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
        Height="350" Width="500" MinHeight="200" MinWidth="300" Topmost="True" WindowStyle="None" AllowsTransparency="True"
        x:Name="me"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResizeWithGrip" ShowInTaskbar="False" Focusable="False"
        Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}" Closing="window_Closing"
        DataContext="{Binding ElementName=me}">
    <Window.Resources>
        <ResourceDictionary>
            <Style x:Key="ImageButton" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Focusable" Value="False" />
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Padding" Value="3" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="MinHeight" Value="0" />
            </Style>
            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                                    Height="14"
                                                    Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ProgressRingStyle" TargetType="telerik:RadBusyIndicator" BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="IsBusy" Value="{Binding IsVisibleProgressRing}"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleProgressRing}" Value="False">
                        <Setter Property="Visibility" Value="Collapsed" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleProgressRing}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="LabelOkStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Visibility" Value="Collapsed" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsOk}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>

            </Style>

            <Style x:Key="LabelErrorStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Visibility" Value="Collapsed" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Error}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>

            </Style>

            <Style x:Key="LabelErrorTooLongTextStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Visibility" Value="Collapsed" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding ErrorTooLongText}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ButtonOkStyle" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Visibility" Value="Collapsed" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsOk}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Error}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding ErrorTooLongText}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="GridVisibility" TargetType="Grid">
                <Setter Property="Visibility" Value="Collapsed" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleGrid}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </Window.Resources>
    <Border BorderThickness="2" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" CornerRadius="0">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="28" />
                <RowDefinition Height="175*" />
            </Grid.RowDefinitions>
            <Border MouseLeftButtonDown="BorderMouseLeftButtonDown" />
            <telerik:Label Content="{telerik:LocalizableResource Key=SpellCheckHeader}" Grid.Row="0" FontSize="13"
                           FontWeight="Bold" MouseLeftButtonDown="BorderMouseLeftButtonDown" />
            <telerik:RadButton Grid.Row="0" Style="{StaticResource ImageButtonClose}" Click="ButtonClickClose"
                               ToolTip="{telerik:LocalizableResource Key=CloseHeaderButton}" IsCancel="True"
                               HorizontalAlignment="Right" Margin="0,0,5,0" />
            <telerik:RadBusyIndicator Style="{StaticResource ProgressRingStyle}" Grid.Row="1" BusyContent="" Grid.ZIndex="1" IsIndeterminate="True"/>
            <telerik:Label Content="{telerik:LocalizableResource Key=LabelNoErrors}" Grid.Row="1"
                           Style="{StaticResource LabelOkStyle}" VerticalAlignment="Top" Margin="0,18,0,0" />
            <telerik:Label Content="{telerik:LocalizableResource Key=LabelError}" Grid.Row="1"
                           Style="{StaticResource LabelErrorStyle}" VerticalAlignment="Top" Margin="0,18,0,0" />
            <telerik:Label Content="{telerik:LocalizableResource Key=LabelTextTooLong}" Grid.Row="1"
                           Style="{StaticResource LabelErrorTooLongTextStyle}" VerticalAlignment="Top"
                           Margin="0,18,0,0" />
            <telerik:RadButton Content="{telerik:LocalizableResource Key=ButtonClose}" Grid.Row="1" MinHeight="0" Padding="5"
                               VerticalAlignment="Bottom" Margin="0,0,0,5" Style="{StaticResource ButtonOkStyle}"
                               Click="ButtonClickClose" IsCancel="True" />
            <Grid Grid.Row="1" Style="{StaticResource GridVisibility}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="32" />
                    <RowDefinition Height="50*" />
                    <RowDefinition Height="50*" />
                    <RowDefinition Height="35" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="60*" />
                    <ColumnDefinition Width="120" />
                </Grid.ColumnDefinitions>

                <TextBox Name="TextBoxCurrentWord" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" MinHeight="0"
                         Text="{Binding CurrentWord}" FontSize="13" TextChanged="textBoxCurrentWord_TextChanged"
                         Margin="2,0,2,0" BorderThickness="0" />
                <richTextBoxEx1:RichTextBoxEx BorderThickness="0" x:Name="SpellTextBox" IsReadOnly="True"
                                             Text="{Binding Path=SourceText}" Grid.Row="1" Grid.Column="0" 
                                             TextChanged="RichTextBoxSourceText_OnTextChanged"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             FontSize="13"
                                             Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                                             Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                             Block.LineHeight="1" Margin="2,2,0,0">
                    <richTextBoxEx1:RichTextBoxEx.TextFormatter>
                        <formatters:PlainTextFormatter />
                    </richTextBoxEx1:RichTextBoxEx.TextFormatter>
                </richTextBoxEx1:RichTextBoxEx>
                <Grid Grid.Row="2" Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="28" />
                        <RowDefinition Height="50*" />
                    </Grid.RowDefinitions>
                    <telerik:Label Name="LabelOptions" Grid.Row="0"
                                   Content="{telerik:LocalizableResource Key=LabelOptions}" FontSize="14" />
                    <telerik:RadListBox Name="ListBoxTrueWordItems" Grid.Row="1" FontSize="13"
                                        SelectionChanged="listBoxTrueWordItems_SelectionChanged" Margin="2,0,0,0">
                        <telerik:RadListBox.ItemContainerStyle>
                            <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                                <Setter Property="Padding" Value="13,3,3,3" />
                                <Setter Property="Margin" Value="5,0,0,0" />
                                <Setter Property="MinHeight" Value="0" />
                                <Setter Property="Height" Value="28" />
                            </Style>
                        </telerik:RadListBox.ItemContainerStyle>
                    </telerik:RadListBox>
                </Grid>
                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,0">
                    <Button Focusable="False" Name="BSkip" Content="{telerik:LocalizableResource Key=bSkip}"
                            Height="33" Click="bSkip_Click" MinHeight="0" Padding="3" />
                    <Button Focusable="False" Name="BSkipAll" Content="{telerik:LocalizableResource Key=bSkipAll}"
                            Margin="0,5,0,0" Height="33" Click="bSkipAll_Click" MinHeight="0" Padding="3" />
                </StackPanel>

                <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,25,0,0">
                    <Button Focusable="False" Name="BReplace" Content="{telerik:LocalizableResource Key=bReplace}"
                            Height="33" Click="bReplace_Click" MinHeight="0" Padding="3" />
                    <Button Focusable="False" Name="BReplaceAll"
                            Content="{telerik:LocalizableResource Key=bReplaceAll}" Margin="0,5,0,0" Height="33"
                            Click="bReplaceAll_Click" MinHeight="0" Padding="3" />
                </StackPanel>

                <WrapPanel Grid.Row="3" Grid.Column="0" Margin="0,3,0,0" Grid.ColumnSpan="2"
                           HorizontalAlignment="Right">
                    <Button Focusable="False" Content="{telerik:LocalizableResource Key=bReplaceText}" Height="30"
                            Margin="0,0,5,0" Width="120" Click="ButtonPlace" MinHeight="0" Padding="3" />
                    <Button Focusable="False" Content="{telerik:LocalizableResource Key=bCopy}" Height="30"
                            Margin="0,0,5,0" Width="120" Click="ButtonCopy" MinHeight="0" Padding="3" />
                    <Button Focusable="False" Name="ButtonBack" Content="{telerik:LocalizableResource Key=buttonBack}"
                            Height="30" Margin="0,0,5,0" Width="120" Click="ButtonBackEvent" IsEnabled="False"
                            MinHeight="0" Padding="3" />
                    <Button Focusable="False" Content="{telerik:LocalizableResource Key=ButtonClose}" Height="30"
                            Width="97" Click="ButtonClickClose" IsCancel="True" MinHeight="0" Margin="0,0,15,0" Padding="3" />
                </WrapPanel>
            </Grid>
        </Grid>
    </Border>
</Window>