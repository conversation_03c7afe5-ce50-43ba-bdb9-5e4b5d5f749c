﻿using System;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;

namespace Everylang.App.Utilities
{
    class HideFormAltTab
    {
        private const int WS_EX_TOOLWINDOW = 0x00000080;

        public static void Hide(Window window)
        {
            IntPtr handle = new WindowInteropHelper(window).Handle;
            User32.SetWindowLong(handle, User32.WindowLongFlags.GWL_EXSTYLE, User32.GetWindowLong(handle, User32.WindowLongFlags.GWL_EXSTYLE) | WS_EX_TOOLWINDOW);
        }
    }
}
