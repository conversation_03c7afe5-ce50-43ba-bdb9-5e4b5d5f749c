﻿<telerik:RadWindow x:Class="Everylang.App.View.Controls.Common.ActionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:navigation="clr-namespace:Telerik.Windows.Controls.Navigation;assembly=Telerik.Windows.Controls.Navigation"
        xmlns:common="clr-namespace:Everylang.App.View.Controls.Common"
        Height="300" Width="300" CanMove="False" ResizeMode="NoResize"
        navigation:RadWindowInteropHelper.ShowInTaskbar="True"
        navigation:RadWindowInteropHelper.AllowTransparency="True"
        telerik:WindowHeaderPanel.ContainerType="None" CanClose="False" CaptionHeight="0" x:ClassModifier="internal">
    <telerik:RadWindow.Style>
        <Style BasedOn="{StaticResource RadWindowStyle}" TargetType="common:ActionWindow" />
    </telerik:RadWindow.Style>
    <Grid Name="GridMain">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <Button Grid.Row="1" HorizontalAlignment="Stretch" VerticalAlignment="Bottom" Content="{telerik:LocalizableResource Key=ButtonClose}" Click="CloseClick"/>
    </Grid>
</telerik:RadWindow>