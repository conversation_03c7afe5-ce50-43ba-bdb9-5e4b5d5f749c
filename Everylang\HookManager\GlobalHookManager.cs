﻿namespace Everylang.App.HookManager
{
    internal static class GlobalHookManager
    {
        internal static void Start()
        {
            GlobalShellHook.Start();
            GlobalLangChangeHook.Start();
            CommonHookListener.Start();
        }

        internal static void Stop()
        {
            GlobalShellHook.Stop();
            GlobalLangChangeHook.Stop();
            CommonHookListener.Stop();
        }
    }
}
