<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Pivot.DataProviders.Adomd</name>
    </assembly>
    <members>
        <member name="M:Telerik.Pivot.Adomd.AdomdMetadataLoader.LoadData">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdPivotSerializationHelper">
            <summary>
            Provides all knownTypes necessary for serializing <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/>.
            Use this class to extract these knownTypes and concatenate them with your knownTypes, so you can pass them to <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> for example.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdPivotSerializationHelper.KnownTypes">
            <summary>
            Gets known types in <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/> to use with serializer such as <see cref="T:System.Runtime.Serialization.DataContractSerializer"/>.
            </summary>
            <returns>An enumeration with the known serializable classes for the <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/> <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Adomd.DefaultAdomdClient.GetMeasureGroupsAndCaptions(System.String,System.String)">
            <summary>
            Gets MeasureGroups from the specified connection.
            </summary>
            <param name="connectionString">The connectionString used by AdomdDataProvider to connect to OLAP Cube.</param>
            <param name="mdxQuery">Query that will be executed on OLAP Server and will return MeasureGroupsAndMeasureCaptions.</param>
            <returns>Dictionary with MEASUREGROUP_NAME as key and MEASUREGROUP_CAPTION as value. If a caption for MEASUREGROUP_NAME doesn't exist in the returned xml, whole pair is skipped.</returns>
        </member>
        <member name="M:Telerik.Pivot.Adomd.DefaultAdomdClient.FixKeysXmlReader(System.Xml.XmlReader,System.IO.MemoryStream)">
            <summary>
            HACK!!! Used to replace Key tags in xml with Key0, Key1 etc.
            </summary>
            <param name="xmlReader">Reader from the input xml.</param>
            <param name="memoryStream">MemoryStream to write to.</param>
            <returns>Reader from the changed xml.</returns>
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdAggregateDescription">
            <summary>
            Used to specify aggregation parameters for <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdAggregateDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdFilterDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdFilterDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdGroupDescription">
            <summary>
            Used to specify grouping parameters for <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdLevelFilterDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdLevelFilterDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Adomd.AdomdLevelFilterDescription" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdLevelFilterDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdLevelGroupDescription">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdLevelGroupDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Adomd.AdomdLevelGroupDescription" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdLevelGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdDataProvider">
            <summary>
            Provides Cube data access and operations using Adomd.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.FilterDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Core.FilterDescription"/> that specified how the pivot items should be filtered.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.RowGroupDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Adomd.AdomdGroupDescription"/> that specified how the pivot should be grouped by rows.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.ColumnGroupDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Adomd.AdomdGroupDescription"/> that specified how the pivot should be grouped by columns.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.AggregateDescriptions">
            <summary>
            A list of <see cref="T:Telerik.Pivot.Adomd.AdomdAggregateDescription"/> that specified how the pivot should be aggregated for the groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.Results">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.ConnectionSettings">
            <summary>
            Gets or sets the connection settings that are used for establishing connection to the data server.
            </summary>
            <value>The connection settings.</value>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdDataProvider.State">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.RefreshOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.BlockUntilRefreshCompletes">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.CreateFieldDescriptionsProvider">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.GetAggregateDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.GetGroupDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.SetAggregateFunctionToAggregateDescription(Telerik.Pivot.Core.IAggregateDescription,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdDataProvider.GetFilterDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdFieldDescriptionProvider">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> implementation for Adomd sources.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdFieldDescriptionProvider.#ctor(Telerik.Pivot.Adomd.AdomdConnectionSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Adomd.AdomdFieldDescriptionProvider" /> class.
            </summary>
            <param name="connectionSettings">The connection settings.</param>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdFieldDescriptionProvider.GetLoader">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Adomd.AdomdConnectionSettings">
            <summary>
            Connection setting class used by <see cref="T:Telerik.Pivot.Adomd.AdomdDataProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdConnectionSettings.Cube">
            <summary>
            Gets or sets the name of the cube that will be used.
            </summary>
            <value>Cube name.</value>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdConnectionSettings.Database">
            <summary>
            Gets or sets the database to connect to.
            </summary>
            <value>Database name.</value>
        </member>
        <member name="P:Telerik.Pivot.Adomd.AdomdConnectionSettings.ConnectionString">
            <summary>
            Gets or sets the connection string (OLE DB connection string format).
            </summary>
            <value>The connection string.</value>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdConnectionSettings.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdConnectionSettings.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdConnectionSettings.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdConnectionSettings.op_Equality(Telerik.Pivot.Adomd.AdomdConnectionSettings,Telerik.Pivot.Adomd.AdomdConnectionSettings)">
            <summary>
            Compares two instances for equality.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>True, if instances are equal.</returns>
        </member>
        <member name="M:Telerik.Pivot.Adomd.AdomdConnectionSettings.op_Inequality(Telerik.Pivot.Adomd.AdomdConnectionSettings,Telerik.Pivot.Adomd.AdomdConnectionSettings)">
            <summary>
            Compares two instances for non-equality.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>True, if instances are not equal.</returns>
        </member>
    </members>
</doc>
