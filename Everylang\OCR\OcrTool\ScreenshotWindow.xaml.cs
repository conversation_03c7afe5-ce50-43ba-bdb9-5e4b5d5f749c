﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using MouseEventArgs = System.Windows.Input.MouseEventArgs;
using Point = System.Windows.Point;
using Size = System.Drawing.Size;

namespace Everylang.App.OCR.OcrTool;

internal partial class ScreenshotWindow
{
    internal event Action<Bitmap>? BitmapRegionAction;

    private readonly Bitmap _bitmap;
    private readonly double _dpiScale;
    private bool _mouseDown;
    private Rect _rectangle;
    private Point _startPoint;

    internal ScreenshotWindow(Rect bounds, int x = 0, int y = 0, int width = 0, int height = 0, double dpiScale = 1)
    {
        InitializeComponent();

        _dpiScale = dpiScale;
        Top = bounds.X;
        Left = bounds.Y;
        Width = bounds.Width;
        Height = bounds.Height;

        Canvas.SetLeft(this, bounds.X);
        Canvas.SetTop(this, bounds.Y);
        LeftMask.Width = bounds.Width;
        LeftMask.Height = bounds.Height;

        _bitmap = new Bitmap(width, height);
        using (var g = Graphics.FromImage(_bitmap))
        {
            g.CopyFromScreen(x, y, 0, 0, new Size(width, height), CopyPixelOperation.SourceCopy);
        }

        Background = Utils.BitmapToImageBrush(_bitmap);
    }

    private void Window_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.KeyStates == Keyboard.GetKeyStates(Key.Escape))
            BitmapRegionAction?.Invoke(null);
    }

    private void Window_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        Close();
    }

    private void Window_MouseMove(object sender, MouseEventArgs e)
    {
        if (_mouseDown && this.IsMouseOver)
        {
            var currentPoint = Mouse.GetPosition(this);
            _rectangle = new Rect(_startPoint, currentPoint);

            Canvas.SetLeft(LeftMask, 0);
            Canvas.SetTop(LeftMask, 0);
            LeftMask.Width = _rectangle.X;
            LeftMask.Height = _bitmap.Height;

            Canvas.SetLeft(RightMask, _rectangle.Left + _rectangle.Width);
            Canvas.SetTop(RightMask, 0);
            RightMask.Width = _bitmap.Width - _rectangle.Left - _rectangle.Width;
            RightMask.Height = _bitmap.Height;

            Canvas.SetLeft(UpMask, _rectangle.Left);
            Canvas.SetTop(UpMask, 0);
            UpMask.Width = _rectangle.Width;
            UpMask.Height = _rectangle.Y;

            Canvas.SetLeft(DownMask, _rectangle.Left);
            Canvas.SetTop(DownMask, _rectangle.Y + _rectangle.Height);
            DownMask.Width = _rectangle.Width;
            DownMask.Height = _bitmap.Height - _rectangle.Height - _rectangle.Y;


            Canvas.SetLeft(SelectionBorder, _startPoint.X);
            Canvas.SetTop(SelectionBorder, _startPoint.Y);

            SelectionBorder.Width = Math.Abs(currentPoint.X - _startPoint.X);
            SelectionBorder.Height = Math.Abs(currentPoint.Y - _startPoint.Y);
            UpdateLayout();
        }
    }

    private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (this.IsMouseOver)
        {
            _mouseDown = true;
            _startPoint = Mouse.GetPosition(this);
        }
    }

    private void Window_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (this.IsMouseOver)
        {
            _mouseDown = false;

            if (_rectangle.Width > 1 || _rectangle.Height > 1)
            {
                var x = (int)(_rectangle.X * _dpiScale);
                var y = (int)(_rectangle.Y * _dpiScale);
                var width = (int)(_rectangle.Width * _dpiScale);
                var height = (int)(_rectangle.Height * _dpiScale);
                if (width <= 0 || height <= 0) return;
                var bmpOut = new Bitmap(width, height, PixelFormat.Format32bppArgb);
                var g = Graphics.FromImage(bmpOut);
                g.DrawImage(_bitmap,
                    new Rectangle(0, 0, width, height),
                    new Rectangle(x, y, width, height),
                    GraphicsUnit.Pixel);
                BitmapRegionAction?.Invoke(bmpOut);
            }
        }
    }



    private void Window_Closed(object sender, EventArgs e)
    {
        Utils.FlushMemory();
    }
}
