﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.SettingsApp;
using Everylang.App.ViewModels;
using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using Telerik.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Common.CommonWindow
{
    /// <summary>
    /// Interaction logic for FastActionDiary.xaml
    /// </summary>
    internal partial class FastActionDiary : IFastActionComponent
    {
        private readonly ICollectionView? _collectionView;
        private string? _selectedText;
        private int _currentSelected;
        internal FastActionDiary()
        {
            InitializeComponent();
            if (!string.IsNullOrEmpty(SettingsManager.Settings.DiaryPassword) && (DateTime.Now - VMContainer.Instance.DiaryViewModel.LastPasswordRequest).TotalMinutes > 10)
            {
                GridPassword.Visibility = Visibility.Visible;
                lvFastAction.Visibility = Visibility.Collapsed;
            }
            lvFastAction.ItemsSource = VMContainer.Instance.DiaryViewModel.AllDiaryItems;
            _collectionView = CollectionViewSource.GetDefaultView(VMContainer.Instance.DiaryViewModel.AllDiaryItems);
        }

        private void LvFastAction_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            Replace();
        }

        private void TextBlockSelectionChanged(string selectedtext)
        {
            _selectedText = selectedtext;
        }

        public void Replace()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                string replacedText = "";
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    replacedText += (lvFastAction.SelectedItems[i] as DiaryDataModel)?.Text;
                    if (i != lvFastAction.SelectedItems.Count - 1)
                    {
                        replacedText += Environment.NewLine;
                    }
                }
                FastActionCommonWindow.Instance?.Hide();
                Utilities.SendText.SendStringByPaste(replacedText, false);
            }
        }

        public void Replace(int i)
        {
            DiaryDataModel diaryDataModel = VMContainer.Instance.DiaryViewModel.AllDiaryItems[i];
            FastActionCommonWindow.Instance?.Hide();
            Utilities.SendText.SendStringByPaste(diaryDataModel.Text, false);
        }

        public void Copy()
        {
            if (!string.IsNullOrEmpty(_selectedText))
            {
                ClipboardOperations.SetTextWithoutHistory(_selectedText);
            }
            else
            {
                if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
                {
                    string replacedText = "";
                    for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                    {
                        replacedText += (lvFastAction.SelectedItems[i] as DiaryDataModel)?.Text;
                        if (i != lvFastAction.SelectedItems.Count - 1)
                        {
                            replacedText += Environment.NewLine;
                        }
                    }
                    ClipboardOperations.SetTextWithoutHistory(replacedText);
                }
            }
        }

        public void SelectAll()
        {

        }

        public void Delete()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                VMContainer.Instance.DiaryViewModel.SelectedItems.Clear();
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    var model = lvFastAction.SelectedItems[i] as DiaryDataModel;
                    if (model != null) VMContainer.Instance.DiaryViewModel.SelectedItems.Add(model);
                }
                VMContainer.Instance.DiaryViewModel.DeleteSelected(null);
            }
        }

        public void Init()
        {
            lvFastAction.Focus();
            lvFastAction.SelectedIndex = 0;
            _currentSelected = 0;
        }

        public void FindText(string text)
        {
            if (_collectionView != null)
            {
                _collectionView.Filter = null;

                _collectionView.Filter = item =>
                {
                    var s = (item as DiaryDataModel)?.Text;
                    return s != null && s.ToLower().Contains(text.ToLower());
                };
            }

            if (lvFastAction.Items.Count > 0)
            {
                lvFastAction.SelectedItem = lvFastAction.Items[0];
            }
            _currentSelected = 0;
        }

        public void Close()
        {
            lvFastAction.ItemsSource = null;
            if (_collectionView != null) _collectionView.Filter = null;
        }

        public void SelectNext(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == lvFastAction.Items.Count - 1)
            {
                _currentSelected = 0;
            }
            else
            {
                _currentSelected += 1;
            }
            if (shift)
            {
                var clipItem = lvFastAction.Items[_currentSelected];
                if (!lvFastAction.SelectedItems.Contains(clipItem))
                {
                    lvFastAction.SelectedItems.Add(clipItem);
                    lvFastAction.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        lvFastAction.SelectedItems.Remove(lvFastAction.Items[oldSelIndex]);
                        lvFastAction.ScrollIntoView(lvFastAction.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                lvFastAction.SelectedIndex = _currentSelected;
                lvFastAction.ScrollIntoView(_currentSelected);
            }
        }

        public void SelectPrev(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == 0)
            {
                _currentSelected = lvFastAction.Items.Count - 1;
            }
            else
            {
                _currentSelected -= 1;
            }
            if (shift)
            {
                var clipItem = lvFastAction.Items[_currentSelected];
                if (!lvFastAction.SelectedItems.Contains(clipItem))
                {
                    lvFastAction.SelectedItems.Add(clipItem);
                    lvFastAction.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        lvFastAction.SelectedItems.Remove(lvFastAction.Items[oldSelIndex]);
                        lvFastAction.ScrollIntoView(lvFastAction.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                lvFastAction.SelectedIndex = _currentSelected;
                lvFastAction.ScrollIntoView(_currentSelected);
            }
        }

        private RadContextMenu? _contextMenu;

        private void OpenMenuMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (lvFastAction.SelectedItem == null)
            {
                return;
            }
            _contextMenu = new RadContextMenu();
            _contextMenu.Closed += (_, _) =>
            {
                if (FastActionCommonWindow.Instance != null) FastActionCommonWindow.Instance.MenuIsOpen = false;
            };

            RadMenuItem itemPastButton = new RadMenuItem();
            itemPastButton.Header = LocalizationManager.GetString("PastButton");
            itemPastButton.PreviewMouseDown += RadMenuItemPaste;
            _contextMenu.Items.Add(itemPastButton);

            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("CopyButton");
            itemCopy.Click += RadMenuItemCopyClick;
            _contextMenu.Items.Add(itemCopy);

            RadMenuItem itemCopyToSnippets = new RadMenuItem();
            itemCopyToSnippets.Header = LocalizationManager.GetString("ToReplacerButton");
            itemCopyToSnippets.PreviewMouseDown += RadMenuItemToSnippets;
            _contextMenu.Items.Add(itemCopyToSnippets);

            _contextMenu.PlacementTarget = (UIElement)sender;
            if (FastActionCommonWindow.Instance != null) FastActionCommonWindow.Instance.MenuIsOpen = true;
            _contextMenu.IsOpen = true;
        }

        private void RadMenuItemPaste(object sender, MouseButtonEventArgs mouseButtonEventArgs)
        {
            Replace();
        }

        private void RadMenuItemToSnippets(object sender, MouseButtonEventArgs e)
        {
            if (lvFastAction.SelectedItem == null) return;
            var text = ((DiaryDataModel)(lvFastAction.SelectedItem)).Text;
            FastActionCommonWindow.Instance?.Hide();
            if (text != null) GlobalEventsApp.OnEventAddNewSnippets(text);
        }

        private void RadMenuItemCopyClick(object sender, RadRoutedEventArgs radRoutedEventArgs)
        {
            Copy();
        }

        private void DiaryPasswordBoxMu_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            if (PasswordBoxMu.Password == SettingsManager.Settings.DiaryPassword)
            {
                GridPassword.Visibility = Visibility.Collapsed;
                lvFastAction.Visibility = Visibility.Visible;
                VMContainer.Instance.DiaryViewModel.LastPasswordRequest = DateTime.Now;
            }
        }

        private async void FastActionDiary_OnIsVisibleChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (IsVisible && GridPassword.Visibility == Visibility.Visible)
            {
                await Task.Delay(100);
                PasswordBoxMu.Focus();
            }
        }
    }
}
