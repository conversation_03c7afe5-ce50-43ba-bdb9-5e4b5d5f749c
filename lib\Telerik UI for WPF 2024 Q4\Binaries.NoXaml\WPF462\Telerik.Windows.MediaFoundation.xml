<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.MediaFoundation</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.MediaFoundation.BitmapFileHeader">
            <summary>
            Holds the file headers of a bitmap file.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.BitmapFileHeader.Type">
            <summary>
            The file type; must be BM.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.BitmapFileHeader.Size">
            <summary>
            The size, in bytes, of the bitmap file.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.BitmapFileHeader.Reserved1">
            <summary>
            Reserved; must be zero.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.BitmapFileHeader.Reserved2">
            <summary>
            Reserved; must be zero.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.BitmapFileHeader.OffBits">
            <summary>
            The offset, in bytes, from the beginning of the BITMAPFILEHEADER structure to the bitmap bits.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFoundationDeviceType">
            <summary>
            Represents the hardware type of a device capable of capturing media.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MediaFoundationDeviceType.AudioCapture">
            <summary>
            A device that is capable of capturing audio.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MediaFoundationDeviceType.VideoCapture">
            <summary>
            A device that is capable of capturing video.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.FrameReadyEventHandler">
            <summary>
            Represents a delegate that is called when a frame is ready for drawing.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.FrameEventArgs">
            <summary>
            Holds information of a single frame.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.FrameEventArgs.#ctor(MediaFoundation.IMFMediaBuffer,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.FrameEventArgs"/> class.
            </summary>
            <param name="buffer">The buffer containing the frame data.</param>
            <param name="width">The frame width.</param>
            <param name="height">The frame height.</param>
            <param name="stride">The frame stride.</param>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.FrameEventArgs.Buffer">
            <summary>
            Gets the <see cref="T:MediaFoundation.IMFMediaBuffer"/> containing the frame data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.FrameEventArgs.Width">
            <summary>
            Gets the width of the frame.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.FrameEventArgs.Height">
            <summary>
            Gets the height of the frame.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.FrameEventArgs.Stride">
            <summary>
            Gets the stride of the frame.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaBufferLock">
            <summary>
            Helper class that facilitates the locking of an <see cref="T:MediaFoundation.IMFMediaBuffer"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaBufferLock.#ctor(MediaFoundation.IMFMediaBuffer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaBufferLock"/> class.
            </summary>
            <param name="buffer">The buffer that will be locked.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaBufferLock.LockBuffer(System.Int32,System.Int32,System.IntPtr@,System.Int32@)">
            <summary>
            Locks the buffer and returns a pointer to scan line 0 and returns the stride.
            </summary>
            <param name="defaultStride">Minimum stride without padding. Used in case the buffer does not implement <see cref="T:MediaFoundation.IMF2DBuffer"/>.</param>
            <param name="heightInPixels">Height of the image, in pixels.</param>
            <param name="scanLine0">Receives a pointer to the start of scan line 0.</param>
            <param name="stride">Receives the actual stride.</param>
            <returns>Returns a pointer to scan line 0 and returns the stride.</returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaBufferLock.UnlockBuffer">
            <summary>
            Unlocks the buffer. Called automatically by Dispose().
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaBufferLock.Dispose">
            <summary>
            Releases resources when the object is destroyed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo">
            <summary>
            Represents a device used by Windows Media Foundation e.g. a web camera or a microphone.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo.#ctor(System.String,System.String,System.Guid)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo"/> class.
            </summary>
            <param name="friendlyNameIn">A user-friendly identifier for this device.</param>
            <param name="symbolicLinkNameIn">The unique identifier that is used by Windows Media Foundation to identify this device.</param>
            <param name="deviceTypeIn">The GUID identifying the type of the device this instance represents.</param>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo.DeviceType">
            <summary>
            Gets or sets the GUID identifying the type of the device this instance represents.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo.FriendlyName">
            <summary>
            Gets or sets a user-friendly identifier for this device.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo.SymbolicName">
            <summary>
            Gets or sets the unique identifier that is used by Windows Media Foundation to identify this device.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo">
            <summary>
            Holds information of a video format used by Windows Media Foundation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.#ctor(System.Guid,System.Guid,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.MajorType">
            <summary>
            Gets or sets the major type of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.SubType">
            <summary>
            Gets or sets the sub type of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameSizeWidth">
            <summary>
            Gets the width of the frames of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameSizeHeight">
            <summary>
            Gets the height of the frames of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.EffectiveFrameRate">
            <summary>
            Gets the effective frame rate by dividing the FrameRate by the FrameRateDenominator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRate">
            <summary>
            Gets the frame rate of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRateDenominator">
            <summary>
            Gets the frame rate denominator of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.EffectiveMinFrameRate">
            <summary>
            Gets the effective minimum frame rate by dividing the FrameRateMin by the FrameRateDenominatorMin.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRateMin">
            <summary>
            Gets the minimum frame rate of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRateMinDenominator">
            <summary>
            Gets the minimum frame rate denominator of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.EffectiveMaxFrameRate">
            <summary>
            Gets the effective maximum frame rate by dividing the FrameRateMax by the FrameRateDenominatorMax.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRateMax">
            <summary>
            Gets the maximum frame rate of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.FrameRateMaxDenominator">
            <summary>
            Gets the maximum frame rate denominator of this video format.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFoundationVideoFormatInfo.SubTypeDisplayName">
            <summary>
            Gets the display name of the media sub type of this video format.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter">
            <summary>
            Represents a base class for an object that creates a media foundation session and controls it.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.#ctor(Telerik.Windows.MediaFoundation.IMediaFoundationRenderSurface)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter"/> class.
            </summary>
            <param name="presenter">A UI presentation surface.</param>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.Presenter">
            <summary>
            Gets the UI video presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.AudioMediaSource">
            <summary>
            Gets the media source for the current session.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.VideoMediaSource">
            <summary>
            Gets the media source for the current session.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.MediaSession">
            <summary>
            Gets the current media session.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.SessionState">
            <summary>
            Gets or sets the current media session state.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.EvrVideoDisplay">
            <summary>
            Gets or sets the EVR display.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.Initialize">
            <summary>
            Starts up Media Foundation, initializes and starts a new media session.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.ShutDown">
            <summary>
            Shuts down the media session and Media Foundation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.SetPosition(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Sets the position in the EVR render control where the video should be presented.
            </summary>
            <param name="x">The x coordinate of the display rectangle in control coordinates.</param>
            <param name="y">The y coordinate of the display rectangle in control coordinates.</param>
            <param name="width">The width in control coordinates.</param>
            <param name="height">The height in control coordinates.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.StartSession">
            <summary>
            Starts the session.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)">
            <summary>
            Builds the media session and prepares it for work.
            </summary>
            <param name="mediaSession">Receives a reference to the media session.</param>
            <param name="videoMediaSource">Receives a reference to the media source.</param>
            <param name="audioMediaSource">Receives a reference to the media source.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.BaseMediaFoundationPresenter.CloseAllMediaDevices">
            <summary>
            Closes and shuts down all media device.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MFPresenterState">
            <summary>
            Represents the state of a <see cref="T:Telerik.Windows.MediaFoundation.MediaFilePresenter"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Ready">
            <summary>
            The presenter is ready for playback.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Opening">
            <summary>
            The presenter is currently opening a media file.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Starting">
            <summary>
            The presenter is starting the playback.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Started">
            <summary>
            The presenter is currently playing.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Pausing">
            <summary>
            The presenter is going to the paused state.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFPresenterState.Paused">
            <summary>
            The playback is paused.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFilePresenter">
            <summary>
            Represents a class that handles the creation and management of a Media Foundation media session for opening and displaying of a media file and gives control over it.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.#ctor(Telerik.Windows.MediaFoundation.IMediaFoundationRenderSurface)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaFilePresenter"/> class.
            </summary>
            <param name="presenter">The presenter where the media session will render.</param>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.FilePath">
            <summary>
            Gets the path to the file that will be displayed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.PresenterState">
            <summary>
            Gets the current state of the presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.VideoDuration">
            <summary>
            Gets the duration of the current video file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.PlayBackRateDelta">
            <summary>
            Gets or sets the amount by which playback rate is changed when calling <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.FastForward"/> and <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Rewind"/>
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.AudioVolumeDelta">
            <summary>
            Gets or sets the amount by which volume is changed when calling <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.VolumeUp"/> and <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.VolumeDown"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.SecondsToSkip">
            <summary>
            Gets or sets the default amount of seconds to skip when calling <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipForward"/> and <see cref="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipBackward"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.RateIsChanging">
            <summary>
            Gets a value indicating whether the playback rate is currently being changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SetMediaSourceFile(System.String)">
            <summary>
            Sets the path to the media file that will be displayed.
            </summary>
            <param name="filePath">The path to the file to display.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.StartSession">
            <summary>
            Starts the session.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Play">
            <summary>
            Starts the playback or resumes it if the presenter is in a paused state.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Pause">
            <summary>
            Pauses the playback.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Stop">
            <summary>
            Stops the playback.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipForward">
            <summary>
            Skips forward the amount of seconds the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.SecondsToSkip"/> property is set to.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipForward(System.Int64)">
            <summary>
            Skips forward the provided amount of seconds.
            </summary>
            <param name="secondsToSkip">The seconds to skip.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipBackward">
            <summary>
            Skips backward the amount of seconds the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.SecondsToSkip"/> property is set to.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.SkipBackward(System.Int64)">
            <summary>
            Skips backward the provided amount of seconds.
            </summary>
            <param name="secondsToSkip">The seconds to skip.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.FastForward">
            <summary>
            Increases the video playback rate with the value of the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.PlayBackRateDelta"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Rewind">
            <summary>
            Decreases the video playback rate with the value of the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.PlayBackRateDelta"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.Mute">
            <summary>
            Mutes the audio.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.VolumeUp">
            <summary>
            Increases the volume with the value of the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.AudioVolumeDelta"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.VolumeDown">
            <summary>
            Decreases the volume with the value of the <see cref="P:Telerik.Windows.MediaFoundation.MediaFilePresenter.AudioVolumeDelta"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.TakeSnapshot">
            <summary>
            Takes an image snapshot of the currently displayed video.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)">
            <summary>
            Builds the media session and prepares it for work.
            </summary>
            <param name="mediaSession">Receives a reference to the media session.</param>
            <param name="videoMediaSource">Receives a reference to the video media source.</param>
            <param name="audioMediaSource">Receives a reference to the audio media source.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFilePresenter.CloseAllMediaDevices">
            <summary>
            Closes and shuts down all media device.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter">
            <summary>
            Represents a class that handles the creation and management of a Media Foundation media session and allows control over it.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.FrameReady">
            <summary>
            Fires when a new frame is available.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)">
            <summary>
            Builds the media session and prepares it for work.
            </summary>
            <param name="mediaSession">Receives a reference to the media session.</param>
            <param name="videoMediaSource">Receives a reference to the video media source.</param>
            <param name="audioMediaSource">Receives a reference to the audio media source.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.OnFrameReady(System.Object,Telerik.Windows.MediaFoundation.FrameEventArgs)">
            <summary>
            Fires the <see cref="E:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.FrameReady"/> event.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFileSampleGrabberPresenter.CloseAllMediaDevices">
            <summary>
            Closes and shuts down all media devices.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MFMediaSessionState">
            <summary>
            Represents the states of a Media Foundation media session.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFMediaSessionState.None">
            <summary>
            The session is not initialized.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFMediaSessionState.Ready">
            <summary>
            The session is initialized.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFMediaSessionState.Paused">
            <summary>
            The session is paused.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFMediaSessionState.Started">
            <summary>
            The session is started and running.
            </summary>
        </member>
        <member name="F:Telerik.Windows.MediaFoundation.MFMediaSessionState.Stopped">
            <summary>
            The session is stopped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.IAMCameraControl">
            <summary>
            The IAMCameraControl interface controls web camera settings such as zoom, pan, aperture adjustment,
            or shutter speed. To obtain this interface, cast a MediaSource.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMCameraControl.GetRange(Telerik.Windows.MediaFoundation.CameraControlProperty,System.Int32@,System.Int32@,System.Int32@,System.Int32@,Telerik.Windows.MediaFoundation.CameraControlFlags@)">
            <summary>
            Get the range and default value of a specified camera property.
            </summary>
            <param name="property">The property to query.</param>
            <param name="min">The minimum value of the property.</param>
            <param name="max">The maximum value of the property.</param>
            <param name="steppingDelta">The step size for the property.</param>
            <param name="defaultValue">The default value of the property. </param>
            <param name="flags">Can it be controlled automatically or manually.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMCameraControl.Set(Telerik.Windows.MediaFoundation.CameraControlProperty,System.Int32,Telerik.Windows.MediaFoundation.CameraControlFlags)">
            <summary>
            Set a specified property on the camera.
            </summary>
            <param name="property">The property to set.</param>
            <param name="value">The new value of the property.</param>
            <param name="flags">Control it manually or automatically.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMCameraControl.Get(Telerik.Windows.MediaFoundation.CameraControlProperty,System.Int32@,Telerik.Windows.MediaFoundation.CameraControlFlags@)">
            <summary>
            Get the current setting of a camera property.
            </summary>
            <param name="property">The property to retrieve.</param>
            <param name="value">The current value of the property.</param>
            <param name="flags">Is it currently manual or automatic.</param>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.IMediaFoundationRenderSurface">
            <summary>
            Represents a UI control that has it's own handle which can be used for drawing.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IMediaFoundationRenderSurface.GetHandle">
            <summary>
            Gets a pointer to the win32 handle of the presenter.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaSessionAsyncCallBackEventHandler">
            <summary>
            A delegate to a method that is called when a Media foundation session reports an async operation completion.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaSessionAsyncCallBackErrorHandler">
            <summary>
            A delegate to a method that is called when a Media foundation session reports an async operation errors.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.AsyncCallbackHandler">
            <summary>
            Represents a class that handles Media foundation async callbacks.
            </summary>
        </member>
        <member name="E:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.MediaSessionAsyncCallBackEvent">
            <summary>
            Fired when the media foundation session reports an async operation completion.
            </summary>
        </member>
        <member name="E:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.MediaSessionAsyncCallBackError">
            <summary>
            Fired when the media foundation session reports an async operation error.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.MediaSession">
            <summary>
            Gets or sets the <see cref="T:MediaFoundation.IMFMediaSession"/> this callback is set to.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.ShutDown">
            <summary>
            Shuts down and clears all references of this handler.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.MediaFoundation#IMFAsyncCallback#GetParameters(MediaFoundation.MFASync@,MediaFoundation.MFAsyncCallbackQueue@)">
            <summary>
            Provides configuration information to the dispatching thread for a callback.
            </summary>
            <param name="flags">Receives a flag indicating the behavior of the callback object's IMFAsyncCallback.Invoke method. The default value is zero.</param>
            <param name="queue">Receives the identifier of the work queue on which the callback is dispatched.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.AsyncCallbackHandler.MediaFoundation#IMFAsyncCallback#Invoke(MediaFoundation.IMFAsyncResult)">
            <summary>
            Called when an asynchronous operation is completed.
            </summary>
            <param name="result">Pointer to the IMFAsyncResult interface. Pass this pointer to the asynchronous End... method to complete the asynchronous call.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler">
            <summary>
            A handler that allows samples for the Media Foundation topology to be passed to external instance for drawing.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.#ctor(System.Guid,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler"/> class.
            </summary>
            <param name="mediaType">The media type of the samples that will pass through this handler.</param>
            <param name="width">The width of the samples.</param>
            <param name="height">The height of the samples.</param>
            <param name="stride">The stride of the samples.</param>
        </member>
        <member name="E:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.FrameReady">
            <summary>
            Fires when a new sample's data is process.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnClockStart(System.Int64,System.Int64)">
            <summary>
            Called when the presentation clock starts.
            </summary>
            <param name="systemTime">The system time when the clock started, in 100-nanosecond units.</param>
            <param name="clockStartOffset">The new starting time for the clock, in 100-nanosecond units. This parameter can also equal PRESENTATION_CURRENT_POSITION, indicating the clock has started or restarted from its current position.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnClockStop(System.Int64)">
            <summary>
            Called when the presentation clock stops.
            </summary>
            <param name="hnsSystemTime">The system time when the clock stopped, in 100-nanosecond units.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnClockPause(System.Int64)">
            <summary>
            Called when the presentation clock pauses.
            </summary>
            <param name="hnsSystemTime">The system time when the clock was paused, in 100-nanosecond units.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnClockRestart(System.Int64)">
            <summary>
            Called when the presentation clock restarts from the same position while paused.
            </summary>
            <param name="hnsSystemTime">The system time when the clock restarted, in 100-nanosecond units.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnClockSetRate(System.Int64,System.Single)">
            <summary>
            Called when the rate changes on the presentation clock.
            </summary>
            <param name="systemTime">The system time when the rate was set, in 100-nanosecond units.</param>
            <param name="rate">The new rate, as a multiplier of the normal playback rate.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnSetPresentationClock(MediaFoundation.IMFPresentationClock)">
            <summary>
            Called when the presentation clock is set on the sample-grabber sink.
            </summary>
            <param name="presentationClock">Pointer to the presentation clock's IMFPresentationClock interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnShutdown">
            <summary>
            Called when the sample-grabber sink is shut down.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnProcessSample(System.Guid,System.Int32,System.Int64,System.Int64,System.IntPtr,System.Int32)">
            <summary>
            Called when the sample-grabber sink receives a new media sample.
            </summary>
            <param name="guidMajorMediaType">The major type GUID that specifies the format of the data. For a list of possible values, see <a href="https://docs.microsoft.com/en-us/windows/desktop/medfound/media-type-guids">Major Media Types</a>.</param>
            <param name="sampleFlags">Sample flags. The sample-grabber sink gets the value of this parameter by calling the IMFSample.GetSampleFlags method of the media sample.</param>
            <param name="sampleTime">The presentation time for this sample, in 100-nanosecond units. If the sample does not have a presentation time, the value of this parameter is Int64.MaxValue.</param>
            <param name="sampleDuration">The duration of the sample, in 100-nanosecond units. If the sample does not have a duration, the value of this parameter is Int64.MaxValue.</param>
            <param name="sampleBuffer">A pointer to a buffer that contains the sample data.</param>
            <param name="sampleSize">The size, in bytes, of the pSampleBuffer buffer.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnProcessSampleEx(System.Guid,System.Int32,System.Int64,System.Int64,System.IntPtr,System.Int32,MediaFoundation.IMFAttributes)">
            <summary>
            Called when the sample-grabber sink receives a new media sample.
            </summary>
            <param name="guidMajorMediaType">The major type GUID that specifies the format of the data. For a list of possible values, see <a href="https://docs.microsoft.com/en-us/windows/desktop/medfound/media-type-guids">Major Media Types</a>.</param>
            <param name="sampleFlags">Sample flags. The sample-grabber sink gets the value of this parameter by calling the IMFSample.GetSampleFlags method of the media sample.</param>
            <param name="sampleTime">The presentation time for this sample, in 100-nanosecond units. If the sample does not have a presentation time, the value of this parameter is Int64.MaxValue.</param>
            <param name="sampleDuration">The duration of the sample, in 100-nanosecond units. If the sample does not have a duration, the value of this parameter is Int64.MaxValue.</param>
            <param name="sampleBuffer">A pointer to a buffer that contains the sample data.</param>
            <param name="sampleSize">The size, in bytes, of the pSampleBuffer buffer.</param>
            <param name="attributes">A pointer to the IMFAttributes interface. Use this interface to get the attributes for this sample (if any). For a list of sample attributes, see <a href="https://docs.microsoft.com/en-us/windows/desktop/medfound/sample-attributes">Sample Attributes</a>.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.OnFrameReady(Telerik.Windows.MediaFoundation.FrameEventArgs)">
            <summary>
            Fires the <see cref="E:Telerik.Windows.MediaFoundation.SampleGrabberSinkCallbackHandler.FrameReady"/> event.
            </summary>
            <param name="e">The event arguments.</param>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter">
            <summary>
            Represents a class that creates and controls a Media foundation session and uses a sample grabber sink.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.FrameReady">
            <summary>
            Fires when a new frame is available.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)">
            <summary>
            Builds the media session and prepares it for work.
            </summary>
            <param name="mediaSession">Receives a reference to the media session.</param>
            <param name="videoMediaSource">Receives a reference to the video media source.</param>
            <param name="audioMediaSource">Receives a reference to the audio media source.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.OnFrameReady(System.Object,Telerik.Windows.MediaFoundation.FrameEventArgs)">
            <summary>
            Fires the <see cref="E:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.FrameReady"/> event.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event argument.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamSampleGrabberPresenter.CloseAllMediaDevices">
            <summary>
            Closes and shuts down all media devices.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.WebCamPresenter">
            <summary>
            Represents a class that handles the creation and management of a Media Foundation media session for capturing data from a web cam and gives control over it.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.#ctor(Telerik.Windows.MediaFoundation.IMediaFoundationRenderSurface)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.WebCamPresenter"/> class.
            </summary>
            <param name="presenter">A presenter where the video will be displayed. Can be null if the media session will use a sample grabber.</param>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.AudioCaptureDevice">
            <summary>
            Gets or sets the current audio capture device. Must be set before calling <see cref="M:Telerik.Windows.MediaFoundation.WebCamPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.VideoCaptureDevice">
            <summary>
            Gets or sets the current video capture device. Must be set before calling <see cref="M:Telerik.Windows.MediaFoundation.WebCamPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.VideoCaptureFormat">
            <summary>
            Gets or sets the format to use from the ones supported by the video capture device.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.CurrentAudioMediaType">
            <summary>
            Gets or sets the current audio media type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.CurrentVideoMediaType">
            <summary>
            Gets or sets the current video media type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.IsRecording">
            <summary>
            Gets whether video recording to a file is currently underway.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.WebCamPresenter.SampleGrabberTransform">
            <summary>
            Gets the sample grabber transform utilized for file recording.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.TakeSnapshot">
            <summary>
            Takes a snapshot of the video frame currently displayed on the EVR display.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.StartRecording(System.String)">
            <summary>
            Starts a new video recording to the specified file path.
            </summary>
            <param name="filePath">The path to the file where the video will be recorded.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.StopRecording">
            <summary>
            Stops the video recording.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.PrepareMediaSession(MediaFoundation.IMFMediaSession@,MediaFoundation.IMFMediaSource@,MediaFoundation.IMFMediaSource@)">
            <summary>
            Builds the media session and prepares it for work.
            </summary>
            <param name="mediaSession">Receives a reference to the media session.</param>
            <param name="videoMediaSource">Receives a reference to the video media source.</param>
            <param name="audioMediaSource">Receives a reference to the audio media source.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.WebCamPresenter.CloseAllMediaDevices">
            <summary>
            Closes and shuts down all media devices.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MediaFoundationHelper">
            <summary>
            Contains methods that facilitate the work with Windows Media Foundation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationHelper.GetVideoCaptureDevices">
            <summary>
            Returns a list of the available video capture devices.
            </summary>
            <returns>A list of the available video capture devices.</returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationHelper.GetAudioCaptureDevices">
            <summary>
            Returns a list of the available audio capture devices.
            </summary>
            <returns>A list of the available audio capture devices.</returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MediaFoundationHelper.GetVideoFormats(Telerik.Windows.MediaFoundation.MediaFoundationDeviceInfo)">
            <summary>
            Returns a list of the available video formats for a given video capture device.
            </summary>
            <param name="device">The device to get video formats for.</param>
            <returns>A list of the available video formats for a given video capture device.</returns>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.CameraControlFlags">
            <summary>
            Represents the control flags for camera settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.CameraControlProperty">
            <summary>
            The list of camera property settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.IAMVideoProcAmp">
            <summary>
            The IAMVideoProcAmp interface controls video camera settings such as brightness, contrast, hue,
            or saturation. To obtain this interface, cast the MediaSource.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMVideoProcAmp.GetRange(Telerik.Windows.MediaFoundation.VideoProcAmpProperty,System.Int32@,System.Int32@,System.Int32@,System.Int32@,Telerik.Windows.MediaFoundation.VideoProcAmpFlags@)">
            <summary>
            Get the range and default value of a camera property.
            </summary>
            <param name="property">The property.</param>
            <param name="min">The min value.</param>
            <param name="max">The max value.</param>
            <param name="steppingDelta">The step size.</param>
            <param name="defaultValue">The default value. </param>
            <param name="flags">Shows if it can be controlled automatically and/or manually.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMVideoProcAmp.Set(Telerik.Windows.MediaFoundation.VideoProcAmpProperty,System.Int32,Telerik.Windows.MediaFoundation.VideoProcAmpFlags)">
            <summary>
            Set a specified property on the camera.
            </summary>
            <param name="property">The property to set.</param>
            <param name="value">The new value of the property.</param>
            <param name="flags">The auto or manual setting.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.IAMVideoProcAmp.Get(Telerik.Windows.MediaFoundation.VideoProcAmpProperty,System.Int32@,Telerik.Windows.MediaFoundation.VideoProcAmpFlags@)">
            <summary>
            Get the current setting of a camera property.
            </summary>
            <param name="property">The property to retrieve.</param>
            <param name="value">The current value of the property.</param>
            <param name="flags">Is it manual or automatic.</param>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.VideoProcAmpFlags">
            <summary>
            Represents the control flags for video settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.VideoProcAmpProperty">
            <summary>
            The list of video property settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform">
            <summary>
            Represents a class that is used as a transform in a Media foundation topology which can record the passing samples to a file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.IsRecording">
            <summary>
            Gets a value indicating whether the transform is currently saving samples to a file.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.StartRecording(System.String,MediaFoundation.IMFMediaType,MediaFoundation.IMFMediaType)">
            <summary>
            Initializes and starts a sink writer that would write all samples passing though this transform to the provided file.
            </summary>
            <param name="fileName">The name of the video file.</param>
            <param name="incomingVideoMediaType">The media type of the incoming video samples.</param>
            <param name="incomingAudioMediaType">The media type of the incoming audio samples.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.StopRecording">
            <summary>
            Stops the video capture and the sink writer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.OnGetInputStreamInfo(MediaFoundation.Transform.MFTInputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an input stream on this Media Foundation transform.
            </summary>
            <param name="streamInfo">Pointer to an <see cref="T:MediaFoundation.Transform.MFTInputStreamInfo"/> structure. The method fills the structure with information about the input stream.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.OnGetOutputStreamInfo(MediaFoundation.Transform.MFTOutputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an output stream on this Media Foundation transform.
            </summary>
            <param name="streamInfo">Pointer to an <see cref="T:MediaFoundation.Transform.MFTOutputStreamInfo"/> structure. The method fills the structure with information about the output stream.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.OnProcessOutput(MediaFoundation.Transform.MFTOutputDataBuffer@)">
            <summary>
            Called after the processing of the input sample is complete.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFSampleGrabberTransform.OnEnumInputTypes(System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Enumerates the available InputTypes.
            </summary>
            <param name="typeIndex">The index of the type.</param>
            <param name="inputType">The available media type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.MediaFoundation.MFTransformBase">
            <summary>
            Represents the base class for Media foundation transforms.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.MediaFoundation.MFTransformBase"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.Finalize">
            <summary>
            Performs clean up logic when the instance is destroyed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFTransformBase.InputTypeAudio">
            <summary>
            Gets or sets the audio input type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFTransformBase.InputTypeVideo">
            <summary>
            Gets or sets the video input type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFTransformBase.OutputType">
            <summary>
            Gets or sets the output type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFTransformBase.InputSample">
            <summary>
            Gets or sets the current sample.
            </summary>
        </member>
        <member name="P:Telerik.Windows.MediaFoundation.MFTransformBase.SyncLockObject">
            <summary>
            Gets the object used for synchronization of async operations.
            </summary>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetStreamLimits(MediaFoundation.Misc.MFInt,MediaFoundation.Misc.MFInt,MediaFoundation.Misc.MFInt,MediaFoundation.Misc.MFInt)">
            <summary>
            Gets the minimum and maximum number of input and output streams for this Media Foundation transform.
            </summary>
            <param name="inputMinimum">Receives the minimum number of input streams.</param>
            <param name="inputMaximum">Receives the maximum number of input streams. If there is no maximum, receives the value MFT_STREAMS_UNLIMITED.</param>
            <param name="outputMinimum">Receives the minimum number of output streams.</param>
            <param name="outputMaximum">Receives the maximum number of output streams. If there is no maximum, receives the value MFT_STREAMS_UNLIMITED.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetStreamCount(MediaFoundation.Misc.MFInt,MediaFoundation.Misc.MFInt)">
            <summary>
            Gets the current number of input and output streams on this Media Foundation transform.
            </summary>
            <param name="inputStreams">Receives the number of input streams.</param>
            <param name="outputStreams">Receives the number of output streams.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetStreamIDs(System.Int32,System.Int32[],System.Int32,System.Int32[])">
            <summary>
            Gets the stream identifiers for the input and output streams on this Media Foundation transform.
            </summary>
            <param name="inputIDArraySize">Number of elements in the inputIDs array.</param>
            <param name="inputIDs">Pointer to an array allocated by the caller. The method fills the array with the input stream identifiers. The array size must be at least equal to the number of input streams. To get the number of input streams, call GetStreamCount.</param>
            <param name="outputIDArraySize">Number of elements in the outputIDs array.</param>
            <param name="outputIDs">Pointer to an array allocated by the caller. The method fills the array with the output stream identifiers. The array size must be at least equal to the number of output streams. To get the number of output streams, call GetStreamCount.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetInputStreamInfo(System.Int32,MediaFoundation.Transform.MFTInputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="streamInfo">Pointer to an MFTInputStreamInfo structure. The method fills the structure with information about the input stream.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetOutputStreamInfo(System.Int32,MediaFoundation.Transform.MFTOutputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an output stream on this Media Foundation transform.
            </summary>
            <param name="outputStreamID">Output stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="streamInfo">Pointer to an MFTOutputStreamInfo structure. The method fills the structure with information about the output stream.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetAttributes(MediaFoundation.IMFAttributes@)">
            <summary>
            Gets the global attribute store for this Media Foundation transform.
            </summary>
            <param name="attributes">Receives a pointer to the IMFAttributes interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetInputStreamAttributes(System.Int32,MediaFoundation.IMFAttributes@)">
            <summary>
            Gets the attribute store for an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="attributes">Receives a pointer to the IMFAttributes interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetOutputStreamAttributes(System.Int32,MediaFoundation.IMFAttributes@)">
            <summary>
            Gets the attribute store for an output stream on this Media Foundation transform.
            </summary>
            <param name="outputStreamID">Output stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="attributes">Receives a pointer to the IMFAttributes interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.DeleteInputStream(System.Int32)">
            <summary>
            Removes an input stream from this Media Foundation transform.
            </summary>
            <param name="streamID">Identifier of the input stream to remove.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.AddInputStreams(System.Int32,System.Int32[])">
            <summary>
            Adds one or more new input streams to this Media Foundation transform.
            </summary>
            <param name="streams">Number of streams to add.</param>
            <param name="streamIDs">Array of stream identifiers. The new stream identifiers must not match any existing input streams.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetInputAvailableType(System.Int32,System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Gets an available media type for an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="typeIndex">Index of the media type to retrieve. Media types are indexed from zero and returned in approximate order of preference.</param>
            <param name="type">Receives a pointer to the IMFMediaType interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetOutputAvailableType(System.Int32,System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Gets an available media type for an output stream on this Media Foundation transform.
            </summary>
            <param name="outputStreamID">Output stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="typeIndex">Index of the media type to retrieve. Media types are indexed from zero and returned in approximate order of preference.</param>
            <param name="type">Receives a pointer to the IMFMediaType interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.SetInputType(System.Int32,MediaFoundation.IMFMediaType,MediaFoundation.Transform.MFTSetTypeFlags)">
            <summary>
            Sets, tests, or clears the media type for an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="type">Pointer to the IMFMediaType interface, or null.</param>
            <param name="flags">Zero or more flags from the MFTSetTypeFlags enumeration.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.SetOutputType(System.Int32,MediaFoundation.IMFMediaType,MediaFoundation.Transform.MFTSetTypeFlags)">
            <summary>
            Sets, tests, or clears the media type for an output stream on this Media Foundation transform.
            </summary>
            <param name="outputStreamID">Output stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="type">Pointer to the IMFMediaType interface, or null.</param>
            <param name="flags">Zero or more flags from the MFTSetTypeFlags enumeration.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetInputCurrentType(System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Gets the current media type for an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="type">Receives a pointer to the IMFMediaType interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetOutputCurrentType(System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Gets the current media type for an output stream on this Media Foundation transform.
            </summary>
            <param name="outputStreamID">Output stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="type">Receives a pointer to the IMFMediaType interface. The caller must release the interface.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetInputStatus(System.Int32,MediaFoundation.Transform.MFTInputStatusFlags@)">
            <summary>
            Queries whether an input stream on this Media Foundation transform can accept more data.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="flags">Receives a member of the MFTInputStatusFlags enumeration, or zero. If the value is MFTInputStatusFlags.AcceptData, the stream specified in inputStreamID can accept more input data.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.GetOutputStatus(MediaFoundation.Transform.MFTOutputStatusFlags@)">
            <summary>
            Queries whether the Media Foundation transform is ready to produce output data.
            </summary>
            <param name="flags">Receives a member of the MFTOutputStatusFlags enumeration, or zero. If the value is MFTOutputStatusFlags.SampleReady, the MFT can produce an output sample.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.SetOutputBounds(System.Int64,System.Int64)">
            <summary>
            Sets the range of time stamps the client needs for output.
            </summary>
            <param name="lowerBound">Specifies the earliest time stamp. The Media Foundation transform will accept input until it can produce an output sample that begins at this time; or until it can produce a sample that ends at this time or later. If there is no lower bound, use the value MFT_OUTPUT_BOUND_LOWER_UNBOUNDED.</param>
            <param name="upperBound">Specifies the latest time stamp. The MFT will not produce an output sample with time stamps later than this time. If there is no upper bound, use the value MFT_OUTPUT_BOUND_UPPER_UNBOUNDED.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.ProcessEvent(System.Int32,MediaFoundation.IMFMediaEvent)">
            <summary>
            Sends an event to an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="mediaEvent">An IMFMediaEvent interface of an event object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.ProcessMessage(MediaFoundation.Transform.MFTMessageType,System.IntPtr)">
            <summary>
            Sends a message to the Media Foundation transform.
            </summary>
            <param name="message">The message to send, specified as a member of the MFTMessageType enumeration.</param>
            <param name="param">Message parameter. The meaning of this parameter depends on the message type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.ProcessInput(System.Int32,MediaFoundation.IMFSample,System.Int32)">
            <summary>
            Delivers data to an input stream on this Media Foundation transform.
            </summary>
            <param name="inputStreamID">Input stream identifier. To get the list of stream identifiers, call GetStreamIDs.</param>
            <param name="sample">Pointer to the IMFSample interface of the input sample. The sample must contain at least one media buffer that contains valid input data.</param>
            <param name="flags">Reserved. Must be zero.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.ProcessOutput(MediaFoundation.Transform.MFTProcessOutputFlags,System.Int32,MediaFoundation.Transform.MFTOutputDataBuffer[],MediaFoundation.Transform.ProcessOutputStatus@)">
            <summary>
            Generates output from the current input data.
            </summary>
            <param name="flags">Bitwise OR of zero or more flags from the MFTProcessOutputFlags enumeration.</param>
            <param name="outputBufferCount">Number of elements in the pOutputSamples array. The value must be at least 1.</param>
            <param name="outputSamples">An array of MFTOutputDataBuffer structures, allocated by the caller. The MFT uses this array to return output data to the caller.</param>
            <param name="status">Receives a bitwise OR of zero or more flags from the ProcessOutputStatus enumeration.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnCheckInputType(MediaFoundation.IMFMediaType)">
            <summary>
            Checks whether the provided <see cref="T:MediaFoundation.IMFMediaType"/> is identical to the one set as <see cref="P:Telerik.Windows.MediaFoundation.MFTransformBase.InputTypeVideo"/>.
            </summary>
            <param name="mediaType">The <see cref="T:MediaFoundation.IMFMediaType"/> to compare to the current <see cref="P:Telerik.Windows.MediaFoundation.MFTransformBase.InputTypeVideo"/>.</param>
            <returns>S_OK or MF_E_INVALIDTYPE.</returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnCheckOutputType(MediaFoundation.IMFMediaType)">
            <summary>
            Checks whether the provided <see cref="T:MediaFoundation.IMFMediaType"/> is identical to the one set as OutputType.
            </summary>
            <param name="mediaType">The <see cref="T:MediaFoundation.IMFMediaType"/> to compare to the current OutputType.</param>
            <returns>S_OK, MF_E_TRANSFORM_TYPE_NOT_SET or MF_E_INVALIDTYPE.</returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnProcessInput">
            <summary>
            Called after the processing of the input sample is complete.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnProcessOutput(MediaFoundation.Transform.MFTOutputDataBuffer@)">
            <summary>
            Called after the processing of the output sample is complete.
            </summary>
            <param name="outputSamples">The data contained in the output sample.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnGetInputStreamInfo(MediaFoundation.Transform.MFTInputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an input stream on this Media Foundation transform.
            </summary>
            <param name="streamInfo">Pointer to an <see cref="T:MediaFoundation.Transform.MFTInputStreamInfo"/> structure. The method fills the structure with information about the input stream.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnGetOutputStreamInfo(MediaFoundation.Transform.MFTOutputStreamInfo@)">
            <summary>
            Gets the buffer requirements and other information for an output stream on this Media Foundation transform.
            </summary>
            <param name="streamInfo">Pointer to an <see cref="T:MediaFoundation.Transform.MFTOutputStreamInfo"/> structure. The method fills the structure with information about the output stream.</param>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnEnumInputTypes(System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Enumerates the available InputTypes.
            </summary>
            <param name="typeIndex">The index of the type.</param>
            <param name="inputType">The available media type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.OnEnumOutputTypes(System.Int32,MediaFoundation.IMFMediaType@)">
            <summary>
            Enumerates the available output types.
            </summary>
            <param name="typeIndex">The index of the type.</param>
            <param name="outputType">The available media type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.MediaFoundation.MFTransformBase.CreateOutputFromInput">
            <summary>
            Creates an output type based on the input one.
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
