﻿using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Vanara.PInvoke;
using Application = System.Windows.Application;
using Button = System.Windows.Controls.Button;
using Timer = System.Windows.Forms.Timer;

namespace Everylang.App.View.Controls.Converter
{
    /// <summary>
    /// Interaction logic for FastActionTextWindow.xaml
    /// </summary>
    internal partial class TextFrameWindow
    {
        internal static readonly DependencyProperty FastActionIndexProperty =
            DependencyProperty.Register("FastActionIndex",
                typeof(string),
                typeof(TextFrameWindow),
                new FrameworkPropertyMetadata(""));

        internal string FastActionIndex
        {
            get { return (string)GetValue(FastActionIndexProperty); }
            set { SetValue(FastActionIndexProperty, value); }
        }

        internal Action? OnClosedWindow;

        internal TextFrameWindow(string textOrig)
        {
            InitializeComponent();
            Opened += OnOpened;

            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackKeyUp.CallbackEventHandler += HookManagerKeyUp;
            HookCallBackMouseWheel.CallbackEventHandler += HookManagerMouseWheel;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            DataContext = this;
            FastActionIndex = LocalizationManager.GetString("FastActionIndex");
            lvFastAction_Bind(textOrig, SettingsManager.Settings.ConverterFramesList.Split(new[] { '¿' }, StringSplitOptions.RemoveEmptyEntries).ToList());
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                CloseWin();
            }
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;

                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void HookManagerMouseWheel(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                return;
            }
            if (VisualTreeHelper.GetChild(lvFastAction, 0) is Decorator border)
            {
                var mouse = InputManager.Current.PrimaryMouseDevice;
                var args = new MouseWheelEventArgs(mouse, Environment.TickCount, (int)e.wheelRotation);
                args.RoutedEvent = MouseWheelEvent;
                if (border.Child is ScrollViewer scrollViewer) scrollViewer.RaiseEvent(args);
                e.Handled = true;
            }
        }

        private int _currentSelected = -1;

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (lvFastAction.Items.Count == 0)
            {
                return;
            }
            if (e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {

                if (lvFastAction.SelectedIndex == -1)
                {
                    _currentSelected = 0;
                    lvFastAction.SelectedIndex = 0;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.UpArrow)
                {
                    ButtonClickRepeater(true, VirtualKeycodes.UpArrow);
                    if (_currentSelected == 0)
                    {
                        _currentSelected = lvFastAction.Items.Count - 1;
                    }
                    else
                    {
                        _currentSelected -= 1;
                    }
                    if (_currentSelected < -1)
                    {
                        _currentSelected = -1;
                    }
                    lvFastAction.SelectedIndex = _currentSelected;

                }
                if (e.KeyCode == VirtualKeycodes.DownArrow)
                {
                    ButtonClickRepeater(true, VirtualKeycodes.DownArrow);
                    if (_currentSelected == lvFastAction.Items.Count - 1)
                    {
                        _currentSelected = 0;
                    }
                    else
                    {
                        _currentSelected += 1;
                    }
                    if (_currentSelected < -1)
                    {
                        _currentSelected = -1;
                    }
                    lvFastAction.SelectedIndex = _currentSelected;
                }
                lvFastAction.UpdateLayout();
                if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
                    lvFastAction.ScrollIntoView(lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1]!);
                lvFastAction.UpdateLayout();
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                e.Handled = true;
                CloseWin();
            }
            if (e.KeyCode == VirtualKeycodes.Enter)
            {
                e.Handled = true;
                Replace();
            }
            int i;
            if (int.TryParse(KeyboardLayoutMethods.CodeToString(e), out i))
            {
                if (i > 0 && i < 10)
                {
                    e.Handled = true;
                    lvFastAction.SelectedIndex = i - 1;
                    Replace();
                }
            }
            if (e.KeyCode == VirtualKeycodes.C && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();
            }

            if (e.KeyCode == VirtualKeycodes.Insert && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();
            }

        }

        private void HookManagerKeyUp(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {
                ButtonClickRepeater(false, VirtualKeycodes.UpArrow);
            }
        }

        private void ButtonClickRepeater(bool start, VirtualKeycodes downKey)
        {
            if (start)
            {
                _timer = new Timer();
                _timer.Interval = 80;
                _timer.Tick += (_, _) =>
                {
                    Key key = Key.Down;
                    if (downKey == VirtualKeycodes.UpArrow)
                    {
                        key = Key.Up;
                    }

                    if (Keyboard.IsKeyDown(key))
                    {
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            (ThreadStart)delegate
                            {
                                if (key == Key.Down)
                                {
                                    if (_currentSelected == lvFastAction.Items.Count - 1)
                                    {
                                        _currentSelected = 0;
                                    }
                                    else
                                    {
                                        _currentSelected += 1;
                                    }
                                    if (_currentSelected < -1)
                                    {
                                        _currentSelected = -1;
                                    }
                                    lvFastAction.SelectedIndex = _currentSelected;
                                }
                                else
                                {
                                    if (_currentSelected == 0)
                                    {
                                        _currentSelected = lvFastAction.Items.Count - 1;
                                    }
                                    else
                                    {
                                        _currentSelected -= 1;
                                    }
                                    if (_currentSelected < -1)
                                    {
                                        _currentSelected = -1;
                                    }
                                    lvFastAction.SelectedIndex = _currentSelected;
                                }
                                lvFastAction.UpdateLayout();
                                if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
                                    lvFastAction.ScrollIntoView(lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1]!);
                                lvFastAction.UpdateLayout();
                            });
                    }
                };
                _timer.Start();
            }
            else
            {
                if (_timer != null) _timer.Stop();
            }


        }

        private Timer? _timer;

        private void CloseWin()
        {
            HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
            HookCallBackKeyUp.CallbackEventHandler -= HookManagerKeyUp;
            HookCallBackMouseWheel.CallbackEventHandler -= HookManagerMouseWheel;

            if (lvFastAction.ContextMenu != null) lvFastAction.ContextMenu.IsOpen = false;
            lvFastAction.ItemsSource = null;
            if (OnClosedWindow != null) OnClosedWindow();
            IsOpen = false;
        }


        internal struct ItemsSourceStruct
        {
            public int Index { get; set; }
            public string Text { get; set; }
            public string FullText { get; set; }
        }

        private void lvFastAction_Bind(string textOrig, List<string> framesList)
        {
            var text = textOrig.Length > 50 ? textOrig.Substring(0, 50) + "..." : textOrig;
            var listRes = new List<ItemsSourceStruct>();
            for (var i = 0; i < framesList.Count; i++)
            {
                var frame = framesList[i];
                listRes.Add(new ItemsSourceStruct()
                {
                    Text = frame.Replace("‽", text),
                    FullText = frame.Replace("‽", textOrig),
                    Index = i
                });
            }

            lvFastAction.ItemsSource = listRes;
        }

        private void Copy()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                var copyText = "";
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    copyText += ((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).FullText;
                    if (i != lvFastAction.SelectedItems.Count - 1)
                    {
                        copyText += Environment.NewLine;
                    }
                }
                ClipboardOperations.SetTextWithoutHistory(copyText);
                CloseWin();
            }
        }

        private void LvFastAction_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
            {
                var selIndex = lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1];
                lvFastAction.SelectedItems.Clear();
                lvFastAction.SelectedItem = selIndex;
                Replace();
            }


        }

        private void Replace()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                string replacedText = "";
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    replacedText += ((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).FullText;
                    if (i != lvFastAction.SelectedItems.Count - 1)
                    {
                        replacedText += Environment.NewLine;
                    }
                }
                CloseWin();
                SendText.SendStringByPaste(replacedText, false);
            }
        }


        private void ListBoxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element)
                {
                    var item = GetSelectedListBoxItem(element);
                    if (item != null)
                    {
                        var buttonListBoxItem = GetSelectedButtonItem() as Button;
                        if (buttonListBoxItem != null && buttonListBoxItem.Name == "ButtonMenu")
                        {
                            lvFastAction.SelectedItem = item.DataContext;
                        }
                        else
                        {
                            if ((Keyboard.Modifiers & ModifierKeys.Shift) != 0)
                            {
                                try
                                {
                                    object ob = (ItemsSourceStruct)item.DataContext;
                                    var mouseIndex = lvFastAction.Items.IndexOf(ob);
                                    var minIndex = lvFastAction.SelectedIndex < mouseIndex
                                        ? lvFastAction.SelectedIndex
                                        : mouseIndex;

                                    var allWillSelecte = Math.Abs(lvFastAction.SelectedIndex - mouseIndex);
                                    for (int i = 0; i < allWillSelecte + 1; i++)
                                    {
                                        lvFastAction.SelectedItems.Add(lvFastAction.Items[i + minIndex]);
                                    }
                                }
                                catch
                                {
                                    // ignore
                                }
                            }
                            else if ((Keyboard.Modifiers & ModifierKeys.Control) != 0)
                            {
                                lvFastAction.SelectedItems.Add(item.DataContext);

                            }
                            else
                            {
                                lvFastAction.SelectedItem = item.DataContext;
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        private object? GetSelectedButtonItem()
        {
            try
            {
                var item = VisualTreeHelper.HitTest(lvFastAction, Mouse.GetPosition(lvFastAction)).VisualHit;
                while (VisualTreeHelper.GetParent(item) != null && !(item is Button))
                {
                    item = VisualTreeHelper.GetParent(item);
                }
                return item;
            }
            catch
            {
                return null;
            }
        }

        private ListBoxItem? GetSelectedListBoxItem(FrameworkElement element)
        {
            try
            {
                var item = element;
                while (item != null && VisualTreeHelper.GetParent(item) != null && !(item is ListBoxItem))
                {
                    item = VisualTreeHelper.GetParent(item) as FrameworkElement;
                }
                if (!(item is ListBoxItem))
                {
                    return null;
                }
                return (ListBoxItem)item;

            }
            catch
            {
                return null;
            }
        }

    }

}
