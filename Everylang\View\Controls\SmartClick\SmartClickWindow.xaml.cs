﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Converter;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.SmartClick.ShortLink;
using System;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using Telerik.Windows;
using Telerik.Windows.Controls;
using Telerik.Windows.Controls.RadialMenu;
using Vanara.PInvoke;
using Application = System.Windows.Application;
using MousePosition = Everylang.App.Utilities.MousePosition;


namespace Everylang.App.View.Controls.SmartClick
{
    /// <summary>
    /// Interaction logic for SmartClickWindow.xaml
    /// </summary>
    internal partial class SmartClickWindow
    {

        private bool _isShowing;
        private bool _isKeyboard;
        private readonly System.Timers.Timer _timer;
        internal string? SourceText { get; set; }

        internal SmartClickWindow()
        {
            InitializeComponent();
            //StyleManager.SetTheme(this, new Windows11Theme());
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            Opened += OnOpened;
            _timer = new System.Timers.Timer();
            _timer.Elapsed += (_, _) =>
            {
                if (Application.Current != null && Application.Current.Dispatcher != null)
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Hide);
            };
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;
                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void MouseOverHide(GlobalMouseEventArgs e)
        {
            if (_isShowing && !IsMouseOver)
            {
                Hide();
                _isShowing = false;
            }
        }

        internal void Hide()
        {
            if (IsOpen)
            {
                HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
                HookCallBackMouseDown.CallbackEventHandler -= MouseOverHide;
                IsOpen = false;
            }
        }

        private int _lastSelected;

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            _timer.Stop();
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                Hide();
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Enter && RadialMenuMu.Items.FirstOrDefault(x => x.IsSelected) is var selItem)
            {
                if (selItem != null)
                {
                    selItem.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, selItem));

                }
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.LeftArrow || e.KeyCode == VirtualKeycodes.UpArrow)
            {
                foreach (var radRadialMenuItem in RadialMenuMu.Items)
                {
                    radRadialMenuItem.IsSelected = false;
                }
                _lastSelected -= 1;
                if (_lastSelected < 0)
                {
                    var item = RadialMenuMu.Items.Last();
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                else
                {
                    var item = RadialMenuMu.Items[_lastSelected];
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.RightArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {
                foreach (var radRadialMenuItem in RadialMenuMu.Items)
                {
                    radRadialMenuItem.IsSelected = false;
                }
                _lastSelected += 1;
                if (_lastSelected > RadialMenuMu.Items.Count - 1)
                {
                    var item = RadialMenuMu.Items.First();
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                else
                {
                    var item = RadialMenuMu.Items[_lastSelected];
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }

                e.Handled = true;
            }
            if (_isKeyboard)
            {
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_1 || e.KeyCode == VirtualKeycodes.Numpad_1)
                {
                    if (RadialMenuMu.Items.Count > 0)
                    {
                        var item = RadialMenuMu.Items[0];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_2 || e.KeyCode == VirtualKeycodes.Numpad_2)
                {
                    if (RadialMenuMu.Items.Count > 1)
                    {
                        var item = RadialMenuMu.Items[1];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }

                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_3 || e.KeyCode == VirtualKeycodes.Numpad_3)
                {
                    if (RadialMenuMu.Items.Count > 2)
                    {
                        var item = RadialMenuMu.Items[2];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_4 || e.KeyCode == VirtualKeycodes.Numpad_4)
                {
                    if (RadialMenuMu.Items.Count > 3)
                    {
                        var item = RadialMenuMu.Items[3];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_5 || e.KeyCode == VirtualKeycodes.Numpad_5)
                {
                    if (RadialMenuMu.Items.Count > 4)
                    {
                        var item = RadialMenuMu.Items[4];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_6 || e.KeyCode == VirtualKeycodes.Numpad_6)
                {
                    if (RadialMenuMu.Items.Count > 5)
                    {
                        var item = RadialMenuMu.Items[5];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_7 || e.KeyCode == VirtualKeycodes.Numpad_7)
                {
                    if (RadialMenuMu.Items.Count > 6)
                    {
                        var item = RadialMenuMu.Items[6];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_8 || e.KeyCode == VirtualKeycodes.Numpad_8)
                {
                    if (RadialMenuMu.Items.Count > 7)
                    {
                        var item = RadialMenuMu.Items[7];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_9 || e.KeyCode == VirtualKeycodes.Numpad_9)
                {
                    if (RadialMenuMu.Items.Count > 8)
                    {
                        var item = RadialMenuMu.Items[8];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
            }
        }

        internal async void ShowWindow(bool isEmpty, bool isKeyboard)
        {
            //_timer.Interval = 7000;
            //_timer.Start();
            _lastSelected = -1;
            RadialMenuMu.Items.Clear();
            _isKeyboard = isKeyboard;
            if (isEmpty)
            {
                AppendRadRadialMenuItem("UniPaste");
                if (ClipboardOperations.IsLastClipContainsRtf)
                {
                    AppendRadRadialMenuItem("UniPasteUnf");
                }
                AppendRadRadialMenuItem("UniAutochange");
                AppendRadRadialMenuItem("UniDiaryHistory");
                AppendRadRadialMenuItem("OcrHeader");
                AppendRadRadialMenuItem("UniClipboardHistory");
            }
            else
            {
                var rgx = new Regex(@"(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?", RegexOptions.IgnoreCase);
                if (SourceText != null && rgx.Replace(SourceText.Trim(), "") != "")
                {
                    AppendRadRadialMenuItem("UniTranslate");
                    AppendRadRadialMenuItem("UniSpellCheck");
                }
                AppendRadRadialMenuItem("UniCopy");
                AppendRadRadialMenuItem("UniPaste");
                AppendRadRadialMenuItem("UniSearch");
                AppendRadRadialMenuItem("UniEmail");
                if (SourceText != null && rgx.Replace(SourceText.Trim(), "") == "")
                {
                    AppendRadRadialMenuItem("UniLink");
                    AppendRadRadialMenuItem("UniLinkTranslate");
                    AppendRadRadialMenuItem("UniLinkShorter");
                }
                else
                {

                    var itemConverter = AppendRadRadialMenuItem("UniConverter");
                    if (itemConverter != null)
                    {

                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniEnclose"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniConvertExpressions"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniTranslit"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("ConverterReplaceSelText"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniCamelCase"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniSnakeCase"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniKebabCase"));
                        itemConverter.ChildItems.Add((RadRadialMenuItem)FindResource("UniPascalCase"));
                    }

                    var itemCase = AppendRadRadialMenuItem("UniCase");
                    if (itemCase != null)
                    {
                        itemCase.ChildItems.Add((RadRadialMenuItem)FindResource("UniInvertCase"));
                        itemCase.ChildItems.Add((RadRadialMenuItem)FindResource("UniUpCase"));
                        itemCase.ChildItems.Add((RadRadialMenuItem)FindResource("UniDownCase"));
                        itemCase.ChildItems.Add((RadRadialMenuItem)FindResource("UniFirstLetterToUp"));
                        itemCase.ChildItems.Add((RadRadialMenuItem)FindResource("UniFirstLetterToDown"));
                    }
                }
            }

            IsOpen = true;
            _isShowing = true;
            RadialMenuMu.IsOpen = true;
            await Task.Delay(50);
            if (isKeyboard)
            {
                for (int i = 0; i < RadialMenuMu.Items.Count; i++)
                {
                    var radRadialMenuItem = RadialMenuMu.Items[i];
                    TextBlock? textBlock = FindChild<TextBlock>(radRadialMenuItem, "TextBlockNumber");
                    if (textBlock != null)
                    {
                        textBlock.Text = "" + (i + 1).ToString();
                        textBlock.Margin = new Thickness(5, 0, 0, 0);
                        textBlock.VerticalAlignment = VerticalAlignment.Center;
                        textBlock.Visibility = Visibility.Visible;
                    }
                }
                System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
                HorizontalOffset = centrePos.X - this.Width / 2;
                VerticalOffset = centrePos.Y - this.Height / 2;
            }
            else
            {
                var pt = MousePosition.GetMousePoint();
                HorizontalOffset = pt.X - Width / 2;
                VerticalOffset = pt.Y - Height / 2;
            }
        }

        private RadRadialMenuItem? AppendRadRadialMenuItem(string name)
        {
            var ctrl = FindResource(name) as RadRadialMenuItem;
            if (ctrl != null && SettingsManager.Settings.SmartClickCheckedItems.Split(',').FirstOrDefault(x => x == name) != null)
            {
                RadialMenuMu.Items.Add(ctrl);
            }

            return ctrl;
        }



        internal static T? FindChild<T>(DependencyObject? parent, string childName) where T : DependencyObject
        {
            // Confirm parent and childName are valid. 
            if (parent == null) return null;

            T? foundChild = null;

            int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childrenCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                // If the child is not of the request child type child
                T? childType = child as T;
                if (childType == null)
                {
                    // recursively drill down the tree
                    foundChild = FindChild<T>(child, childName);

                    // If the child is found, break so we do not overwrite the found child. 
                    if (foundChild != null) break;
                }
                else if (!string.IsNullOrEmpty(childName))
                {
                    // If the child's name is set for search
                    if (child is FrameworkElement frameworkElement && frameworkElement.Name == childName)
                    {
                        // if the child's name is of the request name
                        foundChild = (T)child;
                        break;
                    }
                }
                else
                {
                    // child element found.
                    foundChild = (T)child;
                    break;
                }
            }

            return foundChild;
        }

        private void ButtonClickTranslate(object sender, RoutedEventArgs e)
        {
            if (SourceText != null) GlobalEventsApp.OnEventUniWindowTranslate(SourceText);
            Hide();
        }

        private async void ButtonClickCopy(object sender, RoutedEventArgs e)
        {
            Hide();
            await Task.Delay(100);
            ClipboardOperations.SendCopyText();
        }


        private void ButtonClickSpellCheck(object sender, RoutedEventArgs e)
        {
            if (SourceText != null) GlobalEventsApp.OnEventUniWindowSpellCheck(SourceText, false);
            Hide();
        }


        private void ButtonClickSearch(object sender, RoutedEventArgs e)
        {
            if (SettingsManager.Settings.SmartClickSearchService == 0)
            {
                StartProcess("http://google.com/search?q=" + SourceText);
            }
            if (SettingsManager.Settings.SmartClickSearchService == 1)
            {
                StartProcess("http://yandex.ru/yandsearch?text=" + SourceText);
            }
            if (SettingsManager.Settings.SmartClickSearchService == 2)
            {
                StartProcess("http://www.bing.com/search?q=" + SourceText);
            }
            if (SettingsManager.Settings.SmartClickSearchService == 3)
            {
                StartProcess("https://duckduckgo.com/?q=" + SourceText);
            }
            Hide();
        }

        private void StartProcess(string data)
        {
            try
            {
                Process.Start(data);
            }
            catch
            {
                // ignore
            }
        }


        private void ButtonClickEmail(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SourceText != null)
                {
                    SourceText = Uri.EscapeDataString(SourceText);
                    string emailTag = string.Format("mailto:{0}?subject=&body={1}", "?", SourceText);
                    Process.Start(emailTag);
                }

                Hide();
            }
            catch
            {
                // ignore
            }
        }

        private void ButtonClickLink(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SourceText != null && !SourceText.StartsWith("http://") && !SourceText.StartsWith("https://"))
                {
                    SourceText = "http://" + SourceText;
                }

                if (SourceText != null) Process.Start(SourceText);
                Hide();
            }
            catch
            {
                // ignore
            }
        }

        private void ButtonClickLinkTranslate(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SourceText != null && !SourceText.StartsWith("http://") && !SourceText.StartsWith("https://"))
                {
                    SourceText = "http://" + SourceText;
                }

                Process.Start(@"https://translate.google.com/translate?ie=UTF-8&u=" + SourceText);
                Hide();
            }
            catch
            {
                // ignore
            }
        }

        private async void ButtonClickLinkShorter(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SourceText != null && !SourceText.StartsWith("http://") && !SourceText.StartsWith("https://"))
                {
                    SourceText = "http://" + SourceText;
                }

                var result = "";
                if (SourceText != null && (SourceText.Contains("goo.gl/") || SourceText.Contains("tinyurl.com/") ||
                                           SourceText.Contains("bit.ly/")
                                           || SourceText.Contains("is.gd/")
                                           || SourceText.Contains("soo.gd/"))
                   )
                {
                    result = ShortenerUrl.ShortenExpand(SourceText);
                }
                else
                {
                    if (SourceText != null) result = await ShortenerUrl.Shorten(SourceText);
                }

                if (!string.IsNullOrEmpty(result))
                {
                    ShortLinkPopup linkPopup = new ShortLinkPopup(result);
                    linkPopup.Show(_isKeyboard);
                }

                Hide();
            }
            catch
            {
                // ignore
            }
        }


        private void ButtonClickClipboardHistory(object sender, RoutedEventArgs e)
        {
            Hide();
            GlobalEventsApp.OnEventClipboardView();

        }

        private void ButtonClickPaste(object sender, RoutedEventArgs e)
        {
            Hide();
            ClipboardOperations.SendPasteText();
        }

        private void ButtonClickPasteUnf(object sender, RoutedEventArgs e)
        {
            Hide();
            ClipboardKeyHookManager.KeyCombinationPressedClipboardPasteWithoutFormattingPressed();
        }

        private void ButtonClickDiaryHistory(object sender, RoutedEventArgs e)
        {
            Hide();
            GlobalEventsApp.OnEventDiaryView();

        }

        private void ButtonClickAutochange(object sender, RoutedEventArgs e)
        {
            Hide();
            GlobalEventsApp.OnEventSnippetsView();

        }

        private void ButtonClickConverter(object sender, RoutedEventArgs e)
        {
            NavigateContext context = new NavigateContext(sender as RadRadialMenuItem);
            RadialMenuMu.CommandService.ExecuteCommand(Telerik.Windows.Controls.RadialMenu.Commands.CommandId.NavigateToView, context);
        }

        private void ButtonClickTextCaseConverter(object sender, RadRoutedEventArgs e)
        {
            NavigateContext context = new NavigateContext(sender as RadRadialMenuItem);
            RadialMenuMu.CommandService.ExecuteCommand(Telerik.Windows.Controls.RadialMenu.Commands.CommandId.NavigateToView, context);
        }

        private void ButtonClickConvertExpressions(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterExpresion.ConvertExpresion();
        }

        private void ButtonClickTranslit(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertTransliteration();
        }

        private void ButtonClickEnclose(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertEncloseTextQuotationMarks();
        }

        private void ButtonClickCamelCase(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertCamelCase();
        }

        private void ButtonClickSnakeCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertSnakeCase();
        }

        private void ButtonClickKebabCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertKebabCase();
        }

        private void ButtonClickPascalCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertPascalCase();
        }

        private void ButtonClickInvertCase(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterCaps.ConvertCaps();
        }

        private void ButtonClickUpCase(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterCaps.ConvertCapsUp();
        }

        private void ButtonClickDownCase(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterCaps.ConvertCapsDown();
        }

        private void ButtonClickConvertFirstLetterToUp(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterCaps.ConvertFirstLetterToUp();
        }

        private void ButtonClickConvertFirstLetterToDown(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterCaps.ConvertFirstLetterToDown();
        }


        private void ButtonClickOcr(object sender, RoutedEventArgs e)
        {
            Hide();
            OcrManager.Instance.PressedShortcutOcr(null, null);
        }

        private void ButtonClickSearchAndReplace(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.RunReplaceInSelTextWindow();
        }

        private async void EventSetter_OnHandler(object sender, RoutedEventArgs e)
        {
            if (_isNavigated)
            {
                _isNavigated = false;
                return;
            }
            await Task.Delay(500);
            Hide();
        }

        private bool _isNavigated;
        private void RadialMenuMu_OnNavigated(object sender, RadRoutedEventArgs e)
        {
            _isNavigated = true;
        }


    }
}
