﻿<UserControl x:Class="Everylang.App.View.SettingControls.Diary.DiaryControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:globalization="clr-namespace:System.Globalization;assembly=mscorlib"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
            <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        
        <StackPanel Margin="20,10,0,0" >
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=DiarySettingsHeader}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>

            <telerik:RadToggleSwitchButton
                Margin="0,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                CheckedContent="{telerik:LocalizableResource Key=DiaryIsOn}"
                ContentPosition="Right"
                FontSize="14"
                Focusable="False"
                FontWeight="DemiBold"
                IsChecked="{Binding Path=DiaryViewModel.IsEnabled}"
                UncheckedContent="{telerik:LocalizableResource Key=DiaryIsOff}" />

            <StackPanel Margin="0,15,0,0">
                <TextBlock  IsEnabled="{Binding DiaryViewModel.IsEnabled}" FontSize="14" Text="{telerik:LocalizableResource Key=DiaryShortcuts}" />
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBox IsReadOnly="True" Background="Transparent" HorizontalAlignment="Left" Width="350" Text="{Binding Path=DiaryViewModel.DiaryShortcut}" IsEnabled="{Binding DiaryViewModel.IsEnabled}" ToolTip="{Binding Path=DiaryViewModel.DiaryShortcut}"/>
                    <telerik:RadButton Focusable="False" Margin="5,0,0,0" Padding="5,0,5,0" Click="DiaryShowClick" HorizontalAlignment="Left"  Content="{telerik:LocalizableResource Key=Edit}" IsEnabled="{Binding DiaryViewModel.IsEnabled}"/>
                </StackPanel>
            </StackPanel>

            <TextBlock  Margin="0,10,0,0" Text="{telerik:LocalizableResource Key=DiaryPassword}" />
            <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                <telerik:RadPasswordBox x:Name="PasswordBoxMu" Background="Transparent" Width="300" IsEnabled="{Binding DiaryViewModel.IsEnabled}" WatermarkContent="{telerik:LocalizableResource Key=DiaryPassword}"
                                        HorizontalAlignment="Left"/>
                <telerik:RadButton x:Name="PasswordButton" Margin="5,0,0,0" Padding="5,0,5,0" Focusable="False"  IsEnabled="{Binding DiaryViewModel.IsEnabled}" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=Save}" Click="SavePassword"/>
            </StackPanel>

            <CheckBox Focusable="False" Margin="0,10,0,0" FontSize="14" IsChecked="{Binding Path=DiaryViewModel.IsSaveOneWordSentences}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=IsSaveOneWordSentences}" />
            </CheckBox>
            
            <StackPanel Orientation="Horizontal"  Margin="0,10,0,0" IsEnabled="{Binding Path=DiaryViewModel.IsEnabled}">
                <TextBlock  Text="{telerik:LocalizableResource Key=DiaryMaxItems}" VerticalAlignment="Center" FontSize="13"/>
                <telerik:RadNumericUpDown  Margin="10,0,0,0" Width="110"
                                           IsEditable="True"
                                         VerticalAlignment="Center"
                                           FontSize="13"
                                         Value="{Binding Path=DiaryViewModel.MaxDiaryItems}" >
                    <telerik:RadNumericUpDown.NumberFormatInfo>
                        <globalization:NumberFormatInfo NumberDecimalDigits="0" />
                    </telerik:RadNumericUpDown.NumberFormatInfo>
                </telerik:RadNumericUpDown>
            </StackPanel>

        </StackPanel>
    </Grid>
</UserControl>
