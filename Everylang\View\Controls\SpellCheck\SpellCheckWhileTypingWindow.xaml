﻿<Popup x:Class="Everylang.App.View.Controls.SpellCheck.SpellCheckWhileTypingWindow"
                      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                      xmlns:spellCheck="clr-namespace:Everylang.App.View.Controls.SpellCheck"
                      x:Name="me"
                      Placement="Absolute" StaysOpen="True" Focusable="False" AllowsTransparency="True"  MouseEnter="SpellCheckWhileTypingWindow_OnMouseEnter" MouseLeave="SpellCheckWhileTypingWindow_OnMouseLeave"
                      x:ClassModifier="internal">
    <Border BorderThickness="1" Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadListBox Margin="1" BorderThickness="0" Name="LvSpellCheckAction" ScrollViewer.HorizontalScrollBarVisibility="Disabled" PreviewMouseDoubleClick="LvFastAction_OnPreviewMouseDoubleClick">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate DataType="{x:Type spellCheck:SpellCheckWhileTypingWindow+SuggestionsRes}">
                    <StackPanel Orientation="Horizontal" Margin="5,0,5,0">
                        <telerik:Label Content="{Binding Key}" FontSize="11" VerticalAlignment="Center" Padding="0" Margin="0"/>
                        <telerik:Label  Margin="5,0,0,0" FontSize="13" Content="{Binding Suggestion}" VerticalAlignment="Center" Padding="0"/>
                    </StackPanel>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Height" Value="22" />
                    <Setter Property="Focusable" Value="False"/>
                    <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListBoxItem_MouseLeftButtonDown" />
                </Style>
                
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
    </Border>
</Popup>
