﻿using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace Everylang.App.Utilities
{
    class StartUp
    {
        internal static void CreateStartUp()
        {
            SetAutorunValue(true);
            IsStartUp = true;
        }

        internal static void DeleteStartUp()
        {
            SetAutorunValue(false);
            IsStartUp = false;
        }

        internal static bool IsStartAdmin
        {
            get
            {
                RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
                string? qwe = reg.GetValue("StartAdmin") as string;
                if (!string.IsNullOrEmpty(qwe) && qwe.Equals("true"))
                {
                    return true;
                }
                return false;
            }
        }

        internal static bool IsStartUp { get; set; }

        internal static void CheckStartUp()
        {
            IsStartUp = false;
            RegistryKey? reg = Registry.CurrentUser.OpenSubKey("Software\\Microsoft\\Windows\\CurrentVersion\\Run\\", true);
            if (reg != null)
            {
                string? value = reg.GetValue("EveryLang") as string;
                if (!string.IsNullOrEmpty(value))
                {
                    SetAutorunValue(false);
                    SetAutorunValue(true);
                }
                reg.Close();
                IsStartUp = !string.IsNullOrEmpty(value);
            }
        }

        internal static bool SetAutorunValue(bool autorun)
        {
            var progPath = "\"" + Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Everylang.exe") + "\"";
            RegistryKey? reg = Registry.CurrentUser.OpenSubKey("Software\\Microsoft\\Windows\\CurrentVersion\\Run\\", true);
            try
            {
                if (autorun)
                {
                    if (reg != null) reg.SetValue("EveryLang", progPath);
                }
                else
                {
                    if (reg != null) reg.DeleteValue("EveryLang", false);
                }

                if (reg != null) reg.Close();
            }
            catch
            {
                return false;
            }
            return true;
        }

        internal static void CreateStartAdmin()
        {
            SetStartAdmin(true);
        }

        internal static void DeleteStartAdmin()
        {
            SetStartAdmin(false);
        }

        internal static bool SetStartAdmin(bool isStartAdmin)
        {
            RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
            try
            {
                if (isStartAdmin)
                {
                    reg.SetValue("StartAdmin", "true");
                }
                else
                {
                    reg.DeleteValue("StartAdmin");
                }

                reg.Close();
            }
            catch
            {
                return false;
            }
            return true;
        }

        internal static bool SetLastTryStartAdmin(bool isLastTryStartAdmin)
        {
            RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
            try
            {
                if (isLastTryStartAdmin)
                {
                    reg.SetValue("LastTryStartAdmin", "true");
                }
                else
                {
                    reg.DeleteValue("LastTryStartAdmin");
                }

                reg.Close();
            }
            catch
            {
                return false;
            }
            return true;
        }

        internal static bool CheckStartAdmin()
        {

            if (!SettingsApp.SettingsManager.IsAdministrator() && Administrator.IsCanRunAdministrator())
            {
                RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
                if (reg != null)
                {
                    string qwe = (string)reg.GetValue("StartAdmin");
                    if (!string.IsNullOrEmpty(qwe) && qwe.Equals("true"))
                    {
                        string adminStarted = (string)reg.GetValue("LastTryStartAdmin");
                        if (!string.IsNullOrEmpty(adminStarted) && adminStarted.Equals("true"))
                        {
                            SetLastTryStartAdmin(false);
                            return false;
                        }
                        SetLastTryStartAdmin(true);
                        ProcessStartInfo proc = new ProcessStartInfo();
                        proc.UseShellExecute = true;
                        proc.WorkingDirectory = Environment.CurrentDirectory;
                        var procFileName = Assembly.GetEntryAssembly()?.CodeBase;
                        if (procFileName != null)
                            proc.FileName = procFileName;
                        proc.Verb = "runas";
                        try
                        {
                            Process.Start(proc);
                        }
                        catch
                        {
                            return false;
                        }
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
