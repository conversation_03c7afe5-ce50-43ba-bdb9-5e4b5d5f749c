﻿<Popup x:Class="Everylang.App.View.Controls.Common.CommonWindow.FastActionCommonWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
       xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
       mc:Ignorable="d"
       x:Name="me" AllowsTransparency="True"
       MinHeight="300" MinWidth="400" Height="300" Width="450" MaxHeight="600" MaxWidth="800" Placement="Mouse"
       StaysOpen="True" Focusable="false" x:ClassModifier="internal">
    <Popup.Resources>
        <ResourceDictionary>
            <Style x:Key="ImageButtonStyle" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Focusable" Value="False" />
                <Setter Property="Padding" Value="5" />
                <Setter Property="MinHeight" Value="0" />
            </Style>
            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonSettings" TargetType="telerik:RadButton"
                   BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="CogOutline" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonListSnippets" TargetType="telerik:RadButton"
                   BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="FormatListBulleted" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonTop" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="PinOff" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="Pin" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="ImageButtonClipboardFavorite" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsClipboardFavorite, ElementName=me}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="18"
                                                  Height="18"
                                                  Kind="StarOutline" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsClipboardFavorite, ElementName=me}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="18"
                                                  Height="18"
                                                  Kind="Star" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>

    </Popup.Resources>
    <Grid Background="Transparent">
        <Border BorderThickness="2" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" CornerRadius="4"
                Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <telerik:RadNavigationView Grid.Row="0"
                                           Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}"
                                           PaneHeaderHeight="35" Name="MuNavigationView" AutoChangeDisplayMode="False"
                                           PaneToggleButtonVisibility="Visible" DisplayMode="Minimal"
                                           ExpandedPaneWidth="170" BorderThickness="0"
                                           SelectionChanged="NavigationViewOnSelectionChanged">
                    <telerik:RadNavigationView.PaneToggleButtonStyle>
                        <Style TargetType="telerik:RadToggleButton" BasedOn="{StaticResource {x:Type telerik:RadToggleButton}}">
                            <Setter Property="Focusable" Value="False" />
                            <Setter Property="Padding" Value="2" />
                            <Setter Property="Margin" Value="0" />
                            <Setter Property="MinHeight" Value="0" />

                            <Setter Property="IsBackgroundVisible" Value="False" />
                        </Style>
                    </telerik:RadNavigationView.PaneToggleButtonStyle>
                    <telerik:RadNavigationView.PaneHeader>
                        <Grid>
                            <telerik:Label Margin="0,0,0,0" FontSize="14" VerticalAlignment="Center"
                                           Content="{Binding Path=TitleText}" />
                            <Thumb x:Name="ThumbMy" Opacity="0" Grid.Row="0" Grid.Column="0"
                                   PreviewMouseDoubleClick="WindowMouseLeftButtonDown" DragDelta="OnDragDelta" />
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft" VerticalAlignment="Center">
                                <telerik:RadButton Style="{StaticResource ImageButtonClose}" Click="ButtonClickClose"
                                                   ToolTip="{telerik:LocalizableResource Key=CloseHeaderButton}"
                                                   IsCancel="True" />
                                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonTop}"
                                                   ToolTip="{telerik:LocalizableResource Key=StayOnTopButton}"
                                                   Click="ClickSetStayOnTop" />
                                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonSettings}"
                                                   ToolTip="{telerik:LocalizableResource Key=Settings}"
                                                   Click="ClickOpenSettings" />
                                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonListSnippets}"
                                                   ToolTip="{telerik:LocalizableResource Key=ListOnMainWindow}"
                                                   Click="ClickOpenList" />
                                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonClipboardFavorite}"
                                                   ToolTip="{telerik:LocalizableResource Key=ClipboardFavorite}"
                                                   Visibility="{Binding Path=IsClipboard, ElementName=me, Converter={StaticResource BoolToVis}}"
                                                   Click="ClickClipboardFavoriteCheck" />
                            </StackPanel>
                        </Grid>
                    </telerik:RadNavigationView.PaneHeader>
                    <telerik:RadNavigationView.Items>
                        <telerik:RadNavigationViewItem Content="{telerik:LocalizableResource Key=ClipboardTab}"
                                                       MinHeight="33" MinWidth="15">
                            <telerik:RadNavigationViewItem.Icon>
                                <wpf:MaterialIcon Kind="ClipboardOutline" HorizontalAlignment="Center"
                                                            VerticalAlignment="Center" Height="18" />
                            </telerik:RadNavigationViewItem.Icon>
                        </telerik:RadNavigationViewItem>
                        <telerik:RadNavigationViewItem Content="{telerik:LocalizableResource Key=DiareTab}"
                                                       MinHeight="33" MinWidth="15" Margin="0,5,0,0">
                            <telerik:RadNavigationViewItem.Icon>
                                <wpf:MaterialIcon Kind="NotebookOutline" HorizontalAlignment="Center"
                                                            VerticalAlignment="Center" Height="18" />
                            </telerik:RadNavigationViewItem.Icon>
                        </telerik:RadNavigationViewItem>
                        <telerik:RadNavigationViewItem Content="{telerik:LocalizableResource Key=AutochangeTab}"
                                                       MinHeight="33" MinWidth="15" Margin="0,5,0,0">
                            <telerik:RadNavigationViewItem.Icon>
                                <wpf:MaterialIcon Kind="StickerTextOutline" HorizontalAlignment="Center"
                                                            VerticalAlignment="Center" Height="18" />
                            </telerik:RadNavigationViewItem.Icon>
                        </telerik:RadNavigationViewItem>
                    </telerik:RadNavigationView.Items>
                </telerik:RadNavigationView>
                <Border Grid.Row="1">
                    <Grid>
                        <telerik:RadWatermarkTextBox x:Name="TextBoxSearch" Background="Transparent"
                                                     HorizontalAlignment="Stretch" BorderThickness="0" Padding="0"
                                                     WatermarkContent="{telerik:LocalizableResource Key=FastActionTextWindowSearch}"
                                                     PreviewMouseDown="TextBoxSearch_OnPreviewMouseDown"
                                                     TextChanged="TextBoxSearch_OnTextChanged" />
                    </Grid>
                </Border>
            </Grid>
        </Border>
        <Thumb x:Name="ThumbRightBottom" HorizontalAlignment="Right" Cursor="SizeNWSE" Width="10" Height="10"
               VerticalAlignment="Bottom"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbBottom" HorizontalAlignment="Stretch" Cursor="SizeNS" VerticalAlignment="Bottom" Height="5"
               Margin="0,0,10,0"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbTop" HorizontalAlignment="Stretch" Cursor="SizeNS" VerticalAlignment="Top" Height="5"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbRight" HorizontalAlignment="Right" Cursor="SizeWE" VerticalAlignment="Stretch" Width="5"
               DragDelta="ThumbDragDelta" Opacity="0" Margin="0,0,0,10" />
        <Thumb x:Name="ThumbLeft" HorizontalAlignment="Left" Cursor="SizeWE" VerticalAlignment="Stretch" Width="5"
               DragDelta="ThumbDragDelta" Opacity="0" />
    </Grid>
</Popup>