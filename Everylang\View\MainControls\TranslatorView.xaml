﻿<UserControl
    IsVisibleChanged="UserControl_IsVisibleChanged"
    x:Name="TranslatorViewControl"
    x:Class="Everylang.App.View.MainControls.TranslatorView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:richTextBoxEx1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
    xmlns:formatters1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    xmlns:dataModel="clr-namespace:Everylang.App.Data.DataModel"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">

    <UserControl.Resources>
        <ResourceDictionary>
            <Style TargetType="telerik:RadBusyIndicator" x:Key="ProgressIndicatorVisibility"  BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="IsBusy" Value="{Binding TranslationMainViewModel.IsVisibleProgress}" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding TranslationMainViewModel.IsVisibleProgress}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding TranslationMainViewModel.IsVisibleProgress}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style TargetType="telerik:RadButton" x:Key="RadButtonTranslate"  BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding HistoryViewModel.IsEnabled}" Value="True">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding HistoryViewModel.IsEnabled}" Value="False">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style TargetType="telerik:RadSplitButton" x:Key="RadSplitButtonTranslate"  BasedOn="{StaticResource {x:Type telerik:RadSplitButton}}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding HistoryViewModel.IsEnabled}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding HistoryViewModel.IsEnabled}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style TargetType="telerik:RadButton" x:Key="ImageButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Focusable" Value="False" />
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Padding" Value="6,3" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="MinHeight" Value="0" />
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonleftToRight">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon
                            Height="22"
                            Kind="SwapHorizontalVariant"
                            Width="22" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonSoundFrom">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon
                            Height="12"
                            Kind="VolumeHigh"
                            Width="12" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonSoundTo">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon
                            Height="12"
                            Kind="VolumeHigh"
                            Width="12" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonSiteSource">
                <Setter Property="Content">
                    <Setter.Value>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="0"
                                Text="{telerik:LocalizableResource Key=SiteSourceButton}"
                                VerticalAlignment="Center" />
                            <wpf:MaterialIcon
                                Height="12"
                                Kind="Web"
                                Margin="5,0,0,0"
                                Width="12" />
                        </StackPanel>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonCopy">
                <Setter Property="Content">
                    <Setter.Value>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="0"
                                Text="{telerik:LocalizableResource Key=CopyButton}"
                                VerticalAlignment="Center" />
                            <wpf:MaterialIcon
                                Height="12"
                                Kind="ClipboardOutline"
                                Margin="5,0,0,0"
                                Width="12" />
                        </StackPanel>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style
                BasedOn="{StaticResource ImageButton}"
                TargetType="telerik:RadButton"
                x:Key="ImageButtonClear">
                <Setter Property="Content">
                    <Setter.Value>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="0"
                                Text="{telerik:LocalizableResource Key=ClearAll}"
                                VerticalAlignment="Center" />
                            <wpf:MaterialIcon
                                Height="12"
                                Kind="TrashCanOutline"
                                Margin="5,0,0,0"
                                Width="12" />
                        </StackPanel>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style TargetType="{x:Type richTextBoxEx1:RichTextBoxEx}" x:Key="ExRichTextBoxStyle">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="FontSize" Value="{Binding TranslationMainViewModel.FontSize}" />
                <Setter Property="FontFamily" Value="{Binding TranslationMainViewModel.FontFam}" />
            </Style>
        </ResourceDictionary>

    </UserControl.Resources>

    <UserControl.InputBindings>
        <KeyBinding Command="{Binding TranslationMainViewModel.ClearCommand}" Key="Escape" />
        <KeyBinding
            Command="{Binding TranslationMainViewModel.TranslateCommand}"
            Key="Enter"
            Modifiers="Control" />
        <KeyBinding Command="{Binding MainWindowViewModel.SpellCheckCommand}" Key="F7" />
    </UserControl.InputBindings>
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}" UseLayoutRounding="True">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200*" />
            <ColumnDefinition Width="200*" />
        </Grid.ColumnDefinitions>
        <telerik:RadDropDownButton
            CloseOnPopupMouseLeftButtonUp="True"
            Content="{Binding Path=TranslationMainViewModel.CurrentTranslateService}"
            Focusable="False"
            HorizontalAlignment="Left"
            Margin="10,5,0,0"
            VerticalAlignment="Top"
            Width="120">
            <telerik:RadDropDownButton.DropDownContent>
                <telerik:RadListBox
                    BorderThickness="0"
                    Height="Auto"
                    ItemsSource="{Binding Path=TranslationMainViewModel.TranslateServices}"
                    SelectedIndex="0"
                    SelectedItem="{Binding Path=TranslationMainViewModel.CurrentTranslateService, Mode=TwoWay}"
                    Width="118" />
            </telerik:RadDropDownButton.DropDownContent>
        </telerik:RadDropDownButton>
        <StackPanel
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Grid.Row="0"
            HorizontalAlignment="Center"
            Margin="0,5,30,0"
            Orientation="Horizontal"
            VerticalAlignment="Top">
            <telerik:RadDropDownButton
                Content="{Binding Path=TranslationMainViewModel.LanguageFromCurrent.SelectedNameFrom}"
                DropDownClosed="DropDownButtonFrom_OnDropDownClosed"
                Focusable="False"
                HorizontalContentAlignment="Left"
                FontSize="13"
                Margin="10,0,0,0"
                Width="220"
                x:Name="DropDownButtonFrom">
                <telerik:RadDropDownButton.DropDownContent>
                    <Grid Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <telerik:RadListBox
                            BorderThickness="1"
                            DisplayMemberPath="Name"
                            IsScrollIntoViewEnabled="True"
                            ItemsSource="{Binding Path=TranslationMainViewModel.LanguagesFrom}"
                            MaxHeight="400"
                            SelectedItem="{Binding Path=TranslationMainViewModel.LanguageFromCurrent, Mode=TwoWay}"
                            SelectionChanged="DropDownButtonFromOnSelectionChanged"
                            Width="220">
                            <telerik:RadListBox.ItemContainerStyle>
                                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                                    <Setter Property="Padding" Value="8,0,3,0" />
                                    <Setter Property="Margin" Value="0" />
                                    <Setter Property="MinHeight" Value="0" />
                                    <Setter Property="Height" Value="25" />
                                    <Setter Property="Visibility" Value="Visible" />
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsFavorite}" Value="False" />
                                                <Condition Binding="{Binding Path=DataContext.TranslationSettingsViewModel.TranslateOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="True" />
                                                <Condition Binding="{Binding Path=ViewOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="False" />
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </telerik:RadListBox.ItemContainerStyle>
                        </telerik:RadListBox>
                        <StackPanel Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}" Grid.Row="1">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding Path=DataContext.TranslationSettingsViewModel.TranslateOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="True" />
                                                <Condition Binding="{Binding Path=ViewOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="False" />
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible" />
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <telerik:RadMenuSeparatorItem />
                            <Button
                                Click="DropDownButtonFromLoadMoreClick"
                                Content="{telerik:LocalizableResource Key=TranslateSowAll}"
                                Focusable="False"
                                MinHeight="0"
                                Height="30"
                                Margin="0"
                                Padding="0" />
                        </StackPanel>
                    </Grid>
                </telerik:RadDropDownButton.DropDownContent>
            </telerik:RadDropDownButton>
            <telerik:RadButton
                Command="{Binding Path=TranslationMainViewModel.SwapLangCommand}"
                Height="30"
                Padding="0"
                Style="{StaticResource ImageButtonleftToRight}"
                VerticalAlignment="Center"
                Width="30"
                x:Name="LeftToRightButton" />
            <telerik:RadDropDownButton
                Content="{Binding Path=TranslationMainViewModel.LanguageToCurrent.SelectedNameTo}"
                DropDownClosed="DropDownButtonFrom_OnDropDownClosed"
                Focusable="False"
                HorizontalContentAlignment="Left"
                Margin="0"
                FontSize="13"
                Width="220"
                x:Name="DropDownButtonTo">
                <telerik:RadDropDownButton.DropDownContent>
                    <Grid Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <telerik:RadListBox
                            BorderThickness="1"
                            DisplayMemberPath="Name"
                            IsScrollIntoViewEnabled="True"
                            ItemsSource="{Binding Path=TranslationMainViewModel.LanguagesTo}"
                            MaxHeight="400"
                            SelectedItem="{Binding Path=TranslationMainViewModel.LanguageToCurrent, Mode=TwoWay}"
                            SelectionChanged="DropDownButtonFromOnSelectionChanged"
                            Width="220">
                            <telerik:RadListBox.ItemContainerStyle>
                                <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                                    <Setter Property="Padding" Value="8,0,3,0" />
                                    <Setter Property="Margin" Value="0" />
                                    <Setter Property="MinHeight" Value="0" />
                                    <Setter Property="Height" Value="25" />
                                    <Setter Property="Visibility" Value="Visible" />
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsFavorite}" Value="False" />
                                                <Condition Binding="{Binding Path=DataContext.TranslationSettingsViewModel.TranslateOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="True" />
                                                <Condition Binding="{Binding Path=ViewOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="False" />
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </telerik:RadListBox.ItemContainerStyle>
                        </telerik:RadListBox>
                        <StackPanel Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}" Grid.Row="1">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding Path=DataContext.TranslationSettingsViewModel.TranslateOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="True" />
                                                <Condition Binding="{Binding Path=ViewOnlyFavoriteLanguages, ElementName=TranslatorViewControl}" Value="False" />
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible" />
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <telerik:RadMenuSeparatorItem />
                            <Button
                                Click="DropDownButtonFromLoadMoreClick"
                                Content="{telerik:LocalizableResource Key=TranslateSowAll}"
                                Focusable="False"
                                MinHeight="0"
                                Height="30"
                                Margin="0"
                                Padding="0" />
                        </StackPanel>
                    </Grid>
                </telerik:RadDropDownButton.DropDownContent>
            </telerik:RadDropDownButton>
        </StackPanel>
        <telerik:RadButton
            Command="{Binding Path=TranslationMainViewModel.TranslateCommand}"
            Content="{telerik:LocalizableResource Key=TranslateButton}"
            Focusable="False"
            Grid.Column="1"
            Grid.Row="0"
            HorizontalAlignment="Right"
            Margin="0,5,10,0"
            Style="{StaticResource RadButtonTranslate}"
            VerticalAlignment="Top"
            Width="120" />
        <telerik:RadSplitButton
            Command="{Binding Path=TranslationMainViewModel.TranslateCommand}"
            Content="{telerik:LocalizableResource Key=TranslateButton}"
            Focusable="False"
            Grid.Column="1"
            Grid.Row="0"
            HorizontalAlignment="Right"
            Margin="0,5,10,0"
            Style="{StaticResource RadSplitButtonTranslate}"
            VerticalAlignment="Top"
            x:Name="RadSplitButtonTranslate">
            <telerik:RadSplitButton.DropDownContent>
                <telerik:RadListBox
                    ItemsSource="{Binding HistoryViewModel.AllHistoryItems}"
                    MaxHeight="500"
                    MaxWidth="400"
                    MinWidth="120"
                    MouseDoubleClick="ListBoxHist_OnMouseDoubleClick"
                    SelectedValuePath="Text"
                    SelectionMode="Single">
                    <telerik:RadListBox.ItemTemplate>
                        <DataTemplate  DataType="{x:Type dataModel:HistoryTranslationModel}">
                            <TextBlock MinHeight="0" Text="{Binding Path=ShortText}">
                                <ToolTipService.ToolTip>
                                    <StackPanel>
                                        <TextBlock Text="{Binding Path=TextPrev}" />
                                    </StackPanel>
                                </ToolTipService.ToolTip>
                            </TextBlock>
                        </DataTemplate>
                    </telerik:RadListBox.ItemTemplate>
                    <telerik:RadListBox.ItemContainerStyle>
                        <Style TargetType="telerik:RadListBoxItem"  BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                            <Setter Property="Padding" Value="8,7,0,7" />
                            <Setter Property="MinHeight" Value="0" />
                        </Style>
                    </telerik:RadListBox.ItemContainerStyle>
                </telerik:RadListBox>
            </telerik:RadSplitButton.DropDownContent>
        </telerik:RadSplitButton>

        <telerik:RadBusyIndicator
            BusyContent=""
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Grid.Row="1"
            Grid.ZIndex="1"
            IsIndeterminate="True"
            Margin="0,0,0,0"
            Padding="0,0,17,0"
            Style="{StaticResource ProgressIndicatorVisibility}" />

        <Grid
            Grid.Column="0"
            Grid.Row="1"
            Margin="0,5,0,0">

            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="25" />
            </Grid.RowDefinitions>

            <richTextBoxEx1:RichTextBoxEx
                Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                Block.LineHeight="1.5"
                CaretBrush="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                Margin="10,2,20,0"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                Style="{StaticResource ExRichTextBoxStyle}"
                Text="{Binding TranslationMainViewModel.SourceText}"
                TextChanged="RichTextBoxSourceText_OnTextChanged"
                x:Name="richTextBoxSourceText">
                <richTextBoxEx1:RichTextBoxEx.TextFormatter>
                    <formatters1:StandartFormatter />
                </richTextBoxEx1:RichTextBoxEx.TextFormatter>
                <richTextBoxEx1:RichTextBoxEx.CommandBindings>
                    <CommandBinding Command="{x:Static ApplicationCommands.Copy}" Executed="CommandCopyrichTextBoxSourceText_Executed" />
                    <CommandBinding Command="{x:Static ApplicationCommands.Paste}" Executed="CommandPasteTextBoxSourceText_Executed" />
                    <CommandBinding Command="{x:Static ApplicationCommands.Cut}" Executed="CommandCutrichTextBoxSourceText_Executed" />
                </richTextBoxEx1:RichTextBoxEx.CommandBindings>
                <richTextBoxEx1:RichTextBoxEx.Effect>
                    <DropShadowEffect
                        BlurRadius="20"
                        Color="{Binding Source={x:Static telerik:Windows11Palette.Palette}, Path=PrimaryForegroundColor}"
                        Direction="-150"
                        Opacity="0.10"
                        ShadowDepth="5" />
                </richTextBoxEx1:RichTextBoxEx.Effect>
            </richTextBoxEx1:RichTextBoxEx>
            <StackPanel
                Grid.Column="0"
                Grid.Row="1"
                Orientation="Horizontal"
                VerticalAlignment="Center"
                Visibility="{Binding TranslationMainViewModel.IsVisibleResultPanel, Converter={StaticResource BoolToVis}}">
                <TextBlock
                    Margin="20,0,0,0"
                    Text="{Binding Path=TranslationMainViewModel.FromLang}"
                    VerticalAlignment="Center" />
                <telerik:RadButton
                    Command="{Binding Path=TranslationMainViewModel.ListenTransalatedSourseCommand}"
                    Margin="0,0,0,0"
                    Style="{StaticResource ImageButtonSoundFrom}"
                    ToolTip="{telerik:LocalizableResource Key=SoundButton}"
                    VerticalAlignment="Center" />
                <TextBlock
                    Margin="10,0,0,0"
                    Text="{Binding Path=TranslationMainViewModel.ToLang}"
                    VerticalAlignment="Center" />
                <telerik:RadButton
                    Command="{Binding Path=TranslationMainViewModel.ListenTransalatedResultCommand}"
                    Margin="0,0,0,0"
                    Style="{StaticResource ImageButtonSoundTo}"
                    ToolTip="{telerik:LocalizableResource Key=SoundButton}" />

            </StackPanel>
        </Grid>
        <Grid
            Grid.Column="1"
            Grid.Row="1"
            Margin="0,5,0,0">

            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="25" />
            </Grid.RowDefinitions>

            <richTextBoxEx1:RichTextBoxEx
                Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                Block.LineHeight="1.5"
                CaretBrush="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                IsUndoEnabled="False"
                Margin="0,2,10,0"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                Style="{StaticResource ExRichTextBoxStyle}"
                Text="{Binding TranslationMainViewModel.TranslatedText}"
                x:Name="richTextBoxResultText">
                <richTextBoxEx1:RichTextBoxEx.TextFormatter>
                    <formatters1:StandartFormatter />
                </richTextBoxEx1:RichTextBoxEx.TextFormatter>
                <richTextBoxEx1:RichTextBoxEx.CommandBindings>
                    <CommandBinding Command="{x:Static ApplicationCommands.Copy}" Executed="CommandCopyrichTextBoxResultText_Executed" />
                    <CommandBinding Command="{x:Static ApplicationCommands.Cut}" Executed="CommandCutrichTextBoxResultText_Executed" />
                </richTextBoxEx1:RichTextBoxEx.CommandBindings>
                <richTextBoxEx1:RichTextBoxEx.Effect>
                    <DropShadowEffect
                        BlurRadius="20"
                        Color="{Binding Source={x:Static telerik:Windows11Palette.Palette}, Path=PrimaryForegroundColor}"
                        Direction="-50"
                        Opacity="0.10"
                        ShadowDepth="5" />
                </richTextBoxEx1:RichTextBoxEx.Effect>
            </richTextBoxEx1:RichTextBoxEx>

            <StackPanel
                Grid.Column="0"
                Grid.Row="1"
                HorizontalAlignment="Right"
                Margin="0,0,10,0"
                Orientation="Horizontal"
                VerticalAlignment="Center"
                Visibility="{Binding TranslationMainViewModel.IsVisibleResultPanel, Converter={StaticResource BoolToVis}}">

                <telerik:RadButton
                    Command="{Binding Path=TranslationMainViewModel.SiteSourceCommand}"
                    Margin="0,0,0,0"
                    Style="{StaticResource ImageButtonSiteSource}"
                    ToolTip="{telerik:LocalizableResource Key=SiteSourceTextButton}" />
                <telerik:RadButton
                    Command="{Binding Path=TranslationMainViewModel.CopyTransalatedCommand}"
                    Margin="5,0,0,0"
                    Style="{StaticResource ImageButtonCopy}"
                    ToolTip="Ctrl+C" />
                <telerik:RadButton
                    Command="{Binding Path=TranslationMainViewModel.ClearCommand}"
                    Margin="5,0,0,0"
                    Style="{StaticResource ImageButtonClear}"
                    ToolTip="Esc" />
            </StackPanel>

        </Grid>
    </Grid>
</UserControl>