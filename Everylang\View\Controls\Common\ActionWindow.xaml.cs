﻿using System;
using System.Windows;
using System.Windows.Controls;

namespace Everylang.App.View.Controls.Common
{
    /// <summary>
    /// Interaction logic for ActionWindow.xaml
    /// </summary>
    internal partial class ActionWindow
    {
        internal Action? CloseActionWindow;
        internal ActionWindow()
        {
            InitializeComponent();
        }

        internal void Show(string title, UserControl userControl)
        {
            Header = title;
            Grid.SetRow(userControl, 0);
            GridMain.Children.Add(userControl);
            ShowWindow(true);
        }

        private void CloseClick(object sender, RoutedEventArgs e)
        {
            CloseActionWindow?.Invoke();
            Close();
        }
    }
}
