﻿using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.SpellCheck;
using Everylang.App.Utilities;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Timer = System.Timers.Timer;

namespace Everylang.App.View.Controls.SpellCheck
{

    public partial class SpellCheckWindow
    {
        private readonly Timer _timer;
        private SpellCheckDictionary? _spellCheckDictionaryCurrent;
        public SpellCheckWindow()
        {
            InitializeComponent();
            _timer = new Timer();
            _timer.Interval = 2000;
            _timer.Elapsed += (_, _) =>
            {
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);

            };
            FirstLoad = false;
            _spellCheckDictionary = new List<SpellCheckDictionary>();
            TrueWordItems = new ObservableCollection<string>();
            this.Height = this.MinHeight;
            this.Width = this.MinWidth;
        }

        internal async void Show(string? text)
        {
            Show();
            DoEvents();
            IsVisibleGrid = false;
            IsVisibleProgressRing = true;
            LanguageToolSpellCheck orfoSpellCheck = new LanguageToolSpellCheck();
            if (text != null)
            {
                SpellCheckResult webResult = await orfoSpellCheck.Start(text);
                SpellCheckSuccess(webResult);
            }
        }

        private async void SpellCheckSuccess(SpellCheckResult webResult)
        {
            Error = webResult.WithError;
            await Task.Delay(500);
            if (!webResult.WithError)
            {
                FirstLoad = true;
                AllCheck = false;
                IsOk = webResult.IsOk;
                IsVisibleGrid = false;
                _spellCheckDictionary = webResult.SpellCheckDictionary;
                if (!IsOk)
                {
                    this.Height = 350;
                    this.Width = 500;
                }
                else
                {
                    if (SettingsManager.Settings.SpellCheckCloseByTimer)
                    {
                        _timer.Start();
                    }
                    this.Deactivated += SpellCheckWindow_Deactivated;
                }
                DoEvents();
                SourceText = webResult.SourceText;
            }
            else
            {
                IsVisibleProgressRing = false;
            }
        }

        void SpellCheckWindow_Deactivated(object? sender, EventArgs e)
        {
            _timer.Stop();
            if (IsVisible)
            {
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
            }
        }

        private void BorderMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
        }

        private void RichTextBoxSourceText_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            IsVisibleProgressRing = false;
            IsVisibleGrid = !IsOk;
            if (FirstLoad && !IsOk)
            {
                FirstLoad = false;
                MarkAllKeys();
                if (_spellCheckDictionary != null && _spellCheckDictionary.Count > 0)
                    _spellCheckDictionaryCurrent = _spellCheckDictionary[0];
                MarkCurrentKey();
            }
        }

        void MarkAllKeys()
        {
            _indexCurrentPoint = 0;
            if (_spellCheckDictionary != null)
                for (int i = 0; i < _spellCheckDictionary.Count; i++)
                {
                    var spellCheckDictionary = _spellCheckDictionary[i];
                    var checkDictionariesChildList = new List<SpellCheckDictionary>();
                    for (int j = i + 1; j < _spellCheckDictionary.Count; j++)
                    {
                        if (_spellCheckDictionary[j].Pos != spellCheckDictionary.Pos)
                        {
                            checkDictionariesChildList.Add(_spellCheckDictionary[j]);
                        }
                    }

                    spellCheckDictionary.ChilDictionaries = checkDictionariesChildList;
                    var start = SpellTextBox.Document.ContentStart;
                    var textrange = new TextRange(GetPoint(start, spellCheckDictionary.Pos),
                        GetPoint(start, spellCheckDictionary.Pos + spellCheckDictionary.Len));
                    spellCheckDictionary.TextRange = textrange;
                }
        }

        private int _indexCurrentPoint;
        private TextPointer? GetPoint(TextPointer start, int x)
        {
            var ret = start;
            var i = _indexCurrentPoint;
            while (true)
            {
                _indexCurrentPoint = i;
                DoEvents();
                var pos = ret.GetPositionAtOffset(i, LogicalDirection.Forward);
                if (pos != null)
                {
                    string stringSoFar = new TextRange(ret, pos).Text;
                    if (stringSoFar.Length == x)
                        break;
                }
                i++;
                if (ret.GetPositionAtOffset(i, LogicalDirection.Forward) == null)
                {
                    return ret.GetPositionAtOffset(i - 1, LogicalDirection.Forward);
                }

            }
            ret = ret.GetPositionAtOffset(i, LogicalDirection.Forward);
            return ret;
        }

        internal static void DoEvents()
        {
            Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                                                  new Action(delegate { }));
        }

        void MarkCurrentKey()
        {
            AllCheck = false;
            var textRange = new TextRange(SpellTextBox.Document.ContentStart, SpellTextBox.Document.ContentEnd);
            textRange.ApplyPropertyValue(TextElement.ForegroundProperty, FindResource(Windows11ResourceKey.PrimaryForegroundBrush));
            TrueWordItems.Clear();
            if (_spellCheckDictionaryCurrent != null)
            {
                _spellCheckDictionaryCurrent.SetColorToKey();
                foreach (var suggestion in _spellCheckDictionaryCurrent.Suggestions)
                {
                    TrueWordItems.Add(suggestion);
                }

                ListBoxTrueWordItems.ItemsSource = TrueWordItems;
                if (_spellCheckDictionaryCurrent.Suggestions.Count > 0)
                {
                    LabelOptions.Content = LocalizationManager.GetString("LabelOptions");
                    ListBoxTrueWordItems.SelectedItem = null;
                    ListBoxTrueWordItems.SelectedItem = _spellCheckDictionaryCurrent.Suggestions[0];
                    BReplace.IsEnabled = true;
                    BReplaceAll.IsEnabled = true;
                    ListBoxTrueWordItems.IsEnabled = true;
                }
                else
                {
                    LabelOptions.Content = LocalizationManager.GetString("LabelOptionsNo");
                    ListBoxTrueWordItems.IsEnabled = false;
                    BReplace.IsEnabled = false;
                    BReplaceAll.IsEnabled = false;
                }
                TextBoxCurrentWord.Text = _spellCheckDictionaryCurrent.Key;

                var start = _spellCheckDictionaryCurrent.TextRange.Start.GetCharacterRect(LogicalDirection.Forward);
                var end = _spellCheckDictionaryCurrent.TextRange.End.GetCharacterRect(LogicalDirection.Forward);
                try
                {
                    SpellTextBox.ScrollToVerticalOffset((start.Top + end.Bottom - SpellTextBox.ViewportHeight) / 2 +
                                                        SpellTextBox.VerticalOffset);
                }
                catch (Exception)
                {
                    // ignore
                }
            }
            else
            {
                LabelOptions.Content = LocalizationManager.GetString("LabelOptionsEnd");
                ListBoxTrueWordItems.IsEnabled = false;
                BReplace.IsEnabled = false;
                BReplaceAll.IsEnabled = false;
                AllCheck = true;
                TextBoxCurrentWord.Text = "";
            }
            //OnPropertyChanged(nameof(TrueWordItems));
        }

        private void bSkip_Click(object sender, RoutedEventArgs e)
        {
            _spellCheckDictionaryCurrent?.SetStatus(true, false);
            _spellCheckDictionaryCurrent = _spellCheckDictionary?.Find(x => !x.Ok);
            MarkCurrentKey();
            ButtonBack.IsEnabled = true;
        }

        private void bSkipAll_Click(object sender, RoutedEventArgs e)
        {
            _spellCheckDictionaryCurrent?.SetStatus(true, true);
            _spellCheckDictionaryCurrent = _spellCheckDictionary?.Find(x => !x.Ok);
            MarkCurrentKey();
            ButtonBack.IsEnabled = true;
        }

        private void bReplace_Click(object sender, RoutedEventArgs e)
        {
            _spellCheckDictionaryCurrent?.SetResultText(false);
            _spellCheckDictionaryCurrent?.SetStatus(true, false);

            _spellCheckDictionaryCurrent = _spellCheckDictionary?.Find(x => !x.Ok);
            MarkCurrentKey();
            ButtonBack.IsEnabled = true;
        }

        private void bReplaceAll_Click(object sender, RoutedEventArgs e)
        {
            _spellCheckDictionaryCurrent?.SetResultText(true);
            _spellCheckDictionaryCurrent?.SetStatus(true, true);
            _spellCheckDictionaryCurrent = _spellCheckDictionary?.Find(x => !x.Ok);
            MarkCurrentKey();
            ButtonBack.IsEnabled = true;
        }

        private void textBoxCurrentWord_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_spellCheckDictionaryCurrent != null && TextBoxCurrentWord.Text != _spellCheckDictionaryCurrent.Key)
            {
                _spellCheckDictionaryCurrent.ResultKey = TextBoxCurrentWord.Text;
                ListBoxTrueWordItems.IsEnabled = false;
                BReplace.IsEnabled = true;
                BReplaceAll.IsEnabled = true;
            }
            else
            {
                ListBoxTrueWordItems.IsEnabled = true;
            }
        }

        private void listBoxTrueWordItems_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_spellCheckDictionaryCurrent != null)
                _spellCheckDictionaryCurrent.ResultKey = (string)ListBoxTrueWordItems.SelectedItem;
        }

        private void ButtonPlace(object sender, RoutedEventArgs e)
        {
            if (IsFromMainWindow)
            {
                if (SpellTextBox.Text != null)
                    VMContainer.Instance.TranslationMainViewModel.SourceText = SpellTextBox.Text;
            }
            else
            {
                Topmost = !Topmost;
                SendText.SendStringByPaste(SpellTextBox.Text, true);
            }
            Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
        }

        private void ButtonCopy(object sender, RoutedEventArgs e)
        {
            ClipboardOperations.SetText(SpellTextBox.Text);
            //            VMContainer.Instance.ClipboardViewModel.AddNewClipboardData();
        }

        private void ButtonBackEvent(object sender, RoutedEventArgs e)
        {

            SpellCheckDictionary? spellCheckDictionary = _spellCheckDictionary?.FindLast(x => x.Ok);
            if (spellCheckDictionary != null)
            {
                spellCheckDictionary.Undo();
                _spellCheckDictionaryCurrent = spellCheckDictionary;
            }

            MarkCurrentKey();

            if (_spellCheckDictionary != null && _spellCheckDictionary.FindLast(x => x.Ok) == null)
            {
                ButtonBack.IsEnabled = false;
            }

        }

        private void window_Closing(object sender, CancelEventArgs e)
        {
            SourceText = "";
            CurrentWord = "";
            IsVisibleProgressRing = true;
            IsVisibleGrid = false;
            IsOk = false;
            FirstLoad = false;
            AllCheck = false;
            Error = false;
            _spellCheckDictionary = new List<SpellCheckDictionary>();
            TrueWordItems = new ObservableCollection<string>();
            Height = MinHeight;
            Width = MinWidth;
        }


        // --------------------------------------------------------------------------------------------

        internal static readonly DependencyProperty IsFromMainWindowProperty =
            DependencyProperty.Register("IsFromMainWindow",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty CurrentWordProperty =
            DependencyProperty.Register("CurrentWord",
                typeof(string),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty SourceTextProperty =
            DependencyProperty.Register("SourceText",
                typeof(string),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty IsOkProperty =
            DependencyProperty.Register("IsOk",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty AllCheckProperty =
            DependencyProperty.Register("AllCheck",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty IsVisibleProgressRingProperty =
            DependencyProperty.Register("IsVisibleProgressRing",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty IsVisibleGridProperty =
            DependencyProperty.Register("IsVisibleGrid",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty ErrorProperty =
            DependencyProperty.Register("Error",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal static readonly DependencyProperty ErrorTooLongTextProperty =
            DependencyProperty.Register("ErrorTooLongText",
                typeof(bool),
                typeof(SpellCheckWindow));

        internal bool IsFromMainWindow
        {
            get { return (bool)GetValue(IsFromMainWindowProperty); }
            set { SetValue(IsFromMainWindowProperty, value); OnPropertyChanged(); }
        }

        internal string CurrentWord
        {
            get { return (string)GetValue(CurrentWordProperty); }
            set { SetValue(CurrentWordProperty, value); OnPropertyChanged(); }
        }

        internal string SourceText
        {
            get { return (string)GetValue(SourceTextProperty); }
            set { SetValue(SourceTextProperty, value); OnPropertyChanged(); }
        }

        internal bool IsOk
        {
            get { return (bool)GetValue(IsOkProperty); }
            set { SetValue(IsOkProperty, value); OnPropertyChanged(); }
        }

        internal bool AllCheck
        {
            get { return (bool)GetValue(AllCheckProperty); }
            set { SetValue(AllCheckProperty, value); OnPropertyChanged(); }
        }

        internal bool IsVisibleProgressRing
        {
            get { return (bool)GetValue(IsVisibleProgressRingProperty); }
            set { SetValue(IsVisibleProgressRingProperty, value); OnPropertyChanged(); }
        }

        internal bool IsVisibleGrid
        {
            get { return (bool)GetValue(IsVisibleGridProperty); }
            set { SetValue(IsVisibleGridProperty, value); OnPropertyChanged(); }
        }

        internal bool Error
        {
            get { return (bool)GetValue(ErrorProperty); }
            set { SetValue(ErrorProperty, value); OnPropertyChanged(); }
        }

        internal bool ErrorTooLongText
        {
            get { return (bool)GetValue(ErrorTooLongTextProperty); }
            set { SetValue(ErrorTooLongTextProperty, value); OnPropertyChanged(); }
        }

        public ObservableCollection<string> TrueWordItems { get; set; }
        private List<SpellCheckDictionary>? _spellCheckDictionary;
        internal bool FirstLoad;

        public event PropertyChangedEventHandler? PropertyChanged;
        private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
}
