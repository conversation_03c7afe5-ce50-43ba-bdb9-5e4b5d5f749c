﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using Application = System.Windows.Application;

namespace Everylang.App.License
{
    class CheckHash
    {
        internal static async void Start()
        {
            await Task.Delay(new Random().Next(1800000, 14400000));
            _ = CheckHeshStart();
        }

        private static async Task CheckHeshStart()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                List<string> versionFile = new List<string?>
                {
                    assembly.GetName().Version?.ToString().Replace(".", "_"),
                    assembly.Location,
                    SettingsManager.Settings.HashFromSite,
                    SettingsManager.Settings.LastVersionForCheckHash,
                };

                List<string> funcList = await GetHash(versionFile!);
                if (funcList.Count == 2)
                {
                    SettingsManager.Settings.LastVersionForCheckHash = funcList[0];
                    SettingsManager.Settings.HashFromSite = funcList[1];
                }
            }
            catch
            {
                // Ignore
            }
        }

        private static async Task<List<string>> GetHash(List<string> srd)
        {
            return await Task.Run(() =>
            {
                List<string> result = new List<string>();
                try
                {
                    var currentVersion = srd[0];
                    var exePath = srd[1];
                    var hashFromSite = srd[2];
                    var lastVersionForCheckHash = srd[3];

                    bool check = hashFromSite == "";
                    if (!check)
                    {
                        check = lastVersionForCheckHash != currentVersion;
                    }

                    if (check)
                    {

                        Uri updateLocation = new Uri("https://everylang.net/dist/hash/" + currentVersion);

                        string hashFile = "";

                        var client = new RestClient(new RestClientOptions()
                        {
                            Proxy = NetLib.GetProxy()
                        });
                        var request = new RestRequest(updateLocation);
                        var response = client.Execute(request);

                        if (response.ErrorException != null)
                        {
                            throw response.ErrorException;
                        }

                        if (response.Content != null) hashFile = response.Content;

                        if (!string.IsNullOrEmpty(hashFile) && !hashFile.ToLower().Contains("body"))
                        {
                            hashFromSite = hashFile;
                            lastVersionForCheckHash = currentVersion;
                            result.Add(hashFromSite);
                            result.Add(lastVersionForCheckHash);
                        }

                    }

                    string hashApp;
                    using (var md5 = MD5.Create())
                    {
                        using (var stream = File.OpenRead(exePath))
                        {
                            hashApp = BitConverter.ToString(md5.ComputeHash(stream)).Replace("-", "").ToLower();
                        }
                    }

                    if (hashFromSite != "" && hashFromSite != hashApp)
                    {
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            (ThreadStart)delegate
                            {
                                if (Application.Current.MainWindow != null) Application.Current.MainWindow.Close();
                            });
                    }
                }
                catch
                {
                    // ignore
                }
                return result;
            });
        }
    }
}
