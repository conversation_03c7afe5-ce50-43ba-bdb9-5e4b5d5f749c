﻿<Popup  x:Class="Everylang.App.View.Controls.AutoSwitch.AutoSwitchAcceptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        Placement="Absolute" StaysOpen="True" Focusable="False" AllowsTransparency="True" x:ClassModifier="internal">
        <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <TextBlock VerticalAlignment="Center" Focusable="False" Margin="5" FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitchAcceptText}" Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"/>
        </Grid>
</Popup>
