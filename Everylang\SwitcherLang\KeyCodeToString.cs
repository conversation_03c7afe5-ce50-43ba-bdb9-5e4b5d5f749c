﻿using Everylang.App.HookManager.GlobalHooks;
using System;
using System.Collections.Generic;
using Vanara.PInvoke;

namespace Everylang.App.SwitcherLang
{
    class KeyCodeToString
    {

        private static readonly Dictionary<IntPtr, uint> LastVkCode = new();
        private static readonly Dictionary<IntPtr, uint> LastScanCode = new();
        private static readonly Dictionary<IntPtr, byte[]> LastKeyState = new();
        private static readonly Dictionary<IntPtr, bool> LastIsDead = new();

        /// <summary>
        /// Convert VKCode to Unicode.
        /// <remarks>isKeyDown is required for because of keyboard state inconsistencies!</remarks>
        /// </summary>
        /// <param name="key"></param>
        /// <param name="isKeyDown">Is the key down event?</param>
        /// <param name="hkl"></param>
        /// <returns>String representing single unicode character.</returns>
        internal static string VkCodeToString(GlobalKeyEventArgs key, bool isKeyDown, IntPtr hkl)
        {

            if (!LastVkCode.ContainsKey(hkl))
            {
                LastVkCode.Add(hkl, 0);
            }
            if (!LastScanCode.ContainsKey(hkl))
            {
                LastScanCode.Add(hkl, 0);
            }
            if (!LastKeyState.ContainsKey(hkl))
            {
                LastKeyState.Add(hkl, new byte[256]);
            }
            if (!LastIsDead.ContainsKey(hkl))
            {
                LastIsDead.Add(hkl, false);
            }
            // ToUnicodeEx needs StringBuilder, it populates that during execution.
            System.Text.StringBuilder sbString = new System.Text.StringBuilder(10);

            bool isDead = false;

            byte[] bKeyState = new byte[256];
            if (!User32.GetKeyboardState(bKeyState))
                return string.Empty;

            // Keyboard state goes inconsistent if this is not in place. In other words, we need to call above commands in UP events also.
            if (!isKeyDown)
                return "";

            // Converts the VKCode to unicode
            int relevantKeyCountInBuffer = User32.ToUnicodeEx((uint)key.KeyCode, (uint)key.HardwareScanCode, bKeyState, sbString, sbString.Capacity, 0, hkl);

            string ret = "";

            switch (relevantKeyCountInBuffer)
            {
                // Dead keys (^,`...)
                case -1:
                    isDead = true;

                    // We must clear the buffer because ToUnicodeEx messed it up, see below.
                    ClearKeyboardBuffer((uint)key.KeyCode, (uint)key.HardwareScanCode, hkl);
                    break;

                case 0:
                    break;

                // Single character in buffer
                case 1:
                    ret = sbString[0].ToString();
                    break;

                default:
                    ret = sbString.ToString().Substring(0, 2);
                    break;
            }

            // We inject the last dead key back, since ToUnicodeEx removed it.
            // More about this peculiar behavior see e.g: 
            //   http://www.experts-exchange.com/Programming/System/Windows__Programming/Q_23453780.html
            //   http://blogs.msdn.com/michkap/archive/2005/01/19/355870.aspx
            //   http://blogs.msdn.com/michkap/archive/2007/10/27/5717859.aspx
            if (LastVkCode[hkl] != 0 && LastIsDead[hkl])
            {
                System.Text.StringBuilder sbTemp = new System.Text.StringBuilder(5);
                User32.ToUnicodeEx(LastVkCode[hkl], LastScanCode[hkl], LastKeyState[hkl], sbTemp, sbTemp.Capacity, 0, hkl);

                if (!string.IsNullOrEmpty(ret))
                {
                    LastVkCode[hkl] = 0;
                    LastIsDead[hkl] = false;
                }

                return ret;
            }

            // Save these
            LastScanCode[hkl] = (uint)key.HardwareScanCode;
            LastVkCode[hkl] = (uint)key.KeyCode;
            LastIsDead[hkl] = isDead;
            LastKeyState[hkl] = (byte[])bKeyState.Clone();

            return ret;
        }

        private static void ClearKeyboardBuffer(uint vk, uint sc, IntPtr hkl)
        {
            System.Text.StringBuilder sb = new System.Text.StringBuilder(10);

            int rc;
            do
            {
                byte[] lpKeyStateNull = new byte[256];
                rc = User32.ToUnicodeEx(vk, sc, lpKeyStateNull, sb, sb.Capacity, 0, hkl);
            } while (rc < 0);
        }
    }
}
