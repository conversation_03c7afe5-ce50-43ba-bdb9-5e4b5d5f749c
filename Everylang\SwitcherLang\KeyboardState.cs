﻿using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Vanara.PInvoke;
using WindowsInput;

namespace Everylang.App.SwitcherLang
{
    internal class KeyboardState
    {
        private static readonly byte[] DistinctVirtualKeys = Enumerable.Range(0, 256)
            .Select(KeyInterop.KeyFromVirtualKey)
            .Where(item => item != Key.None)
            .Distinct()
            .Select(item => (byte)KeyInterop.VirtualKeyFromKey(item))
            .ToArray();

        internal static List<Key> GetDownKeys()
        {
            var keyboardState = new byte[256];
            User32.GetKeyboardState(keyboardState);

            var downKeys = new List<Key>();
            for (var index = 0; index < DistinctVirtualKeys.Length; index++)
            {
                var virtualKey = DistinctVirtualKeys[index];
                if ((keyboardState[virtualKey] & 0x80) != 0)
                {
                    downKeys.Add(KeyInterop.KeyFromVirtualKey(virtualKey));
                }
            }

            return downKeys;
        }

        internal static void ReleaseAllKeys(bool shift = false)
        {
            var keysList = GetDownKeys();
            var sim = new InputSimulator();
            foreach (var key in keysList)
            {
                if (shift)
                {
                    if (key != Key.LeftShift)
                    {
                        sim.Keyboard.KeyUp((VirtualKeyCode)KeyInterop.VirtualKeyFromKey(key)).Sleep(5);
                    }
                }
                else
                {
                    sim.Keyboard.KeyUp((VirtualKeyCode)key).Sleep(5);
                }

            }
        }
    }
}
