﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.View.Controls.Translator;
using Everylang.Common.Utilities;
using NHotkey;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

namespace Everylang.App.Translator
{
    class TranslateManager : IDisposable
    {
        private static TranslateManager? _instance;

        internal static TranslateManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new TranslateManager();
                }
                return _instance;
            }
        }

        internal TranslateManager()
        {
            HookCallBackMouseDown.CallbackEventHandler += HookManagerSwitcherMouseDown;
            HookCallBackMouseUp.CallbackEventHandler += HookManagerSwitcherMouseUp;
            HookCallBackMouseMove.CallbackEventHandler += HookManagerMouseMove;
            _translationWindows = new List<TranslationWindow>();
        }



        internal void Start()
        {
            if (SettingsManager.Settings.TranslateIsOn)
            {
                StartTranslater();
            }
        }


        private void StartTranslater()
        {
            //Translation.GetBingAppId();
            GlobalEventsApp.EventUniWindowTranslate += ShowTranslationWindow;
            if (SettingsManager.Settings.TranslateShowMiniFormDoubleCtrl)
            {
                SettingsManager.Settings.TranslateShowMiniFormShortcut = "doubleLeftOrRightCtrl";
                SettingsManager.Settings.TranslateShowMiniFormDoubleCtrl = false;
            }
            if (!SettingsManager.Settings.TranslateShowMiniFormDoubleCtrl)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.TranslateShowMiniFormShortcut), SettingsManager.Settings.TranslateShowMiniFormShortcut, PressedFloating);
            }
        }


        #region Перевод простым выделением

        private Point _mouseLocation;
        private Point _storedMouseLocation;
        private DateTime _lasTimeClick;
        private int _clicks;
        internal bool IsMouseSelected;
        private readonly uint _mSystemDoubleClickTime = User32.GetDoubleClickTime();

        private void HookManagerMouseMove(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            _mouseLocation = new Point(e.PointerPos.X, e.PointerPos.Y);
        }

        private void HookManagerSwitcherMouseUp(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (e.Button == GHMouseButtons.Left)
            {
                IsMouseSelected = _storedMouseLocation != _mouseLocation || _clicks == 2;
            }

            if (SettingsManager.Settings.TranslateIsOn)
            {
                if (SettingsManager.Settings.TranslateShowMiniFormAlwaysWhenSelectText && IsMouseSelected)
                {
                    ShowTranslation();
                }
            }
        }

        private void HookManagerSwitcherMouseDown(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (e.Button == GHMouseButtons.Left)
            {
                _storedMouseLocation = new Point(e.PointerPos.X, e.PointerPos.Y);

                if ((DateTime.Now - _lasTimeClick).TotalMilliseconds <= _mSystemDoubleClickTime && _clicks == 1)
                {
                    _clicks = 2;
                }
                else
                {
                    _clicks = 1;

                }
                _lasTimeClick = DateTime.Now;
            }
        }

        [DllImport("user32")]
        internal static extern int GetDoubleClickTime();
        #endregion


        internal void PressedFloating(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            ShowTranslation();
        }

        private void ShowTranslation()
        {
            ForegroundWindow.StoreForegroundWindow();
            string? text = ClipboardOperations.GetSelectionText();

            if (text?.Trim(' ').Replace("\r\n", "") != "")
            {
                ShowTranslationWindow(text);
            }
        }

        internal async void ShowTranslationFromImage(Bitmap bitmap)
        {
            var ocrEngine = new OcrWorker();
            var text = await ocrEngine.ReadImage(bitmap, SettingsManager.Settings.OcrLangsList);
            ShowTranslationWindow(text);
        }

        internal void ShowTranslationWindow(string? obj)
        {
            ShowTranslationWindow(obj, null);
        }

        private DateTime _dateTime;
        private readonly List<TranslationWindow> _translationWindows;

        internal void ShowTranslationWindow(string? text, TranslationWindow? translationWindow)
        {
            if ((DateTime.Now - _dateTime).TotalSeconds < 1)
            {
                return;
            }
            if (translationWindow == null)
            {
                translationWindow = new TranslationWindow();
                _translationWindows.Add(translationWindow);

                translationWindow.IsOpen = true;
                translationWindow.Closed += (sender, _) =>
                {
                    if (sender != null) _translationWindows.Remove((TranslationWindow)sender);
                };
            }
            _dateTime = DateTime.Now;
            var sel = _translationWindows.FirstOrDefault(x =>
                x.IsOpen && x.IsMouseOver && x.resTextBox.Selection.Text != "");
            if (sel != null)
            {
                text = sel.resTextBox.Selection.Text;
            }

            translationWindow.resTextBox.Clear();
            translationWindow.Translate(text);
        }

        internal void Stop()
        {
            TranslaterStop();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }


        private void TranslaterStop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.TranslateShowMiniFormShortcut));
            GlobalEventsApp.EventUniWindowTranslate -= ShowTranslationWindow;
        }


        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }


    }
}
