<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.FormatProviders.Pdf</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider">
            <summary>
            Represents a format provider that can export PDF documents from a <see cref="T:Telerik.Windows.Documents.Model.RadDocument"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.MeasuringPanel">
            <summary>
            Gets the name of the panel used by this format provider to arrange the document contents.
            </summary>
            <value>
            The panel.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.Name">
            <summary>
            Gets the name of the specific format provider.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.SupportedExtensions">
            <summary>
            Gets the extensions supported by this format provider.
            </summary>
            <value>
            The supported extensions.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.CanImport">
            <summary>
            Gets a value indicating whether this instance can import.
            </summary>
            <value>
            <c>true</c> if this instance can import; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.CanExport">
            <summary>
            Gets a value indicating whether this instance can export.
            </summary>
            <value>
            <c>true</c> if this instance can export; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.ExportSettings">
            <summary>
            Gets or sets the settings which will be used while exporting a document.
            </summary>
            <value>
            The export settings.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.Import(System.IO.Stream)">
            <summary>
            Imports the specified <see cref="T:System.IO.Stream" /> into a <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            <para>Not supported in <see cref="T:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider"/>.</para>
            </summary>
            <param name="input">The <see cref="T:System.IO.Stream" /> containing the document data.</param>
            <returns>
            The generated <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Pdf.PdfFormatProvider.Export(Telerik.Windows.Documents.Model.RadDocument,System.IO.Stream)">
            <summary>
            Exports the specified <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The <see cref="T:System.IO.Stream" /> the document should be saved into.</param>
        </member>
    </members>
</doc>
