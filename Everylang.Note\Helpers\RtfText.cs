﻿using System.Windows.Forms;

namespace Everylang.Note.Helpers
{
    class RtfText
    {
        internal static string? StripRtf(string? rtfString)
        {
            string? result = rtfString;

            try
            {
                if (IsRichText(rtfString))
                {
                    // Put body into a RichTextBox so we can strip RTF
                    using RichTextBox rtfTemp = new RichTextBox();
                    rtfTemp.Rtf = rtfString;
                    result = rtfTemp.Text;
                }
                else
                {
                    result = rtfString;
                }
            }
            catch
            {
                // Ignore
            }

            return result;
        }

        private static bool IsRichText(string? testString)
        {
            if ((testString != null) &&
                (testString.Trim().StartsWith("{\\rtf")))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
