<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.Chart.Skia</name>
    </assembly>
    <members>
        <member name="M:Telerik.Windows.Controls.ChartView.SkiaRenderTarget.CalculateTieredPlotAreaSize(Telerik.Charting.RadRect)">
            <summary>
            A method that calculates a tiered plot area size. This is to reduce the number of times a render target is created, so that flickering is reduced when resizing the chart.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.ChartView.SkiaRenderOptions">
            <summary>
            Contains options for controlling the rendering behavior of <see cref="T:Telerik.Windows.Controls.ChartView.ChartSeries"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ChartView.SkiaRenderOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.ChartView.SkiaRenderOptions" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.ChartView.SkiaRenderOptions.DefaultVisualsRenderMode">
            <summary>
            Gets or sets a value indicating how to create default visuals.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.ChartView.SkiaRenderOptions.IsAntialias">
            <summary>
            Gets or sets a value indicating how the edges of primitives are rendered.
            </summary>
        </member>
    </members>
</doc>
