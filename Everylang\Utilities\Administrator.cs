﻿using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Security.Principal;
using Vanara.PInvoke;

namespace Everylang.App.Utilities
{
    internal class Administrator
    {
        internal static bool IsAdministrator()
        {
            if (AdvApi32.OpenProcessToken(Process.GetCurrentProcess().<PERSON><PERSON>, AdvApi32.TokenAccess.TOKEN_ALL_ACCESS, out var token))
            {
                WindowsIdentity identity = new WindowsIdentity(token.DangerousGetHandle());
                WindowsPrincipal principal = new WindowsPrincipal(identity);
                bool result = principal.IsInRole(WindowsBuiltInRole.Administrator)
                              || principal.IsInRole(0x200); //Domain Administrator
                token.Close();
                return result;
            }
            return false;
        }

        internal static bool IsCanRunAdministrator()
        {
            WindowsIdentity identity = WindowsIdentity.GetCurrent();
            WindowsPrincipal principal = new WindowsPrincipal(identity);
            List<Claim> list = new List<Claim?>(principal.UserClaims);
            Claim? c = list.Find(p => p != null && p.Value.Contains("S-1-5-32-544"));
            return c != null;
        }
    }
}
