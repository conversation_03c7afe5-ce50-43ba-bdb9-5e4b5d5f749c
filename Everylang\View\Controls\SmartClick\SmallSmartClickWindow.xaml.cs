﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.LangFlag;
using Everylang.App.SettingsApp;
using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using MousePosition = Everylang.App.Utilities.MousePosition;
using Timer = System.Timers.Timer;

namespace Everylang.App.View.Controls.SmartClick
{
    /// <summary>
    /// Interaction logic for SmallSmartClickWindow.xaml
    /// </summary>
    internal partial class SmallSmartClickWindow
    {
        internal Action CloseActionFirst;
        internal Action CloseActionSecond;
        private readonly Timer _timer;

        internal string SourceText { get; set; }
        internal bool IsWasCopy { get; set; }

        internal bool IsWasPaste { get; set; }

        private bool _isFirstSelect;
        private bool _mouseEnterTime;

        internal SmallSmartClickWindow()
        {
            InitializeComponent();

            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            _timer = new Timer();
            _timer.Elapsed += (_, _) =>
            {
                if (Application.Current != null && Application.Current.Dispatcher != null)
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Hide);
            };
        }

        internal void Show(bool isFirstSelect)
        {
            BCommon.Visibility = Visibility.Visible;
            BCopy.Visibility = Visibility.Collapsed;
            BTranslate.Visibility = Visibility.Collapsed;
            BSpellCheck.Visibility = Visibility.Collapsed;
            BConvert.Visibility = Visibility.Collapsed;
            BPaste.Visibility = Visibility.Collapsed;
            BPasteUnf.Visibility = Visibility.Collapsed;
            TextBlockCount.Visibility = Visibility.Collapsed;

            TextBlockCount.Content = "";
            SourceText = "";
            IsWasCopy = false;
            IsWasPaste = false;
            _mouseEnterTime = false;
            _isFirstSelect = isFirstSelect;
            LangInfoManager.Stop();
            _timer.Interval = 1800;
            _timer.Start();

            IsOpen = true;
            var pos = MousePosition.GetMousePoint();
            HorizontalOffset = pos.X + SettingsManager.Settings.SmartClickMiniPosX;
            VerticalOffset = pos.Y + SettingsManager.Settings.SmartClickMiniPosY;
        }

        private void MouseOverHide(GlobalMouseEventArgs e)
        {
            if (!IsMouseOver)
            {
                Hide();
            }
        }

        internal void Hide()
        {
            if (IsOpen)
            {
                _timer.Stop();
                LangInfoManager.Start();
                IsOpen = false;
                if (_isFirstSelect)
                {
                    CloseActionFirst();
                }
                else
                {
                    CloseActionSecond();
                }
            }
        }

        private async void ButtonCommon(object sender, RoutedEventArgs e)
        {
            _mouseEnterTime = true;
            BorderMu.Opacity = 1;

            if (!_isFirstSelect)
            {
                BCommon.Visibility = Visibility.Collapsed;
                BPaste.Visibility = Visibility.Visible;
                await Task.Delay(50);
                BPasteUnf.Visibility = Visibility.Visible;
            }
            else
            {
                await Task.Delay(100);
                if (!_mouseEnterTime)
                {
                    Hide();
                    return;
                }
                SourceText = ClipboardOperations.GetSelectionText();
                if (string.IsNullOrEmpty(SourceText))
                {
                    Hide();
                }
                BCommon.Visibility = Visibility.Collapsed;
                BSpellCheck.Visibility = Visibility.Visible;
                await Task.Delay(30);
                BConvert.Visibility = Visibility.Visible;
                await Task.Delay(30);
                BTranslate.Visibility = Visibility.Visible;
                await Task.Delay(30);
                BCopy.Visibility = Visibility.Visible;
                await Task.Delay(30);
                TextBlockCount.Content = SourceText.Length.ToString();
                TextBlockCount.Visibility = Visibility.Visible;

            }
        }

        private void BCommon_OnMouseLeave(object sender, MouseEventArgs e)
        {
            Trace.WriteLine("3");
            _mouseEnterTime = false;
        }

        private async void ButtonCopy(object sender, RoutedEventArgs e)
        {
            IsWasCopy = true;
            Hide();
            await Task.Delay(100);
            ClipboardOperations.SendCopyText();
        }

        private void ButtonTranslate(object sender, RoutedEventArgs e)
        {
            Hide();
            GlobalEventsApp.OnEventUniWindowTranslate(SourceText);
        }

        private void ButtonConvert(object sender, RoutedEventArgs e)
        {
            //Hide();
            //SmartClickManager.Instance.ShowConvertSmartClick(false);
        }

        private void ButtonPaste(object sender, RoutedEventArgs e)
        {
            IsWasPaste = true;
            Hide();
            ClipboardOperations.SendPasteText();
        }

        private void ButtonPasteUnf(object sender, RoutedEventArgs e)
        {
            IsWasPaste = true;
            Hide();
            ClipboardKeyHookManager.KeyCombinationPressedClipboardPasteWithoutFormattingPressed();
        }

        private void SmallSmartClickWindow_OnMouseEnter(object sender, MouseEventArgs e)
        {
            Trace.WriteLine("1");
            BorderMu.Opacity = 1;
            _timer.Stop();
        }

        private void SmallSmartClickWindow_OnMouseLeave(object sender, MouseEventArgs e)
        {
            Trace.WriteLine("2");
            BorderMu.Opacity = 0.5;
            _timer.Interval = 650;
            _timer.Start();
        }

        private void ButtoSnpellCheck(object sender, RoutedEventArgs e)
        {
            Hide();
            GlobalEventsApp.OnEventUniWindowSpellCheck(SourceText, false);
        }



    }
}
