﻿<Popup x:Class="Everylang.App.View.Controls.Translator.TranslationWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
       xmlns:richTextBoxEx="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
       xmlns:formatters1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
       xmlns:materialIcons="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
       mc:Ignorable="d" PopupAnimation="Fade" x:Name="me" AllowsTransparency="True"
       MinHeight="180" MinWidth="340" MaxHeight="600" MaxWidth="800" Placement="Mouse" StaysOpen="True"
       Focusable="False" x:ClassModifier="internal"
       DataContext="{Binding ElementName=me, Path=ViewModel}">

    <Popup.Resources>
        <ResourceDictionary>

            <Style x:Key="ImageButton" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Focusable" Value="False" />
                <Setter Property="IsBackgroundVisible" Value="False" />
                <Setter Property="Padding" Value="2" />
                <Setter Property="Margin" Value="0" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="MinHeight" Value="0" />
            </Style>
            <Style x:Key="RichTextBoxStyle" TargetType="{x:Type richTextBoxEx:RichTextBoxEx}">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
                <Setter Property="FontSize" Value="{Binding FontSize}" />
                <Setter Property="FontFamily" Value="{Binding FontFam}" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleTextPanel}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleTextPanel}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ImageButtonPin" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialIcons:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="PinOff" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialIcons:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="Pin" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="20"
                                                    Height="20"
                                                    Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonCopy" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="ContentCopy" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonSubstitute" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="Cached" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonSoundFrom" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="16"
                                                    Height="16"
                                                    Kind="VolumeHigh" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonSoundTo" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="16"
                                                    Height="16"
                                                    Kind="VolumeHigh" />
                    </Setter.Value>
                </Setter>
            </Style>


            <Style x:Key="ImageButtonleftToRight" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="20"
                                                    Height="20"
                                                    Kind="SwapHorizontal" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonHome" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="HomeOutline" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonSiteSource" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="18"
                                                    Height="18"
                                                    Kind="OpenInNew" />
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonLatin" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButton}">
                <Setter Property="Content">
                    <Setter.Value>
                        <materialIcons:MaterialIcon Width="22"
                                                    Height="22"
                                                    Kind="Alpha" />
                    </Setter.Value>
                </Setter>
                <Setter Property="Visibility" Value="Hidden" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding CurrentTranslateService}" Value="Google">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="WrapPanelStyle" TargetType="{x:Type WrapPanel}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleResultPanel}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleResultPanel}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="BorderStyle" TargetType="{x:Type Border}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleResultPanel}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleResultPanel}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="ProgressRingStyle" TargetType="telerik:RadBusyIndicator" BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="IsBusy" Value="{Binding IsVisibleProgress}" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleProgress}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleProgress}" Value="False">
                        <Setter Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="OverlayStyle"
                   TargetType="Rectangle">
                <Setter Property="Stroke"
                        Value="{Binding Source={x:Static telerik:Windows11Palette.Palette}, Path=AlternativeColor}" />
                <Setter Property="Fill"
                        Value="{Binding Source={x:Static telerik:Windows11Palette.Palette}, Path=AlternativeColor}">
                </Setter>
                <Setter Property="Opacity"
                        Value="0.5" />
            </Style>

            <RoutedUICommand x:Key="CommandReplaceText" />
            <RoutedUICommand x:Key="CommandClose" />
        </ResourceDictionary>
    </Popup.Resources>

    <Popup.CommandBindings>
        <CommandBinding Command="{StaticResource CommandReplaceText}" Executed="ReplaceText" />
        <CommandBinding Command="{StaticResource CommandClose}" Executed="CloseThis" />
    </Popup.CommandBindings>

    <Popup.InputBindings>
        <KeyBinding Key="F5" Command="{Binding ListenTransalatedSourseCommand}" />
        <KeyBinding Key="F6" Command="{Binding ListenTransalatedResultCommand}" />
        <KeyBinding Key="Escape" Command="{StaticResource CommandClose}" />
    </Popup.InputBindings>
    <Grid Background="Transparent">
        <Border BorderThickness="2" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" CornerRadius="4"
                Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Thumb x:Name="ThumbMy" Opacity="0" Grid.Row="0" Grid.Column="0"
                       PreviewMouseDoubleClick="WindowMouseLeftButtonDown" DragDelta="OnDragDelta" />
                <telerik:RadDropDownButton
                    Grid.Row="0" Width="100"
                    MinHeight="0"
                    Padding="10,6,0,4"
                    FontSize="13"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}"
                    Focusable="False"
                    Content="{Binding Path=CurrentTranslateService}"
                    CloseOnPopupMouseLeftButtonUp="True">
                    <telerik:RadDropDownButton.DropDownContent>
                        <telerik:RadListBox Width="98"
                                            Height="Auto"
                                            BorderThickness="0"
                                            ItemsSource="{Binding Path=TranslateServices}"
                                            SelectedItem="{Binding Path=CurrentTranslateService,Mode=TwoWay}"
                                            SelectedIndex="0">

                        </telerik:RadListBox>
                    </telerik:RadDropDownButton.DropDownContent>
                </telerik:RadDropDownButton>
                <WrapPanel Grid.Row="0" Orientation="Horizontal" FlowDirection="RightToLeft" VerticalAlignment="Center">
                    <telerik:RadButton Style="{StaticResource ImageButtonClose}" Margin="1,0,0,0"
                                       Click="ButtonClickClose"
                                       ToolTip="{telerik:LocalizableResource Key=TransCloseHeaderButton}"
                                       IsCancel="True" />
                    <WrapPanel Orientation="Horizontal" Style="{StaticResource WrapPanelStyle}">
                        <telerik:RadButton Style="{StaticResource ImageButtonPin}" Margin="0,0,0,0"
                                           Click="StayOnTopClick"
                                           ToolTip="{telerik:LocalizableResource Key=StayOnTopButton}" />
                        <telerik:RadButton Style="{StaticResource ImageButtonCopy}" Margin="0,0,0,0"
                                           Command="{Binding Path=CopyTransalatedCommand}"
                                           ToolTip="{telerik:LocalizableResource Key=CopyButton}" />
                        <telerik:RadButton Style="{StaticResource ImageButtonSubstitute}" Margin="0,0,0,0"
                                           Command="{StaticResource CommandReplaceText}"
                                           ToolTip="{telerik:LocalizableResource Key=TransReplaceTextButton}" />
                        <telerik:RadButton Style="{StaticResource ImageButtonSiteSource}" Margin="0,0,0,0"
                                           Command="{Binding Path=SiteSourceCommand}"
                                           ToolTip="{telerik:LocalizableResource Key=SiteSourceTextButton}" />
                        <telerik:RadButton Style="{StaticResource ImageButtonHome}" Margin="0,0,0,0"
                                           ToolTip="{telerik:LocalizableResource Key=GoToMainWindowButton}"
                                           Click="GoToMainWindowClick" />
                        <telerik:RadButton Style="{StaticResource ImageButtonLatin}" Margin="0,0,0,0" Padding="0"
                                           ToolTip="{telerik:LocalizableResource Key=LatinButton}"
                                           Click="ButtonClickLatin" />
                    </WrapPanel>
                </WrapPanel>
                <telerik:RadBusyIndicator Style="{StaticResource ProgressRingStyle}" Grid.Row="1" Grid.RowSpan="2"
                                          BusyContent="" OverlayStyle="{StaticResource OverlayStyle}"
                                          IsIndeterminate="True" VerticalAlignment="Center" Grid.ZIndex="1" />
                <richTextBoxEx:RichTextBoxEx x:Name="resTextBox" IsReadOnly="True" Margin="3,0,3,0"
                                             IsInactiveSelectionHighlightEnabled="True"
                                             Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                                             Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                             Style="{StaticResource RichTextBoxStyle}" Grid.Row="1"
                                             Block.LineHeight="1"
                                             TextChanged="RichTextBoxTextChanged">
                    <richTextBoxEx:RichTextBoxEx.TextFormatter>
                        <formatters1:StandartFormatter />
                    </richTextBoxEx:RichTextBoxEx.TextFormatter>
                    <richTextBoxEx:RichTextBoxEx.CommandBindings>
                        <CommandBinding Command="{x:Static ApplicationCommands.Copy}"
                                        Executed="CommandCopyresTextBox_Executed" />
                    </richTextBoxEx:RichTextBoxEx.CommandBindings>
                </richTextBoxEx:RichTextBoxEx>


                <WrapPanel Margin="0,0,0,0" Grid.Row="2" Orientation="Horizontal" VerticalAlignment="Center"
                           Style="{StaticResource WrapPanelStyle}">
                    <telerik:RadDropDownButton Width="125"
                                               Padding="8,0,0,0"
                                               MinHeight="0"
                                               FontSize="13"
                                               x:Name="ListViewLangFrom"
                                               Margin="0 0 0 0"
                                               ToolTip="{Binding Path=LanguageFromCurrent.SelectedNameFrom}"
                                               HorizontalContentAlignment="Left"
                                               Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}"
                                               CloseOnPopupMouseLeftButtonUp="True"
                                               Focusable="False"
                                               Content="{Binding Path=LanguageFromCurrent.Name}">
                        <telerik:RadDropDownButton.DropDownContent>
                            <telerik:RadListBox MaxHeight="250"
                                                x:Name="ListBoxLangFrom"
                                                SelectionChanged="LanguageSelectionChanged"
                                                BorderThickness="0"
                                                DisplayMemberPath="Name"
                                                ItemsSource="{Binding Path=LanguagesFrom}"
                                                SelectedItem="{Binding Path=LanguageFromCurrent,Mode=TwoWay}"
                                                SelectedIndex="0">
                                <telerik:RadListBox.ItemContainerStyle>
                                    <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                                        <Setter Property="Padding" Value="9,0,0,0" />
                                        <Setter Property="Margin" Value="0" />
                                        <Setter Property="MinHeight" Value="0" />
                                        <Setter Property="Height" Value="23" />
                                        <Setter Property="FontSize" Value="13" />
                                    </Style>
                                </telerik:RadListBox.ItemContainerStyle>
                            </telerik:RadListBox>
                        </telerik:RadDropDownButton.DropDownContent>
                    </telerik:RadDropDownButton>
                    <telerik:RadButton Margin="0,0,0,0" Style="{StaticResource ImageButtonSoundFrom}"
                                       Command="{Binding Path=ListenTransalatedSourseCommand}"
                                       ToolTip="{telerik:LocalizableResource Key=SoundButton}" />
                    <telerik:RadButton x:Name="LeftToRightButton"
                                       Style="{StaticResource ImageButtonleftToRight}"
                                       Margin="1,0,1,0"
                                       Background="Transparent"
                                       Command="{Binding Path=SwapLangCommand}" />
                    <telerik:RadDropDownButton Width="125"
                                               Padding="8,0,0,0"
                                               x:Name="ListViewLangTo"
                                               FontSize="13"
                                               MinHeight="0"
                                               Margin="0"
                                               ToolTip="{Binding Path=LanguageToCurrent.SelectedNameTo}"
                                               Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}"
                                               Focusable="False"
                                               HorizontalContentAlignment="Left"
                                               CloseOnPopupMouseLeftButtonUp="True"

                                               Content="{Binding Path=LanguageToCurrent.Name}">
                        <telerik:RadDropDownButton.DropDownContent>

                            <telerik:RadListBox x:Name="ListBoxLangTo"
                                                MaxHeight="250"
                                                SelectionChanged="LanguageSelectionChanged"
                                                BorderThickness="0"
                                                DisplayMemberPath="Name"
                                                ItemsSource="{Binding Path=LanguagesTo}"
                                                SelectedItem="{Binding Path=LanguageToCurrent,Mode=TwoWay}"
                                                SelectedIndex="0">
                                <telerik:RadListBox.ItemContainerStyle>
                                    <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                                        <Setter Property="Padding" Value="9,0,0,0" />
                                        <Setter Property="Margin" Value="0" />
                                        <Setter Property="MinHeight" Value="0" />
                                        <Setter Property="Height" Value="23" />
                                        <Setter Property="FontSize" Value="13" />
                                    </Style>
                                </telerik:RadListBox.ItemContainerStyle>
                            </telerik:RadListBox>
                        </telerik:RadDropDownButton.DropDownContent>
                    </telerik:RadDropDownButton>
                    <telerik:RadButton Margin="0,0,0,0" Style="{StaticResource ImageButtonSoundTo}"
                                       Command="{Binding Path=ListenTransalatedResultCommand}"
                                       ToolTip="{telerik:LocalizableResource Key=SoundButton}" />
                </WrapPanel>
            </Grid>
        </Border>
        <Thumb x:Name="ThumbRightBottom" HorizontalAlignment="Right" Cursor="SizeNWSE" Width="10" Height="10"
               VerticalAlignment="Bottom"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbBottom" HorizontalAlignment="Stretch" Cursor="SizeNS" VerticalAlignment="Bottom" Height="5"
               Margin="0,0,10,0"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbTop" HorizontalAlignment="Stretch" Cursor="SizeNS" VerticalAlignment="Top"  Height="5"
               DragDelta="ThumbDragDelta" Opacity="0" />
        <Thumb x:Name="ThumbRight" HorizontalAlignment="Right" Cursor="SizeWE" VerticalAlignment="Stretch" Width="5"
               DragDelta="ThumbDragDelta" Opacity="0" Margin="0,0,0,10" />
        <Thumb x:Name="ThumbLeft" HorizontalAlignment="Left" Cursor="SizeWE" VerticalAlignment="Stretch" Width="5"
               DragDelta="ThumbDragDelta" Opacity="0" />
    </Grid>
</Popup>