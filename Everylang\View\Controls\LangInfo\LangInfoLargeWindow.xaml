﻿<Popup
    AllowsTransparency="True"
    Focusable="False"
    Placement="Absolute"
    StaysOpen="True"
    x:Class="Everylang.App.View.Controls.LangInfo.LangInfoLargeWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid>
        <Thumb
            Height="0"
            Width="0"
            x:Name="ThumbMy" />
        <StackPanel Margin="1" Orientation="Horizontal">
            <Grid VerticalAlignment="Center">
                <TextBlock
                    FontSize="{Binding LangFlagSettingsViewModel.FontSizeLangInfoForLarge}"
                    FontWeight="Bold"
                    HorizontalAlignment="Center"
                    Name="textBlock"
                    Opacity="{Binding LangFlagSettingsViewModel.OpacityIcon, Mode=TwoWay}"
                    VerticalAlignment="Center" />
                <Image
                    Name="image"
                    Opacity="{Binding LangFlagSettingsViewModel.OpacityIcon, Mode=TwoWay}"
                    RenderOptions.BitmapScalingMode="HighQuality">
                    <Image.LayoutTransform>
                        <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSizeForLarge}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSizeForLarge}" />
                    </Image.LayoutTransform>
                </Image>
            </Grid>
            <Image
                Margin="3,0,0,0"
                Name="NumLockButtonOn"
                RenderOptions.BitmapScalingMode="HighQuality"
                Source="/EveryLang;component/Resources/numlock_on.png"
                VerticalAlignment="Center"
                Visibility="Collapsed">
                <Image.LayoutTransform>
                    <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" />
                </Image.LayoutTransform>
            </Image>
            <Image
                Margin="3,0,0,0"
                Name="NumLockButtonOff"
                RenderOptions.BitmapScalingMode="HighQuality"
                Source="/EveryLang;component/Resources/numlock_off.png"
                VerticalAlignment="Center"
                Visibility="Collapsed">
                <Image.LayoutTransform>
                    <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" />
                </Image.LayoutTransform>
            </Image>
            <Image
                Margin="3,0,0,0"
                Name="CapsLockButtonOn"
                RenderOptions.BitmapScalingMode="HighQuality"
                Source="/EveryLang;component/Resources/capslock_on.png"
                VerticalAlignment="Center"
                Visibility="Collapsed">
                <Image.LayoutTransform>
                    <ScaleTransform ScaleX="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" ScaleY="{Binding LangFlagSettingsViewModel.ImageSizeForLargeCaps}" />
                </Image.LayoutTransform>
            </Image>
        </StackPanel>
    </Grid>

</Popup>
