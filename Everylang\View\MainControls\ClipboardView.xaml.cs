﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.View.Controls.ClipboardFormatControls;
using Everylang.App.ViewModels;
using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Telerik.Windows;
using Telerik.Windows.Controls;
using Telerik.Windows.Controls.GridView;
using Telerik.Windows.Data;
using Image = System.Drawing.Image;
using TextDataFormat = System.Windows.Forms.TextDataFormat;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for ClipboardView.xaml
    /// </summary>
    internal partial class ClipboardView
    {
        private string? _lastSelectedText;
        private TextBox? _selectedTextBox;
        //private bool _menuIsOpen;
        private ClipboardDataModel? _lastSelectedClipboardData;


        internal ClipboardView()
        {
            InitializeComponent();
        }

        private void GridContextMenu_Opened(object sender, RoutedEventArgs e)
        {
            var row = this.GridContextMenu.GetClickedElement<GridViewRow>();
            if (row == null) return;
            this.lvClipboard.SelectedItem = row.DataContext;
            if (_lastSelectedClipboardData == null)
            {
                return;
            }
            //_menuIsOpen = true;
            RadContextMenu contextMenu = GridContextMenu;
            contextMenu.Items.Clear();
            //contextMenu.Closed += (o, args) => { _menuIsOpen = false; };
            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("CopyButton");
            itemCopy.Click += MenuItemCopyClick;
            contextMenu.Items.Add(itemCopy);

            if (_lastSelectedClipboardData.IsImage)
            {
                contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Mouse;
                contextMenu.IsOpen = true;
                return;
            }

            if (_lastSelectedClipboardData.IsHtml && VMContainer.Instance.ClipboardViewModel.SelectedItems.Count == 1)
            {
                RadMenuItem itemCopyHtml = new RadMenuItem();
                itemCopyHtml.Header = LocalizationManager.GetString("CopyButtonHtml");
                itemCopyHtml.Click += MenuItemCopyHtmlClick;
                contextMenu.Items.Add(itemCopyHtml);
            }

            if (_lastSelectedClipboardData.IsRtf && VMContainer.Instance.ClipboardViewModel.SelectedItems.Count == 1)
            {
                RadMenuItem itemCopyRtf = new RadMenuItem();
                itemCopyRtf.Header = LocalizationManager.GetString("CopyButtonRtf");
                itemCopyRtf.Click += MenuItemCopyRtfClick;
                contextMenu.Items.Add(itemCopyRtf);
            }

            RadMenuItem itemBreakInter = new RadMenuItem();
            itemBreakInter.Header = LocalizationManager.GetString("BreakInterButton");
            itemBreakInter.Click += MenuItemBreakInterClick;
            contextMenu.Items.Add(itemBreakInter);

            RadMenuItem itemBreakSpace = new RadMenuItem();
            itemBreakSpace.Header = LocalizationManager.GetString("BreakSpaceButton");
            itemBreakSpace.Click += MenuItemBreakSpaceClick;
            contextMenu.Items.Add(itemBreakSpace);

            if (VMContainer.Instance.ClipboardViewModel.SelectedItems.Count == 1)
            {
                RadMenuItem itemCopyToSnippets = new RadMenuItem();
                itemCopyToSnippets.Header = LocalizationManager.GetString("ToReplacerButton");
                itemCopyToSnippets.Click += MenuItemToSnippets;
                contextMenu.Items.Add(itemCopyToSnippets);

                if (_lastSelectedClipboardData.IsFavorite)
                {
                    RadMenuItem itemRemoveFavorite = new RadMenuItem();
                    itemRemoveFavorite.Header = LocalizationManager.GetString("RemoveFavorite");
                    itemRemoveFavorite.Click += MenuItemRemoveFavoriteClick;
                    contextMenu.Items.Add(itemRemoveFavorite);
                }
                else
                {
                    RadMenuItem itemAddFavorite = new RadMenuItem();
                    itemAddFavorite.Header = LocalizationManager.GetString("AddFavorite");
                    itemAddFavorite.Click += MenuItemitemAddFavoriteClick;
                    contextMenu.Items.Add(itemAddFavorite);
                }
            }
        }

        private void listBoxItem_DoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (_lastSelectedText != null)
                {
                    ClipboardOperations.SetTextWithoutHistory(_lastSelectedText);
                }
            }
            catch
            {
                // ignore
            }
        }

        private void lvClipboard_SelectionChanged(object sender, object e)
        {
            //if (_menuIsOpen)
            //{
            //    return;
            //}
            if (Mouse.RightButton == MouseButtonState.Pressed && VMContainer.Instance.ClipboardViewModel.SelectedItems.Count > 1)
            {
                return;
            }
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
            if (lvClipboard.SelectedItems.Count > 0)
            {
                foreach (var item in lvClipboard.SelectedItems)
                {
                    VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)item);
                }
                _lastSelectedText = VMContainer.Instance.ClipboardViewModel.SelectedItems.Last().Text;
                _lastSelectedClipboardData = VMContainer.Instance.ClipboardViewModel.SelectedItems.Last();
            }
            VMContainer.Instance.ClipboardViewModel.OnPropertyChanged(nameof(VMContainer.Instance.ClipboardViewModel.IsSelectedNotNull));

        }

        private void MenuItemCopyClick(object sender, RoutedEventArgs e)
        {
            if (VMContainer.Instance.ClipboardViewModel.SelectedItems.Count > 0)
            {
                if (_selectedTextBox != null && _selectedTextBox.SelectionLength > 0 && VMContainer.Instance.ClipboardViewModel.SelectedItems.Count == 1)
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedTextBox.SelectedText);
                    return;
                }

                if (_lastSelectedClipboardData != null && _lastSelectedClipboardData.IsImage)
                {
                    ClipboardMonitorWorker.IgnoreLast = true;
                    var imStream = _lastSelectedClipboardData.GetImage();
                    if (imStream != null)
                    {
                        using Image img = Image.FromStream(imStream);
                        System.Windows.Forms.Clipboard.SetImage(img);
                    }

                    return;
                }
                string text = "";
                foreach (var clipboardDataModel in VMContainer.Instance.ClipboardViewModel.SelectedItems)
                {
                    text += clipboardDataModel.Text + Environment.NewLine;
                }
                ClipboardOperations.SetTextWithoutHistory(text.Trim());
            }
        }

        private void MenuItemCopyHtmlClick(object sender, RoutedEventArgs e)
        {
            if (_lastSelectedClipboardData != null)
            {
                if (_selectedTextBox != null && !string.IsNullOrEmpty(_selectedTextBox.SelectedText))
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedTextBox.SelectedText);
                    return;
                }
                ClipboardMonitorWorker.IgnoreLast = true;

                if (_lastSelectedClipboardData.Html != null)
                    System.Windows.Forms.Clipboard.SetText(_lastSelectedClipboardData.Html, TextDataFormat.Text);
            }
        }

        private void MenuItemCopyRtfClick(object sender, RoutedEventArgs e)
        {
            if (_lastSelectedClipboardData != null)
            {
                if (_selectedTextBox != null && !string.IsNullOrEmpty(_selectedTextBox.SelectedText))
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedTextBox.SelectedText);
                    return;
                }
                ClipboardMonitorWorker.IgnoreLast = true;
                System.Windows.Forms.DataObject dataObject = new System.Windows.Forms.DataObject();
                dataObject.SetData("UnicodeText", _lastSelectedClipboardData.Text);
                dataObject.SetData("Rich Text Format", _lastSelectedClipboardData.Rtf);
                System.Windows.Forms.Clipboard.SetDataObject(dataObject);
            }
        }

        private void MenuItemBreakInterClick(object sender, RoutedEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.BreakInter();
            lvClipboard.SelectedItems.Clear();
        }

        private void MenuItemBreakSpaceClick(object sender, RoutedEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.BreakSpace();
            lvClipboard.SelectedItems.Clear();
        }

        private void MenuItemToSnippets(object sender, RoutedEventArgs e)
        {
            if (_lastSelectedClipboardData != null)
                if (_lastSelectedClipboardData.Text != null)
                    GlobalEventsApp.OnEventAddNewSnippets(_lastSelectedClipboardData.Text);
        }

        private void MenuItemitemAddFavoriteClick(object sender, RadRoutedEventArgs e)
        {
            if (_lastSelectedClipboardData != null)
            {
                _lastSelectedClipboardData.IsFavorite = true;
                ClipboadManager.SaveData(_lastSelectedClipboardData);
                lvClipboard.Items.Refresh();
            }

        }

        private void MenuItemRemoveFavoriteClick(object sender, RadRoutedEventArgs e)
        {
            if (_lastSelectedClipboardData != null)
            {
                _lastSelectedClipboardData.IsFavorite = false;
                ClipboadManager.SaveData(_lastSelectedClipboardData);
                lvClipboard.Rebind();
            }
        }

        private void ButtonClickFormat(object sender, RoutedEventArgs e)
        {
            var s = e.Source as FrameworkElement;
            var parentRow = s.ParentOfType<GridViewRow>();
            if (parentRow != null)
            {
                this.lvClipboard.SelectedItem = parentRow.DataContext;
            }

            if (_lastSelectedClipboardData != null)
            {
                if (_lastSelectedClipboardData.IsHtml)
                {
                    ClipboardFormatHtmlWindow clipboardFormatHtmlWindow = new ClipboardFormatHtmlWindow(_lastSelectedClipboardData.Html);
                    clipboardFormatHtmlWindow.Show();
                    return;
                }
                if (_lastSelectedClipboardData.IsRtf)
                {
                    ClipboardFormatRtfWindow clipboardFormatRtfWindow = new ClipboardFormatRtfWindow(_lastSelectedClipboardData.Rtf);
                    clipboardFormatRtfWindow.Show();
                    return;
                }
                if (_lastSelectedClipboardData.IsText)
                {
                    ClipboardFormatTextWindow clipboardFormatTextWindow = new ClipboardFormatTextWindow(_lastSelectedClipboardData.Text);
                    clipboardFormatTextWindow.Show();
                    return;
                }
                if (_lastSelectedClipboardData.IsFile)
                {
                    ClipboardFormatTextWindow clipboardFormatTextWindow = new ClipboardFormatTextWindow(_lastSelectedClipboardData.Files);
                    clipboardFormatTextWindow.Show();
                    return;
                }
                if (_lastSelectedClipboardData.IsImage)
                {
                    using var imStream = _lastSelectedClipboardData.GetImage();
                    if (imStream != null)
                    {
                        ClipboardFormatImageWindow clipboardFormatTextWindow = new ClipboardFormatImageWindow(imStream);
                        clipboardFormatTextWindow.Show();
                    }
                }
            }
        }

        private void TextBoxBase_OnTextChanged(object sender, Telerik.Windows.Controls.AutoSuggestBox.TextChangedEventArgs e)
        {
            if (e.Reason == Telerik.Windows.Controls.AutoSuggestBox.TextChangeReason.UserInput)
            {
                string text = SearchTextBox.Text;
                var deleteCommand = RadGridViewCommands.SearchByText as RoutedUICommand;
                deleteCommand?.Execute(text, this.lvClipboard);
            }
        }

        private void ToggleButtonFavorite_OnChecked(object sender, RoutedEventArgs e)
        {
            var toggleButton = sender as Telerik.Windows.Controls.RadToggleButton;
            if (toggleButton == null) return;

            this.lvClipboard.FilterDescriptors.SuspendNotifications();

            // Получаем фильтр для столбца IsFavorite (первый столбец, индекс 0)
            IColumnFilterDescriptor favoriteFilter = this.lvClipboard.Columns[0].ColumnFilterDescriptor;
            favoriteFilter.SuspendNotifications();

            if (toggleButton.IsChecked == true)
            {
                // Показываем только избранные элементы
                favoriteFilter.FieldFilter.Filter1.Operator = FilterOperator.IsEqualTo;
                favoriteFilter.FieldFilter.Filter1.Value = true;
            }
            else
            {
                // Очищаем фильтр, показываем все элементы
                favoriteFilter.FieldFilter.Clear();
            }

            favoriteFilter.ResumeNotifications();
            this.lvClipboard.FilterDescriptors.ResumeNotifications();
        }
    }
}
