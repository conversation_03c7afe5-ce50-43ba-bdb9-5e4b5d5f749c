﻿using Everylang.App.SettingsApp;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.SmartClick
{
    /// <summary>
    /// Interaction logic for SmartClickItemsChecker.xaml
    /// </summary>
    internal partial class SmartClickItemsChecker
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(SmartClickItemsChecker));

        internal event RoutedEventHandler? HidePanel
        {
            add
            {
                if (value != null) AddHandler(HidePanelEvent, value);
            }
            remove
            {
                if (value != null) RemoveHandler(HidePanelEvent, value);
            }
        }

        internal SmartClickItemsChecker()
        {
            InitializeComponent();
            LoadData();
        }

        private List<SmartClickItem>? _smartClickItemList;

        private void LoadData()
        {
            _smartClickItemList = new List<SmartClickItem>();
            var items = SettingsManager.Settings.SmartClickCheckedItems.Split(',');
            string?[] defaultSmartClickCheckedItems = "UniConverter,UniCase,UniLink,UniLinkTranslate,UniLinkShorter,UniTranslate,UniSpellCheck,UniCopy,UniPaste,UniPasteUnf,UniSearch,UniEmail,UniAutochange,UniClipboardHistory,UniDiaryHistory,OcrHeader".Split(',');
            foreach (var defaultSmartClickCheckedItem in defaultSmartClickCheckedItems)
            {
                SmartClickItem clickItem = new SmartClickItem();
                clickItem.Key = defaultSmartClickCheckedItem;
                clickItem.Checked = items.Contains(defaultSmartClickCheckedItem);
                _smartClickItemList.Add(clickItem);
            }

            LvItems.ItemsSource = _smartClickItemList;
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }

        private void ToggleButton_OnChecked(object sender, RoutedEventArgs e)
        {
            string result = "";
            if (_smartClickItemList != null)
                foreach (var smartClickItem in _smartClickItemList)
                {
                    if (smartClickItem.Checked)
                    {
                        result += smartClickItem.Key + ",";
                    }
                }

            SettingsManager.Settings.SmartClickCheckedItems = result.TrimEnd(',');
        }


    }

    public class SmartClickItem : INotifyPropertyChanged
    {
        private bool _checked;
        public string? Key { get; set; }
        // ReSharper disable once UnusedMember.Local
        public string? Name => LocalizationManager.GetString(Key);

        public bool Checked
        {
            get { return _checked; }
            set
            {
                _checked = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
