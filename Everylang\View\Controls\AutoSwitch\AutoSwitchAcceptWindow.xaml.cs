﻿using Everylang.App.Callback;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.LangFlag;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using MousePosition = Everylang.App.Utilities.MousePosition;

namespace Everylang.App.View.Controls.AutoSwitch
{
    internal partial class AutoSwitchAcceptWindow
    {
        private string? _text;
        private bool _isShow;

        internal AutoSwitchAcceptWindow()
        {
            InitializeComponent();
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!_isShow)
            {
                return;
            }
            Hide();
        }

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!_isShow)
            {
                return;
            }
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Enter)
            {
                if (_text != null) GlobalEventsApp.OnSwitchAcceptAction(_text);

                e.Handled = true;
            }
            Hide();
        }

        internal void Show(string? text)
        {
            _text = text;
            ShowHide();
        }

        internal void Hide()
        {
            _isShow = false;
            IsOpen = false;
        }

        private void ShowHide()
        {
            System.Windows.Point curretPos = CarretPosition.GetPosition();
            System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
            IsOpen = true;
            if (curretPos.X != 0 && curretPos.Y != 0)
            {
                var pos = MousePosition.GetMousePoint(curretPos);
                HorizontalOffset = pos.X + 30;
                VerticalOffset = pos.Y - 30;
            }
            else
            {
                HorizontalOffset = centrePos.X;
                VerticalOffset = centrePos.Y;
            }
            _isShow = true;
        }
    }
}
