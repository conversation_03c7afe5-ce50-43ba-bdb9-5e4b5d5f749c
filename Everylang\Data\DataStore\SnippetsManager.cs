﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    class SnippetsManager
    {
        internal static IEnumerable<SnippetsDataModel> GetAllSnippetsData()
        {
            var collection = new List<SnippetsDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutochangeDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        SnippetsDataModel autochangeDataModel = new SnippetsDataModel();
                        if (!bsonDocument["_id"].IsNull) autochangeDataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["FromText"].IsNull) autochangeDataModel.FromText = bsonDocument["FromText"].AsString;
                        if (!bsonDocument["Text"].IsNull) autochangeDataModel.Text = bsonDocument["Text"].AsString;
                        if (!bsonDocument["Tags"].IsNull) autochangeDataModel.Tags = bsonDocument["Tags"].AsString;
                        if (!bsonDocument["ShortText"].IsNull) autochangeDataModel.ShortText = bsonDocument["ShortText"].AsString;
                        if (!bsonDocument["CountUsage"].IsNull) autochangeDataModel.CountUsage = bsonDocument["CountUsage"].AsInt32;
                        if (!bsonDocument["CursorPosition"].IsNull) autochangeDataModel.CursorPosition = bsonDocument["CursorPosition"].AsInt32;
                        if (!bsonDocument["IsSetCursorPosition"].IsNull) autochangeDataModel.IsSetCursorPosition = bsonDocument["IsSetCursorPosition"].AsBoolean;
                        if (!bsonDocument["IsChangeAtOnce"].IsNull) autochangeDataModel.IsChangeAtOnce = bsonDocument["IsChangeAtOnce"].AsBoolean;
                        if (!bsonDocument["IsHidden"].IsNull) autochangeDataModel.IsHidden = bsonDocument["IsHidden"].AsBoolean;
                        if (!bsonDocument["LangToSwitch"].IsNull) autochangeDataModel.LangToSwitch = bsonDocument["LangToSwitch"].AsString;
                        collection.Add(autochangeDataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddSnippetsData(SnippetsDataModel autochangeDataModel)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutochangeDataModel");

                    BsonDocument bsonDocument = new BsonDocument();
                    autochangeDataModel.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = autochangeDataModel.Id;
                    bsonDocument["FromText"] = autochangeDataModel.FromText;
                    bsonDocument["Text"] = autochangeDataModel.Text;
                    bsonDocument["Tags"] = autochangeDataModel.Tags;
                    bsonDocument["ShortText"] = autochangeDataModel.ShortText;
                    bsonDocument["CountUsage"] = autochangeDataModel.CountUsage;
                    bsonDocument["CursorPosition"] = autochangeDataModel.CursorPosition;
                    bsonDocument["IsSetCursorPosition"] = autochangeDataModel.IsSetCursorPosition;
                    bsonDocument["IsChangeAtOnce"] = autochangeDataModel.IsChangeAtOnce;
                    bsonDocument["IsHidden"] = autochangeDataModel.IsHidden;
                    bsonDocument["LangToSwitch"] = autochangeDataModel.LangToSwitch;
                    schemelessCollection.Insert(bsonDocument);

                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllSnippetsData()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("AutochangeDataModel"))
                    {
                        db.DropCollection("AutochangeDataModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void UpdateData(SnippetsDataModel autochangeDataModel)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutochangeDataModel");
                    var bsonDocument = schemelessCollection.FindById(autochangeDataModel.Id);
                    if (bsonDocument != null)
                    {
                        bsonDocument["FromText"] = autochangeDataModel.FromText;
                        bsonDocument["Text"] = autochangeDataModel.Text;
                        bsonDocument["Tags"] = autochangeDataModel.Tags;
                        bsonDocument["ShortText"] = autochangeDataModel.ShortText;
                        bsonDocument["CountUsage"] = autochangeDataModel.CountUsage;
                        bsonDocument["CursorPosition"] = autochangeDataModel.CursorPosition;
                        bsonDocument["IsSetCursorPosition"] = autochangeDataModel.IsSetCursorPosition;
                        bsonDocument["IsChangeAtOnce"] = autochangeDataModel.IsChangeAtOnce;
                        bsonDocument["IsHidden"] = autochangeDataModel.IsHidden;
                        bsonDocument["LangToSwitch"] = autochangeDataModel.LangToSwitch;
                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelSnippetsData(SnippetsDataModel autochangeDataModel)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection<SnippetsDataModel>("AutochangeDataModel");
                    if (autochangeDataModel.Id != null) schemelessCollection.Delete(autochangeDataModel.Id);
                    else schemelessCollection.DeleteMany(x => x.ShortText == autochangeDataModel.ShortText);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void SaveAllSnippetsData(List<SnippetsDataModel?> autochangeList)
        {

            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("AutochangeDataModel");
                    foreach (var autochangeDataModel in autochangeList)
                    {
                        BsonDocument bsonDocument = new BsonDocument();
                        if (autochangeDataModel.Id == null)
                        {
                            autochangeDataModel.Id = ObjectId.NewObjectId();
                        }
                        bsonDocument["_id"] = autochangeDataModel.Id;
                        bsonDocument["FromText"] = autochangeDataModel.FromText;
                        bsonDocument["Text"] = autochangeDataModel.Text;
                        bsonDocument["Tags"] = autochangeDataModel.Tags;
                        bsonDocument["ShortText"] = autochangeDataModel.ShortText;
                        bsonDocument["CountUsage"] = autochangeDataModel.CountUsage;
                        bsonDocument["CursorPosition"] = autochangeDataModel.CursorPosition;
                        bsonDocument["IsSetCursorPosition"] = autochangeDataModel.IsSetCursorPosition;
                        bsonDocument["IsChangeAtOnce"] = autochangeDataModel.IsChangeAtOnce;
                        bsonDocument["IsHidden"] = autochangeDataModel.IsHidden;
                        bsonDocument["LangToSwitch"] = autochangeDataModel.LangToSwitch;
                        schemelessCollection.Insert(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

        }
    }
}
