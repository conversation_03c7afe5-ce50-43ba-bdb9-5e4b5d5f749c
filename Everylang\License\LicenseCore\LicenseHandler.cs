﻿using System;
using System.IO;
using System.Reflection;

namespace Everylang.App.License.LicenseCore;

internal class LicenseHandler
{

    internal static T? FromProductCode<T>(string pProductCode, string idString) where T : LicenseKeyData
    {
        pProductCode = pProductCode.TrimStart(new[]{
            'e',
            'l'
        });

        pProductCode = pProductCode.TrimStart(new[]{
            't',
            'r',
            'i',
            'a',
            'l'
        });

        pProductCode = pProductCode.Replace("-", "");

        ZBase32Encoder zb = new ZBase32Encoder();

        byte[] acquiredcode = zb.Decode(pProductCode);

        byte[]? decrypted = CryptHelper.Decrypt(acquiredcode, idString);

        if (decrypted == null)
        {
            decrypted = CryptHelper.Decrypt(acquiredcode);
        }

        if (decrypted != null)
        {
            MemoryStream ms = new MemoryStream(decrypted);
            ms.Seek(0, SeekOrigin.Begin);
            return FromDecryptedStream<T>(ms);
        }

        return null;
    }

    internal static T FromDecryptedStream<T>(Stream source) where T : LicenseKeyData
    {

        if (!source.CanRead) throw new ArgumentException(@"Stream must be readable.", "readFrom");
        try
        {

            ConstructorInfo? useConstructor = typeof(T).GetConstructor(new[] { typeof(Stream) });

            if (useConstructor == null)
            {
                throw new ArgumentException("Type " + typeof(T).Name + " does not have a constructor accepting arguments of type (Stream)");
            }

            object builtInstance = useConstructor.Invoke(new object[]{
                source
            });

            return (T)builtInstance;

        }
        catch (Exception ex)
        {
            throw new InvalidKeyException("Invalid Product Key", ex);

        }

    }

}