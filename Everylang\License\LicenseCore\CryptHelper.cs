﻿using Microsoft.Win32;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Everylang.App.License.LicenseCore;

internal class CryptHelper
{
    internal static string LocalMachineId
    {
        get
        {
            var ReadKey = "SOFTWARE\\Microsoft\\Cryptography";

            RegistryKey? rk = null;

            var mkid = "";

            try
            {
                rk = Registry.LocalMachine.OpenSubKey(ReadKey);
            }
            catch
            {
                // ignore
            }

            if (rk != null)

                mkid = rk.GetValue("MachineGuid") as string;

            if (string.IsNullOrEmpty(mkid)) mkid = Environment.MachineName;

            return mkid;
        }
    }

    internal static string WindowsProductId
    {
        get
        {
            var ReadKey = @"SOFTWARE\Microsoft\WIndows NT\CurrentVersion";

            RegistryKey? rk = null;

            var mkid = "";

            try
            {
                rk = Registry.LocalMachine.OpenSubKey(ReadKey);
            }
            catch
            {
                // ignore
            }

            if (rk != null)

                mkid = rk.GetValue("ProductId") as string;

            if (string.IsNullOrEmpty(mkid)) mkid = Environment.MachineName;

            return mkid;
        }
    }

    internal static byte[]? Encrypt(byte[] clearData, string password)
    {
        try
        {
            using var ms = new MemoryStream();
            using var sw = new StreamWriter(ms);
            sw.Write(password);

            ms.Seek(0, SeekOrigin.Begin);

            var readresults = new byte[ms.Length];

            _ = ms.Read(readresults, 0, readresults.Length);

            var pdb = GetPdb(password);

            return Encrypt(clearData, pdb.GetBytes(8), pdb.GetBytes(8));
        }
        catch
        {
            return null;
        }
    }

    internal static byte[]? Decrypt(byte[] clearData, string password = "")
    {
        try
        {
            using var ms = new MemoryStream();
            using var sw = new StreamWriter(ms);

            sw.Write(password);
            ms.Seek(0, SeekOrigin.Begin);

            var readresults = new byte[ms.Length];

            _ = ms.Read(readresults, 0, readresults.Length);

            var pdb = GetPdb(password);

            return Decrypt(clearData, pdb.GetBytes(8), pdb.GetBytes(8));
        }
        catch
        {
            return null;
        }
    }

    internal static string InsertDashes(string usevalue)
    {
        var flip5 = false;

        var countfrom = 0;

        var sb = new StringBuilder();

        for (var i = 0; i < usevalue.Length; i++)
        {
            countfrom++;

            sb.Append(usevalue[i]);

            if (countfrom % (flip5 ? 5 : 4) == 0)
            {
                sb.Append("-");

                flip5 = !flip5;

                countfrom = 0;
            }
        }

        return sb.ToString();
    }


    internal static Rfc2898DeriveBytes GetPdb(string password)
    {
        var useSalt = new byte[]
        {
            12,
            0,
            56,
            34,
            55,
            9,
            26,
            8
        };

#pragma warning disable SYSLIB0041
        return new Rfc2898DeriveBytes(password, useSalt);
#pragma warning restore SYSLIB0041
    }

    // ReSharper disable once InconsistentNaming
    internal static byte[] Encrypt(byte[] clearData, byte[] key, byte[] IV)
    {
        using var ms = new MemoryStream();
        var alg = RC2.Create();
        alg.Key = key;
        alg.IV = IV;
        using (var cs = new CryptoStream(ms, alg.CreateEncryptor(), CryptoStreamMode.Write))
        {
            cs.Write(clearData, 0, clearData.Length);
        }

        var encryptedData = ms.ToArray();
        return encryptedData;
    }

    // ReSharper disable once InconsistentNaming
    internal static byte[] Decrypt(byte[] cipherData, byte[] key, byte[] IV)
    {
        using var ms = new MemoryStream();
        var alg = RC2.Create();
        alg.Key = key;
        alg.IV = IV;

        using (var cs = new CryptoStream(ms, alg.CreateDecryptor(), CryptoStreamMode.Write))
        {
            cs.Write(cipherData, 0, cipherData.Length);
        }

        var decryptedData = ms.ToArray();

        return decryptedData;
    }
}