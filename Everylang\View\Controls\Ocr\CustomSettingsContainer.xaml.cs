﻿using System;
using System.Windows;
using Telerik.Windows.Imaging.Tools;
using Telerik.Windows.Media.Imaging.Tools;

namespace Everylang.App.View.Controls.Ocr
{
    internal partial class CustomSettingsContainer : IToolSettingsContainer
    {
        public CustomSettingsContainer()
        {
            InitializeComponent();
        }

        protected override void OnContentChanged(object oldContent, object? newContent)
        {
            base.OnContentChanged(oldContent, newContent);
            if (newContent == null)
            {
                this.Hide();
            }
        }


        void IToolSettingsContainer.Show(ITool tool, Action applyCallback, Action cancelCallback)
        {
            contentControl.Content = tool.GetSettingsUI();
            if (Visibility == Visibility.Collapsed && contentControl.Content != null)
            {
                Visibility = Visibility.Visible;
            }
        }

        public void Hide()
        {
            if (Visibility == Visibility.Collapsed && contentControl.Content != null)
            {
                Visibility = Visibility.Visible;
            }
            else
            {
                Visibility = Visibility.Collapsed;
            }
        }

        private void ButtonBase_OnClick(object sender, RoutedEventArgs e)
        {
            Hide();
        }
    }
}
