﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:helpers="clr-namespace:Everylang.App.View.Helpers"
                    xmlns:dataModel="clr-namespace:Everylang.App.Data.DataModel"
                    xmlns:translator="clr-namespace:Everylang.App.Translator">
    <Style TargetType="ToggleButton" x:Key="ToggleButtonEx">
        <Setter Property="Background" Value="Transparent"/>

        <Setter Property="FontFamily" Value="{DynamicResource DefaultFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Padding" Value="5,6"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="MinHeight" Value="25"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal"/>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="MouseOverBorder">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="PressedBorder">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity" Storyboard.TargetName="DisabledVisualElement">
                                            <SplineDoubleKeyFrame KeyTime="0" Value=".55"/>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="contentPresenter">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="0.5"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CheckStates">
                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="CheckedRectangle">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="CheckedInnerRectangle">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unchecked"/>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Focused">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="FocusRectangle">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="FocusInnerRectangle">
                                            <EasingDoubleKeyFrame KeyTime="0" Value="1"/>
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unfocused"/>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="Background" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="Transparent" CornerRadius="3"/>
                        <Rectangle x:Name="DisabledVisualElement" Fill="{DynamicResource WhiteColorBrush}" IsHitTestVisible="false" Opacity="0" RadiusY="3" RadiusX="3"/>
                        <Border x:Name="MouseOverBorder" Background="Transparent" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.5" Opacity="0"/>
                        <Border x:Name="PressedBorder"  Background="Transparent" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.5" Opacity="0"/>
                        <Border x:Name="CheckedBorder"  Background="Transparent" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.5" Opacity="0"/>
                        <Rectangle x:Name="FocusRectangle" Stroke="Transparent" RadiusY="4" RadiusX="4" Margin="-1" Opacity="0" />
                        <Rectangle x:Name="FocusInnerRectangle" StrokeThickness="{TemplateBinding BorderThickness}" Stroke="Transparent" RadiusX="3" RadiusY="3" Opacity="0" />
                        <Rectangle x:Name="CheckedRectangle" Stroke="Transparent" RadiusY="4" RadiusX="4" Margin="-1" Opacity="0" />
                        <Rectangle x:Name="CheckedInnerRectangle" Fill="Transparent" StrokeThickness="{TemplateBinding BorderThickness}" Stroke="Transparent" RadiusX="3" RadiusY="3" Opacity="0" />
                        <ContentPresenter x:Name="contentPresenter" RecognizesAccessKey="True" ContentTemplate="{TemplateBinding ContentTemplate}" Content="{TemplateBinding Content}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ComboBoxExWithListViewStyle" TargetType="{x:Type helpers:ComboBoxEx}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type helpers:ComboBoxEx}">
                    <Grid SnapsToDevicePixels="True">

                        <Border x:Name="Bd" SnapsToDevicePixels="true">
                            <Grid Grid.IsSharedSizeScope="true">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentPresenter x:Name="ContentPresenter"
                                                      HorizontalAlignment="Left"
                                                      VerticalAlignment="Center"
                                                      Grid.Column="0"
                                                      Margin="10,0,0,0"
                                                      Content="{TemplateBinding SelectionBoxItem}"
                                                      ContentTemplate="{TemplateBinding SelectedTemplateOverride}"
                                                      ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" />
                                <ToggleButton x:Name="DropDownToggle" 
                                              Style="{StaticResource ToggleButtonEx}"
                                                        Background="Transparent"
                                    					Margin="3" 
                                                        HorizontalContentAlignment="Right" 
                                                        Grid.ColumnSpan="3"
                                						IsChecked="{Binding Path=IsDropDownOpen,Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                                          >
                                    <Path x:Name="BtnArrow" Height="4" Width="8"  
                                							Stretch="Uniform" Margin="0,0,4,0"  Fill="Black" 
                                							Data="F1 M 300,-190L 310,-190L 305,-183L 301,-190 Z " />

                                </ToggleButton>
                            </Grid>
                        </Border>
                        <Popup x:Name="PART_Popup" 
                        			IsOpen="{TemplateBinding IsDropDownOpen}"
                                    >
                            <Border x:Name="PopupBorder"  
                        				HorizontalAlignment="Stretch"                                         
                        				MinWidth="{TemplateBinding ActualWidth}" 
                        				MaxHeight="300" 
                        				BorderThickness="{TemplateBinding BorderThickness}"  
                        				BorderBrush="{DynamicResource MahApps.Brushes.ComboBox.PopupBorder}" Width="Auto">
                                <ScrollViewer x:Name="ScrollViewer" BorderThickness="0" Padding="0" >
                                    <ItemsPresenter/>
                                </ScrollViewer>
                            </Border>
                        </Popup>

                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style x:Key="LangComboBoxStyle" TargetType="{x:Type helpers:ComboBoxEx}" BasedOn="{StaticResource ComboBoxExWithListViewStyle}">
        <Setter Property="Border.CornerRadius" Value="0" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Disabled" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="Width" Value="230" />
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <WrapPanel Orientation="Vertical" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>

        <Setter Property="ItemTemplate">
            <Setter.Value>
                <DataTemplate DataType="{x:Type translator:Language}">
                    <TextBlock Text="{Binding Path=Name}" FontSize="11" />
                </DataTemplate>
            </Setter.Value>
        </Setter>

    </Style>

    <Style x:Key="LangFromComboBoxStyle" TargetType="{x:Type helpers:ComboBoxEx}" BasedOn="{StaticResource LangComboBoxStyle}">
        <Setter Property="SelectedTemplateOverride">
            <Setter.Value>
                <DataTemplate DataType="{x:Type translator:Language}">
                    <Label Content="{Binding Path=SelectedNameFrom, 
                                    UpdateSourceTrigger=PropertyChanged, Mode=OneWay}"
                                VerticalContentAlignment="Center"/>
                </DataTemplate>
            </Setter.Value>

        </Setter>

    </Style>

    <Style x:Key="LangToComboBoxStyle" TargetType="{x:Type helpers:ComboBoxEx}" BasedOn="{StaticResource LangComboBoxStyle}">
        <Setter Property="SelectedTemplateOverride">
            <Setter.Value>
                <DataTemplate DataType="{x:Type translator:Language}">
                    <Label Content="{Binding Path=SelectedNameTo, 
                                    UpdateSourceTrigger=PropertyChanged, Mode=OneWay}"
                                VerticalContentAlignment="Center"/>
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>