﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.SwitcherLang;
using System.Collections.ObjectModel;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class SwitcherSettingsViewModel : ViewModelBase
    {
        public ObservableCollection<string> SwitchOnKeys { get; set; }

        public ObservableCollection<string> SwitchMethods { get; set; }

        public SwitcherSettingsViewModel()
        {
            SwitchOnKeys = new ObservableCollection<string>();
            SwitchMethods = new ObservableCollection<string>();
            FillSwitchOnKeys();
            FillSwitchMethods();
        }

        private void FillSwitchMethods()
        {
            SwitchMethods.Clear();
            SwitchMethods.Add(LocalizationManager.GetString("SwitcherSettingsSwitchMethod1"));
            SwitchMethods.Add(LocalizationManager.GetString("SwitcherSettingsSwitchMethod2"));
            // 0 Эмуляция клавиш переключения, способ 1
            // 1 Выполнение команды Windows
        }

        private void FillSwitchOnKeys()
        {
            SwitchOnKeys.Clear();
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrl"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLCtrl"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRShift"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLShift"));
            SwitchOnKeys.Add("Caps Lock");
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLRCtrl"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLRShift"));
            SwitchOnKeys.Add(LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock"));
            // 0 Стандартные настройки
            // 1 Правому Ctrl
            // 2 Левому Ctrl
            // 3 Правому Shift
            // 4 Левому Shift
            // 5 Caps Lock
            // 6 По правому или левому Ctrl
            // 7 По правому или левому Shift
            // 8 По правому Ctrl или Caps Lock

        }

        public string CurrentSwitchOnKey
        {
            get
            {

                if (SwitchOnKeys.Count > SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey)
                    return SwitchOnKeys[SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey];
                return SwitchOnKeys[0];
            }
            set
            {
                if (SwitchOnKeys.IndexOf(value) != -1)
                {
                    SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey = SwitchOnKeys.IndexOf(value);
                    base.OnPropertyChanged();
                    base.OnPropertyChanged(nameof(ToolTipForCurrentSwitchOnKey));
                }
            }
        }

        public string CurrentSwitchMethod
        {
            get
            {

                if (SwitchMethods.Count > SettingsManager.Settings.SwitcherSwitchMethod)
                    return SwitchMethods[SettingsManager.Settings.SwitcherSwitchMethod];
                return SwitchMethods[0];
            }
            set
            {
                if (SwitchMethods.IndexOf(value) != -1)
                {
                    SettingsManager.Settings.SwitcherSwitchMethod = SwitchMethods.IndexOf(value);
                    base.OnPropertyChanged();
                }
            }
        }

        public string ToolTipForCurrentSwitchOnKey
        {
            get
            {
                if (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 5 || SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 8)
                {
                    return LocalizationManager.GetString("SwitcherSettingsToolTipForCurrentSwitchOnKey");
                }
                return SwitchOnKeys[SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey];
            }
        }

        public string ShortcutSelected
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut);
            }
            set
            {
                SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut = value;
                base.OnPropertyChanged();

            }
        }

        public bool LeaveTextSelectedAfterSwitch
        {
            get
            {
                return SettingsManager.Settings.SwitcherLeaveTextSelectedAfterSwitch;
            }
            set
            {
                SettingsManager.Settings.SwitcherLeaveTextSelectedAfterSwitch = value;
                base.OnPropertyChanged();
            }
        }


        public string SwitcherSwitchTextLangShortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SwitcherSwitchTextLangShortcut);
            }
            set
            {
                SettingsManager.Settings.SwitcherSwitchTextLangShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string SwitcherSwitchTextLangForAllLineShortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut);
            }
            set
            {
                SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public bool SwitcherCtrlNumberIsOn
        {
            get
            {
                return SettingsManager.Settings.SwitcherSwitchLangByCtrlPlusNumberIsOn;
            }
            set
            {
                SettingsManager.Settings.SwitcherSwitchLangByCtrlPlusNumberIsOn = value;
                KeyboardLayoutManager.Instance.Stop();
                KeyboardLayoutManager.Instance.Start();
            }
        }

        public bool SwitcherSountIsOn
        {
            get
            {
                return SettingsManager.Settings.SwitcherSountIsOn;
            }
            set
            {
                SettingsManager.Settings.SwitcherSountIsOn = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(SwitcherIsOn));
            }
        }

        public bool SwitcherIsOn
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.SwitcherIsOn;
            }
            set
            {

                SettingsManager.Settings.SwitcherIsOn = value;

                if (!value)
                {
                    KeyboardLayoutManager.Instance.Stop();
                }
                else
                {
                    KeyboardLayoutManager.Instance.Start();
                }
                base.OnPropertyChanged();
            }
        }

        private void AskAutoSwitcherOff()
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOff"),
                Closed = (_, args) =>
                {
                    if (args.DialogResult == true)
                    {
                        VMContainer.Instance.AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch = false;
                    }
                }
            });
        }

        private void AskAutoSwitcherOn()
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOn"),
                Closed = (_, args) =>
                {
                    if (args.DialogResult == true)
                    {
                        VMContainer.Instance.AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch = true;
                    }
                }
            });
        }
    }
}
