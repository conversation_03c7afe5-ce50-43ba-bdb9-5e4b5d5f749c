﻿using System;
using System.Drawing;
using Vanara.PInvoke;

namespace Everylang.Note.Helpers
{
    class TaskBar
    {

        internal static void EnableCustomWindowPreview(IntPtr hwnd, bool enable)
        {
            try
            {
                int t = enable ? (int)DwmNcRenderingPolicy.Disabled : (int)DwmNcRenderingPolicy.Enabled;
                var rc = DwmApi.DwmSetWindowAttribute(hwnd, DwmApi.DWMWINDOWATTRIBUTE.DWMWA_FLIP3D_POLICY,
                    new IntPtr(t), 4);
                if (rc.Succeeded)
                {
                    DwmApi.DwmSetWindowAttribute(hwnd, DwmApi.DWMWINDOWATTRIBUTE.DWMWA_FORCE_ICONIC_REPRESENTATION,
                        new IntPtr(t), 4);
                }
            }
            catch
            {
                // ignore
            }
        }

        internal static void SetIconicThumbnail(IntPtr hwnd, Bitmap bitmap, bool withFrame, IntPtr lParam)
        {
            try
            {
                int width = (int)((long)lParam >> 16);
                int height = (int)(((long)lParam) & (0xFFFF));
                Size requestedSize = new Size(100, 100);
                bitmap = ResizeImageWithAspect(bitmap, requestedSize.Width, requestedSize.Height, true);
                uint t = (uint)(withFrame ? DwmApi.DWM_SETICONICPREVIEW_Flags.DWM_SIT_DISPLAYFRAME : DwmApi.DWM_SETICONICPREVIEW_Flags.DWM_SIT_NONE);
                var hBitmap = bitmap.GetHbitmap();
                var result = DwmApi.DwmSetIconicThumbnail(hwnd, hBitmap, (DwmApi.DWM_SETICONICPREVIEW_Flags)t);
                Gdi32.DeleteObject(hBitmap);
            }
            catch (Exception)
            {
                // ignore
            }
        }

        internal static Bitmap ResizeImageWithAspect(Bitmap originalBitmap, int newWidth, int maxHeight, bool resizeIfWider)
        {
            try
            {
                if (resizeIfWider && originalBitmap.Width <= newWidth)
                {
                    newWidth = originalBitmap.Width;
                }

                int newHeight = originalBitmap.Height * newWidth / originalBitmap.Width;

                if (newHeight > maxHeight) // Height resize if necessary
                {
                    newWidth = originalBitmap.Width * maxHeight / originalBitmap.Height;
                    newHeight = maxHeight;
                }

                // Create the new image with the sizes we've calculated
                return (Bitmap)originalBitmap.GetThumbnailImage(newWidth, newHeight, null, IntPtr.Zero);
            }
            finally
            {
                originalBitmap.Dispose();
            }
        }

        [Flags]
        internal enum DwmNcRenderingPolicy
        {
            UseWindowStyle,
            Disabled,
            Enabled,
            Last
        }

        internal static void EnableFlip3D(IntPtr hwnd, bool enable)
        {
            try
            {
                int t = enable ? (int)DwmNcRenderingPolicy.Disabled : (int)DwmNcRenderingPolicy.Enabled;
                DwmApi.DwmSetWindowAttribute(hwnd, DwmApi.DWMWINDOWATTRIBUTE.DWMWA_FLIP3D_POLICY, new IntPtr(t), 4);

            }
            catch
            {
                // ignore
            }
        }
    }
}
