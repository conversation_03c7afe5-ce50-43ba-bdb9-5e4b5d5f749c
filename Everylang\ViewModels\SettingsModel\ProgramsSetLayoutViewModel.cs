﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.Utilities;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class ProgramsSetLayoutViewModel : ViewModelBase
    {
        public RadObservableCollection<ProgramsSetLayoutDataModel?>? ProgramsSetLayoutList { get; set; }

        public RadObservableCollection<ProgramInfo> ProgramsList { get; set; }

        public DelegateCommand AddNewCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewTitleCommand
        {
            get;
            private set;
        }

        public DelegateCommand DeleteSelectionCommand
        {
            get;
            private set;
        }

        public DelegateCommand EditSelectionCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewFromListCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewExeFileCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewFilesFromFolderCommand
        {
            get;
            private set;
        }


        private bool _addTitle;

        public ProgramsSetLayoutViewModel()
        {
            ProgramsSetLayoutList = new RadObservableCollection<ProgramsSetLayoutDataModel?>();
            ProgramsList = new RadObservableCollection<ProgramInfo>();
            AddNewNotStarted = true;
            AddNewFromListCommand = new DelegateCommand(AddNewFromList);
            AddNewExeFileCommand = new DelegateCommand(AddNewExeFile);
            AddNewFilesFromFolderCommand = new DelegateCommand(AddNewFilesFromFolder);
            AddNewCommand = new DelegateCommand(AddNew);
            AddNewTitleCommand = new DelegateCommand(AddNewTitle);
            DeleteSelectionCommand = new DelegateCommand(DeleteSelection);
            EditSelectionCommand = new DelegateCommand(EditSelection);
            HookCallBackMouseUp.CallbackEventHandler += AddNewProgramByMouseClickEventHandler;
            GetAllDataFromDb();
        }

        private void AddNewFilesFromFolder(object o)
        {
            var dlg = new FolderBrowserDialog();
            dlg.ShowNewFolderButton = false;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                var exeFiles = ProgramsShellUtils.GetDirectoryFiles(dlg.SelectedPath, "*.exe", SearchOption.AllDirectories).ToList();
                foreach (var exeFile in exeFiles)
                {
                    AddProgramCommon(exeFile);
                }
            }
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
            }
        }

        private void AddNewExeFile(object o)
        {
            var dlg = new OpenFileDialog();
            dlg.DefaultExt = ".exe"; // Default file extension
            dlg.Filter = @"Executable file (.exe)|*.exe";
            if (dlg.ShowDialog() == DialogResult.OK)
                AddProgramCommon(dlg.FileName);
        }

        private void AddNewFromList(object o)
        {
            if (ProgramsListCurrent?.Name != null) AddProgramCommon(ProgramsListCurrent.Name);
        }

        internal async Task UpdateStartedPrograms()
        {
            var allStartedProgramsAsync = await ProgramsShellUtils.GetAllStartedProgramsAsync();
            var programInfos = allStartedProgramsAsync.Where(programInfo => ProgramsSetLayoutList != null && ProgramsSetLayoutList.All(x => x?.Program != programInfo.Name)).ToList();
            ProgramsList.Clear();
            ProgramsList.AddRange(programInfos);
        }


        private void DeleteSelection(object o)
        {
            RemoveProgramFromDb(SelectedProgram);
            ProgramsSetLayoutList?.Remove(SelectedProgram);
            base.OnPropertyChanged(nameof(IsSelected));
            base.OnPropertyChanged(nameof(SelectedProgram));
        }

        private void EditSelection(object o)
        {
            string? text = SelectedProgram?.Program;
            bool isTitle = false;
            if (SelectedProgram?.Title != "")
            {
                text = SelectedProgram?.Title;
                isTitle = true;
            }
            RadWindow.Prompt(LocalizationManager.GetString("Edit"), (_, args) =>
            {
                if (args.DialogResult != null && !string.IsNullOrEmpty(args.PromptResult))
                {
                    if (isTitle)
                    {
                        if (SelectedProgram != null) SelectedProgram.Title = args.PromptResult;
                    }
                    else
                    {
                        if (SelectedProgram != null) SelectedProgram.Program = args.PromptResult;
                    }
                }
            }, text);
            ProgramsSetLayoutManager.UpdateData(SelectedProgram);
            GetAllDataFromDb();
            base.OnPropertyChanged(nameof(IsSelected));
            base.OnPropertyChanged(nameof(SelectedProgram));
        }

        private void AddNew(object o)
        {

            try
            {
                _addTitle = false;
                AddNewNotStarted = false;
            }
            catch
            {
                // Ignored
            }

        }

        private void AddNewTitle(object o)
        {

            try
            {
                _addTitle = true;
                AddNewNotStarted = false;
            }
            catch
            {
                // Ignored
            }

        }

        private void AddNewProgramByMouseClickEventHandler(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (!AddNewNotStarted)
            {
                AddProgramCommon();
            }
        }

        private void AddProgramCommon(string? program = "")
        {
            if (program == "")
            {
                program = CheckActiveProcessFileName.GetActiveProcessName(true, _addTitle);
            }

            ProgramsSetLayoutDataModel? dataModel = ProgramsSetLayoutList?.FirstOrDefault(x => x?.Program == program || x?.Title == program);
            if (dataModel == null)
            {
                dataModel = new ProgramsSetLayoutDataModel();
                if (_addTitle)
                {
                    dataModel.Title = program;
                }
                else
                {
                    dataModel.Program = program;
                }
                ProgramsSetLayoutList?.Add(dataModel);
                AddNewProgramToDb(dataModel);
                SelectedProgram = dataModel;
            }
            else
            {
                SelectedProgram = dataModel;
            }
            AddNewNotStarted = true;
            _addTitle = false;
        }

        private ProgramInfo? _programsListCurrent;

        public ProgramInfo? ProgramsListCurrent
        {
            get
            {
                return _programsListCurrent;
            }
            set
            {
                _programsListCurrent = value;
                base.OnPropertyChanged(nameof(IsProgramsListCurrentSelected));
            }
        }

        public bool IsProgramsListCurrentSelected => ProgramsListCurrent != null;

        public bool IsSelected => SelectedProgram != null;

        private bool _addNewNotStarted;

        public bool AddNewNotStarted
        {
            get { return _addNewNotStarted; }
            set
            {
                _addNewNotStarted = value;
                base.OnPropertyChanged();
            }
        }

        private ProgramsSetLayoutDataModel? _selectedProgram;

        public ProgramsSetLayoutDataModel? SelectedProgram
        {
            get
            {
                return _selectedProgram;
            }
            set
            {
                _selectedProgram = value;
                base.OnPropertyChanged(nameof(IsSelected));
                base.OnPropertyChanged(nameof(SelectedProgram));
            }
        }

        internal void GetAllDataFromDb()
        {
            ProgramsSetLayoutList?.Clear();
            ProgramsSetLayoutList?.AddRange(ProgramsSetLayoutManager.GetAllData());
        }

        internal void AddNewProgramToDb(ProgramsSetLayoutDataModel? model)
        {
            ProgramsSetLayoutManager.AddData(model);
        }

        internal void RemoveProgramFromDb(ProgramsSetLayoutDataModel? model)
        {
            ProgramsSetLayoutManager.DelData(model);
        }

        internal void UpdateAllInDb()
        {
            if (ProgramsSetLayoutList != null)
                foreach (var programsSetLayoutDataModel in ProgramsSetLayoutList)
                {
                    ProgramsSetLayoutManager.UpdateData(programsSetLayoutDataModel);
                }
        }
    }


}