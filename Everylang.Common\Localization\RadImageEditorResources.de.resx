<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Close" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Korrektur</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Summe</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Hintergrund:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Randfarbe:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Randstärke:</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Ändern der Leinwandgröße</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Leinwandgröße</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Trimmen</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Bildtext</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Dein Text</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Änderung</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Verwischen</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Helligkeit</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Kontrast</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Farbton ändern</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Farben umkehren</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Sättigung</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Schärfen</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Horizontal spiegeln</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Vertikal spiegeln</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Schriftgröße</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Höhe:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Horizontale Position</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Bildausrichtung</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Bildvorschau</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Bildgröße</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Offen</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Optionen</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Behalten Sie das ursprüngliche Seitenverhältnis bei</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Radius:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Zurückkehren</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Relative Größe</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Größe ändern</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Um 180° drehen</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Um 270° drehen</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Um 90° drehen</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Drehen</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Abgerundete Ecken</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Textfarbe</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Die Datei kann nicht geöffnet werden.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Die Datei kann nicht geöffnet werden. Dies kann durch eine andere Anwendung blockiert werden.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Konvertieren</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Datei konnte nicht gespeichert werden.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Dieses Dateiformat wird nicht unterstützt.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Vertikale Position</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Breite:</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Ziehen</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Pinselfarbe:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Pinselgröße:</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Figur</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Randfarbe</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Randstärke</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Rohrfüllformular</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Proportionen sperren</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Figur</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Eine Form füllen</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>Weiß</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Zeitplan</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rechteck</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Alles zurücksetzen</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Stornieren</value>
  </data>
</root>