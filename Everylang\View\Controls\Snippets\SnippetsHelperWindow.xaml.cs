﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SwitcherLang;
using Everylang.App.ViewModels;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Snippets
{
    /// <summary>
    /// Interaction logic for AutochangeHelperWindow.xaml
    /// </summary>
    internal partial class SnippetsHelperWindow
    {
        //private bool _shown;
        private readonly bool _new;
        public SnippetsDataModel AutochangeDataModel { get; set; }

        internal SnippetsHelperWindow(SnippetsDataModel? autochangeDataModel, string? text, string tagSt)
        {
            Owner = Application.Current.MainWindow;


            if (autochangeDataModel != null)
            {
                AutochangeDataModel = (SnippetsDataModel)autochangeDataModel.Clone();
            }
            else
            {
                var model = VMContainer.Instance.SnippetsViewModel.SnippetsList.FirstOrDefault(x => x.Text == text);
                if (model == null)
                {
                    AutochangeDataModel = new SnippetsDataModel();
                    AutochangeDataModel.Tags = "";
                    if (!string.IsNullOrEmpty(tagSt))
                    {
                        AutochangeDataModel.Tags = tagSt;
                    }
                    if (!string.IsNullOrEmpty(text))
                    {
                        AutochangeDataModel.Text = text;
                    }
                    _new = true;
                }
                else
                {
                    AutochangeDataModel = model;
                }
            }
            //DataContext = _autochangeDataModel;
            InitializeComponent();
            foreach (var model in VMContainer.Instance.SnippetsViewModel.SnippetsList)
            {
                if (model?.Tags != null)
                {
                    var tags = model.Tags.Split().ToList();
                    foreach (var tag in tags)
                    {
                        if (!string.IsNullOrEmpty(tag.Trim()) && !ComboTrueListOfTags.Items.Contains(tag.Trim()))
                        {
                            ComboTrueListOfTags.Items.Add(tag.Trim());
                        }
                    }
                }
            }
            var list = KeyboardLayoutMethods.GetInputLangs();
            ComboLangList.Items.Clear();
            ComboLangList.Items.Add(LocalizationManager.GetString("AutochangeHelperLangListNoSwitch"));
            foreach (var cultureInfo in list)
            {
                ComboLangList.Items.Add(cultureInfo?.DisplayName);
            }

            if (string.IsNullOrEmpty(AutochangeDataModel.LangToSwitch))
            {
                ComboLangList.SelectedIndex = 0;
            }
            else
            {
                ComboLangList.SelectedItem =
                    list.Find(x => x.TwoLetterISOLanguageName.ToLower() == AutochangeDataModel.LangToSwitch)?.DisplayName;
            }
            TextBoxTo.Focus();
            TextBoxTo.CaretIndex = AutochangeDataModel.CursorPosition;

        }

        private void Close(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
        }

        private void TextBox_OnKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SaveData();
                this.DialogResult = true;
                Close();
            }
        }

        private void SaveData()
        {
            Focus();
            if (string.IsNullOrEmpty(AutochangeDataModel.Text))
            {
                return;
            }
            AutochangeDataModel.CursorPosition = TextBoxTo.CaretIndex;
            if (ComboLangList.SelectedIndex == 0)
            {
                AutochangeDataModel.LangToSwitch = "";
            }
            if (ComboLangList.SelectedIndex > 0)
            {
                var list = KeyboardLayoutMethods.GetInputLangs();
                AutochangeDataModel.LangToSwitch = list
                    .Find(x => x.DisplayName == ComboLangList.SelectedItem.ToString())?.TwoLetterISOLanguageName.ToLower();
            }
            if (string.IsNullOrEmpty(AutochangeDataModel.ShortText))
            {
                AutochangeDataModel.ShortText = AutochangeDataModel.Text.Length > 150 ? AutochangeDataModel.Text.Trim().Substring(0, 150) + "......" : AutochangeDataModel.Text.Trim();
            }
            if (_new)
            {
                SnippetsManager.AddSnippetsData(AutochangeDataModel);
            }
            else
            {
                SnippetsManager.UpdateData(AutochangeDataModel);
            }
        }

        private void SaveCloseButton_OnClick(object sender, RoutedEventArgs e)
        {
            SaveData();
            this.DialogResult = true;
            Close();
        }

        class TagModel : INotifyPropertyChanged
        {
            private Dictionary<string, object> _items;
            private Dictionary<string, object> _selectedItems;

            internal TagModel()
            {
                Items = new Dictionary<string, object>();
                SelectedItems = new Dictionary<string, object>();
            }


            internal Dictionary<string, object> Items
            {
                get
                {
                    return _items;
                }
                set
                {
                    _items = value;
                    OnPropertyChanged();
                }
            }

            internal Dictionary<string, object> SelectedItems
            {
                get
                {
                    return _selectedItems;
                }
                set
                {

                    _selectedItems = value;
                    OnPropertyChanged();
                }
            }

            public event PropertyChangedEventHandler? PropertyChanged;

            protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }

        private void AddTagClick(object sender, RoutedEventArgs e)
        {
            if (AutochangeDataModel.Tags == null)
            {
                AutochangeDataModel.Tags = "";
            }
            if (ComboTrueListOfTags.SelectedItem != null && !AutochangeDataModel.Tags.Contains((string)ComboTrueListOfTags.SelectedItem))
            {
                AutochangeDataModel.Tags += " " + ComboTrueListOfTags.SelectedItem;
            }
        }

        private void SnippetsHelperWindow_OnPreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                this.DialogResult = false;
                Close();
            }
        }
    }
}
