﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.ViewModels;
using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using PlacementMode = System.Windows.Controls.Primitives.PlacementMode;
using TextDataFormat = System.Windows.Forms.TextDataFormat;

namespace Everylang.App.View.Controls.Common
{
    /// <summary>
    /// Interaction logic for FastActionTextWindow.xaml
    /// </summary>
    internal partial class FastActionTextWindow : INotifyPropertyChanged
    {
        private readonly int _type;
        private string? _selectedText;

        internal static readonly DependencyProperty IsStayOnTopProperty =
            DependencyProperty.Register("IsStayOnTop",
                typeof(bool),
                typeof(FastActionTextWindow),
                new FrameworkPropertyMetadata());

        internal bool IsStayOnTop
        {
            get { return (bool)GetValue(IsStayOnTopProperty); }
            set
            {
                SetValue(IsStayOnTopProperty, value);
            }
        }

        internal static readonly DependencyProperty IsNotFindNowProperty =
            DependencyProperty.Register("IsNotFindNow",
                typeof(bool),
                typeof(FastActionTextWindow),
                new FrameworkPropertyMetadata());

        internal bool IsNotFindNow
        {
            get { return (bool)GetValue(IsNotFindNowProperty); }
            set
            {
                SetValue(IsNotFindNowProperty, value);
            }
        }

        internal static readonly DependencyProperty TitleTextProperty =
            DependencyProperty.Register("TitleText",
                typeof(string),
                typeof(FastActionTextWindow),
                new FrameworkPropertyMetadata(""));

        internal string TitleText
        {
            get { return (string)GetValue(TitleTextProperty); }
            set { SetValue(TitleTextProperty, value); }
        }

        internal static readonly DependencyProperty FastActionIndexProperty =
            DependencyProperty.Register("FastActionIndex",
                typeof(string),
                typeof(FastActionTextWindow),
                new FrameworkPropertyMetadata(""));

        internal string FastActionIndex
        {
            get { return (string)GetValue(FastActionIndexProperty); }
            set { SetValue(FastActionIndexProperty, value); }
        }

        internal Action? OnClosedWindow;


        internal FastActionTextWindow(int type)
        {
            InitializeComponent();

            Opened += OnOpened;
            IsNotFindNow = true;
            MouseDown += OnMouseDown;
            ThumbMy.DragDelta += OnDragDelta;

            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            HookCallBackMouseWheel.CallbackEventHandler += HookManagerMouseWheel;
            _currentSelected = -1;
            _type = type;
            _selectedText = null;
            DataContext = this;
            FastActionIndex = LocalizationManager.GetString("FastActionIndex");
            GridPassword.Visibility = Visibility.Collapsed;
            if (type == 1)
            {
                LoadDiary();
                if (!string.IsNullOrEmpty(SettingsManager.Settings.DiaryActionWindowSize))
                {
                    var strSize = SettingsManager.Settings.DiaryActionWindowSize.Split('|');
                    if (strSize.Length > 1)
                    {
                        this.Height = Convert.ToDouble(strSize[0]);
                        this.Width = Convert.ToDouble(strSize[1]);
                    }
                }
            }
            if (type == 2)
            {
                lvFastAction_Bind(VMContainer.Instance.ClipboardViewModel.AllClipboardItems);
                VMContainer.Instance.ClipboardViewModel.AllClipboardItems.CollectionChanged += ItemsOnCollectionChanged;
                TitleText = LocalizationManager.GetString("ClipboardTab");
                if (!string.IsNullOrEmpty(SettingsManager.Settings.ClipboardFastActionWindowSize))
                {
                    var strSize = SettingsManager.Settings.ClipboardFastActionWindowSize.Split('|');
                    if (strSize.Length > 1)
                    {
                        this.Height = Convert.ToDouble(strSize[0]);
                        this.Width = Convert.ToDouble(strSize[1]);
                    }
                }

            }
        }



        [DllImport("user32.dll")]
        static extern IntPtr SetActiveWindow(IntPtr hWnd);

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;

                //activate the popup

                SetActiveWindow(handle);
            }
        }

        private async void LoadDiary()
        {
            if (!string.IsNullOrEmpty(SettingsManager.Settings.DiaryPassword) && (DateTime.Now - VMContainer.Instance.DiaryViewModel.LastPasswordRequest).Minutes > 10)
            {
                GridPassword.Visibility = Visibility.Visible;
                lvFastAction.Visibility = Visibility.Collapsed;
                TextBoxSearch.Visibility = Visibility.Collapsed;
                await Task.Delay(100);
                PasswordBoxMu.Focus();
            }
            else
            {
                GridPassword.Visibility = Visibility.Collapsed;
                lvFastAction_Bind(VMContainer.Instance.DiaryViewModel.AllDiaryItems);
            }
            VMContainer.Instance.DiaryViewModel.AllDiaryItems.CollectionChanged += ItemsOnCollectionChanged;
            TitleText = LocalizationManager.GetString("DiareTab");
        }

        private void HookManagerMouseWheel(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                return;
            }
            //Decorator border = VisualTreeHelper.GetChild(lvFastAction, 0) as Decorator;
            //if (border != null)
            {
                var mouse = InputManager.Current.PrimaryMouseDevice;
                var args = new MouseWheelEventArgs(mouse, Environment.TickCount, (int)e.wheelRotation);
                args.RoutedEvent = MouseWheelEvent;
                ScrollViewer? scrollViewer = lvFastAction.Template.FindName("PART_ScrollViewer", lvFastAction) as ScrollViewer;
                if (scrollViewer != null) scrollViewer.RaiseEvent(args);
                e.Handled = true;
            }
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (TextBoxSearch.IsFocused)
            {
                IsNotFindNow = false;
            }

            if (!IsMouseOver)
            {
                if (_menuIsOpen)
                {
                    if (_contextMenu != null) _contextMenu.IsOpen = false;
                    return;
                }
                if (lvFastAction.ContextMenu != null)
                {
                    if (!lvFastAction.ContextMenu.IsOpen)
                    {
                        CloseWin();
                    }
                }
                else
                {
                    CloseWin();
                }
            }
        }

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {
            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;
        }

        private void OnMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                ThumbMy.RaiseEvent(e);
            }
        }

        private void OnDragDeltaAll(object sender, DragDeltaEventArgs e)
        {
            Width = Math.Min(MaxWidth, Math.Max(Width + e.HorizontalChange, MinWidth));
            Height = Math.Min(MaxHeight, Math.Max(Height + e.VerticalChange, MinHeight));
        }

        DateTime _lastMouseDown = DateTime.MinValue;

        private void Border_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {

            if ((DateTime.Now - _lastMouseDown).TotalMilliseconds < 300)
            {
                if (Math.Abs(this.Width - this.MaxWidth) < 5)
                {
                    this.Width = this.MinWidth;
                    this.Height = this.MinHeight;
                }
                else
                {
                    this.Width = this.MaxWidth;
                    this.Height = this.MaxHeight;
                }
                _lastMouseDown = DateTime.MinValue;
            }
            _lastMouseDown = DateTime.Now;
        }



        // ReSharper disable once MemberInitializerValueIgnored
        private int _currentSelected = -1;

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (GridPassword.Visibility == Visibility.Visible)
            {
                if (e.KeyCode == VirtualKeycodes.Enter)
                {
                    e.Handled = true;
                    DiaryButtonPasswordOk_OnClick(null, null);
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.Backspace)
                {
                    if (PasswordBoxMu.Password.Length < 2)
                    {
                        PasswordBoxMu.Password = "";
                    }
                    else
                    {
                        PasswordBoxMu.Password = PasswordBoxMu.Password.Substring(0, PasswordBoxMu.Password.Length - 1);
                        SetPasswordDiarySelection();
                    }
                }
                else
                {
                    var currentKeyboardLayout = KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl();
                    string currentText = KeyboardLayoutMethods.CodeToString(e, currentKeyboardLayout);
                    PasswordBoxMu.Password += currentText;
                    SetPasswordDiarySelection();
                }
                e.Handled = true;
                return;
            }
            if (TextBoxSearch.IsFocused)
            {
                IsNotFindNow = false;
                if (e.KeyCode == VirtualKeycodes.Backspace && TextBoxSearch.Text != "")
                {
                    TextBoxSearch.Text = TextBoxSearch.Text.Remove(TextBoxSearch.Text.Length - 1);
                    //TextBoxSearch.CaretIndex = TextBoxSearch.Text.Length;
                    e.Handled = true;
                    return;
                }
                string c = KeyboardLayoutMethods.CodeToString(e);
                if (!string.IsNullOrEmpty(c) && (!char.IsSurrogate(c[0]) && !char.IsControl(c[0])))
                {
                    TextBoxSearch.Text += c;
                    //TextBoxSearch.CaretIndex = TextBoxSearch.Text.Length;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.Esc)
                {
                    IsNotFindNow = true;
                    TextBoxSearch.Text = "";
                    lvFastAction.Focus();
                    e.Handled = true;
                    return;
                }

                if (e.KeyCode == VirtualKeycodes.Tab)
                {
                    IsNotFindNow = true;
                    lvFastAction.Focus();
                    e.Handled = true;
                    return;
                }
            }
            if (e.KeyCode == VirtualKeycodes.Tab)
            {
                IsNotFindNow = false;
                TextBoxSearch.Focus();
                e.Handled = true;
                return;
            }
            if (IsStayOnTop || lvFastAction.Items.Count == 0)
            {
                return;
            }


            if (e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {

                if (lvFastAction.SelectedIndex == -1)
                {
                    _currentSelected = 0;
                    lvFastAction.SelectedIndex = 0;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.UpArrow)
                {
                    var oldSelIndex = _currentSelected;
                    if (_currentSelected == 0)
                    {
                        _currentSelected = lvFastAction.Items.Count - 1;
                    }
                    else
                    {
                        _currentSelected -= 1;
                    }
                    if (e.Shift != ModifierKeySide.None)
                    {
                        if (!lvFastAction.SelectedItems.Contains(lvFastAction.Items[_currentSelected]))
                        {
                            lvFastAction.SelectedItems.Add(lvFastAction.Items[_currentSelected]);
                        }
                        else
                        {
                            if (oldSelIndex != -1)
                            {
                                lvFastAction.SelectedItems.Remove(lvFastAction.Items[oldSelIndex]);
                            }
                        }
                    }
                    else
                    {
                        if (_currentSelected < -1)
                        {
                            _currentSelected = -1;
                        }
                        lvFastAction.SelectedIndex = _currentSelected;
                    }

                }
                if (e.KeyCode == VirtualKeycodes.DownArrow)
                {
                    var oldSelIndex = _currentSelected;
                    if (_currentSelected == lvFastAction.Items.Count - 1)
                    {
                        _currentSelected = 0;
                    }
                    else
                    {
                        _currentSelected += 1;
                    }
                    if (e.Shift != ModifierKeySide.None)
                    {
                        if (!lvFastAction.SelectedItems.Contains(lvFastAction.Items[_currentSelected]))
                        {
                            lvFastAction.SelectedItems.Add(lvFastAction.Items[_currentSelected]);
                        }
                        else
                        {
                            if (oldSelIndex != -1)
                            {
                                lvFastAction.SelectedItems.Remove(lvFastAction.Items[oldSelIndex]);
                            }
                        }
                    }
                    else
                    {
                        if (_currentSelected < -1)
                        {
                            _currentSelected = -1;
                        }
                        lvFastAction.SelectedIndex = _currentSelected;
                    }
                }
                lvFastAction.UpdateLayout();
                if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
                    lvFastAction.ScrollIntoView(lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1]);
                lvFastAction.UpdateLayout();
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                e.Handled = true;
                CloseWin();
            }
            if (e.KeyCode == VirtualKeycodes.Enter)
            {
                e.Handled = true;
                Replace();
            }
            int i;
            if (int.TryParse(KeyboardLayoutMethods.CodeToString(e), out i))
            {
                if (i > 0 && i < 10)
                {
                    e.Handled = true;
                    lvFastAction.SelectedIndex = i - 1;
                    Replace();

                }
            }
            if (e.KeyCode == VirtualKeycodes.C && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();
            }

            if (e.KeyCode == VirtualKeycodes.Insert && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();
            }
            if (e.KeyCode == VirtualKeycodes.A && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                //lvFastAction.sele.SelectAll();
            }
            if (e.KeyCode == VirtualKeycodes.Delete)
            {
                if (lvFastAction.SelectedItems.Count > 0)
                {
                    if (_type == 1) VMContainer.Instance.DiaryViewModel.SelectedItems.Clear();
                    if (_type == 2) VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
                    foreach (var selectedItem in lvFastAction.SelectedItems)
                    {
                        if (_type == 1)
                        {
                            VMContainer.Instance.DiaryViewModel.SelectedItems.Add((DiaryDataModel)((ItemsSourceStruct)selectedItem).Obj);
                        }
                        if (_type == 2)
                        {
                            VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)((ItemsSourceStruct)selectedItem).Obj);
                        }
                    }
                    if (_type == 1) VMContainer.Instance.DiaryViewModel.DeleteSelected(null);
                    if (_type == 2) VMContainer.Instance.ClipboardViewModel.DeleteSelected(null);
                    e.Handled = true;
                }
            }
        }

        private void CloseWin()
        {
            if (!IsStayOnTop)
            {
                HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
                HookCallBackMouseDown.CallbackEventHandler -= HookManagerMouseDown;
                HookCallBackMouseWheel.CallbackEventHandler -= HookManagerMouseWheel;
                MouseDown -= OnMouseDown;
                ThumbMy.DragDelta -= OnDragDelta;
                if (_type == 1)
                {
                    SettingsManager.Settings.DiaryActionWindowSize = this.Height + "|" + this.Width;
                }
                if (_type == 2)
                {
                    SettingsManager.Settings.ClipboardFastActionWindowSize = this.Height + "|" + this.Width;
                }
                if (lvFastAction.ContextMenu != null) lvFastAction.ContextMenu.IsOpen = false;
                lvFastAction.ItemsSource = null;
                if (OnClosedWindow != null) OnClosedWindow();
                IsOpen = false;
            }

        }


        private void ItemsOnCollectionChanged(object? sender, NotifyCollectionChangedEventArgs notifyCollectionChangedEventArgs)
        {
            if (_type == 1)
            {
                lvFastAction_Bind(VMContainer.Instance.DiaryViewModel.AllDiaryItems);
            }
            if (_type == 2)
            {
                lvFastAction_Bind(VMContainer.Instance.ClipboardViewModel.AllClipboardItems);
            }
        }

        internal struct ItemsSourceStruct
        {
            internal int Index { get; set; }
            internal object Obj { get; set; }
        }

        private void lvFastAction_Bind(RadObservableCollection<DiaryDataModel> list, string text = "")
        {
            var listNew = list.ToList();
            if (!string.IsNullOrEmpty(text))
            {
                listNew = list.Where(x => x?.Text?.IndexOf(text, StringComparison.InvariantCultureIgnoreCase) >= 0).ToList();
            }
            var query = listNew.Select(e => new ItemsSourceStruct { Index = listNew.IndexOf(e), Obj = e }).ToList();

            lvFastAction.ItemsSource = query;
        }
        private void lvFastAction_Bind(RadObservableCollection<ClipboardDataModel> list, string text = "")
        {
            var listNew = list.ToList();
            if (!string.IsNullOrEmpty(text))
            {
                listNew = list.Where(x => x?.Text?.IndexOf(text, StringComparison.InvariantCultureIgnoreCase) >= 0).ToList();
            }
            var query = listNew.Select(e => new ItemsSourceStruct { Index = listNew.IndexOf(e), Obj = e }).ToList();

            lvFastAction.ItemsSource = query;
        }
        private void lvFastAction_Bind(RadObservableCollection<HistoryTranslationModel> list, string text = "")
        {
            var listNew = list.ToList();
            if (!string.IsNullOrEmpty(text))
            {
                listNew = list.Where(x => x?.Text?.IndexOf(text, StringComparison.InvariantCultureIgnoreCase) >= 0).ToList();
            }
            var query = listNew.Select(e => new ItemsSourceStruct { Index = listNew.IndexOf(e), Obj = e }).ToList();

            lvFastAction.ItemsSource = query;
        }



        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            CloseWin();
        }

        private void ClickSetStayOnTop(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = !IsStayOnTop;
            OnPropertyChanged(nameof(IsStayOnTop));
        }
        private void ClickOpenSettings(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            if (_type == 1) GlobalEventsApp.OnEventOpenSettingsDiary();
            if (_type == 2) GlobalEventsApp.OnEventOpenSettingsClipboard();
            CloseWin();
        }

        private void ClickOpenList(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            if (_type == 1) GlobalEventsApp.OnEventOpenListDiary();
            if (_type == 2) GlobalEventsApp.OnEventOpenListClipboard();
            CloseWin();
        }

        private void Copy()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                if (_selectedText != null && !string.IsNullOrEmpty(_selectedText))
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedText);
                    CloseWin();
                    return;
                }

                if (_type == 2 && lvFastAction.SelectedItems.Count > 0)
                {
                    var asd = ((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[0]!).Obj);
                    if (asd.IsImage)
                    {
                        ClipboardMonitorWorker.IgnoreLast = true;
                        var imStream = asd.GetImage();
                        if (imStream != null)
                            using (System.Drawing.Image img = System.Drawing.Image.FromStream(imStream))
                            {
                                System.Windows.Forms.Clipboard.SetImage(img);
                            }
                        return;
                    }
                }

                var copyText = "";
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    if (_type == 1) copyText += ((DiaryDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (_type == 2) copyText += ((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (_type == 3) copyText += ((HistoryTranslationModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (i != lvFastAction.SelectedItems.Count - 1)
                    {
                        copyText += Environment.NewLine;
                    }
                }
                ClipboardOperations.SetTextWithoutHistory(copyText);
                CloseWin();
            }
        }

        private void LvFastAction_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (lvFastAction.SelectedItems.Count > lvFastAction.SelectedItems.Count - 1)
            {
                var selIndex = lvFastAction.SelectedItems[lvFastAction.SelectedItems.Count - 1];
                lvFastAction.SelectedItems.Clear();
                lvFastAction.SelectedItem = selIndex;
                Replace();
            }


        }

        private void Replace()
        {
            if (lvFastAction.SelectedItems != null && lvFastAction.SelectedItems.Count > 0)
            {
                if (_type == 2)
                {
                    var asd = ((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[0]!).Obj);
                    if (asd.IsImage)
                    {
                        if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
                        {
                            ClipboardMonitorWorker.IgnoreLast = true;
                        }
                        var imStream = asd.GetImage();
                        if (imStream != null)
                            using (System.Drawing.Image img = System.Drawing.Image.FromStream(imStream))
                            {
                                System.Windows.Forms.Clipboard.SetImage(img);
                            }
                        CloseWin();
                        ClipboardOperations.SendPasteText();

                        return;
                    }
                }
                string replacedText = "";
                for (int i = 0; i < lvFastAction.SelectedItems.Count; i++)
                {
                    if (_type == 1) replacedText += ((DiaryDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (_type == 2) replacedText += ((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (_type == 3) replacedText += ((HistoryTranslationModel)((ItemsSourceStruct)lvFastAction.SelectedItems[i]!).Obj).Text;
                    if (i != lvFastAction.SelectedItems.Count - 1)
                    {
                        replacedText += Environment.NewLine;
                    }
                }
                CloseWin();
                if (_type == 2)
                {
                    if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
                    {
                        Utilities.SendText.SendStringByPaste(replacedText, false);
                    }
                    else
                    {
                        Utilities.SendText.SendStringByPaste(replacedText, false, true);
                    }

                }
                else
                {
                    Utilities.SendText.SendStringByPaste(replacedText, false);
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void TextBoxBase_OnSelectionChanged(string text)
        {
            _selectedText = text;
        }

        private RadButton? _currentButtonMenu;

        private void ListBoxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                FrameworkElement? element = sender as FrameworkElement;
                if (element != null)
                {
                    var item = GetSelectedListBoxItem(element);
                    if (item != null)
                    {
                        var buttonListBoxItem = GetSelectedButtonItem() as RadButton;
                        if (buttonListBoxItem != null && buttonListBoxItem.Name == "ButtonMenu")
                        {
                            _currentButtonMenu = buttonListBoxItem;
                            lvFastAction.SelectedItem = item.DataContext;
                            ShowMenuClick();
                        }
                        else
                        {
                            if ((Keyboard.Modifiers & ModifierKeys.Shift) != 0)
                            {
                                try
                                {
                                    object ob = (ItemsSourceStruct)item.DataContext;
                                    var mouseIndex = lvFastAction.Items.IndexOf(ob);
                                    var minIndex = lvFastAction.SelectedIndex < mouseIndex
                                        ? lvFastAction.SelectedIndex
                                        : mouseIndex;

                                    var allWillSelecte = Math.Abs(lvFastAction.SelectedIndex - mouseIndex);
                                    for (int i = 0; i < allWillSelecte + 1; i++)
                                    {
                                        if (lvFastAction.Items.Count > i + minIndex)
                                            lvFastAction.SelectedItems.Add(lvFastAction.Items[i + minIndex]);
                                    }
                                }
                                catch
                                {
                                    // ignore
                                }
                            }
                            else if ((Keyboard.Modifiers & ModifierKeys.Control) != 0)
                            {
                                lvFastAction.SelectedItems.Add(item.DataContext);

                            }
                            else
                            {
                                lvFastAction.SelectedItem = item.DataContext;
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        private object? GetSelectedButtonItem()
        {
            try
            {
                var item = VisualTreeHelper.HitTest(lvFastAction, Mouse.GetPosition(lvFastAction)).VisualHit;
                while (VisualTreeHelper.GetParent(item) != null && !(item is RadButton))
                {
                    item = VisualTreeHelper.GetParent(item);
                }
                return item;
            }
            catch
            {
                return null;
            }
        }

        private RadListBoxItem? GetSelectedListBoxItem(FrameworkElement element)
        {
            try
            {
                var item = element;
                while (item != null && VisualTreeHelper.GetParent(item) != null && !(item is RadListBoxItem))
                {
                    item = VisualTreeHelper.GetParent(item) as FrameworkElement;
                }
                if (!(item is RadListBoxItem))
                {
                    return null;
                }
                return (RadListBoxItem)item;

            }
            catch
            {
                return null;
            }
        }

        private void CommandBindingCopy_OnCanExecute(object sender, CanExecuteRoutedEventArgs e)
        {
            e.CanExecute = !string.IsNullOrEmpty(_selectedText);
        }

        private void CopySelectedText(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetTextWithoutHistory(_selectedText);
        }

        private bool _menuIsOpen;
        private RadContextMenu? _contextMenu;

        private void ShowMenuClick()
        {
            _menuIsOpen = true;
            _contextMenu = new RadContextMenu();
            _contextMenu.Closed += (_, _) => { _menuIsOpen = false; };

            RadMenuItem itemPastButton = new RadMenuItem();
            itemPastButton.Header = LocalizationManager.GetString("PastButton");
            itemPastButton.PreviewMouseDown += RadMenuItemPaste;
            _contextMenu.Items.Add(itemPastButton);

            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("CopyButton");
            itemCopy.PreviewMouseDown += RadMenuItemCopyClick;
            _contextMenu.Items.Add(itemCopy);
            if (_type == 2 && lvFastAction.SelectedItem != null)
            {
                ClipboardDataModel clipboardDataModel = (ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj;
                if (clipboardDataModel.IsImage)
                {
                    _contextMenu.Placement = PlacementMode.Mouse;
                    _contextMenu.IsOpen = true;
                    return;
                }
                if (clipboardDataModel.IsHtml)
                {
                    RadMenuItem itemCopyHtml = new RadMenuItem();
                    itemCopyHtml.Header = LocalizationManager.GetString("CopyButtonHtml");
                    itemCopyHtml.PreviewMouseDown += RadMenuItemCopyHtmlClick;
                    _contextMenu.Items.Add(itemCopyHtml);
                }

                if (clipboardDataModel.IsRtf)
                {
                    RadMenuItem itemCopyRtf = new RadMenuItem();
                    itemCopyRtf.Header = LocalizationManager.GetString("CopyButtonRtf");
                    itemCopyRtf.PreviewMouseDown += RadMenuItemCopyRtfClick;
                    _contextMenu.Items.Add(itemCopyRtf);
                }
                RadMenuItem itemBreakInter = new RadMenuItem();
                itemBreakInter.Header = LocalizationManager.GetString("BreakInterButton");
                itemBreakInter.PreviewMouseDown += RadMenuItemBreakInterClick;
                _contextMenu.Items.Add(itemBreakInter);

                RadMenuItem itemBreakSpace = new RadMenuItem();
                itemBreakSpace.Header = LocalizationManager.GetString("BreakSpaceButton");
                itemBreakSpace.PreviewMouseDown += RadMenuItemBreakSpaceClick;
                _contextMenu.Items.Add(itemBreakSpace);
            }

            RadMenuItem itemCopyToSnippets = new RadMenuItem();
            itemCopyToSnippets.Header = LocalizationManager.GetString("ToReplacerButton");
            itemCopyToSnippets.PreviewMouseDown += RadMenuItemToSnippets;
            _contextMenu.Items.Add(itemCopyToSnippets);

            _contextMenu.PlacementTarget = _currentButtonMenu;

            _contextMenu.IsOpen = true;
        }

        private void RadMenuItemBreakSpaceClick(object sender, MouseButtonEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj);
            VMContainer.Instance.ClipboardViewModel.BreakSpace();
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
        }

        private void RadMenuItemBreakInterClick(object sender, MouseButtonEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj);
            VMContainer.Instance.ClipboardViewModel.BreakInter();
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
        }

        private void RadMenuItemPaste(object sender, MouseButtonEventArgs e)
        {
            Replace();
        }

        private void RadMenuItemToSnippets(object sender, MouseButtonEventArgs e)
        {
            if (lvFastAction.SelectedItem == null) return;
            var textToReplacer = "";
            if (_type == 1) textToReplacer += ((DiaryDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj).Text;
            if (_type == 2) textToReplacer += ((ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj).Text;
            if (_type == 3) textToReplacer += ((HistoryTranslationModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj).Text;
            CloseWin();
            GlobalEventsApp.OnEventAddNewSnippets(textToReplacer);
        }

        private void RadMenuItemCopyRtfClick(object sender, MouseButtonEventArgs e)
        {
            var textToCopy = (ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj;
            ClipboardMonitorWorker.IgnoreLast = true;
            System.Windows.Forms.DataObject dataObject = new System.Windows.Forms.DataObject();
            dataObject.SetData("UnicodeText", textToCopy.Text);
            dataObject.SetData("Rich Text Format", textToCopy.Rtf);
            System.Windows.Forms.Clipboard.SetDataObject(dataObject);
        }

        private void RadMenuItemCopyHtmlClick(object sender, MouseButtonEventArgs e)
        {
            var textToCopy = (ClipboardDataModel)((ItemsSourceStruct)lvFastAction.SelectedItem).Obj;
            ClipboardMonitorWorker.IgnoreLast = true;
            if (textToCopy.Html != null) System.Windows.Forms.Clipboard.SetText(textToCopy.Html, TextDataFormat.Text);
        }

        private void RadMenuItemCopyClick(object sender, MouseButtonEventArgs e)
        {
            Copy();
        }

        private void DiaryPasswordBoxMu_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            DiaryPasswordVerify();
        }

        private void DiaryPasswordVerify()
        {
            if (PasswordBoxMu.Password == SettingsManager.Settings.DiaryPassword)
            {
                GridPassword.Visibility = Visibility.Collapsed;
                lvFastAction.Visibility = Visibility.Visible;
                TextBoxSearch.Visibility = Visibility.Visible;
                lvFastAction_Bind(VMContainer.Instance.DiaryViewModel.AllDiaryItems);
                VMContainer.Instance.DiaryViewModel.LastPasswordRequest = DateTime.Now;
            }
        }

        private void DiaryButtonPasswordOk_OnClick(object? sender, RoutedEventArgs? e)
        {
            DiaryPasswordVerify();
        }

        private void SetPasswordDiarySelection()
        {
            //passwordBox.GetType().GetMethod("Select", BindingFlags.Instance | BindingFlags.Noninternal).Invoke(passwordBox, new object[] { start, length });
            DiaryPasswordVerify();
        }

        private void TextBoxSearch_OnTextChanged(object sender, TextChangedEventArgs textChangedEventArgs)
        {
            string text = ((RadWatermarkTextBox)sender).Text;
            if (_type == 1)
            {
                lvFastAction_Bind(VMContainer.Instance.DiaryViewModel.AllDiaryItems, text);
            }
            if (_type == 2)
            {
                lvFastAction_Bind(VMContainer.Instance.ClipboardViewModel.AllClipboardItems, text);
            }
            if (_type == 3)
            {
                lvFastAction_Bind(VMContainer.Instance.HistoryViewModel.AllHistoryItems, text);
            }
            ((RadWatermarkTextBox)sender).CaretIndex = text.Length;
        }

    }

}
