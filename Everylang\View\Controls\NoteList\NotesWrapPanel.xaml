﻿<UserControl x:Class="Everylang.App.View.Controls.NoteList.NotesWrapPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">

    <ItemsControl x:Name="ItemsControlNotes" Margin="5" ItemsSource="{Binding NotesListControlViewModel.ItemsView}">
        <ItemsControl.ItemsPanel>
            <ItemsPanelTemplate>
                <WrapPanel/>
            </ItemsPanelTemplate>
        </ItemsControl.ItemsPanel>
    </ItemsControl>


</UserControl>
