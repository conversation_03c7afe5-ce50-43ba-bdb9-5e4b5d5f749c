﻿using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Everylang.Common.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using WindowsInput;

namespace Everylang.App.SwitcherLang
{
    static class SwitcherSelectedText
    {
        internal static List<KeyValuePair<string, IntPtr>>? SwitcherLayouts;
        private static string? _currentKeyboardLayoutName;

        internal static void Start()
        {
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcherSelectedText += KeyCombinationSwitcherSelectedTextPressed;
        }

        public static void Stop()
        {
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcherSelectedText -= KeyCombinationSwitcherSelectedTextPressed;
        }

        private static void KeyCombinationSwitcherSelectedTextPressed(object? sender, EventArgs e)
        {
            string? text = ClipboardOperations.GetSelectionText();

            if (!string.IsNullOrEmpty(text))
            {
                ForegroundWindow.StoreForegroundWindow();
                SwitchAndReplaceSelected(text);
            }
        }

        internal static string SwitchAndReplaceSelected(string? text)
        {
            if (text != null)
            {
                var countText = text.Length;
                _currentKeyboardLayoutName = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(IntPtr.Zero);
                SwitcherLayouts = new List<KeyValuePair<string, IntPtr>>();
                if (KeyboardLayoutCommon.AutoSwitcherLayouts.Count > 0)
                {
                    List<KeyValuePair<string, IntPtr>> listLayouts = KeyboardLayoutCommon.AutoSwitcherLayouts.ToList();
                    int indexLang = 0;
                    bool isCurrentLayoutSet = false;
                    while (true)
                    {
                        if (listLayouts[indexLang].Key == _currentKeyboardLayoutName)
                        {
                            if (listLayouts.Count > indexLang) SwitcherLayouts.Add(listLayouts[indexLang]);
                            isCurrentLayoutSet = true;
                        }
                        else if (isCurrentLayoutSet)
                        {
                            if (listLayouts.Count > indexLang) SwitcherLayouts.Add(listLayouts[indexLang]);
                        }
                        indexLang++;
                        if (indexLang == listLayouts.Count)
                        {
                            indexLang = 0;
                        }
                        if (SwitcherLayouts.Count == listLayouts.Count)
                        {
                            break;
                        }
                    }
                }
                var resultText = "";
                string[] textArrInter = text.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
                var langList = new List<string?>();
                for (int i = 0; i < textArrInter.Length; i++)
                {
                    var textInter = textArrInter[i];
                    var textArrSpace = textInter.Split(' ');
                    for (int y = 0; y < textArrSpace.Length; y++)
                    {
                        var s = textArrSpace[y];
                        var switchedTextArr = SwitchSelectedText(s);
                        if (switchedTextArr.Length > 0) resultText += switchedTextArr[0];
                        if (switchedTextArr.Length > 1) langList.Add(switchedTextArr[1]);
                        if (y != textArrSpace.Length - 1)
                        {
                            resultText += " ";
                        }
                    }
                    if (i != textArrInter.Length - 1)
                    {
                        resultText += Environment.NewLine;
                    }
                }
                SendText.SendStringByPaste(resultText, false);
                var lang = KeyboardLayoutMethods.MaxFrequencyLang(langList);
                if (!string.IsNullOrEmpty(lang) && _currentKeyboardLayoutName != lang)
                {
                    var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[lang]];
                    KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
                }
                if (SettingsManager.Settings.SwitcherLeaveTextSelectedAfterSwitch)
                {
                    Thread.Sleep(100);
                    var sim = new InputSimulator();
                    sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT);
                    for (int i = 0; i < countText; i++)
                    {
                        sim.Keyboard.KeyPress(VirtualKeyCode.LEFT);
                    }
                    sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT);
                }
                return resultText;
            }

            return "";
        }



        private static string?[] SwitchSelectedText(string text)
        {
            var detectedLang = KeyboardLayoutMethods.DetectLang(text);
            var listKeys = new List<Keys>();
            foreach (char c in text)
            {
                if (detectedLang == "he" && c == '\'')
                {
                    listKeys.Add(KeyboardLayoutMethods.ConvertCharToVirtualKey('w'));
                }
                else
                {
                    listKeys.Add(KeyboardLayoutMethods.ConvertCharToVirtualKey(c, detectedLang));
                }
            }
            string resultText = "";
            var langList = new List<string?>();
            string firstLang = "";
            for (int i = 0; i < listKeys.Count; i++)
            {
                Keys key = listKeys[i];
                if (key == Keys.None)
                {
                    resultText += text[i];
                }
                else
                {
                    bool isConverted = false;
                    if (KeyboardLayoutCommon.AutoSwitcherLayouts != null)
                    {
                        if (SwitcherLayouts != null)
                        {
                            foreach (var switcherLayout in SwitcherLayouts)
                            {
                                string? c = KeyboardLayoutMethods.GetCharsFromKeys(key, false, switcherLayout.Value);
                                if (c != null &&
                                    ((!text.Contains(c) && text[i].ToString() != c) ||
                                                  (firstLang != "" && firstLang == switcherLayout.Key) ||
                                                  (detectedLang != "" && detectedLang != switcherLayout.Key)))
                                {
                                    if (firstLang == "")
                                    {
                                        firstLang = switcherLayout.Key;
                                    }

                                    langList.Add(switcherLayout.Key);
                                    resultText += c;
                                    if (resultText.EndsWith("https:.."))
                                    {
                                        resultText = resultText.Replace("https:..", "https://");
                                    }

                                    if (resultText.EndsWith("http:.."))
                                    {
                                        resultText = resultText.Replace("http:..", "http://");
                                    }

                                    isConverted = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!isConverted)
                    {
                        resultText += text[i];
                    }
                }
            }
            return new[] { resultText, KeyboardLayoutMethods.MaxFrequencyLang(langList) };
        }


    }
}
