﻿using LiteDB;
using System;

namespace Everylang.App.Data.DataModel
{
    public class HistoryTranslationModel
    {
        internal ObjectId? Id { get; set; }
        internal string? Text { get; set; }
        public string TextPrev => (Text?.Length > 1000 ? Text.Substring(0, 1000) + "......" : Text) ?? string.Empty;
        public string? ShortText { get; set; }
        internal string? Application { get; set; }
        internal string? DateText { get; set; }
        internal DateTime DateTime { get; set; }
    }
}
