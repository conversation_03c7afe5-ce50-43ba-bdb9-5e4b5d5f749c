﻿using LiteDB;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Everylang.Note.NoteDataStore
{
    public class CheckListDataModel : INotifyPropertyChanged
    {
        private bool _isSelected;
        private bool _isDop;
        private string? _text;

        public bool IsSelectedItem
        {
            get { return _isSelected; }
            set
            {
                if (_isSelected == value) return;
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public bool IsDopItem
        {
            get { return _isDop; }
            set
            {
                if (_isDop == value) return;
                _isDop = value;
                OnPropertyChanged();
            }
        }

        public string? Text
        {
            get { return _text; }
            set
            {
                if (_text == value) return;
                _text = value;
                OnPropertyChanged();
            }
        }

        public ObjectId? IdNote { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
