﻿using System;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;
using RECT = Vanara.PInvoke.RECT;

namespace Everylang.App.Utilities
{
    class WindowLocation
    {
        internal static System.Drawing.Point GetReallyCenterToScreen()
        {
            var screen = WpfScreenHelper.Screen.FromHandle(User32.GetForegroundWindow().DangerousGetHandle());
            var workingArea = screen.WorkingArea;
            return new System.Drawing.Point()
            {
                X = (int)(Math.Max(workingArea.X, workingArea.X + (workingArea.Width) / 2) * screen.ScaleFactor),
                Y = (int)(Math.Max(workingArea.Y, workingArea.Y + (workingArea.Height) / 2) * screen.ScaleFactor)
            };
        }

        static Int32Rect GetOsInteropRect(Window w)
        {
            bool multimonSupported = User32.GetSystemMetrics(User32.SystemMetric.SM_CMONITORS) != 0;
            if (!multimonSupported)
            {
                User32.SystemParametersInfo(User32.SPI.SPI_GETWORKAREA, out RECT rc);
                return new Int32Rect(rc.Left, rc.Top, rc.Width, rc.Height);
            }

            var helper = new WindowInteropHelper(w);
            var hmonitor =
                User32.MonitorFromWindow(helper.EnsureHandle(), User32.MonitorFlags.MONITOR_DEFAULTTONEAREST);
            var info = new User32.MONITORINFOEX();
            User32.GetMonitorInfo(hmonitor, ref info);
            return new Int32Rect(info.rcWork.Left, info.rcWork.Top, info.rcWork.Width, info.rcWork.Height);
        }

        internal static System.Drawing.Point GetAbsolutePosition(Window w)
        {
            if (w.WindowState != WindowState.Maximized)
                return new System.Drawing.Point((int)w.Left, (int)w.Top);

            var r = GetOsInteropRect(w);
            return new System.Drawing.Point(r.X, r.Y);
        }
    }
}

