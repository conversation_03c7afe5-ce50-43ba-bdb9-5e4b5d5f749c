﻿namespace Everylang.Note.SettingsApp
{
    public class AppSettings
    {
        private string _fontDefault = "Segoe UI";
        private double _sizeFontDefault = 13;
        private int _transparencyForNotes = 70;
        private string _addNewShortcut = "shortcutCtrl+Alt+Add";
        private bool _addNewShortcutIsEnabled = true;
        private bool _isOnNotes;

        public string? FontDefault
        {
            get => _fontDefault;
            set
            {
                if (value != null) _fontDefault = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }

        public double SizeFontDefault
        {
            get => _sizeFontDefault;
            set
            {
                _sizeFontDefault = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }

        public int TransparencyForNotes
        {
            get => _transparencyForNotes;
            set
            {
                _transparencyForNotes = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }

        public string? AddNewShortcut
        {
            get => _addNewShortcut;
            set
            {
                if (value != null) _addNewShortcut = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }

        public bool AddNewShortcutIsEnabled
        {
            get => _addNewShortcutIsEnabled;
            set
            {
                _addNewShortcutIsEnabled = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }

        public bool IsOnNotes
        {
            get => _isOnNotes;
            set
            {
                _isOnNotes = value;
                SettingsMiminoteManager.SaveSettings();
            }
        }
    }
}
