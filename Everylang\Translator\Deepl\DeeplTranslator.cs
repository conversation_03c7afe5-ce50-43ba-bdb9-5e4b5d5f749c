﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;

namespace Everylang.App.Translator.Deepl
{
    class DeepLTranslator
    {
        private List<TextSlice>? _slices;
        private RequestSettings? _requestSettings = null!;

        internal WebResultTranslator? Translate(RequestSettings? requestSettings)
        {
            _requestSettings = requestSettings;
            var languageFromCurrent = requestSettings.LanguageFromCurrent.Abbreviation;
            var languageToCurrent = requestSettings.LanguageToCurrent.Abbreviation;
            var result = Translate(requestSettings.SourceTextTrimmed, languageFromCurrent, languageToCurrent);
            return result;
        }

        internal WebResultTranslator? Translate(string sourceText, string? languageFromCurrent, string? languageToCurrent, bool second = false)
        {
            try
            {
                if (languageToCurrent == "auto")
                {
                    languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                }
                if (languageFromCurrent == languageToCurrent)
                {
                    languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                }
                if (languageFromCurrent == languageToCurrent)
                {
                    languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                }
                if (languageFromCurrent != "auto")
                {
                    languageFromCurrent = languageFromCurrent.ToUpper();
                }
                languageToCurrent = languageToCurrent.ToUpper();
                var strRequest = GetRequest(sourceText, languageFromCurrent, languageToCurrent);
                var netLib = new NetLibTranslator("https://www2.deepl.com/jsonrpc", strRequest, "https://www.deepl.com/translator");
                var webResult = netLib.StartPostWebRequestDeepL();
                if (!webResult.WithError)
                {
                    if (webResult.ResultText != null)
                    {
                        var response = ProcessingOfTheRequest(webResult.ResultText);
                        if (response != null)
                        {
                            webResult.FromLang = response[0].ToLower();
                            webResult.ToLang = response[1].ToLower();
                            response.RemoveAt(0);
                            response.RemoveAt(0);
                            if (webResult.FromLang == webResult.ToLang && !second)
                            {
                                if (webResult.FromLang != _requestSettings.LanguageToCurrent.Abbreviation)
                                {
                                    return Translate(sourceText, webResult.FromLang,
                                        _requestSettings.LanguageFromCurrent.Abbreviation, true);
                                }
                                else
                                {
                                    return Translate(sourceText, webResult.FromLang,
                                        _requestSettings.LanguageToCurrent.Abbreviation, true);
                                }
                            }

                            if (_requestSettings.IsOneWord)
                            {
                                for (var i = 0; i < response.Count; i++)
                                {
                                    if (i == 0)
                                    {
                                        webResult.ResultText = response[i];
                                        webResult.ResultTextWithNonChar = _requestSettings.StartNonCharList + response[i] + _requestSettings.EndNonCharList;
                                    }
                                    else
                                    {
                                        webResult.ResultText += Environment.NewLine + response[i];
                                    }
                                }
                            }
                            else
                            {
                                webResult.ResultText = response.FirstOrDefault();
                                webResult.ResultText = _requestSettings.StartNonCharList + webResult.ResultText + _requestSettings.EndNonCharList;
                                webResult.ResultTextWithNonChar = webResult.ResultText;
                            }
                        }
                        if (webResult.ResultText != null && webResult.ResultText.Contains(",\"error\":"))
                        {
                            var error = JsonConvert.DeserializeObject<DeeplRoot>(webResult.ResultText);
                            if (error != null)
                            {
                                return new WebResultTranslator() { WithError = true, ErrorText = error.error.message };
                            }
                            return new WebResultTranslator() { WithError = true, ErrorText = "" };
                        }
                    }
                }
                return webResult;
            }
            catch (Exception e)
            {
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }
        }

        private string GetRequest(string sourceText, string? languageFromCurrent, string? languageToCurrent)
        {
            //Regex newlineSplitter = new Regex(@"^\s+|(?:\s*\n)+\s*|[.!?\"":;।](?:\s+)|\s+$");
            Regex newlineSplitter = new Regex(@"/[.!?;"":？。．！\u037e].*\S.*$/m");
            sourceText = Regex.Replace(sourceText, "(\r\n|\r)", "\n");
            _slices = new List<TextSlice>();
            var matches = newlineSplitter.Matches(sourceText);
            int strIndex = 0;
            foreach (Match match in matches)
            {
                TextSlice slice = new TextSlice
                {
                    Delimiter = match.Value,
                    Text = sourceText.Substring(strIndex, match.Index - strIndex)
                };
                strIndex = match.Index + match.Length;
                _slices.Add(slice);
            }
            if (strIndex < sourceText.Length)
            {
                TextSlice slice = new TextSlice
                {
                    Delimiter = "",
                    Text = sourceText.Substring(strIndex, sourceText.Length - strIndex)
                };
                _slices.Add(slice);
            }
            RootRequest rootRequest = new RootRequest
            {
                id = 2,
                jsonrpc = "2.0",
                method = "LMT_handle_jobs",
                @params = new Params()
                {
                    jobs = _slices.Select(x => new Job
                    {
                        raw_en_sentence = x.Text,
                        kind = "default"
                    }).ToList(),
                    priority = 1,
                    lang = new Lang()
                    {
                        //user_preferred_langs = new List<string>() { languageFromCurrent == "auto" ? "EN" : languageFromCurrent, languageToCurrent },
                        target_lang = languageToCurrent,
                        source_lang_user_selected = languageFromCurrent
                    }
                }
            };

            int c = 1;
            foreach (var <NAME_EMAIL>)
            {
                if (paramsJob.raw_en_sentence != null) c += Regex.Matches(paramsJob.raw_en_sentence, "[i]").Count;
            }
            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            timestamp += (c - timestamp % c);
            <EMAIL> = timestamp;
            var strRequest = JsonConvert.SerializeObject(rootRequest, Formatting.Indented);
            return strRequest;
        }

        private List<string>? ProcessingOfTheRequest(string? webResultResultText)
        {
            if (webResultResultText != null)
            {
                var deepLTranslationResponse = JsonConvert.DeserializeObject<RootResponse>(webResultResultText);

                if (deepLTranslationResponse?.result.translations != null)
                {
                    List<string> temporaryResult = new List<string>
                    {
                        deepLTranslationResponse.result.source_lang,
                        deepLTranslationResponse.result.target_lang
                    };
                    List<Translation> translations = deepLTranslationResponse.result.translations;

                    for (var i = 0; i < translations.Count; i++)
                    {
                        var transaltion = translations[i];
                        foreach (var beam in transaltion.beams)
                        {
                            if (beam.postprocessed_sentence != null)
                            {
                                if (_slices?.Count > i) temporaryResult.Add(HttpUtility.HtmlDecode(beam.postprocessed_sentence + _slices?[i].Delimiter));
                                else temporaryResult.Add(HttpUtility.HtmlDecode(beam.postprocessed_sentence));
                            }
                        }
                    }

                    return temporaryResult;
                }
            }

            return null;
        }

        class TextSlice
        {
            internal string? Text { get; set; }
            internal string? Delimiter { get; set; }
        }

    }
}
