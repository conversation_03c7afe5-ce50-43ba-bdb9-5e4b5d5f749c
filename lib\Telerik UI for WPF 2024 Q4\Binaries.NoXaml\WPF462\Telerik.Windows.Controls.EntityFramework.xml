<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.EntityFramework</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Controls.RadEntityFrameworkDataSource">
            <summary>
            Provides an object for loading, paging, filtering, sorting, and editing entities coming from 
            Entity Framework's ObjectContext.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.DataView">
            <summary>
            Gets the current view of entities resulting from the last load operation, 
            using a <see cref="T:Telerik.Windows.Data.DataItemCollection"/>.
            </summary>
            <value>The current view of entities resulting from the last load operation.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadEntityFrameworkDataSource.ObjectContextProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.ObjectContext"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.ObjectContext">
            <summary>
            Gets or sets the Entity Framework ObjectContext instance used for executing the load and submit operations.
            </summary>
            <value>The Entity Framework ObjectContext.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadEntityFrameworkDataSource.DbContextProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.DbContext"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.DbContext">
            <summary>
            Gets or sets the <see cref="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.DbContext"/> instance used for executing the load and submit operations. 
            </summary>
            <value>The context.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadEntityFrameworkDataSource.QueryNameProperty">
            <summary>
            Identifies <see cref="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.QueryName"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.QueryName">
            <summary>
            Gets or sets the name of the query to use for loading.
            </summary>
            <value>The name of the query to use for loading data.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.RelatedObjects">
            <summary>
            The names of the related entities.
            </summary>
            <remarks>
            For example, if your main entity set is called "Orders", you might want to add "OrderDetails"
            to this collection in case you want to get the related OrderDetails for each Order from the database.
            </remarks>
            <value>The names of the related entities.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.FilterDescriptors">
            <summary>
            Gets the filter descriptors used for filtering operations.
            </summary>
            <value>The filter descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.SortDescriptors">
            <summary>
            Gets the sort descriptors used for sorting operations.
            </summary>
            <value>The sort descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.GroupDescriptors">
            <summary>
            Gets the group descriptors used for grouping operations.
            </summary>
            <value>The group descriptors.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.LoadCommand">
            <summary>
            Gets the command for loading data.
            </summary>
            <value>The command for loading data.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadEntityFrameworkDataSource.SubmitChangesCommand">
            <summary>
            Gets the command for submitting changes.
            </summary>
            <value>The command for submitting changes.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadEntityFrameworkDataSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadEntityFrameworkDataSource" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadEntityFrameworkDataSource.Load">
            <summary>
            Initiates a Load operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadEntityFrameworkDataSource.SubmitChanges">
            <summary>
            Initiates a Submit operation if possible.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadEntityFrameworkDataSource.CreateView">
            <summary>
            Creates the view.
            </summary>
            <returns>The view.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadEntityFrameworkDataSource.IsValidQueryProperty(System.Reflection.PropertyInfo)">
            <summary>
            Determines whether the property is a valid query property.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <returns>
            	<c>true</c> if the property is a valid query property; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Data.QueryableEntityCollectionView`1">
            <summary>
            Represent a <see cref="T:System.Linq.Queryable"/> view over an entity set.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.#ctor(System.Data.Objects.ObjectContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableEntityCollectionView`1"/> class.
            </summary>
            <param name="objectContext">The object context.</param>
            <param name="entitySetName">Name of the entity set.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.ConstructNewItem">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.#ctor(System.Data.Objects.ObjectContext,System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableEntityCollectionView`1"/> class.
            </summary>
            <param name="objectContext">The object context.</param>
            <param name="entitySetName">Name of the entity set.</param>
            <param name="relatedObjectsToInclude">The names of the related objects to include.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.#ctor(System.Data.Objects.ObjectQuery{`0},System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Data.QueryableEntityCollectionView`1"/> class.
            </summary>
            <param name="objectQuery">The object query.</param>
            <param name="entitySetName">Name of the entity set.</param>
            <param name="relatedObjectsToInclude">The related objects to include.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.CreateView">
            <summary>
            Returns <see cref="T:System.Linq.IQueryable" /> with applied filtering, sorting, grouping and paging.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the PropertyChanged event.
            </summary>
            <param name="e">The PropertyChangedEventArgs instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Data.QueryableEntityCollectionView`1.RefreshOnItemAction(System.Object,Telerik.Windows.Data.ItemAction)">
            <inheritdoc />
        </member>
    </members>
</doc>
