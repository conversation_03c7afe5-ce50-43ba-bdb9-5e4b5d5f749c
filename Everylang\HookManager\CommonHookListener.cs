﻿using Everylang.App.HookManager.GlobalHooks;
using System;
using System.Threading;
using System.Timers;
using System.Windows.Threading;
using Timer = System.Timers.Timer;

namespace Everylang.App.HookManager
{
    internal static class CommonHookListener
    {
        static GlobalKeyHook? _globalKeyHook;
        static GlobalMouseHook? _globalMouseHook;
        private static Timer? _timer;
        internal static bool IsEnabled { get; set; }
        internal static bool IsLShiftPressed { get; set; }
        internal static bool IsRShiftPressed { get; set; }
        internal static bool IsKeyEnabled { get; set; }

        private static void LoadTimer()
        {
            _timer = new Timer(TimeSpan.FromMinutes(30).TotalMilliseconds);
            _timer.Elapsed += TimerOnElapsed;
            _timer.Start();
        }

        private static void TimerOnElapsed(object? sender, ElapsedEventArgs elapsedEventArgs)
        {
            System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                (ThreadStart)delegate
                {
                    UnSubscribe();
                    Subscribe();
                });
        }

        private static void UnSubscribe()
        {
            try
            {
                if (_globalKeyHook != null)
                {
                    _globalKeyHook.OnKeyDown -= KeyDownHandler;
                    _globalKeyHook.OnKeyUp -= KeyUpHandler;
                    _globalMouseHook = null;
                }

                if (_globalMouseHook != null)
                {
                    _globalMouseHook.OnMouseMove -= MouseMoveHandler;
                    _globalMouseHook.OnMouseWheelScroll -= MouseWheelHandler;
                    _globalMouseHook.OnButtonDown -= MouseDownHandler;
                    _globalMouseHook.OnButtonUp -= MouseUpHandler;

                    _globalKeyHook = null;
                }

                GlobalHook.Dispose();
            }
            catch
            {
                // ignored
            }
        }

        private static void Subscribe()
        {
            _globalKeyHook = new GlobalKeyHook();
            _globalMouseHook = new GlobalMouseHook();
            _globalKeyHook.OnKeyDown += KeyDownHandler;
            _globalKeyHook.OnKeyUp += KeyUpHandler;
            _globalMouseHook.OnMouseMove += MouseMoveHandler;
            _globalMouseHook.OnMouseWheelScroll += MouseWheelHandler;
            _globalMouseHook.OnButtonDown += MouseDownHandler;
            _globalMouseHook.OnButtonUp += MouseUpHandler;
        }

        internal static void Start()
        {
            if (!IsEnabled)
            {
                GlobalLangChangeHook.Start();
                IsEnabled = true;
                IsKeyEnabled = true;
                Subscribe();
                LoadTimer();
            }
        }

        internal static void Stop()
        {
            if (IsEnabled)
            {
                _timer?.Stop();
                GlobalLangChangeHook.Stop();
                IsEnabled = false;
                IsKeyEnabled = true;
                UnSubscribe();
            }
        }

        private static void KeyUpHandler(object? sender, GlobalKeyEventArgs e)
        {
            if (e.KeyCode == VirtualKeycodes.RightShift)
            {
                IsRShiftPressed = false;
            }
            if (e.KeyCode == VirtualKeycodes.LeftShift)
            {
                IsLShiftPressed = false;
            }
            if (IsEnabled && IsKeyEnabled)
            {
                if (e.KeyCode == VirtualKeycodes.LeftCtrl && e.HardwareScanCode == 541)
                {
                    return;
                }
                HookCallBackKeyUp.Event(e);
            }
        }

        private static void KeyDownHandler(object? sender, GlobalKeyEventArgs e)
        {
            if (e.KeyCode == VirtualKeycodes.RightShift)
            {
                IsRShiftPressed = true;
            }
            if (e.KeyCode == VirtualKeycodes.LeftShift)
            {
                IsLShiftPressed = true;
            }
            if (IsEnabled && IsKeyEnabled)
            {
                if (e.KeyCode == VirtualKeycodes.LeftCtrl && e.HardwareScanCode == 541)
                {
                    return;
                }
                HookCallBackKeyDown.Event(e);
            }
        }

        private static void MouseDownHandler(object? sender, GlobalMouseEventArgs e)
        {
            HookCallBackMouseDown.Event(e);
        }

        private static void MouseUpHandler(object? sender, GlobalMouseEventArgs e)
        {
            HookCallBackMouseUp.Event(e);
        }

        private static void MouseWheelHandler(object? sender, GlobalMouseEventArgs e)
        {
            HookCallBackMouseWheel.Event(e);
        }

        private static void MouseMoveHandler(object? sender, GlobalMouseEventArgs e)
        {
            HookCallBackMouseMove.Event(e);
        }
    }

    static class HookCallBackKeyUp
    {
        internal static event Action<GlobalKeyEventArgs>? CallbackEventHandler;

        internal static void Event(GlobalKeyEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }

    static class HookCallBackKeyDown
    {
        internal static event Action<GlobalKeyEventArgs>? CallbackEventHandler;
        internal static void Event(GlobalKeyEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }

    static class HookCallBackMouseDown
    {
        internal static event Action<GlobalMouseEventArgs>? CallbackEventHandler;
        internal static void Event(GlobalMouseEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }

    static class HookCallBackMouseWheel
    {
        internal static event Action<GlobalMouseEventArgs>? CallbackEventHandler;
        internal static void Event(GlobalMouseEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }
    static class HookCallBackMouseMove
    {
        internal static event Action<GlobalMouseEventArgs>? CallbackEventHandler;
        internal static void Event(GlobalMouseEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }

    static class HookCallBackMouseUp
    {
        internal static event Action<GlobalMouseEventArgs>? CallbackEventHandler;
        internal static void Event(GlobalMouseEventArgs e)
        {
            CallbackEventHandler?.Invoke(e);
        }
    }
}
