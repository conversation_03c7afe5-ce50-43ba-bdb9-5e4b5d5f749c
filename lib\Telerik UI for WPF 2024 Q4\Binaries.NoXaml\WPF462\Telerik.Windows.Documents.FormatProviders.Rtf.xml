<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.FormatProviders.Rtf</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException">
            <summary>
            Represents errors that occur during import of RTF content due to invalid number of braces.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException.#ctor">
            <summary>Creates a new instance  of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException"/> class.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException.#ctor(System.String)">
            <summary>Creates a new instance  of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException"/> class with a specified message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance  of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfBraceNestingException"/> class, based on the specified cause.</summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException">
            <summary>
            Represents errors that occur during import of RTF content, caused by incorrect color definition.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException.#ctor">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException"/> class.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException"/> class with the given message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorException"/> class with the given message, based on the given cause.</summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException">
            <summary>
            Represents errors that occur during import of RTF content, caused by incorrect definition of the color table.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfColorTableFormatException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException">
            <summary>
            Represents errors that occur import of RTF content, caused by empty content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException.#ctor">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException"/> class.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException"/> class with the given message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEmptyDocumentException"/> class with the given message, based on the given cause.</summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException">
            <summary>
            Represents errors that occur import of RTF content, caused by incorrect encoding of the content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException"/> class with the given message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException">
            <summary>
            Represents errors that occur during import of RTF content.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException"/> class.</summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException"/> class with the given message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException"/> class with the given message, based on the given cause.</summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException">
            <summary>
            Represents errors that occur during import of RTF content, caused by incorrect definition of the font table.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfFontTableFormatException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException">
            <summary>
            Represents errors that occur import of RTF content, caused by incorrect encoding of the content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfHexEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException">
            <summary>
            Represents errors that occur import of RTF content, caused by a missing character.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMissingCharacterException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException">
            <summary>
            Represents errors that occur import of RTF content, caused by incorrect encoding of the content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfMultiByteEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException">
            <summary>
            Represents errors that occur during import of RTF content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException"/> class with the specified message.</summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException"/> class with the specified message, based on the given cause.</summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException">
            <summary>
            Represents errors that occur import of RTF content, caused by incorrect structure of the content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfParserException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfStructureException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException">
            <summary>
            Represents errors that occur import of RTF content, caused by an element at an unexpected location.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfException" />
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException.Expected">
            <summary>
            Gets the element that has been expected.
            </summary>
            <value>
            The expected.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException.Actual">
            <summary>
            Gets the actual element.
            </summary>
            <value>
            The actual.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException"/> class.
            </summary>
            <param name="expected">The expected element.</param>
            <param name="actual">The actual element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnexpectedElementException"/> class.
            </summary>
            <param name="expected">The expected element.</param>
            <param name="actual">The actual element.</param>
            <param name="message">The message.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException">
            <summary>
            Represents errors that occur import of RTF content, caused by incorrect encoding of the content.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfEncodingException" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Exceptions.RtfUnicodeEncodingException"/> class.
            </summary>
            <param name="message">The message to display.</param>
            <param name="cause">The original cause for this exception.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop">
            <summary>
            Represents a tab stop in the RTF document model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop.Position">
            <summary>
            Gets or sets the position of the tab stop.
            </summary>
            <value>
            The position.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop.Type">
            <summary>
            Gets or sets the type of the tab stop.
            </summary>
            <value>
            The type.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop.Reset">
            <summary>
            Resets this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.Import.RtfTabStop.GetTabStop">
            <summary>
            Gets an instance.
            </summary>
            <returns>The <see cref="T:Telerik.Windows.Documents.Model.TabStop"/> instance.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider">
            <summary>
            Represents a wrapper of <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider"/> allowing the latter to be used in data binding scenarios.
            </summary>
            <seealso cref="T:Telerik.Windows.Documents.FormatProviders.DataProviderBase" />
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider"/> class.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.RtfProperty">
            <summary>
            The RTF property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.Rtf">
            <summary>
            Gets or sets the current document as RTF.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.Bind(Telerik.Windows.Controls.RadRichTextBox)">
            <summary>
            Binds the specified box.
            </summary>
            <param name="box">The box.</param>
        </member>
        <member name="F:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.SourceProperty">
            <summary>
            The source property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.SetSource(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the source.
            </summary>
            <param name="dependencyObject">The dependency object.</param>
            <param name="sourceValue">The source value.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.GetSource(System.Windows.DependencyObject)">
            <summary>
            Gets the source.
            </summary>
            <param name="dependencyObject">The dependency object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.SetAttachedDataProvider(System.Windows.DependencyObject,Telerik.Windows.Documents.FormatProviders.DataProviderBase)">
            <summary>
            Sets the attached data provider.
            </summary>
            <param name="dependencyObject">The dependency object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfDataProvider.GetAttachedDataProvider(System.Windows.DependencyObject)">
            <summary>
            Gets the attached data provider.
            </summary>
            <param name="dependencyObject">The dependency object.</param>
            <returns>The provider.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider">
            <summary>
            Represents a format provider that can import and export RTF documents from/to <see cref="T:Telerik.Windows.Documents.Model.RadDocument"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.Name">
            <summary>
            Gets the name of the specific format provider.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.FilesDescription">
            <summary>
            Gets the description of the supported file formats.
            </summary>
            <value>
            The files description.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.SupportedExtensions">
            <summary>
            Gets the extensions supported by this format provider.
            </summary>
            <value>
            The supported extensions.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.CanExport">
            <summary>
            Gets a value indicating whether this instance can export.
            </summary>
            <value>
            <c>true</c> if this instance can export; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.CanImport">
            <summary>
            Gets a value indicating whether this instance can import.
            </summary>
            <value>
            <c>true</c> if this instance can import; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.ExportSettings">
            <summary>
            Gets or sets the settings used while exporting content.
            </summary>
            <value>
            The export settings.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.ImportSettings">
            <summary>
            Gets or sets the settings used while importing content.
            </summary>
            <value>
            The import settings.
            </value>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.Export(Telerik.Windows.Documents.Model.RadDocument)">
            <summary>
            Exports the specified document to a <see cref="T:System.String" />.
            </summary>
            <param name="document">The document.</param>
            <returns>
            A <see cref="T:System.String" /> containing the document.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.Export(Telerik.Windows.Documents.Model.RadDocument,System.IO.Stream)">
            <summary>
            Exports the specified <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The <see cref="T:System.IO.Stream" /> the document should be saved into.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.Import(System.IO.Stream)">
            <summary>
            Imports the specified <see cref="T:System.IO.Stream" /> into a <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </summary>
            <param name="input">The <see cref="T:System.IO.Stream" /> containing the RTF document data.</param>
            <returns>
            The generated <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.Rtf.RtfFormatProvider.Import(System.String)">
            <summary>
            Imports the specified <see cref="T:System.String" />.
            </summary>
            <param name="input">The string containing the RTF document.</param>
            <returns>
            The generated <see cref="T:Telerik.Windows.Documents.Model.RadDocument" /> instance.
            </returns>
        </member>
    </members>
</doc>
