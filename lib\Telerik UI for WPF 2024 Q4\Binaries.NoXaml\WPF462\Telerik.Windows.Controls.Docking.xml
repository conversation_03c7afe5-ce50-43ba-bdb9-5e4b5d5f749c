<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.Docking</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer">
            <summary>
            Automation Peer for the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer.#ctor(Telerik.Windows.Controls.Docking.AutoHideArea)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer"/> class.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> that is associated with this <see cref="T:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer.GetAutomationIdCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer">
            <summary>
            Automation Peer for the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.#ctor(Telerik.Windows.Controls.DockingNavigator)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.CanSelectMultiple">
            <summary>Gets a value that specifies whether the UI Automation provider allows
            more than one child element to be selected concurrently.</summary>
            <returns>true if multiple selection is allowed; otherwise false.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.IsSelectionRequired">
            <summary>
            ISelectionProvider implementation.
            Gets a value that specifies whether the UI Automation provider requires at least
            one child element to be selected.
            </summary>
            <returns>true if selection is required; otherwise false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>Gets the control pattern for the <see cref="T:System.Windows.UIElement"/>
            that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>.
            </summary>
            <returns>An object that implements the <see cref="T:System.Windows.Automation.Provider.ISynchronizedInputProvider"/>
            interface if <paramref name="patternInterface"/> is <see cref="F:System.Windows.Automation.Peers.PatternInterface.SynchronizedInput"/>;
            otherwise, null.</returns>
            <param name="patternInterface">A value from the enumeration.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetSelection">
            <summary>
            ISelectionProvider implementation.
            Retrieves a UI Automation provider for each child element that is selected.
            </summary>
            <returns>
            An array of UI Automation providers. 
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetAutomationIdCore">
            <summary>
             Gets the string that uniquely identifies the Telerik.Windows.Controls.RadDocking
             that is associated with this Telerik.Windows.Controls.RadDockingAutomationPeer.
            </summary>
            <returns>A string that contains the UI Automation identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.DockingNavigatorAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.ToolWindowAutomationPeer">
            <summary>
            Automation Peer for the <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ToolWindowAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ToolWindowAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.ToolWindowAutomationPeer.#ctor(Telerik.Windows.Controls.Docking.ToolWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.ToolWindowAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> instance.</param>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer">
            <summary>
             Automation Peer for the Telerik.Windows.Controls.RadSplitContainer class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.#ctor(Telerik.Windows.Controls.RadSplitContainer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer"/> class.
            </summary>
            <param name="owner"></param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.DockPosition">
            <summary>
            Gets a value that indicates what is the DockPosition of the RadSplitContainer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the pattern.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.SetDockPosition(System.Windows.Automation.DockPosition)">
            <summary>
            Sets the dock position of the RadSplitContainer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetNameCore">
            <summary>
            Gets the core name for this Telerik.Windows.Controls.RadSplitContainerAutomationPeer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Gets the automation control type for this Telerik.Windows.Controls.RadSplitContainerAutomationPeer.
            </summary>
            <returns>Custom automation control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSplitContainerAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer">
            <summary>
            Automation Peer for the Telerik.Windows.Controls.Docking.PaneHeader class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.#ctor(Telerik.Windows.Controls.Docking.PaneHeader)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer"/> class.
            </summary>
            <param name="owner"></param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetNameCore">
            <summary>
            Gets the core name for this Telerik.Windows.Controls.PaneHeaderAutomationPeer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Gets the automation control type for this Telerik.Windows.Controls.PaneHeaderAutomationPeer.
            </summary>
            <returns>Custom automation control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneHeaderAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer">
            <summary>
            Automation Peer for the Telerik.Windows.Controls.RadDocking class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.#ctor(Telerik.Windows.Controls.RadDocking)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer" /> class.
            </summary>
            <param name="owner">The RadDocking element that is associated with this RadDockingAutomationPeer.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.CanSelectMultiple">
            <summary>
            ISelectionProvider implementation.
            Gets a value that specifies whether the UI Automation provider allows more than
            one child element to be selected concurrently.
            </summary>
            <returns>true if multiple selection is allowed; otherwise false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.IsSelectionRequired">
            <summary>
            ISelectionProvider implementation.
            Gets a value that specifies whether the UI Automation provider requires at least
            one child element to be selected.
            </summary>
            <returns>true if selection is required; otherwise false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the pattern.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetSelection">
            <summary>
            ISelectionProvider implementation.
            Retrieves a UI Automation provider for each child element that is selected.
            </summary>
            <returns>
            An array of UI Automation providers. 
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetAutomationIdCore">
            <summary>
             Gets the string that uniquely identifies the Telerik.Windows.Controls.RadDocking
             that is associated with this Telerik.Windows.Controls.RadDockingAutomationPeer.
            </summary>
            <returns>A string that contains the UI Automation identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetNameCore">
            <summary>
            Gets the core name for this Telerik.Windows.Controls.RadDocking.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Gets the automation control type for this Telerik.Windows.Controls.RadDockingAutomationPeer.
            </summary>
            <returns>Custom automation control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadDockingAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetChildren"/>.
            </summary>
            <returns>
            A list of child <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> elements.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer">
            <summary>
            Automation Peer for the <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.#ctor(Telerik.Windows.Controls.RadPaneGroup)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer"/> class.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> that is associated with this <see cref="T:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.RadPaneGroupOwner">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> that is associated with this <see cref="T:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer.GetLocalizedControlTypeCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer">
            <summary>
            Automation Peer for the Telerik.Windows.Controls.Docking.PaneGroupBase class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.#ctor(Telerik.Windows.Controls.Docking.PaneGroupBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer"/> class.
            </summary>
            <param name="owner">The PaneGroupBase element that is associated with this PaneGroupBaseAutomationPeer.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetAutomationIdCore">
            <summary>
             Gets the string that uniquely identifies the Telerik.Windows.Controls.Docking.PaneGroupBase 
             that is associated with this Telerik.Windows.Controls.PaneGroupBaseAutomationPeer.
            </summary>
            <returns>A string that contains the UI Automation identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.CreateItemAutomationPeer(System.Object)">
            <summary>
            When overridden in a derived class, creates a new instance of the Telerik.Windows.Controls.PaneGroupBaseAutomationPeer
            for a data item in the System.Windows.Controls.ItemsControl.Items collection of this System.Windows.Controls.ItemsControl.
            </summary>
            <param name="item"></param>
            <returns>The data item that is associated with this Telerik.Windows.Controls.RadPaneAutomationPeer.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetLocalizedControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType().
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer.GetNameCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer">
            <summary>
            Automation Peer for the Telerik.Windows.Controls.RadPane class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetClassNameCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetHelpTextCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetItemStatusCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.#ctor(System.Object,Telerik.Windows.Automation.Peers.PaneGroupBaseAutomationPeer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer" /> class.
            </summary>
            <param name="item"></param>
            <param name="groupAutomationPeer"></param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetAutomationIdCore">
            <summary>
             Gets the string that uniquely identifies the Telerik.Windows.Controls.RadPane 
             that corresponds to the data item in the Telerik.Windows.Controls.RadPaneGroup collection 
             that is associated with this Telerik.Windows.Controls.RadPaneAutomationPeer.
            </summary>
            <returns>A string that contains the UI Automation identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetChildrenCore">
            <summary>
             Gets collection of child elements of the Telerik.Windows.Controls.RadPane 
             that corresponds to the data item in the Telerik.Windows.Controls.RadPaneGroup collection 
             that is associated with this Telerik.Windows.Controls.RadPaneAutomationPeer.
            </summary>
            <returns>The collection of child elements.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetLocalizedControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType().
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.GetNameCore">
            <summary>
            Gets the core name for this Telerik.Windows.Controls.RadPaneAutomationPeer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadPaneAutomationPeer.IsOffscreenCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DefaultGeneratedItemsFactory">
            <summary>
            This is the default implementation of the <see cref="T:Telerik.Windows.Controls.Docking.IGeneratedItemsFactory" /> interface.
            It just creates the new instances.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultGeneratedItemsFactory.CreateToolWindow">
            <summary>
            Creates a new ToolWindow instance.
            </summary>
            <returns>A new instance of the ToolWindow class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultGeneratedItemsFactory.CreateSplitContainer">
            <summary>
            Creates a new RadSplitContainer instance.
            </summary>
            <returns>A new instance of the RadSplitContainer class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultGeneratedItemsFactory.CreatePaneGroup">
            <summary>
            Creates a new RadPaneGroup instance.
            </summary>
            <returns>A new instance of the RadPaneGroup class.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingNavigatorListBox">
            <summary>
            Custom ListBox used in the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/> control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.DockingNavigatorListBox"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.ShouldHandleKeyboardInListControl">
            <summary>
            Called during keyboard input to decide whether keyboard should be handled in base ListControl or not.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.GetContainerForItemOverride">
            <summary>
            Creates or identifies the element that is used to display the given item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.HandleKeyboardInput(System.Windows.Input.Key)">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:Telerik.Windows.Controls.Primitives.ListControl.HandleKeyboardInput(System.Windows.Input.Key)"/>. Override this method if a custom keyboard navigation is required.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.KeyDown"/> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBox.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem">
            <summary>
            Custom ListBox item used in the <see cref="T:Telerik.Windows.Controls.Docking.DockingNavigatorListBox" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.GotFocus"/> event occurs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem.OnLostFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.LostFocus"/> event occurs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingNavigatorListBoxItem.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.NavigatorClosedEventArgs">
            <summary>
            Event args for the <see cref="E:Telerik.Windows.Controls.DockingNavigator.Closed"/> event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorClosedEventArgs.PaneToActivate">
            <summary>
            Gets or sets the pane to be activated after navigator closes.
            If null, this means no matching pane is found for the selected item model in navigator.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.NavigatorOpeningEventArgs">
            <summary>
            Event args for the <see cref="E:Telerik.Windows.Controls.DockingNavigator.Opening"/> event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorOpeningEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the opening of the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/> should be cancelled.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.NavigatorViewModel">
            <summary>
            The view model class used for the <see cref="T:Telerik.Windows.Controls.DockingNavigator" /> control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.NavigatorViewModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.NavigatorViewModel"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Docking.NavigatorViewModel.SelectedPaneChanged">
            <summary>
            Called when selected pane in the navigator is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorViewModel.ActivePanesModels">
            <summary>
            Gets the collection of non document pane models.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorViewModel.ActiveDocumentsModels">
            <summary>
            Gets the collection of document pane models.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorViewModel.SelectedModel">
            <summary>
            Gets or sets the selected pane model in the navigator.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorViewModel.ItemModels">
            <summary>
            Gets or sets all item models for this navigator view model.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.NavigatorItemViewModel">
            <summary>
            ViewModel class for the items in the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.Header">
            <summary>
            Gets or sets the header of this navigator item model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.Description">
            <summary>
            Gets or sets the description of this navigator item model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.Footer">
            <summary>
            Gets or sets the footer of this navigator item model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.Icon">
            <summary>
            Gets or sets the icon of this navigator item model.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.IsDocumentPane">
            <summary>
            Gets or sets a value indicating whether this navigator item model is created for document pane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.ToString">
            <summary>
            Gets the string representation of this item model.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal
            to the current <see cref="T:System.Object"/>.
            </summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.NavigatorItemViewModel.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DragDropGroup">
            <summary>
            Defines virtual drag drop group (domain) for dragging Panes / ToolWindows between different RadDocking instances.
            Set the attached property Name to a set (domain) of RadDocking instances to define a drag drop group.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DragDropGroup.NameProperty">
            <summary>
            Identifies the Name dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DragDropGroup.GetName(System.Windows.DependencyObject)">
            <summary>
            Sets the drag drop group name.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DragDropGroup.SetName(System.Windows.DependencyObject,System.String)">
            <summary>
            Gets the drag drop group name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ClosePaneMode">
            <summary>
            Defines the possible ways <see cref="P:Telerik.Windows.Controls.RadDockingCommands.ClosePane"/> command is executed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ClosePaneMode.DocumentPanes">
            <summary>
            The active document pane is closed or if there is no active, the first selected document pane is closed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ClosePaneMode.NonDocumentPanes">
            <summary>
            The active non document pane is closed, if any.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ClosePaneMode.ActivePanes">
            <summary>
            The active pane is closed, if any.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ICancel">
            <summary>
            Adds cancel capability to the deriving classes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ICancel.Cancel">
            <summary>
            Gets or sets a value to the Cancel property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IElementProperties">
            <summary>
            Defines a property that provides information of an object's properties.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IElementProperties.ElementProperties">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.Dictionary`2"/> that contains information about the properties that are available of this object.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory">
            <summary>
            This is the default implementation used by the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control to save and load its layout.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.Docking">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.RadDocking"/> instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.SaveDocking(Telerik.Windows.Controls.SavingElementArgs)">
            <summary>
            Writes the details of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> instance to the generated Xml layout.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:Telerik.Windows.Controls.RadDocking"/> instance being saved to the layout Xml.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.SaveSplitContainer(Telerik.Windows.Controls.SavingElementArgs)">
            <summary>
            Writes the details of the <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> instance to the generated Xml layout.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> instance being saved to the layout Xml.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.SavePaneGroup(Telerik.Windows.Controls.SavingElementArgs)">
            <summary>
            Writes the details of the <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance to the generated Xml layout.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance being saved to the layout Xml.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.SavePane(Telerik.Windows.Controls.SavingElementArgs)">
            <summary>
            Writes the details of the <see cref="T:Telerik.Windows.Controls.RadPane"/> instance to the generated Xml layout.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:Telerik.Windows.Controls.RadPane"/> instance being saved to the layout Xml.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.LoadDocking(Telerik.Windows.Controls.LoadingParentElementArgs)">
            <summary>
            Creates the contents of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> instance from a provided Xml.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:Telerik.Windows.Controls.RadDocking"/> instance layout.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.LoadSplitContainer(Telerik.Windows.Controls.LoadingParentElementArgs)">
            <summary>
            Creates a <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> instance from a provided Xml.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:System.Windows.DependencyObject"/> being restored from the provided layout Xml.</param>
            <returns>An <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> instance with its persisted properties.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.LoadPaneGroup(Telerik.Windows.Controls.LoadingParentElementArgs)">
            <summary>
            Creates a <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance from a provided Xml.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:System.Windows.DependencyObject"/> being restored from the provided layout Xml.</param>
            <returns>An <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance with its persisted properties.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.LoadPane(Telerik.Windows.Controls.LoadingElementArgs)">
            <summary>
            Creates a <see cref="T:Telerik.Windows.Controls.RadPane"/> instance from a provided Xml.
            </summary>
            <param name="args">Arguments containing details about the <see cref="T:System.Windows.DependencyObject"/> being restored from the provided layout Xml.</param>
            <returns>An <see cref="T:Telerik.Windows.Controls.RadPane"/> instance with its persisted properties.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.CleanUpLayout">
            <summary>
            Clears the <see cref="T:Telerik.Windows.Controls.RadDocking"/> content.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.LoadLayout(System.Xml.XmlReader)">
            <summary>
            Initializes the loading of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control's layout to Xml.
            </summary>
            <param name="reader">The used <see cref="T:System.Xml.XmlReader"/> <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.SaveLayout(System.Xml.XmlWriter)">
            <summary>
            Initializes the saving of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control's layout to Xml.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter"/> used by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.ReadPaneGroupXml(System.Xml.XmlReader,Telerik.Windows.Controls.RadPaneGroup,Telerik.Windows.Controls.Docking.PreReadXmlElement,System.Boolean)">
            <summary>
            Reads the provided <see cref="T:System.Xml.XmlReader"/> in order to load a <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance.
            </summary>
            <param name="reader">The used <see cref="T:System.Xml.XmlReader"/>.</param>
            <param name="group">The <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> that is being saved.</param>
            <param name="preReadElement">A proxy that contains information of the currently being saved <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/>.</param>
            <param name="shouldRaiseElementLoading">Indicates if the <see cref="E:Telerik.Windows.Controls.RadDocking.ElementLoading"/> event should be raised for elements with <see cref="F:Telerik.Windows.Controls.RadDocking.SerializationTagProperty"/> set..</param>
            <returns>
            The <see cref="M:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory.ReadPaneGroupXml(System.Xml.XmlReader,Telerik.Windows.Controls.RadPaneGroup,Telerik.Windows.Controls.Docking.PreReadXmlElement,System.Boolean)"/> method can be used in scenarios 
            where the <see cref="E:Telerik.Windows.Controls.RadDocking.ElementLoading"/> event was already raised which is why we need to suspend it.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingPanesFactory">
            <summary>
            This factory helps PanesSource property usage in MVVM scenarios. It creates new <see cref="T:Telerik.Windows.Controls.RadPane"/> instances, check whether an item is its own container or not and adds the new generated <see cref="T:Telerik.Windows.Controls.RadPane"/> to the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.CreatePaneForItem(System.Object)">
            <summary>
            Creates a new RadPane instance for item from the <see cref="P:Telerik.Windows.Controls.RadDocking.PanesSource"/> collection.
            Make sure to apply set any properties, styles and templates so that the generated <see cref="T:Telerik.Windows.Controls.RadPane"/> may look properly in the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.
            For example set any of the DataContext, Header, Title or Content based on the item as well as Style with
            bindings in the style setters to bind properties of the item to properties of the <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="item">The item a <see cref="T:Telerik.Windows.Controls.RadPane"/> is generated for.</param>
            <returns>A new instance of the <see cref="T:Telerik.Windows.Controls.RadPane"/> class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.CreatePaneForItem(Telerik.Windows.Controls.RadDocking,System.Object)">
            <summary>
            Creates a new RadPane instance for item from the <see cref="P:Telerik.Windows.Controls.RadDocking.PanesSource"/> collection.
            Make sure to apply set any properties, styles and templates so that the generated <see cref="T:Telerik.Windows.Controls.RadPane"/> may look properly in the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.
            For example set any of the DataContext, Header, Title or Content based on the item as well as Style with
            bindings in the style setters to bind properties of the item to properties of the <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="radDocking">The <see cref="T:Telerik.Windows.Controls.RadDocking"/> a <see cref="T:Telerik.Windows.Controls.RadPane"/> instance is being added to.</param>
            <param name="item">The item a <see cref="T:Telerik.Windows.Controls.RadPane"/> is generated for.</param>
            <returns>A new instance of the <see cref="T:Telerik.Windows.Controls.RadPane"/> class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.IsItemItsOwnPaneContainer(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own <see cref="T:Telerik.Windows.Controls.RadPane"/> container.
            </summary>
            <param name="item">The item to check.</param>
            <returns>true if the item is (or is eligible to be) its own <see cref="T:Telerik.Windows.Controls.RadPane"/> container; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.AddPane(Telerik.Windows.Controls.RadDocking,Telerik.Windows.Controls.RadPane)">
            <summary>
            Adds the <paramref name="pane"/> to the <paramref name="radDocking"/> layout.
            If there is no available containers to generate the new content
            please use the <paramref name="radDocking"/>'s <see cref="P:Telerik.Windows.Controls.RadDocking.GeneratedItemsFactory"/>
            to create additional <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>s and <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/>s.
            </summary>
            <param name="radDocking">The <see cref="T:Telerik.Windows.Controls.RadDocking"/> a <see cref="T:Telerik.Windows.Controls.RadPane"/> instance is being added to.</param>
            <param name="pane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> to add.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.GetPaneFromItem(Telerik.Windows.Controls.RadDocking,System.Object)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.RadPane"/> from the <paramref name="item"/> parameter.
            </summary>
            <param name="docking">The <see cref="T:Telerik.Windows.Controls.RadDocking"/> a <see cref="T:Telerik.Windows.Controls.RadPane"/> instance is being get to.</param>
            <param name="item">The item which is used to get the pane.</param>
            <returns>The <see cref="T:Telerik.Windows.Controls.RadPane"/> instance.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingPanesFactory.RemovePane(Telerik.Windows.Controls.RadPane)">
            <summary>
            Removes the <paramref name="pane"/> from the <see cref="T:Telerik.Windows.Controls.RadDocking"/> layout. By default clears the Header, Content, DataContext and call RemoveFromParent method.
            </summary>
            <param name="pane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> to remove.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingDragDropMode">
            <summary>
            Specifies the drag drop mode of RadDocking.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockingDragDropMode.Immediate">
            <summary>
            DragDropMode that opens the dragged RadPane or RadPaneGroup in a new ToolWindow.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockingDragDropMode.Deferred">
            <summary>
            DragDropMode that allows to drag drop RadPane or RadPaneGroup without opening them in a new ToolWindow.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingDragDropPayload">
            <summary>
            Represents a wrapper for drag and drop information in RadDocking.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.DockingDragDropPayload.Title">
            <summary>
            The title of the dragged element.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DraggedElementVisualCue">
            <summary>
            A helper class, that serves as a visual cue during drag and drop operations in RadDocking.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DraggedElementVisualCue.#ctor">
            <summary>
            Initializes a new instance of the DraggedElementVisualCue class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ActivationMode">
            <summary>
            Determines which pane should be activated when the selected is removed. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ActivationMode.First">
            <summary>
            The first non disabled and visible item in the items collection is activated.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ActivationMode.Last">
            <summary>
            The last non disable and visible item in the items collection is activated.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ActivationMode.Previous">
            <summary>
            The previous non disabled and visible item in the items collection is activated.
            If there is no such item the next non disabled and visible item is activated.
            If there is no such item no action is performed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ActivationMode.Next">
            <summary>
            The next non disabled and visible item in the items collection is activated.
            If there is no such item the previous non disabled and visible  item is activated.
            If there is no such item no action is performed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ActivationMode.LastActivated">
            <summary>
            The previous activated item in the items collection is activated.
            If there is no such item the first non disabled and visible item is activated.
            If there is no such item no action is performed.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.CloseButtonPosition">
            <summary>
            TODO: Update summary.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.CloseButtonPosition.InGroup">
            <summary>
            Displays CloseButton in PaneGroup.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.CloseButtonPosition.InPane">
            <summary>
            Displays CloseButton in Pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.CloseButtonPosition.InPaneAndGroup">
            <summary>
            Displays CloseButton in Pane and PaneGroup.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.RetainSizeMode">
            <summary>
            Determines whether the size of all panes should be retained when docking/floating them.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.RetainSizeMode.None">
            <summary>
            The size of the panes is never retained. This is the default value.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.RetainSizeMode.Floating">
            <summary>
            Only the last floating size of the panes is retained.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.RetainSizeMode.DockingAndFloating">
            <summary>
            Current size of the panes is always retained.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs">
            <summary>
            The event args are used when the <see cref="T:Telerik.Windows.Controls.Docking.DefaultGeneratedItemsFactory"/> the dynamically creates the <see cref="T:Telerik.Windows.Controls.RadDocking"/>'s elements.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs.#ctor(System.Windows.DependencyObject,System.Windows.DependencyObject)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs"/> class.
            </summary>
            <param name="sourceElement">The root <see cref="T:System.Windows.DependencyObject"/> element.</param>
            <param name="createdElement">The newly auto generated element.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs.SourceElement">
            <summary>
            Gets the <see cref="T:System.Windows.DependencyObject"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs.CreatedElement">
            <summary>
            Gets the newly auto generated element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PreReadXmlElement.#ctor(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.PreReadXmlElement"/> class. This is a proxy that contains information of the currently being saved <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="elementName">The XML tag that triggered the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>'s Load functionality.</param>
            <param name="serializationTag">The <see cref="F:Telerik.Windows.Controls.RadDocking.SerializationTagProperty"/> read from the layout XML.</param>
            <param name="attributes">Dictionary that contains the read attributes related to the 'element'.</param>
            <param name="elementXmlDepth">The <see cref="P:System.Xml.XmlReader.Depth"/> of the current 'element'.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ClickFlyoutBehavior">
            <summary>
            Represents a <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> implementation that opens the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> flyout only when the pane is
            clicked or activated and closes it on second click or deactivation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.HoverFlyoutBehavior">
            <summary>
            Represents a <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> implementation that opens the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> flyout on hover or activation and closes is when 
            the mouse leaves the pane and the flyout.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.FlyoutState">
            <summary>
            Represents a state of the flyout of <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.Closed">
            <summary>
            The flyout is closed and is not in process of opening.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.Opened">
            <summary>
            The flyout is opened and is not in process of closing.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.OpeningTimer">
            <summary>
            The flyout is closed, but the open timer is counting down.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.OpeningAnimation">
            <summary>
            The flyout is visible and its open animation is playing.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.ClosingTimer">
            <summary>
            The flyout is opened, but the close timer is counting down.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.FlyoutState.ClosingAnimation">
            <summary>
            The flyout is visible and its close animation is playing.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior">
            <summary>
            This interface describes a the behavior of the flyout of the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnMouseEnter(Telerik.Windows.Controls.Docking.IFlyoutHost,Telerik.Windows.Controls.RadPane)">
            <summary>
            This method is called when the mouse enters a <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the <see cref="T:Telerik.Windows.Controls.RadPane"/>.</param>
            <param name="targetPane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> in which the mouse just entered.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnMouseLeave(Telerik.Windows.Controls.Docking.IFlyoutHost)">
            <summary>
            This method is called when the mouse leaves a <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the <see cref="T:Telerik.Windows.Controls.RadPane"/>.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnOpeningTimerTimeout(Telerik.Windows.Controls.Docking.IFlyoutHost)">
            <summary>
            This method is called when the time of the open timer is up. Commonly the open animation is started here.
            The open timer is started by calling the StartOpenTimer method of the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutHost"/>.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the flyout.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnClosingTimerTimeout(Telerik.Windows.Controls.Docking.IFlyoutHost)">
            <summary>
            This method is called when the time of the close timer is up. Commonly the close animation is started here.
            The close timer is started by calling the StartCloseTimer method of the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutHost"/>.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the flyout.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnPaneActivated(Telerik.Windows.Controls.Docking.IFlyoutHost,Telerik.Windows.Controls.RadPane)">
            <summary>
            This method is called when a <see cref="T:Telerik.Windows.Controls.RadPane"/> is being activated.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the <see cref="T:Telerik.Windows.Controls.RadPane"/>.</param>
            <param name="targetPane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> which just got activated.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnPaneDeactivated(Telerik.Windows.Controls.Docking.IFlyoutHost,Telerik.Windows.Controls.RadPane)">
            <summary>
            This method is called when a <see cref="T:Telerik.Windows.Controls.RadPane"/> is being deactivated.
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the <see cref="T:Telerik.Windows.Controls.RadPane"/>.</param>
            <param name="targetPane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> which just got deactivated.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutBehavior.OnPaneMouseLeftButtonDown(Telerik.Windows.Controls.Docking.IFlyoutHost,Telerik.Windows.Controls.RadPane)">
            <summary>
            This method is called when a pane receives the MouseLeftButtonDown event (i.e. when the user clicks it).
            </summary>
            <param name="host">The <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> hosting the <see cref="T:Telerik.Windows.Controls.RadPane"/>.</param>
            <param name="targetPane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> which just got clicked.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IFlyoutHost">
            <summary>
            This interface represents an abstraction over the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> allowing the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> to read its state
            and to command it.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IFlyoutHost.SelectedPane">
            <summary>
            Gets the currently selected <see cref="T:Telerik.Windows.Controls.RadPane"/> in the flyout.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IFlyoutHost.CurrentState">
            <summary>
            Gets the current <see cref="T:Telerik.Windows.Controls.Docking.FlyoutState"/> of the flyout.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IFlyoutHost.IsMouseOver">
            <summary>
            Gets a value indicating whether the mouse is over the selected <see cref="T:Telerik.Windows.Controls.RadPane"/> or the flyout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.SetSelectedPane(Telerik.Windows.Controls.RadPane)">
            <summary>
            Changes the current selected <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="pane">The new <see cref="T:Telerik.Windows.Controls.RadPane"/> to be selected.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.ActivatePane(Telerik.Windows.Controls.RadPane)">
            <summary>
            Changes the current active <see cref="T:Telerik.Windows.Controls.RadPane"/>.
            </summary>
            <param name="pane">The pane to be activated.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.StartOpenTimer">
            <summary>
            Starts the open timer of the flyout. When the timeout passes, the OnOpeningTimerTimeout method of the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> is called.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.CancelOpenTimer">
            <summary>
            Cancels the open timer, if running. The behavior is not notified for this, but the current state is updated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.StartCloseTimer">
            <summary>
            Starts the close timer of the flyout. When the timeout passes, the OnClosingTimerTimeout method of the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> is called.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.CancelCloseTimer">
            <summary>
            Cancels the close timer, if running. The behavior is not notified for this, but the current state is updated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.StartCloseAnimation">
            <summary>
            Starts the close animation of the flyout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.StartOpenAnimation">
            <summary>
            Starts the open animation of the flyout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.Open">
            <summary>
            Opens the flyout instantly.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IFlyoutHost.Close">
            <summary>
            Closes the flyout instantly.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DocumentHost">
            <summary>
            This is a placeholder for the document area in the RadDocking.
            </summary>
            <remarks>
            	<para>
            		The DocumentHost is an internal class that identifies the position of the documents area
            		in the <see cref="T:Telerik.Windows.Controls.RadDocking"/>. The use of this class is internal, in the ControlTemplate
            		of the RadDocking.
            	</para>
            </remarks>
            <seealso cref="T:Telerik.Windows.Controls.RadDocking"/>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.#ctor">
            <summary>
            Initializes a new instance of the DocumentHost class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.AddPane(Telerik.Windows.Controls.RadPane)">
            <summary>
            Adds the pane to the document host.
            </summary>
            <param name="pane">The pane to add.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.AddSplitItem(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            Adds the split item to the document host.
            </summary>
            <param name="itemToAdd">The item to add.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DocumentHost.OnContentChanged(System.Object,System.Object)">
            <summary>
            Called when the value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/>
            property changes.
            </summary>
            <param name="oldContent">The old value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/> property.</param>
            <param name="newContent">The new value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/> property.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockPosition">
            <summary>
            Describes position in the compass.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockPosition.Top">
            <summary>
            The top position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockPosition.Bottom">
            <summary>
            The bottom position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockPosition.Center">
            <summary>
            The center position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockPosition.Left">
            <summary>
            The left position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockPosition.Right">
            <summary>
            The right position.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockState">
            <summary>
            Describes state of a item in the docking.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.DockedLeft">
            <summary>
            The item is docked to the left.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.DockedBottom">
            <summary>
            The item is docked to the bottom.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.DockedRight">
            <summary>
            The items is docked to the right.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.DockedTop">
            <summary>
            The item is docked to the top.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.FloatingDockable">
            <summary>
            The item is not docked, but is dockable.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.DockState.FloatingOnly">
            <summary>
            The item is not dockable.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ResizeDirection">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.Left">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.TopLeft">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.Top">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.TopRight">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.Right">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.BottomRight">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.Bottom">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.BottomLeft">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ResizeDirection.None">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs">
            <summary>
            The event arguments are used for ActivePangeChanged event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,Telerik.Windows.Controls.RadPane,Telerik.Windows.Controls.RadPane)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs"/> class.
            </summary>
            <param name="routedEvent">
            The routed event identifier for this instance of the RoutedEventArgs class.
            </param>
            <param name="source">
            An alternate source that will be reported when the event is handled. 
            This pre-populates the Source property.
            </param>
            <param name="oldPane">The old active pane.</param>
            <param name="newPane">The new active pane that was just activated.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs.OldPane">
            <summary>
            Gets the old active pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs.NewPane">
            <summary>
            Gets the new active pane that was just activated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DragInfoEventArgs.#ctor">
            <summary>
            Initializes a new instance of the DragInfoEventArgs class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DragInfoEventArgs.#ctor(System.Windows.RoutedEvent)">
            <summary>
            Initializes a new instance of the DragInfoEventArgs class, 
            using the supplied routed event identifier. 
            </summary>
            <param name="routedEvent">
            The routed event identifier for this instance of the RoutedEventArgs class.
            </param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs">
            <summary>
            The event args are PreviewShowCompass event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.#ctor(Telerik.Windows.Controls.RadPaneGroup,System.Object,Telerik.Windows.Controls.Docking.Compass)">
            <summary>
            Initializes a new instance of the PreviewShowCompassEventArgs class.
            </summary>
            <param name="targetGroup">
            The target group.
            </param>
            <param name="draggedElement">
            The dragged element. 
            </param>
            <param name="compass">
            The compass that is going to be shown.
            </param>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.TargetGroup">
            <summary>
            Gets the target group.
            </summary>
            <value>The target group.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.DraggedElement">
            <summary>
            Gets the dragged element.
            </summary>
            <value>The dragged element. It can be RadSplitContainer, RadPaneGroup or a RadPane.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.Compass">
            <summary>
            Gets the compass that is going to be shown.
            </summary>
            <value>The compass that is going to be shown.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.Canceled">
            <summary>
            Gets or sets a value indicating whether the action is canceled.
            </summary>
            <value><c>True</c> if the action must be canceled; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs.CanDropOnTabStrip">
            <summary>
            Gets or sets a value indicating whether a RadPane can be dropped on the TabStrip of the RadPaneGroup or reordered.
            </summary>
            <value><c>True</c>Can be dropped in RadPaneGroup or reordered.<c>False</c> Cannot be dropped in RadPaneGroup or reordered.<c>Null</c> Gets the value of the <see cref="P:Telerik.Windows.Controls.Docking.Compass.IsCenterIndicatorVisible"/>. </value>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ResizeEventArgs">
            <summary>
            Provides data for the PreviewResize event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ResizeEventArgs.#ctor(System.Windows.RoutedEvent,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.ResizeEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event.</param>
            <param name="source">The source.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ResizeEventArgs.InvokeEventHandler(System.Delegate,System.Object)">
            <summary>
            When overridden in a derived class, provides a way to invoke event handlers in a type-specific way, which can increase efficiency over the base implementation.
            </summary>
            <param name="genericHandler">The generic handler / delegate implementation to be invoked.</param>
            <param name="genericTarget">The target on which the provided handler should be invoked.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs">
            <summary>
            The event args are used for pin/unpin events.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.StateChangeEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,System.Collections.Generic.IEnumerable{Telerik.Windows.Controls.RadPane})">
            <summary>
            Initializes a new instance of the StateChangeEventArgs class.
            </summary>
            <param name="routedEvent">
            The routed event identifier for this instance of the RoutedEventArgs class.
            </param>
            <param name="source">
            An alternate source that will be reported when the event is handled. 
            This pre-populates the Source property.
            </param>
            <param name="panes">The panes that are changed.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.StateChangeEventArgs.Panes">
            <summary>
            Gets the panes that are changed.
            </summary>
            <value>The panes that are changed.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DefaultSaveLoadLayoutHelper">
            <summary>
            Represents an object that is responsible for listening the save/load layout events and caching the controls that should be reused.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultSaveLoadLayoutHelper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.DefaultSaveLoadLayoutHelper"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultSaveLoadLayoutHelper.ElementCleanedOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the cleaned override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DefaultSaveLoadLayoutHelper.ElementLoadingOverride(System.String)">
            <summary>
            Elements the loading override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockElementsPopup.GetWindowOwner">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockElementsPopup.GetWindowOwnerHandle">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper">
            <summary>
            This class helps traversing the logical tree of the Docking control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetRootLevelSplitContainer(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            Resolves the <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>, which direct parent is not a SplitContainer, containing the target element.
            Commonly these are the split containers, hosted in <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> or directly in the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
            <param name="splitItem">The <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> or <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> which root-level container to search for.</param>
            <returns>A <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> which direct parent is not of type <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetRootLevelSplitContainer(Telerik.Windows.Controls.RadPane)">
            <summary>
            Resolves the <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>, which direct parent is not a SplitContainer, containing the target element.
            Commonly these are the split containers, hosted in <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> or directly in the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
            <param name="pane">The <see cref="T:Telerik.Windows.Controls.RadPane"/> which root-level container to search for.</param>
            <returns>A <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> which direct parent is not of type <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentSplitContainer(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            
            </summary>
            <param name="splitItem"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentSplitContainer(Telerik.Windows.Controls.RadPane)">
            <summary>
            
            </summary>
            <param name="radPane"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentToolWindow(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            
            </summary>
            <param name="splitContainer"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentToolWindow(Telerik.Windows.Controls.RadPane)">
            <summary>
            
            </summary>
            <param name="pane"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentDocking(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            
            </summary>
            <param name="splitContainer"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentDocking(Telerik.Windows.Controls.RadPane)">
            <summary>
            
            </summary>
            <param name="pane"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentDocking(Telerik.Windows.Controls.Docking.ToolWindow)">
            <summary>
            Gets the parent Docking control of the ToolWindow.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockingLogicalTreeHelper.GetParentPane(System.Windows.UIElement)">
            <summary>
            
            </summary>
            <param name="element"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper">
            <summary>
            When implemented this class represents an object that is responsible for listening 
            the save/load layout events and caching the controls that should be reused.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.AttachEventsOverride(Telerik.Windows.Controls.RadDocking)">
            <summary>
            Attaches the events override.
            </summary>
            <param name="docking">The docking.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.DetachEventsOverride(Telerik.Windows.Controls.RadDocking)">
            <summary>
            Detaches the events override.
            </summary>
            <param name="docking">The docking.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementSavingOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the saving override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementSavedOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the saved override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementLoadingOverride(System.String)">
            <summary>
            Elements the loading override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementLoadedOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the loaded override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementCleaningOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the cleaning override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.SaveLoadLayoutHelper.ElementCleanedOverride(System.String,System.Windows.DependencyObject)">
            <summary>
            Elements the cleaned override.
            </summary>
            <param name="serializationTag">The serialization tag.</param>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.InputBindingsManager">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.InputBindingsManager.InputBindingsProperty">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.InputBindingsManager.GetInputBindings(System.Windows.DependencyObject)">
            <summary>
            Get the DependencyObject's InputBindings collection. 
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.InputBindingsManager.SetInputBindings(System.Windows.DependencyObject,System.Windows.Input.InputBindingCollection)">
            <summary>
            Set the DependencyObject's InputBindings collection.
            </summary>
            <param name="obj"></param>
            <param name="value"></param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IGeneratedItemsFactory">
            <summary>
            This interface describes a factory for generating items for the Docking control - 
            ToolWindows, RadSplitContainers and RadPaneGroups.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IGeneratedItemsFactory.CreateToolWindow">
            <summary>
            Creates a new ToolWindow instance.
            </summary>
            <returns>A new instance of the ToolWindow class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IGeneratedItemsFactory.CreateSplitContainer">
            <summary>
            Creates a new RadSplitContainer instance.
            </summary>
            <returns>A new instance of the RadSplitContainer class.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IGeneratedItemsFactory.CreatePaneGroup">
            <summary>
            Creates a new RadPaneGroup instance.
            </summary>
            <returns>A new instance of the RadPaneGroup class.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.PaneGroupBase">
            <summary>
            Represents a base class for storing RadPanes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.PaneGroupBase.SelectedPaneProperty">
            <summary> 
            Identifies the SelectedPane dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.PaneGroupBase" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PaneGroupBase.SelectedPane">
            <summary>
            Gets the currently selected pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PaneGroupBase.PaneHeader">
            <summary>
            Gets or sets the PaneHeader control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass)
            call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.OnGotKeyboardFocus(System.Windows.Input.KeyboardFocusChangedEventArgs)">
            <summary>
            Called before the <see cref="T:System.Windows.Input.KeyboardFocusChangedEventArgs"/> event occurs.
            </summary>
            <param name="e">The event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.GotFocus"/> event occurs.
            </summary>
            <param name="e">The event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.UpdateCheckedState(System.Boolean)">
            <summary>
            
            </summary>
            <param name="isChecked"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            Prepares the specified element to display the specified item.
            </summary>
            <param name="element">Element used to display the specified item.</param>
            <param name="item">Specified item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneGroupBase.OnSelectionChanged(Telerik.Windows.Controls.RadSelectionChangedEventArgs)">
            <summary>
            Raises the SelectionChanged routed event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.AutoHideArea">
            <summary>
            Tab control, contains the pane items that hide automatically at the sides of the Docking.
            </summary>
            <remarks>
                <para>
                    This a class for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Docking.AutoHideArea.LayoutChangeStarted">
            <summary>
            Occurs when the layout change is started.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.Docking.AutoHideArea.LayoutChangeEnded">
            <summary>
            Occurs when the layout change is ended.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.AutoHideArea.IsLayoutChanging">
            <summary>
            Gets a value indicating whether this instance is layout changing.
            </summary>
            <value>
            	<c>True</c> if this instance is layout changing; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass)
            call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.OnSelectionChanged(Telerik.Windows.Controls.RadSelectionChangedEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.GetContainerForItemOverride">
            <summary>
            Creates or identifies the element that is used to display the given item.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own container.
            </summary>
            <param name="item">The item to check.</param>
            <returns>
            True if the item is (or is eligible to be) its own container; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.AutoHideArea.OnCreateAutomationPeer">
            <summary>
            Provides an appropriate <see cref="T:Telerik.Windows.Automation.Peers.AutoHideAreaAutomationPeer"/> implementation for this control, as part of the automation infrastructure.
            </summary>
            <returns>The type-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementation.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.Compass">
            <summary>
            Drop indicator that appears in the middle of a drop target.
            </summary>
            <remarks>
                <para>
                    This a class for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.DockPositionProperty">
            <summary>
            Identifies the DockPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.IsLeftIndicatorVisibleProperty">
            <summary>
            Identifies the IsLeftIndicatorVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.IsTopIndicatorVisibleProperty">
            <summary>
            Identifies the IsTopIndicatorVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.IsRightIndicatorVisibleProperty">
            <summary>
            Identifies the IsRightIndicatorVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.IsBottomIndicatorVisibleProperty">
            <summary>
            Identifies the IsBottomIndicatorVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.Compass.IsCenterIndicatorVisibleProperty">
            <summary>
            Identifies the IsCenterIndicatorVisible dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.Compass.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.Compass"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.DockPosition">
            <summary>
            Gets the highlight indicator in the compass.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.IsLeftIndicatorVisible">
            <summary>
            Gets or sets value indicating is the left indicator visible or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.IsTopIndicatorVisible">
            <summary>
            Gets or sets value indicating is the Top indicator visible or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.IsRightIndicatorVisible">
            <summary>
            Gets or sets value indicating is the Right indicator visible or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.IsBottomIndicatorVisible">
            <summary>
            Gets or sets value indicating is the Bottom indicator visible or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.Compass.IsCenterIndicatorVisible">
            <summary>
            Gets or sets value indicating is the Center indicator visible or not.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.Compass.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.Compass.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.DockDecoration">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.DockDecoration.OnApplyTemplate">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.PaneHeader">
            <summary>
            This class represents the header of a selected pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.PaneHeader.SelectedPaneProperty">
            <summary> 
            Identifies the SelectedPane dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.PaneHeader.IsHighlightedProperty">
            <summary>
            Identifies the IsHighlighted property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.PaneHeader.SelectedTitleTemplateProperty">
            <summary>
            Identifies the SelectedTitleTemplate property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.PaneHeader"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PaneHeader.SelectedPane">
            <summary>
            Gets or sets the selected pane.
            </summary>
            <value>The selected pane.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.PaneHeader.IsHighlighted">
            <summary>
            Gets or sets a value indicating whether this instance is highlighted.
            </summary>
            <value>
            	<c>True</c> if this instance is highlighted; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.UpdateVisualState">
            <summary>
            Changes the state of the visual.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnCreateAutomationPeer">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Raises the <see cref="E:MouseLeftButtonDown"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnDragInitialize(System.Object,Telerik.Windows.DragDrop.DragInitializeEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.PaneHeader.OnDragDropCompleted(System.Object,Telerik.Windows.DragDrop.DragDropCompletedEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the drag origin.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.RadGridResizer">
            <summary>
            The dock splitter is used as part of the RadSplitContainer control to resize elements.
            </summary>
            <remarks>
                <para>
                    This class is for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.RadGridResizer.PlacementProperty">
            <summary>
            Identifies the Placement property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.RadGridResizer.#ctor">
            <summary>
            Initializes a new instance of the RadGridResizer class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.RadGridResizer.Placement">
            <summary>
            Gets or sets the Placement which affects the resizing behavior.
            </summary>
            <value>The placement.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.RadGridResizer.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.RootCompass">
            <summary>
            Drop indicator that appears over RadDocking control.
            </summary>
            <remarks>
                <para>
                    This a class for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.RootCompass.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.RootCompass"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.RootCompass.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ToolWindow">
            <summary>
            This class represents a tool window.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.Docking.ToolWindow.IsSelectedProperty">
            <summary> 
            Identifies the IsSelected dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ToolWindow.IsSelected">
            <summary>
            Gets or sets a value indicating whether this pane is selected.
            </summary>
            <value>
            	<c>True</c> if this pane is active; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.UpdatePositionHelper(Telerik.Windows.Controls.Navigation.Positioning.PositionHelper)">
            <summary>
            
            </summary>
            <param name="helper"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.ShouldFocusOnActivate">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">Indicates whether transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.GetWindowOwner">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.GetWindowOwnerHandle">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.KeyDown"/> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnClosing">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnClosed">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnContentChanged(System.Object,System.Object)">
            <summary>
            Called when the value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/> property changes.
            </summary>
            <param name="oldContent">The old value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/> property.</param>
            <param name="newContent">The new value of the <see cref="P:System.Windows.Controls.ContentControl.Content"/> property.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnLayoutChangeEnded(System.EventArgs)">
            <summary>
            Raises the LayoutChangeEnded event of the RadDocking and the WindowBase.
            </summary>
            <param name="args">The System.EventArgs instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnLayoutChangeStarted(System.EventArgs)">
            <summary>
            Raises the LayoutChangeStarted event of the RadDocking and the WindowBase.
            </summary>
            <param name="args">The System.EventArgs instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnActivated(System.EventArgs)">
            <summary>
            Raises the <see cref="E:Activated"/> event. This method is called when the window is activated.
            </summary>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.CheckCanClose">
            <summary>
            Checks whether the window can be closed. This method doesn't consider the CanClose property, 
            but checked for some other kind of constraints.
            </summary>
            <returns><c>True</c> if the window can be changed; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnDragStart(System.Windows.Point,System.Boolean)">
            <summary>
            
            </summary>
            <param name="globalMousePosition"></param>
            <param name="isResize"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnDragDelta(System.Windows.Point,System.Windows.Rect,System.Windows.Rect,System.Boolean)">
            <summary>
            
            </summary>
            <param name="globalMousePosition"></param>
            <param name="initialRect"></param>
            <param name="destinationRect"></param>
            <param name="isResize"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnDragEnd(System.Windows.Point,System.Boolean,System.Boolean)">
            <summary>
            
            </summary>
            <param name="globalMousePosition"></param>
            <param name="isCancel"></param>
            <param name="isResize"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnRightMouseButtonUp">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ToolWindow.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.VisualCue">
            <summary>
            Visual indicator that appears over the area where dragged ToolWindow will be snapped.
            </summary>
            <remarks>
                <para>
                    This a class for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.VisualCue.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.Docking.VisualCue"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.VisualCue.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IDocumentHostAware">
            <summary>
            Interface implemented by the controls that want to be notifies when they are placed in or out of a document host.
            </summary>
            <remarks>
            	<para>
            		This is an interface that is intended to be used internally by the controls
            		that make up the RadDocking.
            	</para>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IDocumentHostAware.IsInDocumentHost">
            <summary>
            Gets or sets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IDocumentPane">
            <summary>
            Represents a document pane.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IDropTarget">
            <summary>
            Represents a drop target.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IPane">
            <summary>
            Represents a pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.IsPinned">
            <summary>
            Gets or sets a value indicating whether this instance is pinned.
            </summary>
            <value><c>True</c> if this instance is pinned; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.IsHidden">
            <summary>
            Gets or sets a value indicating whether this instance is hidden.
            </summary>
            <value><c>True</c> if this instance is hidden; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.IsDockable">
            <summary>
            Gets a value indicating whether this pane is dockable.
            </summary>
            <value>
            	<c>True</c> if this pane is dockable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.IsFloating">
            <summary>
            Gets a value indicating whether this pane is floating.
            </summary>
            <value>
            	<c>True</c> if this pane is floating; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.CanUserPin">
            <summary>
            Gets or sets a value indicating whether this instance can be pinned by the user.
            </summary>
            <value><c>True</c> if this instance can pin; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.CanUserClose">
            <summary>
            Gets or sets a value indicating whether this instance can be closed by the user.
            </summary>
            <value><c>True</c> if this instance can close; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.CanFloat">
            <summary>
            Gets or sets a value indicating whether this pane can float.
            </summary>
            <value><c>True</c> if this pane can float; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.CanDockInDocumentHost">
            <summary>
            Gets or sets a value indicating whether this instance can dock in the document host.
            </summary>
            <value>
            	<c>True</c> if this instance can dock in the document host; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IPane.Title">
            <summary>
            Gets or sets the title of the RadPane. This is a dependency property.
            </summary>
            <remarks>
                <para>
                    The title of the RadPane appears in the top header part of the pane, while its header 
                    is the content of its tab button.
                </para>
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.IPane.RemoveFromParent">
            <summary>
            Removes the pane from its parent.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.ISplitItem">
            <summary>
            Represents an object that can be the item of a SplitContainer.
            </summary>
            <remarks>
                <para>
                    This interface is intended for internal use.
                </para>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ISplitItem.Control">
            <summary>
            Gets the actual control that will be a visual child of the SplitContainer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.ISplitItem.ParentContainer">
            <summary>
            Gets or sets the SplitContainer that holds the item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Docking.ISplitItem.EnumeratePanes">
            <summary>
            Enumerates the panes contained by the split item and its children.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.Docking.IToolWindowAware">
            <summary>
            Interface implemented by the controls that want to be notifies when they are placed in or out of a tool window.
            </summary>
            <remarks>
            	<para>
            		This is an interface that is intended to be used internally by the controls
            		that make up the RadDocking.
            	</para>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.Docking.IToolWindowAware.IsInToolWindow">
            <summary>
            Gets or sets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DockingNavigator">
            <summary>
            Control used for navigating and selecting panes in RadDocking.
            It can be opened with Ctrl + Tab combination similar to the IDE Navigator in Visual Studio.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.HeaderProperty">
            <summary>
            Identifies the Header attached property of the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.DescriptionProperty">
            <summary>
            Identifies the Description attached property of the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.FooterProperty">
            <summary>
            Identifies the Footer attached property of the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.IconProperty">
            <summary>
            Identifies the Icon attached property of the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.GetHeader(System.Windows.DependencyObject)">
            <summary>
            Gets the Header of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.SetHeader(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the Header of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.GetDescription(System.Windows.DependencyObject)">
            <summary>
            Gets the Description of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.SetDescription(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the Description of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.GetFooter(System.Windows.DependencyObject)">
            <summary>
            Gets the Footer of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.SetFooter(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the Footer of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.GetIcon(System.Windows.DependencyObject)">
            <summary>
            Gets the Icon of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.SetIcon(System.Windows.DependencyObject,System.Object)">
            <summary>
            Sets the Icon of the pane used in <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.PreviewWidthProperty">
            <summary>
            Identifies the PreviewWidth dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.PreviewHeightProperty">
            <summary>
            Identifies the PreviewHeight dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.ActivePanesListBoxHeaderProperty">
            <summary>
            Identifies the ActivePanesListBoxHeader dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.ActivePanesListBoxStyleProperty">
            <summary>
            Identifies the ActivePanesListBoxStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.ActiveDocumentsListBoxHeaderProperty">
            <summary>
            Identifies the ActiveDocumentsListBoxHeader dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.ActiveDocumentsListBoxStyleProperty">
            <summary>
            Identifies the ActiveDocumentsListBoxStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.PreviewHeaderProperty">
            <summary>
            Identifies the PreviewHeader dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.ItemTemplateSelectorProperty">
            <summary>
            Identifies the ItemTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.HeaderTemplateProperty">
            <summary>
            Identifies the HeaderTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.HeaderTemplateSelectorProperty">
            <summary>
            Identifies the HeaderTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.HeaderStringFormatProperty">
            <summary>
            Identifies the HeaderStringFormat dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.FooterTemplateProperty">
            <summary>
            Identifies the FooterTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.FooterTemplateSelectorProperty">
            <summary>
            Identifies the FooterTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.FooterStringFormatProperty">
            <summary>
            Identifies the FooterStringFormat dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.DescriptionTemplateProperty">
            <summary>
            Identifies the DescriptionTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.DescriptionTemplateSelectorProperty">
            <summary>
            Identifies the DescriptionTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.DescriptionStringFormatProperty">
            <summary>
            Identifies the DescriptionStringFormat dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.IconTemplateProperty">
            <summary>
            Identifies the IconTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingNavigator.IconTemplateSelectorProperty">
            <summary>
            Identifies the IconTemplateSelector dependency property.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DockingNavigator.Opening">
            <summary>
            Occurs when docking navigator is about to open.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.DockingNavigator.Closed">
            <summary>
            Occurs when docking navigator is closed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.PreviewWidth">
            <summary>
            Gets or sets the width of the preview image.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.PreviewHeight">
            <summary>
            Gets or sets the height of the preview image.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.ActivePanesListBoxHeader">
            <summary>
            Gets or sets the header of the active panes ListBox.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.ActivePanesListBoxStyle">
            <summary>
            Gets or sets the Style for the active panes ListBox.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.ActiveDocumentsListBoxHeader">
            <summary>
            Gets or sets the header of the active documents panes ListBox.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.ActiveDocumentsListBoxStyle">
            <summary>
            Gets or sets the Style for the active documents ListBox.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.PreviewHeader">
            <summary>
            Gets or sets the header of the preview image.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.ItemTemplateSelector">
            <summary>
            Gets or sets the item template selector for this navigator instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.HeaderTemplate">
            <summary>
            Gets or sets the content template of the header presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.HeaderTemplateSelector">
            <summary>
            Gets or sets the content template selector of the header presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.HeaderStringFormat">
            <summary>
            Gets or sets the string format of the header presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.FooterTemplate">
            <summary>
            Gets or sets the content template of the footer presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.FooterTemplateSelector">
            <summary>
            Gets or sets the content template selector of the footer presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.FooterStringFormat">
            <summary>
            Gets or sets the string format of the footer presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.DescriptionTemplate">
            <summary>
            Gets or sets the content template of the description presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.DescriptionTemplateSelector">
            <summary>
            Gets or sets the content template selector of the description presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.DescriptionStringFormat">
            <summary>
            Gets or sets the string format of the description presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.IconTemplate">
            <summary>
            Gets or sets the content template of the icon presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.IconTemplateSelector">
            <summary>
            Gets or sets the content template selector of the icon presenter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.Docking">
            <summary>
            Gets or sets the related Docking control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigator.SelectDocumentPaneOnLoad">
            <summary>
            Gets or sets whether the navigator will select document pane item on load.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass) call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.Initialize">
            <summary>
            Called when view model is created for the navigator control and opening event is fired.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.ShouldOpen(System.Windows.Input.KeyEventArgs)">
            <summary>
            Determines whether the navigator should be opened based on the keyboard input.
            </summary>
            <param name="args">Keyboard event args.</param>
            <returns>True if the navigator should be opened.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnClosed">
            <summary>
            Called when the popup is closed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnCreateAutomationPeer">
            <inheritdoc /> 
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnOpening(Telerik.Windows.Controls.Docking.NavigatorOpeningEventArgs)">
            <summary>
            Called before the popup is opened.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnKeyUp(System.Windows.Input.KeyEventArgs)">
            <summary>
            Invoked when an unhandled System.Windows.Input.Keyboard.KeyUp attached event
             reaches an element in its route that is derived from this class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigator.OnUpdatePreview">
            <summary>
            Called when the preview image of the navigator is being updated.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DockingNavigatorItemTemplateSelector">
            <summary>
            ItemTemplateSelector for the panels and documents lists in the <see cref="T:Telerik.Windows.Controls.DockingNavigator"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigatorItemTemplateSelector.ActivePaneTemplate">
            <summary>
            DataTemplate for model which represents a non-document (panel) pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.DockingNavigatorItemTemplateSelector.ActiveDocumentTemplate">
            <summary>
            DataTemplate for model which represents a document pane.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingNavigatorItemTemplateSelector.SelectTemplate(System.Object,System.Windows.DependencyObject)">
            <summary>
            Selects a template for the given <see cref="T:Telerik.Windows.Controls.Docking.NavigatorItemViewModel"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadDocking">
            <summary>
            Pane navigation control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.NavigatorProperty">
            <summary>
            Identifies the Navigator dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.Navigator">
            <summary>
            Gets or sets the PaneNavigator instance used in the docking control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.KeyDown"/> event occurs.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.AnimationSelectorProxyProperty">
            <summary>
            Identifies the AnimationSelectorProxy dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetRadPane(System.Windows.UIElement)">
            <summary>
            Finds RadPane by visual element using the position of the element in the visual tree. This method implements internal logic of the RadDocking
            control that is able to associate a RadPane control to a visual element.
            </summary>
            <param name="element">The element to associate with a RadPane control.</param>
            <returns>The RadPane control, associated with the provided visual element. This method returns null if not able to find appropriate RadPane control.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application
            code or internal processes (such as a rebuilding layout pass) call
            <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnLostKeyboardFocus(System.Windows.Input.KeyboardFocusChangedEventArgs)">
            <summary>
            Invoked when an unhandled System.Windows.Input.Keyboard.LostKeyboardFocus attached
            event reaches an element in its route that is derived from this class. Tries to close the AutoHideArea if possible.
            </summary>
            <param name="e">The System.Windows.Input.KeyboardFocusChangedEventArgs that contains event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDragContainer(Telerik.Windows.Controls.RadSplitContainer,System.Windows.Point)">
            <summary>
            Call this method to notify the Docking control that a SplitContainer is being dragged by the user.
            </summary>
            <param name="container">The container, dragged by the user.</param>
            <param name="globalMousePosition">The global position of the mouse.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDropContainer(Telerik.Windows.Controls.RadSplitContainer,System.Windows.Point)">
            <summary>
            Call this method to notify the Docking control that a SplitContainer is being dropped by the user.
            </summary>
            <param name="container">The container, dragged by the user.</param>
            <param name="globalMousePosition">The global position of the mouse.</param>
            <returns>True if the drop was successful; otherwise - false. A drop is successful if the element is dropped over the compass.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Raises the <see cref="E:ItemsChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.MeasureOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the "measure" pass of Silverlight layout. Classes can override this method to define their own measure pass behavior.
            </summary>
            <param name="availableSize">The available size that this object can give to child objects. Infinity can be specified as a value to indicate that the object will size to whatever content is available.</param>
            <returns>
            The size that this object determines it needs during layout, based on its calculations of child object allotted sizes.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OpenInToolWindow(Telerik.Windows.Controls.RadPane,System.Boolean)">
            <summary>
            Opens a ToolWindow for a RadPane and returns the window.
            </summary>
            <param name="pane">The RadPane for which to open the window.</param>
            <param name="openImmediately">Indicates if the method will call the ShowWindow() method of the RadDocking. Default value is 'True'.</param>
            <returns>The newly opened window.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OpenInToolWindow(Telerik.Windows.Controls.RadPaneGroup,System.Boolean)">
            <summary>
            Opens a ToolWindow for a RadPaneGroup and returns the window.
            </summary>
            <param name="radPaneGroup">The RadPaneGroup for which to open the window.</param>
            <param name="openImmediately">Indicates if the method will call the ShowWindow() method of the RadDocking. Default value is 'True'.</param>
            <returns>The newly opened window.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OpenInToolWindow(Telerik.Windows.Controls.RadSplitContainer,System.Boolean)">
            <summary>
            Opens a ToolWindow for a RadSplitContainer and returns the window.
            </summary>
            <param name="container">The RadSplitContainer for which to open the window.</param>
            <param name="openImmediately">Indicates if the method will call the ShowWindow() method of the RadDocking. Default value is 'True'.</param>
            <returns>The newly opened window.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDockingElementDragged(System.Windows.Point,System.Object)">
            <summary>
            Called when a dock item is dragged.
            </summary>
            <param name="globalPosition">The global position of the mouse.</param>
            <param name="draggedElement">The element that was dragged.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnCreateAutomationPeer">
            <summary>
            Returns class-specific System.Windows.Automation.Peers.AutomationPeer implementations
            for the Windows Presentation Foundation (WPF) infrastructure.
            </summary>
            <returns>The type-specific System.Windows.Automation.Peers.AutomationPeer implementation.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDocumentHostDragOver(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with the DocumentHost element as the potential drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDocumentHostDragLeave(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with the DocumentHost element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDocumentHostDrop(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drop event with the DocumentHost element as the drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDragOver(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the potential drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDragLeave(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnDrop(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drop event with this element as the drop target.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PreviewPinEvent">
            <summary>
            Identifies the PreviewPin routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PinEvent">
            <summary>
            Identifies the Pin routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PreviewUnpinEvent">
            <summary>
            Identifies the PreviewUnpin routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.UnpinEvent">
            <summary>
            Identifies the Unpin routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PreviewCloseEvent">
            <summary>
            Identifies the PreviewClose routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.CloseEvent">
            <summary>
            Identifies the Close routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PreviewShowEvent">
            <summary>
            Identifies the PreviewShow routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ShowEvent">
            <summary>
            Identifies the Show routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ActivePaneChangedEvent">
            <summary>
            Identifies the ActivePaneChanged routed event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.CustomElementLoading">
            <summary>
            Occurs when an custom element starts loading.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementLoading">
            <summary>
            Occurs when an element starts loading.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementLoaded">
            <summary>
            Occurs when an element is loaded.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementLayoutSaving">
            <summary>
            Occurs when an element starts saving.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementSaved">
            <summary>
            Occurs when an element is saved.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementLayoutCleaning">
            <summary>
            Occurs when an element starts cleaning up.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ToolWindowCreated">
            <summary>
            Occurs when an <see cref="T:Telerik.Windows.Controls.Docking.ToolWindow"/> instance is automatically created by the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PaneGroupCreated">
            <summary>
            Occurs when an <see cref="T:Telerik.Windows.Controls.RadPaneGroup"/> instance is automatically created by the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.SplitContainerCreated">
            <summary>
            Occurs when an <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> instance is automatically created by the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ElementCleaned">
            <summary>
            Occurs when an element is cleaned up.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PreviewShowCompass">
            <summary>
            Occurs before the compass is shown.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PreviewPin">
            <summary>
            Occurs when a pane or a group is pined.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.Pin">
            <summary>
            Occurs when a pane or a group is pined.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PreviewUnpin">
            <summary>
            Occurs when a pane or a group is unpinned.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.Unpin">
            <summary>
            Occurs when a pane or a group is unpinned.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PreviewClose">
            <summary>
            Occurs when a pane or a group is closed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PaneStateChange">
            <summary>
            Occurs when the state of a RadPane is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.Close">
            <summary>
            Occurs when a pane or a group is closed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.PreviewShow">
            <summary>
            Occurs when a pane or a group is shown.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.Show">
            <summary>
            Occurs when a pane or a group is shown.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.ActivePaneChanged">
            <summary>
            Occurs when the active pane is changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.LayoutChangeStarted">
            <summary>
            Occurs when the layout change is started.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadDocking.LayoutChangeEnded">
            <summary>
            Occurs when the layout change is ended.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPreviewPin(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewPin"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPin(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:Pin"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPreviewUnpin(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewUnpin"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnUnpin(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:Pin"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPreviewShowCompass(Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewShowCompass"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.PreviewShowCompassEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPreviewClose(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewClose"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnClose(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:Close"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPreviewShow(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:PreviewShow"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnShow(Telerik.Windows.Controls.Docking.StateChangeEventArgs)">
            <summary>
            Raises the <see cref="E:Show"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.StateChangeEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnToolWindowCreated(Telerik.Windows.Controls.Docking.ElementCreatedEventArgs)">
            <summary>
            Raises the <see cref="E:ToolWindowCreated"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnPaneGroupCreated(Telerik.Windows.Controls.Docking.ElementCreatedEventArgs)">
            <summary>
            Raises the <see cref="E:PaneGroupCreated"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnSplitContainerCreated(Telerik.Windows.Controls.Docking.ElementCreatedEventArgs)">
            <summary>
            Raises the <see cref="E:SplitContainerCreated"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.Docking.ElementCreatedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementLoading(Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs)">
            <summary>
            Raises the <see cref="E:ElementLoading"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnCustomElementLoading(Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs)">
            <summary>
            Raises the <see cref="E:CustomElementLoading"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementLoaded(Telerik.Windows.Controls.LayoutSerializationEventArgs)">
            <summary>
            Raises the <see cref="E:ElementLoaded"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementLayoutSaving(Telerik.Windows.Controls.LayoutSerializationSavingEventArgs)">
            <summary>
            Raises the <see cref="E:ElementLayoutSaving"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementSaved(Telerik.Windows.Controls.LayoutSerializationEventArgs)">
            <summary>
            Raises the <see cref="E:ElementSaved"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementLayoutCleaning(Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs)">
            <summary>
            Raises the <see cref="E:ElementLayoutCleaning"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnElementCleaned(Telerik.Windows.Controls.LayoutSerializationEventArgs)">
            <summary>
            Raises the <see cref="E:ElementCleaned"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.OnActivePaneChanged(Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs)">
            <summary>
            Raises the <see cref="E:ActivePaneChanged"/> routed event. This method is called when the ActivePane property is changed.
            </summary>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.Docking.ActivePangeChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.DetachCurrentSaveLoadLayoutHelper">
            <summary>
            Detaches the current save load layout helper.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.AttachDefaultSaveLoadLayoutHelper">
            <summary>
            Attaches the default SaveLoadLayoutHelper.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SaveLayout(System.IO.Stream)">
            <summary>
            Saves the layout.
            </summary>
            <param name="destination">The destination.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SaveLayout(System.IO.Stream,System.Boolean)">
            <summary>
            Saves the layout.
            </summary>
            <param name="destination">The destination.</param>
            <param name="raiseEventsIfNoSerializationTag">If set to <c>true</c> [raise events if no tag].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.LoadLayout(System.IO.Stream)">
            <summary>
            Loads the layout.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.LoadLayout(System.IO.Stream,System.Boolean)">
            <summary>
            Loads the layout.
            </summary>
            <param name="source">The source.</param>
            <param name="raiseEventsIfNoSerializationTag">If set to <c>true</c> [raise events if no tag].</param>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.DockingPanesFactoryProperty">
            <summary>
            Identifies the DockingPanesFactory dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PanesSourceProperty">
            <summary>
            Identifies the PanesSource dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.DockingPanesFactory">
            <summary>
            Gets or sets the current factory for generating items in MVVM scenarios.
            </summary>
            <value>The current factory for generating items.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.PanesSource">
            <summary>
            Gets or sets a collection used to generate the content of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.
            </summary>
            <value>The collection used to generate the content of the <see cref="T:Telerik.Windows.Controls.RadDocking"/> control.</value>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ActivePaneProperty">
            <summary>
            Identifies ActivePane dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.HasDocumentHostProperty">
            <summary> 
            Identifies the HasDocumentHost dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.FloatingSizeProperty">
            <summary> 
            Identifies the FloatingSize dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.FloatingLocationProperty">
            <summary> 
            Identifies the DocumentsProperty dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.FloatingWindowStateProperty">
            <summary> 
            Identifies the FloatingWindowState dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.SerializationTagProperty">
            <summary> 
            Identifies the SerializationTag dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ExcludedFromLayoutSaveProperty">
            <summary> 
            Identifies the ExcludedFromLayoutSave dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.DockStateProperty">
            <summary> 
            Identifies the DocumentsProperty dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.DocumentHostProperty">
            <summary> 
            Identifies the DocumentHost dependency property.
            </summary>s
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ShowResizePreviewProperty">
            <summary>
            Identifies ShowResizePreview dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.RootCompassStyleProperty">
            <summary>
            Identifies the RootCompassStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.CompassStyleProperty">
            <summary>
            Identifies the CompassStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.VisualCueStyleProperty">
            <summary>
            Identifies the VisualCueStyle dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.CurrentSaveLoadLayoutHelperProperty">
            <summary>
            Identifies the CurrentSaveLoadLayoutHelper dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.GeneratedItemsFactoryProperty">
            <summary>
            Identifies the GeneratedItemsFactory dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.IsRestrictedProperty">
            <summary>
            Identifies the IsRestricted dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.RestrictedAreaMarginProperty">
            <summary>
            Identifies the <see>RestrictedAreaMargin</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.IsRestrictedWhenMaximizedProperty">
            <summary>
            Identifies the <see>IsRestrictedWhenMaximized</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.AllowDragReorderProperty">
            <summary>
            Identifies the <see>AllowDragReorder</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.CloseButtonPositionProperty">
            <summary>
            Identifies the <see>CloseButtonPosition</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.DragDropModeProperty">
            <summary>
            Identifies the <see>DragDropMode</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.ConstraintAutoHideAreaProperty">
            <summary>
            Identifies the <see>ConstraintAutoHideArea</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.FlyoutBehaviorProperty">
            <summary>
            Identifies the <see>FlyoutBehavior</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.IsAutoGeneratedProperty">
            <summary> 
            Identifies the <see>IsAutoGenerated</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.PaneActivationModeProperty">
            <summary>
            Identifies the <see>PaneActivationMode</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.RetainPaneSizeModeProperty">
            <summary>
            Identifies the <see>RetainPaneSizeMode</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.FlyoutMinLengthProperty">
            <summary>
            Identifies the <see>FlyoutMinSize</see> Property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.CanAutoHideAreaExceedScreenProperty">
            <summary>
            Identifies the <see>CanAutoHideAreaExceedScreen</see> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.UseNativeInitialToolWindowDragProperty">
            <summary>
            Identifies the <see>UseNativeInitialToolWindowDrag</see> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.LastDocumentPaneGroupProperty">
            <summary> 
            Identifies the LastDocumentPaneGroup dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.DockingLayoutFactoryProperty">
            <summary>
            Identifies the DockingLayoutFactory dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDocking.LastPaneGroupProperty">
            <summary> 
            Identifies the LastPaneGroup dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.ActivePane">
            <summary>
            Gets or sets the active pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.PaneActivationMode">
            <summary>
            Gets or sets a value indicating which pane should be activated when the active pane is closed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.RetainPaneSizeMode">
            <summary>
            Gets or sets a value indicating whether the pane size should be retained.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.CanAutoHideAreaExceedScreen">
            <summary>
            Gets or sets a value indicating whether the panes in the AutoHideArea could go outside of the screen.
            The default value is False - the unpinned panes respect the boundaries of the screen.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.UseNativeInitialToolWindowDrag">
            <summary>
            Gets or sets a value indicating whether the native WPF Window drag should be used when undocking Pane or PaneGroup.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.SplitItems">
            <summary>
            Gets the list of RadPaneGroup and RadSplitContainer objects.
            </summary>
            <value>The panes.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.Panes">
            <summary>
            Gets the list of panes.
            </summary>
            <value>The panes.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.IsLayoutChanging">
            <summary>
            Gets a value indicating whether this instance is layout changing.
            </summary>
            <value>
            	<c>True</c> if this instance is layout changing; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.HasDocumentHost">
            <summary>
            Gets or sets a value indicating whether the RadDocking control has DocumentHost area or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.DocumentHost">
            <summary>
            Gets the content of the document host.
            </summary>
            <value>The content of the document host.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.FlyoutMinLength">
            <summary>
            Gets or sets the minimum size of the flyout element of the auto-hide area. 
            <remarks>The size determines the minimum width or height depending on the auto-hide area position - left/right or top/bottom.</remarks>
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.SplitContainers">
            <summary>
            Gets the list of all split containers.
            </summary>
            <value>The split containers in the docking.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.ShowResizePreview">
            <summary>
            Gets or sets a value indicating whether a preview visual is shown while resizing.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.IsRestricted">
            <summary>
            Gets or sets a value indicating whether the ToolWindows of the Docking control are restricted to the Restricted area of the control.
            </summary>
            <value>
            <c>True</c> if ToolWindows of the Docking control are restricted; otherwise, <c>false</c>.
            </value>
            <remarks>
            Restricts the ToolWindows of the Docking control to its Restricted area and does not allow dragging out of it. The Restricted area is calculated 
            using the RestrictedAreaMargin property and the application area
            (the containing window in desktop application and the browser's content in XBAP).
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.RestrictedAreaMargin">
            <summary>
            Gets or sets a value indicating the margin of the Restricted area from the application area when the IsRestricted property is set to true.
            </summary>
            <remarks>
            The value of this property is used for calculating the Restricted area of the Docking used to restrict the ToolWindows of the
            Docking control when the IsRestricted property is set to true. The value of this property describes the margins of the Restricted area
            from the application area
            (the containing window in desktop application and the browser's content in XBAP).
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.IsRestrictedWhenMaximized">
            <summary>
            Gets or sets whether the ToolWindow will maximize to the full screen size or to the set restricted area margin.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.AllowDragReorder">
            <summary>
            Gets or sets a value indicating whether reordering of RadPanes is allowed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.RootCompassStyle">
            <summary>
            Gets or sets the style for the root Compass control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.CompassStyle">
            <summary>
            Gets or sets the style for the Compass control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.VisualCueStyle">
            <summary>
            Gets or sets the style of the element that will be used as snapping visual cue.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.CurrentSaveLoadLayoutHelper">
            <summary>
            Gets or sets the current save load layout helper.
            </summary>
            <value>The current save load layout helper.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.GeneratedItemsFactory">
            <summary>
            Gets or sets the current factory for generating items.
            </summary>
            <value>The current factory for generating items.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.CloseButtonPosition">
            <summary>
            Gets or sets the position of the CloseButton.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.DragDropMode">
            <summary>
            Gets or sets the drag drop mode of the RadDocking control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.ConstraintAutoHideArea">
            <summary>
            Gets or sets a value indicating whether the RadDocking control should constraint its AutoHideArea to its size or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.FlyoutBehavior">
            <summary>
            Gets or sets the behavior of the flyout of the <see cref="T:Telerik.Windows.Controls.Docking.AutoHideArea"/>.
            </summary>
            <remarks>
            This property could be set to either one of the existing implementations of the <see cref="T:Telerik.Windows.Controls.Docking.IFlyoutBehavior"/> interface 
            (<see cref="T:Telerik.Windows.Controls.Docking.HoverFlyoutBehavior"/>, <see cref="T:Telerik.Windows.Controls.Docking.ClickFlyoutBehavior"/>) or to a  custom implementation.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.AllowUnsafeMode">
            <summary>
            Gets or sets whether unsafe mode is allowed. Don't set this property to true
            if you are not sure you need to do this.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.DockingLayoutFactory">
            <summary>
            Gets or sets the current <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDocking.LogicalChildren">
            <summary>
            Gets an enumerator for the logical child objects of the System.Windows.Controls.ItemsControl object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetFloatingSize(System.Windows.DependencyObject)">
            <summary>
            Gets the value of FloatingSize attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetFloatingSize(System.Windows.DependencyObject,System.Windows.Size)">
            <summary>
            Sets the value of FloatingSize attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetFloatingLocation(System.Windows.DependencyObject)">
            <summary>
            Gets the value of FloatingLocation attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetFloatingLocation(System.Windows.DependencyObject,System.Windows.Point)">
            <summary>
            Sets the value of FloatingLocation attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetFloatingWindowState(System.Windows.DependencyObject)">
            <summary>
            Gets the value of FloatingWindowState attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetFloatingWindowState(System.Windows.DependencyObject,System.Windows.WindowState)">
            <summary>
            Sets the value of FloatingWindowState attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetDockState(System.Windows.DependencyObject)">
            <summary>
            Gets the value of DockState attached property. By design this method should accepts objects of type <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/>
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetDockState(System.Windows.DependencyObject,Telerik.Windows.Controls.Docking.DockState)">
            <summary>
            Sets the value of DockState attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetSerializationTag(System.Windows.DependencyObject)">
            <summary>
            Gets the serialization tag.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetSerializationTag(System.Windows.DependencyObject,System.String)">
            <summary>
            Sets the serialization tag.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetExcludedFromLayoutSave(System.Windows.DependencyObject)">
            <summary>
            Gets whether the element is excluded from the saving and loading logic of RadDocking's of layout.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetExcludedFromLayoutSave(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets whether the element is excluded from the saving and loading logic of RadDocking's of layout.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.GetIsAutoGenerated(System.Windows.DependencyObject)">
            <summary>
            Gets whether the elements is auto-generated from the Docking control or is set by the user as one.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocking.SetIsAutoGenerated(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets whether the element is auto-generated from the Docking control or is set by the user as one.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs">
            <summary>
            The event args used for <see cref="T:Telerik.Windows.Controls.RadDocking"/> layout Serialization cleaning event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs.#ctor(System.Windows.DependencyObject,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationCleaningEventArgs.Cancel">
            <summary>
            Gets or sets a value that determines whether the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> will be removed from the layout.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs">
            <summary>
            The event arguments used for the <see cref="T:Telerik.Windows.Controls.RadDocking"/>'s layout Serialization saving event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs.#ctor(System.Windows.DependencyObject,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs.#ctor(System.Windows.DependencyObject,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.Dictionary`2"/> containing element properties of the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationSavingEventArgs.Cancel">
            <summary>
            Gets or sets a value that determines whether the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> will be serialized.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs">
            <summary>
            The base event args used for <see cref="T:Telerik.Windows.Controls.RadDocking"/> layout Serialization events.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.#ctor(System.Windows.DependencyObject,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElementSerializationTag">
            <summary>
            Gets serialization tag of the affected element.
            </summary>
            <value>The serialization tag of the affected element.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement">
            <summary>
            Gets the affected element.
            </summary>
            <value>The affected element.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.RadDockingCommands">
            <summary>
            Provides access to all available commands in <see cref="T:Telerik.Windows.Controls.RadDocking"/> control and its child controls.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.Close">
            <summary>
            Closes the pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.Pin">
            <summary>
            Pins the pane.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.Floating">
            <summary>
            Make pane floating.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.Dockable">
            <summary>
            Make pane dockable.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.TabbedDocument">
            <summary>
            Tabbed pane in Document host.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.ContextMenuOpen">
            <summary>
            Open the RadPane's context menu.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.PaneHeaderMenuOpen">
            <summary>
            Open the PaneHeader context menu.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadDockingCommands.CommandId.ClosePane">
            <summary>
            Closes the active pane by default. With parameter it can work only for document or only for non document panes.
            For document panes it closes the active document pane or if no such one, closes the selected document pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.ClosePane">
            <summary>
            Gets value that represents the ClosePane RadDocking command.
            </summary>
            <value>The close command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.Close">
            <summary>
            Gets value that represents the close RadPane command.
            </summary>
            <value>The close command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.Pin">
            <summary>
            Gets value that represents the Pin RadPane command.
            </summary>
            <value>The pin command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.Floating">
            <summary>
            Gets value that represents the Floating RadPane command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.Dockable">
            <summary>
            Gets value that represents the Dockable RadPane command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.TabbedDocument">
            <summary>
            Gets value that represents the TabbedDocument RadPane command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.ContextMenuOpen">
            <summary>
            Gets value that represents the ToggleButtonOpen PaneHeader command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadDockingCommands.PaneHeaderMenuOpen">
            <summary>
            Gets value that represents the PaneHeaderMenuOpen command.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadDocumentPane">
            <summary>
            Base class that implements IDocumentPane and can be used as a Document in the RadDocking.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocumentPane.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.RadDocumentPane"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDocumentPane.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadDocumentPane"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadPane">
            <summary>
            A content holder class for the Docking.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsActiveProperty">
            <summary> 
            Identifies the IsActive dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsPinnedProperty">
            <summary> 
            Identifies the IsPinned dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsHiddenProperty">
            <summary> 
            Identifies the IsHidden dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.CanUserPinProperty">
            <summary> 
            Identifies the CanUserPin dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.CanUserCloseProperty">
            <summary> 
            Identifies the CanUserClose dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.MenuCommandsProperty">
            <summary>
            Identifies the MenuCommands dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.CanDockInDocumentHostProperty">
            <summary> 
            Identifies the CanDockInDocumentHost dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.CanFloatProperty">
            <summary> 
            Identifies the CanFloat dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.PaneHeaderVisibilityProperty">
            <summary> 
            Identifies the PaneHeaderVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.TitleTemplateProperty">
            <summary> 
            Identifies the TitleTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.MenuItemTemplateProperty">
            <summary> 
            Identifies the MenuItemTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.MenuItemTemplateSelectorProperty">
            <summary> 
            Identifies the MenuItemTemplateSelector dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.TitleProperty">
            <summary> 
            Identifies the Title dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.AutoHideWidthProperty">
            <summary> 
            Identifies the AutoHideWidth dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.AutoHideHeightProperty">
            <summary> 
            Identifies the AutoHideHeight dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.DocumentHostTemplateProperty">
            <summary>
            Identifies the DocumentHostTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.ContextMenuTemplateProperty">
            <summary>
            Identifies the ContextMenuTemplate dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsDockableProperty">
            <summary>
            Identifies the IsDockable dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsLastInGroupProperty">
            <summary>
            Identifies the IsLastInGroup dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsInDocumentHostProperty">
            <summary>
            Identifies the IsInDocumentHost property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsFloatingProperty">
            <summary>
            Identifies the IsFloating property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsFloatingOnlyProperty">
            <summary>
            Identifies the IsFloatingOnly property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsDockableOptionCheckedProperty">
            <summary>
            Identifies the IsDockableOptionChecked property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsDraggingProperty">
            <summary>
            Identifies the IsDragging dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.IsReorderingProperty">
            <summary>
            Identifies the IsReordering dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.PinButtonVisibilityProperty">
            <summary>
            Identifies the PinButtonVisibility dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.ParentDockingProperty">
            <summary>
            Identifies the CanUserPin ParentDocking property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.UnpinnedIndexProperty">
            <summary>
            Identifies the UnpinnedIndex dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.StateChangeEvent">
            <summary>
            Identifies the StateChange routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.DockingElementDraggedEvent">
            <summary>
            Identifies the DockingElementDragged routed event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPane.DockingElementReorderedEvent">
            <summary>
            Identifies the DockingElementReordered routed event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Telerik.Windows.Controls.RadPane"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadPane"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadPane.Activated">
            <summary>
            Occurs when the <see cref="T:Telerik.Windows.Controls.RadPane"/> is activated.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadPane.Deactivated">
            <summary>
            Occurs when the <see cref="T:Telerik.Windows.Controls.RadPane"/> is deactivated.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsLastInGroup">
            <summary>
            Gets a value indicating whether this instance is first in group.
            </summary>
            <value>
            <c>True</c> if this instance is first in group; otherwise, <c>False</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.DocumentHostTemplate">
            <summary>
            Gets or sets the template that will be applied when the RadPane is placed in a DocumentHost.
            This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsPinned">
            <summary>
            Gets or sets a value indicating whether this instance is pinned.
            </summary>
            <value><c>True</c> if this instance is pinned; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsHidden">
            <summary>
            Gets or sets a value indicating whether this instance is hidden.
            </summary>
            <value><c>True</c> if this instance is hidden; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.CanUserPin">
            <summary>
            Gets or sets a value indicating whether this instance can be pinned by the user.
            </summary>
            <value><c>True</c> if this instance can pin; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.CanUserClose">
            <summary>
            Gets or sets a value indicating whether this instance can be closed by the user.
            </summary>
            <value><c>True</c> if this instance can close; otherwise, <c>False</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.CanFloat">
            <summary>
            Gets or sets a value indicating whether this pane can float.
            </summary>
            <value><c>True</c> if this pane can float; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.PaneHeaderVisibility">
            <summary>
            Gets or sets a value describing the visibility of the header of the pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.CanDockInDocumentHost">
            <summary>
            Gets or sets a value indicating whether this instance can dock in the document host.
            </summary>
            <value>
            	<c>True</c> if this instance can dock in the document host; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.AutoHideWidth">
            <summary>
            Gets or sets the width of the auto hide.
            </summary>
            <value>The width of the auto hide.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.AutoHideHeight">
            <summary>
            Gets or sets the height of the auto hide.
            </summary>
            <value>The height of the auto hide.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.Title">
            <summary>
            Gets or sets the title of the RadPane. This is a dependency property.
            </summary>
            <remarks>
                <para>
                    The title of the RadPane appears in the top header part of the pane, while its header 
                    is the content of its tab button.
                </para>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.TitleTemplate">
            <summary>
            Gets or sets the title template.
            </summary>
            <value>The title template.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsInDocumentHost">
            <summary>
            Gets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.PinButtonVisibility">
            <summary>
            Gets the visibility of the PinButton of this instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.CloseButtonVisibility">
            <summary>
            This property does not apply to RadPane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsActive">
            <summary>
            Gets or sets a value indicating whether this pane is active.
            </summary>
            <value>
            	<c>True</c> if this pane is active; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.Telerik#Windows#Controls#Docking#IDocumentHostAware#IsInDocumentHost">
            <summary>
            Gets or sets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.Telerik#Windows#Controls#Docking#IToolWindowAware#IsInToolWindow">
            <summary>
            Gets or sets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsDragging">
            <summary>
            Gets whether the <see cref="T:Telerik.Windows.Controls.RadPane"/> control has logical focus 
            and mouse capture and the left mouse button is pressed.
            </summary>
            <returns>True if the <see cref="T:Telerik.Windows.Controls.RadPane"/> control has focus 
            and mouse capture; otherwise false. The default value is false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsReordering">
            <summary>
            Gets whether the <see cref="T:Telerik.Windows.Controls.RadPane"/> control is currently reordering.
            </summary>
            <returns>True if the <see cref="T:Telerik.Windows.Controls.RadPane"/> control is reordering 
            otherwise false. The default value is false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsFloating">
            <summary>
            Gets a value indicating whether this pane is floating.
            </summary>
            <value>
            	<c>True</c> if this pane is floating; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsFloatingOnly">
            <summary>
            Gets a value indicating whether this pane is floating and not dockable.
            </summary>
            <value>
            	<c>True</c> if this pane is floating and not dockable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsDockableOptionChecked">
            <summary>
            Gets a value indicating whether this pane is floating and not dockable.
            </summary>
            <value>
            	<c>True</c> if this pane is floating and not dockable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.IsDockable">
            <summary>
            Gets a value indicating whether this pane is dockable.
            </summary>
            <value>
            	<c>True</c> if this pane is dockable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.ContextMenuTemplate">
            <summary>
            Gets or sets Header's context menu that is containing the commands.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.PaneGroup">
            <summary>
            Gets the group that owns the pane.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPane.UnpinnedIndex">
            <summary>
            Gets or sets a value indicating the index of the pane in the parent auto-hide area.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code
            or internal processes (such as a rebuilding layout pass) call System.Windows.Controls.Control.ApplyTemplate().
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.RemoveFromParent">
            <summary>
            Removes from parent.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.MoveToDocumentHost">
            <summary>
            Moves the pane to the document host.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.MakeDockable">
            <summary>
            Makes the pane dockable.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.MakeFloatingDockable">
            <summary>
            Makes the pane floating dockable.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.MakeFloatingOnly">
            <summary>
            Makes the pane floating and not dockable.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnHeaderMouseDown(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when mouse down is registered in the header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual state of the control.
            </summary>
            <param name="useTransitions">Indicates whether transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnStateChange(Telerik.Windows.RadRoutedEventArgs)">
            <summary>
            Raises the <see cref="E:StateChange"/> event.
            </summary>
            <param name="routedEventArgs">The <see cref="T:Telerik.Windows.RadRoutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnMouseEnter(System.Windows.Input.MouseEventArgs)">
            <summary>
            Raises the <see cref="E:MouseEnter"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnMouseLeave(System.Windows.Input.MouseEventArgs)">
            <summary>
            Raises the <see cref="E:MouseLeave"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnHeaderMouseLeftButtonDown(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Raises the <see cref="E:MouseLeftButtonDown"/> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.FindTemplateFromPosition(System.Windows.Controls.Dock)">
            <summary>
            Returns the template that will be used, based on the position of the TabStrip.
            </summary>
            <param name="position">The position for which to return a template.</param>
            <returns>A control template to use.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnContextMenuTemplateChanged">
            <summary>
            Update the RadPanes' context menu.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnDraggingChanged(System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Responds to a change in the value of the <see cref="P:Telerik.Windows.Controls.RadPane.IsDragging" /> property.
            </summary>
            <param name="eventArgs">The event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnDragInitialize(System.Object,Telerik.Windows.DragDrop.DragInitializeEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnDragDropCompleted(System.Object,Telerik.Windows.DragDrop.DragDropCompletedEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drop event with this element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnIsActiveChanged">
            <summary>
            Occurs when <see cref="P:Telerik.Windows.Controls.RadPane.IsActive"/> property is changed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPane.OnIsInDocumentHostChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IsInDocumentHost property changes. Allows the control to change its appearance
            and behavior when placed in a DocumentHost.
            </summary>
            <param name="oldValue">The old value of the property.</param>
            <param name="newValue">The new value of the property.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.RadPaneGroup">
            <summary>
            Navigation control which presents the RadPanes as TabItems.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.DocumentHostTemplateProperty">
            <summary>
            Identifies the DocumentHostTemplate property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.SplitterPositionProperty">
            <summary>
            Identifies the ResizerPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.IsPaneHeaderVisibleProperty">
            <summary>
            Identifies the IsPaneHeaderVisible dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.IsSingleItemProperty">
            <summary>
            Identifies the IsSingleItem dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.IsInDocumentHostProperty">
            <summary>
            Identifies the IsInDocumentHost property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadPaneGroup.flags">
            <summary>
            The flags show the current status of the control - whether something is present and/or running.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.#ctor">
            <summary>
            Initializes a new instance of the RadPaneGroup class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.SplitterPosition">
            <summary>
            Gets the position of the Resizer in the RadPaneGroup template.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.IsPaneHeaderVisible">
            <summary>
            Gets a value indicating whether the PaneHeader control is visible.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.IsSingleItem">
            <summary>
            Gets a value indicating whether the instance has a single item.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.ParentContainer">
            <summary>
            Gets or sets the SplitContainer that holds the item.
            </summary>
            <value>The RadSplitContainer that contains the pane group.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.Control">
            <summary>
            Gets the actual control that will be a visual child of the SplitContainer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.DocumentHostTemplate">
            <summary>
            Gets or sets the template that will be applied to the TabGroup when it is in a DocumentHost. 
            This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.IsInDocumentHost">
            <summary>
            Gets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.Telerik#Windows#Controls#Docking#IDocumentHostAware#IsInDocumentHost">
            <summary>
            Gets or sets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.IsInToolWindow">
            <summary>
            Gets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.UnpinnedPanes">
            <summary>
            Gets all the unpinned panes owned by the PaneGroup.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.Telerik#Windows#Controls#Docking#IToolWindowAware#IsInToolWindow">
            <summary>
            Gets or sets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.Flags">
            <summary>
            Gets the StateFlags of the control.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadPaneGroup.Panes">
            <summary>
            Gets the panes of the group.
            </summary>
            <value>The panes of the group.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.EnumeratePanes">
            <summary>
            Enumerates the panes contained by the split item and its children.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes (such as a rebuilding layout pass)
            call <see cref="M:System.Windows.Controls.Control.ApplyTemplate"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.AddItem(Telerik.Windows.Controls.RadPane,Telerik.Windows.Controls.Docking.DockPosition)">
            <summary>
            Adds the pane to the specified position.
            </summary>
            <param name="pane">The pane to be added.</param>
            <param name="dockPosition">The position at which the pane will be added.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.AddItem(Telerik.Windows.Controls.Docking.ISplitItem,Telerik.Windows.Controls.Docking.DockPosition)">
            <summary>
            Adds the group to the specified position.
            </summary>
            <param name="item">The split item to be added.</param>
            <param name="dockPosition">The position at which the group will be added.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.RemoveFromParent">
            <summary>
            Removes from parent.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.RemovePane(Telerik.Windows.Controls.RadPane)">
            <summary>
            Removes the pane from the group.
            </summary>
            <param name="pane">The pane to be removed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.ShowAllPanes">
            <summary>
            Shows all panes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.HideAllPanes">
            <summary>
            Hides all panes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.PinAllPanes">
            <summary>
            Pins all panes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.UnpinAllPanes">
            <summary>
            Unpins all panes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual states of the control.
            </summary>
            <param name="useTransitions">Identifies whether the transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnCreateAutomationPeer">
            <summary>
            Provides an appropriate <see cref="T:Telerik.Windows.Automation.Peers.RadPaneGroupAutomationPeer"/> implementation for this control, as part of the automation infrastructure.
            </summary>
            <returns>The type-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementation.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnDockingElementReordered(System.Windows.Point,System.Object)">
            <summary>
            Called when a dock item is dragged.
            </summary>
            <param name="globalPosition">The global position of the mouse.</param>
            <param name="reorderedElement">The element that was reordered.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.GetContainerForItemOverride">
            <summary>
            Creates or identifies the element that is used to display the given item.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own container.
            </summary>
            <param name="item">The item to check.</param>
            <returns>
            True if the item is (or is eligible to be) its own container; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.ClearContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            When overridden in a derived class, undoes the effects of the
            <see cref="M:System.Windows.Controls.ItemsControl.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)"/> method.
            </summary>
            <param name="element">The container element.</param>
            <param name="item">The Item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            Prepares the specified element to display the specified item.
            </summary>
            <param name="element">Element used to display the specified item.</param>
            <param name="item">Specified item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.FindTemplateFromPosition(System.Windows.Controls.Dock)">
            <summary>
            Finds the template from position.
            </summary>
            <param name="position">The position.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Invoked when the <see cref="P:System.Windows.Controls.ItemsControl.Items"/> property changes.
            </summary>
            <param name="e">Information about the change.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnIsInToolWindowChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IToolWindowAware property changes. Allows the control to change its appearance
            and behavior when placed in a ToolWindow.
            </summary>
            <param name="oldValue">The old value of the property.</param>
            <param name="newValue">The new value of the property.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnIsInDocumentHostChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IsInDocumentHost property changes. Allows the control to change its appearance
            and behavior when placed in a DocumentHost.
            </summary>
            <param name="oldValue">The old value of the property.</param>
            <param name="newValue">The new value of the property.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnDragOver(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the potential drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnDragLeave(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with this element as the drag origin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnDrop(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drop event with this element as the drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnTabStripDragOver(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drag event with the TabStrip element as the potential drop target.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadPaneGroup.OnTabStripDrop(System.Object,Telerik.Windows.DragDrop.DragEventArgs)">
            <summary>
            Occurs when the input system reports an underlying drop event with the TabStrip element as the drop target.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadPaneGroup.StateFlags">
            <summary>
            An internal class that represents the status of the control.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.LoadingElementArgs">
            <summary>
            Exposes information about the <see cref="T:System.Windows.DependencyObject"/> elements created by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LoadingElementArgs.#ctor(System.Collections.Generic.Dictionary{System.String,System.String},System.Windows.DependencyObject,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LoadingElementArgs"/> class.
            </summary>
            <param name="attributes">Dictionary that contains the read attributes related to the 'element'.</param>
            <param name="element">The <see cref="T:System.Windows.DependencyObject"/> that is being loaded.</param>
            <param name="elementName">The XML tag that triggered the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>'s Load functionality.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.List`1"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LoadingElementArgs.Element"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingElementArgs.Attributes">
            <summary>
            Represents an <see cref="T:System.Collections.Generic.Dictionary`2"/> containing the attributes of the current node of the read XML.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingElementArgs.Element">
            <summary>
            The <see cref="T:System.Windows.DependencyObject"/> which is associated with the creation process of the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingElementArgs.ElementName">
            <summary>
            The <see cref="P:System.Xml.XmlReader.Name"/> of the <see cref="T:System.Xml.XmlReader"/> before the <see cref="T:Telerik.Windows.Controls.LoadingElementArgs"/> were created.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingElementArgs.ElementProperties">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.List`1"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LoadingElementArgs.Element"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.LoadingParentElementArgs">
            <summary>
            Exposes information about the <see cref="T:System.Windows.DependencyObject"/> elements with child elements created by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LoadingParentElementArgs.#ctor(System.Xml.XmlReader,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String},System.Windows.DependencyObject,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LoadingParentElementArgs"/> class.
            </summary>
            <param name="reader">The used <see cref="T:System.Xml.XmlReader"/> <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.</param>
            <param name="xmlRootDepth">The <see cref="P:System.Xml.XmlReader.Depth"/> of the current 'element'.</param>
            <param name="attributes">Dictionary that contains the read attributes related to the 'element'.</param>
            <param name="element">The <see cref="T:System.Windows.DependencyObject"/> that is being loaded.</param>
            <param name="elementName">The XML tag that triggered the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>'s Load functionality.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.List`1"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LoadingElementArgs.Element"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingParentElementArgs.Reader">
            <summary>
            The <see cref="T:System.Xml.XmlReader"/> instance used by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/> for reading the provided XML.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.LoadingParentElementArgs.XmlRootDepth">
            <summary>
            The starting depth of the element in the <see cref="P:Telerik.Windows.Controls.LoadingParentElementArgs.Reader"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDockingLayoutHelper.GetAllAttributes(System.Xml.XmlReader)">
            <summary>
            Uses an <see cref="T:System.Xml.XmlReader"/> and reads all of its attributes.
            </summary>
            <param name="reader">An <see cref="T:System.Xml.XmlReader"/> instance.</param>
            <returns><see cref="T:System.Collections.Generic.Dictionary`2"/> containing all of the <see cref="T:System.Xml.XmlReader"/> attributes.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDockingLayoutHelper.TryUseProperty(System.Action,System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Executes the <paramref name="setProperty"/> only if the <paramref name="propertyName"/> is present in the <paramref name="properties"/>.
            Also removes the <paramref name="propertyName"/> from the <paramref name="properties"/>.
            </summary>
            <param name="setProperty">An action that uses the <paramref name="propertyName"/>.</param>
            <param name="properties">The elements persisted <see cref="T:System.Windows.DependencyProperty"/>s.</param>
            <param name="propertyName">The currently used by the <paramref name="setProperty"/> <see cref="T:System.Windows.DependencyProperty"/> name.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadDockingLayoutHelper.UseProperty(System.Action,System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Executes the <paramref name="setProperty"/> without the need of the <paramref name="propertyName"/> being present in the <paramref name="properties"/>.
            Also removes the <paramref name="propertyName"/> from the <paramref name="properties"/>.
            </summary>
            <param name="setProperty">An action that uses the <paramref name="propertyName"/>.</param>
            <param name="properties">The elements persisted <see cref="T:System.Windows.DependencyProperty"/>s.</param>
            <param name="propertyName">The currently used by the <paramref name="setProperty"/> <see cref="T:System.Windows.DependencyProperty"/> name.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SavingElementArgs">
            <summary>
            Exposes information about the <see cref="T:System.Windows.DependencyObject"/> which is being exported via <see cref="T:System.Xml.XmlWriter"/> by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SavingElementArgs.#ctor(System.Xml.XmlWriter,System.Windows.DependencyObject,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SavingElementArgs" /> class.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter"/> used by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.</param>
            <param name="element">The <see cref="T:System.Windows.DependencyObject"/> that is being loaded.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.List`1"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.SavingElementArgs.ElementProperties"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SavingElementArgs.Writer">
            <summary>
            The <see cref="T:System.Xml.XmlWriter"/> instance used by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/> for exporting the <see cref="T:Telerik.Windows.Controls.RadDocking"/> layout as XML.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SavingElementArgs.Element">
            <summary>
            The <see cref="T:System.Windows.DependencyObject"/> which is exported to XML by the <see cref="T:Telerik.Windows.Controls.Docking.DefaultDockingLayoutFactory"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SavingElementArgs.ElementProperties">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.List`1"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.SavingElementArgs.Element"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.RadSplitContainer">
            <summary>
            Layout control that stacks pane items with splitters in-between. Not to be used outside RadDocking.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSplitContainer.InitialPositionProperty">
            <summary>
            Identifies the InitialPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSplitContainer.OrientationProperty">
            <summary> 
            Identifies the OrientationProperty dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSplitContainer.SplitterPositionProperty">
            <summary>
            Identifies the ResizerPosition property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadSplitContainer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.Orientation">
            <summary>
            Gets or sets the orientation.
            </summary>
            <value>The orientation.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.Control">
            <summary>
            Gets the actual control that will be a visual child of the SplitContainer.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.ParentContainer">
            <summary>
            Gets or sets the SplitContainer that holds the item.
            </summary>
            <value>The RadSplitContainer that contains the split container.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.InitialPosition">
            <summary>
            Gets or sets the initial position.
            </summary>
            <value>The initial position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.SplitterPosition">
            <summary>
            Gets the position of the Resizer in the RadSplitContainer template.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.IsInDocumentHost">
            <summary>
            Gets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.Telerik#Windows#Controls#Docking#IDocumentHostAware#IsInDocumentHost">
            <summary>
            Gets or sets a value indicating whether the object is contained within a DocumentHost.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.IsInToolWindow">
            <summary>
            Gets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.Telerik#Windows#Controls#Docking#IToolWindowAware#IsInToolWindow">
            <summary>
            Gets or sets a value indicating whether the object is contained within a ToolWindow.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSplitContainer.ParentToolWindow">
            <summary>
            Gets or sets the ToolWindow that holds the item.
            </summary>
            <value>The ToolWindow that contains the item.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.EnumeratePanes">
            <summary>
            Enumerates the panes contained by the split item and its children.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code
            or internal processes call System.Windows.FrameworkElement.ApplyTemplate().
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.AddItem(Telerik.Windows.Controls.Docking.ISplitItem,Telerik.Windows.Controls.Docking.DockPosition,Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            Adds the item relative to another item.
            </summary>
            <param name="item">The item to be added.</param>
            <param name="dockPosition">The relative position at which the item will be added.</param>
            <param name="relativeTo">The item, relative to which the new one will be added.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.RemoveChild(Telerik.Windows.Controls.Docking.ISplitItem)">
            <summary>
            Removes the child.
            </summary>
            <param name="item">The item to be removed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.RemoveFromParent">
            <summary>
            Removes from parent.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.ChangeVisualState(System.Boolean)">
            <summary>
            Updates the visual states of the control.
            </summary>
            <param name="useTransitions">Identifies whether the transitions should be used.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnLoaded(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            Called when the <see cref="E:Loaded"/> event is fired.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines whether is the specified item its own container.
            </summary>
            <param name="item">The item.</param>
            <returns>
            	<c>True</c> if the specified item is its own container; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.GetContainerForItemOverride">
            <summary>
            Gets the container for item override.
            </summary>
            <returns>Ready for use container.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.ClearContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            Clears the container for item override.
            </summary>
            <param name="element">The element.</param>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.PrepareContainerForItemOverride(System.Windows.DependencyObject,System.Object)">
            <summary>
            Prepares the container for item override.
            </summary>
            <param name="element">The element.</param>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Raises the <see cref="E:ItemsChanged"/> event.
            </summary>
            <param name="e">The <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event and sets <see cref="P:System.Windows.FrameworkElement.DefaultStyleKey" /> from the active theme.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnIsInDocumentHostChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IsInDocumentHost property changes. Allows the control to change its appearance
            and behavior when placed in a DocumentHost.
            </summary>
            <param name="oldValue">The old value of the property.</param>
            <param name="newValue">The new value of the property.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnIsInToolWindowChanged(System.Boolean,System.Boolean)">
            <summary>
            Called when the IToolWindowAware property changes. Allows the control to change its appearance
            and behavior when placed in a ToolWindow.
            </summary>
            <param name="oldValue">The old value of the property.</param>
            <param name="newValue">The new value of the property.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.OnCreateAutomationPeer">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSplitContainer.GoToState(System.Boolean,System.String[])">
            <summary>
            Attempts to move the control to one of the states  in the list.
            </summary>
            <param name="useTransitions">Indicates whether transitions should be used.</param>
            <param name="stateNames">The names of the states that should be reached.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs">
            <summary>
            The event args are used for layout Serialization Loading event for custom items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs.#ctor(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationCustomLoadingEventArgs.CustomElementTypeName">
            <summary>
            Gets the type name of the custom affected element.
            </summary>
            <value>The type name of the custom affected element.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationEventArgs">
            <summary>
            Defines event arguments used for <see cref="T:Telerik.Windows.Controls.RadDocking"/> layout Serialization events.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationEventArgs.#ctor(System.Windows.DependencyObject,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationEventArgs.#ctor(System.Windows.DependencyObject,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationEventArgs"/> class.
            </summary>
            <param name="affectedElement">The affected element.</param>
            <param name="affectedElementSerializationTag">The serialization tag of the affected element.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.Dictionary`2"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationEventArgs.ElementProperties">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.Dictionary`2"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> that will be saved to the layout Xml of the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs">
            <summary>
            The event arguments used for the <see cref="T:Telerik.Windows.Controls.RadDocking"/>'s layout Serialization Loading event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs"/> class.
            </summary>
            <param name="affectedElementSerializationTag">The affected element serialization tag.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs.#ctor(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs"/> class.
            </summary>
            <param name="affectedElementSerializationTag">The affected element serialization tag.</param>
            <param name="elementProperties">A <see cref="T:System.Collections.Generic.Dictionary`2"/> containing the string representations of the <see cref="T:System.Windows.DependencyProperty"/>s of the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> that will be loaded from the layout Xml in the <see cref="T:Telerik.Windows.Controls.RadDocking"/>.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs.Cancel">
            <summary>
            Gets or sets a value that determines whether the <see cref="P:Telerik.Windows.Controls.LayoutSerializationBaseEventArgs.AffectedElement"/> will be loaded.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.LayoutSerializationLoadingEventArgs.SetAffectedElement(System.Windows.DependencyObject)">
            <summary>
            Sets the affected element.
            </summary>
            <param name="newElement">The new element be set.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.DockingPanel">
            <summary>
            The docking panel is used as part of the RadDocking control to arrange elements.
            </summary>
            <remarks>
                <para>
                    This a class for internal use and is not meant to be used outside the RadDocking.
                </para>
            </remarks>
        </member>
        <member name="F:Telerik.Windows.Controls.DockingPanel.InitialSizeProperty">
            <summary>
            Identifies the InitialSize attached dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingPanel.GetInitialSize(System.Windows.DependencyObject)">
            <summary>
            Gets the initial size for object.
            </summary>
            <param name="obj">The object.</param>
            <returns>The initial size.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingPanel.SetInitialSize(System.Windows.DependencyObject,System.Windows.Size)">
            <summary>
            Sets the initial size for object.
            </summary>
            <param name="obj">The object.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingPanel.CreateUIElementCollection(System.Windows.FrameworkElement)">
            <summary>
            Creates a new System.Windows.Controls.UIElementCollection.
            </summary>
            <param name="logicalParent">The logical parent element of the collection to be created.</param>
            <returns>An ordered collection of elements that have the specified logical parent.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.DockingPanel.MeasureOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the "measure" pass of Silverlight layout. Classes can override this method to define their own measure pass behavior.
            </summary>
            <param name="availableSize">The available size that this element can give to child elements. Infinity can be specified as a value to indicate that the element will size to whatever content is available.</param>
            <returns>
            The size that this element determines it needs during layout, based on its calculations of child element sizes.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.ProportionalStackPanel">
            <summary>
            Panel that arrange its children as a StackPanel if no DesiredWidth or DesiredHeight
            is set or if set as a Grid with Row/Column Definitions.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.OrientationProperty">
            <summary>
            Identifies the Orientation dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.SuppressMeasureProperty">
            <summary>
            Identifies the SuppressMeasure attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.RelativeSizeProperty">
            <summary>
            Identifies the RelativeSize attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementMinWidthProperty">
            <summary>
            Identifies the ElementMinWidth attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementWidthProperty">
            <summary>
            Identifies the ElementWidth attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementMaxWidthProperty">
            <summary>
            Identifies the ElementMaxWidth attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementMinHeightProperty">
            <summary>
            Identifies the ElementMinHeight attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementHeightProperty">
            <summary>
            Identifies the ElementHeight attached property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ProportionalStackPanel.ElementMaxHeightProperty">
            <summary>
            Identifies the ElementMaxHeight attached property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.ProportionalStackPanel.Orientation">
            <summary>
            Gets or sets a value that specifies the dimension in which child 
            content is arranged. This is a dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetRelativeSize(System.Windows.DependencyObject)">
            <summary>
            Gets the Relative size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetRelativeSize(System.Windows.DependencyObject,System.Windows.Size)">
            <summary>
            Sets the Relative size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetSuppressMeasure(System.Windows.DependencyObject)">
            <summary>
            Gets the SuppressMeasure attached property.
            If true changing RelativeSize will not invalidate measure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetSuppressMeasure(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Sets the SuppressMeasure attached property.
            When set to true changing RelativeSize will not invalidate measure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementMinWidth(System.Windows.DependencyObject)">
            <summary>
            Gets the MinWidth of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementMinWidth(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the MinWidth of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementWidth(System.Windows.DependencyObject)">
            <summary>
            Gets the Width of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementWidth(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the Width of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementMaxWidth(System.Windows.DependencyObject)">
            <summary>
            Gets the MaxWidth of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementMaxWidth(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the MaxWidth of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementMinHeight(System.Windows.DependencyObject)">
            <summary>
            Gets the MinHeight of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementMinHeight(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the MinHeight of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementHeight(System.Windows.DependencyObject)">
            <summary>
            Gets the Height of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementHeight(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the Height of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.GetElementMaxHeight(System.Windows.DependencyObject)">
            <summary>
            Gets the MaxHeight of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.SetElementMaxHeight(System.Windows.DependencyObject,System.Double)">
            <summary>
            Sets the MaxHeight of the child element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.MeasureOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the measure pass of the layout. Classes can override this method to define their own measure pass behavior.
            </summary>
            <param name="availableSize">The available size that this object can give to child objects. Infinity can be specified as a value to indicate that the object will size to whatever content is available.</param>
            <returns>
            The size that this object determines it needs during layout, based on its calculations of child object allotted sizes.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ProportionalStackPanel.ArrangeOverride(System.Windows.Size)">
            <summary>
            Provides the behavior for the Arrange pass of layout. Classes can override this method to define their own arrange pass behavior.
            </summary>
            <param name="finalSize">The final area within the parent that this object should use to arrange itself and its children.</param>
            <returns>The actual size used.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.themes.Resource">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.themes.Resource.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.themes.Resource.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.themes.Resource.This_is_empty_resource_file_to_workaround">
            <summary>
              Looks up a localized string similar to a first chance exception with implicit styles..
            </summary>
        </member>
    </members>
</doc>
