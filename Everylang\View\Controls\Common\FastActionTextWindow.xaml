﻿<Popup x:Class="Everylang.App.View.Controls.Common.FastActionTextWindow"
                      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                      xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
                      xmlns:helpers1="clr-namespace:Everylang.App.View.Helpers"
                      x:Name="me" PopupAnimation="Slide"
                      MinHeight="270" MinWidth="400" Height="270" Width="400" MaxHeight="600" MaxWidth="800" Placement="Mouse" 
                      StaysOpen="True" MouseLeftButtonDown="Border_PreviewMouseLeftButtonDown" Focusable="false"
                      x:ClassModifier="internal">
    <Popup.Resources>
        <ResourceDictionary>
            <helpers1:IndexConverterForFastAction x:Key="IndexConverterMu"></helpers1:IndexConverterForFastAction>

            <Style x:Key="ImageButtonStyle" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="IsBackgroundVisible" Value="False"/>
                <Setter Property="Focusable" Value="False"/>
            </Style>
            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonSettings" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="CogOutline" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonListSnippets" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="FormatListBulleted" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonTop" TargetType="telerik:RadButton" BasedOn="{StaticResource ImageButtonStyle}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="PinOff" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="Pin" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <RoutedUICommand x:Key="CommandCopySelectedText"/>
        </ResourceDictionary>

    </Popup.Resources>

    <Popup.CommandBindings>
        <CommandBinding Command="{StaticResource CommandCopySelectedText}" Executed="CopySelectedText" CanExecute="CommandBindingCopy_OnCanExecute" />
    </Popup.CommandBindings>

    <Border BorderThickness="1" BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}">
        <Grid x:Name="GridMy"  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="50*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
            <Thumb x:Name="ThumbMy" Opacity="0"/>
            <Border Name="Border" Grid.Row="0" Grid.Column="0"/>
            <telerik:Label Grid.Column="0"  Grid.Row="0"  Margin="10,0,0,0" Content="{Binding Path=TitleText, ElementName=me, Mode=Default}" FontSize="14" VerticalAlignment="Center" />
            <StackPanel Grid.Column="0" Grid.Row="0" Orientation="Horizontal" FlowDirection="RightToLeft" VerticalAlignment="Center">
                <telerik:RadButton Style="{StaticResource ImageButtonClose}" Click="ButtonClickClose" ToolTip="{telerik:LocalizableResource Key=CloseHeaderButton}" IsCancel="True" />
                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonTop}" ToolTip="{telerik:LocalizableResource Key=StayOnTopButton}" Click="ClickSetStayOnTop"/>
                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonSettings}" ToolTip="{telerik:LocalizableResource Key=Settings}" Click="ClickOpenSettings"/>
                <telerik:RadButton Margin="5,0,0,0" Style="{StaticResource ImageButtonListSnippets}" ToolTip="{telerik:LocalizableResource Key=ListOnMainWindow}" Click="ClickOpenList"/>
            </StackPanel>
            <Grid Grid.Column="0" Grid.Row="1" x:Name="GridPassword" VerticalAlignment="Stretch" HorizontalAlignment="Stretch">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                    <telerik:RadPasswordBox x:Name="PasswordBoxMu" Width="250" WatermarkContent="{telerik:LocalizableResource Key=DiaryPassword}" PreviewKeyUp="DiaryPasswordBoxMu_OnPreviewKeyUp"/>
                    <telerik:RadButton Margin="10,0,0,0" Content="OK" Width="40" Click="DiaryButtonPasswordOk_OnClick"></telerik:RadButton>
                </StackPanel>
            </Grid>
            <telerik:RadListBox Grid.Column="0" Grid.Row="1" Name="lvFastAction" BorderThickness="0"
                                ScrollViewer.HorizontalScrollBarVisibility="Disabled" SelectionMode="Extended"                
                                PreviewMouseDoubleClick="LvFastAction_OnPreviewMouseDoubleClick" IsScrollIntoViewEnabled="True">
                <telerik:RadContextMenu.ContextMenu>
                    <telerik:RadContextMenu>
                        <telerik:RadMenuItem  Command="{StaticResource CommandCopySelectedText}" Header="{telerik:LocalizableResource Key=bCopy}" PreviewMouseLeftButtonDown="ListBoxItem_MouseLeftButtonDown"/>
                </telerik:RadContextMenu>
                </telerik:RadContextMenu.ContextMenu>
                <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel>
                            <Grid Margin="0" Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="15" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="40" />
                                </Grid.ColumnDefinitions>
                                <telerik:Label Content="{Binding Index, Converter={StaticResource IndexConverterMu}}" FontSize="10"
                                                  VerticalAlignment="Center"  HorizontalAlignment="Left"
                                                  ToolTip="{Binding Path=FastActionIndex, ElementName=me, Mode=Default}" Visibility="{Binding Path=IsNotFindNow, ElementName=me, Converter={StaticResource BoolToVis}}" />
                                <helpers1:TextBlockWithSelection Grid.Column="1"  FontSize="12" Text="{Binding Obj.ShortText}" HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                                TextSelected ="TextBoxBase_OnSelectionChanged" Cursor="IBeam"
                                                                Background="Transparent" TextWrapping="Wrap">
                                    <helpers1:TextBlockWithSelection.ToolTip>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Obj.TextPrev}" />
                                            <Image Margin="0,0,0,2" Source="{Binding Obj.ImagePrev}" />
                                        </StackPanel>
                                    </helpers1:TextBlockWithSelection.ToolTip>
                                </helpers1:TextBlockWithSelection>
                                <telerik:RadButton Grid.Column="2"  Cursor="Hand" x:Name="ButtonMenu" Margin="0,0,10,0" >
                                    <telerik:RadButton.Content>
                                        <wpf:MaterialIcon Width="20" Kind="DotsVertical" />
                                    </telerik:RadButton.Content>
                                    <telerik:RadButton.Style>
                                        <Style BasedOn="{StaticResource ImageButtonStyle}" TargetType="telerik:RadButton">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}},Path=IsMouseOver}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}},Path=IsMouseOver}" Value="False">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </telerik:RadButton.Style>


                                </telerik:RadButton>
                            </Grid>
                        <Separator Opacity="0.3" Padding="0" Margin="0"/>
                        </StackPanel>
                        
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
                <telerik:RadListBox.ItemContainerStyle>
                    <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                    <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListBoxItem_MouseLeftButtonDown" />
                    <Setter Property="Padding" Value="0"></Setter>
                    <Setter Property="Margin" Value="0"></Setter>
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
                </telerik:RadListBox>
        <Border Grid.Row="2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="15" />
                </Grid.ColumnDefinitions>
                <!--<Thumb HorizontalAlignment="Stretch" Grid.Column="0" Cursor="SizeNS" Opacity="0.01" Height="8"
                       VerticalAlignment="Bottom"
                       DragDelta="onDragDeltaVertical" />-->
                    <telerik:RadWatermarkTextBox Grid.Column="0" Background="Transparent" x:Name="TextBoxSearch" HorizontalAlignment="Stretch" BorderThickness="0"
                                               WatermarkContent="{telerik:LocalizableResource Key=FastActionTextWindowSearch}" 
                                               TextChanged="TextBoxSearch_OnTextChanged"/>
                    <ResizeGrip x:Name="WindowResizeGrip" Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            IsTabStop="False"
                            UseLayoutRounding="True"
                            Visibility="Visible" />
                <Thumb HorizontalAlignment="Stretch" Grid.Column="1" Cursor="SizeNWSE"
                       DragDelta="OnDragDeltaAll" Opacity="0.01" />
            </Grid>
        </Border>
        <Border Grid.Row="1">
                <!--<Thumb VerticalAlignment="Stretch" HorizontalAlignment="Stretch" Cursor="SizeWE" Opacity="0.01"
                       DragDelta="onDragDeltaHorisontal" />-->
        </Border>

    </Grid>
    </Border>
</Popup>
