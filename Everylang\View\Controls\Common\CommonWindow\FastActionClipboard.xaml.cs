﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using Everylang.App.ViewModels;
using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using Telerik.Windows;
using Telerik.Windows.Controls;
using Image = System.Drawing.Image;
using PlacementMode = System.Windows.Controls.Primitives.PlacementMode;
using TextDataFormat = System.Windows.Forms.TextDataFormat;

namespace Everylang.App.View.Controls.Common.CommonWindow
{
    /// <summary>
    /// Interaction logic for FastActionClipboard.xaml
    /// </summary>
    internal partial class FastActionClipboard : IFastActionComponent
    {
        private readonly ICollectionView _collectionView;
        private string? _selectedText;
        private int _currentSelected;
        private bool _isClipboardFavorite;

        internal FastActionClipboard()
        {
            InitializeComponent();
            LvFastAction.ItemsSource = VMContainer.Instance.ClipboardViewModel.AllClipboardItems;
            _collectionView = CollectionViewSource.GetDefaultView(VMContainer.Instance.ClipboardViewModel.AllClipboardItems);
            VMContainer.Instance.ClipboardViewModel.AllClipboardItems.CollectionChanged += AllClipboardItemsOnCollectionChanged;
        }

        private void AllClipboardItemsOnCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            LvFastAction.Items.Refresh();
            if (LvFastAction.Items.Count > 0) LvFastAction.SelectedItem = LvFastAction.Items[0];
            LvFastAction.UpdateLayout();

        }

        private void LvFastAction_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            Replace();
        }

        private void TextBlockSelectionChanged(string selectedtext)
        {
            _selectedText = selectedtext;
        }

        public void Replace()
        {
            if (LvFastAction.SelectedItems != null && LvFastAction.SelectedItems.Count > 0)
            {
                if (LvFastAction.SelectedItems[0] is ClipboardDataModel clipboardDataModel && clipboardDataModel.IsImage)
                {
                    var stateManager = new ClipboardStateManager();
                    if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
                    {
                        stateManager.Initialize();
                    }
                    var imStream = clipboardDataModel.GetImage();
                    if (imStream != null)
                    {
                        using Image img = Image.FromStream(imStream);
                        System.Windows.Forms.Clipboard.SetImage(img);
                    }
                    FastActionCommonWindow.Instance?.Hide();
                    ClipboardOperations.SendPasteText();
                    stateManager.Dispose();
                    return;
                }
                string replacedText = "";
                for (int i = 0; i < LvFastAction.SelectedItems.Count; i++)
                {
                    replacedText += (LvFastAction.SelectedItems[i] as ClipboardDataModel)?.Text;
                    if (i != LvFastAction.SelectedItems.Count - 1)
                    {
                        replacedText += Environment.NewLine;
                    }
                }
                FastActionCommonWindow.Instance?.Hide();
                if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
                {
                    Utilities.SendText.SendStringByPaste(replacedText, false);
                }
                else
                {
                    Utilities.SendText.SendStringByPaste(replacedText, false, true);
                }
            }
        }
        public void Replace(int i)
        {
            ClipboardDataModel clipboardDataModel = VMContainer.Instance.ClipboardViewModel.AllClipboardItems[i];
            if (clipboardDataModel.IsImage)
            {
                var stateManager = new ClipboardStateManager();
                if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
                {
                    stateManager.Initialize();
                }
                var imStream = clipboardDataModel.GetImage();
                if (imStream != null)
                {
                    using Image img = Image.FromStream(imStream);
                    System.Windows.Forms.Clipboard.SetImage(img);
                }
                FastActionCommonWindow.Instance?.Hide();
                ClipboardOperations.SendPasteText();
                stateManager.Dispose();
                return;
            }
            FastActionCommonWindow.Instance?.Hide();
            if (SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard)
            {
                Utilities.SendText.SendStringByPaste(clipboardDataModel.Text, false);
            }
            else
            {
                Utilities.SendText.SendStringByPaste(clipboardDataModel.Text, false, true);
            }
        }

        public void Copy()
        {
            if (!string.IsNullOrEmpty(_selectedText))
            {
                ClipboardOperations.SetTextWithoutHistory(_selectedText);
            }
            else
            {
                if (LvFastAction.SelectedItems != null && LvFastAction.SelectedItems.Count > 0)
                {
                    if (LvFastAction.SelectedItems[0] is ClipboardDataModel clipboardDataModel && clipboardDataModel.IsImage)
                    {
                        using var imStream = clipboardDataModel.GetImage();
                        if (imStream != null)
                        {
                            using Image img = Image.FromStream(imStream);
                            System.Windows.Forms.Clipboard.SetImage(img);
                        }
                        return;
                    }
                    string replacedText = "";
                    for (int i = 0; i < LvFastAction.SelectedItems.Count; i++)
                    {
                        replacedText += (LvFastAction.SelectedItems[i] as ClipboardDataModel)?.Text;
                        if (i != LvFastAction.SelectedItems.Count - 1)
                        {
                            replacedText += Environment.NewLine;
                        }
                    }
                    ClipboardOperations.SetTextWithoutHistory(replacedText);
                }
            }
        }

        public void SelectAll()
        {
        }

        public void Delete()
        {
            if (LvFastAction.SelectedItems != null && LvFastAction.SelectedItems.Count > 0)
            {
                VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
                for (int i = 0; i < LvFastAction.SelectedItems.Count; i++)
                {
                    if (LvFastAction.SelectedItems[i] is ClipboardDataModel model) VMContainer.Instance.ClipboardViewModel.SelectedItems.Add(model);
                }
                VMContainer.Instance.ClipboardViewModel.DeleteSelected(null);
            }
        }

        public void Init()
        {
            LvFastAction.Focus();
            LvFastAction.SelectedIndex = 0;
            _currentSelected = 0;
        }

        public void Close()
        {
            LvFastAction.ItemsSource = null;
            _collectionView.Filter = null;
        }

        public void FindText(string text)
        {
            _collectionView.Filter = null;

            _collectionView.Filter = item =>
            {
                var s = (item as ClipboardDataModel)?.Text;
                return s != null && s.ToLower().Contains(text.ToLower());
            };
            if (LvFastAction.Items.Count > 0)
            {
                LvFastAction.SelectedItem = LvFastAction.Items[0];
            }
            _currentSelected = 0;
        }

        public void SelectNext(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == LvFastAction.Items.Count - 1)
            {
                _currentSelected = 0;
            }
            else
            {
                _currentSelected += 1;
            }
            if (shift)
            {
                var clipItem = LvFastAction.Items[_currentSelected];
                if (!LvFastAction.SelectedItems.Contains(clipItem))
                {
                    LvFastAction.SelectedItems.Add(clipItem);
                    LvFastAction.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        LvFastAction.SelectedItems.Remove(LvFastAction.Items[oldSelIndex]);
                        LvFastAction.ScrollIntoView(LvFastAction.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                LvFastAction.SelectedIndex = _currentSelected;
                LvFastAction.ScrollIntoView(_currentSelected);
            }
        }

        public void SelectPrev(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == 0)
            {
                _currentSelected = LvFastAction.Items.Count - 1;
            }
            else
            {
                _currentSelected -= 1;
            }
            if (shift)
            {
                var clipItem = LvFastAction.Items[_currentSelected];
                if (!LvFastAction.SelectedItems.Contains(clipItem))
                {
                    LvFastAction.SelectedItems.Add(clipItem);
                    LvFastAction.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        LvFastAction.SelectedItems.Remove(LvFastAction.Items[oldSelIndex]);
                        LvFastAction.ScrollIntoView(LvFastAction.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                LvFastAction.SelectedIndex = _currentSelected;
                LvFastAction.ScrollIntoView(_currentSelected);
            }
        }

        private RadContextMenu? _contextMenu;

        private void OpenMenuMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (LvFastAction.SelectedItem == null)
            {
                return;
            }
            _contextMenu = new RadContextMenu();
            _contextMenu.Closed += (_, _) => { FastActionCommonWindow.Instance!.MenuIsOpen = false; };

            RadMenuItem itemPastButton = new RadMenuItem();
            itemPastButton.Header = LocalizationManager.GetString("PastButton");
            itemPastButton.PreviewMouseDown += RadMenuItemPaste;
            _contextMenu.Items.Add(itemPastButton);

            RadMenuItem itemPasteBySendKeys = new RadMenuItem();
            itemPasteBySendKeys.Header = LocalizationManager.GetString("PasteButtonWithoutClipboard");
            itemPasteBySendKeys.PreviewMouseDown += RadMenuItemPasteBySendKeys;
            _contextMenu.Items.Add(itemPasteBySendKeys);

            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("CopyButton");
            itemCopy.Click += RadMenuItemCopyClick;
            _contextMenu.Items.Add(itemCopy);
            ClipboardDataModel clipboardDataModel = (ClipboardDataModel)LvFastAction.SelectedItem;
            if (clipboardDataModel.IsImage)
            {
                _contextMenu.Placement = PlacementMode.Mouse;
                _contextMenu.IsOpen = true;
                return;
            }
            if (clipboardDataModel.IsHtml)
            {
                RadMenuItem itemCopyHtml = new RadMenuItem();
                itemCopyHtml.Header = LocalizationManager.GetString("CopyButtonHtml");
                itemCopyHtml.PreviewMouseDown += RadMenuItemCopyHtmlClick;
                _contextMenu.Items.Add(itemCopyHtml);
            }

            if (clipboardDataModel.IsRtf)
            {
                RadMenuItem itemCopyRtf = new RadMenuItem();
                itemCopyRtf.Header = LocalizationManager.GetString("CopyButtonRtf");
                itemCopyRtf.PreviewMouseDown += RadMenuItemCopyRtfClick;
                _contextMenu.Items.Add(itemCopyRtf);
            }

            RadMenuItem itemBreakInter = new RadMenuItem();
            itemBreakInter.Header = LocalizationManager.GetString("BreakInterButton");
            itemBreakInter.PreviewMouseDown += RadMenuItemBreakInterClick;
            _contextMenu.Items.Add(itemBreakInter);

            RadMenuItem itemBreakSpace = new RadMenuItem();
            itemBreakSpace.Header = LocalizationManager.GetString("BreakSpaceButton");
            itemBreakSpace.PreviewMouseDown += RadMenuItemBreakSpaceClick;
            _contextMenu.Items.Add(itemBreakSpace);

            RadMenuItem itemCopyToSnippets = new RadMenuItem();
            itemCopyToSnippets.Header = LocalizationManager.GetString("ToReplacerButton");
            itemCopyToSnippets.PreviewMouseDown += RadMenuItemToSnippets;
            _contextMenu.Items.Add(itemCopyToSnippets);

            if (clipboardDataModel.IsFavorite)
            {
                RadMenuItem itemRemoveFavorite = new RadMenuItem();
                itemRemoveFavorite.Header = LocalizationManager.GetString("RemoveFavorite");
                itemRemoveFavorite.Click += MenuItemRemoveFavoriteClick;
                _contextMenu.Items.Add(itemRemoveFavorite);
            }
            else
            {
                RadMenuItem itemAddFavorite = new RadMenuItem();
                itemAddFavorite.Header = LocalizationManager.GetString("AddFavorite");
                itemAddFavorite.Click += MenuItemitemAddFavoriteClick;
                _contextMenu.Items.Add(itemAddFavorite);
            }



            _contextMenu.PlacementTarget = (UIElement)sender;
            FastActionCommonWindow.Instance!.MenuIsOpen = true;
            _contextMenu.IsOpen = true;
        }

        private void RadMenuItemPasteBySendKeys(object sender, MouseButtonEventArgs e)
        {
            Utilities.SendText.SendStringByTextEntry((LvFastAction.SelectedItems[0] as ClipboardDataModel)?.Text, false);
            FastActionCommonWindow.Instance!.Hide();
        }

        private void RadMenuItemBreakSpaceClick(object sender, MouseButtonEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)LvFastAction.SelectedItem);
            VMContainer.Instance.ClipboardViewModel.BreakSpace();
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
        }

        private void RadMenuItemBreakInterClick(object sender, MouseButtonEventArgs e)
        {
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Add((ClipboardDataModel)LvFastAction.SelectedItem);
            VMContainer.Instance.ClipboardViewModel.BreakInter();
            VMContainer.Instance.ClipboardViewModel.SelectedItems.Clear();
        }

        private void RadMenuItemPaste(object sender, MouseButtonEventArgs mouseButtonEventArgs)
        {
            Replace();
        }

        private void MenuItemitemAddFavoriteClick(object sender, RadRoutedEventArgs e)
        {
            if (LvFastAction.SelectedItem is ClipboardDataModel model)
            {
                model.IsFavorite = true;
                ClipboadManager.SaveData(model);
                FavoriteCheck(_isClipboardFavorite);
            }

        }

        private void MenuItemRemoveFavoriteClick(object sender, RadRoutedEventArgs e)
        {
            if (LvFastAction.SelectedItem is ClipboardDataModel model)
            {
                model.IsFavorite = false;
                ClipboadManager.SaveData(model);
                FavoriteCheck(_isClipboardFavorite);
            }
        }

        private void RadMenuItemToSnippets(object sender, MouseButtonEventArgs e)
        {
            if (LvFastAction.SelectedItem == null) return;
            var text = ((ClipboardDataModel)(LvFastAction.SelectedItem)).Text;
            FastActionCommonWindow.Instance!.Hide();
            if (text != null) GlobalEventsApp.OnEventAddNewSnippets(text);
        }

        private void RadMenuItemCopyRtfClick(object sender, MouseButtonEventArgs e)
        {
            var textToCopy = (ClipboardDataModel)LvFastAction.SelectedItem;
            ClipboardMonitorWorker.IgnoreLast = true;
            System.Windows.Forms.DataObject dataObject = new System.Windows.Forms.DataObject();
            dataObject.SetData("UnicodeText", textToCopy.Text);
            dataObject.SetData("Rich Text Format", textToCopy.Rtf);
            System.Windows.Forms.Clipboard.SetDataObject(dataObject);
        }

        private void RadMenuItemCopyHtmlClick(object sender, MouseButtonEventArgs e)
        {
            var textToCopy = (ClipboardDataModel)LvFastAction.SelectedItem;
            ClipboardMonitorWorker.IgnoreLast = true;
            if (textToCopy.Html != null) System.Windows.Forms.Clipboard.SetText(textToCopy.Html, TextDataFormat.Html);
        }

        private void RadMenuItemCopyClick(object sender, RadRoutedEventArgs radRoutedEventArgs)
        {
            Copy();
        }

        public void FavoriteCheck(bool isClipboardFavorite)
        {
            _isClipboardFavorite = isClipboardFavorite;
            if (isClipboardFavorite)
            {
                LvFastAction.ItemsSource = VMContainer.Instance.ClipboardViewModel.AllClipboardItems.Where(x => x.IsFavorite).ToList();
            }
            else
            {
                LvFastAction.ItemsSource = VMContainer.Instance.ClipboardViewModel.AllClipboardItems;
            }
            LvFastAction.Items.Refresh();
        }
    }
}
