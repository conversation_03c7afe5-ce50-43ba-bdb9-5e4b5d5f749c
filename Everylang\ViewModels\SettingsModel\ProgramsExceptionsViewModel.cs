﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.Utilities;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class ProgramsExceptionsViewModel : ViewModelBase
    {
        public RadObservableCollection<ProgramsExceptionsDataModel?> ProgramsExceptionsList { get; set; }

        public RadObservableCollection<ProgramInfo> ProgramsList { get; set; }

        public DelegateCommand AddNewCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewTitleCommand
        {
            get;
            private set;
        }

        public DelegateCommand DeleteSelectionCommand
        {
            get;
            private set;
        }

        public DelegateCommand EditSelectionCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewFromListCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewExeFileCommand
        {
            get;
            private set;
        }

        public DelegateCommand AddNewFilesFromFolderCommand
        {
            get;
            private set;
        }


        //readonly SystemCursor _systemCursor;
        private bool _addTitle;

        public ProgramsExceptionsViewModel()
        {
            ProgramsExceptionsList = new RadObservableCollection<ProgramsExceptionsDataModel?>();
            ProgramsList = new RadObservableCollection<ProgramInfo>();
            //_systemCursor = new SystemCursor();
            AddNewNotStarted = true;
            AddNewFromListCommand = new DelegateCommand(AddNewFromList);
            AddNewExeFileCommand = new DelegateCommand(AddNewExeFile);
            AddNewFilesFromFolderCommand = new DelegateCommand(AddNewFilesFromFolder);
            AddNewCommand = new DelegateCommand(AddNew);
            AddNewTitleCommand = new DelegateCommand(AddNewTitle);
            DeleteSelectionCommand = new DelegateCommand(DeleteSelection);
            EditSelectionCommand = new DelegateCommand(EditSelection);
            HookCallBackMouseUp.CallbackEventHandler += AddNewProgramByMouseClickEventHandler;
            GetAllDataFromDb();
        }

        private void AddNewFilesFromFolder(object o)
        {
            var dlg = new FolderBrowserDialog();
            dlg.ShowNewFolderButton = false;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                var exeFiles = ProgramsShellUtils.GetDirectoryFiles(dlg.SelectedPath, "*.exe", SearchOption.AllDirectories).ToList();
                foreach (var exeFile in exeFiles)
                {
                    AddProgramCommon(exeFile);
                }
            }
        }

        private void AddNewExeFile(object o)
        {
            var dlg = new OpenFileDialog();
            dlg.DefaultExt = ".exe"; // Default file extension
            dlg.Filter = @"Executable file (.exe)|*.exe";
            if (dlg.ShowDialog() == DialogResult.OK)
                AddProgramCommon(dlg.FileName);
        }

        private void AddNewFromList(object o)
        {
            AddProgramCommon(ProgramsListCurrent?.Name);
        }

        internal async Task UpdateStartedPrograms()
        {
            var allStartedProgramsAsync = await ProgramsShellUtils.GetAllStartedProgramsAsync();
            var programInfos = allStartedProgramsAsync.Where(programInfo => ProgramsExceptionsList.All(x => x?.Program != programInfo.Name)).ToList();
            ProgramsList.Clear();
            ProgramsList.AddRange(programInfos);
        }


        private void DeleteSelection(object o)
        {
            RemoveProgramFromDb(SelectedProgram);
            ProgramsExceptionsList.Remove(SelectedProgram);
            base.OnPropertyChanged(nameof(IsSelected));
            base.OnPropertyChanged(nameof(SelectedProgram));
        }

        private void EditSelection(object o)
        {
            string? text = SelectedProgram?.Program;
            bool isTitle = false;
            if (SelectedProgram?.Title != "")
            {
                text = SelectedProgram?.Title;
                isTitle = true;
            }
            RadWindow.Prompt(LocalizationManager.GetString("Edit"), (_, args) =>
            {
                if (args.DialogResult != null && !string.IsNullOrEmpty(args.PromptResult))
                {
                    if (isTitle)
                    {
                        if (SelectedProgram != null) SelectedProgram.Title = args.PromptResult;
                    }
                    else
                    {
                        if (SelectedProgram != null) SelectedProgram.Program = args.PromptResult;
                    }
                }
            }, text);
            UpdateSelectedProgramInDb();
            GetAllDataFromDb();
            base.OnPropertyChanged(nameof(IsSelected));
            base.OnPropertyChanged(nameof(SelectedProgram));
        }

        private void AddNew(object o)
        {

            try
            {
                _addTitle = false;
                AddNewNotStarted = false;
                //_systemCursor.Copy(User32.OCR.OCR_CROSS, User32.OCR.OCR_NORMAL);
            }
            catch
            {
                // Ignored
            }

        }

        private void AddNewTitle(object o)
        {

            try
            {
                _addTitle = true;
                AddNewNotStarted = false;
                //_systemCursor.Copy(User32.OCR.OCR_CROSS, User32.OCR.OCR_NORMAL);
            }
            catch
            {
                // Ignored
            }

        }

        private void AddNewProgramByMouseClickEventHandler(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (!AddNewNotStarted)
            {
                AddProgramCommon();
                //_systemCursor.TryRestoreAll();
            }
        }

        private void AddProgramCommon(string? program = "")
        {
            if (string.IsNullOrEmpty(program))
            {
                program = CheckActiveProcessFileName.GetActiveProcessName(true, _addTitle);
            }

            ProgramsExceptionsDataModel? dataModel = ProgramsExceptionsList.FirstOrDefault(x => x?.Program == program || x?.Title == program);
            if (dataModel == null)
            {
                dataModel = new ProgramsExceptionsDataModel()
                {
                    IsOnAutoSwitch = false,
                    IsOnClipboard = false,
                    IsOnClipboardImage = false,
                    IsOnDiary = false,
                    IsOnLayoutFlag = false,
                    IsOnLayoutSwitcher = false,
                    IsOnSmartClick = false,
                    IsOnSpellCheckWhileTyping = false,
                    IsOnConverter = false,
                    IsOnHotKeys = false,
                    IsOnAutochange = false,
                };
                if (_addTitle)
                {
                    dataModel.Title = program;
                }
                else
                {
                    dataModel.Program = program;
                }
                ProgramsExceptionsList.Add(dataModel);
                AddNewProgramToDb(dataModel);
                SelectedProgram = dataModel;
            }
            else
            {
                SelectedProgram = dataModel;
            }
            AddNewNotStarted = true;
            _addTitle = false;
        }

        private ProgramInfo? _programsListCurrent;

        public ProgramInfo? ProgramsListCurrent
        {
            get
            {
                return _programsListCurrent;
            }
            set
            {
                _programsListCurrent = value;
                base.OnPropertyChanged(nameof(IsProgramsListCurrentSelected));
            }
        }

        public bool IsProgramsListCurrentSelected => ProgramsListCurrent != null;

        public bool IsSelected
        {
            get
            {
                return SelectedProgram != null;
            }
        }

        private bool _addNewNotStarted;
        private ProgramsExceptionsDataModel? _selectedProgram;

        public bool AddNewNotStarted
        {
            get { return _addNewNotStarted; }
            set
            {
                _addNewNotStarted = value;
                base.OnPropertyChanged();
            }
        }

        public ProgramsExceptionsDataModel? SelectedProgram
        {
            get => _selectedProgram;
            set
            {
                if (Equals(value, _selectedProgram)) return;
                _selectedProgram = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        internal void GetAllDataFromDb()
        {
            ProgramsExceptionsList.Clear();
            ProgramsExceptionsList.AddRange(ProgramsExceptionsManager.GetAllData());
        }

        internal void AddNewProgramToDb(ProgramsExceptionsDataModel? model)
        {
            ProgramsExceptionsManager.AddData(model);
        }

        internal void RemoveProgramFromDb(ProgramsExceptionsDataModel? model)
        {
            ProgramsExceptionsManager.DelData(model);
        }

        internal void UpdateSelectedProgramInDb(object? _ = null)
        {
            ProgramsExceptionsManager.UpdateData(SelectedProgram);
        }
    }
}
