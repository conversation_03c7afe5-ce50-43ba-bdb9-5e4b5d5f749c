﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class AppearanceViewModel : ViewModelBase
    {
        public RadObservableCollection<LangData> LanguagesUi { get; private set; }

        private string? _lastLang;

        public AppearanceViewModel()
        {
            LanguagesUi = new RadObservableCollection<LangData>(GetLangDataList());
        }

        private List<LangData> GetLangDataList()
        {
            var langDataList = new List<LangData>
            {
                new() { LangId = "en", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("en").DisplayName) },
                new() { LangId = "ru", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("ru").DisplayName) },
                new() { LangId = "uk", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("uk").DisplayName) },
                new() { LangId = "es", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("es").DisplayName) },
                new() { LangId = "it", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("it").DisplayName) },
                new() { LangId = "de", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("de").DisplayName) },
                new() { LangId = "fr", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("fr").DisplayName) },
                new() { LangId = "cs", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("cs").DisplayName) },
                new() { LangId = "pl", LangName = FirstLetterCapital(CultureInfo.GetCultureInfo("pl").DisplayName) }
            };
            return langDataList;
        }

        private string FirstLetterCapital(string str)
        {
            return Char.ToUpper(str[0]) + str.Remove(0, 1);
        }

        internal bool IsUseNightTheme
        {
            get
            {
                return SettingsManager.Settings.IsUseNightTheme;
            }
            set
            {
                SettingsManager.Settings.IsUseNightTheme = value;
                base.OnPropertyChanged(nameof(IsUseNightTheme));
            }
        }

        internal TimeSpan ThemeTimeStartNight
        {
            get
            {
                if (SettingsManager.Settings.ThemeTimeStartNight.Equals(DateTime.MinValue))
                {
                    SettingsManager.Settings.ThemeTimeStartNight = DateTime.MinValue.AddHours(20);
                }
                return SettingsManager.Settings.ThemeTimeStartNight.TimeOfDay;
            }
            set
            {
                SettingsManager.Settings.ThemeTimeStartNight = DateTime.MinValue.Add(value);
                base.OnPropertyChanged(nameof(ThemeTimeStartNight));
            }
        }

        internal TimeSpan ThemeTimeEndNight
        {
            get
            {
                if (SettingsManager.Settings.ThemeTimeEndNight.Equals(DateTime.MinValue))
                {
                    SettingsManager.Settings.ThemeTimeEndNight = DateTime.MinValue.AddHours(7);
                }
                return SettingsManager.Settings.ThemeTimeEndNight.TimeOfDay;
            }
            set
            {
                SettingsManager.Settings.ThemeTimeEndNight = DateTime.MinValue.Add(value);
                base.OnPropertyChanged();
            }
        }

        public LangData LanguageUi
        {
            get
            {
                var lang = LanguagesUi.FirstOrDefault(x => x.LangId == SettingsManager.Settings.AppUILang);
                if (lang != null) return lang;
                return LanguagesUi[2];
            }
            set
            {
                _lastLang = SettingsManager.Settings.AppUILang;
                SettingsManager.Settings.AppUILang = value.LangId;
                IsRestart();
                base.OnPropertyChanged(nameof(LanguageUi));
            }
        }

        private void IsRestart()
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("GeneralSettingsLanguageProgramRestart"),
                Closed = (_, args) =>
                {
                    if (args.DialogResult == true)
                    {
                        SettingsManager.SaveSettings();
                        GlobalEventsApp.OnEventRestart(false);
                    }
                    else
                    {
                        SettingsManager.Settings.AppUILang = _lastLang;
                        base.OnPropertyChanged(nameof(LanguageUi));
                    }
                }
            });
        }
    }

    public class LangData
    {
        public string? LangName { get; set; }

        public string? LangId { get; set; }
    }
}