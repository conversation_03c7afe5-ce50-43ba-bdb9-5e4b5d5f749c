﻿<UserControl x:Class="Everylang.App.View.SettingControls.Converter.ConverterControl"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
        xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
        mc:Ignorable="d" x:ClassModifier="internal"
        DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <StackPanel Margin="20,10,0,10" Orientation="Horizontal">
            <TextBlock FontSize="15" FontWeight="Bold"  Text="{telerik:LocalizableResource Key=ConverterSettingsHeader}" />
            <TextBlock FontSize="15" FontWeight="Bold" Margin="7,0,0,0" Text="{telerik:LocalizableResource Key=OnlyPro}" />
        </StackPanel>
        <ScrollViewer Margin="20,35,0,10" VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled" >
            <StackPanel  HorizontalAlignment="Left" VerticalAlignment="Top" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">

                <TextBlock Margin="0,10,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsOpenWindow}" />
                <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindow}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindow}"/>
                    <telerik:RadButton Focusable="False" Margin="5,0,0,0" Click="ExpressionOpenWindowClick" HorizontalAlignment="Left" Padding="5,0,5,0" VerticalAlignment="Center" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,20,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsExpression}" />
                <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutExpresion}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutExpresion}"/>
                    <telerik:RadButton Focusable="False" Margin="5,0,0,0" Click="ExpressionShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" VerticalAlignment="Center" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsTransliteration}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutTransliteration}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutTransliteration}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="TransliterationShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsEncloseTextQuotationMarks}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="EncloseTextQuotationMarksShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="OpenFramesSettingsClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Settings}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterReplaceSelText}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutReplaceSelText}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutReplaceSelText}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="ReplaceSelTextShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsCamelCase}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutCamelCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutCamelCase}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="CamelCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsSnakeCase}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSnakeCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSnakeCase}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="SnakeCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsKebabCase}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutKebabCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutKebabCase}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="KebabCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                <TextBlock Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsPascalCase}" />
                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=ConverterSettingsViewModel.ShortcutPascalCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutPascalCase}"/>
                    <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="PascalCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                </StackPanel>

                
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
