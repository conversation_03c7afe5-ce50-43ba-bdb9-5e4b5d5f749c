﻿using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.AutoSwitch
{
    /// <summary>
    /// Interaction logic for AutoSwitchListRules.xaml
    /// </summary>
    internal partial class AutoSwitchListRules
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(AutoSwitchListRules));

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        internal AutoSwitchListRules()
        {
            InitializeComponent();
            SetItemsSource(false);
        }

        private void SetItemsSource(bool withIntermidate)
        {

            if (withIntermidate)
            {
                this.DataGridListRules.Columns[1].ClearFilters();
            }
            else
            {
                Telerik.Windows.Controls.GridViewColumn countryColumn = this.DataGridListRules.Columns[1];
                Telerik.Windows.Controls.GridView.IColumnFilterDescriptor countryFilter = countryColumn.ColumnFilterDescriptor;
                countryFilter.SuspendNotifications();
                countryFilter.DistinctFilter.AddDistinctValue(LocalizationManager.GetString("AutoSwitcherSettingsRuleActionConvert"));
                countryFilter.DistinctFilter.AddDistinctValue(LocalizationManager.GetString("AutoSwitcherSettingsRuleActionNotConvert"));
                countryFilter.ResumeNotifications();
            }
            DataGridListRules.Focus();
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }

        private void TextBoxBase_OnTextChanged(object sender, Telerik.Windows.Controls.AutoSuggestBox.TextChangedEventArgs e)
        {
            if (e.Reason == Telerik.Windows.Controls.AutoSuggestBox.TextChangeReason.UserInput)
            {
                string text = TextBoxSearch.Text;
                var deleteCommand = RadGridViewCommands.SearchByText as RoutedUICommand;
                deleteCommand?.Execute(text, this.DataGridListRules);
            }
        }

        private void CheckBoxIsShowAll_OnChecked(object sender, RoutedEventArgs e)
        {
            SetItemsSource(CheckBoxIsShowAll != null && ((bool)CheckBoxIsShowAll.IsChecked)!);
        }

        private void DataGridListRules_OnRowEditEnded(object sender, GridViewRowEditEndedEventArgs e)
        {
            if (e.OldValues.First().Value == null)
            {
                AutoSwitchRuleManager.AddAutoSwitchRule((AutoSwitchRuleDataModel)e.EditedItem);
            }
            else
            {
                AutoSwitchRuleManager.UpdateData((AutoSwitchRuleDataModel)e.EditedItem);
            }

        }

        private void DataGridListRules_OnDeleted(object sender, GridViewDeletedEventArgs e)
        {
            foreach (var eItem in e.Items)
            {
                AutoSwitchRuleManager.DelAutoSwitchRule((AutoSwitchRuleDataModel)eItem);
            }
        }
    }
}
