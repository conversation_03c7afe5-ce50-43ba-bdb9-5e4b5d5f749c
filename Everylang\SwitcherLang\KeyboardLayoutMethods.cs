﻿using Everylang.App.HookManager.GlobalHooks;
using Everylang.Common.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Windows.Input;
using Vanara.PInvoke;

namespace Everylang.App.SwitcherLang
{
    class KeyboardLayoutMethods
    {
        internal static List<CultureInfo> GetInputLangs()
        {
            List<CultureInfo> cultureInfoList = new List<CultureInfo>();
            foreach (var availableInputLanguage in GetAvailableInputLanguages())
            {
                cultureInfoList.Add(availableInputLanguage.Culture);
            }
            return cultureInfoList;
        }

        internal static List<InputLanguage> GetAvailableInputLanguages()
        {
            List<InputLanguage> inputLanguages = new List<InputLanguage>();
            foreach (InputLanguage installedInputLanguage in InputLanguage.InstalledInputLanguages)
            {
                inputLanguages.Add(installedInputLanguage);
            }
            if (InputLanguageManager.Current.AvailableInputLanguages != null)
            {
                var cultureInfoList = InputLanguageManager.Current.AvailableInputLanguages.Cast<CultureInfo>().ToList();
                foreach (InputLanguage installedInputLanguage in InputLanguage.InstalledInputLanguages)
                {
                    int index = cultureInfoList.IndexOf(installedInputLanguage.Culture);
                    if (index > -1)
                    {
                        var insIndex = inputLanguages.IndexOf(installedInputLanguage);
                        if (index != insIndex)
                        {
                            inputLanguages.RemoveAt(insIndex);
                            inputLanguages.Insert(index, installedInputLanguage);
                        }
                    }
                }
            }
            return inputLanguages;
        }

        internal static string CodeToString(GlobalKeyEventArgs key, IntPtr layout = default(IntPtr))
        {
            try
            {
                IntPtr hkl = layout;
                if (layout == default)
                {
                    hkl = GetCurrentKeyboardLayout();
                }

                return KeyCodeToString.VkCodeToString(key, true, hkl);
            }
            catch
            {
                // Ignore
            }
            return "";
        }

        internal static bool IsPrintable(GlobalKeyEventArgs key, IntPtr layout = default(IntPtr))
        {
            try
            {
                IntPtr hkl = layout;
                if (layout == default(IntPtr))
                {
                    hkl = GetCurrentKeyboardLayout();
                }

                return User32.MapVirtualKeyEx((uint)key.KeyCode, User32.MAPVK.MAPVK_VK_TO_CHAR, hkl) != 0;
            }
            catch
            {
                // Ignore
            }
            return false;
        }

        internal static string GetCharsFromKeys(Keys keys, bool shift = false, IntPtr layout = default)
        {
            try
            {
                IntPtr hkl = layout;
                if (layout == default)
                {
                    hkl = GetCurrentKeyboardLayout();
                }

                var buf = new StringBuilder(256);
                var keyboardState = new byte[256];
                if (keys.ToString().Contains("Shift") || shift)
                    keyboardState[(int)Keys.ShiftKey] = 0xff;
                uint lScanCode = User32.MapVirtualKeyEx((uint)keys, 0, hkl);
                User32.ToUnicodeEx((uint)keys, lScanCode, keyboardState, buf, buf.Capacity, 0, hkl);
                return buf.ToString();
            }
            catch
            {
                // Ignore
            }
            return "";
        }

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        internal static extern short VkKeyScanEx(char ch, IntPtr dwhkl);


        internal static Keys ConvertCharToVirtualKey(char character, string? lang = "")
        {
            try
            {
                UInt16 scanCode = character;
                short keyNumber;
                if (!string.IsNullOrEmpty(lang) && KeyboardLayoutCommon.AutoSwitcherLayouts.ContainsKey(lang))
                {
                    keyNumber = VkKeyScanEx(character, KeyboardLayoutCommon.AutoSwitcherLayouts[lang]);
                    if (keyNumber != -1)
                    {
                        return (Keys)(((keyNumber & 0xFF00) << 8) | (keyNumber & 0xFF));
                    }
                }

                if (scanCode < 128)
                {
                    if (KeyboardLayoutCommon.AutoSwitcherLayouts.ContainsKey("en"))
                    {
                        keyNumber = VkKeyScanEx(character, KeyboardLayoutCommon.AutoSwitcherLayouts["en"]);
                        if (keyNumber != -1)
                        {
                            return (Keys)(((keyNumber & 0xFF00) << 8) | (keyNumber & 0xFF));
                        }
                    }
                }

                foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                {
                    keyNumber = VkKeyScanEx(character, switcherLayout.Value);
                    if (keyNumber != -1)
                    {
                        return (Keys)(((keyNumber & 0xFF00) << 8) | (keyNumber & 0xFF));
                    }
                }

                return Keys.None;
            }
            catch
            {
                // Ignore
            }
            return Keys.None;
        }

        internal static Dictionary<string, string?> GetTextForAllLayouts(string text)
        {
            var result = new Dictionary<string, string?>();
            foreach (char character in text)
            {
                Keys key = ConvertCharToVirtualKey(character);
                if (KeyboardLayoutCommon.AutoSwitcherLayouts != null)
                {
                    foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                    {
                        var c = GetCharsFromKeys(key, false, switcherLayout.Value);
                        if (result.ContainsKey(switcherLayout.Key))
                        {
                            result[switcherLayout.Key] += c;
                        }
                        else
                        {
                            if (!result.ContainsKey(switcherLayout.Key))
                                result.Add(switcherLayout.Key, c);
                        }
                    }
                }
            }
            return result;
        }


        internal static IntPtr GetCurrentKeyboardLayout()
        {
            try
            {
                var keyboardLayout = KeyboardLayoutCommon.CurrentKeyboardLayout.ToInt32() & 0x0000FFFF;
                return new IntPtr(keyboardLayout);
            }
            catch
            {
                // Ignore
            }
            return new IntPtr();
        }

        internal static IntPtr GetCurrentKeyboardLayoutHdl()
        {
            try
            {
                if (GetWindowClassName().ToLower() == "consolewindowclass")
                {
                    return KeyboardLayoutCommon.CurrentKeyboardLayout;
                }

                return User32.GetKeyboardLayout(
                    User32.GetWindowThreadProcessId(ForegroundWindow.GetForeground(), out _)).DangerousGetHandle();
            }
            catch
            {
                // Ignore
            }
            return IntPtr.Zero;
        }

        internal static string GetEnglishNameCurrentKeyboardLayout(IntPtr langCode)
        {
            foreach (CultureInfo? lang in KeyboardLayoutMethods.GetInputLangs())
            {
                var inputLanguage = InputLanguage.FromCulture(lang);
                if (inputLanguage != null)
                {

                    if (inputLanguage.Handle.Equals(langCode))
                    {
                        var m = Regex.Match(lang.EnglishName, @"\((.*)\)");
                        if (m.Groups.Count > 1)
                        {
                            var resArr = m.Groups[1].Value.Split(',');
                            string res;
                            if (resArr.Length > 1)
                            {
                                res = resArr.Last().Trim();
                            }
                            else
                            {
                                res = resArr[0].Trim();
                            }
                            return res.Replace(" ", "-");
                        }
                    }
                }
            }
            return "";
        }

        internal static string GetCurrentKeyboardLayoutName(IntPtr currentKeyboardLayout)
        {
            if (currentKeyboardLayout == IntPtr.Zero)
            {
                return GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.CurrentKeyboardLayout);
            }
            else
            {
                return GetKeyboardLayoutNameByHandle(currentKeyboardLayout);
            }
        }

        internal static string GetKeyboardLayoutNameByHandle(IntPtr lang)
        {
            try
            {
                var keyboardLayout = lang.ToInt32() & 0x0000FFFF;
                var sb = new StringBuilder(9);
                Kernel32.GetLocaleInfo((uint)keyboardLayout, Kernel32.LCTYPE.LOCALE_SISO639LANGNAME, sb, sb.Capacity);
                return sb.ToString();
            }
            catch
            {
                // Ignore
            }
            return "";
        }

        private static Dictionary<string, List<char>> _langsDic;

        internal static string? DetectLang(string text)
        {
            var langListResult = new List<string?>();
            foreach (var keyValuePair in KeyboardLayoutCommon.AutoSwitcherPossibleList)
            {
                var langList = new List<string?>();
                foreach (var t in keyValuePair.Value)
                {
                    if (text.ToLower().Contains(t))
                    {
                        langList.Add(keyValuePair.Key);
                    }
                }
                if (langList.Count == 0)
                {
                    langList = DetectByWord(text, keyValuePair);
                }
                langListResult.AddRange(langList);
            }
            return MaxFrequencyLang(langListResult);
        }

        private static List<string> DetectByWord(string text, KeyValuePair<string, List<string>> keyValuePair)
        {
            if (_langsDic == null)
            {
                _langsDic = new Dictionary<string, List<char>>();
            }
            if (!_langsDic.ContainsKey(keyValuePair.Key))
            {
                _langsDic.Add(keyValuePair.Key, keyValuePair.Value.SelectMany(s => s).Distinct().ToList());
            }

            var langList = new List<string>();
            foreach (char c in text)
            {
                if (_langsDic[keyValuePair.Key].Contains(c))
                {
                    langList.Add(keyValuePair.Key);
                }
            }
            return langList;

        }

        internal static string? MaxFrequencyLang(List<string?>? langList)
        {
            if (langList != null && langList.Count > 0)
                return (from item in langList
                        group item by item into g
                        orderby g.Count() descending
                        select g.Key).First();
            return "";
        }

        private static string GetWindowClassName()
        {
            IntPtr hWnd = ForegroundWindow.GetForeground();
            StringBuilder buffer = new StringBuilder(128);
            User32.GetClassName(hWnd, buffer, buffer.Capacity);
            return buffer.ToString();
        }
    }
}
