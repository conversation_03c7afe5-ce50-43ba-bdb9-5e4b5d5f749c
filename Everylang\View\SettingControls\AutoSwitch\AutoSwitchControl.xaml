﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.AutoSwitch.AutoSwitchControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:global="clr-namespace:System.Globalization;assembly=mscorlib"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.Row="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <StackPanel Margin="20,10,0,0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="15"
                    FontWeight="Bold"
                    Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsHeader}" />
                <TextBlock
                    FontSize="15"
                    FontWeight="Bold"
                    Margin="7,0,0,0"
                    Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>
            <telerik:RadToggleSwitchButton
                CheckedContent="{telerik:LocalizableResource Key=AutoSwitchSettingsIsOn}"
                ContentPosition="Right"
                Focusable="False"
                FontSize="14"
                FontWeight="DemiBold"
                HorizontalAlignment="Left"
                IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch}"
                Margin="0,0,0,0"
                UncheckedContent="{telerik:LocalizableResource Key=AutoSwitchSettingsIsOff}"
                VerticalAlignment="Center" />
            <StackPanel IsEnabled="{Binding AutoSwitcherSettingsViewModel.IsEnabledAutoSwitch}">
                <telerik:RadButton
                    Click="OpenRulesListOnClick"
                    Content="{telerik:LocalizableResource Key=AutoSwitcherSettingsOpenRulesList}"
                    Focusable="False"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,0" />
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsOnAddingRule}"
                    Margin="0,3,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsOnAddingRule}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.AutoSwitcherShowAcceptWindow}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsShowAcceptWindow}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsSwitchOneLetter}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsSwitchOneLetter}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsOnTwoUpperCaseLetters}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsOnTwoUpperCaseLetters}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsOnFixWrongUpperCase}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsOnFixWrongUpperCase}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsOnUpperCaseNotSwitch}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsOnUpperCaseNotSwitch}" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.IsOnByEnter}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsIsOnByEnter}" />
                </CheckBox>

                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.AutoSwitcherDisableAutoSwitchAfterManualSwitch}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock
                        FontSize="14"
                        Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch}"
                        TextWrapping="Wrap" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.AutoSwitcherOnlyAfterSeparator}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock
                        FontSize="14"
                        Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsOnlyAfterSeparator}"
                        TextWrapping="Wrap" />
                </CheckBox>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=AutoSwitcherSettingsViewModel.AutoSwitcherAfterPause}"
                    Margin="0,0,0,0"
                    MinHeight="0">
                    <TextBlock
                        FontSize="14"
                        Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsAfterPause}"
                        TextWrapping="Wrap" />
                </CheckBox>
                <StackPanel Margin="0,3,0,0">
                    <TextBlock
                        FontSize="13"
                        Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsCountCheckRule}"
                        VerticalAlignment="Center" />
                    <telerik:RadNumericUpDown
                        Focusable="False"
                        FontSize="13"
                        HorizontalAlignment="Left"
                        IsEnabled="{Binding Path=AutoSwitcherSettingsViewModel.IsOnAddingRule}"
                        Margin="0,2,0,0"
                        Maximum="5"
                        Minimum="1"
                        Value="{Binding Path=AutoSwitcherSettingsViewModel.AutoSwitcherCountCheckRule}"
                        VerticalAlignment="Center"
                        Width="100">
                        <telerik:RadNumericUpDown.NumberFormatInfo>
                            <global:NumberFormatInfo NumberDecimalDigits="0" />
                        </telerik:RadNumericUpDown.NumberFormatInfo>
                    </telerik:RadNumericUpDown>
                </StackPanel>
                <TextBlock
                    FontSize="14"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsTrueListOfLang}" />
                <Grid Name="GridForViewModel">
                    <telerik:RadComboBox
                        AllowMultipleSelection="True"
                        BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                        DisplayMemberPath="Key"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="0,3,0,0"
                        ToolTip="{Binding Path=Text, RelativeSource={RelativeSource Self}}"
                        VerticalAlignment="Center"
                        Width="300"
                        x:Name="ComboTrueListOfLang" />
                </Grid>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>