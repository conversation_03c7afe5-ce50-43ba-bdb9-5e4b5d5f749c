﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;

namespace Everylang.Note.Helpers
{
    public class RichTextBoxHelper : DependencyObject
    {
        public static FlowDocument GetDocumentFlow(DependencyObject obj)
        {
            return (FlowDocument)obj.GetValue(DocumentFlowProperty);
        }

        public static void SetDocumentFlow(DependencyObject obj, FlowDocument value)
        {
            obj.SetValue(DocumentFlowProperty, value);
        }

        public static readonly DependencyProperty DocumentFlowProperty =
            DependencyProperty.RegisterAttached(
                "DocumentFlow",
                typeof(FlowDocument),
                typeof(RichTextBoxHelper),
                new FrameworkPropertyMetadata
                {
                    BindsTwoWayByDefault = false,
                    PropertyChangedCallback = (obj, e) =>
                    {
                        var richTextBox = (RichTextBox)obj;

                        // Parse the XAML to a document (or use XamlReader.Parse())
                        var flowDocument = GetDocumentFlow(richTextBox);
                        // Set the document
                        richTextBox.Document = flowDocument;
                    }
                });
    }
}
