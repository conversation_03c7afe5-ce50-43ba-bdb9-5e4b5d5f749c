﻿<UserControl x:Class="Everylang.App.View.SettingControls.General.GeneralControlExportImport"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel>
            <StackPanel  Margin="20,10,0,0" Orientation="Horizontal" VerticalAlignment="Top" >
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock Margin="10,0,0,0" FontSize="15" VerticalAlignment="Center" HorizontalAlignment="Left" Text="{telerik:LocalizableResource Key=GeneralSettingsExportImport}" />
            </StackPanel>
            <StackPanel Margin="50,30,0,0">
                <CheckBox FontSize="14" Focusable="False" Margin="0,0,0,0" IsChecked="{Binding Path=GeneralSettingsViewModel.IsExportImportGeneralSetting}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsHeader}" />
                </CheckBox>
                <CheckBox FontSize="14" Focusable="False" Margin="0,5,0,0" IsChecked="{Binding Path=GeneralSettingsViewModel.IsExportImportAutochange}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnAutochange}" />
                </CheckBox>
                <CheckBox FontSize="14" Focusable="False" Margin="0,5,0,0" IsChecked="{Binding Path=GeneralSettingsViewModel.IsExportImportClipboard}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnClipboard}" />
                </CheckBox>
                <CheckBox FontSize="14" Focusable="False" Margin="0,5,0,0" IsChecked="{Binding Path=GeneralSettingsViewModel.IsExportImportDiare}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnDiary}" /></CheckBox>

                <CheckBox FontSize="14" Focusable="False" Margin="0,5,0,0" IsChecked="{Binding Path=GeneralSettingsViewModel.IsExportImportAutoSwitcherRules}" >
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutoSwitcherSettingsListRulesHeader}" />
                </CheckBox>
                <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                    <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Margin="0,0,0,0" Content="{telerik:LocalizableResource Key=GeneralSettingsExport}" Command="{Binding Path=GeneralSettingsViewModel.ExportCommand}" />
                    <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Margin="15,0,0,0" Content="{telerik:LocalizableResource Key=GeneralSettingsImport}" Command="{Binding Path=GeneralSettingsViewModel.ImportCommand}" />
                </StackPanel>
                
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
