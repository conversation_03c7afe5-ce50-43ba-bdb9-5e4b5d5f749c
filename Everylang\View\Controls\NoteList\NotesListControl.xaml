﻿<UserControl x:Class="Everylang.App.View.Controls.NoteList.NotesListControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:wpf="clr-namespace:MaterialDesignThemes.Wpf;assembly=MaterialDesignThemes.Wpf"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:noteDataStore="clr-namespace:Everylang.Note.NoteDataStore;assembly=Everylang.Note"
             xmlns:helpers="clr-namespace:Everylang.Note.Helpers;assembly=Everylang.Note"
             xmlns:local="clr-namespace:Everylang.App.View.Controls.NoteList"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             xmlns:richTextBoxEx="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
                <wpf:CustomColorTheme BaseTheme="Light" ColorAdjustment="{wpf:ColorAdjustment}" PrimaryColor="#757575"
                                      SecondaryColor="#BDBDBD"/>
            </ResourceDictionary.MergedDictionaries>
           
            <DataTemplate DataType="{x:Type noteDataStore:NoteDataModel}">
                <DataTemplate.Resources>
                    <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                        <Setter Property="Margin" Value="0"/>
                        <Setter Property="CommandParameter" Value="{Binding}"/>
                        <Setter Property="VerticalAlignment" Value="Stretch"/>
                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                    </Style>
                </DataTemplate.Resources>
                <Grid>
                    <wpf:Card
                        x:Name="CardNote"
                         Margin="3"
                         Width="320"
                         Height="250"
                        Background="{Binding ColorBrushList[4]}"
                        Foreground="{DynamicResource PrimaryHueLightForegroundBrush}"
                        PreviewMouseLeftButtonDown="CardNoteMouseLeftButtonDown">
                        <wpf:Card.Resources>
                            <ResourceDictionary>
                                <ResourceDictionary.MergedDictionaries>
                                    <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                                </ResourceDictionary.MergedDictionaries>
                            </ResourceDictionary>
                        </wpf:Card.Resources>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Border Name="borderMain" Grid.Row="0" Background="{Binding ColorBrushList[0]}" Height="25">
                                <Grid>
                                    <TextBlock Margin="10,0,0,0" Text="{Binding NoteName}" Foreground="#212121" VerticalAlignment="Center" FontSize="14" Cursor="Arrow"  ContextMenuService.IsEnabled="false" Opacity="0.70" />
                                    <TextBlock Margin="0,0,30,0" Visibility="{Binding IsArchived, Converter={StaticResource BoolToVis}}" Text="{telerik:LocalizableResource Key=NoteInArchive}" VerticalAlignment="Center" HorizontalAlignment="Right" FontSize="14" ContextMenuService.IsEnabled="false" Opacity="0.70" />
                                    <CheckBox x:Name="CheckBoxNote" HorizontalAlignment="Right" Style="{StaticResource MaterialDesignCheckBox}" Cursor="Hand" Background="{Binding ColorBrushList[5]}"  Margin="0,0,5,0" IsChecked="{Binding IsSelected}" Command="{Binding RelativeSource={RelativeSource FindAncestor,
                                                                                                    AncestorType={x:Type local:NotesListControl}},
                                                                                                    Path= DataContext.NotesListControlViewModel.CheckCommand, Mode=OneTime}"
                                              CommandParameter="{Binding}"/>

                                </Grid>
                            </Border>
                            <TextBox Text="{Binding TextView, Mode=OneWay}" VerticalContentAlignment="Top" Grid.Row="1"  Foreground="#212121" Margin="8,0,8,3" IsReadOnly="True" VerticalScrollBarVisibility="Auto" wpf:TextFieldAssist.DecorationVisibility="Hidden" IsInactiveSelectionHighlightEnabled="True" BorderThickness="0"  Block.LineHeight="1" BorderBrush="{x:Null}" Opacity="0.90"></TextBox>
                            <Border x:Name="BorderCard" Grid.Row="2" Background="{Binding ColorBrushList[0]}" Height="28">
                                <Grid Margin="0,0,0,0">
                                    <TextBlock Margin="7,0,0,0" Text="{Binding DateTimeLastEdit}" FontSize="13" Foreground="#212121" VerticalAlignment="Center" HorizontalAlignment="Left"/>
                                    <Button  Margin="0,0,40,0" VerticalAlignment="Center" CommandParameter="{Binding}" Cursor="Hand" HorizontalAlignment="Right" Padding="0">
                                        <Button.Style>
                                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsArchived}" Value="True">
                                                        <Setter Property="Content">
                                                            <Setter.Value>
                                                                <wpf:PackIcon Kind="ArchiveArrowUpOutline" Height="20" Width="20" />
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Setter Property="Command" Value="{Binding RelativeSource={RelativeSource FindAncestor,
                                                                                            AncestorType={x:Type local:NotesListControl}},
                                                                                            Path=DataContext.NotesListControlViewModel.ReturnFromArchivCommand, Mode=OneTime}">
                                                            
                                                        </Setter>
                                                        <Setter Property="ToolTip" Value="{telerik:LocalizableResource Key=NotesFromArchive}"></Setter>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsArchived}" Value="False">
                                                        <Setter Property="Content">
                                                            <Setter.Value>
                                                                <wpf:PackIcon Kind="ArchiveArrowDownOutline" Height="20" Width="20" />
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Setter Property="Command" Value="{Binding RelativeSource={RelativeSource FindAncestor,
                                                                                            AncestorType={x:Type local:NotesListControl}},
                                                                                            Path=DataContext.NotesListControlViewModel.ToArchivCommand, Mode=OneTime}">

                                                        </Setter>
                                                        <Setter Property="ToolTip" Value="{telerik:LocalizableResource Key=NotesToArchive}"></Setter>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                    <Button Command="{Binding RelativeSource={RelativeSource FindAncestor,
                                        AncestorType={x:Type local:NotesListControl}},
                                        Path=DataContext.NotesListControlViewModel.ToDeleteCommand, Mode=OneTime}" CommandParameter="{Binding}" Margin="0,0,10,0" VerticalAlignment="Center" ToolTip="{telerik:LocalizableResource Key=Delete}" Cursor="Hand" HorizontalAlignment="Right" Padding="0" Style="{StaticResource MaterialDesignToolButton}">
                                        <wpf:PackIcon Kind="DeleteOutline" Height="20" Width="20" />
                                    </Button>
                                </Grid>
                            </Border>
                        </Grid>
                    </wpf:Card>
                </Grid>
                <DataTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter TargetName="BorderCard" Property="Visibility" Value="Visible" />
                        <Setter TargetName="CheckBoxNote" Property="Visibility" Value="Visible" />
                    </Trigger>
                    <Trigger Property="IsMouseOver" Value="False">
                        <Setter TargetName="BorderCard" Property="Visibility" Value="Collapsed" />
                        <Setter TargetName="CheckBoxNote" Property="Visibility" Value="Hidden" />
                    </Trigger>
                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                        <Setter TargetName="CheckBoxNote" Property="Visibility" Value="Visible" />
                        <Setter TargetName="CardNote" Property="Background" Value="{Binding ColorBrushList[1]}" />
                        <Setter TargetName="BorderCard" Property="Background" Value="{Binding ColorBrushList[3]}" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisible}" Value="False">
                        <Setter TargetName="CheckBoxNote" Property="Visibility" Value="Hidden" />
                    </DataTrigger>
                </DataTemplate.Triggers>
            </DataTemplate>
        </ResourceDictionary>
    </UserControl.Resources>


    <DockPanel>
        <wpf:DrawerHost
                x:Name="DrawerHostTop"
                OpenMode="Standard"
                IsTopDrawerOpen="{Binding NotesListControlViewModel.IsHaveSelected}"
                BorderBrush="{DynamicResource MaterialDesignDivider}">
            <wpf:DrawerHost.TopDrawerContent>
                <Grid Background="{telerik:Windows11Resource ResourceKey=SelectedUnfocusedBrush }" HorizontalAlignment="Stretch">
                    <StackPanel
                        HorizontalAlignment="Left"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                        <Button
                            Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush }"
                            Command="{Binding NotesListControlViewModel.UnCheckAllCommand}"
                            Style="{DynamicResource MaterialDesignToolButton}"
                            Padding="0"
                            VerticalAlignment="Center">
                            <wpf:PackIcon Kind="Close" Height="20" Width="20" Margin="20,0,0,0"/>
                        </Button>
                        <TextBlock Text="{Binding NotesListControlViewModel.SelectedCount}" Margin="15,1,0,0" VerticalAlignment="Center" FontSize="15" Padding="0"/>
                    </StackPanel>

                    <StackPanel
                        Margin="3"
                        HorizontalAlignment="Right"
                        Orientation="Horizontal">
                        <Button
                            Command="{Binding NotesListControlViewModel.ToArchivSelectedCommand}"
                            Style="{DynamicResource MaterialDesignFlatButton}"
                            VerticalAlignment="Center"
                            Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush }"
                            Content="{telerik:LocalizableResource Key=NotesToArchive}"/>
                        <Button
                            Command="{Binding NotesListControlViewModel.ToDeleteSelectedCommand}"
                            Style="{DynamicResource MaterialDesignFlatButton}"
                            VerticalAlignment="Center"
                            Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush }"
                            Margin="0,0,5,0"
                            Content="{telerik:LocalizableResource Key=Delete}"/>
                    </StackPanel>
                </Grid>
            </wpf:DrawerHost.TopDrawerContent>
            <Grid Margin="0,0,0,5">
                <ScrollViewer VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled" wpf:ScrollViewerAssist.IsAutoHideEnabled="True">
                    <ItemsControl  x:Name="ItemsControlList" Margin="5" ItemsSource="{Binding NotesListControlViewModel.ItemsView}">
                        <ItemsControl.ItemsPanel>  
                             <ItemsPanelTemplate> 
                                    <WrapPanel Orientation="Horizontal"/>  
                             </ItemsPanelTemplate>  
                         </ItemsControl.ItemsPanel>  
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </wpf:DrawerHost>
    </DockPanel>
    

</UserControl>
