﻿using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Translator
{
    internal class RequestSettings
    {
        private Language? _languageFromCurrent;
        private Language? _languageToCurrent;

        internal RequestSettings()
        {
            LastSourceTextList = new List<string>();
            ListLangs = new List<Language>();
            //GetCurrentTranslateServiceLangs();
            CurrentTranslateServiceIndex = TranslateCommonSettings.TranslateServiceDefault;
        }

        internal List<Language> ListLangs;

        internal void GetCurrentTranslateServiceLangs()
        {
            if (CurrentTranslateServiceIndex == 0)
            {
                ListLangs = TranslateCommonSettings.ListGoogleLangs.ToList();
            }
            if (CurrentTranslateServiceIndex == 1)
            {
                ListLangs = TranslateCommonSettings.ListBingLangs.ToList();
            }
            if (CurrentTranslateServiceIndex == 2)
            {
                ListLangs = TranslateCommonSettings.ListYandexLangs.ToList();
            }
            if (CurrentTranslateServiceIndex == 3)
            {
                ListLangs = TranslateCommonSettings.ListDeeplLangs.ToList();
            }
            if (CurrentTranslateServiceIndex == 4)
            {
                ListLangs = TranslateCommonSettings.ListMicrosoftLangs.ToList();
            }
        }

        internal int CurrentTranslateServiceIndex { get; set; }

        internal bool IsOneWord { get; set; }
        internal bool IsNowTranslating { get; set; }
        internal string LastSourceText { get; set; } = "";
        internal string SourceText { get; set; } = "";
        internal string SourceTextTrimmed { get; set; } = "";
        internal string? TranslatedText { get; set; } = "";
        internal string? TranslatedTextWithNonChar { get; set; } = "";
        internal string? TranslatedTextLatin { get; set; } = "";
        internal List<string> LastSourceTextList { get; set; }
        internal string StartNonCharList { get; set; } = null!;
        internal string EndNonCharList { get; set; } = null!;

        internal Language? LanguageFromCurrent
        {
            get
            {
                if (_languageFromCurrent != null) return _languageFromCurrent;
                return TranslateCommonSettings.LanguageFromDefault(ListLangs);
            }
            set => _languageFromCurrent = value;
        }

        internal Language? LanguageToCurrent
        {
            get
            {
                if (_languageToCurrent != null) return _languageToCurrent;
                return TranslateCommonSettings.LanguageToDefault(ListLangs);
            }
            set => _languageToCurrent = value;
        }
    }
}