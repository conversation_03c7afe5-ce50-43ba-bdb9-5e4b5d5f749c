﻿using Everylang.App.SettingsApp;
using Everylang.App.Snippets;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Snippets
{
    /// <summary>
    /// Interaction logic for SnippetsControl.xaml
    /// </summary>
    internal partial class SnippetsControl : UserControl
    {
        internal SnippetsControl()
        {
            InitializeComponent();
        }

        private void AutochangeShortcutShowListClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("AutochangeKeyboardShortcuts"), SettingsManager.Settings.SnippetsShowAllShortcut, nameof(SettingsManager.Settings.SnippetsShowAllShortcut), SnippetsManager.Instance.PressedSnippetsView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SnippetsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void AutochangeShortcutAddNewClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("AutochangeKeyboardShortcutsAddNew"), SettingsManager.Settings.SnippetsAddNewShortcut, nameof(SettingsManager.Settings.SnippetsAddNewShortcut), SnippetsManager.Instance.PressedSnippetsAddNew);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SnippetsViewModel.ShortcutAddNew = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
    }
}
