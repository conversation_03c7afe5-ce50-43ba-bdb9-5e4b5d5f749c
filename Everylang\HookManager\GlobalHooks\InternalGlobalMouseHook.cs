﻿using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using Vanara.PInvoke;

namespace Everylang.App.HookManager.GlobalHooks
{
    class GlobalMouseHookEventArgs : HandledEventArgs
    {
        internal User32.WindowMessage MouseMessages { get; private set; }
        internal User32.MSLLHOOKSTRUCT MouseData { get; private set; }

        internal GlobalMouseHookEventArgs(User32.WindowMessage mouseMessages, User32.MSLLHOOKSTRUCT mouseData)
        {
            MouseMessages = mouseMessages;
            MouseData = mouseData;
        }
    }

    class InternalGlobalMouseHook
    {
        internal event EventHandler<GlobalMouseHookEventArgs>? MouseEvent;
        private static User32.SafeHHOOK? _hookId;

        internal InternalGlobalMouseHook()
        {
            _hookId = GlobalHook.CreateMouseHook(LowLevelMouseEvent);
        }


        private IntPtr LowLevelMouseEvent(int nCode, IntPtr wParam, IntPtr lParam)
        {
            bool fEatMouseEvent = false;
            if (nCode < 0)
            {
                if (_hookId != null) return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
            }
            var wparamTyped = wParam.ToInt32();

            object? o = Marshal.PtrToStructure(lParam, typeof(User32.MSLLHOOKSTRUCT));
            User32.MSLLHOOKSTRUCT p = (User32.MSLLHOOKSTRUCT)(o ?? new User32.MSLLHOOKSTRUCT());

            var eventArguments = new GlobalMouseHookEventArgs((User32.WindowMessage)wparamTyped, p);

            EventHandler<GlobalMouseHookEventArgs>? handler = MouseEvent;
            handler?.Invoke(this, eventArguments);
            fEatMouseEvent = eventArguments.Handled;
            return fEatMouseEvent ? (IntPtr)(-1) : User32.CallNextHookEx(_hookId, nCode, wParam, lParam);

        }
    }

}