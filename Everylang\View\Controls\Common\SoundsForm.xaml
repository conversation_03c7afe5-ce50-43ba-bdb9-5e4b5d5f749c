﻿<UserControl
    x:Class="Everylang.App.View.Controls.Common.SoundsForm"
    x:Name="Me"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    mc:Ignorable="d" x:ClassModifier="internal">
    <Grid  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel>
            <StackPanel VerticalAlignment="Top" Orientation="Horizontal"  Margin="20,10,10,0">
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock
                    Margin="10,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    FontSize="15"
                    Text="{Binding Path=HeaderText, ElementName=Me}"
                    TextWrapping="Wrap" />
            </StackPanel>
            <StackPanel Margin="0,0,0,0">
                <telerik:RadCarousel x:Name="RadCarouselSounds"
                                     Padding="0"
                                     Margin="0"
                                     MinHeight="0"
                                     HorizontalAlignment="Stretch"
                                     VerticalAlignment="Stretch"
                                     ItemsSource="{Binding EmployeesCollection}"
                                     Background="Transparent"
                                     HorizontalScrollBarVisibility="Hidden"
                                     SelectionChanged="RadCarouselSoundsOnSelectionChanged"
                                     VirtualizingStackPanel.IsVirtualizing="False">

                    <telerik:RadCarousel.ItemTemplate>
                        <DataTemplate>
                            <Border 
                                Padding="30,20">
                            <StackPanel HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Top"
                                    FontSize="20"
                                    Text="{Binding}"/>
                                <telerik:RadButton
                                    Margin="0,10,0,0"
                                    IsBackgroundVisible="False"
                                    Padding="0"
                                    Cursor="Hand"
                                    Focusable="False"
                                    MinHeight="0" MinWidth="0"
                                    Width="40" Height="40"
                                    Click="ButtonPlaySound"
                                    HorizontalAlignment="Center"
                                    telerik:CornerRadiusHelper.ClipRadius="30">
                                    <wpf:MaterialIcon Kind="PlayCircle" Width="30" Height="30"/>
                                </telerik:RadButton>
                            </StackPanel>
                            </Border>
                        </DataTemplate>
                    </telerik:RadCarousel.ItemTemplate>
                </telerik:RadCarousel>
                <StackPanel Margin="0,0,0,0" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock
                        Margin="0,0,0,20"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        FontSize="14"
                        Text="{telerik:LocalizableResource Key=SoundFormVolume}"/>
                    <telerik:RadSlider
                        x:Name="SliderVolume"
                        Margin="10,0,0,16"
                        Width="250"
                        VerticalAlignment="Top"
                        AutoToolTipPlacement="BottomRight"
                        Maximum="100"
                        Focusable="False"
                        Minimum="10"
                        Orientation="Horizontal"
                        TickPlacement="TopLeft"
                        ValueChanged="SliderVolume_OnValueChanged" />
                </StackPanel>
                <!--<StackPanel Margin="0,10,0,0" Orientation="Horizontal">
                    <StackPanel>
                        <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SoundFormSelectTrack}" />
                        <telerik:RadListBox
                            Height="400"
                            Width="120"
                            SelectionMode="Single"
                            Focusable="False"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Left"
                            Name="ListBoxSounds"
                            SelectionChanged="ListBoxSounds_OnSelectionChanged">
                            <telerik:RadListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                        <telerik:RadButton
                                            Margin="0,0,0,0"
                                            IsBackgroundVisible="False"
                                            Padding="3"
                                            Cursor="Hand"
                                            MinHeight="0" MinWidth="0"
                                            Click="ButtonPlaySound"
                                            HorizontalAlignment="Right"
                                            telerik:CornerRadiusHelper.ClipRadius="7">
                                                            <wpf:MaterialIcon
                                                                
                                                Width="16"
                                                Height="16"
                                                Kind="PlayCircle" />
                                            <telerik:RadButton.Style>
                                                <Style TargetType="telerik:RadButton">
                                                    <Setter Property="Visibility" Value="Hidden" />
                                                    <Style.Triggers>
                                                        <DataTrigger Value="True" Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}},Path=IsMouseOver}">
                                                            <Setter Property="Visibility" Value="Visible" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </telerik:RadButton.Style>
                                        </telerik:RadButton>
                                    </Grid>
                                </DataTemplate>
                            </telerik:RadListBox.ItemTemplate>

                        </telerik:RadListBox>
                    </StackPanel>
                    <StackPanel Margin="25,0,0,0" Orientation="Vertical">
                        <TextBlock
                            Margin="0,0,0,20"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Text="{telerik:LocalizableResource Key=SoundFormVolume}"/>
                        <telerik:RadSlider
                            x:Name="SliderVolume"
                            Width="250"
                            HorizontalAlignment="Left"
                            AutoToolTipPlacement="BottomRight"
                            Maximum="100"
                            Minimum="10"
                            Orientation="Horizontal"
                            TickPlacement="TopLeft"
                            ValueChanged="SliderVolume_OnValueChanged" />                        
                    </StackPanel>
                </StackPanel>-->
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>