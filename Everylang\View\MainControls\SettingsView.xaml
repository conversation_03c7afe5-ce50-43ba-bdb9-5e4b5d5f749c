﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.MainControls.SettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:helpers1="clr-namespace:Everylang.App.View.Helpers"
    x:ClassModifier="internal">
    <UserControl.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource RadNavigationViewItemStyle}" TargetType="telerik:RadNavigationViewItem">
                <Setter Property="Padding" Value="10,0,10,0" />
                <Setter Property="FontSize" Value="15" />
                <Setter Property="IconVisibility" Value="Collapsed" />
                <Setter Property="MinHeight" Value="26.8" />
                <Setter Property="Margin" Value="0" />
            </Style>
        </ResourceDictionary>

    </UserControl.Resources>
    <Grid>
        <telerik:RadNavigationView
            AutoChangeDisplayMode="False"
            BorderThickness="0"
            CanKeyboardNavigationSelectItems="False"
            DisplayMode="Expanded"
            ExpandedPaneWidth="250"
            Margin="0,0,0,0"
            PaneHeaderHeight="0"
            PaneToggleButtonVisibility="Hidden"
            SelectedIndex="0"
            SubItemsIndentation="5"
            SelectionChanged="NavigationViewOnSelectionChanged"
            x:Name="MuNavigationView">
            <telerik:RadNavigationView.Items>
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=GeneralTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="GeneralTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=AppearanceTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="AppearanceTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=TranslationTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="TranslationTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=CheckSpellingTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="SpellcheckingTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=ProTabs}"
                    FontWeight="Bold"
                    HorizontalContentAlignment="Left"
                    IsEnabled="False" />
                <telerik:RadNavigationViewItem Content="{telerik:LocalizableResource Key=KeyboardLayoutTab}" IsSelectable="False">
                    <telerik:RadNavigationViewItem.Items>
                        <telerik:RadNavigationViewItem
                            Content="{telerik:LocalizableResource Key=SwitcherTab}"
                            HorizontalContentAlignment="Left"
                            x:Name="SwitcherTabItem" />
                        <telerik:RadNavigationViewItem
                            Content="{telerik:LocalizableResource Key=AutoSwitcherTab}"
                            HorizontalContentAlignment="Left"
                            x:Name="AutoSwitchTabItem" />
                        <telerik:RadNavigationViewItem
                            Content="{telerik:LocalizableResource Key=ProgramsSetLayoutTab}"
                            HorizontalContentAlignment="Left"
                            x:Name="TabItemProgramsSetLayout" />
                        <telerik:RadNavigationViewItem
                            Content="{telerik:LocalizableResource Key=LangFlagTab}"
                            HorizontalContentAlignment="Left"
                            x:Name="LangFlagTabItem" />
                    </telerik:RadNavigationViewItem.Items>
                </telerik:RadNavigationViewItem>
                
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=ClipboardTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="ClipboardTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=AutochangeTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="AutochangeTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=MiminoteTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="MiminoteTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=OcrTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="OcrTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=UniversalWindowTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="SmartClickWindowTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=DiareTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="DiareTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=ConverterTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="ConverterTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=CapsTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="CapsTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=ProgramsExceptionsTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="TabItemProgramsExceptions" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=HotkeysMenuItem}"
                    HorizontalContentAlignment="Left"
                    x:Name="TabItemAllHotKeys" />
                <!--<telerik:RadNavigationViewItem HorizontalContentAlignment="Left" IsEnabled="False" Content="{telerik:LocalizableResource Key=ProTabs}"/>-->
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=ProTab}"
                    HorizontalContentAlignment="Left"
                    Margin="0,15,0,0"
                    x:Name="ProTabItem" />
                <telerik:RadNavigationViewItem
                    Content="{telerik:LocalizableResource Key=AboutTab}"
                    HorizontalContentAlignment="Left"
                    x:Name="AboutTabItem" />


            </telerik:RadNavigationView.Items>
        </telerik:RadNavigationView>
    </Grid>
</UserControl>
