﻿using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class ClipboardSettingsViewModel : ViewModelBase
    {
        public string ClipboardPasteWithoutFormattingShortcut
        {
            get => ShortcutManager.GetCharFromKey(SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut);
            set
            {
                SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutView
        {
            get => ShortcutManager.GetCharFromKey(SettingsManager.Settings.ClipboardShowHistoryShortcut);
            set
            {
                SettingsManager.Settings.ClipboardShowHistoryShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutRound
        {
            get => ShortcutManager.GetCharFromKey(SettingsManager.Settings.ClipboardPasteRoundShortcut);
            set
            {
                SettingsManager.Settings.ClipboardPasteRoundShortcut = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(ClipboardPasteWithoutFormattingShortcutIsOn));
                base.OnPropertyChanged(nameof(ClipboardPasteRoundIsOn));
                base.OnPropertyChanged(nameof(ClipboardPasteByIndexIsOn));
            }
        }


        public bool ClipboardPasteWithoutFormattingShortcutIsOn
        {
            get => jgebhdhs && SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcutIsOn;
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcutIsOn = value;
                Clipboard.ClipboardHookManager.Instance.Restart();
                base.OnPropertyChanged();
            }
        }

        public bool ClipboardPasteRoundIsOn
        {
            get => SettingsManager.Settings.ClipboardPasteRoundIsOn;
            set
            {
                SettingsManager.Settings.ClipboardPasteRoundIsOn = value;
                Clipboard.ClipboardHookManager.Instance.Restart();
                base.OnPropertyChanged();
            }
        }

        public bool ClipboardReplaceWithoutChangeClipboard
        {
            get => !SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard;
            set
            {
                SettingsManager.Settings.ClipboardReplaceWithoutChangeClipboard = !value;
                base.OnPropertyChanged();
            }
        }

        public bool ClipboardPasteByIndexIsOn
        {
            get => SettingsManager.Settings.ClipboardPasteByCtrlPlusIndexIsOn;
            set
            {
                SettingsManager.Settings.ClipboardPasteByCtrlPlusIndexIsOn = value;
                Clipboard.ClipboardHookManager.Instance.Restart();
                base.OnPropertyChanged();
            }
        }

        public bool ClipboardSaveFilePath
        {
            get => SettingsManager.Settings.ClipboardSaveFilePath;
            set
            {
                SettingsManager.Settings.ClipboardSaveFilePath = value;
                Clipboard.ClipboardHookManager.Instance.Restart();
                base.OnPropertyChanged();
            }
        }

        public bool ClipboardSaveImage
        {
            get => SettingsManager.Settings.ClipboardSaveImage;
            set
            {
                SettingsManager.Settings.ClipboardSaveImage = value;
                Clipboard.ClipboardHookManager.Instance.Restart();
                base.OnPropertyChanged();
            }
        }

        public int MaxClipboardItems
        {
            get
            {
                if (SettingsManager.Settings.ClipboardMaxClipboardItems == 0)
                {
                    SettingsManager.Settings.ClipboardMaxClipboardItems = 600;
                }
                return SettingsManager.Settings.ClipboardMaxClipboardItems;
            }
            set
            {
                if (value == 0)
                {
                    SettingsManager.Settings.ClipboardMaxClipboardItems = 600;
                }
                else
                {
                    SettingsManager.Settings.ClipboardMaxClipboardItems = value;
                }

                base.OnPropertyChanged();
            }
        }

        public bool ClipboardSountIsOn
        {
            get => SettingsManager.Settings.ClipboardSountIsOn;
            set
            {
                SettingsManager.Settings.ClipboardSountIsOn = value;
                base.OnPropertyChanged();
            }
        }



    }
}
