﻿<Window x:Class="Everylang.Note.WindowNote"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        xmlns:wpf1="clr-namespace:StickyWindows.WPF"
        xmlns:helpers="clr-namespace:Everylang.Note.Helpers"
        xmlns:note="clr-namespace:Everylang.Note"
        x:Name="windowNote"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
       Title="TranslationWindow" MinHeight="150" MinWidth="220" Height="150" Width="220" MaxHeight="1000" MaxWidth="1400"
        ShowInTaskbar="False" ResizeMode="CanResizeWithGrip" Deactivated="OnDeactivated" Activated="OnActivated" Loaded="NoteFormOnLoaded" 
        TextOptions.TextFormattingMode="Display" Opacity="{Binding Path=TransparencyForNotesP, ElementName=windowNote}">
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="0" ResizeBorderThickness="5" />
    </WindowChrome.WindowChrome>
    <b:Interaction.Behaviors>
        <wpf1:StickyWindowBehavior 
            StickOnMove="True"
            StickOnResize="True"
            StickToOther="True"
            StickToScreen="True"/>
    </b:Interaction.Behaviors>
    <Window.Resources>
        <ResourceDictionary>


            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Grey.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--<LinearGradientBrush x:Key="MouseOverBrush" StartPoint="0.5, 0" EndPoint="0.5, 1">
                <GradientStop Color="#22000000" Offset="0" />
                <GradientStop Color="#44000000" Offset="0.4" />
                <GradientStop Color="#55000000" Offset="0.6" />
                <GradientStop Color="#33000000" Offset="0.9" />
                <GradientStop Color="#22000000" Offset="1" />
            </LinearGradientBrush>-->
            <Style x:Key="ToolButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
                <Setter Property="Width" Value="18" />
                <Setter Property="Height" Value="18" />
                <Setter Property="Padding" Value="0"/>
            </Style>

            <helpers:ColorToSolidColorBrushValueConverter  x:Key="ColorToSolidColorBrushValueConverter"/>
            <helpers:BindingProxy x:Key="Primary400Proxy" Data="{DynamicResource Primary400}" />
            <helpers:BindingProxy x:Key="Primary200Proxy" Data="{DynamicResource Primary200}" />
            <helpers:BindingProxy x:Key="Primary100Proxy" Data="{DynamicResource Primary100}" />
            <helpers:BindingProxy x:Key="Primary50Proxy" Data="{DynamicResource Primary50}" />

            <Style x:Key="TextBoxTitleStyle" TargetType="{x:Type TextBox}" BasedOn="{StaticResource {x:Type TextBox}}">
                <Setter Property="SnapsToDevicePixels" Value="True"/>
                <Setter Property="OverridesDefaultStyle" Value="True"/>
                <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
                <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
                <Setter Property="MinWidth" Value="5"/>
                <Setter Property="MinHeight" Value="20"/>
                <Setter Property="AllowDrop" Value="false"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type TextBoxBase}">
                            <Border 
                                  Name="Border"
                                  CornerRadius="2" 
                                  Padding="2"
                                  Background="Transparent"
                                  BorderBrush="Transparent"
                                  BorderThickness="1" >
                                <ScrollViewer Margin="0" x:Name="PART_ContentHost"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="Border" Property="Background" Value="Transparent"/>
                                    <Setter TargetName="Border" Property="BorderBrush" Value="Transparent"/>
                                    <Setter Property="Foreground" Value="Black"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ImageButtonPin" TargetType="Button" BasedOn="{StaticResource ToolButton}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=windowNote, Mode=Default}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialDesign:PackIcon Kind="PinOff" Height="18" Width="18" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=windowNote, Mode=Default}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialDesign:PackIcon Kind="Pin" Height="18" Width="18" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <Style x:Key="ImageButtonCheckOrNote" TargetType="Button" BasedOn="{StaticResource ToolButton}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsCheckList, ElementName=windowNote, Mode=Default}" Value="true">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialDesign:PackIcon Kind="PencilBoxOutline" Height="18" Width="18" />
                            </Setter.Value>
                        </Setter>
                        <Setter Property="ToolTip" Value="{telerik:LocalizableResource Key=NoteConvertToNote}"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsCheckList, ElementName=windowNote, Mode=Default}" Value="false">
                        <Setter Property="Content">
                            <Setter.Value>
                                <materialDesign:PackIcon Kind="CheckboxMarkedOutline" Height="18" Width="18" />
                            </Setter.Value>
                        </Setter>
                        <Setter Property="ToolTip" Value="{telerik:LocalizableResource Key=NoteConvertToTaskList}"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <!--<Style x:Key="ImageButtonFormat" TargetType="Button" BasedOn="{StaticResource ToolButton}">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsNoteSelected, ElementName=noteControl, Mode=Default}" Value="true">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsNoteSelected, ElementName=noteControl, Mode=Default}" Value="false">
                        <Setter Property="Visibility" Value="Collapsed"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <CollectionViewSource x:Key="groups" Source="{Binding CheckListCollection}">
                <CollectionViewSource.GroupDescriptions>
                    <PropertyGroupDescription PropertyName="IsSelectedItem"/>
                </CollectionViewSource.GroupDescriptions>
            </CollectionViewSource>-->

            <!--<Style TargetType="{x:Type GroupItem}" x:Key="NoGroupHeaderStyle">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupItem}">
                            <ItemsPresenter />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style TargetType="{x:Type GroupItem}" x:Key="WithGroupHeaderStyle">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupItem}">
                            <Expander materialDesign:ExpanderAssist.HorizontalHeaderPadding="27,2,20,2" IsExpanded="True" Background="{Binding Source={StaticResource  Primary100Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}">
                                <Expander.Header>
                                    <TextBlock Text="Marked" FontSize="14" Padding="0" />
                                </Expander.Header>
                                <ItemsPresenter/>
                            </Expander>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <note:NullGroupStyleSelector  x:Key="grpStyleSelector" DefaultStyle="{StaticResource WithGroupHeaderStyle}" NullGroupStyle="{StaticResource NoGroupHeaderStyle}"/>-->
        </ResourceDictionary>
    </Window.Resources>


    <Grid Margin="2" Name="grid">
        <Grid.Effect>
            <DropShadowEffect BlurRadius="5" RenderingBias="Quality" ShadowDepth="0" Opacity="0.6"/>
        </Grid.Effect>
        <Grid.RowDefinitions>
            <RowDefinition Height="30" />
            <RowDefinition Height="0" />
            <RowDefinition Height="50*" />
            <RowDefinition Height="7" />
        </Grid.RowDefinitions>

        <Border Name="borderMain" Grid.Row="0" MouseMove="BorderMain_OnMouseMove"  MouseLeave="BorderMain_OnMouseLeave" Background="{Binding Source={StaticResource  Primary200Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}">
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <WrapPanel Margin="0,0,0,0" Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Name="buttonMenu" VerticalAlignment="Center" Margin="5,0,0,0" Click="ButtonMenu_Click"  Style="{StaticResource ToolButton}"  ContextMenuService.IsEnabled="false">
                        <Button.ContextMenu>
                            <ContextMenu Name="contextMenuButtonMenu" Background="{Binding Source={StaticResource  Primary50Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}">
                            </ContextMenu>
                        </Button.ContextMenu>
                        <materialDesign:PackIcon Kind="DotsVertical" Height="18" Width="18" />
                    </Button>
                    <Button Name="buttonAdd" Style="{StaticResource ToolButton}" Click="AddNew" VerticalAlignment="Center" Margin="5,0,0,0" ToolTip="{telerik:LocalizableResource Key=NotesAddNew}">
                        <materialDesign:PackIcon Kind="Plus" Height="18" Width="18" />
                    </Button>
                    <Button Name="buttonToList" Style="{StaticResource ImageButtonCheckOrNote}" Click="ToListOrNote" VerticalAlignment="Center" Margin="5,0,0,0" />
                </WrapPanel>
                <TextBox  Name="textBoxTitle" Style="{StaticResource TextBoxTitleStyle}" Grid.Column="1" Margin="5,0,0,0" VerticalAlignment="Center" FontSize="14" Cursor="Arrow" MouseMove="TextBoxTitle_OnMouseMove" MouseDoubleClick="TextBoxTitle_OnMouseDoubleClick" ContextMenuService.IsEnabled="false" Opacity="0.70" PreviewMouseUp="TextBoxTitle_OnMouseUp"/>
                <WrapPanel Margin="0,0,5,0" Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" FlowDirection="RightToLeft">
                    <Button Name="buttonClose" Style="{StaticResource ToolButton}" Click="Close" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" ToolTip="{telerik:LocalizableResource Key=Close}">
                        <materialDesign:PackIcon Kind="Close" Height="18" Width="18" />
                    </Button>
                    <Button Name="buttonPin" Style="{StaticResource ImageButtonPin}" Click="ButtonPin_OnClick" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" ToolTip="{telerik:LocalizableResource Key=StayOnTopButton}"/>
                </WrapPanel>
            </Grid>
        </Border>


        <StackPanel  Grid.Row="1" Orientation="Vertical">
            <Rectangle HorizontalAlignment="Stretch" Stroke="{Binding Source={StaticResource  Primary400Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" Fill="{Binding Source={StaticResource  Primary400Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" Height="1"/>
        </StackPanel>
        <note:NoteControl x:Name="noteControl" Grid.Row="2" />
        <Border Name="border"  Grid.Row="3" MouseLeftButtonDown="BorderMouseLeftButtonDown" Background="{Binding Source={StaticResource  Primary50Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}"/>

    </Grid>

</Window>

