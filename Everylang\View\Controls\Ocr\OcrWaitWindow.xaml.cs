﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Common.RichTextBoxEx;
using System;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Vanara.PInvoke;
using Application = System.Windows.Application;
using Timer = System.Timers.Timer;

namespace Everylang.App.View.Controls.Ocr
{
    /// <summary>
    /// Логика взаимодействия для OcrWaitWindow.xaml
    /// </summary>
    internal partial class OcrWaitWindow
    {

        internal static readonly DependencyProperty IsVisibleProgressProperty =
            DependencyProperty.Register("IsVisibleProgress",
                typeof(bool),
                typeof(OcrWaitWindow),
                new FrameworkPropertyMetadata());

        internal bool IsVisibleProgress
        {
            get { return (bool)GetValue(IsVisibleProgressProperty); }
            set
            {
                SetValue(IsVisibleProgressProperty, value);
            }
        }

        internal static readonly DependencyProperty IsVisibleResultProperty =
            DependencyProperty.Register("IsVisibleResult",
                typeof(bool),
                typeof(OcrWaitWindow),
                new FrameworkPropertyMetadata());

        internal bool IsVisibleResult
        {
            get { return (bool)GetValue(IsVisibleResultProperty); }
            set
            {
                SetValue(IsVisibleResultProperty, value);
            }
        }

        private readonly Timer _timer;

        internal OcrWaitWindow()
        {
            InitializeComponent();
            IsVisibleProgress = true;
            IsVisibleResult = false;
            Opened += OnOpened;
            HookCallBackKeyDown.CallbackEventHandler += OnKeyDown;
            resTextBox.CaretBrush = new SolidColorBrush(System.Windows.Media.Color.FromArgb(0, 0, 0, 0));
            _mouseEntered = false;
            _timer = new Timer();
            _timer.Interval = 3000;
            _timer.Elapsed += (_, _) =>
            {
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)CloseThis);
            };
        }

        private void OnKeyDown(GlobalKeyEventArgs e)
        {
            if (e.KeyCode == VirtualKeycodes.C && (e.Control == ModifierKeySide.Left || e.Control == ModifierKeySide.Right))
            {
                var sel = resTextBox.Selection.Text;
                if (!string.IsNullOrEmpty(sel))
                {
                    ClipboardOperations.SetText(sel);
                    e.Handled = true;
                }
            }
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;

                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void CloseThis()
        {
            _timer.Stop();
            HookCallBackKeyDown.CallbackEventHandler -= OnKeyDown;
            IsOpen = false;
        }

        private Bitmap? _image;

        internal async void Show(Bitmap? bitmapArea)
        {
            if (bitmapArea == null)
            {
                return;
            }
            IsOpen = true;
            _image = bitmapArea;
            System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
            HorizontalOffset = centrePos.X - this.Width / 2;
            VerticalOffset = centrePos.Y - this.Height / 2;
            IsVisibleProgress = true;
            OcrWorker ocrEngine = new OcrWorker();
            var text = await ocrEngine.ReadImage(bitmapArea, SettingsManager.Settings.OcrLangsList);
            await Task.Delay(300);
            if (!string.IsNullOrEmpty(text))
            {
                ClipboardOperations.SetText(text);
                LabelMu.Content = LocalizationManager.GetString("OcrWaitResult");
                resTextBox.Text = text;
            }
            else
            {
                LabelMu.Content = LocalizationManager.GetString("OcrWaitResultFail");
            }

            var formattedText = resTextBox.Document.GetFormattedText(this);
            this.Width = formattedText!.WidthIncludingTrailingWhitespace + 35;
            this.Height = formattedText.Height + 10;
            IsVisibleProgress = false;
            IsVisibleResult = true;
            _timer.Start();
        }

        bool _mouseEntered;

        private void me_MouseEnter(object sender, MouseEventArgs e)
        {
            _mouseEntered = true;
            _timer.Stop();
        }

        private void me_MouseLeave(object sender, MouseEventArgs e)
        {
            if (_mouseEntered && IsVisibleResult) CloseThis();
        }

        private void EditImage_OnClick(object sender, RoutedEventArgs e)
        {
            if (_image != null) GlobalEventsApp.OnEventOcrMainWindow(_image);
            CloseThis();
        }

    }
}
