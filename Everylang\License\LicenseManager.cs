﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.ViewModels;

namespace Everylang.App.License
{
    internal static class LicenseManager
    {
        internal static void Init()
        {
            CheckHash.Start();
            PropNames += nameof(VMContainer.Instance.ClipboardViewModel) + nameof(VMContainer.Instance.ClipboardViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.ClipboardSettingsViewModel) + nameof(VMContainer.Instance.ClipboardSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.DiaryViewModel) + nameof(VMContainer.Instance.DiaryViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.UniversalWindowSettingsViewModel) + nameof(VMContainer.Instance.UniversalWindowSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.LangFlagSettingsViewModel) + nameof(VMContainer.Instance.LangFlagSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.SnippetsViewModel) + nameof(VMContainer.Instance.SnippetsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.AutoSwitcherSettingsViewModel) + nameof(VMContainer.Instance.AutoSwitcherSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.ConverterSettingsViewModel) + nameof(VMContainer.Instance.ConverterSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.SwitcherSettingsViewModel) + nameof(VMContainer.Instance.SwitcherSettingsViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.ProgramsSetLayoutViewModel) + nameof(VMContainer.Instance.ProgramsSetLayoutViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.NotesListControlViewModel) + nameof(VMContainer.Instance.NotesListControlViewModel.jgebhdhs);
            PropNames += nameof(VMContainer.Instance.OcrViewModel) + nameof(VMContainer.Instance.OcrViewModel.jgebhdhs);

            GlobalEventsApp.EventPropPro += CheckLic;
            GlobalEventsApp.EventPro += GlobalEventsAppOnEventPro;
        }

        private static void GlobalEventsAppOnEventPro(string obj)
        {
            VMContainer.Instance.ClipboardViewModel.OnPropertyChanged(nameof(VMContainer.Instance.ClipboardViewModel.jgebhdhs));
            VMContainer.Instance.ClipboardSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.ClipboardSettingsViewModel.jgebhdhs));
            VMContainer.Instance.DiaryViewModel.OnPropertyChanged(nameof(VMContainer.Instance.DiaryViewModel.jgebhdhs));
            VMContainer.Instance.UniversalWindowSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.UniversalWindowSettingsViewModel.jgebhdhs));
            VMContainer.Instance.LangFlagSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.LangFlagSettingsViewModel.jgebhdhs));
            VMContainer.Instance.SnippetsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.SnippetsViewModel.jgebhdhs));
            VMContainer.Instance.AutoSwitcherSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.AutoSwitcherSettingsViewModel.jgebhdhs));
            VMContainer.Instance.ConverterSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.ConverterSettingsViewModel.jgebhdhs));
            VMContainer.Instance.SwitcherSettingsViewModel.OnPropertyChanged(nameof(VMContainer.Instance.SwitcherSettingsViewModel.jgebhdhs));
            VMContainer.Instance.ProgramsSetLayoutViewModel.OnPropertyChanged(nameof(VMContainer.Instance.ProgramsSetLayoutViewModel.jgebhdhs));
            VMContainer.Instance.NotesListControlViewModel.OnPropertyChanged(nameof(VMContainer.Instance.NotesListControlViewModel.jgebhdhs));
            VMContainer.Instance.OcrViewModel.OnPropertyChanged(nameof(VMContainer.Instance.OcrViewModel.jgebhdhs));
        }

        internal static string PropNames = null!;

        private static async void CheckLic(object ob, string classN, string prop)
        {
            if (PropNames.Contains(classN + prop))
            {
                var qwe = await LicHelper.Check();
                if (!qwe || !SettingsManager.LicIsActivated)
                {
                    VMContainer.Instance.RunProp(ob, classN, prop, false);
                }
                else
                {
                    VMContainer.Instance.RunProp(ob, classN, prop, true);
                }
            }
        }
    }
}
