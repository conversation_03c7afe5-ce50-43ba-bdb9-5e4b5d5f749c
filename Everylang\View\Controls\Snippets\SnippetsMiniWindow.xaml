﻿<Window x:Class="Everylang.App.View.Controls.Snippets.SnippetsMiniWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="SnippetsMiniWindow" Height="25" Width="25"  Topmost="True" 
        ShowInTaskbar="False" WindowStyle="None" ResizeMode="NoResize" AllowsTransparency="True" Background="Transparent" ShowActivated="False" Focusable="False"
        x:ClassModifier="internal">
    <Grid>
        <TextBlock Name="TextBlock" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="#737070"></TextBlock>
    </Grid>
</Window>
