﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Everylang.Common.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class LangResource_ru {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public LangResource_ru() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Everylang.Common.Localization.LangResource.ru", typeof(LangResource_ru).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to О программе.
        /// </summary>
        public static string About {
            get {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть окно приветствия.
        /// </summary>
        public static string AboutOpenStartWindow {
            get {
                return ResourceManager.GetString("AboutOpenStartWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сброс всех настроек программы.
        /// </summary>
        public static string AboutResetSettings {
            get {
                return ResourceManager.GetString("AboutResetSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сбросить все настройки и данные программы?.
        /// </summary>
        public static string AboutResetSettingsQuestion {
            get {
                return ResourceManager.GetString("AboutResetSettingsQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to КОМПОНЕНТЫ.
        /// </summary>
        public static string AboutSettingsAckowledgementsHeader {
            get {
                return ResourceManager.GetString("AboutSettingsAckowledgementsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ОБРАТНАЯ СВЯЗЬ.
        /// </summary>
        public static string AboutSettingsContactHeader {
            get {
                return ResourceManager.GetString("AboutSettingsContactHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Контактная форма.
        /// </summary>
        public static string AboutSettingsContactLink {
            get {
                return ResourceManager.GetString("AboutSettingsContactLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Напишите ваши комментарии и/или вопросы.
        /// </summary>
        public static string AboutSettingsContactText {
            get {
                return ResourceManager.GetString("AboutSettingsContactText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Позволяет переводить тексты, проверять орфографию и переключать раскладку в любых сторонних программах.
        /// </summary>
        public static string AboutSettingsDesc {
            get {
                return ResourceManager.GetString("AboutSettingsDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to О ПРОГРАММЕ.
        /// </summary>
        public static string AboutSettingsHeader {
            get {
                return ResourceManager.GetString("AboutSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программа запущена от имени администратора.
        /// </summary>
        public static string AboutSettingsIsAdmin {
            get {
                return ResourceManager.GetString("AboutSettingsIsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программа запущена не от имени администратора.
        /// </summary>
        public static string AboutSettingsIsNotAdmin {
            get {
                return ResourceManager.GetString("AboutSettingsIsNotAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Лицензионное соглашение.
        /// </summary>
        public static string AboutSettingsLicense {
            get {
                return ResourceManager.GetString("AboutSettingsLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обновить.
        /// </summary>
        public static string AboutSettingsUpdate {
            get {
                return ResourceManager.GetString("AboutSettingsUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Доступно обновление.
        /// </summary>
        public static string AboutSettingsUpdateAvailable {
            get {
                return ResourceManager.GetString("AboutSettingsUpdateAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Версия:.
        /// </summary>
        public static string AboutSettingsVersion {
            get {
                return ResourceManager.GetString("AboutSettingsVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to О программе.
        /// </summary>
        public static string AboutTab {
            get {
                return ResourceManager.GetString("AboutTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Прилагательное.
        /// </summary>
        public static string adjective {
            get {
                return ResourceManager.GetString("adjective", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Наречие.
        /// </summary>
        public static string adverb {
            get {
                return ResourceManager.GetString("adverb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Все.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Все программы.
        /// </summary>
        public static string AllApp {
            get {
                return ResourceManager.GetString("AllApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Внешний вид.
        /// </summary>
        public static string AppearanceTab {
            get {
                return ResourceManager.GetString("AppearanceTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Для корректной работы программы со всеми приложениями, необходимо запустить ее от имени администратора.
        /// </summary>
        public static string AppNotAsAdmin {
            get {
                return ResourceManager.GetString("AppNotAsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Приложение обновлено, список изменений на сайте.
        /// </summary>
        public static string AppUpdated {
            get {
                return ResourceManager.GetString("AppUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить.
        /// </summary>
        public static string AutochangeAddNew {
            get {
                return ResourceManager.GetString("AutochangeAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Учитывать регистр букв.
        /// </summary>
        public static string AutochangeCaseLetters {
            get {
                return ResourceManager.GetString("AutochangeCaseLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменять по:.
        /// </summary>
        public static string AutochangeChangeMethods {
            get {
                return ResourceManager.GetString("AutochangeChangeMethods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить.
        /// </summary>
        public static string AutochangeDelete {
            get {
                return ResourceManager.GetString("AutochangeDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить все шаблоны с данным тегом?.
        /// </summary>
        public static string AutochangeDelWithTag {
            get {
                return ResourceManager.GetString("AutochangeDelWithTag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменить.
        /// </summary>
        public static string AutochangeEdit {
            get {
                return ResourceManager.GetString("AutochangeEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Редактор шаблона.
        /// </summary>
        public static string AutochangeEditor {
            get {
                return ResourceManager.GetString("AutochangeEditor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Что заменять.
        /// </summary>
        public static string AutochangeHeaderFromText {
            get {
                return ResourceManager.GetString("AutochangeHeaderFromText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На что заменять.
        /// </summary>
        public static string AutochangeHeaderToText {
            get {
                return ResourceManager.GetString("AutochangeHeaderToText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отмена.
        /// </summary>
        public static string AutochangeHelperCancel {
            get {
                return ResourceManager.GetString("AutochangeHelperCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменять при наборе.
        /// </summary>
        public static string AutochangeHelperChangeAtOnce {
            get {
                return ResourceManager.GetString("AutochangeHelperChangeAtOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Описание:.
        /// </summary>
        public static string AutochangeHelperDesc {
            get {
                return ResourceManager.GetString("AutochangeHelperDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст для замены (необязательно):.
        /// </summary>
        public static string AutochangeHelperFromText {
            get {
                return ResourceManager.GetString("AutochangeHelperFromText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст для замены:.
        /// </summary>
        public static string AutochangeHelperFromTextTootlTip {
            get {
                return ResourceManager.GetString("AutochangeHelperFromTextTootlTip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На какой язык переключать раскладку.
        /// </summary>
        public static string AutochangeHelperLangListDesc {
            get {
                return ResourceManager.GetString("AutochangeHelperLangListDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не переключать.
        /// </summary>
        public static string AutochangeHelperLangListNoSwitch {
            get {
                return ResourceManager.GetString("AutochangeHelperLangListNoSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранить.
        /// </summary>
        public static string AutochangeHelperOk {
            get {
                return ResourceManager.GetString("AutochangeHelperOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранять позицию курсора.
        /// </summary>
        public static string AutochangeHelperSaveCursorPosition {
            get {
                return ResourceManager.GetString("AutochangeHelperSaveCursorPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Тэги (через пробел):.
        /// </summary>
        public static string AutochangeHelperTags {
            get {
                return ResourceManager.GetString("AutochangeHelperTags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Тэги.
        /// </summary>
        public static string AutochangeHelperTagsWatermark {
            get {
                return ResourceManager.GetString("AutochangeHelperTagsWatermark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст шаблона:.
        /// </summary>
        public static string AutochangeHelperToText {
            get {
                return ResourceManager.GetString("AutochangeHelperToText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сортировать в зависимости от частоты использования.
        /// </summary>
        public static string AutochangeIsEnabledCountUsage {
            get {
                return ResourceManager.GetString("AutochangeIsEnabledCountUsage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шаблоны включены.
        /// </summary>
        public static string AutochangeIsOn {
            get {
                return ResourceManager.GetString("AutochangeIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть список шаблонов для вставки.
        /// </summary>
        public static string AutochangeKeyboardShortcuts {
            get {
                return ResourceManager.GetString("AutochangeKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить выделенный текст в шаблоны.
        /// </summary>
        public static string AutochangeKeyboardShortcutsAddNew {
            get {
                return ResourceManager.GetString("AutochangeKeyboardShortcutsAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Включена автозамена.
        /// </summary>
        public static string AutochangeOnInSnippetsList {
            get {
                return ResourceManager.GetString("AutochangeOnInSnippetsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Клавиша Enter.
        /// </summary>
        public static string AutochangeOnInter {
            get {
                return ResourceManager.GetString("AutochangeOnInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пробел.
        /// </summary>
        public static string AutochangeOnSpace {
            get {
                return ResourceManager.GetString("AutochangeOnSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пробел или Enter.
        /// </summary>
        public static string AutochangeOnSpaceOrInter {
            get {
                return ResourceManager.GetString("AutochangeOnSpaceOrInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Клавиша Tab.
        /// </summary>
        public static string AutochangeOnTab {
            get {
                return ResourceManager.GetString("AutochangeOnTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tab или Enter.
        /// </summary>
        public static string AutochangeOnTabOrInter {
            get {
                return ResourceManager.GetString("AutochangeOnTabOrInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменять при наборе в другой раскладке.
        /// </summary>
        public static string AutochangeOtherLayout {
            get {
                return ResourceManager.GetString("AutochangeOtherLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать подсказку при наборе текста.
        /// </summary>
        public static string AutochangeShowMiniWindow {
            get {
                return ResourceManager.GetString("AutochangeShowMiniWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Редактировать шаблоны.
        /// </summary>
        public static string AutochangeSnippetsList {
            get {
                return ResourceManager.GetString("AutochangeSnippetsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шаблоны.
        /// </summary>
        public static string AutochangeTab {
            get {
                return ResourceManager.GetString("AutochangeTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шаблоны.
        /// </summary>
        public static string AutochangeTextHeader {
            get {
                return ResourceManager.GetString("AutochangeTextHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить слово в правила автопереключения? Enter - ДА.
        /// </summary>
        public static string AutoSwitchAcceptText {
            get {
                return ResourceManager.GetString("AutoSwitchAcceptText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Действие.
        /// </summary>
        public static string AutoSwitcherSettingsAction {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключать раскладку после остановки ввода слова.
        /// </summary>
        public static string AutoSwitcherSettingsAfterPause {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAfterPause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Все раскладки.
        /// </summary>
        public static string AutoSwitcherSettingsAllLayouts {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAllLayouts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст.
        /// </summary>
        public static string AutoSwitcherSettingsCombination {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsCombination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Количество ручных переключений слова для включения в правила.
        /// </summary>
        public static string AutoSwitcherSettingsCountCheckRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsCountCheckRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не исправлять раскладку, если до этого она была изменена вручную.
        /// </summary>
        public static string AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключение раскладки.
        /// </summary>
        public static string AutoSwitcherSettingsHeader {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правила автопереключения.
        /// </summary>
        public static string AutoSwitcherSettingsHelpWindowTitle {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsHelpWindowTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автоматически добавлять правила переключения.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnAddingRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnAddingRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Исправлять после нажатия на Enter.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnByEnter {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnByEnter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Исправлять случайное нажатие Caps Lock.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnFixWrongUpperCase {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnFixWrongUpperCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Исправлять две заглавные буквы в начале слова.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnTwoUpperCaseLetters {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnTwoUpperCaseLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не исправлять, если все буквы в слове заглавные.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnUpperCaseNotSwitch {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnUpperCaseNotSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавлять в правила однобуквенные слова.
        /// </summary>
        public static string AutoSwitcherSettingsIsSwitchOneLetter {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsSwitchOneLetter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Список правил для автопереключения.
        /// </summary>
        public static string AutoSwitcherSettingsListRulesHeader {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsListRulesHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать кандидатов.
        /// </summary>
        public static string AutoSwitcherSettingsListRulesHeaderShowAll {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsListRulesHeaderShowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключать раскладку только после ввода слова целиком.
        /// </summary>
        public static string AutoSwitcherSettingsOnlyAfterSeparator {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsOnlyAfterSeparator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть список правил автопереключения.
        /// </summary>
        public static string AutoSwitcherSettingsOpenRulesList {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsOpenRulesList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить все правила переключения.
        /// </summary>
        public static string AutoSwitcherSettingsResetRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsResetRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключать.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionConvert {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionConvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Кандидат.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionIntermediate {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionIntermediate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не переключать.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionNotConvert {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionNotConvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавлять правила только после подтверждении.
        /// </summary>
        public static string AutoSwitcherSettingsShowAcceptWindow {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsShowAcceptWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Список языков для которых будет работать автопереключение.
        /// </summary>
        public static string AutoSwitcherSettingsTrueListOfLang {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsTrueListOfLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключение раскладки.
        /// </summary>
        public static string AutoSwitcherTab {
            get {
                return ResourceManager.GetString("AutoSwitcherTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключение раскладки выключено.
        /// </summary>
        public static string AutoSwitchSettingsIsOff {
            get {
                return ResourceManager.GetString("AutoSwitchSettingsIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключение раскладки включено.
        /// </summary>
        public static string AutoSwitchSettingsIsOn {
            get {
                return ResourceManager.GetString("AutoSwitchSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вспомогательный глагол.
        /// </summary>
        public static string auxiliary_verb {
            get {
                return ResourceManager.GetString("auxiliary verb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Доступна новая версия, перезапустите приложение.
        /// </summary>
        public static string AvailableNewVersion {
            get {
                return ResourceManager.GetString("AvailableNewVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать.
        /// </summary>
        public static string bCopy {
            get {
                return ResourceManager.GetString("bCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Разбить текст по символу перевода строки.
        /// </summary>
        public static string BreakInterButton {
            get {
                return ResourceManager.GetString("BreakInterButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Разбить текст по пробелу.
        /// </summary>
        public static string BreakSpaceButton {
            get {
                return ResourceManager.GetString("BreakSpaceButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменить.
        /// </summary>
        public static string bReplace {
            get {
                return ResourceManager.GetString("bReplace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменить все.
        /// </summary>
        public static string bReplaceAll {
            get {
                return ResourceManager.GetString("bReplaceAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставить текст.
        /// </summary>
        public static string bReplaceText {
            get {
                return ResourceManager.GetString("bReplaceText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пропустить.
        /// </summary>
        public static string bSkip {
            get {
                return ResourceManager.GetString("bSkip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пропустить все.
        /// </summary>
        public static string bSkipAll {
            get {
                return ResourceManager.GetString("bSkipAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вернуть.
        /// </summary>
        public static string buttonBack {
            get {
                return ResourceManager.GetString("buttonBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрыть.
        /// </summary>
        public static string ButtonClose {
            get {
                return ResourceManager.GetString("ButtonClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отмена.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CAPSLOCK включен.
        /// </summary>
        public static string CapsLockIsOn {
            get {
                return ResourceManager.GetString("CapsLockIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка орфографии.
        /// </summary>
        public static string CheckSpellingTab {
            get {
                return ResourceManager.GetString("CheckSpellingTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Очистить все.
        /// </summary>
        public static string ClearAll {
            get {
                return ResourceManager.GetString("ClearAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Очистить.
        /// </summary>
        public static string ClearButton {
            get {
                return ResourceManager.GetString("ClearButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to БУФЕР ОБМЕНА.
        /// </summary>
        public static string ClipboardHeaderButton {
            get {
                return ResourceManager.GetString("ClipboardHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Последовательная вставка текста из буфера обмена для текущего окна (каждая последующая вставка будет предыдущим значением из буфера обмена).
        /// </summary>
        public static string ClipboardKeyboardRoundShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardRoundShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Последовательная вставка текста из буфера обмена.
        /// </summary>
        public static string ClipboardKeyboardRoundShortcutsShort {
            get {
                return ResourceManager.GetString("ClipboardKeyboardRoundShortcutsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставка текста без форматированияи и вставка пути скопированного файла.
        /// </summary>
        public static string ClipboardKeyboardShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открывать историю буфера обмена по сочетанию.
        /// </summary>
        public static string ClipboardKeyboardViewShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardViewShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер истории буфера обмена.
        /// </summary>
        public static string ClipboardMaxClipboardItems {
            get {
                return ResourceManager.GetString("ClipboardMaxClipboardItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Буфер обмена.
        /// </summary>
        public static string ClipboardMenuItem {
            get {
                return ResourceManager.GetString("ClipboardMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Менеджер буфера обмена выключен.
        /// </summary>
        public static string ClipboardOff {
            get {
                return ResourceManager.GetString("ClipboardOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Менеджер буфера обмена включен.
        /// </summary>
        public static string ClipboardOn {
            get {
                return ResourceManager.GetString("ClipboardOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Менеджер буфера обмена.
        /// </summary>
        public static string ClipboardSettingsHeader {
            get {
                return ResourceManager.GetString("ClipboardSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставлять текст по Ctrl+Shift+(число) - число 1, 2, 3, 4, 5, 6, 7, 8, 9 - индекс записи в истории буфера обмена.
        /// </summary>
        public static string ClipboardSettingsPasteByIndexIsOn {
            get {
                return ResourceManager.GetString("ClipboardSettingsPasteByIndexIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to При вставке текста из истории не заменять текущее значение в буфере обмена.
        /// </summary>
        public static string ClipboardSettingsReplaceWithoutChangeClipboard {
            get {
                return ResourceManager.GetString("ClipboardSettingsReplaceWithoutChangeClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранять в истории буфера обмена путь к скопированному файлу.
        /// </summary>
        public static string ClipboardSettingsSaveFilePath {
            get {
                return ResourceManager.GetString("ClipboardSettingsSaveFilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранять в истории буфера изображения.
        /// </summary>
        public static string ClipboardSettingsSaveImage {
            get {
                return ResourceManager.GetString("ClipboardSettingsSaveImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Звук изменения буфера обмена.
        /// </summary>
        public static string ClipboardSound {
            get {
                return ResourceManager.GetString("ClipboardSound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Буфер обмена.
        /// </summary>
        public static string ClipboardTab {
            get {
                return ResourceManager.GetString("ClipboardTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрыть.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрыть.
        /// </summary>
        public static string CloseHeaderButton {
            get {
                return ResourceManager.GetString("CloseHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрыть программу?.
        /// </summary>
        public static string CloseQuestion {
            get {
                return ResourceManager.GetString("CloseQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Для вставки текста нажмите на клавишу.
        /// </summary>
        public static string CommonWindowPressKeyForPast {
            get {
                return ResourceManager.GetString("CommonWindowPressKeyForPast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Союз.
        /// </summary>
        public static string conjunction {
            get {
                return ResourceManager.GetString("conjunction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить.
        /// </summary>
        public static string ConverterAdd {
            get {
                return ResourceManager.GetString("ConverterAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить.
        /// </summary>
        public static string ConverterDelete {
            get {
                return ResourceManager.GetString("ConverterDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Справа.
        /// </summary>
        public static string ConverterEnd {
            get {
                return ResourceManager.GetString("ConverterEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Поиск и замена в выделенном тексте.
        /// </summary>
        public static string ConverterReplaceSelText {
            get {
                return ResourceManager.GetString("ConverterReplaceSelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to текст для примера.
        /// </summary>
        public static string ConverterSampleText {
            get {
                return ResourceManager.GetString("ConverterSampleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Преобразование текста в CamelCase стиле.
        /// </summary>
        public static string ConverterSettingsCamelCase {
            get {
                return ResourceManager.GetString("ConverterSettingsCamelCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обрамление выделенного текста.
        /// </summary>
        public static string ConverterSettingsEncloseTextQuotationMarks {
            get {
                return ResourceManager.GetString("ConverterSettingsEncloseTextQuotationMarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер чисел и дат в строки, вычисление выражений.
        /// </summary>
        public static string ConverterSettingsExpression {
            get {
                return ResourceManager.GetString("ConverterSettingsExpression", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер текста.
        /// </summary>
        public static string ConverterSettingsHeader {
            get {
                return ResourceManager.GetString("ConverterSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to К нижнему регистру первый символ слова под курсором.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsFirstLetterToDown {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to К верхнему регистру первый символ слова под курсором.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsFirstLetterToUp {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настроить горячие клавиши.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchCapsSettings {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchCapsSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Привести к нижнему регистру выделенный текст.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Инвертировать регистр выделенного текста.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Привести к верхнему регистру выделенный текст.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Транслитерация выделенного текста.
        /// </summary>
        public static string ConverterSettingsTransliteration {
            get {
                return ResourceManager.GetString("ConverterSettingsTransliteration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Слева.
        /// </summary>
        public static string ConverterStart {
            get {
                return ResourceManager.GetString("ConverterStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер текста.
        /// </summary>
        public static string ConverterTab {
            get {
                return ResourceManager.GetString("ConverterTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать.
        /// </summary>
        public static string CopyButton {
            get {
                return ResourceManager.GetString("CopyButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать Html.
        /// </summary>
        public static string CopyButtonHtml {
            get {
                return ResourceManager.GetString("CopyButtonHtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать Rtf.
        /// </summary>
        public static string CopyButtonRtf {
            get {
                return ResourceManager.GetString("CopyButtonRtf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать переведенный текст.
        /// </summary>
        public static string CopyTranslatedText {
            get {
                return ResourceManager.GetString("CopyTranslatedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник.
        /// </summary>
        public static string DiareTab {
            get {
                return ResourceManager.GetString("DiareTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ПРИЛОЖЕНИЕ.
        /// </summary>
        public static string DiaryHeaderApp {
            get {
                return ResourceManager.GetString("DiaryHeaderApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ДНЕВНИК.
        /// </summary>
        public static string DiaryHeaderButton {
            get {
                return ResourceManager.GetString("DiaryHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ДАТА.
        /// </summary>
        public static string DiaryHeaderDate {
            get {
                return ResourceManager.GetString("DiaryHeaderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ТИП.
        /// </summary>
        public static string DiaryHeaderFormat {
            get {
                return ResourceManager.GetString("DiaryHeaderFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ТЕКСТ.
        /// </summary>
        public static string DiaryHeaderText {
            get {
                return ResourceManager.GetString("DiaryHeaderText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник включен.
        /// </summary>
        public static string DiaryIsOn {
            get {
                return ResourceManager.GetString("DiaryIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Количество записей в дневнике.
        /// </summary>
        public static string DiaryMaxItems {
            get {
                return ResourceManager.GetString("DiaryMaxItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник.
        /// </summary>
        public static string DiaryMenuItem {
            get {
                return ResourceManager.GetString("DiaryMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник выключен.
        /// </summary>
        public static string DiaryOff {
            get {
                return ResourceManager.GetString("DiaryOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введенный пароль не верен, сбросить текущий пароль? При этом все данные из дневника будут удалены.
        /// </summary>
        public static string DiaryOldPasswordWrong {
            get {
                return ResourceManager.GetString("DiaryOldPasswordWrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник включен.
        /// </summary>
        public static string DiaryOn {
            get {
                return ResourceManager.GetString("DiaryOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пароль на дневник.
        /// </summary>
        public static string DiaryPassword {
            get {
                return ResourceManager.GetString("DiaryPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите старый пароль на дневник.
        /// </summary>
        public static string DiaryPasswordOld {
            get {
                return ResourceManager.GetString("DiaryPasswordOld", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пароль сброшен.
        /// </summary>
        public static string DiaryPasswordReset {
            get {
                return ResourceManager.GetString("DiaryPasswordReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Новый пароль сохранен.
        /// </summary>
        public static string DiaryPasswordSaved {
            get {
                return ResourceManager.GetString("DiaryPasswordSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник.
        /// </summary>
        public static string DiarySettingsHeader {
            get {
                return ResourceManager.GetString("DiarySettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть дневник.
        /// </summary>
        public static string DiaryShortcuts {
            get {
                return ResourceManager.GetString("DiaryShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменить.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Английский.
        /// </summary>
        public static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Невозможно зарегистрировать комбинацию клавиш.
        /// </summary>
        public static string ErrorHotkey {
            get {
                return ResourceManager.GetString("ErrorHotkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Произошла ошибка, приложение будет завершено. Пожалуйста отправьте текст с ошибками на адрес <EMAIL>.
        /// </summary>
        public static string ErrorText {
            get {
                return ResourceManager.GetString("ErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выход.
        /// </summary>
        public static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите на цифру для вставки текста.
        /// </summary>
        public static string FastActionIndex {
            get {
                return ResourceManager.GetString("FastActionIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tab - Поиск.  Esc - Отмена и очистка.
        /// </summary>
        public static string FastActionTextWindowSearch {
            get {
                return ResourceManager.GetString("FastActionTextWindowSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активирован пробный период на 40 дней, включены все функции.
        /// </summary>
        public static string FirstStartWithEvaluate {
            get {
                return ResourceManager.GetString("FirstStartWithEvaluate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрепить окно.
        /// </summary>
        public static string FixWindow {
            get {
                return ResourceManager.GetString("FixWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Французский.
        /// </summary>
        public static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to С языка:.
        /// </summary>
        public static string FromLang {
            get {
                return ResourceManager.GetString("FromLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Папка для сохранения настроек программы.
        /// </summary>
        public static string GeneralSettingsDataFilePath {
            get {
                return ResourceManager.GetString("GeneralSettingsDataFilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Экспорт.
        /// </summary>
        public static string GeneralSettingsExport {
            get {
                return ResourceManager.GetString("GeneralSettingsExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Экспорт-Импорт настроек.
        /// </summary>
        public static string GeneralSettingsExportImport {
            get {
                return ResourceManager.GetString("GeneralSettingsExportImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шрифт.
        /// </summary>
        public static string GeneralSettingsFont {
            get {
                return ResourceManager.GetString("GeneralSettingsFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Общие настройки.
        /// </summary>
        public static string GeneralSettingsHeader {
            get {
                return ResourceManager.GetString("GeneralSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Импорт.
        /// </summary>
        public static string GeneralSettingsImport {
            get {
                return ResourceManager.GetString("GeneralSettingsImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверять обновления.
        /// </summary>
        public static string GeneralSettingsIsCheckUpdate {
            get {
                return ResourceManager.GetString("GeneralSettingsIsCheckUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обновлять до бета-версии.
        /// </summary>
        public static string GeneralSettingsIsCheckUpdateBeta {
            get {
                return ResourceManager.GetString("GeneralSettingsIsCheckUpdateBeta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать системные настройки прокси.
        /// </summary>
        public static string GeneralSettingsIsProxyUseIE {
            get {
                return ResourceManager.GetString("GeneralSettingsIsProxyUseIE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отключить все функции в программах запущенных в полноэкранном режиме.
        /// </summary>
        public static string GeneralSettingsIsStopWorkingFullScreen {
            get {
                return ResourceManager.GetString("GeneralSettingsIsStopWorkingFullScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Язык интерфейса программы.
        /// </summary>
        public static string GeneralSettingsLanguageProgram {
            get {
                return ResourceManager.GetString("GeneralSettingsLanguageProgram", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перезапустить приложение для изменения языка?.
        /// </summary>
        public static string GeneralSettingsLanguageProgramRestart {
            get {
                return ResourceManager.GetString("GeneralSettingsLanguageProgramRestart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сворачивать в трей.
        /// </summary>
        public static string GeneralSettingsMinimizeToTray {
            get {
                return ResourceManager.GetString("GeneralSettingsMinimizeToTray", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Разное.
        /// </summary>
        public static string GeneralSettingsOther {
            get {
                return ResourceManager.GetString("GeneralSettingsOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настройки прокси.
        /// </summary>
        public static string GeneralSettingsProxy {
            get {
                return ResourceManager.GetString("GeneralSettingsProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ошибка параметров прокси сервера, измените настройки.
        /// </summary>
        public static string GeneralSettingsProxyError {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите пароль.
        /// </summary>
        public static string GeneralSettingsProxyPassword {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите порт.
        /// </summary>
        public static string GeneralSettingsProxyPort {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите адрес сервера.
        /// </summary>
        public static string GeneralSettingsProxyServer {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите имя пользователя.
        /// </summary>
        public static string GeneralSettingsProxyUsername {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранить настройки прокси.
        /// </summary>
        public static string GeneralSettingsSaveProxy {
            get {
                return ResourceManager.GetString("GeneralSettingsSaveProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Запуск с правами администратора.
        /// </summary>
        public static string GeneralSettingsStartAdmin {
            get {
                return ResourceManager.GetString("GeneralSettingsStartAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Запуск с windows.
        /// </summary>
        public static string GeneralSettingsStartUpWithWindows {
            get {
                return ResourceManager.GetString("GeneralSettingsStartUpWithWindows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Тема.
        /// </summary>
        public static string GeneralSettingsTheme {
            get {
                return ResourceManager.GetString("GeneralSettingsTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Стили оформления.
        /// </summary>
        public static string GeneralSettingsThemeAccent {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeAccent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to День или ночь.
        /// </summary>
        public static string GeneralSettingsThemeDayNight {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeDayNight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перезапустить программу для изменения темы?.
        /// </summary>
        public static string GeneralSettingsThemeProgramRestart {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeProgramRestart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать темную тему в вечерние часы.
        /// </summary>
        public static string GeneralSettingsUseNightTheme {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вечерние часы до.
        /// </summary>
        public static string GeneralSettingsUseNightThemeEnd {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightThemeEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вечерние часы от.
        /// </summary>
        public static string GeneralSettingsUseNightThemeStart {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightThemeStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Основные настройки.
        /// </summary>
        public static string GeneralTab {
            get {
                return ResourceManager.GetString("GeneralTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перенести результат в главное окно.
        /// </summary>
        public static string GoToMainWindowButton {
            get {
                return ResourceManager.GetString("GoToMainWindowButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите здесь, чтобы добавить новый элемент.
        /// </summary>
        public static string GridViewAlwaysVisibleNewRow {
            get {
                return ResourceManager.GetString("GridViewAlwaysVisibleNewRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Очистить фильтр.
        /// </summary>
        public static string GridViewClearFilter {
            get {
                return ResourceManager.GetString("GridViewClearFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите столбцы.
        /// </summary>
        public static string GridViewColumnsSelectionButtonTooltip {
            get {
                return ResourceManager.GetString("GridViewColumnsSelectionButtonTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Фильтр.
        /// </summary>
        public static string GridViewFilter {
            get {
                return ResourceManager.GetString("GridViewFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to И.
        /// </summary>
        public static string GridViewFilterAnd {
            get {
                return ResourceManager.GetString("GridViewFilterAnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Содержит.
        /// </summary>
        public static string GridViewFilterContains {
            get {
                return ResourceManager.GetString("GridViewFilterContains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не содержит.
        /// </summary>
        public static string GridViewFilterDoesNotContain {
            get {
                return ResourceManager.GetString("GridViewFilterDoesNotContain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заканчивается на.
        /// </summary>
        public static string GridViewFilterEndsWith {
            get {
                return ResourceManager.GetString("GridViewFilterEndsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Содержится в.
        /// </summary>
        public static string GridViewFilterIsContainedIn {
            get {
                return ResourceManager.GetString("GridViewFilterIsContainedIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пустой.
        /// </summary>
        public static string GridViewFilterIsEmpty {
            get {
                return ResourceManager.GetString("GridViewFilterIsEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Равно.
        /// </summary>
        public static string GridViewFilterIsEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Больше, чем.
        /// </summary>
        public static string GridViewFilterIsGreaterThan {
            get {
                return ResourceManager.GetString("GridViewFilterIsGreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Больше или равно.
        /// </summary>
        public static string GridViewFilterIsGreaterThanOrEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsGreaterThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Меньше чем.
        /// </summary>
        public static string GridViewFilterIsLessThan {
            get {
                return ResourceManager.GetString("GridViewFilterIsLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Меньше или равно.
        /// </summary>
        public static string GridViewFilterIsLessThanOrEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsLessThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не содержится в.
        /// </summary>
        public static string GridViewFilterIsNotContainedIn {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotContainedIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to не пустой.
        /// </summary>
        public static string GridViewFilterIsNotEmpty {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to не равно.
        /// </summary>
        public static string GridViewFilterIsNotEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не является нулевым.
        /// </summary>
        public static string GridViewFilterIsNotNull {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нулевой.
        /// </summary>
        public static string GridViewFilterIsNull {
            get {
                return ResourceManager.GetString("GridViewFilterIsNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Учитывать регистр.
        /// </summary>
        public static string GridViewFilterMatchCase {
            get {
                return ResourceManager.GetString("GridViewFilterMatchCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Или.
        /// </summary>
        public static string GridViewFilterOr {
            get {
                return ResourceManager.GetString("GridViewFilterOr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выбрать все.
        /// </summary>
        public static string GridViewFilterSelectAll {
            get {
                return ResourceManager.GetString("GridViewFilterSelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показать строки со значением, которое.
        /// </summary>
        public static string GridViewFilterShowRowsWithValueThat {
            get {
                return ResourceManager.GetString("GridViewFilterShowRowsWithValueThat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Начинается с.
        /// </summary>
        public static string GridViewFilterStartsWith {
            get {
                return ResourceManager.GetString("GridViewFilterStartsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перетащите заголовок столбца и поместите его сюда, чтобы сгруппировать по этому столбцу.
        /// </summary>
        public static string GridViewGroupPanelText {
            get {
                return ResourceManager.GetString("GridViewGroupPanelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заголовок группы.
        /// </summary>
        public static string GridViewGroupPanelTopText {
            get {
                return ResourceManager.GetString("GridViewGroupPanelTopText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сгруппировано по:.
        /// </summary>
        public static string GridViewGroupPanelTopTextGrouped {
            get {
                return ResourceManager.GetString("GridViewGroupPanelTopTextGrouped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Полнотекстовый поиск.
        /// </summary>
        public static string GridViewSearchPanelTopText {
            get {
                return ResourceManager.GetString("GridViewSearchPanelTopText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Свернуть.
        /// </summary>
        public static string HideHeaderButton {
            get {
                return ResourceManager.GetString("HideHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Очистить.
        /// </summary>
        public static string HistoryClear {
            get {
                return ResourceManager.GetString("HistoryClear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конец:.
        /// </summary>
        public static string HistoryDateEnd {
            get {
                return ResourceManager.GetString("HistoryDateEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Начало:.
        /// </summary>
        public static string HistoryDateStart {
            get {
                return ResourceManager.GetString("HistoryDateStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить.
        /// </summary>
        public static string HistoryDelSelected {
            get {
                return ResourceManager.GetString("HistoryDelSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ИСТОРИЯ.
        /// </summary>
        public static string HistoryHeaderButton {
            get {
                return ResourceManager.GetString("HistoryHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to История.
        /// </summary>
        public static string HistoryMenuItem {
            get {
                return ResourceManager.GetString("HistoryMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Включено.
        /// </summary>
        public static string HistoryToggleSwitch {
            get {
                return ResourceManager.GetString("HistoryToggleSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Двойное нажатие.
        /// </summary>
        public static string HotKeyDoubleKeyDown {
            get {
                return ResourceManager.GetString("HotKeyDoubleKeyDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите клавишу для двойного нажатия.
        /// </summary>
        public static string HotKeyDoubleKeyDownSelectKey {
            get {
                return ResourceManager.GetString("HotKeyDoubleKeyDownSelectKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левый Alt.
        /// </summary>
        public static string HotKeyDoubleLeftAlt {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftAlt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левый Ctrl.
        /// </summary>
        public static string HotKeyDoubleLeftCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левый или правый Ctrl.
        /// </summary>
        public static string HotKeyDoubleLeftOrRightCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftOrRightCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левый или правый Shift.
        /// </summary>
        public static string HotKeyDoubleLeftOrRightShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftOrRightShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левый Shift.
        /// </summary>
        public static string HotKeyDoubleLeftShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правый Alt.
        /// </summary>
        public static string HotKeyDoubleRightAlt {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightAlt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правый Ctrl.
        /// </summary>
        public static string HotKeyDoubleRightCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правый Shift.
        /// </summary>
        public static string HotKeyDoubleRightShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши выключены.
        /// </summary>
        public static string HotKeyIsOff {
            get {
                return ResourceManager.GetString("HotKeyIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши включены.
        /// </summary>
        public static string HotKeyIsON {
            get {
                return ResourceManager.GetString("HotKeyIsON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажатие кнопки мыши.
        /// </summary>
        public static string HotKeyMouse {
            get {
                return ResourceManager.GetString("HotKeyMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите дополнительную кнопку мыши.
        /// </summary>
        public static string HotKeyMouseSelectKey {
            get {
                return ResourceManager.GetString("HotKeyMouseSelectKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сочетание.
        /// </summary>
        public static string HotKeyShortcut {
            get {
                return ResourceManager.GetString("HotKeyShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши.
        /// </summary>
        public static string HotkeysMenuItem {
            get {
                return ResourceManager.GetString("HotkeysMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Двойное нажатие клавиши.
        /// </summary>
        public static string HotKeyUseDoubleKeyDown {
            get {
                return ResourceManager.GetString("HotKeyUseDoubleKeyDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши.
        /// </summary>
        public static string HotKeyUseHotkey {
            get {
                return ResourceManager.GetString("HotKeyUseHotkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажатие кнопки мыши.
        /// </summary>
        public static string HotKeyUseMouseXKey {
            get {
                return ResourceManager.GetString("HotKeyUseMouseXKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите горячие клавиши.
        /// </summary>
        public static string HotKeyWithoutPressShortcut {
            get {
                return ResourceManager.GetString("HotKeyWithoutPressShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отсутствует.
        /// </summary>
        public static string HotKeyWithoutShortcutNull {
            get {
                return ResourceManager.GetString("HotKeyWithoutShortcutNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Коррекция.
        /// </summary>
        public static string ImageEditor_Adjust {
            get {
                return ResourceManager.GetString("ImageEditor_Adjust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сумма.
        /// </summary>
        public static string ImageEditor_Amount {
            get {
                return ResourceManager.GetString("ImageEditor_Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Авто.
        /// </summary>
        public static string ImageEditor_Auto {
            get {
                return ResourceManager.GetString("ImageEditor_Auto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Фон:.
        /// </summary>
        public static string ImageEditor_Background {
            get {
                return ResourceManager.GetString("ImageEditor_Background", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Цвет границы:.
        /// </summary>
        public static string ImageEditor_BorderColor {
            get {
                return ResourceManager.GetString("ImageEditor_BorderColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Толщина границ:.
        /// </summary>
        public static string ImageEditor_BorderThickness {
            get {
                return ResourceManager.GetString("ImageEditor_BorderThickness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменение размера полотна.
        /// </summary>
        public static string ImageEditor_CanvasResize {
            get {
                return ResourceManager.GetString("ImageEditor_CanvasResize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер полотна.
        /// </summary>
        public static string ImageEditor_CanvasSize {
            get {
                return ResourceManager.GetString("ImageEditor_CanvasSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to белый.
        /// </summary>
        public static string ImageEditor_ColorPicker_NoColorText_White {
            get {
                return ResourceManager.GetString("ImageEditor_ColorPicker_NoColorText_White", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обрезать.
        /// </summary>
        public static string ImageEditor_Crop {
            get {
                return ResourceManager.GetString("ImageEditor_Crop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст рисунка.
        /// </summary>
        public static string ImageEditor_DrawText {
            get {
                return ResourceManager.GetString("ImageEditor_DrawText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ваш текст.
        /// </summary>
        public static string ImageEditor_DrawText_YourTextHere {
            get {
                return ResourceManager.GetString("ImageEditor_DrawText_YourTextHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нарисовать.
        /// </summary>
        public static string ImageEditor_DrawTool {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Цвет кисти:.
        /// </summary>
        public static string ImageEditor_DrawTool_BrushColor {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool_BrushColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер кисти:.
        /// </summary>
        public static string ImageEditor_DrawTool_BrushSize {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool_BrushSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размытие.
        /// </summary>
        public static string ImageEditor_Effect_Blur {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Blur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Яркость.
        /// </summary>
        public static string ImageEditor_Effect_Brightness {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Brightness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Контраст.
        /// </summary>
        public static string ImageEditor_Effect_ContrastAdjust {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_ContrastAdjust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменение оттенка.
        /// </summary>
        public static string ImageEditor_Effect_HueShift {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_HueShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обратить цвета.
        /// </summary>
        public static string ImageEditor_Effect_InvertColors {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_InvertColors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Насыщенность.
        /// </summary>
        public static string ImageEditor_Effect_Saturation {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Saturation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Повысить резкость.
        /// </summary>
        public static string ImageEditor_Effect_Sharpen {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Sharpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Видоизменение.
        /// </summary>
        public static string ImageEditor_Effects {
            get {
                return ResourceManager.GetString("ImageEditor_Effects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевернуть горизонтально.
        /// </summary>
        public static string ImageEditor_FlipHorizontal {
            get {
                return ResourceManager.GetString("ImageEditor_FlipHorizontal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевернуть вертикально.
        /// </summary>
        public static string ImageEditor_FlipVertical {
            get {
                return ResourceManager.GetString("ImageEditor_FlipVertical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер шрифта.
        /// </summary>
        public static string ImageEditor_FontSize {
            get {
                return ResourceManager.GetString("ImageEditor_FontSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Высота:.
        /// </summary>
        public static string ImageEditor_Height {
            get {
                return ResourceManager.GetString("ImageEditor_Height", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горизонтальное положение.
        /// </summary>
        public static string ImageEditor_HorizontalPosition {
            get {
                return ResourceManager.GetString("ImageEditor_HorizontalPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выравнивание изображения.
        /// </summary>
        public static string ImageEditor_ImageAlignment {
            get {
                return ResourceManager.GetString("ImageEditor_ImageAlignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Предварительный просмотр изображения.
        /// </summary>
        public static string ImageEditor_ImagePreview {
            get {
                return ResourceManager.GetString("ImageEditor_ImagePreview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер изображения.
        /// </summary>
        public static string ImageEditor_ImageSize {
            get {
                return ResourceManager.GetString("ImageEditor_ImageSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть.
        /// </summary>
        public static string ImageEditor_Open {
            get {
                return ResourceManager.GetString("ImageEditor_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Параметры.
        /// </summary>
        public static string ImageEditor_Options {
            get {
                return ResourceManager.GetString("ImageEditor_Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранить исходное соотношение сторон.
        /// </summary>
        public static string ImageEditor_PreserveAspectRatio {
            get {
                return ResourceManager.GetString("ImageEditor_PreserveAspectRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Радиус:.
        /// </summary>
        public static string ImageEditor_Radius {
            get {
                return ResourceManager.GetString("ImageEditor_Radius", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вернуть.
        /// </summary>
        public static string ImageEditor_Redo {
            get {
                return ResourceManager.GetString("ImageEditor_Redo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Относительный размер.
        /// </summary>
        public static string ImageEditor_RelativeSize {
            get {
                return ResourceManager.GetString("ImageEditor_RelativeSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменить размер.
        /// </summary>
        public static string ImageEditor_Resize {
            get {
                return ResourceManager.GetString("ImageEditor_Resize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Повернуть на 180°.
        /// </summary>
        public static string ImageEditor_Rotate180 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate180", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Повернуть на 270°.
        /// </summary>
        public static string ImageEditor_Rotate270 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate270", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Повернуть на 90°.
        /// </summary>
        public static string ImageEditor_Rotate90 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate90", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Поворот.
        /// </summary>
        public static string ImageEditor_Rotation {
            get {
                return ResourceManager.GetString("ImageEditor_Rotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Скругленные углы.
        /// </summary>
        public static string ImageEditor_RoundCorners {
            get {
                return ResourceManager.GetString("ImageEditor_RoundCorners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранить.
        /// </summary>
        public static string ImageEditor_Save {
            get {
                return ResourceManager.GetString("ImageEditor_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Фигура.
        /// </summary>
        public static string ImageEditor_Shape {
            get {
                return ResourceManager.GetString("ImageEditor_Shape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Эллипс.
        /// </summary>
        public static string ImageEditor_Shapes_Ellipse {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Ellipse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to График.
        /// </summary>
        public static string ImageEditor_Shapes_Line {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Прямоугольник.
        /// </summary>
        public static string ImageEditor_Shapes_Rectangle {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Rectangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Цвет границы.
        /// </summary>
        public static string ImageEditor_ShapeTool_BorderColor {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_BorderColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Толщина границ.
        /// </summary>
        public static string ImageEditor_ShapeTool_BorderThickness {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_BorderThickness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Форма Заполнения Трубы.
        /// </summary>
        public static string ImageEditor_ShapeTool_FillShape {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_FillShape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заблокировать пропорции.
        /// </summary>
        public static string ImageEditor_ShapeTool_LockRatio {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_LockRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Фигура.
        /// </summary>
        public static string ImageEditor_ShapeTool_Shape {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_Shape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заливка фигуры.
        /// </summary>
        public static string ImageEditor_ShapeTool_ShapeFill {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_ShapeFill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст.
        /// </summary>
        public static string ImageEditor_Text {
            get {
                return ResourceManager.GetString("ImageEditor_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Цвет текста.
        /// </summary>
        public static string ImageEditor_TextColor {
            get {
                return ResourceManager.GetString("ImageEditor_TextColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Невозможно открыть файл..
        /// </summary>
        public static string ImageEditor_TheFileCannotBeOpened {
            get {
                return ResourceManager.GetString("ImageEditor_TheFileCannotBeOpened", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Невозможно открыть файл. Это может быть заблокировано другим приложением..
        /// </summary>
        public static string ImageEditor_TheFileIsLocked {
            get {
                return ResourceManager.GetString("ImageEditor_TheFileIsLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Преобразовать.
        /// </summary>
        public static string ImageEditor_Transform {
            get {
                return ResourceManager.GetString("ImageEditor_Transform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не удалось сохранить файл..
        /// </summary>
        public static string ImageEditor_UnableToSaveFile {
            get {
                return ResourceManager.GetString("ImageEditor_UnableToSaveFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отменить.
        /// </summary>
        public static string ImageEditor_Undo {
            get {
                return ResourceManager.GetString("ImageEditor_Undo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Этот формат файла не поддерживается..
        /// </summary>
        public static string ImageEditor_UnsupportedFileFormat {
            get {
                return ResourceManager.GetString("ImageEditor_UnsupportedFileFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вертикальное положение.
        /// </summary>
        public static string ImageEditor_VerticalPosition {
            get {
                return ResourceManager.GetString("ImageEditor_VerticalPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ширина:.
        /// </summary>
        public static string ImageEditor_Width {
            get {
                return ResourceManager.GetString("ImageEditor_Width", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На Вашем компьютере для некоторых языков установлено более одной раскладки клавиатуры, что может негативно повлиять на правильность работы функции переключения раскладки.
        /// </summary>
        public static string InputLanguagesError {
            get {
                return ResourceManager.GetString("InputLanguagesError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Междометие.
        /// </summary>
        public static string interjection {
            get {
                return ResourceManager.GetString("interjection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите комбинацию клавиш.
        /// </summary>
        public static string InterKeyboardShortcuts {
            get {
                return ResourceManager.GetString("InterKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Скрывать индикатор в программах, запущенных на полный экран.
        /// </summary>
        public static string IsHideIndicateInFullScreenApp {
            get {
                return ResourceManager.GetString("IsHideIndicateInFullScreenApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать состояние CAPSLOCK.
        /// </summary>
        public static string IsIndicateCapsLockState {
            get {
                return ResourceManager.GetString("IsIndicateCapsLockState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Индикация текущего языка на клавиатуре.
        /// </summary>
        public static string IsIndicateCurrentLangInKeyboardLed {
            get {
                return ResourceManager.GetString("IsIndicateCurrentLangInKeyboardLed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текущий язык в системном трее.
        /// </summary>
        public static string IsLangInfoInTray {
            get {
                return ResourceManager.GetString("IsLangInfoInTray", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать флаг страны.
        /// </summary>
        public static string IsLangInfoShowIconsImage {
            get {
                return ResourceManager.GetString("IsLangInfoShowIconsImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать название языка.
        /// </summary>
        public static string IsLangInfoShowIconsText {
            get {
                return ResourceManager.GetString("IsLangInfoShowIconsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текущий язык ввода в текстовом курсоре.
        /// </summary>
        public static string IsLangInfoWindowShowForCaret {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForCaret", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Расширенные возможности.
        /// </summary>
        public static string IsLangInfoWindowShowForCaretEx {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForCaretEx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текущий язык ввода на указателе мыши.
        /// </summary>
        public static string IsLangInfoWindowShowForMouse {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отдельное окно индикатора языка.
        /// </summary>
        public static string IsLangInfoWindowShowLargeWindow {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowLargeWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранять однословные предложения.
        /// </summary>
        public static string IsSaveOneWordSentences {
            get {
                return ResourceManager.GetString("IsSaveOneWordSentences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Итальянский.
        /// </summary>
        public static string Italian {
            get {
                return ResourceManager.GetString("Italian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Произошла ошибка.
        /// </summary>
        public static string LabelError {
            get {
                return ResourceManager.GetString("LabelError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ошибок нет.
        /// </summary>
        public static string LabelNoErrors {
            get {
                return ResourceManager.GetString("LabelNoErrors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Варианты:.
        /// </summary>
        public static string LabelOptions {
            get {
                return ResourceManager.GetString("LabelOptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка завершена.
        /// </summary>
        public static string LabelOptionsEnd {
            get {
                return ResourceManager.GetString("LabelOptionsEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Варианты отсутствуют.
        /// </summary>
        public static string LabelOptionsNo {
            get {
                return ResourceManager.GetString("LabelOptionsNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст слишком длинный.
        /// </summary>
        public static string LabelTextTooLong {
            get {
                return ResourceManager.GetString("LabelTextTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Индикатор раскладки.
        /// </summary>
        public static string LangFlagTab {
            get {
                return ResourceManager.GetString("LangFlagTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Язык не поддерживается.
        /// </summary>
        public static string LangNotCorrect {
            get {
                return ResourceManager.GetString("LangNotCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На латинице.
        /// </summary>
        public static string LatinButton {
            get {
                return ResourceManager.GetString("LatinButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть список в главном окне.
        /// </summary>
        public static string ListOnMainWindow {
            get {
                return ResourceManager.GetString("ListOnMainWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Загрузка.
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Развернуть.
        /// </summary>
        public static string MaxHeaderButton {
            get {
                return ResourceManager.GetString("MaxHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Приложение запущено и свернуто.
        /// </summary>
        public static string MinimizedText {
            get {
                return ResourceManager.GetString("MinimizedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Создать.
        /// </summary>
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нет.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Совпадений не найдено!.
        /// </summary>
        public static string NoMatchFound {
            get {
                return ResourceManager.GetString("NoMatchFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Существительное.
        /// </summary>
        public static string noun {
            get {
                return ResourceManager.GetString("noun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Азиатские языки:.
        /// </summary>
        public static string OcrAsianLang {
            get {
                return ResourceManager.GetString("OcrAsianLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Захват области экрана.
        /// </summary>
        public static string OcrCaptureArea {
            get {
                return ResourceManager.GetString("OcrCaptureArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Скопировать изображение.
        /// </summary>
        public static string OcrCopyImage {
            get {
                return ResourceManager.GetString("OcrCopyImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Скопировать текст.
        /// </summary>
        public static string OcrCopyText {
            get {
                return ResourceManager.GetString("OcrCopyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите европейские или азиатские языки по умолчанию.
        /// </summary>
        public static string OcrDescDefault {
            get {
                return ResourceManager.GetString("OcrDescDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите европейские или азиатские языки.
        /// </summary>
        public static string OcrDescLang {
            get {
                return ResourceManager.GetString("OcrDescLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использование одновременно европейских и азиатских языков не поддерживается.
        /// </summary>
        public static string OcrDescNotSup {
            get {
                return ResourceManager.GetString("OcrDescNotSup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Редактировать изображение.
        /// </summary>
        public static string OcrEditImage {
            get {
                return ResourceManager.GetString("OcrEditImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Европейские языки:.
        /// </summary>
        public static string OcrEuropeanLang {
            get {
                return ResourceManager.GetString("OcrEuropeanLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Из буфера обмена.
        /// </summary>
        public static string OcrFromClipboard {
            get {
                return ResourceManager.GetString("OcrFromClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Распознавание текста.
        /// </summary>
        public static string OcrHeader {
            get {
                return ResourceManager.GetString("OcrHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Подождите, идет загрузка модуля.
        /// </summary>
        public static string OcrInit {
            get {
                return ResourceManager.GetString("OcrInit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Комбинация клавиш для запуска распознавания текста.
        /// </summary>
        public static string OcrKeyboardShortcuts {
            get {
                return ResourceManager.GetString("OcrKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите для загрузки модуля.
        /// </summary>
        public static string OcrLoadLibs {
            get {
                return ResourceManager.GetString("OcrLoadLibs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Модуль распознавания текста не загружен.
        /// </summary>
        public static string OcrModuleNotLoaded {
            get {
                return ResourceManager.GetString("OcrModuleNotLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть изображение или файл PDF.
        /// </summary>
        public static string OcrOpenImageOrPDFFile {
            get {
                return ResourceManager.GetString("OcrOpenImageOrPDFFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Распознать.
        /// </summary>
        public static string OcrRecognize {
            get {
                return ResourceManager.GetString("OcrRecognize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Распознать barcode.
        /// </summary>
        public static string OcrRecognizeBarcode {
            get {
                return ResourceManager.GetString("OcrRecognizeBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите языки.
        /// </summary>
        public static string OcrSelectLanguages {
            get {
                return ResourceManager.GetString("OcrSelectLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выделите область на экране или загрузите файл для распознавания.
        /// </summary>
        public static string OcrStartText {
            get {
                return ResourceManager.GetString("OcrStartText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Распознавание текста.
        /// </summary>
        public static string OcrTab {
            get {
                return ResourceManager.GetString("OcrTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст скопирован.
        /// </summary>
        public static string OcrWaitResult {
            get {
                return ResourceManager.GetString("OcrWaitResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Текст не распознан.
        /// </summary>
        public static string OcrWaitResultFail {
            get {
                return ResourceManager.GetString("OcrWaitResultFail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отключены.
        /// </summary>
        public static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ОК.
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Включены.
        /// </summary>
        public static string On {
            get {
                return ResourceManager.GetString("On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Только в PRO версии.
        /// </summary>
        public static string OnlyInProVersion {
            get {
                return ResourceManager.GetString("OnlyInProVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (PRO версия).
        /// </summary>
        public static string OnlyPro {
            get {
                return ResourceManager.GetString("OnlyPro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Прозрачность индикатора языка.
        /// </summary>
        public static string OpacityIconLangInfo {
            get {
                return ResourceManager.GetString("OpacityIconLangInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Комбинация клавиш для открытия главного окна.
        /// </summary>
        public static string OpenMainWindowShortcut {
            get {
                return ResourceManager.GetString("OpenMainWindowShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Порядок функций.
        /// </summary>
        public static string OrderFunctionsTab {
            get {
                return ResourceManager.GetString("OrderFunctionsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вводное слово.
        /// </summary>
        public static string parenthetic {
            get {
                return ResourceManager.GetString("parenthetic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Междометие.
        /// </summary>
        public static string participle {
            get {
                return ResourceManager.GetString("participle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставить.
        /// </summary>
        public static string PastButton {
            get {
                return ResourceManager.GetString("PastButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставить эмулируя ввод.
        /// </summary>
        public static string PasteButtonWithoutClipboard {
            get {
                return ResourceManager.GetString("PasteButtonWithoutClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Позиция индикатора в текстовом курсоре.
        /// </summary>
        public static string PosCarret {
            get {
                return ResourceManager.GetString("PosCarret", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Позиция индикатора на указателе мыши.
        /// </summary>
        public static string PosMouse {
            get {
                return ResourceManager.GetString("PosMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Предлог.
        /// </summary>
        public static string preposition {
            get {
                return ResourceManager.GetString("preposition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ваша лицензия заблокирована, все PRO функции будут отключены.
        /// </summary>
        public static string ProBlocked {
            get {
                return ResourceManager.GetString("ProBlocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вы преувеличили лимит количества рабочих мест для лицензии, все PRO функции будут отключены.
        /// </summary>
        public static string ProBlockedByEarlyActivation {
            get {
                return ResourceManager.GetString("ProBlockedByEarlyActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Преувеличен лимит реактиваций лицензии за последние 30 дней, все PRO функции будут отключены.
        /// </summary>
        public static string ProBlockedByMonthActivateLimit {
            get {
                return ResourceManager.GetString("ProBlockedByMonthActivateLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Срок действия PRO версии истек, новую лицензию можно приобрести на сайте EVERYLANG.NET.
        /// </summary>
        public static string ProBlockedExp {
            get {
                return ResourceManager.GetString("ProBlockedExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Срок действия PRO версии скоро истекает.
        /// </summary>
        public static string ProBlockedExpToDays {
            get {
                return ResourceManager.GetString("ProBlockedExpToDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить.
        /// </summary>
        public static string ProgramsExceptionsAddNew {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить exe файл.
        /// </summary>
        public static string ProgramsExceptionsAddNewExeFile {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewExeFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить папку.
        /// </summary>
        public static string ProgramsExceptionsAddNewFilesFromFolder {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewFilesFromFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Кликните мышкой в необходимой программе.
        /// </summary>
        public static string ProgramsExceptionsAddNewHelp {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавить по заголовку окна.
        /// </summary>
        public static string ProgramsExceptionsAddNewTitle {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Функции.
        /// </summary>
        public static string ProgramsExceptionsCurrentInfo {
            get {
                return ResourceManager.GetString("ProgramsExceptionsCurrentInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отметьте функции, которые будут работать для программы.
        /// </summary>
        public static string ProgramsExceptionsCurrentInfoTooltip {
            get {
                return ResourceManager.GetString("ProgramsExceptionsCurrentInfoTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить.
        /// </summary>
        public static string ProgramsExceptionsDelete {
            get {
                return ResourceManager.GetString("ProgramsExceptionsDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавление с помощью курсора.
        /// </summary>
        public static string ProgramsExceptionsFromPoint {
            get {
                return ResourceManager.GetString("ProgramsExceptionsFromPoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программы-исключения.
        /// </summary>
        public static string ProgramsExceptionsHeader {
            get {
                return ResourceManager.GetString("ProgramsExceptionsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шаблоны.
        /// </summary>
        public static string ProgramsExceptionsIsOnAutochange {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключения языка.
        /// </summary>
        public static string ProgramsExceptionsIsOnAutoSwitch {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnAutoSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to История буфера обмена.
        /// </summary>
        public static string ProgramsExceptionsIsOnClipboard {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранять изображения.
        /// </summary>
        public static string ProgramsExceptionsIsOnClipboardImage {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnClipboardImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер.
        /// </summary>
        public static string ProgramsExceptionsIsOnConverter {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник ввода текста.
        /// </summary>
        public static string ProgramsExceptionsIsOnDiary {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnDiary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши.
        /// </summary>
        public static string ProgramsExceptionsIsOnHotKeys {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnHotKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Индикатор раскладки.
        /// </summary>
        public static string ProgramsExceptionsIsOnLayoutFlag {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnLayoutFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение раскладки.
        /// </summary>
        public static string ProgramsExceptionsIsOnLayoutSwitcher {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnLayoutSwitcher", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string ProgramsExceptionsIsOnSmartClick {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnSmartClick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Добавление из списка программ.
        /// </summary>
        public static string ProgramsExceptionsProgramsList {
            get {
                return ResourceManager.GetString("ProgramsExceptionsProgramsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программы-исключения.
        /// </summary>
        public static string ProgramsExceptionsTab {
            get {
                return ResourceManager.GetString("ProgramsExceptionsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программы-языки.
        /// </summary>
        public static string ProgramsSetLayoutHeader {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Раскладки по умолчанию.
        /// </summary>
        public static string ProgramsSetLayoutTab {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Язык по умолчанию для выбранных программ.
        /// </summary>
        public static string ProgramsSetLayoutTabHeader {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutTabHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активировать PRO.
        /// </summary>
        public static string ProHeaderButton {
            get {
                return ResourceManager.GetString("ProHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Местоимение.
        /// </summary>
        public static string pronoun {
            get {
                return ResourceManager.GetString("pronoun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Информация по вашей лицензии отправлена на email:.
        /// </summary>
        public static string ProSendEmailLic {
            get {
                return ResourceManager.GetString("ProSendEmailLic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активировать.
        /// </summary>
        public static string ProSettingsActivation {
            get {
                return ResourceManager.GetString("ProSettingsActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация завершена с ошибкой, ваша лицензия заблокирована.
        /// </summary>
        public static string ProSettingsActivationBlocked {
            get {
                return ResourceManager.GetString("ProSettingsActivationBlocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация завершена с ошибкой.
        /// </summary>
        public static string ProSettingsActivationError {
            get {
                return ResourceManager.GetString("ProSettingsActivationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация завершена с ошибкой, возможно введен неверный email.
        /// </summary>
        public static string ProSettingsActivationErrorEmail {
            get {
                return ResourceManager.GetString("ProSettingsActivationErrorEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация завершена с ошибкой, проверьте интернет соединение.
        /// </summary>
        public static string ProSettingsActivationInternetError {
            get {
                return ResourceManager.GetString("ProSettingsActivationInternetError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация завершена успешно.
        /// </summary>
        public static string ProSettingsActivationOk {
            get {
                return ResourceManager.GetString("ProSettingsActivationOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программа была активирована на новом рабочем месте, при этом была деактивирована на компьютере.
        /// </summary>
        public static string ProSettingsActivationReactivated {
            get {
                return ResourceManager.GetString("ProSettingsActivationReactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация невозможна из-за реактивации данной лицензии на другом компьютере.
        /// </summary>
        public static string ProSettingsBlockedByEarlyActivation {
            get {
                return ResourceManager.GetString("ProSettingsBlockedByEarlyActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация невозможна по причине превышения лимита реактиваций, докупите количество рабочих мест для данной лицензии.
        /// </summary>
        public static string ProSettingsBlockedByMonthActivateLimit {
            get {
                return ResourceManager.GetString("ProSettingsBlockedByMonthActivateLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите код.
        /// </summary>
        public static string ProSettingsCode {
            get {
                return ResourceManager.GetString("ProSettingsCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите код и email.
        /// </summary>
        public static string ProSettingsCodeEmail {
            get {
                return ResourceManager.GetString("ProSettingsCodeEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Удалить лицензию.
        /// </summary>
        public static string ProSettingsDelete {
            get {
                return ResourceManager.GetString("ProSettingsDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Введите email.
        /// </summary>
        public static string ProSettingsEmail {
            get {
                return ResourceManager.GetString("ProSettingsEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Попробовать.
        /// </summary>
        public static string ProSettingsEvaluation {
            get {
                return ResourceManager.GetString("ProSettingsEvaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Срок действия лицензии истек.
        /// </summary>
        public static string ProSettingsExpired {
            get {
                return ResourceManager.GetString("ProSettingsExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Пробный период истек.
        /// </summary>
        public static string ProSettingsExpiredEva {
            get {
                return ResourceManager.GetString("ProSettingsExpiredEva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Информация о лицензии.
        /// </summary>
        public static string ProSettingsGetData {
            get {
                return ResourceManager.GetString("ProSettingsGetData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Активация PRO функций.
        /// </summary>
        public static string ProSettingsHeader {
            get {
                return ResourceManager.GetString("ProSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Код активации.
        /// </summary>
        public static string ProSettingsInput {
            get {
                return ResourceManager.GetString("ProSettingsInput", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO активированно на пробный период.
        /// </summary>
        public static string ProSettingsIsEvaluation {
            get {
                return ResourceManager.GetString("ProSettingsIsEvaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дата активации:.
        /// </summary>
        public static string ProSettingsLicenseActivateDate {
            get {
                return ResourceManager.GetString("ProSettingsLicenseActivateDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Свободных мест:.
        /// </summary>
        public static string ProSettingsLicenseCountFreeSeats {
            get {
                return ResourceManager.GetString("ProSettingsLicenseCountFreeSeats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Количество доступных реактиваций:.
        /// </summary>
        public static string ProSettingsLicenseCountReact {
            get {
                return ResourceManager.GetString("ProSettingsLicenseCountReact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email:.
        /// </summary>
        public static string ProSettingsLicenseEmail {
            get {
                return ResourceManager.GetString("ProSettingsLicenseEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Срок действия лицензии:.
        /// </summary>
        public static string ProSettingsLicenseExpiryDate {
            get {
                return ResourceManager.GetString("ProSettingsLicenseExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сведения о лицензии.
        /// </summary>
        public static string ProSettingsLicenseInfo {
            get {
                return ResourceManager.GetString("ProSettingsLicenseInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Тип лицензии:.
        /// </summary>
        public static string ProSettingsLicenseIsEnterprise {
            get {
                return ResourceManager.GetString("ProSettingsLicenseIsEnterprise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO не активированно.
        /// </summary>
        public static string ProSettingsLicenseNotPro {
            get {
                return ResourceManager.GetString("ProSettingsLicenseNotPro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Владелец:.
        /// </summary>
        public static string ProSettingsLicenseUserName {
            get {
                return ResourceManager.GetString("ProSettingsLicenseUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Количество мест:.
        /// </summary>
        public static string ProSettingsLicenseUsersCount {
            get {
                return ResourceManager.GetString("ProSettingsLicenseUsersCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ваш код активации был обновлен.
        /// </summary>
        public static string ProSettingsNewLic {
            get {
                return ResourceManager.GetString("ProSettingsNewLic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Бессрочная лицензия.
        /// </summary>
        public static string ProSettingsNoExpiry {
            get {
                return ResourceManager.GetString("ProSettingsNoExpiry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Купить.
        /// </summary>
        public static string ProSettingsPurchase {
            get {
                return ResourceManager.GetString("ProSettingsPurchase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Статус.
        /// </summary>
        public static string ProSettingsStatus {
            get {
                return ResourceManager.GetString("ProSettingsStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Оценка в течение 40 дней.
        /// </summary>
        public static string ProSettingsStatusEvaluate {
            get {
                return ResourceManager.GetString("ProSettingsStatusEvaluate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO активировано.
        /// </summary>
        public static string ProSettingsStatusOk {
            get {
                return ResourceManager.GetString("ProSettingsStatusOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Лицензия.
        /// </summary>
        public static string ProTab {
            get {
                return ResourceManager.GetString("ProTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Функции PRO.
        /// </summary>
        public static string ProTabs {
            get {
                return ResourceManager.GetString("ProTabs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменить на:.
        /// </summary>
        public static string ReplaceTo {
            get {
                return ResourceManager.GetString("ReplaceTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сброс.
        /// </summary>
        public static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сбросить все.
        /// </summary>
        public static string ResetAll {
            get {
                return ResourceManager.GetString("ResetAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Русский.
        /// </summary>
        public static string Russian {
            get {
                return ResourceManager.GetString("Russian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сохранить.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Искать:.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Найти....
        /// </summary>
        public static string SearchText {
            get {
                return ResourceManager.GetString("SearchText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сбросить настройки.
        /// </summary>
        public static string SetDefaultSetting {
            get {
                return ResourceManager.GetString("SetDefaultSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шрифт.
        /// </summary>
        public static string SetFont {
            get {
                return ResourceManager.GetString("SetFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to НАСТРОЙКИ.
        /// </summary>
        public static string SettingHeaderButton {
            get {
                return ResourceManager.GetString("SettingHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настройки.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показать историю.
        /// </summary>
        public static string ShowHistoryButton {
            get {
                return ResourceManager.GetString("ShowHistoryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть в браузере.
        /// </summary>
        public static string SiteSourceButton {
            get {
                return ResourceManager.GetString("SiteSourceButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть сайт сервиса перевода.
        /// </summary>
        public static string SiteSourceTextButton {
            get {
                return ResourceManager.GetString("SiteSourceTextButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Увеличение размера индикатора языка в процентах.
        /// </summary>
        public static string SizeIconLangInfo {
            get {
                return ResourceManager.GetString("SizeIconLangInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Позиция окна относительно указателя мыши.
        /// </summary>
        public static string SmartClickMiniPos {
            get {
                return ResourceManager.GetString("SmartClickMiniPos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Размер окна.
        /// </summary>
        public static string SmartClickMiniSize {
            get {
                return ResourceManager.GetString("SmartClickMiniSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Горячие клавиши для открытия окна SmartClick.
        /// </summary>
        public static string SmartClickShortcutSettingsHeader {
            get {
                return ResourceManager.GetString("SmartClickShortcutSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ШАБЛОНЫ.
        /// </summary>
        public static string SnippetsHeaderButton {
            get {
                return ResourceManager.GetString("SnippetsHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Прослушать.
        /// </summary>
        public static string SoundButton {
            get {
                return ResourceManager.GetString("SoundButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите звук.
        /// </summary>
        public static string SoundFormSelectTrack {
            get {
                return ResourceManager.GetString("SoundFormSelectTrack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Громкость.
        /// </summary>
        public static string SoundFormVolume {
            get {
                return ResourceManager.GetString("SoundFormVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Звук включен.
        /// </summary>
        public static string SoundOnOff {
            get {
                return ResourceManager.GetString("SoundOnOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка правописания.
        /// </summary>
        public static string SpellCheckHeader {
            get {
                return ResourceManager.GetString("SpellCheckHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Комбинация клавиш для проверки орфографии выделенного текста.
        /// </summary>
        public static string SpellcheckingKeyboardShortcuts {
            get {
                return ResourceManager.GetString("SpellcheckingKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка орфографии выделенного текста.
        /// </summary>
        public static string SpellcheckingKeyboardShortcutsShort {
            get {
                return ResourceManager.GetString("SpellcheckingKeyboardShortcutsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрывать окно, если нет ошибок, через 2 секунды.
        /// </summary>
        public static string SpellcheckingSettingsCloseByTimer {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsCloseByTimer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка орфографии.
        /// </summary>
        public static string SpellcheckingSettingsHeader {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка орфографии включена.
        /// </summary>
        public static string SpellcheckingSettingsIsOn {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверка орфографии при наборе текста.
        /// </summary>
        public static string SpellcheckingSettingsWhileTyping {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTyping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настройки звука при проверке орфографии.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingSoundEdit {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingSoundEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Звук для проверки орфографии при наборе текста.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingSoundOn {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingSoundOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Для быстрой замены использовать числа.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingUseNumber {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingUseNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перед началом использования программы ознакомьтесь с функционалом.
        /// </summary>
        public static string StartPageHeader {
            get {
                return ResourceManager.GetString("StartPageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изучите функции программы.
        /// </summary>
        public static string StartPageHelp {
            get {
                return ResourceManager.GetString("StartPageHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Приобрести лицензию.
        /// </summary>
        public static string StartPageLicense {
            get {
                return ResourceManager.GetString("StartPageLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сайт программы.
        /// </summary>
        public static string StartPageSite {
            get {
                return ResourceManager.GetString("StartPageSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Посмотрите видео-презентацию.
        /// </summary>
        public static string StartPageVideo {
            get {
                return ResourceManager.GetString("StartPageVideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перед началом использования программы ознакомьтесь с функционалом.
        /// </summary>
        public static string StartWindowTitle {
            get {
                return ResourceManager.GetString("StartWindowTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Не закрывать.
        /// </summary>
        public static string StayOnTopButton {
            get {
                return ResourceManager.GetString("StayOnTopButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Комбинация клавиш для отключения всех функций.
        /// </summary>
        public static string StopWorkingShortcut {
            get {
                return ResourceManager.GetString("StopWorkingShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настройка клавиш для переключения на конкретный язык.
        /// </summary>
        public static string SwitcherLangAndKeysForSwitch {
            get {
                return ResourceManager.GetString("SwitcherLangAndKeysForSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отключить автопереключение раскладки?.
        /// </summary>
        public static string SwitcherSettingsAskToDeactivateAutoswitcherOff {
            get {
                return ResourceManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Включить автопереключение раскладки?.
        /// </summary>
        public static string SwitcherSettingsAskToDeactivateAutoswitcherOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменение раскладки.
        /// </summary>
        public static string SwitcherSettingsHeader {
            get {
                return ResourceManager.GetString("SwitcherSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Автопереключение включено.
        /// </summary>
        public static string SwitcherSettingsHeaderAuto {
            get {
                return ResourceManager.GetString("SwitcherSettingsHeaderAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение раскладки отключено.
        /// </summary>
        public static string SwitcherSettingsIsOff {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение раскладки включено.
        /// </summary>
        public static string SwitcherSettingsIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение с начала строки.
        /// </summary>
        public static string SwitcherSettingsIsOnInsert {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOnInsert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать Break.
        /// </summary>
        public static string SwitcherSettingsIsUseBreak {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseBreak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать двойное нажатие кнопки ScrollLock.
        /// </summary>
        public static string SwitcherSettingsIsUseScrollLock {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseScrollLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать двойное нажатие кнопки Shift.
        /// </summary>
        public static string SwitcherSettingsIsUseShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение раскладки последнего слова.
        /// </summary>
        public static string SwitcherSettingsKeyboardShortcutsSwitch {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardShortcutsSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Смена раскладки выделенного текста.
        /// </summary>
        public static string SwitcherSettingsKeyboardShortcutsSwitchSelected {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardShortcutsSwitchSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключать раскладку по кнопке.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левому Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to По правому или левому Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLRCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLRCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to По правому или левому Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLRShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLRShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Левому Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правому Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правому Ctrl или CapsLock.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Правому Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Системные настройки.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnStandart {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Оставлять текст выделенным после переключения раскладки.
        /// </summary>
        public static string SwitcherSettingsLeaveTextSelectedAfterSwitch {
            get {
                return ResourceManager.GetString("SwitcherSettingsLeaveTextSelectedAfterSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выбор метода переключения раскладки.
        /// </summary>
        public static string SwitcherSettingsMethodSelect {
            get {
                return ResourceManager.GetString("SwitcherSettingsMethodSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Настройка звука переключения раскладки.
        /// </summary>
        public static string SwitcherSettingsSoundEdit {
            get {
                return ResourceManager.GetString("SwitcherSettingsSoundEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать Ctrl+(число) для переключения на конкретный язык.
        /// </summary>
        public static string SwitcherSettingsSwitcherCtrlNumberIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitcherCtrlNumberIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Звук переключения раскладки.
        /// </summary>
        public static string SwitcherSettingsSwitcherSountIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitcherSountIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Эмуляция клавиш переключения раскладки.
        /// </summary>
        public static string SwitcherSettingsSwitchMethod1 {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitchMethod1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выполнение команды Windows.
        /// </summary>
        public static string SwitcherSettingsSwitchMethod2 {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitchMethod2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Для переключения режима ввода заглавных букв используйте одновременное нажатие на правый и левый Shift.
        /// </summary>
        public static string SwitcherSettingsToolTipForCurrentSwitchOnKey {
            get {
                return ResourceManager.GetString("SwitcherSettingsToolTipForCurrentSwitchOnKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Список языков на которые будет переключаться раскладка.
        /// </summary>
        public static string SwitcherSettingsTrueListOfLang {
            get {
                return ResourceManager.GetString("SwitcherSettingsTrueListOfLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переключение раскладки.
        /// </summary>
        public static string SwitcherTab {
            get {
                return ResourceManager.GetString("SwitcherTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программа свернута в системный трей.
        /// </summary>
        public static string SystemTrayHide {
            get {
                return ResourceManager.GetString("SystemTrayHide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to На язык:.
        /// </summary>
        public static string ToLang {
            get {
                return ResourceManager.GetString("ToLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Новый шаблон.
        /// </summary>
        public static string ToReplacerButton {
            get {
                return ResourceManager.GetString("ToReplacerButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Всего найдено соответствий:.
        /// </summary>
        public static string TotalMatchFound {
            get {
                return ResourceManager.GetString("TotalMatchFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Всего произведено замен:.
        /// </summary>
        public static string TotalMatchFoundReplace {
            get {
                return ResourceManager.GetString("TotalMatchFoundReplace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Закрыть ESC.
        /// </summary>
        public static string TransCloseHeaderButton {
            get {
                return ResourceManager.GetString("TransCloseHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевести.
        /// </summary>
        public static string TranslateButton {
            get {
                return ResourceManager.GetString("TranslateButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to В процессе перевода возникла ошибка, попробуйте еще раз или выберите другой сервис для перевода.
        /// </summary>
        public static string TranslateError {
            get {
                return ResourceManager.GetString("TranslateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать только избранные языки.
        /// </summary>
        public static string TranslateOnlyFavoriteLanguages {
            get {
                return ResourceManager.GetString("TranslateOnlyFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показать все.
        /// </summary>
        public static string TranslateSowAll {
            get {
                return ResourceManager.GetString("TranslateSowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переводчик.
        /// </summary>
        public static string TranslationTab {
            get {
                return ResourceManager.GetString("TranslationTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Заменить текст ENTER.
        /// </summary>
        public static string TransReplaceTextButton {
            get {
                return ResourceManager.GetString("TransReplaceTextButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите ваши избранные языки.
        /// </summary>
        public static string TransSettingsChooseYourFavoriteLanguages {
            get {
                return ResourceManager.GetString("TransSettingsChooseYourFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Очистить историю переводов.
        /// </summary>
        public static string TransSettingsClearAllHistory {
            get {
                return ResourceManager.GetString("TransSettingsClearAllHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Избранные языки.
        /// </summary>
        public static string TransSettingsFavoriteLanguages {
            get {
                return ResourceManager.GetString("TransSettingsFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевод.
        /// </summary>
        public static string TransSettingsHeader {
            get {
                return ResourceManager.GetString("TransSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Хранить историю переводов.
        /// </summary>
        public static string TransSettingsHistoryIsOn {
            get {
                return ResourceManager.GetString("TransSettingsHistoryIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Нажмите комбинацию клавиш.
        /// </summary>
        public static string TransSettingsInterKeyboardShortcuts {
            get {
                return ResourceManager.GetString("TransSettingsInterKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевод включен.
        /// </summary>
        public static string TransSettingsIsOn {
            get {
                return ResourceManager.GetString("TransSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Комбинация клавиш для перевода выделенного текста.
        /// </summary>
        public static string TransSettingsKeyboardShortcuts {
            get {
                return ResourceManager.GetString("TransSettingsKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Язык по умолчанию с которого переводить.
        /// </summary>
        public static string TransSettingsLanguageFromTranslate {
            get {
                return ResourceManager.GetString("TransSettingsLanguageFromTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Основной язык для перевода.
        /// </summary>
        public static string TransSettingsMainLanguageForTheTranslation {
            get {
                return ResourceManager.GetString("TransSettingsMainLanguageForTheTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Язык по умолчанию на который переводить.
        /// </summary>
        public static string TransSettingsNativeLanguage {
            get {
                return ResourceManager.GetString("TransSettingsNativeLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Сервис перевода.
        /// </summary>
        public static string TransSettingsProviderOfTranslation {
            get {
                return ResourceManager.GetString("TransSettingsProviderOfTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Переводить при выделении текста мышкой.
        /// </summary>
        public static string TransSettingsTranslationIsAlways {
            get {
                return ResourceManager.GetString("TransSettingsTranslationIsAlways", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Двойное нажатие кнопки Ctrl.
        /// </summary>
        public static string TransSettingsUseDoubleCtrl {
            get {
                return ResourceManager.GetString("TransSettingsUseDoubleCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Украинский.
        /// </summary>
        public static string Ukrainian {
            get {
                return ResourceManager.GetString("Ukrainian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть список шаблонов.
        /// </summary>
        public static string UniAutochange {
            get {
                return ResourceManager.GetString("UniAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Преобразование выделенного текста в CamelCase стиле.
        /// </summary>
        public static string UniCamelCase {
            get {
                return ResourceManager.GetString("UniCamelCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть историю буфера обмена.
        /// </summary>
        public static string UniClipboardHistory {
            get {
                return ResourceManager.GetString("UniClipboardHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер текста.
        /// </summary>
        public static string UniConverter {
            get {
                return ResourceManager.GetString("UniConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер чисел и дат в строки, вычисление выражений.
        /// </summary>
        public static string UniConvertExpressions {
            get {
                return ResourceManager.GetString("UniConvertExpressions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Скопировать.
        /// </summary>
        public static string UniCopy {
            get {
                return ResourceManager.GetString("UniCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть дневник.
        /// </summary>
        public static string UniDiaryHistory {
            get {
                return ResourceManager.GetString("UniDiaryHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Привести к нижнему регистру выделенный текст.
        /// </summary>
        public static string UniDownCase {
            get {
                return ResourceManager.GetString("UniDownCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Создать почтовое сообщение.
        /// </summary>
        public static string UniEmail {
            get {
                return ResourceManager.GetString("UniEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обрамление выделенного текста кавычками.
        /// </summary>
        public static string UniEnclose {
            get {
                return ResourceManager.GetString("UniEnclose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Инвертировать регистр выделенного текста.
        /// </summary>
        public static string UniInvertCase {
            get {
                return ResourceManager.GetString("UniInvertCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть ссылку в браузере.
        /// </summary>
        public static string UniLink {
            get {
                return ResourceManager.GetString("UniLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Генерация короткой ссылки.
        /// </summary>
        public static string UniLinkShorter {
            get {
                return ResourceManager.GetString("UniLinkShorter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевести сайт по ссылке.
        /// </summary>
        public static string UniLinkTranslate {
            get {
                return ResourceManager.GetString("UniLinkTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставить текст.
        /// </summary>
        public static string UniPaste {
            get {
                return ResourceManager.GetString("UniPaste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставить текст без форматирования.
        /// </summary>
        public static string UniPasteUnf {
            get {
                return ResourceManager.GetString("UniPasteUnf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вызов поиска.
        /// </summary>
        public static string UniSearch {
            get {
                return ResourceManager.GetString("UniSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Проверить орфографию.
        /// </summary>
        public static string UniSpellCheck {
            get {
                return ResourceManager.GetString("UniSpellCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Шаблоны.
        /// </summary>
        public static string UniTextAutochange {
            get {
                return ResourceManager.GetString("UniTextAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to История.
        /// </summary>
        public static string UniTextClipboardHistory1 {
            get {
                return ResourceManager.GetString("UniTextClipboardHistory1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to буфера.
        /// </summary>
        public static string UniTextClipboardHistory2 {
            get {
                return ResourceManager.GetString("UniTextClipboardHistory2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Конвертер.
        /// </summary>
        public static string UniTextConverter {
            get {
                return ResourceManager.GetString("UniTextConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выражения.
        /// </summary>
        public static string UniTextConvertExpressions {
            get {
                return ResourceManager.GetString("UniTextConvertExpressions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Копировать.
        /// </summary>
        public static string UniTextCopy {
            get {
                return ResourceManager.GetString("UniTextCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Дневник.
        /// </summary>
        public static string UniTextDiaryHistory {
            get {
                return ResourceManager.GetString("UniTextDiaryHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Регистр вниз.
        /// </summary>
        public static string UniTextDownCase {
            get {
                return ResourceManager.GetString("UniTextDownCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string UniTextEmail {
            get {
                return ResourceManager.GetString("UniTextEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Кавычки.
        /// </summary>
        public static string UniTextEnclose {
            get {
                return ResourceManager.GetString("UniTextEnclose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Инвертировать регистр.
        /// </summary>
        public static string UniTextInvertCase {
            get {
                return ResourceManager.GetString("UniTextInvertCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ссылка.
        /// </summary>
        public static string UniTextLink {
            get {
                return ResourceManager.GetString("UniTextLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL Short.
        /// </summary>
        public static string UniTextLinkShorter {
            get {
                return ResourceManager.GetString("UniTextLinkShorter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перев. сайт.
        /// </summary>
        public static string UniTextLinkTranslate {
            get {
                return ResourceManager.GetString("UniTextLinkTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставка.
        /// </summary>
        public static string UniTextPaste {
            get {
                return ResourceManager.GetString("UniTextPaste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Вставка.
        /// </summary>
        public static string UniTextPasteUnf1 {
            get {
                return ResourceManager.GetString("UniTextPasteUnf1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to без формат..
        /// </summary>
        public static string UniTextPasteUnf2 {
            get {
                return ResourceManager.GetString("UniTextPasteUnf2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Поиск.
        /// </summary>
        public static string UniTextSearch {
            get {
                return ResourceManager.GetString("UniTextSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Орфография.
        /// </summary>
        public static string UniTextSpellCheck {
            get {
                return ResourceManager.GetString("UniTextSpellCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевод.
        /// </summary>
        public static string UniTextTranslate {
            get {
                return ResourceManager.GetString("UniTextTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Транслит.
        /// </summary>
        public static string UniTextTranslit {
            get {
                return ResourceManager.GetString("UniTextTranslit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Регистр вверх.
        /// </summary>
        public static string UniTextUpCase {
            get {
                return ResourceManager.GetString("UniTextUpCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Регистр.
        /// </summary>
        public static string UniTextUppercase {
            get {
                return ResourceManager.GetString("UniTextUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Перевести.
        /// </summary>
        public static string UniTranslate {
            get {
                return ResourceManager.GetString("UniTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открыть историю переводов.
        /// </summary>
        public static string UniTranslateHistory {
            get {
                return ResourceManager.GetString("UniTranslateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Транслитерация выделенного текста.
        /// </summary>
        public static string UniTranslit {
            get {
                return ResourceManager.GetString("UniTranslit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Привести к верхнему регистру выделенный текст.
        /// </summary>
        public static string UniUpCase {
            get {
                return ResourceManager.GetString("UniUpCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Изменить регистр выделенного текста.
        /// </summary>
        public static string UniUppercase {
            get {
                return ResourceManager.GetString("UniUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string UniversalWindowSettingsHeader {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отметьте функции, которые будут доступны.
        /// </summary>
        public static string UniversalWindowSettingsItemsCheck {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsItemsCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Выберите поисковый сервис для SmartClick.
        /// </summary>
        public static string UniversalWindowSettingsSearchServices {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsSearchServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Показывать вспомогательное окно после выделения текста.
        /// </summary>
        public static string UniversalWindowSettingsShowMiniOn {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowMiniOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открывать при двойном нажатии на среднюю кнопку мыши.
        /// </summary>
        public static string UniversalWindowSettingsShowOnDoubleMiddle {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnDoubleMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Использовать горячии клавиши для открытия.
        /// </summary>
        public static string UniversalWindowSettingsShowOnPressHotKeys {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnPressHotKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Открывать при нажатии левой и затем правой кнопки мыши.
        /// </summary>
        public static string UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick включен.
        /// </summary>
        public static string UniversalWindowSettingsUniversalWindowIsOn {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsUniversalWindowIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string UniversalWindowTab {
            get {
                return ResourceManager.GetString("UniversalWindowTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Обновление.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Доступно обновление, перезапустите приложение.
        /// </summary>
        public static string UpdateAvailable {
            get {
                return ResourceManager.GetString("UpdateAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Глагол.
        /// </summary>
        public static string verb {
            get {
                return ResourceManager.GetString("verb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Отключить программу.
        /// </summary>
        public static string WorkingOff {
            get {
                return ResourceManager.GetString("WorkingOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Программа отключена.
        /// </summary>
        public static string WorkingOffTitle {
            get {
                return ResourceManager.GetString("WorkingOffTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Включить программу.
        /// </summary>
        public static string WorkingOn {
            get {
                return ResourceManager.GetString("WorkingOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Да.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
