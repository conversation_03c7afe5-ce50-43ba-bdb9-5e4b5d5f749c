﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    internal class TranslationHistoryManager
    {

        internal static IEnumerable<HistoryTranslationModel> GetAllHistory()
        {
            var collection = new List<HistoryTranslationModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("HistoryTranslationModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        HistoryTranslationModel dataModel = new HistoryTranslationModel();
                        if (!bsonDocument["_id"].IsNull) dataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["Text"].IsNull) dataModel.Text = bsonDocument["Text"].AsString;
                        if (!bsonDocument["DateTime"].IsNull) dataModel.DateTime = bsonDocument["DateTime"].AsDateTime;
                        if (!bsonDocument["Application"].IsNull) dataModel.Application = bsonDocument["Application"].AsString;
                        if (!bsonDocument["DateText"].IsNull) dataModel.DateText = bsonDocument["DateText"].AsString;
                        if (!bsonDocument["ShortText"].IsNull) dataModel.ShortText = bsonDocument["ShortText"].AsString;
                        collection.Add(dataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddHistoryTranslation(HistoryTranslationModel historyTranslation)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("HistoryTranslationModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    historyTranslation.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = historyTranslation.Id;
                    bsonDocument["DateTime"] = historyTranslation.DateTime;
                    bsonDocument["Application"] = historyTranslation.Application;
                    bsonDocument["DateText"] = historyTranslation.DateText;
                    bsonDocument["ShortText"] = historyTranslation.ShortText;
                    bsonDocument["Text"] = historyTranslation.Text;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearHistoryTranslation()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("HistoryTranslationModel"))
                    {
                        db.DropCollection("HistoryTranslationModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelHistoryTranslation(List<HistoryTranslationModel> historyTranslationModels)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("HistoryTranslationModel");
                    foreach (var data in historyTranslationModels)
                    {
                        var item = data;
                        if (item != null)
                        {
                            if (item.Id != null) schemelessCollection.Delete(item.Id);
                            else schemelessCollection.DeleteMany(Query.EQ("ShortText", item.ShortText));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
