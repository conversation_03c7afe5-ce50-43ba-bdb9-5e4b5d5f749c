﻿using Everylang.App.Callback;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.LangFlag;
using Everylang.App.SettingsApp;
using Everylang.App.SpellCheck;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Vanara.PInvoke;
using MousePosition = Everylang.App.Utilities.MousePosition;
using Timer = System.Timers.Timer;


namespace Everylang.App.View.Controls.SpellCheck
{
    /// <summary>
    /// Interaction logic for SnippetsMiniWindow.xaml
    /// </summary>
    internal partial class SpellCheckWhileTypingWindow
    {
        private List<SuggestionsRes> _suggestionsList;
        private List<string> _numberList;
        private bool _isShow;
        private string? _originText;
        private readonly Timer _timer;

        internal SpellCheckWhileTypingWindow()
        {
            InitializeComponent();
            Opened += OnOpened;
            _timer = new Timer();
            _timer.Elapsed += (_, _) =>
            {
                if (Application.Current != null && Application.Current.Dispatcher != null)
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() => Hide(true)));
            };
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;
                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (IsMouseOver || !_isShow)
            {
                return;
            }
            Hide();
        }

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!_isShow)
            {
                return;
            }
            if (SettingsManager.Settings.SpellCheckWhileTypingUseNumber)
            {
                var key = KeyboardLayoutMethods.CodeToString(e);
                if (_numberList.Contains(key) && _numberList.IndexOf(key) >= 0)
                {
                    e.Handled = true;
                    if (_suggestionsList.Count > _numberList.IndexOf(key))
                    {
                        GlobalEventsApp.OnSpellCheckReplaceAction(_suggestionsList[_numberList.IndexOf(key)].Suggestion);
                    }
                }
                Hide();
            }
            else
            {
                if (_numberList.Contains(e.KeyCode.ToString()) && _numberList.IndexOf(e.KeyCode.ToString()) >= 0)
                {
                    e.Handled = true;
                    if (_suggestionsList.Count > _numberList.IndexOf(e.KeyCode.ToString()))
                    {
                        GlobalEventsApp.OnSpellCheckReplaceAction(_suggestionsList[_numberList.IndexOf(e.KeyCode.ToString())].Suggestion);
                    }
                }
                Hide();
            }

            if (e.KeyCode == VirtualKeycodes.Delete)
            {
                e.Handled = true;
                if (_originText != null) SpellCheckWhileTyping.AddWord(_originText);
                Hide();
            }
            if (e.KeyCode == VirtualKeycodes.Insert)
            {
                e.Handled = true;
                Hide();
                GlobalEventsApp.OnSpellCheckReplaceAction(_suggestionsList[0].Suggestion);
            }
        }

        internal void Show(List<string> suggestions, string? text)
        {
            _timer.Interval = 4000;
            _timer.Start();
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            if (SettingsManager.Settings.SpellCheckWhileTypingUseNumber)
            {
                _numberList = new List<string>() { "1", "2", "3", "4", "5", "6", "7", "8", "9" };
            }
            else
            {
                _numberList = new List<string>() { "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9" };
            }
            _originText = text;
            _suggestionsList = new List<SuggestionsRes>();
            for (int i = 0; i < suggestions.Count; i++)
            {
                if (i == 9)
                {
                    break;
                }
                var s = suggestions[i];
                _suggestionsList.Add(new SuggestionsRes()
                {
                    Key = _numberList[i],
                    Suggestion = s
                });
            }
            LvSpellCheckAction.ItemsSource = _suggestionsList;

            ShowHide();
        }

        internal Action HideAction;

        internal void Hide(bool timer = false)
        {
            if (IsMouseOver && timer)
            {
                return;
            }
            _timer.Stop();
            HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler -= HookManagerMouseDown;
            _isShow = false;
            IsOpen = false;
            HideAction();
        }

        private void ShowHide()
        {
            Point curretPos = CarretPosition.GetPosition();
            IsOpen = true;
            UpdateLayout();
            if (curretPos.X != 0 && curretPos.Y != 0)
            {
                var pos = MousePosition.GetMousePoint(curretPos);
                HorizontalOffset = pos.X + 30;
                VerticalOffset = pos.Y - 30;
                Trace.WriteLine(pos.ToString());
            }
            else
            {
                System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
                HorizontalOffset = centrePos.X;
                VerticalOffset = centrePos.Y;
            }
            _isShow = true;
        }

        internal class SuggestionsRes
        {
            public string Key { get; set; } = String.Empty;
            public string Suggestion { get; set; } = String.Empty;
        }


        private void LvFastAction_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (LvSpellCheckAction.SelectedItem != null)
                {
                    if (_suggestionsList.Count > LvSpellCheckAction.SelectedIndex)
                    {
                        GlobalEventsApp.OnSpellCheckReplaceAction(_suggestionsList[LvSpellCheckAction.SelectedIndex]
                            .Suggestion);
                    }

                    Hide();
                }
            }
            catch
            {
                // ignore
            }
        }

        private void ListBoxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element)
                {
                    var item = GetSelectedListBoxItem(element);
                    if (item != null)
                    {
                        LvSpellCheckAction.SelectedItem = item.DataContext;
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        private RadListBoxItem? GetSelectedListBoxItem(FrameworkElement element)
        {
            try
            {
                var item = element;
                while (item != null && VisualTreeHelper.GetParent(item) != null && !(item is RadListBoxItem))
                {
                    item = VisualTreeHelper.GetParent(item) as FrameworkElement;
                }
                if (!(item is RadListBoxItem))
                {
                    return null;
                }
                return (RadListBoxItem)item;

            }
            catch
            {
                return null;
            }
        }

        private void SpellCheckWhileTypingWindow_OnMouseEnter(object sender, MouseEventArgs e)
        {
            _timer.Stop();
        }

        private void SpellCheckWhileTypingWindow_OnMouseLeave(object sender, MouseEventArgs e)
        {
            _timer.Interval = 2000;
            _timer.Start();
        }
    }
}
