﻿using Everylang.App.SettingsApp;
using Everylang.App.SpellCheck;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Spellchecking
{
    /// <summary>
    /// Interaction logic for SpellcheckingControl.xaml
    /// </summary>
    internal partial class SpellcheckingControl
    {
        internal SpellcheckingControl()
        {
            InitializeComponent();
        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SpellcheckingKeyboardShortcutsShort"), SettingsManager.Settings.SpellCheckShortcut, nameof(SettingsManager.Settings.SpellCheckShortcut), SpellCheckManager.Instance.PressedSpellCheck);

            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SpellcheckingSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SoundClick(object sender, RoutedEventArgs e)
        {
            SoundsForm? form = new SoundsForm("spellcheck");
            form.HeaderText = LocalizationManager.GetString("SpellcheckingSettingsWhileTypingSoundEdit");
            PageTransitionControl.Content = form;
            form.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                form = null;
            };
        }


        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }
    }
}
