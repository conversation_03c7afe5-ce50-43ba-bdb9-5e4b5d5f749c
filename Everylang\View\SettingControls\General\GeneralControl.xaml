﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.General.GeneralControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.Row="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <StackPanel Margin="20,10,0,0">
            <TextBlock
                FontSize="15"
                FontWeight="Bold"
                Text="{telerik:LocalizableResource Key=GeneralSettingsHeader}" />

            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.MinimizeToTray}"
                Margin="0,10,0,0"
                MinHeight="0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsMinimizeToTray}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.CanClose}"
                Margin="0,2,0,0"
                MinHeight="0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsCanClose}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.IsStartUp}"
                Margin="0,2,0,0"
                MinHeight="0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsStartUpWithWindows}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.IsStartAdmin}"
                Margin="0,2,0,0"
                MinHeight="0"
                Visibility="{Binding Path=GeneralSettingsViewModel.IsAdmin, Converter={StaticResource BoolToVis}, FallbackValue=Collapsed}">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsStartAdmin}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.IsCheckUpdate}"
                Margin="0,2,0,0"
                MinHeight="0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsIsCheckUpdate}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="14"
                IsChecked="{Binding Path=GeneralSettingsViewModel.IsCheckUpdateBeta}"
                IsEnabled="{Binding Path=GeneralSettingsViewModel.IsCheckUpdate}"
                Margin="0,2,0,0"
                MinHeight="0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsIsCheckUpdateBeta}" />
            </CheckBox>
            <CheckBox
                Focusable="False"
                FontSize="12"
                IsChecked="{Binding Path=GeneralSettingsViewModel.IsStopWorkingFullScreen}"
                Margin="0,2,0,0"
                MinHeight="0">
                <TextBlock FontSize="12" Text="{telerik:LocalizableResource Key=GeneralSettingsIsStopWorkingFullScreen}" />
            </CheckBox>
            <StackPanel Margin="0,2,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=StopWorkingShortcut}" />
                <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                    <TextBox
                        Background="Transparent"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=GeneralSettingsViewModel.StopWorkingShortcut}"
                        ToolTip="{Binding Path=GeneralSettingsViewModel.StopWorkingShortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="StopWorkingShortcutClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
            </StackPanel>

            <StackPanel Margin="0,5,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=OpenMainWindowShortcut}" />
                <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                    <TextBox
                        Background="Transparent"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=GeneralSettingsViewModel.Shortcut}"
                        ToolTip="{Binding Path=GeneralSettingsViewModel.Shortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="NewShortCutClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
            </StackPanel>
            <StackPanel Margin="0,5,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=GeneralSettingsDataFilePath}" />
                <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                    <TextBox
                        Background="Transparent"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=GeneralSettingsViewModel.DataFilePath}"
                        ToolTip="{Binding Path=GeneralSettingsViewModel.DataFilePath}"
                        Width="350" />
                    <telerik:RadButton
                        Click="EditDataFilePathClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
            </StackPanel>
            <telerik:RadButton
                Margin="0,5,0,0"
                Click="OpenProxySettings"
                Content="{telerik:LocalizableResource Key=GeneralSettingsProxy}"
                Focusable="False"
                HorizontalAlignment="Left"/>
            <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Margin="0,0,0,0" Content="{telerik:LocalizableResource Key=GeneralSettingsExport}" Command="{Binding Path=GeneralSettingsViewModel.ExportCommand}" />
                <telerik:RadButton  Focusable="False" HorizontalAlignment="Left" Margin="10,0,0,0" Content="{telerik:LocalizableResource Key=GeneralSettingsImport}" Command="{Binding Path=GeneralSettingsViewModel.ImportCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
