﻿using LiteDB;

namespace Everylang.App.Data.DataModel
{
    public class ProgramsExceptionsDataModel
    {
        internal ObjectId Id { get; set; }
        internal string? Program { get; set; } = "";
        internal string? Title { get; set; } = "";

        public string? ProgramTitle
        {
            get
            {
                if (Title != "")
                {
                    return "<TITLE> " + Title;
                }
                return Program;
            }
        }

        public bool IsOnAutoSwitch { get; set; }
        public bool IsOnDiary { get; set; }
        public bool IsOnConverter { get; set; }
        public bool IsOnClipboard { get; set; }
        public bool IsOnClipboardImage { get; set; }
        public bool IsOnLayoutFlag { get; set; }
        public bool IsOnLayoutSwitcher { get; set; }
        public bool IsOnSpellCheckWhileTyping { get; set; }
        public bool IsOnSmartClick { get; set; }
        public bool IsOnAutochange { get; set; }
        public bool IsOnHotKeys { get; set; }


    }
}
