﻿using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.SmartClick;
using NHotkey;
using System;
using System.Drawing;
using System.Windows.Forms;
using Vanara.PInvoke;
using MousePosition = Everylang.App.Utilities.MousePosition;


namespace Everylang.App.SmartClick
{
    internal class SmartClickManager : IDisposable
    {
        private bool _isStarted;
        private string? _selectedText;
        private int _clicksMiddle;
        private int _mSystemDoubleClickTime;
        private DateTime _lasTimeMiddleClick;
        private Point _mouseLocation;
        private bool _isCaretNow;
        private Point _storedMouseLocation;
        private DateTime _lasTimeClick;
        private DateTime _lasTimeOpen;
        private bool _isLasOpen;
        private bool _isWasCopy;
        private bool _isWasPaste;
        private int _clicks;
        private bool _isMouseSelected;
        private bool _rbUpHandled;

        private SmallSmartClickWindow? _smallSmartClickWindow;

        private static SmartClickManager? _instance;

        internal static SmartClickManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SmartClickManager();
                }
                return _instance;
            }
        }

        internal void RestartWatcher()
        {
            StopWatcher();
            StartWatcher();
        }

        internal void StartWatcher()
        {
            if (SettingsManager.Settings.SmartClickIsOn && !_isStarted)
            {
                _clicksMiddle = 0;
                _selectedText = "";
                _smallSmartClickWindow = new SmallSmartClickWindow();
                _smallSmartClickWindow.CloseActionFirst += () =>
                {
                    _lasTimeOpen = DateTime.Now;
                    _isWasCopy = _smallSmartClickWindow.IsWasCopy;
                };
                _smallSmartClickWindow.CloseActionSecond += () =>
                {
                    _isWasPaste = _smallSmartClickWindow.IsWasPaste;
                    _isLasOpen = false;
                };
                _mSystemDoubleClickTime = (int)User32.GetDoubleClickTime();
                _lasTimeMiddleClick = new DateTime();
                HookCallBackMouseDown.CallbackEventHandler += HookManagerSwitcherMouseDown;
                HookCallBackMouseUp.CallbackEventHandler += HookManagerSwitcherMouseUp;
                HookCallBackMouseMove.CallbackEventHandler += HookManagerSmallMouseMove;
                _isStarted = true;
                if (SettingsManager.Settings.SmartClickShowOnPressHotKeys)
                {
                    ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SmartClickShortcut), SettingsManager.Settings.SmartClickShortcut, PressedShortcutSmartClick);
                }
            }
        }

        internal void StopWatcher()
        {
            if (_isStarted)
            {
                _smallSmartClickWindow = null;
                _mSystemDoubleClickTime = (int)User32.GetDoubleClickTime();
                _lasTimeMiddleClick = new DateTime();
                HookCallBackMouseDown.CallbackEventHandler -= HookManagerSwitcherMouseDown;
                HookCallBackMouseUp.CallbackEventHandler -= HookManagerSwitcherMouseUp;
                HookCallBackMouseMove.CallbackEventHandler -= HookManagerSmallMouseMove;
                _isStarted = false;
                ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.ConverterExpresionShortcut));
            }
        }

        private void HookManagerSwitcherMouseDown(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            HookManagerSmallMouseDown(e);
            _rbUpHandled = false;
            if (!SettingsManager.Settings.SmartClickIsOn)
            {
                return;
            }
            _isCaretNow = MousePosition.IsTextCursor;
            if (e.Button == GHMouseButtons.Middle && SettingsManager.Settings.SmartClickShowOnDoubleMiddle)
            {
                MouseDownMiddle();
            }
            else
            {
                _clicksMiddle = 0;
            }
            if (SettingsManager.Settings.SmartClickShowOnPressLeftAndRightMouseButtons)
            {
                if ((e.Button == GHMouseButtons.Right && (Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left))
                {
                    e.Handled = true;
                    _rbUpHandled = true;
                    ShowSmartClickhow();
                }
            }
        }


        #region Простым выделением

        private void HookManagerSmallMouseMove(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!SettingsManager.Settings.SmartClickMiniIsOn) return;
            if (MousePosition.IsTextCursor)
            {
                _isCaretNow = true;
            }
            _mouseLocation = new Point(e.PointerPos.X, e.PointerPos.Y);

        }

        private void HookManagerSmallMouseUp(GlobalMouseEventArgs e)
        {
            if (!SettingsManager.Settings.SmartClickMiniIsOn) return;
            _isMouseSelected = false;
            if (e.Button == GHMouseButtons.Left)
            {
                _isMouseSelected = _storedMouseLocation != _mouseLocation || _clicks == 2;
            }

            if (_isMouseSelected && !_isWasCopy && _isCaretNow)
            {
                if (!CheckActiveProcessFileName.CheckLayoutSmartClick())
                {
                    _isCaretNow = false;
                    return;
                }
                _smallSmartClickWindow?.Show(true);

                _isWasPaste = false;


            }
            else if ((DateTime.Now - _lasTimeOpen).TotalMilliseconds > 500 && (DateTime.Now - _lasTimeOpen).TotalSeconds < 7 && !_isWasPaste && _isWasCopy && !_isLasOpen)
            {
                if (_isCaretNow)
                {
                    _isLasOpen = true;
                    _smallSmartClickWindow?.Show(false);
                }
            }
            else
            {
                _isWasCopy = false;
            }
            _isCaretNow = false;
        }

        private void HookManagerSmallMouseDown(GlobalMouseEventArgs e)
        {
            if (!SettingsManager.Settings.SmartClickMiniIsOn) return;
            if (e.Button == GHMouseButtons.Left)
            {
                var locationTheSame = Math.Abs(_storedMouseLocation.X - e.PointerPos.X) < 3 &&
                    Math.Abs(_storedMouseLocation.Y - e.PointerPos.Y) < 3;
                if ((DateTime.Now - _lasTimeClick).TotalMilliseconds <= _mSystemDoubleClickTime && _clicks == 1 && locationTheSame)
                {
                    _clicks = 2;
                }
                else
                {
                    _clicks = 1;

                }
                _storedMouseLocation = new Point(e.PointerPos.X, e.PointerPos.Y);
                _lasTimeClick = DateTime.Now;
            }
        }

        #endregion

        private void ShowSmartClickhow()
        {
            _selectedText = "";
            _selectedText = ClipboardOperations.GetSelectionText();
            ShowSmartClick(false, false);
        }

        private void MouseDownMiddle()
        {
            if (!SettingsManager.Settings.SmartClickIsOn)
            {
                return;
            }
            _clicksMiddle = 0;
            if ((DateTime.Now - _lasTimeMiddleClick).TotalMilliseconds > _mSystemDoubleClickTime)
            {
                _lasTimeMiddleClick = DateTime.Now;
                return;
            }
            if ((DateTime.Now - _lasTimeMiddleClick).TotalMilliseconds <= _mSystemDoubleClickTime)
            {
                _clicksMiddle = 2;
            }
            _lasTimeMiddleClick = DateTime.Now;
        }

        private void HookManagerSwitcherMouseUp(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            HookManagerSmallMouseUp(e);
            if (_rbUpHandled && e.Button == GHMouseButtons.Right)
            {
                e.Handled = true;
            }

            _rbUpHandled = false;
            if (!SettingsManager.Settings.SmartClickIsOn)
            {
                return;
            }
            if (SettingsManager.Settings.SmartClickShowOnDoubleMiddle)
            {
                if (e.Button == GHMouseButtons.Middle && _clicksMiddle == 2)
                {
                    _clicksMiddle = 0;
                    _lasTimeMiddleClick = DateTime.Now;
                    ShowSmartClick(true, false);
                }
            }

        }

        private void ShowSmartClick(bool getSelectedText, bool isKeyboard)
        {
            try
            {
                if (MouseXKeyManager.FromMouseX)
                {
                    isKeyboard = false;
                }
                if (!CheckActiveProcessFileName.CheckLayoutSmartClick())
                {
                    return;
                }
                string? selectedText = _selectedText;
                if (getSelectedText)
                {
                    selectedText = ClipboardOperations.GetSelectionText();
                }
                var smartClickWindow = new SmartClickWindow();
                smartClickWindow.Closed += (_, _) =>
                {
                    smartClickWindow = null;
                };

                if (selectedText?.Trim().Replace("\r\n", "") != "")
                {
                    smartClickWindow.SourceText = selectedText;
                    smartClickWindow.ShowWindow(false, isKeyboard);
                }
                else
                {
                    smartClickWindow.ShowWindow(true, isKeyboard);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        internal void PressedShortcutSmartClick(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            ShowSmartClick(true, true);
        }

        public void Dispose()
        {
            StopWatcher();
        }
    }
}
