﻿using Everylang.App.ViewModels.SettingsModel;
using System;

namespace Everylang.App.ViewModels
{
    public class VMContainer
    {
        private static VMContainer? _instance;

        private VMContainer()
        {
            TranslationMainViewModel = new TranslationMainViewModel();
            //TranslationMiniViewModel = new TranslationMiniViewModel();
            MainWindowViewModel = new MainWindowViewModel();
            GeneralSettingsViewModel = new GeneralSettingsViewModel();
            TranslationSettingsViewModel = new TranslationSettingsViewModel();
            SpellcheckingSettingsViewModel = new SpellcheckingSettingsViewModel();
            SwitcherSettingsViewModel = new SwitcherSettingsViewModel();
            AutoSwitcherSettingsViewModel = new AutoSwitcherSettingsViewModel();
            AboutSettingsViewModel = new AboutSettingsViewModel();
            LangFlagSettingsViewModel = new LangFlagSettingsViewModel();
            ClipboardSettingsViewModel = new ClipboardSettingsViewModel();
            HistoryViewModel = new HistoryViewModel();
            ClipboardViewModel = new ClipboardViewModel();
            DiaryViewModel = new DiaryViewModel();
            ProgramsExceptionsViewModel = new ProgramsExceptionsViewModel();
            UniversalWindowSettingsViewModel = new UniversalWindowSettingsViewModel();
            SnippetsViewModel = new SnippetsViewModel();
            ConverterSettingsViewModel = new ConverterSettingsViewModel();
            OcrViewModel = new OcrViewModel();
            ProgramsSetLayoutViewModel = new ProgramsSetLayoutViewModel();
            AppearanceViewModel = new AppearanceViewModel();
            NotesListControlViewModel = new NotesListControlViewModel();
        }

        public static VMContainer Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new VMContainer();
                }
                return _instance;
            }
        }


        public TranslationMainViewModel TranslationMainViewModel { get; set; }
        //internal TranslationMiniViewModel TranslationMiniViewModel { get; set; }
        public MainWindowViewModel MainWindowViewModel { get; set; }
        public GeneralSettingsViewModel GeneralSettingsViewModel { get; set; }
        public TranslationSettingsViewModel TranslationSettingsViewModel { get; set; }
        public SpellcheckingSettingsViewModel SpellcheckingSettingsViewModel { get; set; }
        public SwitcherSettingsViewModel SwitcherSettingsViewModel { get; set; }
        public AboutSettingsViewModel AboutSettingsViewModel { get; set; }
        public LangFlagSettingsViewModel LangFlagSettingsViewModel { get; set; }
        public ClipboardSettingsViewModel ClipboardSettingsViewModel { get; set; }
        public HistoryViewModel HistoryViewModel { get; set; }
        public ClipboardViewModel ClipboardViewModel { get; set; }
        public DiaryViewModel DiaryViewModel { get; set; }
        public ProgramsExceptionsViewModel ProgramsExceptionsViewModel { get; set; }
        public UniversalWindowSettingsViewModel UniversalWindowSettingsViewModel { get; set; }
        public AutoSwitcherSettingsViewModel AutoSwitcherSettingsViewModel { get; set; }
        public SnippetsViewModel SnippetsViewModel { get; set; }
        public ConverterSettingsViewModel ConverterSettingsViewModel { get; set; }
        public OcrViewModel OcrViewModel { get; set; }
        public ProgramsSetLayoutViewModel ProgramsSetLayoutViewModel { get; set; }
        public AppearanceViewModel AppearanceViewModel { get; set; }
        public NotesListControlViewModel NotesListControlViewModel { get; set; }


        internal void RunProp(object ob, string classN, string propN, object data)
        {
            Type typeThis = this.GetType();
            var propThis = typeThis.GetProperty(classN);
            Type? typeProp = propThis?.PropertyType;
            var propProp = typeProp?.GetProperty(propN);
            if (propProp != null && !propProp.GetValue(ob)!.Equals(data))
            {
                propProp.SetValue(ob, data);
            }
        }
    }
}
