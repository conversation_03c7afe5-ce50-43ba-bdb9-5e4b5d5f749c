﻿using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Common.CommonWindow
{
    internal class ViewIndexConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            RadListBoxItem item = (RadListBoxItem)value;
            RadListBox? listView = ItemsControl.ItemsControlFromItemContainer(item) as RadListBox;
            if (listView != null)
            {
                int index = listView.ItemContainerGenerator.IndexFromContainer(item) + 1;
                if (index > 9)
                {
                    return "";
                }
                return index.ToString();
            }
            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    internal class ViewIndexTooltipConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            RadListBoxItem item = (RadListBoxItem)value;
            RadListBox? listView = ItemsControl.ItemsControlFromItemContainer(item) as RadListBox;
            if (listView != null)
            {
                int index = listView.ItemContainerGenerator.IndexFromContainer(item) + 1;
                if (index > 9)
                {
                    return "";
                }
                return LocalizationManager.GetString("CommonWindowPressKeyForPast") + " " + index;
            }
            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}