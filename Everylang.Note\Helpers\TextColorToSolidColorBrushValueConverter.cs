﻿using MaterialDesignColors;
using System;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using System.Windows.Media;

namespace Miminote
{
    internal class TextColorToSolidColorBrushValueConverter : IValueConverter
    {

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (null == value)
            {
                return null;
            }

            Color color = new Color();
            var swatch = new SwatchesProvider().Swatches.ToList().FirstOrDefault(x => x.Name.ToLower() == ((string)value).ToLower());
            if (swatch != null)
            {
                color = swatch.PrimaryHues.First(x => x.Name == "Primary100").Color;
            }
            return new SolidColorBrush(color);
            // You can support here more source types if you wish
            // For the example I throw an exception

            Type type = value.GetType();
            throw new InvalidOperationException("Unsupported type [" + type.Name + "]");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // If necessary, here you can convert back. Check if which brush it is (if its one),
            // get its Color-value and return it.
            throw new NotImplementedException();
        }
    }
}
