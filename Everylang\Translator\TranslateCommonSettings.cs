﻿using Everylang.App.SettingsApp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Everylang.App.Translator
{
    internal static class TranslateCommonSettings
    {
        internal static string[] TranslateServices { get; set; }

        internal static List<Language> AllLangs;

        private static List<Language> _favoriteLanguages;
        internal static List<Language> FavoriteLanguages
        {
            get => _favoriteLanguages;
            set
            {
                _favoriteLanguages = value;
                var favLangs = string.Join("|", _favoriteLanguages.Select(x => x.Abbreviation).ToList());
                SettingsManager.Settings.TranslateFavoriteLanguages = favLangs;
            }
        }

        internal static List<Language> ListGoogleLangs => GetLanguages(_listGoogleLangs);

        internal static List<Language> ListBingLangs => GetLanguages(_listBingLangs);

        internal static List<Language> ListYandexLangs => GetLanguages(_listYandexLangs);

        internal static List<Language> ListDeeplLangs => GetLanguages(_listDeeplLangs);

        internal static List<Language> ListMicrosoftLangs => GetLanguages(_listMicrosoftLangs);

        private static readonly List<Language> _listGoogleLangs;
        private static readonly List<Language> _listBingLangs;
        private static readonly List<Language> _listYandexLangs;
        private static readonly List<Language> _listDeeplLangs;
        private static readonly List<Language> _listMicrosoftLangs;

        static TranslateCommonSettings()
        {

            if (SettingsManager.Settings.TranslateLangTo == "")
            {
                SettingsManager.Settings.TranslateLangTo = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
            }
            if (SettingsManager.Settings.TranslateLangFrom == "")
            {
                SettingsManager.Settings.TranslateLangFrom = "en";
            }
            var dataLanguages = new GetLanguages();
            TranslateServices = new[] { "Google", "Bing", "Yandex", "DeepL", "Microsoft" };
            _listGoogleLangs = dataLanguages.LoadLangs("google");
            _listBingLangs = dataLanguages.LoadLangs("bing");
            _listYandexLangs = dataLanguages.LoadLangs("yandex");
            _listDeeplLangs = dataLanguages.LoadLangs("deepl");
            _listMicrosoftLangs = dataLanguages.LoadLangs("microsoft");
            AllLangs = _listGoogleLangs.GetRange(1, _listGoogleLangs.Count - 1);
            _favoriteLanguages = new List<Language>();
            foreach (var s in SettingsManager.Settings.TranslateFavoriteLanguages.Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries))
            {
                var lang = _listGoogleLangs.FirstOrDefault(x => x.Abbreviation == s);
                if (lang != null)
                {
                    _favoriteLanguages.Add(lang);
                }
            }
            if (SettingsManager.Settings.TranslateProvider > 4)
            {
                SettingsManager.Settings.TranslateProvider = 0;
            }
            TranslateServiceDefault = SettingsManager.Settings.TranslateProvider;
            TranslateServiceLast = TranslateServiceDefault;


        }

        internal static DateTime BingAppIdLastTime { get; set; }

        internal static string BingAppId { get; set; }

        internal static int TranslateServiceDefault { get; set; }

        internal static int TranslateServiceLast { get; set; }



        internal static Language? LanguageFromDefault(List<Language> listLangs)
        {
            Language? languageFromCurrent = listLangs.FirstOrDefault(x => x.Abbreviation == "auto");
            if (languageFromCurrent == null)
            {
                languageFromCurrent =
                    listLangs.FirstOrDefault(x =>
                        x.Abbreviation == SettingsManager.Settings.TranslateLangFrom);
            }

            if (languageFromCurrent == null)
            {
                languageFromCurrent = listLangs.FirstOrDefault(x =>
                    x.Abbreviation == CultureInfo.CurrentCulture.TwoLetterISOLanguageName);
            }

            if (languageFromCurrent == null)
            {
                languageFromCurrent = listLangs.First();
            }

            return languageFromCurrent;
        }

        internal static Language LanguageToDefault(List<Language> listLangs)
        {
            Language? languageToCurrent = listLangs.FirstOrDefault(
                x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo);
            if (languageToCurrent == null)
            {
                languageToCurrent = listLangs.First();
            }
            return languageToCurrent;
        }

        private static List<Language> GetLanguages(List<Language> languages)
        {
            var langs = new List<Language>(languages);
            langs.ForEach(x => x.IsFavorite = false);
            if (_favoriteLanguages.Any())
            {
                for (var i = 0; i < _favoriteLanguages.Count; i++)
                {
                    if (i == 0)
                    {
                        langs[i].IsFavorite = true;
                    }
                    var favoriteLanguage = _favoriteLanguages[i];
                    var index = langs.FindIndex(x => x.Abbreviation != null && x.Abbreviation.Equals(favoriteLanguage.Abbreviation));
                    if (index > -1)
                    {
                        langs[index].IsFavorite = true;
                        langs.Move(langs[index], i + 1);
                    }
                }
            }
            return langs;
        }

        internal static void Move<T>(this List<T> list, T item, int newIndex)
        {
            if (item != null)
            {
                var oldIndex = list.IndexOf(item);
                if (oldIndex > -1)
                {
                    list.RemoveAt(oldIndex);
                    if (newIndex > oldIndex) newIndex--;
                    list.Insert(newIndex, item);
                }
            }

        }
    }
}
