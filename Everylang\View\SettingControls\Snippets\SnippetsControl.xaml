﻿<UserControl x:Class="Everylang.App.View.SettingControls.Snippets.SnippetsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">

        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>
        <telerik:RadTransitionControl Grid.ZIndex="1" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <StackPanel  Margin="20,10,0,0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=AutochangeTextHeader}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>
            <telerik:RadToggleSwitchButton
                    Margin="0,5,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    CheckedContent="{telerik:LocalizableResource Key=AutochangeIsOn}"
                    ContentPosition="Right"
                    Focusable="False"
                    FontSize="14"
                    FontWeight="DemiBold"
                    IsChecked="{Binding Path=SnippetsViewModel.IsOnSnippets}"
                    UncheckedContent="{telerik:LocalizableResource Key=AutochangeIsOff}" />
            <CheckBox Focusable="False" IsEnabled="{Binding Path=SnippetsViewModel.IsOnSnippets}"  Margin="0,5,0,0" FontSize="14" IsChecked="{Binding Path=SnippetsViewModel.IsChangeOtherLayout}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeOtherLayout}" />
            </CheckBox>
            <CheckBox Focusable="False" IsEnabled="{Binding Path=SnippetsViewModel.IsOnSnippets}"  Margin="0,0,0,0" FontSize="14" IsChecked="{Binding Path=SnippetsViewModel.IsChangeCaseLetters}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeCaseLetters}" />
            </CheckBox>
            <CheckBox Focusable="False" IsEnabled="{Binding Path=SnippetsViewModel.IsOnSnippets}"  Margin="0,0,0,0" FontSize="14" IsChecked="{Binding Path=SnippetsViewModel.IsShowMiniWindow}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeShowMiniWindow}" />
            </CheckBox>
            <StackPanel Margin="0,5,0,0">

                <RadioButton  Focusable="False" MinHeight="0" IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}" FontSize="13" Margin="0,0,0,0"  IsChecked="{Binding Path=SnippetsViewModel.IsEnabledCountUsage}" Background="Transparent">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeIsEnabledCountUsage}" />
                </RadioButton>

                <RadioButton  Focusable="False" MinHeight="0" IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}" FontSize="13" Margin="0,0,0,0" IsChecked="{Binding Path=SnippetsViewModel.IsEnabledSortingAlphabet}" Background="Transparent">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeSortingByAlphabet}" />
                </RadioButton>

            </StackPanel>
            <!--<CheckBox Focusable="False" IsEnabled="{Binding Path=SnippetsViewModel.IsOnSnippets}"  Margin="0,0,0,0" FontSize="14" IsChecked="{Binding Path=SnippetsViewModel.IsEnabledCountUsage}" >
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeIsEnabledCountUsage}" />
            </CheckBox>-->
            <TextBlock Margin="0,10,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeChangeMethods}" />
            <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" IsEnabled="{Binding Path=SnippetsViewModel.IsOnSnippets}" Focusable="False"  Width="200" HorizontalAlignment="Left"  Margin="0,8,0,0"  ItemsSource="{Binding Path=SnippetsViewModel.ChangeMethodsList}" SelectedIndex="{Binding Path=SnippetsViewModel.ChangeMethodsCurrentIndex, Mode=TwoWay, NotifyOnSourceUpdated=True}"/>
            <TextBlock  IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}" Margin="0,5,0,0"  FontSize="14"  Text="{telerik:LocalizableResource Key=AutochangeKeyboardShortcuts}" />
            <StackPanel Orientation="Horizontal" Margin="0,8,0,0" IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}">
                <TextBox IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=SnippetsViewModel.Shortcut}" ToolTip="{Binding Path=SnippetsViewModel.Shortcut}"/>
                <telerik:RadButton Focusable="False"  Margin="5,0,0,0" Click="AutochangeShortcutShowListClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
            </StackPanel>
            <TextBlock  IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}" Margin="0,5,0,0" FontSize="14"  Text="{telerik:LocalizableResource Key=AutochangeKeyboardShortcutsAddNew}" />
            <StackPanel Orientation="Horizontal" Margin="0,8,0,0" IsEnabled="{Binding SnippetsViewModel.IsOnSnippets}">
                <TextBox IsReadOnly="True"  HorizontalAlignment="Left" Width="350" Text="{Binding Path=SnippetsViewModel.ShortcutAddNew}" ToolTip="{Binding Path=SnippetsViewModel.ShortcutAddNew}"/>
                <telerik:RadButton Focusable="False"  Margin="5,0,0,0" Click="AutochangeShortcutAddNewClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
