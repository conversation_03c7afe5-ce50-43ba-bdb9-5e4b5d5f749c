﻿using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Clipboard
{
    /// <summary>
    /// Interaction logic for ClipboardControl.xaml
    /// </summary>
    internal partial class ClipboardControl
    {
        internal ClipboardControl()
        {
            InitializeComponent();
        }

        private void ClipboardPasteWithoutFormattingShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardShortcuts"), SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut, nameof(SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut), ClipboardHookManager.Instance.PressedClipboardPasteWithoutFormatting);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ClipboardClipboardShortcutViewClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardViewShortcuts"), SettingsManager.Settings.ClipboardShowHistoryShortcut, nameof(SettingsManager.Settings.ClipboardShowHistoryShortcut), ClipboardHookManager.Instance.PressedClipboardView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ShortcutView = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ClipboardShortcutRoundClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardRoundShortcutsShort"), SettingsManager.Settings.ClipboardPasteRoundShortcut, nameof(SettingsManager.Settings.ClipboardPasteRoundShortcut), ClipboardHookManager.Instance.PressedClipboardRoundPaste);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ShortcutRound = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void SoundClick(object sender, RoutedEventArgs e)
        {
            SoundsForm? form = new SoundsForm("clipboard");
            form.HeaderText = LocalizationManager.GetString("ClipboardSound");
            PageTransitionControl.Content = form;
            form.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                form = null;
            };
        }
    }
}
