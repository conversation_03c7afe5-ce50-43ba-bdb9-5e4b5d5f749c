﻿<UserControl x:Class="Everylang.App.View.MainControls.NotesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:materialIcons="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:noteList="clr-namespace:Everylang.App.View.Controls.NoteList"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid IsEnabled="{Binding Path=NotesListControlViewModel.jgebhdhs}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <StackPanel Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
            <Grid
                HorizontalAlignment="Stretch"
                Margin="5"
                VerticalAlignment="Top">
                <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                    <telerik:RadToggleButton
                        Name="OpenNotesListButton"
                        IsChecked="{Binding NotesListControlViewModel.IsShowingAll, Mode=TwoWay}"
                        Focusable="False"
                        Margin="0,0,0,0">
                        <telerik:RadToggleButton.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialIcons:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="ListBoxOutline" />
                                <TextBlock VerticalAlignment="Center" Margin="3,0,0,0" Text="{telerik:LocalizableResource Key=NotesList}" />
                                
                            </StackPanel>
                            
                        </telerik:RadToggleButton.Content>
                    </telerik:RadToggleButton>
                    <telerik:RadToggleButton
                        Name="OpenNotesArchiveButton"
                        IsChecked="{Binding NotesListControlViewModel.IsShowingArchived, Mode=TwoWay}"
                        Focusable="False"
                        Margin="0,0,10,0">
                        <telerik:RadToggleButton.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialIcons:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="ArchiveOutline" />
                                <TextBlock VerticalAlignment="Center" Margin="3,0,0,0" Text="{telerik:LocalizableResource Key=NoteArchiveList}" />

                            </StackPanel>

                        </telerik:RadToggleButton.Content>
                    </telerik:RadToggleButton>
                </StackPanel>
                <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                    <telerik:RadButton
                        Focusable="False"
                        Command="{Binding Path=NotesListControlViewModel.AddNewCommand}"
                        ToolTip="{telerik:LocalizableResource Key=NotesAddNew}"
                        Margin="0,0,0,0">
                        <telerik:RadButton.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialIcons:MaterialIcon Width="18"
                                                            Height="18"
                                                            Kind="Plus" />
                                <TextBlock VerticalAlignment="Center" Margin="3,0,0,0" Text="{telerik:LocalizableResource Key=ProgramsExceptionsAddNew}" />

                            </StackPanel>

                        </telerik:RadButton.Content>
                    </telerik:RadButton>
                    <telerik:RadToggleSwitchButton
                        CheckedContent="{telerik:LocalizableResource Key=NotesIsShowing}"
                        ContentPosition="Left"
                        Focusable="False"
                        HorizontalAlignment="Right"
                        IsChecked="{Binding NotesListControlViewModel.IsOnNotes, Mode=TwoWay}"
                        Margin="0,0,5,0"
                        UncheckedContent="{telerik:LocalizableResource Key=NotesIsHiding}"
                        VerticalAlignment="Center" />
                    <telerik:RadAutoSuggestBox
                        Text="{Binding NotesListControlViewModel.SearchText, Mode=TwoWay}"
                        ClearButtonVisibility="Auto"
                        WatermarkContent="{telerik:LocalizableResource Key=SearchHelperText}"
                        Name="SearchTextBox"
                        Width="250" />
                </StackPanel>
            </Grid>
        </StackPanel>
        <Grid Grid.Row="1">
            <noteList:NotesListControl x:Name="NotesList"/>
        </Grid>
        
    </Grid>
</UserControl>
