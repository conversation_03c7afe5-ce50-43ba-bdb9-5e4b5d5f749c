﻿using Everylang.App.LangFlag;
using Everylang.App.SettingsApp;
using System;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class LangFlagSettingsViewModel : ViewModelBase
    {
        public int OpacityIconLangInfo
        {
            get
            {
                return SettingsManager.Settings.LangFlagOpacityIcon;
            }
            set
            {
                SettingsManager.Settings.LangFlagOpacityIcon = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(OpacityIcon));
            }
        }

        public int SizeIconLangInfo
        {
            get
            {
                return SettingsManager.Settings.LangFlagSizeIcon;
            }
            set
            {
                SettingsManager.Settings.LangFlagSizeIcon = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(FontSizeLangInfo));
                base.OnPropertyChanged(nameof(ImageSize));
                base.OnPropertyChanged(nameof(FontSizeLangInfoForLarge));
                base.OnPropertyChanged(nameof(ImageSizeForLarge));
                base.OnPropertyChanged(nameof(ImageSizeCaps));
                base.OnPropertyChanged(nameof(ImageSizeForLargeCaps));

            }
        }

        internal bool FontSizeForCarret { get; set; }

        public double FontSizeLangInfo
        {
            get
            {
                if (FontSizeForCarret)
                {
                    return 10 + Convert.ToDouble(SizeIconLangInfo) / Convert.ToDouble(10);
                }
                return 12 + Convert.ToDouble(SizeIconLangInfo) / Convert.ToDouble(10);
            }
        }

        public double FontSizeLangInfoForLarge
        {
            get
            {
                return 20 + Convert.ToDouble(SizeIconLangInfo) / Convert.ToDouble(6);
            }
        }


        public double ImageSizeForLarge
        {
            get
            {
                return Convert.ToDouble(SizeIconLangInfo) / 95 + 0.6;
            }
        }

        public double ImageSizeForLargeCaps
        {
            get
            {
                return Convert.ToDouble(SizeIconLangInfo) / 95 + 0.2;
            }
        }

        internal double LangInfoLargeWindowPosX
        {
            get
            {
                return SettingsManager.Settings.LangInfoLargeWindowPosX;
            }
            set
            {
                SettingsManager.Settings.LangInfoLargeWindowPosX = value;
                base.OnPropertyChanged();
            }
        }

        internal double LangInfoLargeWindowPosY
        {
            get
            {
                return SettingsManager.Settings.LangInfoLargeWindowPosY;
            }
            set
            {
                SettingsManager.Settings.LangInfoLargeWindowPosY = value;
                base.OnPropertyChanged();
            }
        }

        public double ImageSize => Convert.ToDouble(SizeIconLangInfo) / 100 + 0.3;

        public double ImageSizeCaps => Convert.ToDouble(SizeIconLangInfo) / 100 + 0.1;

        public double OpacityIcon
        {
            get => Convert.ToDouble(SettingsManager.Settings.LangFlagOpacityIcon) / 100;
            set => base.OnPropertyChanged(nameof(OpacityIconLangInfo));
        }

        public bool ShowIconsIsOn => IsLangInfoWindowShowForMouse || IsLangInfoWindowShowForCaret || IsLangInfoInTray || IsLangInfoWindowShowLargeWindow;

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsHideIndicateInFullScreenApp));
                base.OnPropertyChanged(nameof(IsLangInfoWindowShowLargeWindow));
                base.OnPropertyChanged(nameof(IsLangInfoWindowShowForMouse));
                base.OnPropertyChanged(nameof(IsLangInfoWindowShowForCaret));
                base.OnPropertyChanged(nameof(IsLangInfoInTray));
                base.OnPropertyChanged(nameof(IsLangInfoShowIcons));
                base.OnPropertyChanged(nameof(IsIndicateCapsLockState));
                base.OnPropertyChanged(nameof(IsIndicateNumLockState));

            }
        }

        public bool IsHideIndicateInFullScreenApp
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.IsHideIndicateInFullScreenApp;
            }
            set
            {
                if (_isPro) SettingsManager.Settings.IsHideIndicateInFullScreenApp = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsLangInfoWindowShowLargeWindow
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.LangFlagShowLargeWindow;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowLargeWindow = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged(nameof(ShowIconsIsOn));
                base.OnPropertyChanged();
            }
        }


        public bool IsLangInfoWindowShowForMouse
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.LangFlagShowForMouse;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowForMouse = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged(nameof(ShowIconsIsOn));
                base.OnPropertyChanged();
            }
        }


        public bool IsLangInfoWindowShowForCaret
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.LangFlagShowForCaret;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowForCaret = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged(nameof(ShowIconsIsOn));
                base.OnPropertyChanged();
            }
        }

        public bool IsLangInfoInTray
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.LangFlagShowInTray;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowInTray = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged(nameof(ShowIconsIsOn));
                base.OnPropertyChanged();
            }
        }

        public bool IsLangInfoShowIcons
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.LangFlagShowIcons;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowIcons = value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged();
            }
        }

        public bool IsIndicateCapsLockState
        {
            get { return jgebhdhs && SettingsManager.Settings.LangFlagIsIndicateCapsLockState; }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagIsIndicateCapsLockState = value;
                // if (!value)
                // {
                //     GlobalEventsApp.OnEventCapsLock(false);
                // }
                base.OnPropertyChanged();
            }
        }

        public bool IsIndicateNumLockState
        {
            get { return jgebhdhs && SettingsManager.Settings.LangFlagIsIndicateNumLockState; }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagIsIndicateNumLockState = value;
                // if (!value)
                // {
                //     GlobalEventsApp.OnEventNumLock(false);
                // }
                base.OnPropertyChanged();
            }
        }

        public bool IsLangInfoShowIconsNot
        {
            get { return jgebhdhs && !SettingsManager.Settings.LangFlagShowIcons; }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.LangFlagShowIcons = !value;
                LangInfoManager.LangFlagRestart();
                base.OnPropertyChanged(nameof(IsLangInfoShowIcons));
            }
        }

        public int PosCarretX
        {
            get { return SettingsManager.Settings.LangFlagPosCarretX; }
            set
            {
                if (Equals(value, SettingsManager.Settings.LangFlagPosCarretX))
                {
                    return;
                }

                SettingsManager.Settings.LangFlagPosCarretX = value;
                OnPropertyChanged();
            }
        }

        public int PosCarretY
        {
            get { return SettingsManager.Settings.LangFlagPosCarretY; }
            set
            {
                if (Equals(value, SettingsManager.Settings.LangFlagPosCarretY))
                {
                    return;
                }

                SettingsManager.Settings.LangFlagPosCarretY = value;
                OnPropertyChanged();
            }
        }

        public int PosMouseX
        {
            get { return SettingsManager.Settings.LangFlagPosMouseX; }
            set
            {
                if (Equals(value, SettingsManager.Settings.LangFlagPosMouseX))
                {
                    return;
                }

                SettingsManager.Settings.LangFlagPosMouseX = value;
                OnPropertyChanged();
            }
        }

        public int PosMouseY
        {
            get { return SettingsManager.Settings.LangFlagPosMouseY; }
            set
            {
                if (Equals(value, SettingsManager.Settings.LangFlagPosMouseY))
                {
                    return;
                }

                SettingsManager.Settings.LangFlagPosMouseY = value;
                OnPropertyChanged();
            }
        }


    }
}
