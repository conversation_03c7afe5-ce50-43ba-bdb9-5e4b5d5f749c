﻿using Everylang.App.Converter;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Converter
{
    /// <summary>
    /// Interaction logic for ConverterControl.xaml
    /// </summary>
    internal partial class ConverterControl
    {
        internal ConverterControl()
        {
            InitializeComponent();
        }

        private void ExpressionOpenWindowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsOpenWindow"), SettingsManager.Settings.ConverterOpenWindowShortcut, nameof(SettingsManager.Settings.ConverterOpenWindowShortcut), ConverterManager.Instance.PressedConverterOpenWindow);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutOpenWindow = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ExpressionShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsExpression"), SettingsManager.Settings.ConverterExpresionShortcut, nameof(SettingsManager.Settings.ConverterExpresionShortcut), ConverterManager.Instance.PressedConverterExpresion);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutExpresion = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void TransliterationShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsTransliteration"), SettingsManager.Settings.ConverterTransliterationShortcut, nameof(SettingsManager.Settings.ConverterTransliterationShortcut), ConverterManager.Instance.PressedConvertTransliteration);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutTransliteration = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void EncloseTextQuotationMarksShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsEncloseTextQuotationMarks"), SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut, nameof(SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut), ConverterManager.Instance.PressedConvertEncloseTextQuotationMarks);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void OpenFramesSettingsClick(object sender, RoutedEventArgs e)
        {
            ConverterFramesControl? converterFramesControl = new ConverterFramesControl();
            PageTransitionControl.Content = converterFramesControl;
            converterFramesControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                converterFramesControl = null;
            };
        }

        private void CamelCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsCamelCase"), SettingsManager.Settings.ConverterShortcutCamelCase, nameof(SettingsManager.Settings.ConverterShortcutCamelCase), ConverterManager.Instance.PressedCamelCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutCamelCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void PascalCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsPascalCase"), SettingsManager.Settings.ConverterShortcutPascalCase, nameof(SettingsManager.Settings.ConverterShortcutPascalCase), ConverterManager.Instance.PressedPascalCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutPascalCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
        private void KebabCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKebabCase"), SettingsManager.Settings.ConverterShortcutKebabCase, nameof(SettingsManager.Settings.ConverterShortcutKebabCase), ConverterManager.Instance.PressedKebabCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutKebabCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
        private void SnakeCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsSnakeCase"), SettingsManager.Settings.ConverterShortcutSnakeCase, nameof(SettingsManager.Settings.ConverterShortcutSnakeCase), ConverterManager.Instance.PressedSnakeCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSnakeCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void ReplaceSelTextShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterReplaceSelText"), SettingsManager.Settings.ConverterShortcutReplaceSelText, nameof(SettingsManager.Settings.ConverterShortcutReplaceSelText), ConverterManager.Instance.PressedReplaceSelText);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutReplaceSelText = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
    }
}
