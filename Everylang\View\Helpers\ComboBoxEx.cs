﻿using System.Windows;
using System.Windows.Controls;

namespace Everylang.App.View.Helpers
{
    internal class ComboBoxEx : ComboBox
    {
        #region SelectedTemplateOverride

        /// <summary>
        /// SelectedTemplateOverride Dependency Property
        /// </summary>
        internal static readonly DependencyProperty SelectedTemplateOverrideProperty =
            DependencyProperty.Register("SelectedTemplateOverride",
            typeof(DataTemplate), typeof(ComboBoxEx),
                new FrameworkPropertyMetadata(null as DataTemplate));

        /// <summary>
        /// Gets or sets the SelectedTemplateOverride property.  
        /// </summary>
        internal DataTemplate SelectedTemplateOverride
        {
            get { return (DataTemplate)GetValue(SelectedTemplateOverrideProperty); }
            set { SetValue(SelectedTemplateOverrideProperty, value); }
        }

        #endregion
    }
}
