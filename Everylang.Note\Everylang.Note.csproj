﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>true</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <LangVersion>12.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>el_key.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Design" />
    <Reference Include="Telerik.Windows.Controls">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\favicon.ico" />
    <Resource Include="Resources\taskbar.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Everylang.Common\Everylang.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="gong-wpf-dragdrop">
      <Version>3.2.1</Version>
    </PackageReference>
    <PackageReference Include="LiteDB">
      <Version>5.0.21</Version>
    </PackageReference>
    <PackageReference Include="MaterialDesignThemes">
      <Version>5.1.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Serilog">
      <Version>4.1.0</Version>
    </PackageReference>
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Drawing.Common">
      <Version>9.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Management" Version="9.0.0" />
    <PackageReference Include="Vanara.PInvoke.DwmApi">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="Vanara.Windows.Shell">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="WindowsAPICodePack-Core">
      <Version>1.1.1</Version>
    </PackageReference>
    <PackageReference Include="WindowsAPICodePackShell">
      <Version>8.0.6</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Resources\Resource.Designer.cs" />
  </ItemGroup>
</Project>