﻿using Everylang.App.SettingsApp;
using Everylang.App.ViewModels.SettingsModel;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using UserControl = System.Windows.Controls.UserControl;

namespace Everylang.App.View.SettingControls.AutoSwitch
{
    /// <summary>
    /// Interaction logic for AutoSwitchControl.xaml
    /// </summary>
    internal partial class AutoSwitchControl : UserControl
    {
        private SelectLangForSwitchViewModel _model;

        internal AutoSwitchControl()
        {
            InitializeComponent();
            PreparationLangForSwitch();
        }

        private void PreparationLangForSwitch()
        {
            _model = new SelectLangForSwitchViewModel(SettingsManager.Settings.AutoSwitcherNotTrueListOfLang, true);
            ComboTrueListOfLang.ItemsSource = _model.Items;
            ComboTrueListOfLang.SelectionChanged += SelectedChanged;
            
            ComboTrueListOfLang.SelectedItems.Clear();
            if (_model.SelectedItems != null)
            {
                foreach (var item in _model.SelectedItems)
                {
                    ComboTrueListOfLang.SelectedItems.Add(item);
                }
            }
        }

        private void SelectedChanged(object sender, SelectionChangedEventArgs e)
        {
            SettingsManager.Settings.AutoSwitcherNotTrueListOfLang = "";
            _model.SelectedItems = new Dictionary<string, object?>();
            foreach (KeyValuePair<string, object?> selectedItem in ComboTrueListOfLang.SelectedItems)
            {
                _model.SelectedItems.Add(selectedItem.Key, selectedItem.Value);
            }

            if (_model.Items != null)
            {
                foreach (var trueLang in _model.Items)
                {
                    if (_model.SelectedItems.Count > 0 && !_model.SelectedItems.ContainsKey(trueLang.Key))
                    {
                        SettingsManager.Settings.AutoSwitcherNotTrueListOfLang += trueLang.Value + ";";
                    }
                }
            }
        }

        private void OpenRulesListOnClick(object sender, RoutedEventArgs e)
        {
            AutoSwitchListRules? autoSwitchListRules = new AutoSwitchListRules();
            PageTransitionControl.Content = autoSwitchListRules;
            autoSwitchListRules.HidePanel += (sender1, e1) =>
            {
                PageTransitionControl.Content = null;
                autoSwitchListRules = null;
            };
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }
    }


}
