﻿using Everylang.Common.LogManager;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace Everylang.App.LangFlag
{
    static class LangInfoManager
    {
        private static LangFlagInfo? _langFlagInfo;
        private static bool _isStarted;
        internal static List<LangClass>? LangClassList;
        internal static string OldLang = "";

        internal static void Start()
        {
            if (_isStarted)
            {
                return;
            }

            OldLang = "";
            _isStarted = true;

            InputLanguageCollection installedInputLanguages = InputLanguage.InstalledInputLanguages;
            LangClassList = new List<LangClass>();
            List<Color> colors = new List<Color>
            {
                ColorTranslator.FromHtml("#007BFF"),
                ColorTranslator.FromHtml("#DC3545"),
                ColorTranslator.FromHtml("#28A745"),
                ColorTranslator.FromHtml("#FFC107"),
                ColorTranslator.FromHtml("#17A2B8"),
                ColorTranslator.FromHtml("#00695c"),
                ColorTranslator.FromHtml("#795548"),
                ColorTranslator.FromHtml("#6a1b9a"),
                ColorTranslator.FromHtml("#c62828")
            };

            for (var i = 0; i < installedInputLanguages.Count; i++)
            {
                try
                {
                    InputLanguage lang = installedInputLanguages[i];
                    var englishName = lang.Culture.EnglishName.ToLower();
                    var m = Regex.Match(englishName, @"\((.*)\)");
                    if (m.Groups.Count > 1)
                    {
                        string res;
                        var resArr = m.Groups[1].Value.Split(',');
                        if (resArr.Length > 1)
                        {
                            res = resArr.Last().Trim();
                        }
                        else
                        {
                            res = resArr[0].Trim();
                        }

                        englishName = res.Replace(" ", "-");
                    }

                    if (LangClassList.FirstOrDefault(x => x.EnglishLangName != null && x.EnglishLangName.Equals(englishName)) != null)
                    {
                        continue;
                    }


                    LangClass langClass = new LangClass
                    {
                        EnglishLangName = englishName
                    };
                    var assembly = Assembly.GetExecutingAssembly();
                    var manifestResourceNames = assembly.GetManifestResourceNames();
                    var langFlagIcon =
                        assembly.GetManifestResourceStream(manifestResourceNames.FirstOrDefault(x => x.Contains($"FlagsImages.{englishName}.png")) ?? string.Empty);
                    if (langFlagIcon != null)
                    {
                        Image image = new Bitmap(langFlagIcon);

                        Bitmap bitmap = ResizeImage(image, 16, 16);
                        var icon = Icon.FromHandle(bitmap.GetHicon());
                        langClass.FlagIcon = icon;
                    }
                    using Bitmap? bitmapText = CreateBitmapImage(lang.Culture.TwoLetterISOLanguageName.ToUpper(), colors[i]);
                    if (bitmapText != null)
                    {
                        var iconText = Icon.FromHandle(bitmapText.GetHicon());
                        langClass.TwoLetterIcon = iconText;
                    }

                    LangClassList.Add(langClass);
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            }

            _langFlagInfo = new LangFlagInfo();
            _langFlagInfo.Start();
        }

        private static Bitmap ResizeImage(Image image, int width, int height)
        {
            var destRect = new Rectangle(0, 0, width, height);
            var destImage = new Bitmap(width, height);

            destImage.SetResolution(image.HorizontalResolution, image.VerticalResolution);

            using var graphics = Graphics.FromImage(destImage);
            graphics.CompositingMode = CompositingMode.SourceCopy;
            graphics.CompositingQuality = CompositingQuality.HighQuality;
            graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            graphics.SmoothingMode = SmoothingMode.HighQuality;
            graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

            using var wrapMode = new ImageAttributes();
            wrapMode.SetWrapMode(WrapMode.TileFlipXY);
            graphics.DrawImage(image, destRect, 0, 0, image.Width, image.Height, GraphicsUnit.Pixel, wrapMode);

            return destImage;
        }

        internal static Icon? GetFlagIcon(string name)
        {
            if (LangClassList != null)
            {
                var langClass = LangClassList.FirstOrDefault(x => x.EnglishLangName != null && x.EnglishLangName.Equals(name));
                if (langClass != null)
                {
                    return langClass.FlagIcon;
                }
            }

            return null;
        }
        internal static Icon? GetTwoLetterIcon(string name)
        {
            if (LangClassList != null)
            {
                var langClass = LangClassList.FirstOrDefault(x => x.EnglishLangName != null && x.EnglishLangName.Equals(name));
                if (langClass != null)
                {
                    return langClass.TwoLetterIcon;
                }
            }

            return null;
        }


        private static Bitmap? CreateBitmapImage(string sImageText, Color brush)
        {

            try
            {
                Bitmap bmp = new Bitmap(32, 32);
                using var g = Graphics.FromImage(bmp);
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var localeName = sImageText;
                var displayText = localeName[0] + char.ToLower(localeName[1]).ToString();
                var font = new Font("Verdana", 16);

                var size = g.MeasureString(displayText, font);
                using Brush b = new SolidBrush(brush);
                g.FillRectangle(b, 0, 0, 32, 32);
                g.DrawString(displayText, font, Brushes.White, (int)((32 - size.Width) / 2),
                    (int)((32 - size.Height) / 2) + 2);

                return bmp;
            }
            catch
            {
                // ignore
            }
            return null;
        }

        internal static void Stop()
        {
            if (_isStarted && _langFlagInfo != null)
            {
                _langFlagInfo.Stop();
                _langFlagInfo = null;
                _isStarted = false;
            }

        }

        internal static void LangFlagRestart()
        {
            Stop();
            Start();
        }

        internal class LangClass
        {
            internal Icon? TwoLetterIcon { get; set; }
            internal Icon? FlagIcon { get; set; }
            internal string? EnglishLangName { get; set; }
        }

    }
}
