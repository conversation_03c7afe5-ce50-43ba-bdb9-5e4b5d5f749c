﻿using Everylang.App.HookManager;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System.Threading.Tasks;

namespace Everylang.App.SwitcherLang
{
    internal static class KeyboardLayoutSetDefaultLayout
    {
        internal static void Start()
        {
            GlobalLangChangeHook.ForegroundWindowChanged += ForegroundWindowChanged;
        }

        internal static void Stop()
        {
            GlobalLangChangeHook.ForegroundWindowChanged -= ForegroundWindowChanged;
        }

        private static async void ForegroundWindowChanged()
        {
            if (!SettingsManager.LicIsActivated) return;
            await Task.Delay(500);
            var model = CheckActiveProcessFileName.GetModelCurrentApp();
            if (!string.IsNullOrEmpty(model?.Lang))
            {
                var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[model.Lang.ToLower()]];
                if (KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl() != langCode)
                {
                    KeyboardLayoutSwitcher.SwitchToLayout(langCode, true);
                }
            }
        }
    }
}
