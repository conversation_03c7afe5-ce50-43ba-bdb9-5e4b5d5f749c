﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.Switcher.SwitcherControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.Row="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <StackPanel Margin="20,10,0,0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="15"
                    FontWeight="Bold"
                    Text="{telerik:LocalizableResource Key=SwitcherTab}" />
                <TextBlock
                    FontSize="15"
                    FontWeight="Bold"
                    Margin="7,0,0,0"
                    Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>

            <telerik:RadToggleSwitchButton
                CheckedContent="{telerik:LocalizableResource Key=SwitcherSettingsIsOn}"
                ContentPosition="Right"
                Focusable="False"
                FontSize="14"
                FontWeight="DemiBold"
                HorizontalAlignment="Left"
                IsChecked="{Binding Path=SwitcherSettingsViewModel.SwitcherIsOn}"
                Margin="0,0,0,0"
                UncheckedContent="{telerik:LocalizableResource Key=SwitcherSettingsIsOff}"
                VerticalAlignment="Center" />
        </StackPanel>
        <ScrollViewer CanContentScroll="True" Margin="0,70,0,0">
            <StackPanel Margin="20,0,0,0">
                <TextBlock
                    FontSize="14"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsMethodSelect}" />
                <telerik:RadComboBox
                    BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                    Focusable="False"
                    HorizontalAlignment="Left"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    ItemsSource="{Binding Path=SwitcherSettingsViewModel.SwitchMethods}"
                    Margin="0,1,0,0"
                    SelectedItem="{Binding Path=SwitcherSettingsViewModel.CurrentSwitchMethod}"
                    Width="350" />
                <StackPanel
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Orientation="Horizontal">
                    <CheckBox
                        Focusable="False"
                        FontSize="14"
                        IsChecked="{Binding Path=SwitcherSettingsViewModel.SwitcherSountIsOn}"
                        IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}">
                        <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsSwitcherSountIsOn}" />
                    </CheckBox>
                    <telerik:RadButton
                        Click="SoundClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="0,0,0,0"
                        Padding="5"
                        VerticalAlignment="Center" />
                </StackPanel>

                <TextBlock
                    FontSize="14"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsKeyboardShortcutsSwitch}" />
                <StackPanel
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,1,0,0"
                    Orientation="Horizontal">
                    <TextBox
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut}"
                        ToolTip="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="KeyboardShortcutsSwitchClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
                <TextBlock
                    FontSize="14"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsIsOnInsert}" />
                <StackPanel
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,1,0,0"
                    Orientation="Horizontal">
                    <TextBox
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut}"
                        ToolTip="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="SwitchTextLangForAllLineShortcutClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
                <TextBlock
                    FontSize="14"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsKeyboardShortcutsSwitchSelected}" />
                <StackPanel
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,1,0,0"
                    Orientation="Horizontal">
                    <TextBox
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=SwitcherSettingsViewModel.ShortcutSelected}"
                        ToolTip="{Binding Path=SwitcherSettingsViewModel.ShortcutSelected}"
                        Width="350" />
                    <telerik:RadButton
                        Click="SwitchTextShortcutSelectedClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0"
                        VerticalAlignment="Center" />
                </StackPanel>
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=SwitcherSettingsViewModel.LeaveTextSelectedAfterSwitch}"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsLeaveTextSelectedAfterSwitch}" />
                </CheckBox>

                <TextBlock
                    FontSize="14"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsKeyboardSwitchOn}" />
                <telerik:RadComboBox
                    BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                    Focusable="False"
                    HorizontalAlignment="Left"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    ItemsSource="{Binding Path=SwitcherSettingsViewModel.SwitchOnKeys}"
                    Margin="0,1,0,0"
                    SelectedItem="{Binding Path=SwitcherSettingsViewModel.CurrentSwitchOnKey}"
                    SelectionChanged="CurrentSwitchOnKeySelectionChanged"
                    ToolTip="{Binding Path=SwitcherSettingsViewModel.ToolTipForCurrentSwitchOnKey}"
                    Width="300" />
                <CheckBox
                    Focusable="False"
                    FontSize="14"
                    IsChecked="{Binding Path=SwitcherSettingsViewModel.SwitcherCtrlNumberIsOn}"
                    IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"
                    Margin="0,0,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsSwitcherCtrlNumberIsOn}" />
                </CheckBox>

                <TextBlock
                    FontSize="14"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherLangAndKeysForSwitch}" />
                <Grid IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}" VerticalAlignment="Stretch">
                    <Grid Name="GridLangAndKeysForSwitchList">
                        <telerik:RadComboBox
                            AllowMultipleSelection="True"
                            BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                            DisplayMemberPath="Key"
                            Focusable="False"
                            HorizontalAlignment="Left"
                            Margin="0,3,0,0"
                            VerticalAlignment="Center"
                            Width="300"
                            x:Name="ComboLangAndKeysForSwitchList" />
                    </Grid>
                </Grid>
                <TextBlock
                    FontSize="14"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,0"
                    Text="{telerik:LocalizableResource Key=SwitcherSettingsTrueListOfLang}" />
                <Grid IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}" VerticalAlignment="Stretch">
                    <Grid Name="GridForViewModel">
                        <telerik:RadComboBox
                            AllowMultipleSelection="True"
                            BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                            DisplayMemberPath="Key"
                            Focusable="False"
                            HorizontalAlignment="Left"
                            Margin="0,3,0,0"
                            ToolTip="{Binding Path=Text, RelativeSource={RelativeSource Self}}"
                            VerticalAlignment="Center"
                            Width="300"
                            x:Name="ComboTrueListOfLang" />
                    </Grid>
                </Grid>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>