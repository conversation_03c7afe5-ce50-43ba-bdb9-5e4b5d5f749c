﻿<Popup x:Class="Everylang.App.View.Controls.Snippets.SnippetsActionWindow"
                      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                      xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
                      xmlns:snippets1="clr-namespace:Everylang.App.View.Controls.Snippets"
                      x:Name="me" 
                      MinHeight="270" MinWidth="450" Height="270" Width="450" MaxHeight="600" MaxWidth="800" Placement="Mouse" StaysOpen="True" MouseLeftButtonDown="Border_PreviewMouseLeftButtonDown" Focusable="False"
                      x:ClassModifier="internal">
    <Popup.Resources>
        <ResourceDictionary>
            <snippets1:IndexConverterForLvSnippets x:Key="IndexConverterMuLvSnippets"></snippets1:IndexConverterForLvSnippets>
            <snippets1:IndexConverterForLvTags x:Key="IndexConverterMuLvTags"></snippets1:IndexConverterForLvTags>
            <Style x:Key="ImageButtonClose" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="IsBackgroundVisible" Value="False"/>
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="Close" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonSettings" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="IsBackgroundVisible" Value="False"/>
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="CogOutline" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonListSnippets" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="IsBackgroundVisible" Value="False"/>
                <Setter Property="Content">
                    <Setter.Value>
                        <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="FormatListBulleted" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="ImageButtonTop" TargetType="telerik:RadButton" BasedOn="{StaticResource {x:Type telerik:RadButton}}">
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="IsBackgroundVisible" Value="False"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="False">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="PinOff" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding Path=IsStayOnTop, ElementName=me}" Value="True">
                        <Setter Property="Content">
                            <Setter.Value>
                                <wpf:MaterialIcon Width="14"
                                              Height="14"
                                              Kind="Pin" />
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
            <RoutedUICommand x:Key="CommandCopySelectedText"/>
        </ResourceDictionary>

    </Popup.Resources>

    <Popup.CommandBindings>
        <CommandBinding Command="{StaticResource CommandCopySelectedText}" Executed="CopySelectedText" CanExecute="CommandBindingCopy_OnCanExecute" />
    </Popup.CommandBindings>
    <Border  BorderThickness="1" BorderBrush="{telerik:Windows11Resource ResourceKey=ReadOnlyBorderBrush}" >
        <Grid x:Name="GridMy" Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="50*" />
            <RowDefinition Height="25" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="2" />
        </Grid.ColumnDefinitions>
            <Thumb x:Name="ThumbMy" Opacity="0"/>
        <Border Name="Border" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2"/>
        <TextBlock Grid.Column="0" Grid.Row="0" Margin="10,0,0,0"
                   Text="{telerik:LocalizableResource Key=AutochangeTab}" FontSize="14" VerticalAlignment="Center"/>
        <WrapPanel Grid.Column="0" Grid.Row="0" Orientation="Horizontal" FlowDirection="RightToLeft"
                   VerticalAlignment="Center" x:Name="meWrapPanel">
                <telerik:RadButton Style="{StaticResource ImageButtonClose}" Click="ButtonClickClose"
                                   ToolTip="{telerik:LocalizableResource Key=CloseHeaderButton}"
                                   IsCancel="True" />
                <telerik:RadButton Margin="3,0,0,0" Style="{StaticResource ImageButtonTop}"
                                   ToolTip="{telerik:LocalizableResource Key=StayOnTopButton}"
                                   Click="ClickSetStayOnTop" />
                <telerik:RadButton Margin="3,0,0,0" Style="{StaticResource ImageButtonSettings}"
                                   ToolTip="{telerik:LocalizableResource Key=Settings}"
                                   Click="ClickOpenSettings" />
                <telerik:RadButton Margin="3,0,0,0" Style="{StaticResource ImageButtonListSnippets}"
                                   ToolTip="{telerik:LocalizableResource Key=AutochangeSnippetsList}"
                                   Click="ClickOpenListSnippets" />
        </WrapPanel>
        <Grid Grid.Column="0" Grid.Row="1" Margin="2,0,0,0" x:Name="StackPanelMu">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="150" />
            </Grid.ColumnDefinitions>
                <telerik:RadListBox BorderThickness="1"
                                    x:Name="LvSnippets"
                                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                    VerticalAlignment="Stretch" SelectionMode="Extended"                     
                                    PreviewMouseDoubleClick="LvSnippets_OnPreviewMouseDoubleClick">
                    <telerik:RadListBox.ContextMenu>
                    <ContextMenu>
                            <MenuItem Command="{StaticResource CommandCopySelectedText}" Header="{telerik:LocalizableResource Key=bCopy}"/>
                    </ContextMenu>
                </telerik:RadListBox.ContextMenu>
                    <telerik:RadListBox.ItemTemplate>
                    <DataTemplate>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="25" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label Content="{Binding Index, Converter={StaticResource IndexConverterMuLvSnippets}}"
                                   FontSize="10" VerticalAlignment="Top"
                                   ToolTip="{Binding Path=FastActionIndex, ElementName=me, Mode=Default}"/>
                            <TextBlock
                                Grid.Column="1" HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontSize="14"
                                Text="{Binding  Obj.ShortText}"
                                ToolTip="{Binding  Obj.Text}" />
                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button Margin="0,0,0,0" x:Name="ButtonReplace" Content="{Binding Path=PasteText, ElementName=me, Mode=Default}"/>
                                <Button Margin="5,0,0,0" x:Name="ButtonCopy" Content="{Binding Path=CopyText, ElementName=me, Mode=Default}"/>
                                <StackPanel.Style>
                                    <Style TargetType="StackPanel">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger
                                                Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}},Path=IsMouseOver}"
                                                Value="True">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </StackPanel.Style>
                            </StackPanel>
                            </Grid>
                        
                        </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
                    <telerik:RadListBox.ItemContainerStyle>
                        <Style TargetType="telerik:RadListBoxItem" BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}">
                            <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListBoxItemLvSnippets_MouseLeftButtonDown" />
                    </Style>
                </telerik:RadListBox.ItemContainerStyle>
            </telerik:RadListBox>

            <telerik:RadListBox
                x:Name="LvTags"
                Grid.Column="1"
                VerticalAlignment="Stretch"
                SelectionChanged="LvTags_SelectionChanged"
                SelectionMode="Extended">
                <telerik:RadListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                HorizontalAlignment="Left"
                                FontSize="14"
                                Text="{Binding Index, Converter={StaticResource IndexConverterMuLvTags}}" />
                                <TextBlock
                                HorizontalAlignment="Left"
                                FontSize="14"
                                Text="{Binding}" />
                        </StackPanel>
                        </DataTemplate>
                </telerik:RadListBox.ItemTemplate>
            </telerik:RadListBox>



            </Grid>

            <Border Grid.Row="2" Grid.ColumnSpan="2" Grid.Column="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="15" />
                </Grid.ColumnDefinitions>
                <Thumb HorizontalAlignment="Stretch" Grid.Column="0" Cursor="SizeNS" Opacity="0.01" Height="8"
                       VerticalAlignment="Bottom"
                       DragDelta="OnDragDeltaVertical" />
                    <telerik:RadMaskedTextInput Grid.Column="0"  x:Name="TextBoxSearch" IsClearButtonVisible="True" HorizontalAlignment="Stretch" BorderThickness="0"
                                                EmptyContent="{telerik:LocalizableResource Key=FastActionTextWindowSearch}" TextMode="PlainText"
                                                ValueChanged="TextBoxSearch_OnTextChanged" />
                    <ResizeGrip x:Name="WindowResizeGrip" Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            IsTabStop="False"
                            UseLayoutRounding="True"
                            Visibility="Visible" />
                <Thumb HorizontalAlignment="Stretch" Grid.Column="1" Cursor="SizeNWSE"
                       DragDelta="OnDragDeltaAll" Opacity="0.01" />
            </Grid>
        </Border>
        <Border Grid.Row="1" Grid.Column="1">
            <Thumb VerticalAlignment="Stretch" HorizontalAlignment="Stretch" Cursor="SizeWE" Opacity="0.01"
                   DragDelta="OnDragDeltaHorisontal" />
        </Border>

    </Grid>
    </Border>
</Popup>
