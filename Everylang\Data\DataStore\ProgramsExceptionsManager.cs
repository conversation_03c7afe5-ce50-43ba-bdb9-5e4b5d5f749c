﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    class ProgramsExceptionsManager
    {
        internal static IEnumerable<ProgramsExceptionsDataModel> GetAllData()
        {
            var collection = new List<ProgramsExceptionsDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsExceptionsDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        ProgramsExceptionsDataModel dataModel = new ProgramsExceptionsDataModel();

                        if (!bsonDocument["_id"].IsNull) dataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["IsOnAutoSwitch"].IsNull) dataModel.IsOnAutoSwitch = bsonDocument["IsOnAutoSwitch"].AsBoolean;
                        if (!bsonDocument["IsOnClipboard"].IsNull) dataModel.IsOnClipboard = bsonDocument["IsOnClipboard"].AsBoolean;
                        if (!bsonDocument["IsOnClipboardImage"].IsNull) dataModel.IsOnClipboardImage = bsonDocument["IsOnClipboardImage"].AsBoolean;
                        if (!bsonDocument["IsOnDiary"].IsNull) dataModel.IsOnDiary = bsonDocument["IsOnDiary"].AsBoolean;
                        if (!bsonDocument["IsOnLayoutFlag"].IsNull) dataModel.IsOnLayoutFlag = bsonDocument["IsOnLayoutFlag"].AsBoolean;
                        if (!bsonDocument["IsOnHotKeys"].IsNull) dataModel.IsOnHotKeys = bsonDocument["IsOnHotKeys"].AsBoolean;
                        if (!bsonDocument["IsOnLayoutSwitcher"].IsNull) dataModel.IsOnLayoutSwitcher = bsonDocument["IsOnLayoutSwitcher"].AsBoolean;
                        if (!bsonDocument["IsOnSmartClick"].IsNull) dataModel.IsOnSmartClick = bsonDocument["IsOnSmartClick"].AsBoolean;
                        if (!bsonDocument["IsOnAutochange"].IsNull) dataModel.IsOnAutochange = bsonDocument["IsOnAutochange"].AsBoolean;
                        if (!bsonDocument["IsOnSpellCheckWhileTyping"].IsNull) dataModel.IsOnSpellCheckWhileTyping = bsonDocument["IsOnSpellCheckWhileTyping"].AsBoolean;
                        if (!bsonDocument["IsOnConverter"].IsNull) dataModel.IsOnConverter = bsonDocument["IsOnConverter"].AsBoolean;
                        if (!bsonDocument["Program"].IsNull) dataModel.Program = bsonDocument["Program"].AsString;
                        if (!bsonDocument["Title"].IsNull) dataModel.Title = bsonDocument["Title"].AsString;
                        collection.Add(dataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddData(ProgramsExceptionsDataModel? programsExceptionsData)
        {
            try
            {
                if (programsExceptionsData == null)
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsExceptionsDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    programsExceptionsData.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = programsExceptionsData.Id;
                    bsonDocument["IsOnAutoSwitch"] = programsExceptionsData.IsOnAutoSwitch;
                    bsonDocument["IsOnClipboard"] = programsExceptionsData.IsOnClipboard;
                    bsonDocument["IsOnClipboardImage"] = programsExceptionsData.IsOnClipboardImage;
                    bsonDocument["IsOnDiary"] = programsExceptionsData.IsOnDiary;
                    bsonDocument["IsOnConverter"] = programsExceptionsData.IsOnConverter;
                    bsonDocument["IsOnHotKeys"] = programsExceptionsData.IsOnHotKeys;
                    bsonDocument["IsOnLayoutFlag"] = programsExceptionsData.IsOnLayoutFlag;
                    bsonDocument["IsOnLayoutSwitcher"] = programsExceptionsData.IsOnLayoutSwitcher;
                    bsonDocument["IsOnSmartClick"] = programsExceptionsData.IsOnSmartClick;
                    bsonDocument["IsOnAutochange"] = programsExceptionsData.IsOnAutochange;
                    bsonDocument["IsOnSpellCheckWhileTyping"] = programsExceptionsData.IsOnSpellCheckWhileTyping;
                    bsonDocument["Program"] = programsExceptionsData.Program;
                    bsonDocument["Title"] = programsExceptionsData.Title;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllData()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("ProgramsExceptionsDataModel"))
                    {
                        db.DropCollection("ProgramsExceptionsDataModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void UpdateData(ProgramsExceptionsDataModel? programsExceptionsData)
        {
            try
            {
                if (programsExceptionsData == null)
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsExceptionsDataModel");
                    var bsonDocument = schemelessCollection.FindById(programsExceptionsData.Id);
                    if (bsonDocument != null)
                    {
                        bsonDocument["IsOnAutoSwitch"] = programsExceptionsData.IsOnAutoSwitch;
                        bsonDocument["IsOnClipboard"] = programsExceptionsData.IsOnClipboard;
                        bsonDocument["IsOnClipboardImage"] = programsExceptionsData.IsOnClipboardImage;
                        bsonDocument["IsOnDiary"] = programsExceptionsData.IsOnDiary;
                        bsonDocument["IsOnHotKeys"] = programsExceptionsData.IsOnHotKeys;
                        bsonDocument["IsOnConverter"] = programsExceptionsData.IsOnConverter;
                        bsonDocument["IsOnLayoutFlag"] = programsExceptionsData.IsOnLayoutFlag;
                        bsonDocument["IsOnLayoutSwitcher"] = programsExceptionsData.IsOnLayoutSwitcher;
                        bsonDocument["IsOnSmartClick"] = programsExceptionsData.IsOnSmartClick;
                        bsonDocument["IsOnAutochange"] = programsExceptionsData.IsOnAutochange;
                        bsonDocument["Program"] = programsExceptionsData.Program;
                        bsonDocument["Title"] = programsExceptionsData.Title;
                        bsonDocument["IsOnSpellCheckWhileTyping"] = programsExceptionsData.IsOnSpellCheckWhileTyping;
                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelData(ProgramsExceptionsDataModel? programsExceptionsData)
        {
            try
            {
                if (programsExceptionsData == null)
                {
                    return;
                }
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsExceptionsDataModel");
                    if (programsExceptionsData.Id != null) schemelessCollection.Delete(programsExceptionsData.Id);
                    else
                    {
                        if (programsExceptionsData.Title != "")
                        {
                            schemelessCollection.DeleteMany(Query.EQ("Title", programsExceptionsData.Title));
                        }
                        else
                        {
                            schemelessCollection.DeleteMany(Query.EQ("Program", programsExceptionsData.Program));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
