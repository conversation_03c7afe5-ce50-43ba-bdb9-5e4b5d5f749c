﻿using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using Telerik.Windows.Controls;
using MouseEventArgs = System.Windows.Input.MouseEventArgs;

namespace Everylang.App.View.Helpers
{
    internal class TextBlockWithSelection : TextBlock
    {
        internal string? SelectedText { get; set; }

        TextPointer? _startSelectPosition;
        TextPointer? _endSelectPosition;
        readonly object _startBackgroundProperty;

        internal delegate void TextSelectedHandler(string selectedText);
        internal event TextSelectedHandler? TextSelected;

        internal TextBlockWithSelection()
        {
            HookCallBackMouseDown.CallbackEventHandler += MouseDownGlobal;
            TextRange otr = new TextRange(this.ContentStart, this.ContentEnd);
            _startBackgroundProperty = otr.GetPropertyValue(TextElement.BackgroundProperty);
        }

        private void MouseDownGlobal(GlobalMouseEventArgs e)
        {
            if (e.Button == GHMouseButtons.Left && (_startSelectPosition != null && _endSelectPosition != null))
            {
                TextRange otr = new TextRange(this.ContentStart, this.ContentEnd);
                _startSelectPosition = null;
                _endSelectPosition = null;
                otr.ApplyPropertyValue(TextElement.BackgroundProperty, _startBackgroundProperty);
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            if (e.LeftButton != MouseButtonState.Pressed)
            {
                return;
            }
            Point mousePoint = e.GetPosition(this);
            if (_startSelectPosition == null)
            {
                _startSelectPosition = this.GetPositionFromPoint(mousePoint, true);
            }
            _endSelectPosition = this.GetPositionFromPoint(mousePoint, true);
            TextRange ntr = new TextRange(_startSelectPosition, _endSelectPosition);
            ntr.ApplyPropertyValue(TextElement.BackgroundProperty, new SolidColorBrush(Windows11Palette.Palette.AccentSelectedColor));
            SelectedText = ntr.Text;
            if (TextSelected != null)
            {
                TextSelected(SelectedText);
            }
        }

    }
}
