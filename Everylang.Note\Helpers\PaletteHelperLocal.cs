﻿using MaterialDesignColors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using Application = System.Windows.Application;
using Color = System.Windows.Media.Color;

namespace Everylang.Note.Helpers
{
    class PaletteHelperLocal
    {
        internal void ReplacePrimaryColor(Swatch swatch, ResourceDictionary? parentDictionary = null)
        {
            if (swatch == null)
                throw new ArgumentNullException("swatch");
            List<Hue> list = swatch.PrimaryHues.ToList();
            Hue hue1 = list[2];
            Hue exemplarHue = swatch.ExemplarHue;
            Hue hue2 = list[7];
            foreach (Hue hue3 in swatch.PrimaryHues)
            {
                if (parentDictionary != null)
                {
                    ReplaceEntry(hue3.Name, hue3.Color, parentDictionary);
                    ReplaceEntry(hue3.Name + "Foreground", hue3.Foreground, parentDictionary);
                }
            }

            if (parentDictionary != null)
            {
                ReplaceEntry("PrimaryHueLightBrush", new SolidColorBrush(hue1.Color), parentDictionary);
                ReplaceEntry("PrimaryHueLightForegroundBrush", new SolidColorBrush(hue1.Foreground),
                    parentDictionary);
                ReplaceEntry("PrimaryHueMidBrush", new SolidColorBrush(exemplarHue.Color),
                    parentDictionary);
                ReplaceEntry("PrimaryHueMidForegroundBrush",
                    new SolidColorBrush(exemplarHue.Foreground), parentDictionary);
                ReplaceEntry("PrimaryHueDarkBrush", new SolidColorBrush(hue2.Color), parentDictionary);
                ReplaceEntry("PrimaryHueDarkForegroundBrush", new SolidColorBrush(hue2.Foreground),
                    parentDictionary);
                ReplaceEntry("HighlightBrush", new SolidColorBrush(hue2.Color), parentDictionary);
                ReplaceEntry("AccentColorBrush", new SolidColorBrush(list[5].Color), parentDictionary);
                ReplaceEntry("AccentColorBrush2", new SolidColorBrush(list[4].Color), parentDictionary);
                ReplaceEntry("AccentColorBrush3", new SolidColorBrush(list[3].Color), parentDictionary);
                ReplaceEntry("AccentColorBrush4", new SolidColorBrush(list[2].Color), parentDictionary);
                ReplaceEntry("WindowTitleColorBrush", new SolidColorBrush(hue2.Color),
                    parentDictionary);
                ReplaceEntry("AccentSelectedColorBrush", new SolidColorBrush(list[5].Foreground),
                    parentDictionary);
                ReplaceEntry("ProgressBrush", new LinearGradientBrush(hue2.Color, list[3].Color, 90.0),
                    parentDictionary);
                ReplaceEntry("CheckmarkFill", new SolidColorBrush(list[5].Color), parentDictionary);
                ReplaceEntry("RightArrowFill", new SolidColorBrush(list[5].Color), parentDictionary);
                ReplaceEntry("IdealForegroundColorBrush", new SolidColorBrush(list[5].Foreground),
                    parentDictionary);
            }

            string str = "IdealForegroundDisabledBrush";
            SolidColorBrush solidColorBrush = new SolidColorBrush(hue2.Color);
            double num = 0.4;
            solidColorBrush.Opacity = num;
            // ISSUE: variable of the null type
            ResourceDictionary? local = null;
            ReplaceEntry(str, solidColorBrush, local);
        }

        internal void ReplacePrimaryColor(string? name, ResourceDictionary? parentDictionary = null)
        {
            if (name == null)
                return;
            Swatch? swatch = new SwatchesProvider().Swatches.FirstOrDefault(s => string.Compare(s.Name, name, StringComparison.InvariantCultureIgnoreCase) == 0);
            if (swatch == null)
                throw new ArgumentException($@"No such swatch '{(object)name}'", "name");
            this.ReplacePrimaryColor(swatch, parentDictionary);
        }

        private static void ReplaceEntry(object entryName, object newValue, ResourceDictionary? parentDictionary = null)
        {
            if (parentDictionary == null)
                parentDictionary = Application.Current.Resources;
            if (parentDictionary.Contains(entryName))
            {
                if (parentDictionary[entryName] is SolidColorBrush solidColorBrush && !solidColorBrush.IsFrozen)
                {
                    ColorAnimation colorAnimation1 = new ColorAnimation();
                    Color? nullable1 = ((SolidColorBrush)parentDictionary[entryName]).Color;
                    colorAnimation1.From = nullable1;
                    Color? nullable2 = ((SolidColorBrush)newValue).Color;
                    colorAnimation1.To = nullable2;
                    Duration duration = new Duration(TimeSpan.FromMilliseconds(300.0));
                    colorAnimation1.Duration = duration;
                    ColorAnimation colorAnimation2 = colorAnimation1;
                    solidColorBrush.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation2);
                }
                else
                    parentDictionary[entryName] = newValue;
            }
            foreach (ResourceDictionary parentDictionary1 in parentDictionary.MergedDictionaries)
                ReplaceEntry(entryName, newValue, parentDictionary1);
        }

    }
}
