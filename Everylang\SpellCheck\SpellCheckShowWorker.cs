﻿using Everylang.App.Clipboard;
using Everylang.App.View.Controls.SpellCheck;
using Everylang.Common.Utilities;
using System.Linq;

namespace Everylang.App.SpellCheck
{
    class SpellCheckShowWorker
    {
        internal static void ShowSpellAfterKeyPress()
        {
            string? text = ClipboardOperations.GetSelectionText();

            if (text.Trim().Replace("\r\n", "") != "")
            {
                text = AddSpaceAfterPunctuation(text);
                ShowSpellCheckWindow(text, false);
            }
        }

        internal static void ShowSpellCheckWindow(string? text, bool isMain)
        {
            if (IsContainsOnlyLettes(text.Trim()))
            {
                ForegroundWindow.StoreForegroundWindow();
                SmallSpellCheckWindow smallSpellCheckWindow = new SmallSpellCheckWindow();
                smallSpellCheckWindow.IsFromMainWindow = isMain;
                smallSpellCheckWindow.Show(text);

            }
            else
            {
                ForegroundWindow.StoreForegroundWindow();
                SpellCheckWindow spellCheckWindow = new SpellCheckWindow();
                if (text.Length > 1800)
                {
                    spellCheckWindow.ErrorTooLongText = true;
                    spellCheckWindow.Show();
                    spellCheckWindow.IsVisibleProgressRing = false;
                    spellCheckWindow.Show();
                    return;
                }
                spellCheckWindow.IsFromMainWindow = isMain;
                spellCheckWindow.Show(text);
            }

        }

        static bool IsContainsOnlyLettes(string text)
        {
            foreach (var t in text)
            {
                if (!char.IsLetter(t))
                {
                    return false;
                }
            }
            return true;
        }

        static string? AddSpaceAfterPunctuation(string? text)
        {
            var punctuationArray = new char[] { ',', '?', '!', ';', ':', '»' };
            int addition = 1;
            var resultText = text;
            for (int i = 0; i < text.Length; i++)
            {
                var c = text[i];
                if (i != text.Length - 1 && punctuationArray.Contains(c))
                {
                    if (text[i + 1] != ' ')
                    {
                        resultText = resultText.Insert(i + addition, " ");
                        addition++;
                    }
                }
            }
            return resultText;
        }
    }
}
