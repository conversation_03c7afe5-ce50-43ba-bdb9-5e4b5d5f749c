﻿using Everylang.App.View.SettingControls.About;
using Everylang.App.View.SettingControls.AllHotKeys;
using Everylang.App.View.SettingControls.Appearance;
using Everylang.App.View.SettingControls.AutoSwitch;
using Everylang.App.View.SettingControls.CapsText;
using Everylang.App.View.SettingControls.Clipboard;
using Everylang.App.View.SettingControls.Converter;
using Everylang.App.View.SettingControls.Diary;
using Everylang.App.View.SettingControls.General;
using Everylang.App.View.SettingControls.LangFlag;
using Everylang.App.View.SettingControls.Miminote;
using Everylang.App.View.SettingControls.Ocr;
using Everylang.App.View.SettingControls.ProgramsExceptions;
using Everylang.App.View.SettingControls.ProgramsSetLayout;
using Everylang.App.View.SettingControls.ProSettings;
using Everylang.App.View.SettingControls.SmartClick;
using Everylang.App.View.SettingControls.Snippets;
using Everylang.App.View.SettingControls.Spellchecking;
using Everylang.App.View.SettingControls.Switcher;
using Everylang.App.View.SettingControls.Translation;
using Everylang.App.ViewModels;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for SettingsView.xaml
    /// </summary>
    internal partial class SettingsView
    {
        internal SettingsView()
        {
            InitializeComponent();
        }

        private void NavigationViewOnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "GeneralTabItem")
            {
                MuNavigationView.Content = new GeneralControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "AppearanceTabItem")
            {
                MuNavigationView.Content = new AppearanceControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "TranslationTabItem")
            {
                MuNavigationView.Content = new TranslationControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "SpellcheckingTabItem")
            {
                MuNavigationView.Content = new SpellcheckingControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "SwitcherTabItem")
            {
                MuNavigationView.Content = new SwitcherControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "AutoSwitchTabItem")
            {
                MuNavigationView.Content = new AutoSwitchControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "TabItemProgramsSetLayout")
            {
                MuNavigationView.Content = new ProgramsSetLayoutControl();
                _ = VMContainer.Instance.ProgramsSetLayoutViewModel.UpdateStartedPrograms();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "LangFlagTabItem")
            {
                MuNavigationView.Content = new LangFlagControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "ClipboardTabItem")
            {
                MuNavigationView.Content = new ClipboardControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "AutochangeTabItem")
            {
                MuNavigationView.Content = new SnippetsControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "MiminoteTabItem")
            {
                MuNavigationView.Content = new MiminoteControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "OcrTabItem")
            {
                MuNavigationView.Content = new OcrControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "SmartClickWindowTabItem")
            {
                MuNavigationView.Content = new SmartClickWindowSettings();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "DiareTabItem")
            {
                MuNavigationView.Content = new DiaryControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "ConverterTabItem")
            {
                MuNavigationView.Content = new ConverterControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "CapsTabItem")
            {
                MuNavigationView.Content = new ConverterCapsControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "TabItemProgramsExceptions")
            {
                MuNavigationView.Content = new ProgramsExceptionsControl();
                _ = VMContainer.Instance.ProgramsExceptionsViewModel.UpdateStartedPrograms();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "TabItemAllHotKeys")
            {
                MuNavigationView.Content = new AllHotKeysControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "ProTabItem")
            {
                MuNavigationView.Content = new ProSettingsControl();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Name == "AboutTabItem")
            {
                MuNavigationView.Content = new AboutControl();
            }


        }

    }
}
