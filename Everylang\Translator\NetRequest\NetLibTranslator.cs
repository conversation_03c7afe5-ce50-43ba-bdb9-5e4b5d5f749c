﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using RestSharp;
using System;
using System.IO;

namespace Everylang.App.Translator.NetRequest
{
    internal class NetLibTranslator
    {
        private readonly string _uri;
        private readonly string _data;
        private readonly string _referer;

        internal NetLibTranslator(string uri, string data = "", string referer = "https://www.google.com/")
        {
            _referer = referer;
            _data = data;
            _uri = uri;
        }

        internal WebResultTranslator StartGetWebRequestWithCookies()
        {
            var webResult = new WebResultTranslator();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = SettingsManager.UserAgent
                });
                var request = new RestRequest(_uri);
                var response = client.Get(request);
                webResult.Cookies = response.Cookies;
                webResult.ResultText = response.Content;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal WebResultTranslator StartPostWebRequestDeepL()
        {
            var webResult = new WebResultTranslator();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = SettingsManager.UserAgent
                });
                var req = new RestRequest(_uri, Method.Post);
                req.AddHeader("Accept-Charset", "utf-8");
                req.AddHeader("Accept-Encoding", "gzip,deflate");
                req.RequestFormat = DataFormat.Json;
                req.AddJsonBody(_data);
                var res = client.Execute(req);
                webResult.ResultText = res.Content;
                webResult.Cookies = res.Cookies;
                return webResult;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal WebResultTranslator StartPostWebRequestBing()
        {
            var webResult = new WebResultTranslator();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = SettingsManager.UserAgent

                });
                var req = new RestRequest(_uri, Method.Post);
                req.AddParameter("application/x-www-form-urlencoded", _data, ParameterType.RequestBody);
                req.Timeout = TimeSpan.FromSeconds(3);
                var res = client.Execute(req);
                webResult.ResultText = res.Content;
                webResult.Cookies = res.Cookies;
                return webResult;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal WebResultTranslator StartPostWebRequestYandex()
        {
            var webResult = new WebResultTranslator();
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = "ru.yandex.translate/3.20.2024"

                });
                var req = new RestRequest(_uri, Method.Post);
                req.AddHeader("Referer", _referer);
                req.AddParameter("application/x-www-form-urlencoded", _data, ParameterType.RequestBody);
                var res = client.Execute(req);
                webResult.ResultText = res.Content;
                webResult.Cookies = res.Cookies;
                return webResult;
            }
            catch (Exception e)
            {
                webResult.WithError = true;
                webResult.ErrorText = e.Message;
            }
            return webResult;
        }

        internal Stream? StartGetWebRequestGoogleTts()
        {
            try
            {
                var client = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = SettingsManager.UserAgent

                });
                var req = new RestRequest(_uri);
                req.AddHeader("Referer", _referer);
                var res = client.Get(req);
                if (res.RawBytes != null)
                {
                    return new MemoryStream(res.RawBytes);
                }
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
