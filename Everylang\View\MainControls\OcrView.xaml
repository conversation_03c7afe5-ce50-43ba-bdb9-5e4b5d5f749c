﻿<UserControl
    PreviewKeyUp="OcrView_OnPreviewKeyUp"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Everylang.App.View.MainControls.OcrView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shapes="clr-namespace:Telerik.Windows.Media.Imaging.Shapes;assembly=Telerik.Windows.Controls.ImageEditor"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:tools="clr-namespace:Telerik.Windows.Media.Imaging.Tools;assembly=Telerik.Windows.Controls.ImageEditor"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:richTextBoxEx1="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx"
    xmlns:ocr1="clr-namespace:Everylang.App.View.Controls.Ocr"
    xmlns:formatters="clr-namespace:Everylang.App.View.Controls.Common.RichTextBoxEx.Formatters"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"  IsEnabled="{Binding Path=OcrViewModel.jgebhdhs}">
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <telerik:RadButton
            Click="ClearAll"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            Margin="0,2,2,0"
            MinHeight="0"
            Cursor="Hand"
            Padding="4"
            ToolTip="{telerik:LocalizableResource Key=ClearAll}"
            VerticalAlignment="Top"
            Visibility="Hidden"
            x:Name="RadButtonClearAll">
            <wpf:MaterialIcon Kind="Remove" Height="20" Width="20"/>
        </telerik:RadButton>
        <telerik:RadButton
            Click="SelectLangsClick"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            Margin="0,2,38,0"
            MinHeight="0"
            Cursor="Hand"
            Padding="4"
            ToolTip="{telerik:LocalizableResource Key=OcrSelectLanguages}"
            VerticalAlignment="Top"
            Visibility="Hidden"
            x:Name="RadButtonSettings">
            <wpf:MaterialIcon Kind="SpokenLanguage"  Height="20" Width="20"/>
        </telerik:RadButton>
        <telerik:RadButton
            Click="OcrClick"
            Focusable="False"
            Grid.ZIndex="1"
            Cursor="Hand"
            HorizontalAlignment="Right"
            Margin="0,2,76,0"
            MinHeight="0"
            Padding="4"
            ToolTip="{telerik:LocalizableResource Key=OcrRecognize}"
            VerticalAlignment="Top"
            Visibility="Hidden"
            x:Name="RadButtonOcr">
            <wpf:MaterialIcon Kind="Ocr"  Height="20" Width="20"/>
        </telerik:RadButton>
        <Grid
            IsVisibleChanged="GridStart_IsVisibleChanged"
            Visibility="Visible"
            x:Name="GridStart">
            <TextBlock
                FontSize="20"
                HorizontalAlignment="Center"
                Margin="0,0,0,100"
                Text="{telerik:LocalizableResource Key=OcrStartText}"
                VerticalAlignment="Center" />
            <WrapPanel
                HorizontalAlignment="Center"
                Margin="0,0,0,0"
                Orientation="Horizontal"
                VerticalAlignment="Center">
                <telerik:RadButton
                    Click="CaptureClick"
                    Content="{telerik:LocalizableResource Key=OcrCaptureArea}"
                    Margin="5,0,0,0" />
                <telerik:RadButton
                    Click="FromClipboardClick"
                    Content="{telerik:LocalizableResource Key=OcrFromClipboard}"
                    Margin="5,0,0,0"
                    x:Name="ButtonFromClipboard" />
                <telerik:RadButton
                    Click="OpenFileClick"
                    Content="{telerik:LocalizableResource Key=OcrOpenImageOrPDFFile}"
                    Margin="10,0,0,0" />
            </WrapPanel>
        </Grid>
        <telerik:RadTabControl
            BorderThickness="0"
            Visibility="Hidden"
            x:Name="OcrTab">
            <telerik:RadTabItem Header="{telerik:LocalizableResource Key=OcrImage}">
                <telerik:RadTabItem.Content>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <StackPanel  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}"
                                     Grid.ColumnSpan="2"
                                     Grid.Row="0"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Stretch"
                                     Margin="0,0,0,2"
                                     Orientation="Horizontal">
                            <telerik:RadButton
                                Command="{Binding Path=Commands.Undo, ElementName=imageEditor}"
                                Focusable="False"
                                Margin="41,0,0,0"
                                MinHeight="0"
                                Cursor="Hand"
                                Height="35"
                                Padding="4"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Undo}">
                                <wpf:MaterialIcon Kind="Undo" />
                            </telerik:RadButton>
                            <telerik:RadButton
                                Command="{Binding Path=Commands.Redo, ElementName=imageEditor}"
                                Focusable="False"
                                Margin="0,0,0,0"
                                MinHeight="0"
                                Cursor="Hand"
                                Height="35"
                                Padding="4"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Redo}">
                                <wpf:MaterialIcon Kind="Redo" />
                            </telerik:RadButton>
                            <telerik:RadButton
                                Click="CopyClick"
                                Focusable="False"
                                Margin="0,0,0,0"
                                MinHeight="0"
                                Cursor="Hand"
                                Height="35"
                                Padding="4"
                                ToolTip="{telerik:LocalizableResource Key=CopyButton}">
                                <wpf:MaterialIcon Kind="ContentCopy" />
                            </telerik:RadButton>
                            <telerik:RadButton
                                Click="SaveClick"
                                Focusable="False"
                                Margin="0,0,0,0"
                                MinHeight="0"
                                Cursor="Hand"
                                Height="35"
                                Padding="4"
                                ToolTip="{telerik:LocalizableResource Key=Save}">
                                <wpf:MaterialIcon Kind="ContentSave" />
                            </telerik:RadButton>

                        </StackPanel>
                        <StackPanel
                            Grid.Column="0"
                            Grid.Row="1"
                            Orientation="Vertical">
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Crop"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Crop}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:CropTool />
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Shape"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Shape}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:ShapeTool>
                                        <tools:ShapeTool.Shapes>
                                            <shapes:RectangleShape  />
                                        </tools:ShapeTool.Shapes>
                                    </tools:ShapeTool>
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                ImageKey="DrawText"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_DrawText}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:DrawTextTool />
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.Rotate90Clockwise, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Rotate90CW"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Rotate90}" />
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.Rotate180, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Rotate180CW"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Rotate180}" />
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.FlipHorizontal, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="FlipHorizontal"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_FlipHorizontal}" />
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.FlipVertical, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="FlipVertical"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_FlipVertical}" />

                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="HueShift"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Effect_HueShift}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:HueShiftTool />
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Saturation"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Effect_Saturation}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:SaturationTool />
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.ExecuteTool, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Contrast"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Effect_ContrastAdjust}">
                                <telerik:RadButton.CommandParameter>
                                    <tools:ContrastTool />
                                </telerik:RadButton.CommandParameter>
                            </telerik:RadImageEditorButton>
                            <telerik:RadImageEditorButton
                                Command="{Binding Path=Commands.InvertColors, ElementName=imageEditor}"
                                Focusable="False"
                                ImageKey="Invert"
                                Margin="0,0,0,0"
                                ToolTip="{telerik:LocalizableResource Key=ImageEditor_Effect_InvertColors}" />

                        </StackPanel>
                        <telerik:RadImageEditor
                            Grid.Column="1"
                            Grid.Row="1"
                            Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            x:Name="imageEditor" />
                        <ocr1:CustomSettingsContainer
                            Grid.Column="2"
                            Grid.Row="0"
                            Grid.RowSpan="2"
                            x:Name="CustomSettingsContainer" />
                    </Grid>

                </telerik:RadTabItem.Content>
            </telerik:RadTabItem>
            <telerik:RadTabItem Header="{telerik:LocalizableResource Key=OcrRecognizedText}">
                <telerik:RadTabItem.Content>
                    <telerik:RadBusyIndicator
                        BusyContent="{telerik:LocalizableResource Key=OcrTab}"
                        IsBusy="False"
                        IsIndeterminate="True"
                        x:Name="BusyIndicatorOcr">
                        <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <StackPanel
                                Grid.ColumnSpan="2"
                                Grid.Row="0"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,2"
                                Orientation="Horizontal">
                                <telerik:RadButton
                                    Click="ContentCopyClick"
                                    Focusable="False"
                                    Cursor="Hand"
                                    Margin="5,0,0,0"
                                    MinHeight="0"
                                    Height="35"
                                    Padding="4"
                                    ToolTip="{telerik:LocalizableResource Key=OcrCopyText}">
                                    <wpf:MaterialIcon Kind="ContentCopy" />
                                </telerik:RadButton>
                                <telerik:RadButton
                                    Click="TranslateClick"
                                    Focusable="False"
                                    Cursor="Hand"
                                    MinHeight="0"
                                    Height="35"
                                    Padding="4"
                                    ToolTip="{telerik:LocalizableResource Key=TranslateButton}">
                                    <wpf:MaterialIcon Kind="Translate" />
                                </telerik:RadButton>
                            </StackPanel>
                            <richTextBoxEx1:RichTextBoxEx
                                Background="{telerik:Windows11Resource ResourceKey=PrimaryBackgroundBrush}"
                                BorderThickness="0"
                                Padding="5"
                                Block.LineHeight="2"
                                Foreground="{telerik:Windows11Resource ResourceKey=PrimaryForegroundBrush}"
                                Grid.Row="1"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                x:Name="edText">
                                <richTextBoxEx1:RichTextBoxEx.TextFormatter>
                                    <formatters:PlainTextFormatter />
                                </richTextBoxEx1:RichTextBoxEx.TextFormatter>
                            </richTextBoxEx1:RichTextBoxEx>
                        </Grid>
                    </telerik:RadBusyIndicator>
                </telerik:RadTabItem.Content>
            </telerik:RadTabItem>
        </telerik:RadTabControl>
    </Grid>
</UserControl>

