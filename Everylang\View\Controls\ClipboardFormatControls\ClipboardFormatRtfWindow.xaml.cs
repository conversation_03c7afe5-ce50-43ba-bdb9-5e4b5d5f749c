﻿using Everylang.App.Clipboard;
using Everylang.App.Utilities;
using System;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;

namespace Everylang.App.View.Controls.ClipboardFormatControls
{
    /// <summary>
    /// Interaction logic for ClipboardFormatRtfWindow.xaml
    /// </summary>
    internal partial class ClipboardFormatRtfWindow
    {
        internal ClipboardFormatRtfWindow(string? text)
        {
            InitializeComponent();
            richTextBox.Text = text;
        }

        private void RichTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var text = GetText();
            if (string.IsNullOrEmpty(text)) return;
            this.UpdateLayout();
            var size = MeasureString(text);
            if (size.Height > 180)
            {
                Height = size.Height + 60;
            }
            else
            {
                Height = 200;
            }
            if (size.Width > 280)
            {
                Width = size.Width + 20;
            }
            else
            {
                Width = 300;
            }
            this.UpdateLayout();
        }

        private string GetText()
        {
            TextRange tr = new TextRange(richTextBox.Document.ContentStart, richTextBox.Document.ContentEnd);
            using (var ms = new MemoryStream())
            {
                tr.Save(ms, DataFormats.Text);
                return Encoding.UTF8.GetString(ms.ToArray());
            }
        }

        private FormattedText MeasureString(string textBlock)
        {
            var formattedText = new FormattedText(
                textBlock,
                CultureInfo.CurrentUICulture,
                FlowDirection.LeftToRight,
                new Typeface(richTextBox.FontFamily, richTextBox.FontStyle, richTextBox.FontWeight, richTextBox.FontStretch), richTextBox.FontSize, Brushes.Black, VisualTreeHelper.GetDpi(this).PixelsPerDip);

            return formattedText;
        }

        private void me_Deactivated(object sender, EventArgs e)
        {
            if (IsVisible)
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
            Application curApp = Application.Current;
            if (curApp.MainWindow != null) curApp.MainWindow.Activate();
        }

        private void CommandCopyresTextBox_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(richTextBox.Selection.Text);
        }

        private void richTextBox_Loaded(object sender, RoutedEventArgs e)
        {
            if (Application.Current.MainWindow != null)
            {
                var pos = WindowLocation.GetAbsolutePosition(Application.Current.MainWindow);
                this.Left = (Application.Current.MainWindow.ActualWidth - this.ActualWidth) / 2 + pos.X;
                this.Top = (Application.Current.MainWindow.ActualHeight - this.ActualHeight) / 2 + pos.Y;
            }

            if (this.Top < 1)
            {
                this.Top = 5;
            }
        }
    }


}
