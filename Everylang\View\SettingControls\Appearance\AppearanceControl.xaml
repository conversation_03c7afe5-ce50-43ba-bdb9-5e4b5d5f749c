﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.Appearance.AppearanceControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    xmlns:system="clr-namespace:System;assembly=mscorlib"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <Style TargetType="telerik:RadListBoxItem" x:Key="DraggableListBoxItem"  BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}"
               >
            <Setter Property="telerik:DragDropManager.AllowCapturedDrag" Value="True" />
        </Style>
        <ObjectDataProvider x:Key="SystemFontSizes">
            <ObjectDataProvider.ObjectInstance>
                <x:Array Type="system:Double">
                    <system:Double>8</system:Double>
                    <system:Double>9</system:Double>
                    <system:Double>10</system:Double>
                    <system:Double>11</system:Double>
                    <system:Double>12</system:Double>
                    <system:Double>13</system:Double>
                    <system:Double>14</system:Double>
                    <system:Double>15</system:Double>
                    <system:Double>16</system:Double>
                    <system:Double>18</system:Double>
                    <system:Double>20</system:Double>
                    <system:Double>22</system:Double>
                    <system:Double>24</system:Double>
                    <system:Double>26</system:Double>
                    <system:Double>28</system:Double>
                    <system:Double>36</system:Double>
                    <system:Double>48</system:Double>
                    <system:Double>72</system:Double>
                </x:Array>
            </ObjectDataProvider.ObjectInstance>
        </ObjectDataProvider>
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <StackPanel Margin="20,10,0,0">
            <TextBlock
                FontSize="15"
                FontWeight="Bold"
                Text="{telerik:LocalizableResource Key=AppearanceTab}" />

            <StackPanel Margin="0,5,0,5" Orientation="Horizontal">
                <TextBlock
                    FontSize="15"
                    Text="{telerik:LocalizableResource Key=GeneralSettingsLanguageProgram}"
                    VerticalAlignment="Center" />
                <telerik:RadComboBox
                    BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                    Focusable="False"
                    DisplayMemberPath="LangName"
                    HorizontalAlignment="Left"
                    ItemsSource="{Binding Path=AppearanceViewModel.LanguagesUi}"
                    Margin="10,0,0,0"
                    SelectedItem="{Binding Path=AppearanceViewModel.LanguageUi}"
                    Width="188" />
            </StackPanel>
            <StackPanel Margin="0,0,0,5" Orientation="Horizontal">
                <TextBlock
                    FontSize="15"
                    Text="{telerik:LocalizableResource Key=GeneralSettingsTheme}"
                    VerticalAlignment="Center" />
                <telerik:RadComboBox
                    BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                    Focusable="False"
                    HorizontalAlignment="Left"
                    ItemsSource="{Binding Path=MainWindowViewModel.AppThemes}"
                    Margin="5,0,0,0"
                    SelectedItem="{Binding Path=MainWindowViewModel.SelectedAppTheme}"
                    Width="150" />
            </StackPanel>
            <TextBlock
                FontSize="15"
                Text="{telerik:LocalizableResource Key=GeneralSettingsFont}"
                VerticalAlignment="Center" />
            <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                <telerik:RadComboBox
                    AutoCompleteSelectionMode="PartialMatch"
                    BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"
                    CanKeyboardNavigationSelectItems="True"
                    HorizontalAlignment="Left"
                    IsEditable="True"
                    IsFilteringEnabled="True"
                    IsReadOnly="True"
                    ItemsSource="{Binding Path=MainWindowViewModel.Fonts}"
                    Margin="0,0,0,0"
                    OpenDropDownOnFocus="True"
                    SelectedItem="{Binding Path=MainWindowViewModel.AppFont}"
                    TextSearchMode="Contains"
                    UpdateSelectionOnLostFocus="False"
                    UpdateTextOnLostFocus="True"
                    Width="200" />
                <telerik:RadComboBox  Margin="5,0,0,0"
                                      Width="80"
                                      Text="{Binding MainWindowViewModel.AppFontSize,Mode=TwoWay}"
                                      VerticalAlignment="Bottom" 
                                      IsEditable="True"
                                      IsTextSearchEnabled="True"
                                      IsManipulationEnabled="True"
                                      Focusable="False"
                                      FontSize="14"
                                      >
                    <telerik:RadComboBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <VirtualizingStackPanel />
                        </ItemsPanelTemplate>
                    </telerik:RadComboBox.ItemsPanel>
                    <telerik:RadComboBox.ItemsSource>
                        <Binding Source="{StaticResource SystemFontSizes}" />
                    </telerik:RadComboBox.ItemsSource>
                </telerik:RadComboBox>
            </StackPanel>
            <TextBlock
                FontSize="15"
                Margin="0,10,0,0"
                Text="{telerik:LocalizableResource Key=OrderFunctionsTab}"
                VerticalAlignment="Center" />
            <telerik:RadListBox
                AllowDrop="True"
                Drop="LvFunctionOrder_OnDrop"
                HorizontalAlignment="Left"
                ItemContainerStyle="{StaticResource DraggableListBoxItem}"
                Margin="0,10,0,0"
                SelectionMode="Single"
                VerticalAlignment="Stretch"
                Width="300"
                x:Name="LvFunctionOrder">
                <telerik:RadListBox.DragDropBehavior>
                    <telerik:ListBoxDragDropBehavior AllowReorder="True" telerik:TouchManager.DragStartTrigger="TapHoldAndMove" />
                </telerik:RadListBox.DragDropBehavior>
                <telerik:RadListBox.DragVisualProvider>
                    <telerik:ScreenshotDragVisualProvider />
                </telerik:RadListBox.DragVisualProvider>
                <telerik:RadListBox.Items>
                    <telerik:RadListBoxItem Tag="0">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="Translate"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=TranslationTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>
                    <telerik:RadListBoxItem Tag="1">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="ClipboardOutline"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=ClipboardTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>
                    <telerik:RadListBoxItem Tag="2">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="NotebookOutline"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=DiareTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>
                    <telerik:RadListBoxItem Tag="3">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="NoteMultiple"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=NoteTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>
                    <telerik:RadListBoxItem Tag="4">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="StickerTextOutline"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=AutochangeTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>
                    <telerik:RadListBoxItem Tag="5">
                        <StackPanel Orientation="Horizontal">
                            <wpf:MaterialIcon
                                Foreground="{telerik:Windows11Resource ResourceKey=AccentBrush}"
                                HorizontalAlignment="Center"
                                Kind="ImageSearchOutline"
                                VerticalAlignment="Center" />
                            <TextBlock Margin="10,0,0,0" Text="{telerik:LocalizableResource Key=OcrTab}" />
                        </StackPanel>
                    </telerik:RadListBoxItem>

                </telerik:RadListBox.Items>
            </telerik:RadListBox>
        </StackPanel>
    </Grid>
</UserControl>
