﻿using Everylang.App.Shortcut;
using Everylang.Note;
using Everylang.Note.Callbacks;
using Everylang.Note.NoteDataStore;
using Everylang.Note.SettingsApp;
using Microsoft.Xaml.Behaviors.Core;
using System;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels
{
    public class NotesListControlViewModel : ViewModelBase
    {
        public ICommand CheckCommand { get; }
        public ICommand UnCheckAllCommand { get; }
        public ICommand ToArchivCommand { get; }
        public ICommand ToDeleteCommand { get; }
        public ICommand ToArchivSelectedCommand { get; }
        public ICommand ReturnFromArchivCommand { get; }
        public ICommand ToDeleteSelectedCommand { get; }
        public ICommand AddNewCommand { get; }

        public NotesListControlViewModel()
        {
            CheckCommand = new ActionCommand(Check);
            UnCheckAllCommand = new ActionCommand(UnCheckAll);
            ToArchivCommand = new ActionCommand(o => ToArchiv(o as NoteDataModel));
            ReturnFromArchivCommand = new ActionCommand(o => ReturnFromArchiv(o as NoteDataModel));
            ToDeleteCommand = new ActionCommand(o => ToDelete(o as NoteDataModel));
            ToArchivSelectedCommand = new ActionCommand(ToArchivSelected);
            ToDeleteSelectedCommand = new ActionCommand(ToDeleteSelected);
            AddNewCommand = new ActionCommand(AddNew);
            CallBack.MiminoteCallBackAddNewNote += AddNewNote;
            _isShowingAll = true;
        }

        private void AddNewNote()
        {
            ShowAll();
        }

        private void HideAll()
        {
            SettingsMiminoteManager.AppSettings.IsOnNotes = false;
            if (_miminoteMainWindow != null)
            {
                _miminoteMainWindow.Close();
            }
            OnPropertyChanged(nameof(IsShowAll));
            base.OnPropertyChanged(nameof(IsOnNotes));
        }

        private void ShowAll()
        {
            if (NoteDataManager.NotesList.Any(x => x.IsVisible))
            {
                SettingsMiminoteManager.AppSettings.IsOnNotes = true;
                ShowMainNoteWindow(false);
                OnPropertyChanged(nameof(IsShowAll));
                base.OnPropertyChanged(nameof(IsOnNotes));
            }
        }

        private void AddNew()
        {
            ShowMainNoteWindow(true);
            SettingsMiminoteManager.AppSettings.IsOnNotes = true;
            CallBack.OnMiminoteCallBackAddNewNote();
            OnPropertyChanged(nameof(IsShowAll));
            base.OnPropertyChanged(nameof(IsOnNotes));
        }

        private void Check()
        {
            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void UnCheckAll()
        {
            foreach (var noteDataModel in NoteDataManager.NotesList)
            {
                if (noteDataModel != null) noteDataModel.IsSelected = false;
            }
            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void ToArchivSelected()
        {
            foreach (var noteDataModel in NoteDataManager.NotesList.Where(x => x != null && x.IsSelected))
            {
                if (noteDataModel != null)
                {
                    noteDataModel.IsArchived = true;
                    noteDataModel.IsSelected = false;
                    NoteDataManager.UpdateData(noteDataModel);
                }
            }
            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void ReturnFromArchiv(NoteDataModel? dataModel)
        {
            if (dataModel != null)
            {
                dataModel.IsArchived = false;
                NoteDataManager.UpdateData(dataModel);
                OnPropertyChanged(nameof(ItemsView));
            }
        }

        private void ToDeleteSelected()
        {
            foreach (var noteDataModel in NoteDataManager.NotesList.Where(x => x != null && x.IsSelected))
            {
                if (noteDataModel != null)
                {
                    noteDataModel.IsDeleted = true;
                    noteDataModel.IsSelected = false;
                    NoteDataManager.UpdateData(noteDataModel);
                }
            }
            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private void ToArchiv(NoteDataModel? dataModel)
        {
            if (dataModel != null)
            {
                dataModel.IsArchived = true;
                dataModel.IsSelected = false;
                NoteDataManager.UpdateData(dataModel);
            }

            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
            OnPropertyChanged(nameof(ItemsView));
        }

        private void ToDelete(NoteDataModel? dataModel)
        {
            if (dataModel != null)
            {
                dataModel.IsDeleted = true;
                NoteDataManager.DelData(dataModel);
                NoteDataManager.NotesList.Remove(dataModel);
                CallBack.OnMiminoteCallBackCloseNote(dataModel);
            }

            OnPropertyChanged(nameof(IsHaveSelected));
            OnPropertyChanged(nameof(SelectedCount));
        }

        private ListCollectionView? _itemsView;

        public ListCollectionView? ItemsView
        {
            get
            {
                foreach (var noteDataModel in NoteDataManager.NotesList)
                {
                    noteDataModel.IsSelected = false;
                }
                OnPropertyChanged(nameof(IsHaveSelected));
                if (_itemsView == null)
                {
                    _itemsView = new ListCollectionView(NoteDataManager.NotesList);
                    _itemsView.IsLiveFiltering = true;
                }
                if (IsShowingArchived)
                {
                    _itemsView.Filter = o => ((NoteDataModel)o).IsArchived;
                    _itemsView.LiveFilteringProperties.Add(nameof(NoteDataModel.IsArchived));
                }
                if (IsShowingAll)
                {
                    _itemsView.Filter = o => ((NoteDataModel)o).IsVisible;
                    _itemsView.LiveFilteringProperties.Add(nameof(NoteDataModel.IsVisible));
                }
                if (!string.IsNullOrEmpty(_searchText))
                {
                    _itemsView.Filter = o => ((NoteDataModel)o).TextView.Contains(_searchText);
                    //_itemsView.LiveFilteringProperties.Add(nameof(NoteDataModel.IsVisible));
                }

                return _itemsView;
            }
        }

        private string _searchText = "";

        internal bool IsShowAll => SettingsMiminoteManager.AppSettings.IsOnNotes;

        public string SearchText
        {
            get
            {
                return _searchText;
            }
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(ItemsView));
            }
        }

        private bool _isShowingArchived = false;

        public bool IsShowingArchived
        {
            get
            {
                return _isShowingArchived;
            }
            set
            {
                _isShowingArchived = value;
                _isShowingAll = !_isShowingArchived;
                OnPropertyChanged(nameof(ItemsView));
                OnPropertyChanged(nameof(IsShowingAll));
            }
        }

        private bool _isShowingAll = false;

        public bool IsShowingAll
        {
            get
            {
                return _isShowingAll;
            }
            set
            {
                _isShowingAll = value;
                _isShowingArchived = !_isShowingAll;
                OnPropertyChanged(nameof(ItemsView));
                OnPropertyChanged(nameof(IsShowingArchived));
            }
        }

        public bool IsHaveSelected
        {
            get => NoteDataManager.NotesList.Any(x => x != null && x.IsSelected && x.IsVisible);
            set { }
        }

        public string SelectedCount
        {
            get => LocalizationManager.GetString("NoteCheckMarket") + " " + NoteDataManager.NotesList.Count(x => x != null && x.IsSelected);
            set { }
        }

        bool _miminoteMainWindowClosed;

        internal void ShowMainNoteWindow(bool isNotClose)
        {
            if (_miminoteMainWindow == null)
            {
                _miminoteMainWindow = new MiminoteMainWindow(isNotClose);
                _miminoteMainWindow.Closed += (_, _) =>
                {
                    _miminoteMainWindowClosed = true;
                    _miminoteMainWindow = null;
                    //SettingsMiminoteManager.AppSettings.IsOnNotes = false;
                    base.OnPropertyChanged(nameof(IsOnNotes));
                };
            }
            if (_miminoteMainWindow != null)
            {
                _miminoteMainWindow.Show();
                _miminoteMainWindow.ShowAll();
            }
        }

        internal void CloseMainNoteWindow()
        {
            if (_miminoteMainWindow != null)
            {
                _miminoteMainWindow.Close();
            }
        }

        private MiminoteMainWindow? _miminoteMainWindow;


        #region Settings

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsOnNotes));
            }
        }

        public bool IsOnNotes
        {
            get
            {
                var isOnNotes = jgebhdhs && !_miminoteMainWindowClosed && SettingsMiminoteManager.AppSettings.IsOnNotes && NoteDataManager.NotesList.Any(x => x.IsVisible);
                _miminoteMainWindowClosed = false;
                if (isOnNotes)
                {
                    ShowMainNoteWindow(false);
                }
                return isOnNotes;
            }
            set
            {
                if (value && _isPro)
                {
                    ShowAll();
                }
                else
                {
                    HideAll();
                }
                SettingsMiminoteManager.AppSettings.IsOnNotes = value;
                base.OnPropertyChanged(nameof(IsOnNotes));
            }
        }

        internal bool AddNewShortcutIsEnabled
        {
            get { return SettingsMiminoteManager.AppSettings.AddNewShortcutIsEnabled; }
            set
            {
                SettingsMiminoteManager.AppSettings.AddNewShortcutIsEnabled = value;
                OnPropertyChanged();
            }
        }

        public string AddNewShortcut
        {
            get { return ShortcutManager.GetCharFromKey(SettingsMiminoteManager.AppSettings.AddNewShortcut); }
            set
            {
                SettingsMiminoteManager.AppSettings.AddNewShortcut = value;
                OnPropertyChanged();
            }
        }

        public string SizeFontDefaultText
        {
            get => SettingsMiminoteManager.AppSettings.SizeFontDefault.ToString();
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    if (Double.TryParse(value, out var size))
                    {
                        SettingsMiminoteManager.AppSettings.SizeFontDefault = size;
                        OnPropertyChanged();
                    }
                }
            }
        }

        public FontFamily FontFamilyDefault
        {
            get => new FontFamily(SettingsMiminoteManager.AppSettings.FontDefault);
            set
            {
                SettingsMiminoteManager.AppSettings.FontDefault = value.ToString();
                OnPropertyChanged();
            }
        }

        public int TransparencyForNotes
        {
            get
            {
                return SettingsMiminoteManager.AppSettings.TransparencyForNotes;
            }
            set
            {
                SettingsMiminoteManager.AppSettings.TransparencyForNotes = value;
                CallBack.OnMiminoteCallBackTransparencyForNotesChange();
                OnPropertyChanged();
            }
        }

        #endregion

    }
}
