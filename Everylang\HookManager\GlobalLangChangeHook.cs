﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using System;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Forms;
using System.Windows.Threading;
using Vanara.PInvoke;

namespace Everylang.App.HookManager
{
    static class GlobalLangChangeHook
    {
        private static readonly System.Timers.Timer _timer;
        private static bool _timerStarted;
        private static IntPtr _lastKeyboardLayout;
        private static HWND _lastWindowHwnd;


        internal static bool IsPassword;
        internal static bool IsEditControl;
        internal static Rectangle ControlRect;
        internal static bool IsUiStarted;
        internal static bool IsFullScreen { get; set; }

        internal static event Action? ForegroundWindowChanged;
        private static Thread? _thread;
        private static CancellationTokenSource _cancellationToken;

        static GlobalLangChangeHook()
        {

            _timer = new System.Timers.Timer(500);
            _timer.Elapsed += TimerOnElapsed;
        }

        internal static void SetNewKeyboardLayout(IntPtr hwnd)
        {
            _lastKeyboardLayout = hwnd;
        }

        internal static void Start()
        {
            if (!_timerStarted)
            {
                _timer.Start();
                _timerStarted = true;
                if (!IsUiStarted)
                {
                    _cancellationToken = new CancellationTokenSource();
                    _thread = new Thread(RunFocusChanged);
                    _thread.SetApartmentState(ApartmentState.STA);
                    _thread.Start();
                    IsUiStarted = true;
                }
            }
        }

        private static void RunFocusChanged()
        {
            var globalFocusChanged = new GlobalFocusChanged();
            globalFocusChanged.FocusChangedEvent += OnFocusChanged;
            globalFocusChanged.Start(_cancellationToken.Token);
        }

        private static void OnFocusChanged(Rectangle controlRect, bool isEditControl, bool isPassword)
        {
            System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)delegate ()
            {
                ControlRect = controlRect;
                IsEditControl = isEditControl;
                IsPassword = isPassword;
            });
        }

        internal static void Stop()
        {
            if (_timerStarted)
            {
                _timer.Stop();
                _timerStarted = false;
                if (IsUiStarted)
                {
                    _cancellationToken.Cancel();
                    _thread = null;
                    IsUiStarted = false;
                }
            }
        }

        private static bool _inWork;

        private static void TimerOnElapsed(object? sender, ElapsedEventArgs elapsedEventArgs)
        {
            if (_inWork)
            {
                return;
            }
            _inWork = true;

            var keyboardLayout = KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl();
            if (keyboardLayout != _lastKeyboardLayout)
            {
                System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)delegate ()
                    {
                        GlobalEventsApp.OnEventKeyboardLayoutChanged((IntPtr)keyboardLayout);
                    });

                _lastKeyboardLayout = (IntPtr)keyboardLayout;
            }
            GetLastActiveProcessName();
            _inWork = false;
        }

        private static async void GetLastActiveProcessName()
        {
            await Task.Delay(100);
            var hwnd = User32.GetForegroundWindow();
            IsFullScreen = IsFull(hwnd);
            if (IsFullScreen && SettingsManager.Settings.IsStopWorkingFullScreen)
            {
                await System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)
                    delegate { SettingsManager.IsStopWorking = true; });

            }
            else if (!SettingsManager.IsStopWorkingAll)
            {
                await System.Windows.Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)
                    delegate { SettingsManager.IsStopWorking = false; });
            }
            if (_lastWindowHwnd != hwnd)
            {
                ForegroundWindowChanged?.Invoke();
            }
            _lastWindowHwnd = hwnd;
        }

        //Get the handles for the desktop and shell now.
        internal static bool IsFull(HWND hWnd)
        {


            bool runningFullScreen = false;
            if (!hWnd.IsNull)
            {
                var desktopHandle = User32.GetDesktopWindow();
                var shellHandle = User32.GetShellWindow();
                //Check we haven't picked up the desktop or the shell
                if (!(hWnd.Equals(desktopHandle) || hWnd.Equals(shellHandle)))
                {
                    User32.GetWindowRect(hWnd, out var appBounds);
                    //determine if window is fullscreen
                    var screenBounds = Screen.FromHandle((IntPtr)hWnd).Bounds;
                    if ((appBounds.Bottom - appBounds.Top) == screenBounds.Height && (appBounds.Right - appBounds.Left) == screenBounds.Width)
                    {
                        runningFullScreen = true;
                    }
                }
            }

            return (runningFullScreen);
        }




    }
}
