﻿<UserControl x:Class="Everylang.App.View.SettingControls.AllHotKeys.AllHotKeysControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <Grid>
        <telerik:RadTransitionControl Grid.ZIndex="1" Grid.Row="0" Transition="Fade" Duration="0:0:0.5" Grid.Column="0" x:Name="PageTransitionControl" Margin="0"/>
        <ScrollViewer VerticalScrollBarVisibility="Visible" HorizontalScrollBarVisibility="Disabled"  Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
            <StackPanel Margin="20,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Top">
                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=OpenMainWindowShortcut}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" >
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=GeneralSettingsViewModel.Shortcut}" ToolTip="{Binding Path=GeneralSettingsViewModel.Shortcut}" />
                        <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="NewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>
                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=StopWorkingShortcut}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" >
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=GeneralSettingsViewModel.StopWorkingShortcut}" ToolTip="{Binding Path=GeneralSettingsViewModel.StopWorkingShortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="StopWorkingShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>
                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=TransSettingsKeyboardShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding TranslationSettingsViewModel.TranslationIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=TranslationSettingsViewModel.Shortcut}" ToolTip="{Binding Path=TranslationSettingsViewModel.Shortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="TranslationNewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>
                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=SpellcheckingKeyboardShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" >
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SpellcheckingSettingsViewModel.Shortcut}" ToolTip="{Binding Path=SpellcheckingSettingsViewModel.Shortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SpellcheckingNewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}" FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsKeyboardShortcutsSwitch}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut}" ToolTip="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="KeyboardShortcutsSwitchClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}" FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsIsOnInsert}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut}" ToolTip="{Binding Path=SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchTextLangForAllLineShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}"  FontSize="14" Text="{telerik:LocalizableResource Key=SwitcherSettingsKeyboardShortcutsSwitchSelected}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding SwitcherSettingsViewModel.SwitcherIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SwitcherSettingsViewModel.ShortcutSelected}" ToolTip="{Binding Path=SwitcherSettingsViewModel.ShortcutSelected}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchTextShortcutSelectedClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ClipboardKeyboardViewShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding ClipboardViewModel.IsEnabled}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ClipboardSettingsViewModel.ShortcutView}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ShortcutView}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="ClipboardClipboardShortcutViewClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0" IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ClipboardKeyboardShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcutIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="ClipboardPasteWithoutFormattingShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0" IsEnabled="{Binding Path=ClipboardViewModel.IsEnabled}">
                    <TextBlock TextWrapping="Wrap"  FontSize="14" Text="{telerik:LocalizableResource Key=ClipboardKeyboardRoundShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding ClipboardSettingsViewModel.ClipboardPasteRoundIsOn}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ClipboardSettingsViewModel.ShortcutRound}" ToolTip="{Binding Path=ClipboardSettingsViewModel.ShortcutRound}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="ClipboardShortcutRoundClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  IsEnabled="{Binding SnippetsViewModel.jgebhdhs}" FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeKeyboardShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding SnippetsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SnippetsViewModel.Shortcut}" ToolTip="{Binding Path=SnippetsViewModel.Shortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="AutochangeShortcutShowListClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <TextBlock  IsEnabled="{Binding SnippetsViewModel.jgebhdhs}" Margin="0,4,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=AutochangeKeyboardShortcutsAddNew}" />
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0" IsEnabled="{Binding SnippetsViewModel.jgebhdhs}">
                    <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=SnippetsViewModel.ShortcutAddNew}" ToolTip="{Binding Path=SnippetsViewModel.ShortcutAddNew}"/>
                    <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="AutochangeShortcutAddNewClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=OcrKeyboardShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0"  IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=OcrViewModel.Shortcut}" ToolTip="{Binding Path=OcrViewModel.Shortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="OcrNewShortCutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.UniversalWindowIsOn}">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=SmartClickShortcutSettingsHeader}" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressHotKeys}"/>
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=UniversalWindowSettingsViewModel.ShowOnPressHotKeys}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=UniversalWindowSettingsViewModel.Shortcut}" ToolTip="{Binding Path=UniversalWindowSettingsViewModel.Shortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SmartClickShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  IsEnabled="{Binding DiaryViewModel.IsEnabled}" FontSize="14" Text="{telerik:LocalizableResource Key=DiaryShortcuts}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=DiaryViewModel.DiaryShortcut}" IsEnabled="{Binding DiaryViewModel.IsEnabled}" ToolTip="{Binding Path=DiaryViewModel.DiaryShortcut}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="DiaryShowClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}" IsEnabled="{Binding DiaryViewModel.IsEnabled}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsOpenWindow}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindow}" />
                        <telerik:RadButton Focusable="False" Margin="5,0,0,0" Click="ExpressionOpenWindowClick" HorizontalAlignment="Left" Padding="5,0,5,0" VerticalAlignment="Center" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsExpression}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutExpresion}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutExpresion}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="ExpressionShortcutClick" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsTransliteration}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutTransliteration}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutTransliteration}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="TransliterationShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0"  Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsEncloseTextQuotationMarks}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="EncloseTextQuotationMarksShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsCamelCase}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutCamelCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutCamelCase}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="CamelCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsSnakeCase}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSnakeCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSnakeCase}"/>
                        <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="SnakeCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsKebabCase}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutKebabCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutKebabCase}"/>
                        <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="KebabCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14"  Text="{telerik:LocalizableResource Key=ConverterSettingsPascalCase}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutPascalCase}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutPascalCase}"/>
                        <telerik:RadButton  Focusable="False" Margin="5,0,0,0" Click="PascalCaseShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterReplaceSelText}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutReplaceSelText}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutReplaceSelText}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="ReplaceSelTextShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsCapsOpenWindow}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindowCaps}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutOpenWindowCaps}"/>
                        <telerik:RadButton Focusable="False" Margin="5,0,0,0" Click="SwitchSelectedCapsOpenWindowClick" HorizontalAlignment="Left" Padding="5,0,5,0" VerticalAlignment="Center" Content="{telerik:LocalizableResource Key=Edit}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCaps}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCaps}"/>
                        <telerik:RadButton  Focusable="False"  Margin="7,0,0,0" Click="SwitchSelectedCapsShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsUp}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsUp}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchSelectedCapsUpShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsDown}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutSelectedCapsDown}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchSelectedCapsDownShortcutClick" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,0">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsFirstLetterToUp}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToUp}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToUp}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchFirstLetterToUp" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Margin="0,4,0,4">
                    <TextBlock  FontSize="14" Text="{telerik:LocalizableResource Key=ConverterSettingsKeyboardShortcutsFirstLetterToDown}" />
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ConverterSettingsViewModel.jgebhdhs}">
                        <TextBox Background="Transparent" IsReadOnly="True"  HorizontalAlignment="Left" Width="400" Text="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToDown}" ToolTip="{Binding Path=ConverterSettingsViewModel.ShortcutFirstLetterToDown}"/>
                        <telerik:RadButton  Focusable="False"  Margin="5,0,0,0" Click="SwitchFirstLetterToDown" HorizontalAlignment="Left" Padding="5,0,5,0" Content="{telerik:LocalizableResource Key=Edit}"/>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
