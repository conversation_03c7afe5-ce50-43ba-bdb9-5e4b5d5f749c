﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    internal class DiaryManager
    {
        internal static IEnumerable<DiaryDataModel> GetAllDiaryData()
        {
            var collection = new List<DiaryDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("DiaryDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        DiaryDataModel dataModel = new DiaryDataModel();
                        if (!bsonDocument["_id"].IsNull) dataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["Text"].IsNull) dataModel.Text = bsonDocument["Text"].AsString;
                        if (!bsonDocument["DateTime"].IsNull) dataModel.DateTime = bsonDocument["DateTime"].AsDateTime;
                        if (!bsonDocument["Application"].IsNull) dataModel.Application = bsonDocument["Application"].AsString;
                        if (!bsonDocument["DateText"].IsNull) dataModel.DateText = bsonDocument["DateText"].AsString;
                        if (!bsonDocument["ShortText"].IsNull) dataModel.ShortText = bsonDocument["ShortText"].AsString;

                        collection.Add(dataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddDiaryData(DiaryDataModel diaryData)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("DiaryDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    diaryData.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = diaryData.Id;
                    bsonDocument["DateTime"] = diaryData.DateTime;
                    bsonDocument["Application"] = diaryData.Application;
                    bsonDocument["DateText"] = diaryData.DateText;
                    bsonDocument["ShortText"] = diaryData.ShortText;
                    bsonDocument["Text"] = diaryData.Text;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllDiaryData()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("DiaryDataModel"))
                    {
                        db.DropCollection("DiaryDataModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelDiaryData(List<DiaryDataModel> diaryDataList)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("DiaryDataModel");
                    foreach (var data in diaryDataList)
                    {
                        var item = data;
                        if (item != null)
                        {
                            if (item.Id != null) schemelessCollection.Delete(item.Id);
                            else schemelessCollection.DeleteMany(Query.EQ("ShortText", item.ShortText));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void SaveAllData(IEnumerable<DiaryDataModel> diaryDataModels)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("DiaryDataModel");
                    foreach (var diaryDataModel in diaryDataModels)
                    {
                        BsonDocument bsonDocument = new BsonDocument();
                        if (diaryDataModel.Id == null)
                        {
                            diaryDataModel.Id = ObjectId.NewObjectId();
                        }
                        bsonDocument["_id"] = diaryDataModel.Id;
                        bsonDocument["DateTime"] = diaryDataModel.DateTime;
                        bsonDocument["Application"] = diaryDataModel.Application;
                        bsonDocument["DateText"] = diaryDataModel.DateText;
                        bsonDocument["ShortText"] = diaryDataModel.ShortText;
                        bsonDocument["Text"] = diaryDataModel.Text;
                        schemelessCollection.Insert(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

        }
    }
}
