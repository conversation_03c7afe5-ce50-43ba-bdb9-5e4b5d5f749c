﻿using System.Net;

namespace Everylang.App.Translator.NetRequest
{
    internal class WebResultTranslator
    {
        internal string? ErrorText { get; set; }
        internal bool WithError { get; set; }
        internal string? ResultText { get; set; } = "";
        internal string? ResultTextWithNonChar { get; set; } = "";
        internal string? LatinText { get; set; } = "";
        internal string? FromLang { get; set; }
        internal string? ToLang { get; set; }
        internal CookieCollection? Cookies { get; set; }
    }
}
