<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>O programie</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Dostępna jest nowa wersja. Uruchom ponownie aplikację</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Jasne</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Okres próbny aktywowany na 40 dni ze wszystkimi funkcjami</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Zamknąć</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Kopia</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Skopiuj HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Skopiuj Rtf</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Kliknij numer, aby wstawić tekst</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Podziel tekst przy znaku nowej linii</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Podziel tekst spacją</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Wyjście</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Zamroź okno</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>Z języka:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Pokaż historię</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Zawalić się</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Zwiększać</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Tak</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>NIE</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Wstawić</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Otwórz główne okno z tłumaczeniem</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Otwórz witrynę usługi tłumaczeniowej</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Skrót klawiaturowy otwierający okno główne</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Ustawienia</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Otwórz listę w oknie głównym</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Skrót klawiaturowy umożliwiający wyłączenie wszystkich funkcji</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Wyłącz program</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Włącz program</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>Program jest wyłączony</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Nie zamykaj</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>Po łacinie</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Język:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Tłumaczyć</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Wystąpił błąd podczas procesu tłumaczenia. Spróbuj ponownie lub wybierz inną usługę tłumaczeniową</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Słuchać</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aktualizacja</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>USTAWIENIA</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>HISTORIA</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>SCHOWEK</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>DZIENNIK</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>FRAGMENTY</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Aktywuj PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Historia</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Schowek</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Skróty klawiszowe</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Dziennik</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Nie można zarejestrować kombinacji klawiszy</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Dostępna aktualizacja, uruchom ponownie aplikację</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>Aplikacja działa i jest zminimalizowana</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>Aplikacja została zaktualizowana, lista zmian znajduje się na stronie internetowej</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Aby program działał poprawnie ze wszystkimi aplikacjami, należy go uruchomić jako administrator</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Anulować</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Wystąpił błąd i aplikacja zostanie zakończona. Wyślij SMS-a z błędami <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Na komputerze zainstalowano więcej niż jeden układ klawiatury dla niektórych języków, co może niekorzystnie wpłynąć na prawidłowe działanie funkcji przełączania układu.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Chrzcielnica</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Znajdować...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(wersja PRO)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Zmiana</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Ratować</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Usuwać</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Tworzyć</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Wszystko</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Wszystkie programy</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Tylko w wersji PRO</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>Program jest zminimalizowany do zasobnika</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Przed użyciem programu zapoznaj się z dokumentacją</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Zakładka - Szukaj.  Esc – Anuluj i wyczyść</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Rzeczownik</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Zaimek</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Przymiotnik</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Czasownik</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Przysłówek</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Pretekst</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Unia</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Wykrzyknik</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Komunia</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Czasownik pomocniczy</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Słowo wprowadzające</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Sprawdzanie pisowni</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Żadnych błędów</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Język nie jest obsługiwany</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Wystąpił błąd</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Tekst jest za długi</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Zamknąć</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Opcje:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Brak opcji</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Weryfikacja zakończona</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Pominąć</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Pomiń wszystko</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Zastępować</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Wymień wszystkie</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Wstaw tekst</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Kopia</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Powrót</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Zresetuj ustawienia</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Naciśnij kombinację klawiszy</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>rosyjski</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>angielski</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>francuski</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>włoski</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ukraiński</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>O PROGRAMIE</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Umowa licencyjna</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Uniwersalny asystent do pracy z tekstem w różnych językach</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Wersja:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Dostępna jest nowa wersja programu</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Aktualizacja</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>INFORMACJA ZWROTNA</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Napisz swoje uwagi i/lub pytania</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Zresetuj wszystkie ustawienia programu</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Otwórz okno powitalne</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Zresetować wszystkie ustawienia i dane programu?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Formularz kontaktowy</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>Program działa jako administrator</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>Program nie uruchamia się jako administrator</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>KOMPONENTY</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Zmiana układu</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Zmień układ ostatniego słowa</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Użyj Przerwy</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Zmień układ zaznaczonego tekstu</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Wybór metody przełączania układu</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulacja klawiszy do przełączania układów</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Uruchamianie polecenia systemu Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Zachowaj zaznaczony tekst po zmianie układu</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Włączono przełączanie układu</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Użyj Ctrl+(liczba), aby przełączyć się na określony język</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Użyj podwójnego kliknięcia klawisza Shift</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Przełącz od początku linii</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Użyj podwójnego kliknięcia przycisku ScrollLock</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Przełącz układ za pomocą przycisku</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Ustawienia systemowe</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Prawy Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Lewy Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Prawy Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Lewy Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Prawo lub lewo Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Przesunięcie w prawo lub w lewo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Prawy Ctrl lub CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Aby włączyć lub wyłączyć funkcję CapsLock, naciśnij jednocześnie prawy i lewy Shift</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Dźwięk przełączania układu</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Ustawianie dźwięku przełączania układów</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista języków, na które przełączy się układ</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Ustawianie klawiszy umożliwiających przełączenie na określony język</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Wyłączyć automatyczne przełączanie układu?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Włączyć automatyczne przełączanie układu?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Automatyczne przełączanie układu</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Automatyczne przełączanie włączone</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Popraw dwie duże litery na początku słowa</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Napraw przypadkowe naciśnięcie klawisza CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Nie przełączaj, jeśli wszystkie litery w słowie są pisane wielkimi literami</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Popraw układ po naciśnięciu klawisza Enter</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Dodaj jednoliterowe słowa do reguł</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Automatycznie dodawaj reguły przełączania</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Nie poprawiaj układu, jeśli został on wcześniej zmieniony ręcznie</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Zmień układ dopiero po wpisaniu całego słowa</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Zmień układ po zakończeniu pisania</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Usuń wszystkie reguły automatycznego przełączania</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Tekst</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Wszystkie układy</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Działanie</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Dodaj reguły dopiero po potwierdzeniu</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Przełącznik</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Nie przełączaj</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Kandydat</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Zasady automatycznego przełączania</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista języków, dla których będzie działać automatyczne przełączanie</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Lista reguł automatycznego przełączania</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Pokaż kandydatów</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Otwórz listę reguł automatycznego przełączania</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Liczba ręcznych przełączeń układu słów do uwzględnienia w regułach</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Dodać słowo do reguł automatycznego przełączania? Wejdź - TAK</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Bieżący język wprowadzania na wskaźniku myszy</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Bieżący język wprowadzania w kursorze tekstowym</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Oddzielne okno wskaźnika języka</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Bieżący język w zasobniku systemowym</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Zaawansowane funkcje</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Pokaż flagę kraju</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Pokaż nazwę języka</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Przezroczystość wskaźnika</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Zwiększenie wielkości wskaźnika w procentach</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Wskazanie aktualnego języka na klawiaturze</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Pozycja wskaźnika na wskaźniku myszy</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Ukryj wskaźnik w programach działających na pełnym ekranie</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Pokaż status CapsLocka</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock włączony</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Pozycja wskaźnika w kursorze tekstowym</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Sprawdzanie pisowni</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Sprawdzanie pisowni włączone</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Sprawdź pisownię podczas pisania</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Dźwięk sprawdzania pisowni podczas pisania</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Użyj liczb do szybkiej wymiany</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Ustawienia dźwięku do sprawdzania pisowni</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Skrót klawiaturowy umożliwiający sprawdzenie pisowni zaznaczonego tekstu</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Sprawdź pisownię zaznaczonego tekstu</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Zamknij okno, jeśli po 3 sekundach nie pojawią się żadne błędy</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Menedżer schowka</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Wklej tekst bez formatowania i wklej ścieżkę skopiowanego pliku</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Menedżer schowka włączony</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Otwórz historię schowka za pomocą skrótu klawiaturowego</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Sekwencyjne wklejanie tekstu z historii schowka dla bieżącego okna</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Sekwencyjne wklejanie tekstu z historii schowka</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Wklej tekst klawiszem Ctrl+Shift+(liczba) - liczba 1, 2, 3, 4, 5, 6, 7, 8, 9 - indeks wpisu w historii schowka</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Zapisz ścieżkę do skopiowanego pliku w historii schowka</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Zapisz historię bufora obrazu</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Wklejając tekst z historii, zastąp bieżącą wartość w schowku</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Rozmiar historii schowka</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Dźwięk zmiany schowka</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Konwerter tekstu</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Konwertuj w oparciu o bieżący układ klawiatury</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Konwertuj liczby i daty na ciągi znaków, oceniaj wyrażenia</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Skonfiguruj skróty klawiszowe</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Odwróć wielkość liter w zaznaczonym tekście</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Konwertuj zaznaczony tekst na wielkie litery</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Zaznaczony tekst małymi literami</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Mały znak pierwszego znaku słowa pod kursorem</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Wielkim literą pierwszy znak słowa pod kursorem</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Transliteruj zaznaczony tekst</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Konwertuj tekst na styl camelCase</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Znajdź i zamień tekst w zaznaczonym tekście</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Obramuj zaznaczony tekst symbolami (przykład)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Dodać</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Lewy</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Prawidłowy</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Usuwać</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>przykładowy tekst</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Zamknij ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Zamień tekst ENTER</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Tłumaczenie</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Domyślny język do tłumaczenia</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Domyślny język do tłumaczenia</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Główny język tłumaczenia</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Usługa tłumaczeniowa</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Skrót klawiaturowy do tłumaczenia wybranego tekstu</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Naciśnij kombinację klawiszy</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Dwukrotne kliknięcie przycisku Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Tłumaczenie podczas zaznaczania tekstu myszą</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Tłumaczenie w cenie</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Ustawienia ogólne</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Język interfejsu programu</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Zrestartować aplikację, aby zmienić język?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Różnorodny</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Minimalizuj do zasobnika</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Uciekanie z okien</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Uruchom z uprawnieniami administratora</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Sprawdź aktualizacje</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Aktualizacja do wersji beta</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Temat</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>Dzień lub noc</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Style projektowania</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Folder do zapisywania ustawień programu</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Użyj systemowych ustawień serwera proxy</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Wpisz adres serwera</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Wprowadź port</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Wpisz swoją nazwę użytkownika</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Wpisz swoje hasło</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Zapisz ustawienia proxy</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Ustawienia proxy</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Błąd ustawień serwera proxy, zmień ustawienia</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Wieczorem używaj ciemnego motywu</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Godziny wieczorne od</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Godziny wieczorne do godz</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Wyłącz wszystkie funkcje w programach działających w trybie pełnoekranowym</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Importuj ustawienia</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Eksportuj ustawienia</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Podstawowe ustawienia</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Tłumacz</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Sprawdzanie pisowni</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Wskaźnik układu</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Funkcje PRO</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Schowek</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Dziennik</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Konwerter tekstu</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Przełączanie układów</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Przełącznik automatyczny</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Programy wyjątków</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Domyślne układy</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>Inteligentne kliknięcie</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>O programie</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Licencja</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Fragmenty</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Tłumaczyć</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Kopia</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Sprawdź pisownię</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Wyszukiwanie połączeń</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Otwórz link w przeglądarce</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Przetłumacz witrynę za pomocą linku</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Generowanie krótkiego linku</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Utwórz wiadomość e-mail</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Wstaw tekst</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Wklej tekst bez formatowania</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Otwórz historię schowka</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Otwórz historię tłumaczeń</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Otwórz dziennik</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Zmień wielkość zaznaczonego tekstu</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Otwórz listę fragmentów</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Konwerter tekstu</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Zaznaczony tekst małymi literami</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Konwertuj zaznaczony tekst na wielkie litery</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Odwróć wielkość liter w zaznaczonym tekście</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Obramuj zaznaczony tekst symbolami</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Transliteruj zaznaczony tekst</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Konwertuj liczby i daty na ciągi znaków, oceniaj wyrażenia</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Przetwornik</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Tłumaczenie</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Kopia</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Pisownia</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Szukaj</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Połączyć</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Tłumaczenie strony internetowej</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>Adres URL krótszy</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Wstawić</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Wstawienie bez formatu.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>bez formatu.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Historia bufora</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>bufor</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Dziennik</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Fragmenty</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Zarejestruj się</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Zarejestruj się</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Odwróć rejestr</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Kadrowanie tekstu</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Transliteracja</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Mata. wyrażenia</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Start:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Koniec:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Usuwać</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Jasne</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Dołączony</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEKST</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>APLIKACJA</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Programy wyjątków</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Dodać</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Dodaj plik exe</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Dodaj folder</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Dodaj według tytułu okna</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Wskaźnik układu</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Przełączanie układów</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>Inteligentne kliknięcie</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Fragmenty</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Skróty klawiszowe</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Automatyczne przełączanie języków</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Dziennik wprowadzania tekstu</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Konwerter i obudowa tekstowa</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Historia schowka</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Zapisz obrazy</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Usuwać</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Kliknij żądany program</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Zaznacz funkcje, które będą działać w programie</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Dodawanie z listy programów</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Dodawanie za pomocą kursora</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Języki programu</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Domyślny język dla wybranych programów</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Aktywacja funkcji PRO</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Kod aktywacyjny</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Aktywować</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Spróbuj</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO aktywowane</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Okres próbny 40 dni</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Informacje o licencji</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Data aktywacji:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Okres ważności licencji:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>E-mail:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Właściciel:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Informacje o licencji</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Liczba miejsc:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Liczba dostępnych reaktywacji:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Dostępne miejsca:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Typ licencji:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO nie zostało aktywowane</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO aktywowane na okres próbny</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Kupić</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Wpisz adres e-mail</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Wpisz kod</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Informacje o Twojej licencji zostały wysłane do:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Usuń licencję</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Wpisz kod i e-mail</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Aktywacja zakończona pomyślnie</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Aktywacja zakończona błędem</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Aktywacja zakończyła się błędem, Twoja licencja została zablokowana</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>Program został aktywowany na nowym stanowisku pracy, ale został dezaktywowany na komputerze</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Aktywacja zakończyła się błędem. Sprawdź swoje połączenie internetowe</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Aktywacja zakończyła się błędem, mógł zostać wpisany błędny adres e-mail</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>Okres próbny dobiegł końca</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>Licencja wygasła</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Aktywacja nie jest możliwa ze względu na przekroczenie limitu reaktywacji, należy zakupić dodatkowe stanowiska dla tej licencji</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>Aktywacja nie jest możliwa ze względu na ponowną aktywację tej licencji na innym komputerze</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Twój kod aktywacyjny został zaktualizowany</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Licencja wieczysta</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Przekroczono limit liczby stanowisk dla licencji, wszystkie funkcje PRO zostaną wyłączone</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>Wersja PRO wygasła, nową licencję można kupić na stronie EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>Wersja PRO wkrótce wygaśnie</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Przekroczono limit reaktywacji licencji z ostatnich 30 dni, wszystkie funkcje PRO zostaną wyłączone</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Twoja licencja jest zablokowana, wszystkie funkcje PRO zostaną wyłączone</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>Inteligentne kliknięcie</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>Włączono funkcję SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Wybierz usługę wyszukiwania dla SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Sprawdź funkcje, które będą dostępne</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Otwórz, naciskając lewy, a następnie prawy przycisk myszy</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Otwórz, klikając dwukrotnie środkowym przyciskiem myszy</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Użyj klawiszy skrótu, aby otworzyć</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Klawisze skrótu do otwierania okna SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Pokaż okno pomocy po zaznaczeniu tekstu</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Rozmiar okna</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Położenie okna względem wskaźnika myszy</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Dziennik</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Otwórz dziennik</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>W zestawie dziennik</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Hasło do pamiętnika</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Wprowadź swoje stare hasło do pamiętnika</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Nowe hasło zapisane</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Resetowanie hasła</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Liczba wpisów w dzienniku</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>Wpisane hasło jest nieprawidłowe. Zresetować obecne hasło? W takim przypadku wszystkie dane z dziennika zostaną usunięte</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Zachowaj zdania składające się z jednego wyrazu</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Tekst do zastąpienia (opcjonalnie):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Fragment tekstu:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>Na jaki język powinienem przełączyć układ?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Nie przełączaj</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Tagi (oddzielone spacjami):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Tagi</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Opis:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Ratować</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Anulować</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Utrzymuj pozycję kursora</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Zamień podczas pisania</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Fragmenty</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Nowy fragment</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Usunąć wszystkie fragmenty z tym tagiem?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Włączono opcję Snippetst</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Edytuj fragmenty</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Co wymienić</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>Czym zastąpić</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Dodać</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Zmiana</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Usuwać</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Zamień podczas pisania w innym układzie</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Dopasuj wielkość liter</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Pokaż podpowiedź podczas pisania</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Sortuj według częstotliwości użytkowania</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Zastąp przez:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Fragmenty włączone</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Otwórz listę fragmentów do wstawienia</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Dodaj zaznaczony tekst do fragmentów</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Klawisz Tab</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Wprowadź klucz</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tab lub Enter</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Przestrzeń</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Spacja lub Enter</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Skróty klawiszowe</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Podwójne naciśnięcie klawisza</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Kliknięcie przycisku myszy</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Włączono klawisze skrótu</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Nieobecny</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Naciśnij klawisze skrótu</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Połączenie</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Kliknij dwukrotnie</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Kliknięcie przycisku myszy</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Wybierz klawisz, który chcesz dwukrotnie nacisnąć</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Wybierz dodatkowy przycisk myszy</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Klawisze skrótu wyłączone</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Lewy lub prawy Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Lewy Ctrl</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Prawy Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Przesunięcie w lewo lub w prawo</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Lewy Shift</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Prawy Shift</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Lewy Alt</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Prawy Alt</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Włącz dźwięk</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Tom</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Wybierz dźwięk</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Przed użyciem programu zapoznaj się z dokumentacją</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Poznaj funkcje programu</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Obejrzyj prezentację wideo</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Strona programu</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Kup licencję</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Rozpoznawanie tekstu</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Skrót klawiaturowy umożliwiający rozpoczęcie OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Wybierz języki domyślne</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Wybierz języki</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>Używanie języków europejskich i azjatyckich nie jest obsługiwane</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Języki europejskie:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Języki azjatyckie:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Wybierz obszar ekranu</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Otwórz obraz</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Rozpoznać</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Rozpoznaj kod kreskowy</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Wybierz języki</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Wybierz obszar na ekranie lub prześlij plik do rozpoznania</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Rozpoznawanie tekstu</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Proszę czekać, moduł się ładuje</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Kliknij, aby pobrać moduł</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Tekst skopiowany</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Tekst nie został rozpoznany</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Skopiuj obraz</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Skopiuj tekst</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Zamień na:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Szukaj:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Znaleziono wszystkie dopasowania:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Nie znaleziono żadnych dopasowań!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Całkowita liczba dokonanych wymian:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATA</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TYP</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>Menedżer schowka jest wyłączony</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Wyczyść filtr</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filtr</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Wybierz Kolumny</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Wyszukiwanie pełnotekstowe</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Pogrupowane według:</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Nagłówek grupy</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Przeciągnij nagłówek kolumny i upuść go tutaj, aby pogrupować według tej kolumny</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>nie pusty</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Pusty</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Nie jest pusty</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Nieważny</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Zaczyna się od</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Pokaż wiersze z wartością that</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Zaznacz wszystko</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Lub</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Wielkość liter ma znaczenie</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>nie równe</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Mniejsze lub równe</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Mniej niż</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Nie zawarte w</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Większe lub równe</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Ponad</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Równe</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Zawarte w</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Kończy się z</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Nie zawiera</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Zawiera</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>I</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>W zestawie dziennik</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Dziennik jest wyłączony</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Dołączony</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Wyłączony</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Przełączanie układu wyłączone</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Włączono automatyczne przełączanie układu</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>Automatyczne przełączanie układu jest wyłączone</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Kliknij tutaj, aby dodać nowy element</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Wyczyść historię tłumaczeń</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Przechowuj historię tłumaczeń</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Skopiuj przetłumaczony tekst</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Wyczyść wszystko</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Otwórz w przeglądarce</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Zamknąć program?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Polecane języki</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Wybierz swoje ulubione języki</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Chrzcielnica</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Funkcje</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Załadunek</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Zmiana rozmiaru płótna</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Zamknąć</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Korekta</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Suma</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Automatyczny</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Tło:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Kolor obramowania:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Grubość obramowania:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Rozmiar płótna</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>biały</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Przycinać</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Tekst obrazkowy</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Twój tekst</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Rysować</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Kolor pędzla:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Rozmiar pędzla:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Plama</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Jasność</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Kontrast</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Zmiana odcienia</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Odwróć kolory</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Nasycenie</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Wyostrzyć</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modyfikacja</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Odwróć poziomo</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Odwróć w pionie</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Rozmiar czcionki</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Wysokość:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Pozycja pozioma</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Wyrównanie obrazu</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Podgląd obrazu</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Rozmiar obrazu</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Otwarte</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Opcje</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Zachowaj oryginalne proporcje</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Promień:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Powrót</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Rozmiar względny</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Zmień rozmiar</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Obróć o 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Obróć o 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Obróć o 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Zakręt</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Zaokrąglone rogi</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Ratować</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Postać</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Elipsa</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Harmonogram</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Prostokąt</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Kolor obramowania</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Grubość granicy</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Formularz napełniania rur</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Zablokuj proporcje</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Postać</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Wypełnienie kształtu</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Tekst</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Kolor tekstu</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Nie można otworzyć pliku.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Nie można otworzyć pliku. Może to być blokowane przez inną aplikację.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Konwertować</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Nie udało się zapisać pliku.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Anulować</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Ten format pliku nie jest obsługiwany.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Pozycja pionowa</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Szerokość:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Nastawić</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Zresetuj wszystko</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Ze schowka</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Edytuj obraz</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Wklej wejście emulujące</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Edytor fragmentów</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Tekst zastępczy:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Moduł rozpoznawania tekstu nie jest załadowany</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Wygląd</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Kolejność funkcji</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Pokaż tylko wybrane języki</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Pokaż wszystko</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Zrestartować program, aby zmienić motyw?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Aby wstawić tekst, naciśnij klawisz</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Notatki</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Notatki</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Pokaż notatki</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>Do archiwum</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Dodaj notatkę</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Lista notatek</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Uwaga kolor</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Konwertuj na notatkę</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Konwertuj na listę zadań</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Wklej jako zwykły tekst</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Wklej jako tekst</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Archiwum notatek</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Przywrócić</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Rodzina czcionek i rozmiar notatek</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Przejrzystość nieaktywnych notatek</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>Funkcja SmartClick wyłączona</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Dziennik wyłączony</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Fragmenty wyłączone</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Pokaż przycisk zamykania</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Limit czasu dla emulacji naciśnięcia klawisza</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Obraz</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Rozpoznany tekst</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Aktualizacja nie została ukończona. Zaktualizuj ręcznie.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Błąd aktualizacji EveryLang</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Oznaczone</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Dołączony</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Wpisz tekst do wyszukania</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Automatyczny</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Z archiwum</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Wyłączony</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>W archiwum</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Układ klawiatury</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Sprawa tekstowa</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Pokaż stan NumLock</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock jest wyłączony</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>Włączony NumLock</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Otwórz okno z funkcjami konwertera</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Pierwszą literę pisz wielką literą</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Najpierw</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Pierwsza litera z małej litery</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Najpierw w dół</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Otwórz okno z funkcjami zmiany wielkości liter</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Konwertuj tekst na styl Snake_case</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Konwersja tekstu na styl kebaba</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Konwersja tekstu na styl PascalCase</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Sprawa tekstowa</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Kliknij, aby pobrać</value>
  </data>
  <data name="AutochangeSortingByAlphabet" xml:space="preserve">
    <value>Sortuj według alfabetu</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Ulubione</value>
  </data>
  <data name="RemoveFavorite" xml:space="preserve">
    <value>Usuń z ulubionych</value>
  </data>
  <data name="AddFavorite" xml:space="preserve">
    <value>Dodaj do ulubionych</value>
  </data>
  <data name="ClipboardFavorite" xml:space="preserve">
    <value>Ulubione schowki</value>
  </data>
</root>