﻿using System;
using System.Diagnostics;
using System.Management;
using Vanara.PInvoke;

namespace Everylang.App.Utilities
{
    class SingleInstance
    {
        static string GetProcessOwner(int processId)
        {
            try
            {
                string query = "Select * From Win32_Process Where ProcessID = " + processId;
                ManagementObjectSearcher searcher = new ManagementObjectSearcher(query);
                ManagementObjectCollection processList = searcher.Get();

                foreach (var o in processList)
                {
                    var obj = (ManagementObject)o;
                    object[] argList = { string.Empty, string.Empty };
                    int returnVal = Convert.ToInt32(obj.InvokeMethod("GetOwner", argList));
                    if (returnVal == 0)
                    {
                        // return DOMAIN\user
                        return argList[1] + "\\" + argList[0];
                    }
                }
            }
            catch
            {
                // ignored
            }
            return "NO OWNER";
        }

        internal static bool AlreadyRunning()
        {
            bool running = false;
            try
            {
                // Getting collection of process  
                Process currentProcess = Process.GetCurrentProcess();

                // Check with other process already running   
                foreach (var p in Process.GetProcesses())
                {
                    if (p.Id != currentProcess.Id) // Check running process   
                    {
                        if (p.ProcessName.Equals(currentProcess.ProcessName) && p.MainWindowHandle != IntPtr.Zero)
                        {
                            if (GetProcessOwner(p.Id).Contains(Environment.UserName))
                            {
                                running = true;
                                IntPtr hFound = p.MainWindowHandle;
                                if (User32.IsIconic(hFound)) // If application is in ICONIC mode then  
                                    User32.ShowWindow(hFound, ShowWindowCommand.SW_RESTORE);
                                User32.SetForegroundWindow(
                                    hFound); // Activate the window, if process is already running  
                                break;
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }
            return running;
        }
    }
}
