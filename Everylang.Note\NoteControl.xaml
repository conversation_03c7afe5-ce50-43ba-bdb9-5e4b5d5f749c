﻿<UserControl x:Class="Everylang.Note.NoteControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Everylang.Note"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:helpers="clr-namespace:Everylang.Note.Helpers"
             xmlns:dd="urn:gong-wpf-dragdrop"
             mc:Ignorable="d" 
             Name="me"
             x:ClassModifier="internal" PreviewKeyUp="NoteControl_OnPreviewKeyUp" PreviewKeyDown="NoteControl_OnPreviewKeyDown">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Grey.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <Style x:Key="TextChanged" TargetType="{x:Type TextBox}" BasedOn="{StaticResource {x:Type TextBox}}">
                <EventSetter Event="TextChanged" Handler="TextBoxBase_OnTextChanged"/>
            </Style>
            <helpers:ColorToSolidColorBrushValueConverter  x:Key="ColorToSolidColorBrushValueConverter"/>
            <helpers:BindingProxy x:Key="Primary400Proxy" Data="{DynamicResource Primary400}" />
            <helpers:BindingProxy x:Key="Primary300Proxy" Data="{DynamicResource Primary200}" />
            <helpers:BindingProxy x:Key="Primary100Proxy" Data="{DynamicResource Primary100}" />
            <helpers:BindingProxy x:Key="Primary50Proxy" Data="{DynamicResource Primary50}" />
            <RoutedUICommand x:Key="CommandPasteWithoutFormatting" Text="" />
            <CollectionViewSource x:Key="Groups" Source="{Binding CheckListCollection}">
                <CollectionViewSource.GroupDescriptions>
                    <PropertyGroupDescription PropertyName="IsSelectedItem"/>
                </CollectionViewSource.GroupDescriptions>
            </CollectionViewSource>
            <Style x:Key="ToolButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
                <Setter Property="Width" Value="18" />
                <Setter Property="Height" Value="18" />
                <Setter Property="Padding" Value="0"/>
            </Style>

            <Style TargetType="{x:Type GroupItem}" x:Key="NoGroupHeaderStyle">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupItem}">
                            <ItemsPresenter />
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style TargetType="{x:Type GroupItem}" x:Key="WithGroupHeaderStyle">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupItem}">
                            <Expander materialDesign:ExpanderAssist.HorizontalHeaderPadding="27,2,20,2" IsExpanded="True" Background="{Binding Source={StaticResource  Primary100Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}">
                                <Expander.Header>
                                    <TextBlock Text="{telerik:LocalizableResource Key=NoteCheckMarket}" FontSize="14" Padding="0" />
                                </Expander.Header>
                                <ItemsPresenter/>
                            </Expander>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            <local:NullGroupStyleSelector  x:Key="GrpStyleSelector" DefaultStyle="{StaticResource WithGroupHeaderStyle}" NullGroupStyle="{StaticResource NoGroupHeaderStyle}"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <UserControl.CommandBindings>
        <CommandBinding Command="{StaticResource CommandPasteWithoutFormatting}" Executed="PasteWithoutFormatting" CanExecute="CommandBinding_OnCanExecute"/>
    </UserControl.CommandBindings>

    <!--<UserControl.InputBindings>
        <KeyBinding Gesture="Ctrl+Shift+V" Command="{StaticResource CommandPasteWithoutFormatting}"/>
    </UserControl.InputBindings>-->

    <Grid>
        <Grid x:Name="StackPanelCheckList"  Visibility="Collapsed" Background="{Binding Source={StaticResource  Primary50Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}">
            <ListBox  x:Name="CheckListControl"  ItemsSource="{Binding Source={StaticResource Groups}}"  Background="{Binding Source={StaticResource  Primary50Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" BorderBrush="{x:Null}" BorderThickness="0"
                     dd:DragDrop.IsDragSource="True"
                      dd:DragDrop.IsDropTarget="True"
                     dd:DragDrop.UseDefaultEffectDataTemplate="False"
                      dd:DragDrop.UseDefaultDragAdorner="True"
                     AlternationCount="{Binding RelativeSource={RelativeSource Self}, Path=Items.Count}"
                          Drop="CheckListControl_OnDrop"
                          VerticalAlignment="Stretch"
                          HorizontalAlignment="Stretch">

                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource {x:Type ListBoxItem}}">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ListBoxItem">
                                    <Border x:Name="Border" Padding="0,0,1,0" >
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="25"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="20*"/>
                                                <ColumnDefinition Width="22"/>
                                                <ColumnDefinition Width="5"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock x:Name="buttonMove" VerticalAlignment="Center" Cursor="SizeAll">
                                                <materialDesign:PackIcon Kind="Drag" Height="24" Width="24" />
                                            </TextBlock>
                                            <CheckBox Style="{StaticResource MaterialDesignCheckBox}" VerticalAlignment="Center" Background="{Binding Source={StaticResource  Primary400Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" IsChecked="{Binding IsSelectedItem}" Grid.Column="1" Checked="ToggleButton_OnChecked" Unchecked="ToggleButton_OnChecked"/>
                                            <StackPanel Name="stackPanel" Margin="5 0 0 0" Grid.Column="2" HorizontalAlignment="Stretch" VerticalAlignment="Center">
                                                <TextBox Margin="5 0 0 0" Padding="0,2" x:Name="textBox" SelectionBrush="SteelBlue" Text="{Binding Text, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" FontSize="14" TextWrapping="Wrap" BorderBrush="Transparent" CaretBrush="{DynamicResource MaterialDesignTextBoxBorder}" GotFocus="Testing_GotFocus" PreviewKeyUp="TextBox_OnPreviewKeyUp" UndoLimit="10">
                                                    <TextBox.Template>
                                                        <ControlTemplate TargetType="{x:Type TextBox}">
                                                            <Grid>
                                                                <Border x:Name="border"
                                                                                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"
                                                                                        Background="{TemplateBinding Background}"
                                                                                        SnapsToDevicePixels="True"
                                                                                        Padding="0 1 0 1">
                                                                    <Grid Margin="{TemplateBinding Padding}"
                                                                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                                                        <ScrollViewer x:Name="PART_ContentHost" Focusable="false" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden"/>
                                                                        <TextBlock Text="{Binding Path=(materialDesign:HintAssist.Hint), RelativeSource={RelativeSource TemplatedParent}}"
                                                                                           Visibility="{TemplateBinding Text, Converter={StaticResource TextFieldHintVisibilityConverter}}"
                                                                                           IsHitTestVisible="False"
                                                                                           x:Name="Hint"
                                                                                           Margin="1 0 1 0"
                                                                                           Opacity="{Binding Path=(materialDesign:HintAssist.HintOpacity), RelativeSource={RelativeSource TemplatedParent}}"/>
                                                                    </Grid>
                                                                </Border>
                                                                <materialDesign:Underline x:Name="Underline" Visibility="{Binding Path=(materialDesign:TextFieldAssist.DecorationVisibility), RelativeSource={RelativeSource TemplatedParent}}"/>
                                                            </Grid>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsEnabled" Value="false">
                                                                    <Setter Property="Opacity" TargetName="border" Value="0.56"/>
                                                                </Trigger>
                                                                <Trigger Property="IsMouseOver" Value="true">
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                                                </Trigger>
                                                                <Trigger Property="IsKeyboardFocused" Value="true">
                                                                    <Setter TargetName="Underline" Property="IsActive" Value="True"/>
                                                                </Trigger>
                                                                <Trigger Property="Validation.HasError" Value="true">
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource ValidationErrorBrush}"/>
                                                                    <Setter TargetName="Underline" Property="Background" Value="{DynamicResource ValidationErrorBrush}"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </TextBox.Template>
                                                </TextBox>
                                            </StackPanel>
                                            <Button x:Name="buttonDelete" Grid.Column="3" Style="{StaticResource ToolButton}" Click="DeleteSelected" VerticalAlignment="Center" ToolTip="{telerik:LocalizableResource Key=Delete}">
                                                <materialDesign:PackIcon Kind="CloseCircle" />
                                            </Button>
                                        </Grid>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <DataTrigger Binding="{Binding IsSelectedItem}" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="{Binding Source={StaticResource  Primary100Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" />
                                            <Setter TargetName="textBox" Property="TextDecorations">
                                                <Setter.Value>
                                                    <TextDecorationCollection>
                                                        <TextDecoration Location="Strikethrough">
                                                            <TextDecoration.Pen>
                                                                <Pen Brush="Black" />
                                                            </TextDecoration.Pen>
                                                        </TextDecoration>
                                                    </TextDecorationCollection>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>

                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="textBox" Property="FocusManager.FocusedElement" Value="{Binding ElementName=textBox}"/>
                                        </Trigger>

                                        <DataTrigger Binding="{Binding IsMouseOver, ElementName=Border}" Value="True">
                                            <Setter TargetName="buttonDelete" Property="Opacity" Value="1"></Setter>
                                            <Setter TargetName="buttonMove" Property="Opacity" Value="1"></Setter>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsMouseOver, ElementName=Border}" Value="False">
                                            <Setter TargetName="buttonDelete" Property="Opacity" Value="0"></Setter>
                                            <Setter TargetName="buttonMove" Property="Opacity" Value="0"></Setter>
                                        </DataTrigger>


                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsDopItem}" Value="True">
                                <Setter  Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate  TargetType="ListBoxItem">
                                            <Border>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="25"/>
                                                        <ColumnDefinition Width="20"/>
                                                        <ColumnDefinition Width="20*"/>
                                                        <ColumnDefinition Width="25"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Column="1" Foreground="Gray" >
                                                                    <materialDesign:PackIcon Kind="Plus" Height="20" Width="20" />
                                                    </TextBlock>
                                                    <TextBox Name="textBoxNewTask" Margin="11 0 0 0" Style="{StaticResource TextChanged}" Grid.Column="2" BorderBrush="Transparent" materialDesign:HintAssist.Hint="New Task" CaretBrush="{DynamicResource MaterialDesignTextBoxBorder}"/>
                                                </Grid>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>

                        </Style.Triggers>
                    </Style>
                </ListBox.ItemContainerStyle>
                <ListBox.GroupStyle>
                    <GroupStyle ContainerStyleSelector="{StaticResource GrpStyleSelector}" />
                </ListBox.GroupStyle>
            </ListBox>
        </Grid>
        <RichTextBox materialDesign:TextFieldAssist.DecorationVisibility="Hidden" CaretBrush="Black" SelectionBrush="SteelBlue" Padding="3" UndoLimit="50" LostKeyboardFocus="ResTextBox_OnLostKeyboardFocus" GotKeyboardFocus="ResTextBox_OnGotKeyboardFocus" IsInactiveSelectionHighlightEnabled="True" Name="ResTextBox" BorderThickness="0"  Block.LineHeight="1" BorderBrush="{x:Null}" Background="{Binding Source={StaticResource  Primary50Proxy}, Path=Data, Converter={StaticResource ColorToSolidColorBrushValueConverter}}" VerticalScrollBarVisibility="Auto">
            <RichTextBox.ContextMenu>
                <ContextMenu Opened="ContextMenu_OnOpened">
                    <MenuItem Command="ApplicationCommands.Cut"/>
                    <MenuItem Command="ApplicationCommands.Copy"/>
                    <MenuItem Command="ApplicationCommands.Paste"/>
                    <MenuItem Name="ItemPasteWf" Header="{telerik:LocalizableResource Key=NotePasteAsText}" ToolTip="{telerik:LocalizableResource Key=NotePasteAsPlainText}" Command="{StaticResource CommandPasteWithoutFormatting}" />
                    <MenuItem Command="ApplicationCommands.Delete"/>
                    <Separator/>
                    <MenuItem Command="ApplicationCommands.SelectAll"/>
                </ContextMenu>
            </RichTextBox.ContextMenu>
        </RichTextBox>
    </Grid>
</UserControl>
