﻿namespace Everylang.Note.Helpers.StickyWindow
{
    internal static class Win32
    {
        /// <summary>
        /// VK is just a placeholder for VK (VirtualKey) general definitions
        /// </summary>
        internal class VK
        {
            internal const int VK_SHIFT = 0x10;
            internal const int VK_CONTROL = 0x11;
            internal const int VK_MENU = 0x12;
            internal const int VK_ESCAPE = 0x1B;
        }

        /// <summary>
        /// WM is just a placeholder class for WM (WindowMessage) definitions
        /// </summary>
        internal class WM
        {
            internal const int WM_MOUSEMOVE = 0x0200;
            internal const int WM_NCMOUSEMOVE = 0x00A0;
            internal const int WM_NCLBUTTONDOWN = 0x00A1;
            internal const int WM_NCLBUTTONUP = 0x00A2;
            internal const int WM_NCLBUTTONDBLCLK = 0x00A3;
            internal const int WM_LBUTTONDOWN = 0x0201;
            internal const int WM_LBUTTONUP = 0x0202;
            internal const int WM_KEYDOWN = 0x0100;
        }

        /// <summary>
        /// HT is just a placeholder for HT (HitTest) definitions
        /// </summary>
        internal class HT
        {
            internal const int HTERROR = -2;
            internal const int HTTRANSPARENT = -1;
            internal const int HTNOWHERE = 0;
            internal const int HTCLIENT = 1;
            internal const int HTCAPTION = 2;
            internal const int HTSYSMENU = 3;
            internal const int HTGROWBOX = 4;
            internal const int HTSIZE = HTGROWBOX;
            internal const int HTMENU = 5;
            internal const int HTHSCROLL = 6;
            internal const int HTVSCROLL = 7;
            internal const int HTMINBUTTON = 8;
            internal const int HTMAXBUTTON = 9;
            internal const int HTLEFT = 10;
            internal const int HTRIGHT = 11;
            internal const int HTTOP = 12;
            internal const int HTTOPLEFT = 13;
            internal const int HTTOPRIGHT = 14;
            internal const int HTBOTTOM = 15;
            internal const int HTBOTTOMLEFT = 16;
            internal const int HTBOTTOMRIGHT = 17;
            internal const int HTBORDER = 18;
            internal const int HTREDUCE = HTMINBUTTON;
            internal const int HTZOOM = HTMAXBUTTON;
            internal const int HTSIZEFIRST = HTLEFT;
            internal const int HTSIZELAST = HTBOTTOMRIGHT;

            internal const int HTOBJECT = 19;
            internal const int HTCLOSE = 20;
            internal const int HTHELP = 21;
        }

        internal class Bit
        {
            internal static int HiWord(int iValue)
            {
                return (iValue >> 16) & 0xFFFF;
            }

            internal static int LoWord(int iValue)
            {
                return iValue & 0xFFFF;
            }
        }
    }
}
