﻿using System.Runtime.InteropServices;
using System.Windows;
using Vanara.PInvoke;
using Cursors = System.Windows.Forms.Cursors;


namespace Everylang.App.Utilities
{
    class MousePosition
    {
        internal static bool IsTextCursor
        {
            get
            {
                var h = Cursors.IBeam.Handle;
                User32.CURSORINFO pci = default;
                pci.cbSize = (uint)Marshal.SizeOf(typeof(User32.CURSORINFO));
                User32.GetCursorInfo(ref pci);
                return h == pci.hCursor;
            }
        }

        internal static Point GetMousePoint(Point point)
        {
            return MousePointData(point);
        }

        internal static Point GetMousePoint()
        {
            Point point = Position();
            return MousePointData(point);
        }

        private static Point MousePointData(Point point)
        {
            if (Application.Current.MainWindow != null)
            {
                var presentationSource = PresentationSource.FromVisual(Application.Current.MainWindow);
                if (presentationSource != null)
                {
                    if (presentationSource.CompositionTarget != null)
                    {
                        var transform = presentationSource.CompositionTarget.TransformFromDevice;
                        var mouse = transform.Transform(point);
                        return new Point(mouse.X, mouse.Y);
                    }
                }
            }

            return point;
        }

        internal static Point Position()
        {
            System.Drawing.Point point = System.Windows.Forms.Control.MousePosition;
            return new Point(point.X, point.Y);
        }
    }


}
