﻿using Everylang.App.Shortcut;
using Everylang.App.ViewModels;
using Everylang.Note.Callbacks;
using Everylang.Note.SettingsApp;
using NHotkey;

namespace Everylang.App.Miminote
{
    internal static class MiminoteManager
    {
        internal static void Start()
        {
            SettingsMiminoteManager.LoadSettings();
            if (SettingsMiminoteManager.AppSettings.AddNewShortcutIsEnabled)
            {
                if (SettingsMiminoteManager.AppSettings.AddNewShortcut != null)
                    ShortcutManager.RegisterShortcut(nameof(SettingsMiminoteManager.AppSettings.AddNewShortcut),
                        SettingsMiminoteManager.AppSettings.AddNewShortcut, AddNewNoteShortcut);
            }
        }

        internal static void Stop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsMiminoteManager.AppSettings.AddNewShortcut));
            VMContainer.Instance.NotesListControlViewModel.CloseMainNoteWindow();
            SettingsMiminoteManager.LiteDb.Dispose();
        }

        internal static void AddNewNoteShortcut(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            VMContainer.Instance.NotesListControlViewModel.ShowMainNoteWindow(true);
            CallBack.OnMiminoteCallBackAddNewNote();
        }
    }
}
