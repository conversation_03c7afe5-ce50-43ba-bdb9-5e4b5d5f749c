﻿using Everylang.App.SettingsApp;
using Everylang.App.Translator.NetRequest;
using Everylang.App.Utilities.NetRequest;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;

namespace Everylang.App.Translator.Google
{
    class GoogleTranslator
    {
        private RequestSettings _requestSettings = null!;
        private string? _languageFromCurrent;
        private string? _languageToCurrent;
        private string? _latinText;
        private bool _isSecont;

        internal WebResultTranslator? Translate(RequestSettings requestSettings)
        {
            _requestSettings = requestSettings;
            return Translate(false);
        }

        private WebResultTranslator? Translate(bool second)
        {
            try
            {
                WebResultTranslator? webResult = new WebResultTranslator();
                _isSecont = second;
                if (!second)
                {
                    _languageFromCurrent = _requestSettings.LanguageFromCurrent?.Abbreviation;
                    _languageToCurrent = _requestSettings.LanguageToCurrent?.Abbreviation;
                }

                RestClient restClient = new RestClient(new RestClientOptions()
                {
                    Proxy = NetLib.GetProxy(),
                    UserAgent = "AndroidTranslate/5.3.0.RC02.130475354-53000263 5.1 phone TRANSLATE_OPM5_TEST_1"
                });
                var req = new RestRequest(new Uri($"https://translate.google.com/translate_a/single?client=at&dt=t&dt=ld&dt=qca&dt=rm&dt=bd&dj=1&hl={CultureInfo.CurrentCulture.TwoLetterISOLanguageName.ToLower()}&ie=UTF-8&oe=UTF-8&inputm=2&otf=2&iid=1dd3b944-fa62-4b55-b330-74909a99969e"), Method.Post);
                req.AddParameter("application/x-www-form-urlencoded",
                    "sl=" + _languageFromCurrent + "&tl=" + _languageToCurrent + "&q=" +
                    WebUtility.UrlEncode(_requestSettings.SourceTextTrimmed), ParameterType.RequestBody);
                var result = restClient.Execute(req);

                if (result.IsSuccessful && result.Content?.Length > 0 && result.Content.Contains("\"trans\""))
                {
                    webResult = ProcessingOfTheRequest(result.Content);
                    webResult.LatinText = _latinText;
                }
                else
                {
                    webResult.WithError = true;
                    webResult.ResultText = result.ErrorMessage;
                    webResult.ResultTextWithNonChar = result.Content;
                }

                webResult.FromLang = _languageFromCurrent;
                webResult.ToLang = _languageToCurrent;
                return webResult;
            }
            catch (Exception e)
            {
                return new WebResultTranslator() { WithError = true, ErrorText = e.Message };
            }
        }

        WebResultTranslator? ProcessingOfTheRequest(string? resultHttp)
        {
            var webResult = new WebResultTranslator();
            try
            {
                var resultText = new List<string>();
                if (resultHttp != null)
                {
                    _latinText = "";
                    JObject json = JObject.Parse(resultHttp);

                    _languageFromCurrent = json.SelectToken("src")?.Value<string>();
                    if (!_isSecont)
                    {
                        if (_languageFromCurrent == _languageToCurrent ||
                            (resultText.Count > 0 && _requestSettings.SourceTextTrimmed.ToLower() == resultText[0]?.ToLower()))
                        {
                            if (!string.Equals(_languageToCurrent, SettingsManager.Settings.TranslateLangTo,
                                    StringComparison.InvariantCultureIgnoreCase) &&
                                SettingsManager.Settings.TranslateLangTo != _languageFromCurrent)
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                            }
                            else if (!string.Equals(_languageToCurrent, SettingsManager.Settings.TranslateLangFrom,
                                StringComparison.InvariantCultureIgnoreCase))
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangFrom;
                            }

                            if (_languageFromCurrent == _languageToCurrent &&
                                _languageToCurrent != SettingsManager.Settings.TranslateLangTo)
                            {
                                _languageToCurrent = SettingsManager.Settings.TranslateLangTo;
                            }
                            return Translate(true);
                        }
                    }
                    if (json.TryGetValue("sentences", out var sentencesToken))
                    {
                        if (sentencesToken.Any())
                        {
                            foreach (var jToken in sentencesToken)
                            {
                                resultText.Add(jToken.SelectToken("trans")?.Value<string>());
                            }
                        }
                        if (sentencesToken.Count() > 1 && sentencesToken[1] != null)
                        {
                            var translitToken = sentencesToken[1]?.SelectToken("translit");
                            if (translitToken == null)
                            {
                                translitToken = sentencesToken[1]?.SelectToken("src_translit");
                            }
                            if (translitToken != null)
                            {
                                _latinText = translitToken.Value<string>();
                            }

                        }
                    }


                    // дополнительные переводы
                    var traslateResultStructList = new List<TraslateResultStruct>();
                    if (json.TryGetValue("dict", out var termsToken))
                    {
                        var terms = termsToken.ToList();
                        foreach (var term in terms)
                        {
                            var traslateResultStruct = new TraslateResultStruct();
                            var termArr = term.ToList();
                            var jToken = termArr[0].First;
                            if (jToken != null)
                            {
                                traslateResultStruct.Pos = jToken.Value<string>();
                                traslateResultStruct.Terms = new List<TermsStruct>();
                                foreach (var item in termArr[2])
                                {
                                    var itemArr = item.ToList();
                                    foreach (var itemTerm in itemArr)
                                    {
                                        var termsStruct = new TermsStruct();
                                        termsStruct.ReverseTranslation = new List<string?>();
                                        termsStruct.Word = itemTerm.First?.First?.Value<string>();
                                        // значения дополнения
                                        foreach (var entryColl in itemTerm["reverse_translation"]!)
                                        {
                                            termsStruct.ReverseTranslation.Add(entryColl.Value<string>());
                                        }

                                        traslateResultStruct.Terms.Add(termsStruct);
                                    }
                                }
                            }

                            traslateResultStructList.Add(traslateResultStruct);
                        }
                    }

                    string result = "";
                    foreach (var item in resultText)
                    {
                        result += item;
                    }
                    webResult.ResultTextWithNonChar = _requestSettings.StartNonCharList + result + _requestSettings.EndNonCharList;
                    foreach (var traslateResultStruct in traslateResultStructList)
                    {
                        result = "<bold>" + result + "\n" + "\n";
                        if (traslateResultStruct.Pos == "")
                        {
                            result += "\n<underline>" + _requestSettings.SourceTextTrimmed + ":\n";
                        }
                        else
                        {
                            result += "\n<underline>" + traslateResultStruct.Pos + ":\n";
                        }
                        foreach (var item in traslateResultStruct.Terms)
                        {
                            result += "      " + item.Word + " (";
                            for (int index = 0; index < item.ReverseTranslation.Count; index++)
                            {
                                var text = item.ReverseTranslation[index];
                                if (index + 1 == item.ReverseTranslation.Count)
                                {
                                    result += text;
                                }
                                else
                                {
                                    result += text + ", ";
                                }
                            }
                            result += ")\n";
                        }
                    }

                    webResult.ResultText = result;
                    return webResult;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return webResult;
        }

        internal struct TraslateResultStruct
        {
            internal List<TermsStruct> Terms;
            internal string? Pos;
        }

        internal struct TermsStruct
        {
            internal List<string?> ReverseTranslation;
            internal string? Word;
        }

        internal static Stream? GetListening(string? text, string? lang)
        {
            var url = $"http://translate.google.com/translate_tts?ie=UTF-8&q={HttpUtility.UrlEncode(text)}&tl={lang}&client=tw-ob";
            var netLib = new NetLibTranslator(url, "", "http://translate.google.com/");
            return netLib.StartGetWebRequestGoogleTts();
        }
    }
}
