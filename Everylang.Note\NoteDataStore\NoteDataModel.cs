﻿using Everylang.Note.Helpers;
using Everylang.Note.SettingsApp;
using LiteDB;
using MaterialDesignColors;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Media;

namespace Everylang.Note.NoteDataStore
{
    [Serializable]
    public class NoteDataModel : INotifyPropertyChanged
    {
        private string? _text = "";
        private bool _isSelected;
        private string? _dateTimeLastEdit;
        private bool _isCheckList;
        private string? _color;
        private bool _isDeleted;
        private bool _isArchived;
        private string? _noteName = "";
        public ObjectId? Id { get; set; }

        public Action<string, ObjectId>? TextChangesAction { get; set; }

        public string? NoteName
        {
            get => _noteName;
            set
            {
                _noteName = value;
                OnPropertyChanged();
            }
        }

        public string? Text
        {
            get { return _text; }
            set
            {
                if (_text != null && _text == value) return;
                _text = value;
            }
        }

        public string? Color
        {
            get { return _color; }
            set
            {
                _color = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ColorBrushList));
            }
        }

        public SolidColorBrush[] ColorBrushList
        {
            get
            {
                var swatches = new SwatchesProvider().Swatches.ToList();
                var color = swatches.FirstOrDefault(x => _color != null && x.Name.ToLower() == _color.ToLower());
                if (color != null)
                {
                    return color.PrimaryHues.Select(x => new SolidColorBrush(x.Color)).ToArray();
                }
                return new SolidColorBrush[] { };
            }
        }

        public Brush OneColorBrush
        {
            get
            {
                return ColorBrushList[0];
            }
        }

        public Brush SecondColorBrush
        {
            get
            {
                return ColorBrushList[1];
            }
        }

        public int TextSize { get; set; }

        public bool IsDeleted
        {
            get => _isDeleted;
            set
            {
                _isDeleted = value;
                if (_isDeleted)
                {
                    DateTimeDeleted = DateTime.Now;
                    CloseForm();
                }
                else if (IsVisible)
                {
                    Show();
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsVisible));
            }

        }

        public bool IsArchived
        {
            get => _isArchived;
            set
            {
                _isArchived = value;
                if (_isArchived)
                {
                    CloseForm();
                }
                else if (IsVisible)
                {
                    Show();
                }
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsVisible));
            }
        }

        public bool IsVisible => !IsArchived && !IsDeleted;

        public bool IsLoaded { get; set; }

        public DateTime DateTimeDeleted { get; set; }

        public string Tags { get; set; } = "";

        public int LocationX { get; set; }

        public int LocationY { get; set; }

        public int Height { get; set; }

        public int Width { get; set; }

        public WindowNote? NoteForm { get; set; }

        public bool IsCallNewNote { get; set; }

        public bool IsCheckList
        {
            get { return _isCheckList; }
            set
            {
                _isCheckList = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CheckListDataModel>? CheckListCollection { get; set; }

        public NoteDataModel()
        {
            CheckListCollection = new ObservableCollection<CheckListDataModel>();
        }

        public string DateTimeLastEdit
        {
            get
            {
                if (_dateTimeLastEdit == null) _dateTimeLastEdit = DateTime.Now.ToString("g");
                return _dateTimeLastEdit;
            }
            set
            {
                _dateTimeLastEdit = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TextView));
            }
        }

        public bool IsSelected
        {
            get { return _isSelected; }
            set
            {
                if (_isSelected == value) return;
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public string TextView
        {
            get
            {
                if (IsCheckList)
                {
                    if (CheckListCollection != null && CheckListCollection.Count > 0)
                    {
                        var textresult = "";
                        foreach (var checkListDataModel in CheckListCollection)
                        {
                            if (checkListDataModel.IsDopItem)
                            {
                                continue;
                            }
                            textresult += Environment.NewLine;
                            if (checkListDataModel.IsSelectedItem)
                            {
                                textresult += "\u2713 " + checkListDataModel.Text;
                            }
                            else
                            {
                                textresult += "\u2610 " + checkListDataModel.Text;
                            }
                        }
                        return textresult.Trim();
                    }
                }
                var text = RtfText.StripRtf(Text);
                return text;
            }
        }

        public void Show()
        {
            if (IsLoaded && SettingsMiminoteManager.AppSettings.IsOnNotes)
            {
                if (NoteForm == null)
                {
                    NoteForm = new WindowNote(this);
                }
                NoteForm.Owner = SettingsMiminoteManager.OwnerWindow;
                NoteForm.Left = LocationX;
                NoteForm.Top = LocationY;
                NoteForm.Width = Width;
                NoteForm.Height = Height;
                NoteForm.Show();
            }
        }

        public void Hide()
        {
            if (NoteForm != null)
            {
                NoteForm.Hide();
            }
        }

        public void CloseForm()
        {
            if (NoteForm != null)
            {
                NoteForm.Close();
                NoteForm = null;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) handler(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
