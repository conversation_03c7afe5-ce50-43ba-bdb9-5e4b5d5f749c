﻿using Everylang.App.Diary;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.Diary
{
    /// <summary>
    /// Interaction logic for DiaryControl.xaml
    /// </summary>
    internal partial class DiaryControl : UserControl
    {
        internal DiaryControl()
        {
            InitializeComponent();
            PasswordBoxMu.Password = SettingsManager.Settings.DiaryPassword;
        }


        private void DiaryShowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("DiaryShortcuts"), SettingsManager.Settings.DiaryShowShortcut, nameof(SettingsManager.Settings.DiaryShowShortcut), DiaryManager.Instance.PressedDiaryView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.DiaryViewModel.DiaryShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SavePassword(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(PasswordBoxMu.Password))
            {
                SettingsManager.Settings.DiaryPassword = "";
            }
            else
            {
                SettingsManager.Settings.DiaryPassword = PasswordBoxMu.Password;
            }
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }
    }
}
