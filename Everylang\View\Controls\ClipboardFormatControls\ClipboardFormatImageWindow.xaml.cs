﻿using Everylang.App.Utilities;
using System;
using System.IO;
using System.Threading;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Threading;

namespace Everylang.App.View.Controls.ClipboardFormatControls
{
    internal partial class ClipboardFormatImageWindow
    {
        internal ClipboardFormatImageWindow(Stream? image)
        {
            InitializeComponent();
            if (image == null)
            {
                return;
            }
            var imageSource = new BitmapImage();
            imageSource.BeginInit();
            imageSource.CacheOption = BitmapCacheOption.OnLoad;
            imageSource.StreamSource = image;
            imageSource.EndInit();
            imageSource.Freeze();
            ImageMu.Source = imageSource;

        }

        private void me_Deactivated(object sender, EventArgs e)
        {
            if (IsVisible)
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Close);
            Application curApp = Application.Current;
            if (curApp.MainWindow != null) curApp.MainWindow.Activate();
        }

        private void ImageMu_Loaded(object sender, RoutedEventArgs e)
        {
            if (Application.Current.MainWindow != null)
            {
                var pos = WindowLocation.GetAbsolutePosition(Application.Current.MainWindow);
                this.Left = (Application.Current.MainWindow.ActualWidth - this.ActualWidth) / 2 + pos.X;
                this.Top = (Application.Current.MainWindow.ActualHeight - this.ActualHeight) / 2 + pos.Y;
            }

            if (this.Top < 1)
            {
                this.Top = 5;
            }
        }
    }


}
