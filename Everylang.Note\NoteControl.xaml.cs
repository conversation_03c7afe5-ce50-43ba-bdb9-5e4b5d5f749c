﻿using Everylang.Note.Callbacks;
using Everylang.Note.Helpers;
using Everylang.Note.NoteDataStore;
using Everylang.Note.SettingsApp;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;

namespace Everylang.Note
{
    /// <summary>
    /// Interaction logic for NoteControl.xaml
    /// </summary>
    internal partial class NoteControl : UserControl
    {
        internal bool IsNoteSelected
        {
            get
            {
                return NoteDataModelObj != null && NoteDataModelObj.IsCheckList;
            }
            set
            {
                if (NoteDataModelObj != null) NoteDataModelObj.IsCheckList = value;
            }

        }

        internal NoteDataModel NoteDataModelObj;
        internal string? ColorNote;
        private bool _checkListIsChanged;

        internal new event Action<Size> SizeChanged;

        public ObservableCollection<CheckListDataModel>? CheckListCollection { get; set; }

        public NoteControl()
        {
            InitializeComponent();
            DataContext = this;
        }

        internal void SetData(NoteDataModel noteDataModel)
        {
            if (noteDataModel != null)
            {
                CheckListCollection = noteDataModel.CheckListCollection;
                NoteDataModelObj = noteDataModel;
            }

        }

        internal void LoadNoteData()
        {
            if (SettingsMiminoteManager.AppSettings.FontDefault != null)
                ResTextBox.FontFamily = new FontFamily(SettingsMiminoteManager.AppSettings.FontDefault);
            ResTextBox.FontSize = SettingsMiminoteManager.AppSettings.SizeFontDefault;
            SetRtf(NoteDataModelObj?.Text);

            if (NoteDataModelObj != null)
            {
                ColorNote = NoteDataModelObj.Color;
                ResTextBox.FontSize = NoteDataModelObj.TextSize;
                if (NoteDataModelObj.IsCheckList && CheckListCollection != null)
                {
                    var firstChecked = CheckListCollection.FirstOrDefault(x => x.IsSelectedItem);
                    if (firstChecked != null)
                    {
                        CheckListCollection.Insert(CheckListCollection.IndexOf(firstChecked),
                            new CheckListDataModel() { Text = "", IsSelectedItem = false, IsDopItem = true });
                    }
                    else
                    {
                        CheckListCollection.Add(new CheckListDataModel()
                        { Text = "", IsSelectedItem = false, IsDopItem = true });
                    }

                    ResTextBox.Visibility = Visibility.Collapsed;
                    StackPanelCheckList.Visibility = Visibility.Visible;
                }
            }
        }

        internal void Close()
        {
            if (NoteDataModelObj != null) CallBack.OnMiminoteCallBackCloseNote(NoteDataModelObj);
        }

        internal void ToArchiveNote()
        {
            if (NoteDataModelObj != null) CallBack.OnMiminoteCallBackToArchiveNote(NoteDataModelObj);
        }

        internal void Save(int left, int top, int width, int height, string? titleText)
        {
            var rtfText = GetRtf();
            if (NoteDataModelObj.Text != rtfText ||
                Math.Abs(NoteDataModelObj.TextSize - ResTextBox.FontSize) > 0.1 ||
                Math.Abs(NoteDataModelObj.LocationX - left) > 0.1 ||
                Math.Abs(NoteDataModelObj.LocationY - top) > 0.1 ||
                Math.Abs(NoteDataModelObj.Height - height) > 0.1 ||
                Math.Abs(NoteDataModelObj.Width - width) > 0.1 ||
                NoteDataModelObj.Color != ColorNote ||
                _checkListIsChanged ||
                titleText != NoteDataModelObj.NoteName
               )
            {
                var rtfTextChanged = NoteDataModelObj?.Text != rtfText;
                if (NoteDataModelObj != null)
                {
                    NoteDataModelObj.Text = rtfText;
                    NoteDataModelObj.NoteName = titleText;
                    NoteDataModelObj.TextSize = (int)ResTextBox.FontSize;
                    NoteDataModelObj.LocationX = left;
                    NoteDataModelObj.LocationY = top;
                    NoteDataModelObj.Height = height;
                    NoteDataModelObj.Width = width;
                    NoteDataModelObj.Color = ColorNote;
                    NoteDataModelObj.CheckListCollection = CheckListCollection;
                    if (_checkListIsChanged || rtfTextChanged)
                    {
                        NoteDataModelObj.DateTimeLastEdit = DateTime.Now.ToString("g");
                    }
                    NoteDataManager.UpdateData(NoteDataModelObj);
                }

                _checkListIsChanged = false;
            }
        }

        private void TextBox_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            var selectedItem = ObjectByTypes.GetObjectAtObject<ListBoxItem>(CheckListControl, (DependencyObject)sender) as CheckListDataModel;
            if (selectedItem == null)
            {
                return;
            }
            if (e.Key == Key.Enter && (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl)))
            {
                var textBox = ((TextBox)sender);
                textBox.Text += Environment.NewLine;
                textBox.CaretIndex = textBox.Text.Length;
                return;
            }
            if (e.Key == Key.Enter)
            {
                if (!selectedItem.IsSelectedItem)
                {
                    if (CheckListCollection != null)
                    {
                        var index = CheckListCollection.IndexOf(selectedItem);
                        CheckListCollection.Insert(index + 1, new CheckListDataModel() { Text = "", IsSelectedItem = false, IdNote = NoteDataModelObj?.Id });
                        CheckListControl.SelectedIndex = index + 1;
                    }

                    CheckListControl.UpdateLayout();
                    CheckListControl.ScrollIntoView(CheckListControl.SelectedItem);
                }
            }
            if (e.Key == Key.Down)
            {
                if (CheckListCollection != null)
                {
                    var index = CheckListCollection.IndexOf(selectedItem);
                    CheckListControl.SelectedIndex = index + 1;
                }

                CheckListControl.UpdateLayout();
                CheckListControl.ScrollIntoView(CheckListControl.SelectedItem);
            }
            if (e.Key == Key.Up)
            {
                if (CheckListCollection != null)
                {
                    var index = CheckListCollection.IndexOf(selectedItem);
                    CheckListControl.SelectedIndex = index - 1;
                }

                CheckListControl.UpdateLayout();
                CheckListControl.ScrollIntoView(CheckListControl.SelectedItem);
            }
            _checkListIsChanged = true;
        }

        private async void CheckListControl_OnDrop(object sender, DragEventArgs e)
        {
            await Task.Delay(150);
            if (CheckListCollection != null)
            {
                var unSelectedList = CheckListCollection.Where(x => !x.IsSelectedItem && !x.IsDopItem).ToArray();
                var selectedList = CheckListCollection.Where(x => x.IsSelectedItem && !x.IsDopItem).ToArray();
                var dop = CheckListCollection.First(x => x.IsDopItem);
                CheckListCollection.Clear();
                foreach (CheckListDataModel b in unSelectedList)
                {
                    CheckListCollection.Add(b);
                }
                CheckListCollection.Add(dop);
                foreach (CheckListDataModel b in selectedList)
                {
                    CheckListCollection.Add(b);
                }
            }

            UpdateLayout();
            _checkListIsChanged = true;
        }

        internal void ToListOrNote()
        {
            if (StackPanelCheckList.Visibility == Visibility.Collapsed)
            {
                CheckListCollection?.Clear();
                var textList = GetText().Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var s in textList)
                {
                    if (CheckListCollection != null && CheckListCollection.FirstOrDefault(x => x.Text == s) == null)
                    {
                        CheckListCollection.Add(new CheckListDataModel() { Text = s, IsSelectedItem = false, IdNote = NoteDataModelObj?.Id });
                    }
                }
                CheckListCollection?.Add(new CheckListDataModel() { Text = "", IsSelectedItem = false, IsDopItem = true });
                ResTextBox.Visibility = Visibility.Collapsed;
                StackPanelCheckList.Visibility = Visibility.Visible;
                if (NoteDataModelObj != null) NoteDataModelObj.IsCheckList = true;
                ResTextBox.Document.Blocks.Clear();
                _checkListIsChanged = true;
            }
            else
            {
                ResTextBox.Visibility = Visibility.Visible;
                StackPanelCheckList.Visibility = Visibility.Collapsed;
                FlowDocument myFlowDocument = new FlowDocument();

                if (CheckListCollection != null)
                {
                    foreach (CheckListDataModel t in CheckListCollection)
                    {
                        if (!t.IsDopItem)
                        {
                            Paragraph myParagraph = new Paragraph();
                            var checkListDataModel = t;
                            myParagraph.Inlines.Add(new Run(checkListDataModel.Text));
                            myFlowDocument.Blocks.Add(myParagraph);
                        }
                    }

                    CheckListCollection.Clear();
                }

                ResTextBox.Document = myFlowDocument;
                if (NoteDataModelObj != null) NoteDataModelObj.IsCheckList = false;
                _checkListIsChanged = false;
            }

        }

        private async void DeleteSelected(object sender, RoutedEventArgs e)
        {
            await PutTaskDelay();
            if (ObjectByTypes.GetObjectAtObject<ListBoxItem>(CheckListControl, (DependencyObject)sender) is CheckListDataModel selectedItem)
            {
                CheckListCollection?.Remove(selectedItem);
                UpdateLayout();
                _checkListIsChanged = true;

            }
        }

        private void PasteWithoutFormatting(object sender, ExecutedRoutedEventArgs e)
        {
            if (!ResTextBox.Selection.IsEmpty)
            {
                var documentBytes = Encoding.UTF8.GetBytes(Clipboard.GetText());
                using var reader = new MemoryStream(documentBytes);
                reader.Position = 0;
                ResTextBox.Selection.Load(reader, DataFormats.Rtf);
            }
            else
            {
                ResTextBox.CaretPosition = ResTextBox.CaretPosition.GetPositionAtOffset(0, LogicalDirection.Forward);
                if (ResTextBox.CaretPosition != null) ResTextBox.CaretPosition.InsertTextInRun(Clipboard.GetText());
            }
        }

        private void CommandBinding_OnCanExecute(object sender, CanExecuteRoutedEventArgs e)
        {
            e.CanExecute = Clipboard.ContainsText();
        }

        async Task PutTaskDelay()
        {
            await Task.Delay(150);
        }

        private async void ToggleButton_OnChecked(object sender, RoutedEventArgs e)
        {
            try
            {
                await Task.Delay(100);
                if (ObjectByTypes.GetObjectAtObject<ListBoxItem>(CheckListControl, (DependencyObject)sender) is CheckListDataModel selectedItem)
                {
                    if (CheckListCollection != null)
                    {
                        int isDopIndex = CheckListCollection.IndexOf(CheckListCollection.First(x => x.IsDopItem));
                        CheckListCollection.Remove(selectedItem);
                        CheckListCollection.Insert(isDopIndex, selectedItem);
                        if (selectedItem.IsSelectedItem)
                        {
                            CheckListControl.SelectedItem = CheckListCollection[isDopIndex];
                        }
                    }

                    CheckListControl.UpdateLayout();
                    CheckListControl.ScrollIntoView(CheckListControl.SelectedItem);
                    _checkListIsChanged = true;
                }
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }
        }

        private void TextBoxBase_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            var textBox = (TextBox)sender;
            if (!string.IsNullOrEmpty(textBox.Text))
            {
                var text = textBox.Text;
                textBox.Text = "";
                if (CheckListCollection != null)
                {
                    int isDopIndex = CheckListCollection.IndexOf(CheckListCollection.First(x => x.IsDopItem));
                    CheckListCollection.Insert(isDopIndex, new CheckListDataModel() { Text = text, IsSelectedItem = false, IdNote = NoteDataModelObj.Id });
                    CheckListControl.SelectedItem = CheckListCollection[isDopIndex];
                }

                CheckListControl.UpdateLayout();
                CheckListControl.ScrollIntoView(CheckListControl.SelectedItem);
                _checkListIsChanged = true;
            }
        }

        private void Testing_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = ((TextBox)sender);
            textBox.CaretIndex = textBox.Text.Length;
        }

        private TextPointer? _pointerStart;
        private TextPointer? _pointerEnd;

        private void ResTextBox_OnLostKeyboardFocus(object sender, KeyboardFocusChangedEventArgs e)
        {
            _pointerStart = ResTextBox.Selection.Start;
            _pointerEnd = ResTextBox.Selection.End;
        }

        private void ResTextBox_OnGotKeyboardFocus(object sender, KeyboardFocusChangedEventArgs e)
        {
            try
            {
                if (_pointerStart != null)
                    if (_pointerEnd != null)
                        ResTextBox.Selection.Select(_pointerStart, _pointerEnd);
            }
            catch
            {
                // ignore
            }
        }

        private string GetRtf()
        {
            TextRange tr = new TextRange(ResTextBox.Document.ContentStart, ResTextBox.Document.ContentEnd);
            using var ms = new MemoryStream();
            tr.Save(ms, DataFormats.Rtf);
            return Encoding.UTF8.GetString(ms.ToArray());
        }

        private string GetText()
        {
            TextRange tr = new TextRange(ResTextBox.Document.ContentStart, ResTextBox.Document.ContentEnd);
            using var ms = new MemoryStream();
            tr.Save(ms, DataFormats.Text);
            return Encoding.UTF8.GetString(ms.ToArray());
        }

        private void SetRtf(string? document)
        {
            if (!string.IsNullOrEmpty(document))
            {
                var documentBytes = Encoding.UTF8.GetBytes(document);
                using var reader = new MemoryStream(documentBytes);
                reader.Position = 0;
                ResTextBox.SelectAll();
                ResTextBox.Selection.Load(reader, DataFormats.Rtf);
                ResTextBox.CaretPosition = ResTextBox.Document.ContentStart;
            }
        }

        private void ContextMenu_OnOpened(object sender, RoutedEventArgs e)
        {
            ItemPasteWf.IsEnabled = Clipboard.ContainsText();
        }

        private void NoteControl_OnPreviewKeyUp(object sender, KeyEventArgs e)
        {
            UpdateLayout();
            Size size = new Size(250, 300);
            if (NoteDataModelObj != null && NoteDataModelObj.IsCheckList)
            {
                if (e.Key == Key.Enter)
                {
                    size.Width = 0;
                    size.Height = CheckListControl.ActualHeight + 70;
                    SizeChanged?.Invoke(size);
                }
            }
            else
            {
                var formattedText = ResTextBox.Document.GetFormattedText(this);
                if (formattedText != null)
                {
                    size.Width = formattedText.WidthIncludingTrailingWhitespace;
                    if (size.Width.Equals(_lastSize.Width))
                    {
                        size.Width = 0;
                    }
                    else
                    {
                        size.Width += 50;
                    }

                    size.Height = 0;
                    if (e.Key == Key.Enter)
                    {
                        size.Height = ResTextBox.ActualHeight + 50;
                    }

                    if (e.Key == Key.Delete)
                    {
                        size.Height = ResTextBox.ViewportHeight + 50;
                    }
                    SizeChanged?.Invoke(size);
                }
            }
        }

        private Size _lastSize;

        private void NoteControl_OnPreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (NoteDataModelObj != null && !NoteDataModelObj.IsCheckList)
            {
                var formattedText = ResTextBox.Document.GetFormattedText(this);
                if (formattedText != null)
                {
                    _lastSize = new Size(formattedText.WidthIncludingTrailingWhitespace, formattedText.Height);
                }
            }
        }
    }

    public class NullGroupStyleSelector : StyleSelector
    {
        public Style? NullGroupStyle { get; set; }
        public Style? DefaultStyle { get; set; }

        public override Style? SelectStyle(object item, DependencyObject container)
        {
            if (container is FrameworkElement)
            {
                var group = item as CollectionViewGroup;
                if (group == null || group.Name == null || group.Name is bool value && !value)
                {
                    return NullGroupStyle;
                }
            }

            return DefaultStyle;
        }
    }
}
