﻿using Everylang.App.SwitcherLang;
using Everylang.Common.LogManager;
using PlatformSpellCheck;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Everylang.App.SpellCheck
{
    static class SpellCheckWhileTyping
    {
        internal static List<string> Suggestions;
        private static List<string> _lastSuggestions;
        private static string? _lastText;
        static Dictionary<string?, SpellChecker> _spellCheckers;

        internal static void Init()
        {
            if (SpellChecker.IsPlatformSupported())
            {
                _spellCheckers = new Dictionary<string?, SpellChecker>();
                try
                {
                    foreach (CultureInfo? lang in KeyboardLayoutMethods.GetInputLangs())
                    {
                        if (SpellChecker.IsLanguageSupported(lang.Name))
                        {
                            var spelling = new SpellChecker(lang.Name);
                            if (!_spellCheckers.ContainsKey(lang.TwoLetterISOLanguageName))
                            {
                                _spellCheckers.Add(lang.TwoLetterISOLanguageName, spelling);
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            }
            Suggestions = new List<string>();
        }

        internal static bool CheckLast(string? text)
        {
            if (_lastSuggestions != null && _lastSuggestions.Any() && text.Equals(_lastText))
            {
                Suggestions.Clear();
                Suggestions.AddRange(_lastSuggestions);
                return true;
            }
            return false;
        }

        internal static void SaveLast(string? text)
        {
            _lastSuggestions = new List<string>(Suggestions);
            _lastText = text;
        }

        internal static void CheckWord(string? text, string? locale)
        {
            Task.Run(() =>
            {
                try
                {
                    Suggestions.Clear();
                    if (SpellChecker.IsPlatformSupported() && _spellCheckers.Count > 0)
                    {
                        if (_spellCheckers.ContainsKey(locale))
                        {
                            var spelling = _spellCheckers[locale];
                            if (spelling.Check(text).Any())
                            {
                                foreach (var word in spelling.Suggestions(text))
                                {
                                    Suggestions.Add(word);
                                }
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    Logger.LogTo.Error(e, e.Message);
                }
            });
        }

        internal static List<string> CheckWordForSmallSpellCheck(string? text)
        {
            var result = new List<string>();
            try
            {

                if (SpellChecker.IsPlatformSupported() && _spellCheckers.Count > 0)
                {
                    foreach (var keyValuePair in _spellCheckers)
                    {
                        var spelling = keyValuePair.Value;
                        if (spelling.Check(text).Any())
                        {
                            foreach (var word in spelling.Suggestions(text))
                            {
                                result.Add(word);
                            }
                            if (result.Any())
                            {
                                return result;
                            }

                        }
                    }
                }

            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return result;

        }

        internal static void AddWord(string text)
        {
            Task.Run(() =>
            {
                var locale = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(IntPtr.Zero);
                if (_spellCheckers.ContainsKey(locale))
                {
                    var spelling = _spellCheckers[locale];
                    spelling.Add(text);
                }
            });
        }

        internal static void AddAutoCorrect(string from, string to)
        {
            Task.Run(() =>
            {
                var locale = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(IntPtr.Zero);
                if (_spellCheckers.ContainsKey(locale))
                {
                    var spelling = _spellCheckers[locale];
                    spelling.AutoCorrect(from, to);
                }
            });
        }


    }
}
