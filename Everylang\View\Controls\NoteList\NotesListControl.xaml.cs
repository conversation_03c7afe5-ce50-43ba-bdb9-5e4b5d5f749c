﻿using Everylang.Common.Utilities;
using Everylang.Note.Helpers;
using Everylang.Note.NoteDataStore;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Everylang.App.View.Controls.NoteList
{
    internal partial class NotesListControl : UserControl
    {
        public NotesListControl()
        {
            InitializeComponent();
        }

        private async void CardNoteMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {

            if (ObjectByTypes.GetObjectAtObject<ContentPresenter>(ItemsControlList, (DependencyObject)sender) is NoteDataModel selectedItem)
            {
                if (selectedItem.NoteForm != null && selectedItem.IsVisible)
                {
                    ForegroundWindow.StoreForegroundWindow();
                    try
                    {
                        selectedItem.NoteForm.Show();
                        await Task.Delay(100);
                        selectedItem.NoteForm.Hide();
                        await Task.Delay(100);
                        selectedItem.NoteForm.Show();
                    }
                    catch
                    {
                        // ignore
                    }
                    ForegroundWindow.RestoreForegroundWindow();
                }
            }
        }
    }
}
