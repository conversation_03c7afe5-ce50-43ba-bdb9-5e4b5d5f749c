﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Telerik.Windows;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;
using Vanara.PInvoke;
using Button = System.Windows.Controls.Button;

namespace Everylang.App.View.Controls.Snippets
{
    /// <summary>
    /// Interaction logic for FastActionTextWindow.xaml
    /// </summary>
    internal partial class SnippetsActionWindow : INotifyPropertyChanged
    {
        private string? _selectedText;


        internal static readonly DependencyProperty CopyTextProperty =
            DependencyProperty.Register("CopyText",
                typeof(string),
                typeof(SnippetsActionWindow),
                new FrameworkPropertyMetadata(""));

        internal string CopyText
        {
            get { return (string)GetValue(CopyTextProperty); }
            set { SetValue(CopyTextProperty, value); }
        }

        internal static readonly DependencyProperty PasteTextProperty =
             DependencyProperty.Register("PasteText",
                 typeof(string),
                 typeof(SnippetsActionWindow),
                 new FrameworkPropertyMetadata(""));

        internal string PasteText
        {
            get { return (string)GetValue(PasteTextProperty); }
            set { SetValue(PasteTextProperty, value); }
        }

        internal static readonly DependencyProperty IsStayOnTopProperty =
            DependencyProperty.Register("IsStayOnTopVisible",
                typeof(bool),
                typeof(SnippetsActionWindow),
                new FrameworkPropertyMetadata());

        internal bool IsStayOnTop
        {
            get { return (bool)GetValue(IsStayOnTopProperty); }
            set
            {
                SetValue(IsStayOnTopProperty, value);
            }
        }

        internal static readonly DependencyProperty FastActionIndexProperty =
            DependencyProperty.Register("FastActionIndex",
                typeof(string),
                typeof(SnippetsActionWindow),
                new FrameworkPropertyMetadata(""));

        internal string FastActionIndex
        {
            get { return (string)GetValue(FastActionIndexProperty); }
            set { SetValue(FastActionIndexProperty, value); }
        }

        internal SnippetsActionWindow()
        {
            InitializeComponent();
            MouseDown += OnMouseDown;
            ThumbMy.DragDelta += OnDragDelta;
            Opened += OnOpened;

            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            HookCallBackMouseWheel.CallbackEventHandler += HookManagerMouseWheel;
            _selectedText = null;
            DataContext = this;
            CopyText = LocalizationManager.GetString("bCopy");
            PasteText = LocalizationManager.GetString("PastButton");
            FastActionIndex = LocalizationManager.GetString("FastActionIndex");

            var tagList = new Dictionary<string, int>();
            foreach (var autochangeDataModel in VMContainer.Instance.SnippetsViewModel.SnippetsList)
            {
                if (autochangeDataModel != null && autochangeDataModel.Tags != null)
                {
                    var tags = autochangeDataModel.Tags.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    foreach (var tag in tags)
                    {
                        if (!tagList.ContainsKey(tag))
                        {
                            tagList.Add(tag, autochangeDataModel.CountUsage);
                        }
                        else
                        {
                            tagList[tag] += autochangeDataModel.CountUsage;
                        }
                    }
                }
            }
            var ordered = tagList.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            List<string> keyList = new List<string>(ordered.Keys);
            keyList.Insert(0, LocalizationManager.GetString("All"));
            LvTags.ItemsSource = keyList.Select(e => new ItemsSourceStruct { Index = keyList.IndexOf(e), Obj = e });
            LvTags.SelectedIndex = 0;
            _currentListBox = LvSnippets;

            if (!string.IsNullOrEmpty(SettingsManager.Settings.SnippetsActionWindowSize))
            {
                var strSize = SettingsManager.Settings.SnippetsActionWindowSize.Split('|');
                if (strSize.Length > 1)
                {
                    this.Height = Convert.ToDouble(strSize[0]);
                    this.Width = Convert.ToDouble(strSize[1]);
                }
            }
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;

                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void MouseOverHide(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                if (LvSnippets.ContextMenu != null && !LvSnippets.ContextMenu.IsOpen)
                {
                    CloseWin();
                }
            }
        }


        #region Перемещение формы и изменение размера

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {
            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;
        }

        private void OnMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                ThumbMy.RaiseEvent(e);
            }
        }

        private void OnDragDeltaVertical(object sender, DragDeltaEventArgs e)
        {
            Height = Math.Min(MaxHeight, Math.Max(Height + e.VerticalChange, MinHeight));
        }

        private void OnDragDeltaHorisontal(object sender, DragDeltaEventArgs e)
        {
            Width = Math.Min(MaxWidth, Math.Max(Width + e.HorizontalChange, MinWidth));
        }


        private void OnDragDeltaAll(object sender, DragDeltaEventArgs e)
        {
            Width = Math.Min(MaxWidth, Math.Max(Width + e.HorizontalChange, MinWidth));
            Height = Math.Min(MaxHeight, Math.Max(Height + e.VerticalChange, MinHeight));
        }

        DateTime _lastMouseDown = DateTime.MinValue;

        private void Border_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if ((DateTime.Now - _lastMouseDown).TotalMilliseconds < 300)
            {
                if (Math.Abs(this.Width - this.MaxWidth) < 5)
                {
                    this.Width = this.MinWidth;
                    this.Height = this.MinHeight;
                }
                else
                {
                    this.Width = this.MaxWidth;
                    this.Height = this.MaxHeight;
                }
                _lastMouseDown = DateTime.MinValue;
            }
            _lastMouseDown = DateTime.Now;
        }

        #endregion


        private void HookManagerMouseWheel(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!IsMouseOver)
            {
                return;
            }
            Decorator? border = null;
            if (LvSnippets.IsMouseOver)
            {
                border = VisualTreeHelper.GetChild(LvSnippets, 0) as Decorator;
            }
            if (LvTags.IsMouseOver)
            {
                border = VisualTreeHelper.GetChild(LvTags, 0) as Decorator;
            }
            if (border != null)
            {
                var mouse = InputManager.Current.PrimaryMouseDevice;
                var args = new MouseWheelEventArgs(mouse, Environment.TickCount, (int)e.wheelRotation);
                args.RoutedEvent = MouseWheelEvent;
                if (border.Child is ScrollViewer scrollViewer) scrollViewer.RaiseEvent(args);
                e.Handled = true;
            }
        }

        private RadListBox _currentListBox;

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (TextBoxSearch.IsFocused)
            {
                if (e.KeyCode == VirtualKeycodes.Backspace && TextBoxSearch.Text != "")
                {
                    e.Handled = true;
                    TextBoxSearch.Text = TextBoxSearch.Text.Remove(TextBoxSearch.Text.Length - 1);
                    return;
                }
                string c = KeyboardLayoutMethods.CodeToString(e);
                if (!string.IsNullOrEmpty(c) && (!char.IsSurrogate(c[0]) && !char.IsControl(c[0])))
                {
                    e.Handled = true;
                    TextBoxSearch.Text += c;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.Esc)
                {
                    e.Handled = true;
                    TextBoxSearch.Text = "";
                    LvSnippets.Focus();
                    return;
                }

                if (e.KeyCode == VirtualKeycodes.Tab)
                {
                    e.Handled = true;
                    LvSnippets.Focus();
                    return;
                }
            }
            if (e.KeyCode == VirtualKeycodes.Tab)
            {
                TextBoxSearch.Focus();
                e.Handled = true;
                return;
            }

            if (IsStayOnTop || LvSnippets.Items.Count == 0)
            {
                return;
            }

            if (e.KeyCode == VirtualKeycodes.RightArrow && Equals(_currentListBox, LvSnippets))
            {
                e.Handled = true;
                _currentListBox = LvTags;
                _currentListBox.BorderBrush = (Brush)FindResource("HighlightBrush");
                LvSnippets.BorderBrush = Brushes.Transparent;
            }
            if (e.KeyCode == VirtualKeycodes.LeftArrow && Equals(_currentListBox, LvTags))
            {
                e.Handled = true;
                _currentListBox = LvSnippets;
                _currentListBox.BorderBrush = (Brush)FindResource("HighlightBrush");
                LvTags.BorderBrush = Brushes.Transparent;
            }

            if (e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {

                if (_currentListBox.SelectedIndex == -1)
                {
                    _currentListBox.SelectedIndex = 0;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.UpArrow)
                {
                    if (_currentListBox.SelectedIndex == 0)
                    {
                        _currentListBox.SelectedIndex = _currentListBox.Items.Count - 1;
                    }
                    else
                    {
                        _currentListBox.SelectedIndex -= 1;
                    }

                }
                if (e.KeyCode == VirtualKeycodes.DownArrow)
                {
                    if (_currentListBox.SelectedIndex == _currentListBox.Items.Count - 1)
                    {
                        _currentListBox.SelectedIndex = 0;
                    }
                    else
                    {
                        _currentListBox.SelectedIndex += 1;
                    }
                }
                _currentListBox.UpdateLayout();
                if (_currentListBox.SelectedItems.Count > _currentListBox.SelectedItems.Count - 1)
                    _currentListBox.ScrollIntoView(_currentListBox.SelectedItems[_currentListBox.SelectedItems.Count - 1]);
                _currentListBox.UpdateLayout();
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                e.Handled = true;
                CloseWin();
            }
            if (e.KeyCode == VirtualKeycodes.Enter && Equals(_currentListBox, LvSnippets) && _currentListBox.SelectedIndex != -1)
            {
                if (!_replacing)
                {
                    e.Handled = true;
                    Replace();
                }
            }
            int i;
            if (int.TryParse(KeyboardLayoutMethods.CodeToString(e), out i))
            {
                if (i > 0 && i < 10)
                {
                    _currentListBox.SelectedIndex = i - 1;
                    e.Handled = true;
                    if (Equals(_currentListBox, LvSnippets))
                    {
                        Replace();
                    }
                }
            }
            if (e.KeyCode == VirtualKeycodes.C && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();

            }

            if (e.KeyCode == VirtualKeycodes.Insert && e.Control != ModifierKeySide.None)
            {
                e.Handled = true;
                Copy();

            }
        }

        private void CloseWin()
        {
            if (!IsStayOnTop)
            {
                HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
                HookCallBackMouseDown.CallbackEventHandler -= MouseOverHide;
                HookCallBackMouseWheel.CallbackEventHandler -= HookManagerMouseWheel;
                MouseDown -= OnMouseDown;
                ThumbMy.DragDelta -= OnDragDelta;
                if (LvSnippets.ContextMenu != null) LvSnippets.ContextMenu.IsOpen = false;
                SettingsManager.Settings.SnippetsActionWindowSize = this.Height.ToString(CultureInfo.InvariantCulture) + "|" + this.Width.ToString(CultureInfo.InvariantCulture);
                IsOpen = false;
            }
        }

        internal struct ItemsSourceStruct
        {
            internal int Index { get; set; }
            internal object Obj { get; set; }
        }
        private void lvFastAction_Bind(RadObservableCollection<SnippetsDataModel?> list)
        {

            var snippetList = new List<SnippetsDataModel>();
            var selectedItems = LvTags.SelectedItems.Cast<ItemsSourceStruct>().ToList();
            if (LvTags.SelectedIndex == 0)
            {
                var autochangeDataModels = list.OrderByDescending(x => x.CountUsage).ToList();
                LvSnippets.ItemsSource = autochangeDataModels.Select(e => new ItemsSourceStruct { Index = autochangeDataModels.IndexOf(e), Obj = e });
            }
            else
            {
                foreach (var selectedItem in selectedItems)
                {
                    foreach (var snippet in VMContainer.Instance.SnippetsViewModel.SnippetsList)
                    {
                        if (!string.IsNullOrEmpty(snippet?.Tags) && (snippet.Tags.Contains(selectedItem.Obj.ToString()) && !snippetList.Contains(snippet)))
                        {
                            snippetList.Add(snippet);
                        }
                    }
                }
                var autochangeDataModels = snippetList.OrderByDescending(x => x.CountUsage).ToList();
                LvSnippets.ItemsSource = autochangeDataModels.Select(e => new ItemsSourceStruct { Index = autochangeDataModels.IndexOf(e), Obj = e });
            }
        }



        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            CloseWin();
        }

        private void ClickSetStayOnTop(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = !IsStayOnTop;
            OnPropertyChanged(nameof(IsStayOnTop));
        }
        private void ClickOpenSettings(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            GlobalEventsApp.OnEventOpenSnippetsSettings();
            CloseWin();
        }

        private void ClickOpenListSnippets(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            GlobalEventsApp.OnEventOpenSnippetsList();
            CloseWin();
        }

        private void Copy()
        {
            if (LvSnippets.SelectedItems.Count > 0)
            {
                if (_selectedText != null && !string.IsNullOrEmpty(_selectedText))
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedText);
                    CloseWin();
                    return;
                }
                var copyText = "";
                for (int i = 0; i < LvSnippets.SelectedItems.Count; i++)
                {
                    SnippetsDataModel dataModel = ((SnippetsDataModel)((ItemsSourceStruct)LvSnippets.SelectedItems[i]).Obj);
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        dataModel.CountUsage++;
                    }
                    SnippetsManager.UpdateData(dataModel);
                    copyText += dataModel.Text;
                    if (i != LvSnippets.SelectedItems.Count - 1)
                    {
                        copyText += Environment.NewLine;
                    }
                }
                ClipboardOperations.SetTextWithoutHistory(copyText);
                CloseWin();
            }
        }

        private void LvSnippets_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            Replace();
        }

        private bool _replacing;

        private void Replace()
        {
            _replacing = true;
            if (LvSnippets.SelectedItems != null && LvSnippets.SelectedItems.Count > 0)
            {
                string replacedText = "";
                string langName = "";
                for (int i = 0; i < LvSnippets.SelectedItems.Count; i++)
                {
                    SnippetsDataModel dataModel = ((SnippetsDataModel)((ItemsSourceStruct)LvSnippets.SelectedItems[i]).Obj);
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        dataModel.CountUsage++;
                    }
                    SnippetsManager.UpdateData(dataModel);
                    replacedText += dataModel.Text;
                    if (i != LvSnippets.SelectedItems.Count - 1)
                    {
                        replacedText += Environment.NewLine;
                    }

                    langName = dataModel.LangToSwitch;
                }
                CloseWin();
                SendText.SendStringByPaste(replacedText, false, false);
                if (!string.IsNullOrEmpty(langName))
                {
                    var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[langName.ToLower()]];
                    KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
                }
            }
            _replacing = false;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void LvTags_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (LvTags.SelectedItem != null)
            {
                lvFastAction_Bind(VMContainer.Instance.SnippetsViewModel.SnippetsList);
            }
        }


        private void ListBoxItemLvSnippets_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element)
                {
                    var item = GetSelectedListBoxItem(element);
                    if (item != null)
                    {
                        if ((Keyboard.Modifiers & ModifierKeys.Shift) != 0)
                        {
                            try
                            {
                                object ob = (ItemsSourceStruct)item.DataContext;
                                var mouseIndex = LvSnippets.Items.IndexOf(ob);
                                var minIndex = LvSnippets.SelectedIndex < mouseIndex
                                    ? LvSnippets.SelectedIndex
                                    : mouseIndex;

                                var allWillSelecte = Math.Abs(LvSnippets.SelectedIndex - mouseIndex);
                                for (int i = 0; i < allWillSelecte + 1; i++)
                                {
                                    LvSnippets.SelectedItems.Add(LvSnippets.Items[i + minIndex]);
                                }
                            }
                            catch
                            {
                            }
                        }
                        else if ((Keyboard.Modifiers & ModifierKeys.Control) != 0)
                        {
                            LvSnippets.SelectedItems.Add(item.DataContext);

                        }
                        else
                        {
                            LvSnippets.SelectedItem = item.DataContext;
                        }

                        if (GetSelectedButtonItem() is Button buttonListBoxItem)
                        {
                            if (buttonListBoxItem.Name == "ButtonReplace")
                            {
                                Replace();
                            }

                            if (buttonListBoxItem.Name == "ButtonCopy")
                            {
                                Copy();
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }


        private void ListBoxItemLvTags_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element)
                {
                    var item = GetSelectedListBoxItem(element);
                    if (item != null)
                    {
                        if ((Keyboard.Modifiers & ModifierKeys.Shift) != 0)
                        {
                            try
                            {
                                object ob = (ItemsSourceStruct)item.DataContext;
                                var mouseIndex = LvTags.Items.IndexOf(ob);
                                var minIndex = LvTags.SelectedIndex < mouseIndex ? LvTags.SelectedIndex : mouseIndex;

                                var allWillSelecte = Math.Abs(LvTags.SelectedIndex - mouseIndex);
                                for (int i = 0; i < allWillSelecte + 1; i++)
                                {
                                    LvTags.SelectedItems.Add(LvTags.Items[i + minIndex]);
                                }
                            }
                            catch
                            {
                                // ignore
                            }
                        }
                        else if ((Keyboard.Modifiers & ModifierKeys.Control) != 0)
                        {
                            LvTags.SelectedItems.Add(item.DataContext);

                        }
                        else
                        {
                            LvTags.SelectedItem = item.DataContext;
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        private ListBoxItem? GetSelectedListBoxItem(FrameworkElement element)
        {
            try
            {
                var item = element;
                while (item != null && VisualTreeHelper.GetParent(item) != null && !(item is ListBoxItem))
                {
                    item = VisualTreeHelper.GetParent(item) as FrameworkElement;
                }
                if (!(item is ListBoxItem))
                {
                    return null;
                }
                return (ListBoxItem)item;
            }
            catch
            {
                return null;
            }
        }

        private object? GetSelectedButtonItem()
        {
            try
            {
                var item = VisualTreeHelper.HitTest(LvSnippets, Mouse.GetPosition(LvSnippets)).VisualHit;
                while (VisualTreeHelper.GetParent(item) != null && !(item is Button))
                {
                    item = VisualTreeHelper.GetParent(item);
                }
                return item;
            }
            catch
            {
                return null;
            }
        }
        private void CommandBindingCopy_OnCanExecute(object sender, CanExecuteRoutedEventArgs e)
        {
            e.CanExecute = !string.IsNullOrEmpty(_selectedText);
        }

        private void CopySelectedText(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetTextWithoutHistory(_selectedText);
        }

        private void TextBoxSearch_OnTextChanged(object sender, RadRoutedEventArgs radRoutedEventArgs)
        {
            string text = ((TextBox)sender).Text;
            var searchList = VMContainer.Instance.SnippetsViewModel.SnippetsList.Where(x => x?.ShortText != null && x.Text != null && (x.Text.ToLower().Contains(text.ToLower()) || x.ShortText.ToLower().Contains(text.ToLower()))).ToList();
            var list = new RadObservableCollection<SnippetsDataModel?>(searchList);
            lvFastAction_Bind(list);
        }
    }

    internal class IndexConverterForLvSnippets : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            int index = (int)value;
            if (index > 9)
            {
                return "";
            }
            return index + 1;

        }
        public object? ConvertBack(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            return null;
        }
    }

    internal class IndexConverterForLvTags : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            int index = (int)value;
            if (index > 9)
            {
                return "";
            }
            return index + 1;
        }
        public object? ConvertBack(object value, Type targetType, object parameter,
            CultureInfo culture)
        {
            return null;
        }
    }
}
