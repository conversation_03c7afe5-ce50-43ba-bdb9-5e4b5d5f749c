﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using Everylang.App.Utilities.NetRequest;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Controls;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class GeneralSettingsViewModel : ViewModelBase
    {
        public DelegateCommand SaveProxyCommand
        {
            get;
            private set;
        }

        public DelegateCommand ExportCommand
        {
            get;
            private set;
        }

        public DelegateCommand ImportCommand
        {
            get;
            private set;
        }

        public GeneralSettingsViewModel()
        {
            SaveProxyCommand = new DelegateCommand(SaveProxy);
            ExportCommand = new DelegateCommand(Export);
            ImportCommand = new DelegateCommand(Import);
            _proxyServer = SettingsManager.Settings.ProxyServer;
            _proxyPort = SettingsManager.Settings.ProxyPort;
            _proxyUserName = SettingsManager.Settings.ProxyUserName;

            GlobalEventsApp.EventPro += (_) =>
            {
                jgebhdhs = SettingsManager.LicIsActivated;
            };
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
            }
        }

        private async void SaveProxy(object parameter)
        {
            if (parameter is PasswordBox passwordBox)
            {
                var password = passwordBox.Password;
                var proxyServerOld = SettingsManager.Settings.ProxyServer;
                var proxyPortOld = SettingsManager.Settings.ProxyPort;
                var proxyUserNameOld = SettingsManager.Settings.ProxyUserName;
                var proxyPasswordOld = SettingsManager.Settings.ProxyPassword;

                SettingsManager.Settings.ProxyServer = _proxyServer;
                SettingsManager.Settings.ProxyPort = _proxyPort;
                SettingsManager.Settings.ProxyUserName = _proxyUserName;
                SettingsManager.Settings.ProxyPassword = password;

                bool proxyIsOk = await CheckProxy();
                if (!proxyIsOk)
                {
                    SettingsManager.Settings.ProxyServer = proxyServerOld;
                    ProxyServer = proxyServerOld;
                    SettingsManager.Settings.ProxyPort = proxyPortOld;
                    ProxyPort = proxyPortOld;
                    SettingsManager.Settings.ProxyUserName = proxyUserNameOld;
                    SettingsManager.Settings.ProxyPassword = proxyPasswordOld;
                    base.OnPropertyChanged(nameof(ProxyServer));
                    base.OnPropertyChanged(nameof(ProxyPort));
                    ShowError();
                }
            }
        }

        private async Task<bool> CheckProxy()
        {
            bool result = false;
            await Task.Factory.StartNew(() =>
            {
                try
                {
                    string uri = "http://google.com/";
                    var netLib = new NetLib(uri);
                    var webResult = netLib.StartGetWebRequest();
                    result = !webResult.WithError;
                }
                catch
                {
                    // Ignored
                }
                return false;
            });
            return result;
        }

        private void ShowError()
        {
            RadWindow.Alert(LocalizationManager.GetString("GeneralSettingsProxyError"));
        }

        private string _shortcut;

        public string Shortcut
        {
            get
            {
                _shortcut = ShortcutManager.GetCharFromKey(SettingsManager.Settings.OpenMainWindowShortcut);
                return _shortcut;
            }
            set
            {
                SettingsManager.Settings.OpenMainWindowShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string StopWorkingShortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.StopWorkingShortcut);
            }
            set
            {
                SettingsManager.Settings.StopWorkingShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsStopWorkingFullScreen
        {
            get
            {
                return SettingsManager.Settings.IsStopWorkingFullScreen;
            }
            set
            {
                SettingsManager.Settings.IsStopWorkingFullScreen = value;
                base.OnPropertyChanged();
            }
        }


        public string? DataFilePath
        {
            get
            {
                return SettingsManager.DataFilePath;
            }
            set
            {
                SettingsManager.DataFilePath = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsProxyUseIE
        {
            get
            {
                return SettingsManager.Settings.ProxyUseIE;
            }
            set
            {
                SettingsManager.Settings.ProxyUseIE = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsNotProxyUseIE));
            }
        }

        public bool IsNotProxyUseIE
        {
            get
            {
                return !SettingsManager.Settings.ProxyUseIE;
            }
        }

        private string? _proxyServer;

        public string? ProxyServer
        {
            get
            {
                return _proxyServer;
            }
            set
            {
                _proxyServer = value;
            }
        }

        private string? _proxyPort;

        public string? ProxyPort
        {
            get
            {
                return _proxyPort;
            }
            set
            {
                _proxyPort = value;
            }
        }

        private string? _proxyUserName;

        public string? ProxyUserName
        {
            get
            {
                return _proxyUserName;
            }
            set
            {
                _proxyUserName = value;
            }
        }

        internal string? ProxyPassword
        {
            get
            {
                return SettingsManager.Settings.ProxyPassword;
            }
        }

        public bool IsCheckUpdate
        {
            get
            {
                return SettingsManager.Settings.IsCheckUpdate;
            }
            set
            {
                SettingsManager.Settings.IsCheckUpdate = value;
                if (!value)
                {
                    try
                    {
                        var updatePath =
                            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                                "EveryLang\\EveryLangUpdate");
                        if (Directory.Exists(updatePath))
                        {
                            Directory.Delete(updatePath, true);
                        }
                    }
                    catch
                    {
                        // Ignored
                    }
                }
                base.OnPropertyChanged();
            }
        }

        public bool IsCheckUpdateBeta
        {
            get
            {
                return SettingsManager.Settings.IsCheckUpdateBeta;
            }
            set
            {
                SettingsManager.Settings.IsCheckUpdateBeta = value;
            }
        }

        public bool IsStartUp
        {
            get
            {
                return StartUp.IsStartUp;
            }
            set
            {
                if (value)
                {
                    StartUp.CreateStartUp();
                }
                else
                {
                    StartUp.DeleteStartUp();
                }
                base.OnPropertyChanged();
            }
        }

        public bool IsStartAdmin
        {
            get
            {
                return StartUp.IsStartAdmin;
            }
            set
            {
                if (value)
                {
                    StartUp.CreateStartAdmin();
                }
                else
                {
                    StartUp.DeleteStartAdmin();
                }
                base.OnPropertyChanged();
            }
        }

        public bool IsAdmin
        {
            get
            {
                return Administrator.IsCanRunAdministrator();
            }
        }

        public bool MinimizeToTray
        {
            get
            {
                return SettingsManager.Settings.MainFormMinimizeToTray;
            }
            set
            {
                SettingsManager.Settings.MainFormMinimizeToTray = value;
                base.OnPropertyChanged();
            }
        }

        public bool CanClose
        {
            get
            {
                return SettingsManager.Settings.CanClose;
            }
            set
            {
                SettingsManager.Settings.CanClose = value;
                base.OnPropertyChanged();
            }
        }

        private void Import(object o)
        {
            ExportImportSettings.Import();
        }

        private void Export(object o)
        {
            ExportImportSettings.Export();
        }

    }

}
