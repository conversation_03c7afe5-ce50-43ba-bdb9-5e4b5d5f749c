﻿using Everylang.App.Clipboard;
using Everylang.App.Converter;
using Everylang.App.Diary;
using Everylang.App.Main;
using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.SmartClick;
using Everylang.App.Snippets;
using Everylang.App.SpellCheck;
using Everylang.App.SwitcherLang;
using Everylang.App.Translator;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.AllHotKeys
{
    internal partial class AllHotKeysControl
    {
        internal AllHotKeysControl()
        {
            InitializeComponent();
        }

        private void NewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("OpenMainWindowShortcut"), SettingsManager.Settings.OpenMainWindowShortcut, nameof(SettingsManager.Settings.OpenMainWindowShortcut), AppHookManager.Instance.PressedMain);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.GeneralSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void StopWorkingShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("StopWorkingShortcut"), SettingsManager.Settings.StopWorkingShortcut, nameof(SettingsManager.Settings.StopWorkingShortcut), AppHookManager.Instance.StopWorking);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.GeneralSettingsViewModel.StopWorkingShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void TranslationNewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("TransSettingsKeyboardShortcuts"), SettingsManager.Settings.TranslateShowMiniFormShortcut, nameof(SettingsManager.Settings.TranslateShowMiniFormShortcut), TranslateManager.Instance.PressedFloating);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.TranslationSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SpellcheckingNewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SpellcheckingKeyboardShortcutsShort"), SettingsManager.Settings.SpellCheckShortcut, nameof(SettingsManager.Settings.SpellCheckShortcut), SpellCheckManager.Instance.PressedSpellCheck);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SpellcheckingSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void KeyboardShortcutsSwitchClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsKeyboardShortcutsSwitch"), SettingsManager.Settings.SwitcherSwitchTextLangShortcut, nameof(SettingsManager.Settings.SwitcherSwitchTextLangShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSwitchLangForWord);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.SwitcherSwitchTextLangShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchTextLangForAllLineShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsIsOnInsert"), SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut, nameof(SettingsManager.Settings.SwitcherSwitchTextLangForAllLineShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSwitchLangForAllLine);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.SwitcherSwitchTextLangForAllLineShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchTextShortcutSelectedClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SwitcherSettingsKeyboardShortcutsSwitchSelected"), SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut, nameof(SettingsManager.Settings.SwitcherShortcutSwitchLangSelectedTextShortcut), KeyboardLayoutManager.Instance.PressedSwitcherSelectedText);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SwitcherSettingsViewModel.ShortcutSelected = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ClipboardPasteWithoutFormattingShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardShortcuts"), SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut, nameof(SettingsManager.Settings.ClipboardPasteWithoutFormattingShortcut), ClipboardHookManager.Instance.PressedClipboardPasteWithoutFormatting);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ClipboardPasteWithoutFormattingShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ClipboardClipboardShortcutViewClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardViewShortcuts"), SettingsManager.Settings.ClipboardShowHistoryShortcut, nameof(SettingsManager.Settings.ClipboardShowHistoryShortcut), ClipboardHookManager.Instance.PressedClipboardView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ShortcutView = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ClipboardShortcutRoundClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ClipboardKeyboardRoundShortcutsShort"), SettingsManager.Settings.ClipboardPasteRoundShortcut, nameof(SettingsManager.Settings.ClipboardPasteRoundShortcut), ClipboardHookManager.Instance.PressedClipboardRoundPaste);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ClipboardSettingsViewModel.ShortcutRound = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void AutochangeShortcutShowListClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("AutochangeKeyboardShortcuts"), SettingsManager.Settings.SnippetsShowAllShortcut, nameof(SettingsManager.Settings.SnippetsShowAllShortcut), SnippetsManager.Instance.PressedSnippetsView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SnippetsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void AutochangeShortcutAddNewClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("AutochangeKeyboardShortcutsAddNew"), SettingsManager.Settings.SnippetsAddNewShortcut, nameof(SettingsManager.Settings.SnippetsAddNewShortcut), SnippetsManager.Instance.PressedSnippetsAddNew);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.SnippetsViewModel.ShortcutAddNew = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void OcrNewShortCutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("OcrKeyboardShortcuts"), SettingsManager.Settings.OcrShortcut, nameof(SettingsManager.Settings.OcrShortcut), OcrManager.Instance.PressedShortcutOcr);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.OcrViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SmartClickShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("SmartClickShortcutSettingsHeader"), SettingsManager.Settings.SmartClickShortcut, nameof(SettingsManager.Settings.SmartClickShortcut), SmartClickManager.Instance.PressedShortcutSmartClick);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.UniversalWindowSettingsViewModel.Shortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
        private void DiaryShowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("DiaryShortcuts"), SettingsManager.Settings.DiaryShowShortcut, nameof(SettingsManager.Settings.DiaryShowShortcut), DiaryManager.Instance.PressedDiaryView);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.DiaryViewModel.DiaryShortcut = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ExpressionOpenWindowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsOpenWindow"), SettingsManager.Settings.ConverterOpenWindowShortcut, nameof(SettingsManager.Settings.ConverterOpenWindowShortcut), ConverterManager.Instance.PressedConverterOpenWindow);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutOpenWindow = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ExpressionShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsExpression"), SettingsManager.Settings.ConverterExpresionShortcut, nameof(SettingsManager.Settings.ConverterExpresionShortcut), ConverterManager.Instance.PressedConverterExpresion);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutExpresion = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void TransliterationShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsTransliteration"), SettingsManager.Settings.ConverterTransliterationShortcut, nameof(SettingsManager.Settings.ConverterTransliterationShortcut), ConverterManager.Instance.PressedConvertTransliteration);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutTransliteration = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void EncloseTextQuotationMarksShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsEncloseTextQuotationMarks"), SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut, nameof(SettingsManager.Settings.ConverterEncloseTextQuotationMarksShortcut), ConverterManager.Instance.PressedConvertEncloseTextQuotationMarks);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutEncloseTextQuotationMarks = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchSelectedCapsOpenWindowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsCapsOpenWindow"), SettingsManager.Settings.ConverterShortcutCapsOpenWindow, nameof(SettingsManager.Settings.ConverterShortcutCapsOpenWindow), ConverterManager.Instance.PressedSwitcherOpenWindowCaps);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutOpenWindowCaps = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void CamelCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsCamelCase"), SettingsManager.Settings.ConverterShortcutCamelCase, nameof(SettingsManager.Settings.ConverterShortcutCamelCase), ConverterManager.Instance.PressedCamelCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutCamelCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void PascalCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsPascalCase"), SettingsManager.Settings.ConverterShortcutPascalCase, nameof(SettingsManager.Settings.ConverterShortcutPascalCase), ConverterManager.Instance.PressedPascalCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutPascalCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
        private void KebabCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKebabCase"), SettingsManager.Settings.ConverterShortcutKebabCase, nameof(SettingsManager.Settings.ConverterShortcutKebabCase), ConverterManager.Instance.PressedKebabCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutKebabCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
        private void SnakeCaseShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsSnakeCase"), SettingsManager.Settings.ConverterShortcutSnakeCase, nameof(SettingsManager.Settings.ConverterShortcutSnakeCase), ConverterManager.Instance.PressedSnakeCase);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSnakeCase = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void ReplaceSelTextShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterReplaceSelText"), SettingsManager.Settings.ConverterShortcutReplaceSelText, nameof(SettingsManager.Settings.ConverterShortcutReplaceSelText), ConverterManager.Instance.PressedReplaceSelText);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutReplaceSelText = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchSelectedCapsShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert"), SettingsManager.Settings.ConverterShortcutCapsInvert, nameof(SettingsManager.Settings.ConverterShortcutCapsInvert), ConverterManager.Instance.PressedSwitcherSelectedCaps);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCaps = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchSelectedCapsUpShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp"), SettingsManager.Settings.ConverterShortcutCapsUp, nameof(SettingsManager.Settings.ConverterShortcutCapsUp), ConverterManager.Instance.PressedSwitcherSelectedCapsUp);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCapsUp = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchSelectedCapsDownShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown"), SettingsManager.Settings.ConverterShortcutCapsDown, nameof(SettingsManager.Settings.ConverterShortcutCapsDown), ConverterManager.Instance.PressedSwitcherSelectedCapsDown);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCapsDown = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchFirstLetterToDown(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToDown"), SettingsManager.Settings.ConverterFirstLetterToDown, nameof(SettingsManager.Settings.ConverterFirstLetterToDown), ConverterManager.Instance.PressedSwitcherFirstLetterToDown);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutFirstLetterToDown = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchFirstLetterToUp(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToUp"), SettingsManager.Settings.ConverterFirstLetterToUp, nameof(SettingsManager.Settings.ConverterFirstLetterToUp), ConverterManager.Instance.PressedSwitcherFirstLetterToUp);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutFirstLetterToUp = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }


    }
}
