﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Translator;
using System;
using System.Collections.Specialized;
using System.Drawing;
using System.Globalization;
using System.Linq;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;
using FontFamily = System.Drawing.FontFamily;
using SystemFonts = System.Drawing.SystemFonts;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class TranslationSettingsViewModel : ViewModelBase
    {
        public RadObservableCollection<string> LanguagesForNativeLanguage { get; set; }
        public RadObservableCollection<string> LanguagesForTheTranslation { get; set; }
        public RadObservableCollection<string> ProvidersForTranslation { get; set; }
        public RadObservableCollection<Language> FavoriteLanguages { get; set; }
        public RadObservableCollection<Language> AllLanguages { get; set; }

        public DelegateCommand SetFontCommand
        {
            get;
            private set;
        }

        public TranslationSettingsViewModel()
        {
            SettingsManager.Settings.TranslateFontFamily = GetFontExist(SettingsManager.Settings.TranslateFontFamily).Name;
            SetFontCommand = new DelegateCommand(SetFont);
            LanguagesForNativeLanguage = new RadObservableCollection<string>(TranslateCommonSettings.ListGoogleLangs.Select(x => x.Name)!);
            LanguagesForNativeLanguage.RemoveAt(0);
            LanguagesForTheTranslation = new RadObservableCollection<string>(TranslateCommonSettings.ListGoogleLangs.Select(x => x.Name)!);
            LanguagesForTheTranslation.RemoveAt(0);
            ProvidersForTranslation = new RadObservableCollection<string>(TranslateCommonSettings.TranslateServices);
            AllLanguages = new RadObservableCollection<Language>(TranslateCommonSettings.AllLangs);
            FavoriteLanguages = new RadObservableCollection<Language>(TranslateCommonSettings.FavoriteLanguages);
            FavoriteLanguages.CollectionChanged += FavoriteLanguagesOnCollectionChanged;
            base.OnPropertyChanged(nameof(FontText));
            base.OnPropertyChanged(nameof(FavoriteLanguages));
        }

        private void FavoriteLanguagesOnCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            TranslateCommonSettings.FavoriteLanguages = FavoriteLanguages.ToList();
            VMContainer.Instance.TranslationMainViewModel.CurrentTranslateService = VMContainer.Instance.TranslationMainViewModel.CurrentTranslateService;
            if (!TranslateCommonSettings.FavoriteLanguages.Any())
            {
                TranslateOnlyFavoriteLanguages = false;
            }
            base.OnPropertyChanged(nameof(TranslateOnlyFavoriteLanguages));
            base.OnPropertyChanged(nameof(TranslateOnlyFavoriteLanguagesEnabled));
        }

        private void SetFont(object o)
        {

            System.Windows.Forms.FontDialog fontDialog = new System.Windows.Forms.FontDialog();
            var fontSise = Convert.ToDouble(SettingsManager.Settings.TranslateFontSize);
            float fontSizeFloat = (float)fontSise;
            fontDialog.Font = new Font(GetFontExist(SettingsManager.Settings.TranslateFontFamily), fontSizeFloat);
            if (fontDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {

                var fontSizeRes = fontDialog.Font.Size;
                var fontFamRes = fontDialog.Font.Name;
                SettingsManager.Settings.TranslateFontFamily = fontFamRes;
                SettingsManager.Settings.TranslateFontSize = Math.Round(fontSizeRes).ToString(CultureInfo.InvariantCulture);
                base.OnPropertyChanged(nameof(FontText));
                VMContainer.Instance.TranslationMainViewModel.OnPropertyChanged(nameof(VMContainer.Instance.TranslationMainViewModel.FontFam));
                VMContainer.Instance.TranslationMainViewModel.OnPropertyChanged(nameof(VMContainer.Instance.TranslationMainViewModel.FontSize));
            }
        }

        private FontFamily GetFontExist(string font)
        {
            FontFamily fontFamily;
            try
            {
                fontFamily = new FontFamily(font);
            }
            catch
            {
                fontFamily = SystemFonts.DefaultFont.FontFamily;
            }
            return fontFamily;
        }


        public string FontText
        {
            get
            {

                return SettingsManager.Settings.TranslateFontFamily + " " + SettingsManager.Settings.TranslateFontSize;
            }
        }

        public bool TranslationIsOn
        {
            get
            {
                return SettingsManager.Settings.TranslateIsOn;
            }
            set
            {
                SettingsManager.Settings.TranslateIsOn = value;
                if (!value)
                {
                    TranslateManager.Instance.Stop();
                }
                else
                {
                    TranslateManager.Instance.Start();
                }
                base.OnPropertyChanged();
            }
        }

        public string? LanguageNative
        {
            get
            {
                var lang = TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo);
                if (lang != null)
                    return lang.Name;

                return TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Abbreviation == "en")?.Name;
            }
            set
            {
                var lang = TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Name == value);
                if (lang != null)
                {
                    SettingsManager.Settings.TranslateLangTo = lang.Abbreviation;
                }
                base.OnPropertyChanged();
            }
        }

        public string? LanguageForTheTranslation
        {
            get
            {
                var lang = TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangFrom);
                if (lang != null)
                    return lang.Name;

                return TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Abbreviation == "en")?.Name;
            }
            set
            {

                var lang = TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(x => x.Name == value);
                if (lang != null)
                {
                    SettingsManager.Settings.TranslateLangFrom = lang.Abbreviation;
                }
                base.OnPropertyChanged();
            }
        }

        public string ProviderForTranslation
        {
            get
            {
                return TranslateCommonSettings.TranslateServices[SettingsManager.Settings.TranslateProvider];
            }
            set
            {
                TranslateCommonSettings.TranslateServiceDefault = Array.IndexOf(TranslateCommonSettings.TranslateServices, value);
                SettingsManager.Settings.TranslateProvider = Array.IndexOf(TranslateCommonSettings.TranslateServices, value);
                base.OnPropertyChanged();
            }
        }

        public string Shortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.TranslateShowMiniFormShortcut);
            }
            set
            {
                SettingsManager.Settings.TranslateShowMiniFormShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public bool TranslationIsAlways
        {
            get
            {
                return SettingsManager.Settings.TranslateShowMiniFormAlwaysWhenSelectText;
            }
            set
            {
                SettingsManager.Settings.TranslateShowMiniFormAlwaysWhenSelectText = value;
                base.OnPropertyChanged();
            }
        }

        public bool TranslateOnlyFavoriteLanguagesEnabled
        {
            get
            {
                return !string.IsNullOrEmpty(SettingsManager.Settings.TranslateFavoriteLanguages);
            }
            set
            {

            }
        }

        public bool TranslateOnlyFavoriteLanguages
        {
            get
            {
                return SettingsManager.Settings.TranslateOnlyFavoriteLanguages;
            }
            set
            {
                SettingsManager.Settings.TranslateOnlyFavoriteLanguages = value;
                GlobalEventsApp.OnEventOnlyFavoriteLangForTranslate();
                base.OnPropertyChanged();
            }
        }

        internal void SetLanguegeUI()
        {
            LanguagesForNativeLanguage = new RadObservableCollection<string>(TranslateCommonSettings.ListGoogleLangs.Select(x => x.Name)!);
            LanguagesForNativeLanguage.RemoveAt(0);
            LanguagesForTheTranslation = new RadObservableCollection<string>(TranslateCommonSettings.ListGoogleLangs.Select(x => x.Name)!);
            LanguagesForTheTranslation.RemoveAt(0);
            ProvidersForTranslation = new RadObservableCollection<string>(TranslateCommonSettings.TranslateServices);
            LanguageForTheTranslation = TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(
                    x => x.Abbreviation == SettingsManager.Settings.TranslateLangFrom)
                ?.Name;
            LanguageNative =
                TranslateCommonSettings.ListGoogleLangs.FirstOrDefault(
                    x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo)
                    ?.Name;

            base.OnPropertyChanged(nameof(LanguagesForNativeLanguage));
            base.OnPropertyChanged(nameof(LanguagesForTheTranslation));
            base.OnPropertyChanged(nameof(LanguageNative));
            base.OnPropertyChanged(nameof(LanguageForTheTranslation));
            base.OnPropertyChanged(nameof(ProviderForTranslation));
        }

    }
}
