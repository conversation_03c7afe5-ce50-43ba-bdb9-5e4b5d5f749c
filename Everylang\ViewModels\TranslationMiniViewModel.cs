﻿
using Everylang.App.Clipboard;
using Everylang.App.SettingsApp;
using Everylang.App.Translator;
using Everylang.App.Translator.NetRequest;
using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Net;
using Telerik.Windows.Controls;
using Language = Everylang.App.Translator.Language;
using Translation = Everylang.App.Translator.Translation;

namespace Everylang.App.ViewModels
{
    internal class TranslationMiniViewModel : ViewModelBase
    {
        internal bool WithError;

        internal RequestSettings RequestSettings;
        internal Action<WebResultTranslator>? ShowTranlatedText;
        public ObservableCollection<Language> LanguagesFrom { get; private set; }
        public ObservableCollection<Language>? LanguagesTo { get; private set; }
        public ObservableCollection<string> TranslateServices { get; private set; }


        public DelegateCommand ListenTransalatedSourseCommand
        {
            get;
            private set;
        }

        public DelegateCommand ListenTransalatedResultCommand
        {
            get;
            private set;
        }

        internal DelegateCommand TranslateCommand
        {
            get;
            private set;
        }

        public DelegateCommand CopyTransalatedCommand
        {
            get;
            private set;
        }

        public DelegateCommand SiteSourceCommand
        {
            get;
            private set;
        }

        public DelegateCommand SwapLangCommand
        {
            get;
            private set;
        }

        private readonly Translation _translation;

        public TranslationMiniViewModel()
        {
            RequestSettings = new RequestSettings();
            RequestSettings.GetCurrentTranslateServiceLangs();
            _translation = new Translation();
            SwapLangCommand = new DelegateCommand(CommandSwapLang);
            ListenTransalatedSourseCommand = new DelegateCommand(CommandListenTransalatedFromText, (_) => !WithError);
            ListenTransalatedResultCommand = new DelegateCommand(CommandListenTransalatedToText, (_) => !WithError);
            CopyTransalatedCommand = new DelegateCommand(CommandCopyTransalated, (_) => !WithError);
            SiteSourceCommand = new DelegateCommand(CommandSiteSource);
            TranslateCommand = new DelegateCommand(CommandTranslate);

            TranslateServices = new ObservableCollection<string>(TranslateCommonSettings.TranslateServices);
            LanguagesFrom = new ObservableCollection<Language>(RequestSettings.ListLangs);
            LanguageFromCurrent = TranslateCommonSettings.LanguageFromDefault(RequestSettings.ListLangs);
            _translation = new Translation();
            _translation.CallBackTranslate += TranslationSuccess;
            RequestSettings.CurrentTranslateServiceIndex = TranslateCommonSettings.TranslateServiceLast;

        }

        internal void Translate(string text)
        {
            if (!string.IsNullOrEmpty(text.Trim()) && !RequestSettings.IsNowTranslating)
            {
                IsVisibleResultPanel = false;
                SourceText = text;
                RequestSettings.IsNowTranslating = true;
                IsVisibleProgress = true;
                IsVisibleTextPanel = false;
                _translation.GetTranslation(RequestSettings);
            }
        }

        private void CommandSiteSource(object o)
        {

            var text = WebUtility.UrlEncode(SourceText);
            if (RequestSettings.CurrentTranslateServiceIndex == 0)
            {
                StartProcess("https://translate.google.com/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 1)
            {
                StartProcess("https://www.bing.com/translator/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 2)
            {
                StartProcess("https://translate.yandex.ru/?text=" + text);
            }
            if (RequestSettings.CurrentTranslateServiceIndex == 3)
            {
                StartProcess("https://www.deepl.com/translator#" + text);
            }

        }

        private void StartProcess(string data)
        {
            try
            {
                Process.Start(data);
            }
            catch
            {
                // ignored
            }
        }

        internal void CommandTranslate(object? o)
        {
            Translate(SourceText);
        }

        private void CommandSwapLang(object o)
        {
            var langFrom = LanguageFromCurrent;
            var langTo = LanguageToCurrent;
            if (LanguageFromCurrent?.Abbreviation == "auto")
            {
                langFrom = LanguagesTo?.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangFrom);
            }
            LanguageFromCurrent = langTo;
            LanguageToCurrent = langFrom;

            base.OnPropertyChanged(nameof(LanguageFromCurrent));
            GetLanguagesTo();
            CommandTranslate(null);
        }

        private void CommandCopyTransalated(object o)
        {
            try
            {
                ClipboardOperations.SetText(TranslatedTextWithNonChar);
            }
            catch
            {
                // ignored
            }
        }

        private void CommandListenTransalatedFromText(object o)
        {
            try
            {
                _translation.GetListening(SourceText.Trim(), LanguageFromCurrent?.Abbreviation);
            }
            catch
            {
                // ignored
            }
        }

        private void CommandListenTransalatedToText(object o)
        {
            try
            {
                if (TranslatedTextWithNonChar != null)
                    _translation.GetListening(TranslatedTextWithNonChar.Trim(), LanguageToCurrent?.Abbreviation);
            }
            catch
            {
                // ignored
            }
        }

        public string CurrentTranslateService
        {
            get { return TranslateCommonSettings.TranslateServices[TranslateCommonSettings.TranslateServiceLast]; }
            set
            {
                var languageName = RequestSettings.LanguageFromCurrent?.Name;
                TranslateCommonSettings.TranslateServiceLast = Array.IndexOf(TranslateCommonSettings.TranslateServices, value);
                RequestSettings.CurrentTranslateServiceIndex = TranslateCommonSettings.TranslateServiceLast;
                RequestSettings.GetCurrentTranslateServiceLangs();
                LanguagesFrom = new ObservableCollection<Language>(RequestSettings.ListLangs);
                var language = RequestSettings.ListLangs.FirstOrDefault(x => x.Name != null && x.Name.Equals(languageName));
                if (language == null)
                {
                    RequestSettings.LanguageFromCurrent = TranslateCommonSettings.LanguageFromDefault(RequestSettings.ListLangs);
                }
                else
                {
                    RequestSettings.LanguageFromCurrent = language;
                }
                base.OnPropertyChanged(nameof(LanguagesFrom));
                base.OnPropertyChanged(nameof(CurrentTranslateService));
                base.OnPropertyChanged(nameof(LanguageFromCurrent));
                CommandTranslate(null);
            }
        }

        private bool _isVisibleResultPanel;

        public bool IsVisibleResultPanel
        {
            get { return _isVisibleResultPanel; }
            set
            {
                _isVisibleResultPanel = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isVisibleTextPanel;

        public bool IsVisibleTextPanel
        {
            get { return _isVisibleTextPanel; }
            set
            {
                _isVisibleTextPanel = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isStayOnTop;

        internal bool IsStayOnTop
        {
            get { return _isStayOnTop; }
            set
            {
                _isStayOnTop = value;
                base.OnPropertyChanged();
            }
        }

        public Language? LanguageFromCurrent
        {
            get
            {
                return RequestSettings.LanguageFromCurrent;
            }
            set
            {
                if (value != null) RequestSettings.LanguageFromCurrent = value;
                base.OnPropertyChanged();
                GetLanguagesTo();
            }
        }

        private static Language? _languageToCurrentSaved;
        private static DateTime LastTimeClear { get; set; }

        public Language? LanguageToCurrent
        {
            get
            {
                return RequestSettings.LanguageToCurrent;
            }
            set
            {
                if (value != null)
                {
                    RequestSettings.LanguageToCurrent = value;
                    if (_languageToCurrentSaved != value)
                    {
                        _languageToCurrentSaved = value;
                        LastTimeClear = DateTime.Now;
                    }
                    base.OnPropertyChanged();
                }
            }
        }

        private void GetLanguagesTo()
        {
            LanguagesTo = new ObservableCollection<Language>(LanguagesFrom);
            LanguagesTo.Remove(LanguagesTo.FirstOrDefault(x => x.Abbreviation == "auto")!);
            if (LanguageToCurrent != null && LanguagesTo.Contains(LanguageToCurrent))
            {
                var languageTo = LanguagesTo.FirstOrDefault(x => x.Abbreviation == SettingsManager.Settings.TranslateLangTo);
                if (languageTo == null || !LanguagesTo.Contains(languageTo))
                {
                    LanguageToCurrent = LanguagesTo.First();
                }
                else
                {
                    if ((DateTime.Now - LastTimeClear).Minutes > 10 || _languageToCurrentSaved == null)
                    {

                        LanguageToCurrent = languageTo;
                    }
                    else
                    {
                        LanguageToCurrent = _languageToCurrentSaved;
                    }
                }
            }

            base.OnPropertyChanged(nameof(LanguagesTo));
        }

        internal string? TranslatedText
        {
            get { return RequestSettings.TranslatedText; }
        }

        internal string? TranslatedTextWithNonChar
        {
            get { return RequestSettings.TranslatedTextWithNonChar; }
        }

        internal string SourceText
        {
            get
            {
                return RequestSettings.SourceText;
            }
            set
            {
                RequestSettings.SourceText = value;
                base.OnPropertyChanged();
            }
        }

        private bool _isVisibleProgress;

        public bool IsVisibleProgress
        {
            get { return _isVisibleProgress; }
            set
            {
                _isVisibleProgress = value;
                base.OnPropertyChanged();

            }
        }
        bool _isTranslationComplete;

        internal bool IsTranslationComplete
        {
            get { return _isTranslationComplete; }
            set
            {
                _isTranslationComplete = value;
                base.OnPropertyChanged();

            }

        }

        public string FontFam
        {
            get
            {
                return SettingsManager.Settings.TranslateFontFamily;
            }
        }

        public string FontSize
        {
            get
            {
                return SettingsManager.Settings.TranslateFontSize;
            }
        }



        private void TranslationSuccess(WebResultTranslator webResultTranslator)
        {
            IsVisibleProgress = false;
            if (!webResultTranslator.WithError && !string.IsNullOrEmpty(webResultTranslator.ResultText))
            {

                WithError = false;
                RequestSettings.TranslatedText = webResultTranslator.ResultText;
                RequestSettings.TranslatedTextWithNonChar = webResultTranslator.ResultTextWithNonChar;
                RequestSettings.TranslatedTextLatin = webResultTranslator.LatinText;
                Language? langFrom = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == webResultTranslator.FromLang);
                if (langFrom != null)
                {
                    RequestSettings.LanguageFromCurrent = langFrom;
                }
                Language? langTo = RequestSettings.ListLangs.FirstOrDefault(x => x.Abbreviation == webResultTranslator.ToLang);
                if (langTo != null)
                {
                    RequestSettings.LanguageToCurrent = langTo;
                }
                IsVisibleResultPanel = true;
                OnPropertyChanged(nameof(LanguageFromCurrent));
                OnPropertyChanged(nameof(LanguageToCurrent));
                OnPropertyChanged(nameof(TranslatedText));
            }
            else
            {
                WithError = true;
                IsVisibleResultPanel = false;
                RequestSettings.TranslatedText = LocalizationManager.GetString("TranslateError") + Environment.NewLine + webResultTranslator.ErrorText;
                OnPropertyChanged(nameof(TranslatedText));
            }

            IsVisibleTextPanel = true;
            if (ShowTranlatedText != null) ShowTranlatedText(webResultTranslator);
            RequestSettings.IsNowTranslating = false;
            IsTranslationComplete = true;
        }

    }
}
