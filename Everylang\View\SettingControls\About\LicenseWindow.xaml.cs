﻿using Everylang.App.Resources;
using Everylang.App.SettingsApp;
using System.Windows;
using Telerik.Windows.Documents.FormatProviders;
using Telerik.Windows.Documents.FormatProviders.Rtf;

namespace Everylang.App.View.SettingControls.About
{
    /// <summary>
    /// Interaction logic for LicenseWindow.xaml
    /// </summary>
    internal partial class LicenseWindow
    {
        internal LicenseWindow()
        {
            Owner = Application.Current.MainWindow;
            DocumentFormatProvidersManager.RegisterFormatProvider(new RtfFormatProvider());
            InitializeComponent();
            InitDoc();
        }

        internal void InitDoc()
        {
            string documentPath;
            if (SettingsManager.Settings.AppUILang == "ru")
            {
                documentPath = Resource.license_ru;
            }
            else
            {
                documentPath = Resource.license_en;
            }
            RadRichTextBox.Text = documentPath;
        }

        private void CloseClick(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
