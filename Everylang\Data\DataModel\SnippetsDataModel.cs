﻿using LiteDB;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Telerik.Windows.Controls;

namespace Everylang.App.Data.DataModel
{
    internal class SnippetsDataModel : INotifyPropertyChanged, ICloneable
    {
        private string? _fromText;
        private string? _shortText;
        private string? _tags;
        private string? _text;
        private int _countUsage;
        private int _cursorPosition;
        private bool _isSetCursorPosition;
        private bool _isChangeAtOnce;
        private bool _isHidden;
        internal ObjectId? Id { get; set; }

        public string? FromText
        {
            get { return _fromText; }
            set
            {
                _fromText = value;
                OnPropertyChanged(nameof(FromText));
            }
        }

        public string? ShortText
        {
            get { return _shortText; }
            set
            {
                _shortText = value;
                OnPropertyChanged(nameof(ShortText));
            }
        }

        internal string? LangToSwitch { get; set; } = "";

        public string? Tags
        {
            get
            {
                if (_tags != null) return _tags.Trim();
                return "";
            }
            set
            {
                _tags = value;
                OnPropertyChanged(nameof(Tags));
            }
        }

        public string? Text
        {
            get { return _text; }
            set
            {
                _text = value;
                OnPropertyChanged(nameof(Text));
            }
        }

        internal int CountUsage
        {
            get { return _countUsage; }
            set
            {
                _countUsage = value;
                OnPropertyChanged(nameof(CountUsage));
            }
        }

        internal int CursorPosition
        {
            get { return _cursorPosition; }
            set
            {
                _cursorPosition = value;
                OnPropertyChanged(nameof(CursorPosition));
            }
        }

        public bool IsSetCursorPosition
        {
            get { return _isSetCursorPosition; }
            set
            {
                _isSetCursorPosition = value;
                OnPropertyChanged(nameof(IsSetCursorPosition));
            }
        }

        public bool IsChangeAtOnce
        {
            get { return _isChangeAtOnce; }
            set
            {
                _isChangeAtOnce = value;
                OnPropertyChanged(nameof(IsChangeAtOnce));
            }
        }

        internal bool IsHidden
        {
            get { return _isHidden; }
            set
            {
                _isHidden = value;
                OnPropertyChanged(nameof(IsHidden));
            }
        }

        public bool IsSnippets
        {
            get { return !string.IsNullOrEmpty(FromText); }

        }

        public string TextToolTip
        {
            get { return LocalizationManager.GetString("AutochangeHelperFromTextTootlTip") + " " + FromText; }

        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public object Clone()
        {
            return MemberwiseClone();
        }
    }
}
