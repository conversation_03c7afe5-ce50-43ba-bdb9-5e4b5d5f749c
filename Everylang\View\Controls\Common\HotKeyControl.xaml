﻿<UserControl x:Class="Everylang.App.View.Controls.Common.HotKeyControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:common1="clr-namespace:Everylang.App.View.Controls.Common"
             mc:Ignorable="d" x:Name="Me" x:ClassModifier="internal">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <StackPanel Margin="20,10,0,0">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Top" Margin="10,10,0,0">
                <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                    <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
                </telerik:RadButton>
                <TextBlock Margin="10,0,0,0" HorizontalAlignment="Left" VerticalAlignment="Center" FontSize="15" TextWrapping="Wrap" Text="{Binding Path=HeaderText, ElementName=Me}"/>
            </StackPanel>
            <StackPanel Margin="50,30,0,0">
                <telerik:RadToggleSwitchButton  VerticalAlignment="Center"
                                                HorizontalAlignment="Left"
                                                FontSize="16"
                                                Focusable="False"
                                                CheckedContent="{telerik:LocalizableResource Key=HotKeyIsON}"
                                                UncheckedContent="{telerik:LocalizableResource Key=HotKeyIsOff}"
                                                ContentPosition="Right"
                                                IsChecked="{Binding Path=HotkeyIsOn, ElementName=Me}"
                                                Unchecked="SwitchOnOffHotkeyChecked"/>
                <RadioButton x:Name="RadioButtonHotkey" FontSize="13" Margin="0,20,0,0" Background="Transparent" Focusable="False"
                             IsEnabled="{Binding Path=HotkeyIsOn, ElementName=Me}"
                             Checked="RadioButtonHotkey_OnChecked">
                    <TextBlock FontSize="14"
                               Text="{telerik:LocalizableResource Key=HotKeyUseHotkey}" />
                </RadioButton>
                <StackPanel Margin="0 0 0 0" Name="StackPanelShortcut" Orientation="Horizontal">
                    <common1:HotKeyEditorControl x:Name="HotKeyBoxMy" IsEnabled="{Binding Path=IsChanging, ElementName=Me}"
                                        Width="195" />
                    <telerik:RadButton Margin="5,0,0,0"
                                       Focusable="False"
                            Content="{telerik:LocalizableResource Key=Edit}"
                            Click="ChangeShortcut" VerticalAlignment="Center"
                            IsEnabled="{Binding Path=IsChecked, ElementName=RadioButtonHotkey}"
                            Visibility="{Binding Path=IsNotChanging, ElementName=Me, Mode=Default, Converter={StaticResource BoolToVis}}" />
                    <telerik:RadButton Margin="5,0,0,0" 
                                       Focusable="False"
                            Content="{telerik:LocalizableResource Key=Save}"
                            Click="SaveShortcut" VerticalAlignment="Center"
                            IsEnabled="{Binding Path=IsChecked, ElementName=RadioButtonHotkey}"
                            Visibility="{Binding Path=IsChanging, ElementName=Me, Mode=Default, Converter={StaticResource BoolToVis}, FallbackValue=Collapsed}" />
                    <telerik:RadButton Margin="5,0,0,0" 
                                       Focusable="False"
                            Content="{telerik:LocalizableResource Key=Cancel}"
                            Click="CancelShortcut" VerticalAlignment="Center"
                            IsEnabled="{Binding Path=IsChecked, ElementName=RadioButtonHotkey}"
                            Visibility="{Binding Path=IsChanging, ElementName=Me, Mode=Default, Converter={StaticResource BoolToVis}, FallbackValue=Collapsed}" />
                </StackPanel>
                <RadioButton x:Name="RadioButtonDoubleKeyDown" FontSize="13" Margin="0,20,0,0"  Background="Transparent"
                             IsEnabled="{Binding Path=HotkeyIsOn, ElementName=Me}"
                             Focusable="False"
                             Checked="RadioButtonDoubleKeyDown_OnChecked">
                    <TextBlock FontSize="14" 
                               Text="{telerik:LocalizableResource Key=HotKeyUseDoubleKeyDown}" />
                </RadioButton> 
                <TextBlock Margin="0,0,0,0" FontSize="14" 
                           Text="{telerik:LocalizableResource Key=HotKeyDoubleKeyDownSelectKey}" />
                <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" Name="ComboBoxDoubleClick" Width="280" HorizontalAlignment="Left" DisplayMemberPath="Name"
                          IsEnabled="{Binding Path=IsChecked, ElementName=RadioButtonDoubleKeyDown}" Margin="0,5,0,0" Focusable="False"
                          SelectionChanged="ComboBoxDoubleClick_OnSelectionChanged" />

                <StackPanel IsEnabled="{Binding Path=MouseIsOn, ElementName=Me}">
                    <CheckBox x:Name="CheckBoxMouseXKey" FontSize="13" Margin="0,20,0,0"  Focusable="False"
                                 IsEnabled="{Binding Path=HotkeyIsOn, ElementName=Me}"
                                 Checked="RadioButtonMouseXKey_OnChecked" Unchecked="RadioButtonMouseXKey_OnChecked">
                        <TextBlock FontSize="14" 
                                   Text="{telerik:LocalizableResource Key=HotKeyUseMouseXKey}" />
                    </CheckBox>
                    <TextBlock Margin="0,0,0,0" FontSize="14" 
                               Text="{telerik:LocalizableResource Key=HotKeyMouseSelectKey}" />
                    <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}" Name="ComboBoxMouseXKey" Width="280" HorizontalAlignment="Left"
                              IsEnabled="{Binding Path=IsChecked, ElementName=CheckBoxMouseXKey}" Margin="0,5,0,0" Focusable="False"
                              SelectionChanged="ComboBoxMouseXKeyClick_OnSelectionChanged" />
                </StackPanel>

            </StackPanel>
        </StackPanel>
    </Grid>

</UserControl>
