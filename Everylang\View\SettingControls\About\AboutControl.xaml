﻿<UserControl x:Class="Everylang.App.View.SettingControls.About.AboutControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal" DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <Style TargetType="TextBlock" x:Key="TextBlockIsAdmin">
            <Style.Triggers>
                <DataTrigger Binding="{Binding AboutSettingsViewModel.IsAdmin}" Value="False">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <Style TargetType="TextBlock" x:Key="TextBlockIsNotAdmin">
            <Style.Triggers>
                <DataTrigger Binding="{Binding AboutSettingsViewModel.IsAdmin}" Value="True">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <!--<ScrollViewer VerticalScrollBarVisibility="Auto">-->
            <StackPanel Margin="20,10,0,0">
                <TextBlock FontSize="16" FontWeight="Bold" Text="{telerik:LocalizableResource Key=AboutSettingsHeader}"  />
                <TextBlock Margin="0,5,0,0" FontSize="14" TextWrapping="Wrap" Text="{telerik:LocalizableResource Key=AboutSettingsDesc}"/>
                <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                    <TextBlock FontSize="14"  Text="{telerik:LocalizableResource Key=AboutSettingsVersion}"  />
                    <TextBlock FontSize="14" Margin="5,0,0,0"  Text="{Binding Path=AboutSettingsViewModel.SelfName}" FontWeight="Bold" />
                </StackPanel>
            <telerik:RadHyperlinkButton Focusable="False" HorizontalAlignment="Left" Margin="-15,10,0,0" FontSize="16"  Cursor="Hand" Click="GoToWebsiteUpdate" Content="{Binding Path=AboutSettingsViewModel.UpdateText}" Visibility="{Binding Path=AboutSettingsViewModel.IsVisibleUpdate}" />
            <TextBlock Margin="0,10,0,0" FontSize="14"  Text="Copyright © Sergey Gulyaev"  />
                <Button Focusable="False" Margin="0,10,0,0" FontSize="14" MinWidth="250" Content="{telerik:LocalizableResource Key=AboutSettingsLicense}" Click="ShowLicense" HorizontalAlignment="Left"/>
                <TextBlock Margin="0,15,0,0" FontSize="16"  Text="{telerik:LocalizableResource Key=AboutSettingsContactHeader}"  />
                <TextBlock Margin="0,0,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=AboutSettingsContactText}"  />
            <telerik:RadHyperlinkButton Focusable="False" Margin="0,5,0,0" HorizontalAlignment="Left" FontSize="14" Content="{telerik:LocalizableResource Key=AboutSettingsContactLink}" Cursor="Hand" Click="GoToContactForm"  Padding="3" MinHeight="0"/>
            <telerik:RadHyperlinkButton Focusable="False" Margin="0,0,0,0" HorizontalAlignment="Left" FontSize="14" Content="<EMAIL>" Cursor="Hand" Click="GoToContactFormMail"  Padding="3" MinHeight="0"/>
                <TextBlock Margin="0,20,0,0" FontSize="16"  Text="{telerik:LocalizableResource Key=AboutSettingsIsAdmin}"  Style="{StaticResource TextBlockIsAdmin}"/>
                <TextBlock Margin="0,10,0,0" FontSize="16"  Text="{telerik:LocalizableResource Key=AboutSettingsIsNotAdmin}"  Style="{StaticResource TextBlockIsNotAdmin}"/>
                <Button Focusable="False" Margin="0,10,0,0" FontSize="14" MinWidth="250" Content="{telerik:LocalizableResource Key=AboutResetSettings}" Click="ResetSettings" HorizontalAlignment="Left"/>
            </StackPanel>
        <!--</ScrollViewer>-->

    </Grid>
</UserControl>
