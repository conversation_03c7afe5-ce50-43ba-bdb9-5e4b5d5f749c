﻿using Everylang.App.License.LicenseCore;
using Everylang.App.SettingsApp;
using System.Threading.Tasks;

namespace Everylang.App.License
{
    internal static class LicHelper
    {
        internal static async Task<bool> Check()
        {
            try
            {
                string licStatus = "";
                if (!string.IsNullOrEmpty(SettingsManager.Settings.LicCode) &&
                    !string.IsNullOrEmpty(SettingsManager.Settings.LicEmail))
                {
                    licStatus = await new LicenseInfo().Check(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail, false, false);
                }
                else
                {
                    if (SettingsManager.Settings.LicIsEvaluate)
                    {
                        if (!string.IsNullOrEmpty(SettingsManager.Settings.LicEvaluateCode))
                            licStatus = await new LicenseInfo().Check(SettingsManager.Settings.LicEvaluateCode, "eltrial", true, false);
                    }
                }
                return SettingsManager.LicenseModel != null && !string.IsNullOrEmpty(SettingsManager.LicenseModel.License) && !string.IsNullOrEmpty(licStatus) && Secure.DecryptStringAES(licStatus, SettingsManager.LicenseModel.License).ToLower().Contains("hol");
            }
            catch
            {
                return false;
            }

        }

        internal static async Task<bool> CheckExp()
        {
            try
            {
                string licStatus = "";
                if (!string.IsNullOrEmpty(SettingsManager.Settings.LicCode) &&
                    !string.IsNullOrEmpty(SettingsManager.Settings.LicEmail))
                {
                    licStatus = await new LicenseInfo().Check(SettingsManager.Settings.LicCode, SettingsManager.Settings.LicEmail, false, false);
                }
                else
                {
                    if (SettingsManager.Settings.LicIsEvaluate)
                    {
                        if (!string.IsNullOrEmpty(SettingsManager.Settings.LicEvaluateCode))
                            licStatus = await new LicenseInfo().Check(SettingsManager.Settings.LicEvaluateCode, "eltrial", true, false);
                    }
                }
                if (SettingsManager.LicenseModel != null && !string.IsNullOrEmpty(SettingsManager.LicenseModel.License) && !string.IsNullOrEmpty(licStatus))
                {
                    return
                        Secure.DecryptStringAES(licStatus, SettingsManager.LicenseModel.License)
                            .ToLower()
                            .Contains("censeexpir");
                }
            }
            catch
            {
                return false;
            }
            return false;
        }


    }
}
