﻿using Everylang.App.Callback;
using Everylang.App.Data;
using Everylang.Note.SettingsApp;
using Ookii.Dialogs.Wpf;
using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using Telerik.Windows.Controls;

namespace Everylang.App.SettingsApp
{
    class ExportImportSettings
    {
        internal static void Export()
        {
            try
            {
                var browserDialog = new VistaSaveFileDialog();
                browserDialog.Filter = @"Zip files (*.zip)|*.zip";
                browserDialog.RestoreDirectory = true;
                if (browserDialog.ShowDialog() == true)
                {
                    var fileZip = new FileInfo(browserDialog.FileName);
                    if (fileZip.Extension != ".zip")
                    {
                        fileZip = new FileInfo(browserDialog.FileName + ".zip");
                    }
                    if (DataBaseManager.DbPath != null)
                    {
                        DataBaseManager.Dispose();
                        SettingsMiminoteManager.Dispose();
                        var dbFolder = Path.GetDirectoryName(DataBaseManager.DbPath);
                        var dbFileEl = new FileInfo(Path.Combine(dbFolder, "eldata3.db"));
                        var dbFileNote = new FileInfo(Path.Combine(dbFolder, "everynote.db"));

                        var zip = new ZipArchive(File.Create(fileZip.FullName), ZipArchiveMode.Create, true);
                        zip.CreateEntryFromFile(dbFileEl.FullName, "eldata3.db");
                        zip.CreateEntryFromFile(dbFileNote.FullName, "everynote.db");
                        zip.Dispose();
                        DataBaseManager.Init();
                    }
                }
            }
            catch (Exception e)
            {
                RadWindow.Alert(new DialogParameters() { Content = e.Message, Header = "Error", });
            }
        }

        internal static void Import()
        {
            try
            {
                VistaOpenFileDialog browserDialog = new VistaOpenFileDialog();
                browserDialog.Filter = @"Zip files (*.zip)|*.zip";
                browserDialog.RestoreDirectory = true;
                if (browserDialog.ShowDialog() == true)
                {
                    DataBaseManager.Dispose();
                    SettingsMiminoteManager.Dispose();
                    if (DataBaseManager.DbPath != null)
                    {
                        var dbFolder = Path.GetDirectoryName(DataBaseManager.DbPath);
                        ZipArchive zip = ZipFile.OpenRead(browserDialog.FileName);
                        zip.Entries.ToList().First(x => x.Name == "eldata3.db").ExtractToFile(Path.Combine(dbFolder!, "eldata3.db"), true);
                        zip.Entries.ToList().First(x => x.Name == "everynote.db").ExtractToFile(Path.Combine(dbFolder, "everynote.db"), true);
                        zip.Dispose();
                    }

                    GlobalEventsApp.OnEventRestart(false);
                }
            }
            catch (Exception e)
            {
                RadWindow.Alert(new DialogParameters() { Content = e.Message, Header = "Error", });
            }
        }
    }

}
