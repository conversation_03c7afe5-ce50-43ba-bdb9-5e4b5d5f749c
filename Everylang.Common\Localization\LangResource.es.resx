<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>Sobre el programa</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Nueva versión disponible, reinicie la aplicación.</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Claro</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Período de prueba activado por 40 días, todas las funciones incluidas</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Cerca</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Copiar HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Copiar Rtf</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Haga clic en el número para insertar texto</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Dividir texto en carácter de nueva línea</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Dividir texto por espacio</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Salida</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Congelar ventana</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>Del idioma:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Mostrar historial</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Colapsar</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Expandir</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Sí</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Insertar</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Abra la ventana principal con traducción.</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Abra el sitio web del servicio de traducción.</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Atajo de teclado para abrir la ventana principal</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Ajustes</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Abrir lista en la ventana principal</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Atajo de teclado para desactivar todas las funciones</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Deshabilitar el programa</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Habilitar programa</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>El programa esta deshabilitado</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>no cerrar</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>En latín</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Lengua:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Traducir</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Se produjo un error durante el proceso de traducción, inténtelo nuevamente o seleccione otro servicio de traducción.</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Escuchar</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>AJUSTES</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>HISTORIA</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>PORTAPAPELES</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>DIARIO</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>FRAGMENTOS</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Activar PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Historia</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Portapapeles</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Teclas de acceso rápido</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>No se puede registrar la combinación de teclas</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Actualización disponible, reinicia la aplicación</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>La aplicación está ejecutándose y minimizada.</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>La aplicación ha sido actualizada, la lista de cambios está en el sitio web.</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Para que el programa funcione correctamente con todas las aplicaciones, debes ejecutarlo como administrador</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Ha ocurrido un error y la aplicación será cancelada. Envíe el texto con <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Su computadora tiene instalada más de una distribución de teclado para algunos idiomas, lo que puede afectar negativamente el funcionamiento correcto de la función de cambio de distribución.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Fuente</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Encontrar...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(versión PRO)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Cambiar</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Crear</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Todo</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Todos los programas</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Sólo en versión PRO</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>El programa se minimiza en la bandeja.</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Antes de utilizar el programa, lea la documentación.</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Pestaña - Buscar.  Esc - Cancelar y borrar</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Sustantivo</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronombre</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Adjetivo</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verbo</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Adverbio</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Pretexto</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Unión</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Interjección</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Comunión</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Verbo auxiliar</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>palabra introductoria</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>revisión ortográfica</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Sin errores</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Idioma no compatible</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Ha ocurrido un error</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>El texto es demasiado largo.</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Cerca</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Opciones:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Sin opciones</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Verificación completada</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Saltar</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Saltar todo</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Reemplazar</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Reemplazar todo</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Insertar texto</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Devolver</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Restablecer configuración</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Presione la combinación de teclas</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>ruso</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Inglés</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Francés</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>italiano</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ucranio</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>ACERCA DEL PROGRAMA</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Acuerdo de licencia</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Un asistente universal para trabajar con texto en diferentes idiomas.</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Versión:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Nueva versión del programa está disponible.</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>COMENTARIO</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Escribe tus comentarios y/o preguntas</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Restablecer todas las configuraciones del programa</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Abrir ventana de bienvenida</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>¿Restablecer todas las configuraciones y datos del programa?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Formulario de contacto</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>El programa se está ejecutando como administrador.</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>El programa no se ejecuta como administrador.</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>COMPONENTES</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Cambiando el diseño</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Cambiar el diseño de la última palabra</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Usar descanso</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Cambiar el diseño del texto seleccionado</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Seleccionar un método de cambio de diseño</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulación de teclas para cambiar diseños.</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Ejecutar un comando de Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Mantener el texto seleccionado después de cambiar de diseño</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Cambio de diseño habilitado</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Utilice Ctrl+(número) para cambiar a un idioma específico</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Utilice la tecla Shift de doble clic</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Cambiar desde el principio de la línea</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Utilice el botón ScrollLock de doble clic</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Cambiar diseño por botón</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Configuración del sistema</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Ctrl derecho</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Ctrl izquierdo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Desplazamiento a la derecha</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Desplazamiento a la izquierda</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Ctrl derecha o izquierda</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Mayús derecha o izquierda</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Ctrl derecho o Bloq Mayús</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Para habilitar o deshabilitar la función Bloq Mayús, presione Shift derecha e izquierda simultáneamente</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Sonido de cambio de diseño</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Configurar el sonido para cambiar de diseño</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista de idiomas a los que cambiará el diseño</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Configurar las teclas para cambiar a un idioma específico</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>¿Desactivar el cambio automático de diseño?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>¿Habilitar el cambio de diseño automático?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Cambio automático de diseño</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Cambio automático habilitado</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Corregir dos letras mayúsculas al principio de una palabra.</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Reparar la pulsación accidental de CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>No cambiar si todas las letras de una palabra están en mayúscula</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Corrija el diseño después de presionar la tecla Enter</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Agregar palabras de una letra a las reglas</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Agregar automáticamente reglas de cambio</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>No corrija el diseño si previamente se cambió manualmente</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Cambiar diseño solo después de ingresar una palabra completa</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Cambiar diseño después de dejar de escribir</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Eliminar todas las reglas de cambio automático</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Todos los diseños</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Acción</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Agregar reglas solo después de la confirmación</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Cambiar</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>no cambies</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Candidato</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Reglas de cambio automático</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista de idiomas para los que funcionará el cambio automático</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Lista de reglas para el cambio automático</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Mostrar candidatos</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Abra la lista de reglas de cambio automático</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Número de cambios manuales del diseño de palabras para su inclusión en las reglas</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>¿Agregar una palabra a las reglas de cambio automático? Entrar - SÍ</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Idioma de entrada actual en el puntero del mouse</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Idioma de entrada actual en el cursor de texto</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Ventana indicadora de idioma separada</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Idioma actual en la bandeja del sistema</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Funciones avanzadas</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Mostrar bandera del país</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Mostrar nombre de idioma</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Transparencia del indicador</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Aumentar el tamaño del indicador como porcentaje.</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Indicación del idioma actual en el teclado.</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Posición del indicador en el puntero del mouse.</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Ocultar el indicador en programas que se ejecutan en pantalla completa.</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Mostrar estado de Bloq Mayús</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>Bloq Mayús habilitado</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Posición del indicador en el cursor de texto.</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>revisión ortográfica</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Revisión ortográfica habilitada</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Revisa la ortografía mientras escribes</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Sonido del corrector ortográfico al escribir</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Utilice números para un reemplazo rápido</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Configuración de sonido para revisión ortográfica</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Atajo de teclado para comprobar la ortografía del texto seleccionado</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Revisar la ortografía del texto seleccionado</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Cierra la ventana si no hay errores después de 3 segundos.</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Administrador del portapapeles</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Pegue texto sin formato y pegue la ruta del archivo copiado</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Administrador de portapapeles habilitado</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Abrir el historial del portapapeles usando el método abreviado de teclado</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Pegar secuencialmente texto del historial del portapapeles para la ventana actual</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Pegar texto secuencialmente desde el historial del portapapeles</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Pegue texto con Ctrl+Shift+(número) - número 1, 2, 3, 4, 5, 6, 7, 8, 9 - índice de entrada en el historial del portapapeles</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Guarde la ruta al archivo copiado en el historial del portapapeles</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Guardar el historial del búfer de imágenes</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Al pegar texto del historial, reemplace el valor actual en el portapapeles</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Tamaño del historial del portapapeles</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Sonido de cambio de portapapeles</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Convertir según la distribución actual del teclado</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Convierta números y fechas en cadenas, evalúe expresiones</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Configurar teclas de acceso rápido</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Invertir mayúsculas y minúsculas del texto seleccionado</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Convertir el texto seleccionado a mayúsculas</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Texto seleccionado en minúsculas</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Ponga en minúscula el primer carácter de la palabra debajo del cursor</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Ponga en mayúscula el primer carácter de la palabra debajo del cursor</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Transliterar texto seleccionado</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Convertir texto al estilo camelCase</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Buscar y reemplazar texto en el texto seleccionado</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Enmarcar el texto seleccionado con símbolos (ejemplo)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Izquierda</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Bien</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>texto de ejemplo</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Cerrar ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Reemplazar texto ENTRAR</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Traducción</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Idioma predeterminado al que traducir</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Idioma predeterminado para traducir</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Idioma principal para la traducción</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Servicio de traducción</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Atajo de teclado para traducir el texto seleccionado</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Presione la combinación de teclas</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Hacer doble clic en el botón Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Traducir al seleccionar texto con el mouse</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Traducción incluida</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Configuraciones generales</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Idioma de la interfaz del programa</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>¿Reiniciar la aplicación para cambiar el idioma?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Misceláneas</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Minimizar a bandeja</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>corriendo desde windows</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Ejecutar con derechos de administrador</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Buscar actualizaciones</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Actualizar a la versión beta</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Sujeto</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>día o noche</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Estilos de diseño</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Carpeta para guardar la configuración del programa.</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Usar la configuración del proxy del sistema</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Introduzca la dirección del servidor</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Introducir puerto</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Ingrese su nombre de usuario</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Introduce tu contraseña</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Guardar configuración de proxy</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Configuración de proxy</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Error de configuración del servidor proxy, cambiar configuración</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Utilice un tema oscuro en las horas de la noche.</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>horas de la tarde desde</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>horario de tarde hasta</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Deshabilite todas las funciones en programas que se ejecutan en modo de pantalla completa</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Importar configuración</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Exportar configuración</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Configuraciones básicas</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Traductor</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>revisión ortográfica</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Indicador de diseño</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Funciones profesionales</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Portapapeles</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Cambiar diseños</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>cambio automático</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Programas de excepción</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Diseños predeterminados</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>Clic inteligente</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>Sobre el programa</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Licencia</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Fragmentos</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Traducir</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>revisar la ortografía</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>búsqueda de llamadas</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Abrir enlace en el navegador</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Traducir el sitio usando el enlace.</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Generando un enlace corto</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Crear un mensaje de correo</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Insertar texto</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Pegar texto sin formato</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Abrir historial del portapapeles</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Abrir historial de traducción</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>diario abierto</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Cambiar el caso del texto seleccionado</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Abrir lista de fragmentos</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Texto seleccionado en minúsculas</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Convertir el texto seleccionado a mayúsculas</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Invertir mayúsculas y minúsculas del texto seleccionado</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Enmarcar el texto seleccionado con símbolos</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Transliterar texto seleccionado</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Convierta números y fechas en cadenas, evalúe expresiones</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Convertidor</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Traducción</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Ortografía</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Buscar</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Enlace</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Traducción de sitios web</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL más corta</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>Correo electrónico</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Insertar</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Inserción sin formato.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>sin formato.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Historial de búfer</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>buffer</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Fragmentos</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Regístrese abajo</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Regístrate</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>registro invertido</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Texto enmarcado</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Transcripción</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Estera. expresiones</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Comenzar:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Fin:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Claro</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Incluido</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEXTO</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>SOLICITUD</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Programas de excepción</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Agregar archivo exe</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Agregar carpeta</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Agregar por título de ventana</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Indicador de diseño</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Cambiar diseños</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>Clic inteligente</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Fragmentos</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Teclas de acceso rápido</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Cambio automático de idioma</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Diario de entrada de texto</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Convertidor y caso de texto</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Historial del portapapeles</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Guardar imágenes</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Haga clic en el programa deseado</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Marque las funciones que funcionarán para el programa.</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Agregar de la lista de programas</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Sumar con el cursor</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Idiomas del programa</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Idioma predeterminado para programas seleccionados</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Activación de funciones PRO</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>código de activación</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Activar</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Pruébalo</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO activado</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Período de prueba durante 40 días.</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Información de licencia</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Fecha de activación:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Período de validez de la licencia:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>Correo electrónico:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Dueño:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Información de licencia</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Número de asientos:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Número de reactivaciones disponibles:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Asientos disponibles:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Tipo de licencia:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO no activado</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO activado para periodo de prueba</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Comprar</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Ingrese el correo electrónico</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Introduce el código</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>La información de su licencia ha sido enviada a:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Quitar licencia</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Introduce código y correo electrónico</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Activación completada exitosamente</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Activación completada con error.</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Activación completada con error, su licencia está bloqueada</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>El programa se activó en el nuevo lugar de trabajo, pero se desactivó en la computadora.</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Activación completada con error, verifique su conexión a Internet</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>La activación se completó con un error, es posible que se haya ingresado un correo electrónico incorrecto</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>El período de prueba ha expirado</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>La licencia ha caducado</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>La activación no es posible debido a que se excede el límite de reactivación; compre puestos adicionales para esta licencia</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>La activación no es posible debido a la reactivación de esta licencia en otra computadora</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Su código de activación ha sido actualizado</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>licencia perpetua</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Ha excedido el límite en la cantidad de puestos para la licencia, todas las funciones PRO se desactivarán</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>La versión PRO ha caducado, se puede comprar una nueva licencia en el sitio web EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>La versión PRO caduca pronto</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Se superó el límite de reactivación de licencia de los últimos 30 días, todas las funciones PRO se desactivarán</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Su licencia está bloqueada, todas las funciones PRO se desactivarán</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>Clic inteligente</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick habilitado</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Seleccione un servicio de búsqueda para SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Consulta las funciones que estarán disponibles</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Abra presionando el botón izquierdo y luego derecho del mouse.</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Abrir haciendo doble clic en el botón central del ratón</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Utilice teclas de acceso rápido para abrir</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Teclas de acceso rápido para abrir la ventana SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Mostrar ventana de ayuda después de la selección de texto</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Tamaño de ventana</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Posición de la ventana en relación con el puntero del mouse</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>diario abierto</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Diario incluido</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Contraseña del diario</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Ingrese la contraseña de su antiguo diario</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Nueva contraseña guardada</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Restablecer contraseña</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Número de entradas en el diario.</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>La contraseña ingresada es incorrecta, ¿restablecer la contraseña actual? En este caso, se eliminarán todos los datos del diario.</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Mantenga oraciones de una palabra</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Texto a reemplazar (opcional):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Fragmento de texto:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>¿A qué idioma debo cambiar el diseño?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>no cambies</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Etiquetas (separadas por espacios):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Etiquetas</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Descripción:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Mantener la posición del cursor</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Reemplazar al escribir</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Fragmentos</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Nuevo fragmento</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>¿Eliminar todos los fragmentos con esta etiqueta?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Fragmentos habilitados</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Editar fragmentos</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Que reemplazar</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>Con que reemplazar</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Cambiar</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Reemplazar al escribir en otro diseño</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Coincidir con mayúsculas y minúsculas</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Mostrar pista al escribir</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Ordenar por frecuencia de uso</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Reemplazar por:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Fragmentos habilitados</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Abrir lista de fragmentos para insertar</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Agregar texto resaltado a fragmentos</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Tecla de tabulación</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Introducir clave</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tabulador o Intro</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Espacio</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Espacio o Entrar</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Teclas de acceso rápido</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Pulsación doble de tecla</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Hacer clic en un botón del mouse</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Teclas de acceso rápido habilitadas</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Ausente</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Presione teclas de acceso rápido</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Combinación</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Doble toque</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Hacer clic en un botón del mouse</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Seleccione una tecla para presionar dos veces</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Seleccione un botón adicional del mouse</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Teclas de acceso rápido deshabilitadas</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Ctrl izquierda o derecha</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Ctrl izquierdo</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Ctrl derecho</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Desplazamiento hacia la izquierda o hacia la derecha</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Desplazamiento a la izquierda</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Desplazamiento a la derecha</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Alt izquierda</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Alt derecha</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Sonido encendido</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Selecciona un sonido</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Antes de utilizar el programa, lea la documentación.</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Explora las características del programa</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Mira el vídeo de presentación</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Sitio web del programa</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>comprar una licencia</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Reconocimiento de texto</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Atajo de teclado para iniciar OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Seleccionar idiomas predeterminados</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Seleccionar idiomas</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>No se admite el uso de idiomas europeos y asiáticos</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Idiomas europeos:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Idiomas asiáticos:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Seleccionar área de pantalla</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Abrir imagen</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Reconocer</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>reconocer código de barras</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Seleccionar idiomas</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Seleccione un área en la pantalla o cargue un archivo para su reconocimiento</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Reconocimiento de texto</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Espere, el módulo se está cargando</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Haga clic para descargar el módulo</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Texto copiado</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Texto no reconocido</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Copiar imagen</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Copiar texto</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Reemplazar con:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Buscar:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Total de coincidencias encontradas:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>¡No se encontraron coincidencias!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Reemplazos totales realizados:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>FECHA</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TIPO</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>El administrador del portapapeles está deshabilitado</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Limpiar filtro</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filtrar</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Seleccionar columnas</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Búsqueda de texto completo</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Agrupado por:</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>encabezado de grupo</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Arrastre el encabezado de una columna y suéltelo aquí para agruparlo por esta columna.</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>no vacio</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Vacío</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>no es nulo</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Nulo</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>comienza con</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Mostrar filas con valor que</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Seleccionar todo</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Distingue mayúsculas y minúsculas</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>no igual</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Menor o igual a</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Menos que</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>No contenido en</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Mayor o igual a</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Más que</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>igual</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Contenido en</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Termina con</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>no contiene</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Contiene</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>Y</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Diario incluido</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>El diario está apagado</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Incluido</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Desactivado</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Cambio de diseño deshabilitado</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Cambio de diseño automático habilitado</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>El cambio de diseño automático está deshabilitado</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Haga clic aquí para agregar un nuevo artículo</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Borrar historial de traducción</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Almacenar el historial de traducción</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Copiar texto traducido</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Borrar todo</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Abrir en el navegador</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>¿Cerrar el programa?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Idiomas destacados</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Selecciona tus idiomas favoritos</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Fuente</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Funciones</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Cargando</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Cambiar el tamaño del lienzo</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Cerca</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Corrección</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Suma</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Fondo:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Color del borde:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Grosor del borde:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Tamaño del lienzo</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>blanco</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Recortar</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Texto de imagen</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>tu texto</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Dibujar</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Color del pincel:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Tamaño del pincel:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Difuminar</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Brillo</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Contraste</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Cambiando el tono</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>invertir colores</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Saturación</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Afilar</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modificación</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Voltear horizontalmente</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Voltear verticalmente</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Tamaño de fuente</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Altura:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>posición horizontal</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Alineación de imagen</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Vista previa de la imagen</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Tamaño de imagen</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Abierto</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Opciones</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Mantener la relación de aspecto original</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Radio:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Devolver</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Tamaño relativo</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Cambiar tamaño</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Girar 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Girar 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Girar 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Doblar</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Esquinas redondeadas</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Cifra</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Elipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Cronograma</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rectángulo</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Color del borde</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Grosor del borde</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Formulario de llenado de tuberías</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Bloquear proporciones</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Cifra</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Llenando una forma</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Color del texto</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>El archivo no se puede abrir.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>El archivo no se puede abrir. Esto puede estar bloqueado por otra aplicación.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Convertir</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>No se pudo guardar el archivo.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Este formato de archivo no es compatible.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>posición vertical</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Ancho:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Restablecer todo</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Del portapapeles</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Editar imagen</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Pegar emulando entrada</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>editor de fragmentos</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Texto de reemplazo:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>El módulo de reconocimiento de texto no está cargado</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Apariencia</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Orden de funciones</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Mostrar solo los idiomas seleccionados</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Mostrar todo</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>¿Reiniciar el programa para cambiar el tema?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Para insertar texto, presione la tecla</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Mostrar notas</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>al archivo</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Añadir una nota</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Lista de notas</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Color de nota</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Convertir a nota</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Convertir a lista de tareas</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Pegar como texto normal</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Pegar como texto</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Archivo de notas</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Restaurar</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Familia de fuentes y tamaño de notas</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Transparencia de notas inactivas</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick desactivado</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Diario desactivado</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Fragmentos deshabilitados</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Mostrar botón de cierre</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Tiempo de espera para la emulación de pulsaciones de teclas</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Imagen</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Texto reconocido</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>La actualización no se completó; actualícela manualmente.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Error de actualización de EveryLang</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Etiquetado</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Incluido</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Ingrese el texto para buscar</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Del archivo</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Desactivado</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>en el archivo</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Distribución del teclado</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Caso de texto</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Mostrar estado de Bloq Num</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>Bloq Num está deshabilitado</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>Bloq Num habilitado</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Abrir una ventana con funciones de conversión.</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Mayúscula la primera letra</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Primero arriba</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Primera letra minúscula</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Primero abajo</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Abrir una ventana con funciones de cambio de caso.</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Convertir texto al estilo Snake_case</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Convertir texto al estilo kebab-case</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Convertir texto al estilo PascalCase</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Caso de texto</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Haga clic para descargar</value>
  </data>
  <data name="AutochangeSortingByAlphabet" xml:space="preserve">
    <value>Ordenar por alfabeto</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Favoritos</value>
  </data>
  <data name="RemoveFavorite" xml:space="preserve">
    <value>Eliminar de favoritos</value>
  </data>
  <data name="AddFavorite" xml:space="preserve">
    <value>Añadir a favoritos</value>
  </data>
  <data name="ClipboardFavorite" xml:space="preserve">
    <value>Clipboard favorites</value>
  </data>
</root>