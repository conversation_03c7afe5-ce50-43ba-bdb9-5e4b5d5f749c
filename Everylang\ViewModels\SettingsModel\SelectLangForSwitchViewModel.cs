﻿using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using System.Collections.Generic;
using System.Globalization;
using Telerik.Windows.Controls;

namespace Everylang.App.ViewModels.SettingsModel
{
    internal class SelectLangForSwitchViewModel : ViewModelBase
    {

        private Dictionary<string, object>? _items;
        private Dictionary<string, object?>? _selectedItems;


        internal Dictionary<string, object?>? Items
        {
            get
            {
                return _items;
            }
            set
            {
                _items = value;
                base.OnPropertyChanged();
            }
        }

        internal Dictionary<string, object?>? SelectedItems
        {
            get
            {
                return _selectedItems;
            }
            set
            {

                _selectedItems = value;
                base.OnPropertyChanged();
            }
        }

        private Dictionary<string, object?>? _itemsLangAndKeysForSwitch;
        private Dictionary<string, object?>? _selectedItemsLangAndKeysForSwitch;


        internal Dictionary<string, object?>? SelectedItemsLangAndKeysForSwitch
        {
            get
            {
                return _selectedItemsLangAndKeysForSwitch;
            }
            set
            {
                _selectedItemsLangAndKeysForSwitch = value;
                base.OnPropertyChanged();
            }
        }

        internal Dictionary<string, object?>? ItemsLangAndKeysForSwitch
        {
            get
            {
                return _itemsLangAndKeysForSwitch;
            }
            set
            {

                _itemsLangAndKeysForSwitch = value;
                base.OnPropertyChanged();
            }
        }



        public SelectLangForSwitchViewModel(string settingNotTrueLang, bool isAutoSwitch)
        {
            LoadNotTrueLang(settingNotTrueLang, isAutoSwitch);
            LoadItemsLangAndKeysForSwitch(SettingsManager.Settings.SwitcherLangAndKeysForSwitch);
        }

        internal void UpdateItemsLangAndKeysForSwitch()
        {
            base.OnPropertyChanged(nameof(SelectedItemsLangAndKeysForSwitch));
        }

        private void LoadItemsLangAndKeysForSwitch(string setting)
        {
            ItemsLangAndKeysForSwitch = new Dictionary<string, object?>();
            SelectedItemsLangAndKeysForSwitch = new Dictionary<string, object?>();

            var list = KeyboardLayoutMethods.GetInputLangs();
            foreach (var cultureInfo in list)
            {
                if (!ItemsLangAndKeysForSwitch.ContainsKey(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrl")))
                {
                    ItemsLangAndKeysForSwitch.Add(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrl"), cultureInfo?.TwoLetterISOLanguageName + "+rctrl");
                }

                if (!ItemsLangAndKeysForSwitch.ContainsKey(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLCtrl")))
                {
                    ItemsLangAndKeysForSwitch.Add(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLCtrl"), cultureInfo?.TwoLetterISOLanguageName + "+lctrl");
                }

                if (!ItemsLangAndKeysForSwitch.ContainsKey(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRShift")))
                {
                    ItemsLangAndKeysForSwitch.Add(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnRShift"), cultureInfo?.TwoLetterISOLanguageName + "+rshift");
                }

                if (!ItemsLangAndKeysForSwitch.ContainsKey(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLShift")))
                {
                    ItemsLangAndKeysForSwitch.Add(cultureInfo?.TwoLetterISOLanguageName.ToUpper() + " - " + LocalizationManager.GetString("SwitcherSettingsKeyboardSwitchOnLShift"), cultureInfo?.TwoLetterISOLanguageName + "+lshift");
                }
            }

            foreach (var o in ItemsLangAndKeysForSwitch)
            {
                var oValue = o.Value;
                if (oValue != null && setting.Contains(oValue.ToString()!))
                {
                    if (!SelectedItemsLangAndKeysForSwitch.ContainsKey(o.Key))
                        SelectedItemsLangAndKeysForSwitch.Add(o.Key, oValue);
                }
            }
        }

        private void LoadNotTrueLang(string setting, bool isAutoSwitch)
        {
            Items = new Dictionary<string, object?>();
            SelectedItems = new Dictionary<string, object?>();
            if (isAutoSwitch)
            {
                var list = KeyboardLayoutMethods.GetInputLangs();
                foreach (var cultureInfo in list)
                {
                    var listT = KeyboardLayoutMethods.GetInputLangs();
                    foreach (var cultureInfoT in listT)
                    {
                        if (cultureInfo?.TwoLetterISOLanguageName != cultureInfoT?.TwoLetterISOLanguageName)
                        {
                            var name = cultureInfo?.TwoLetterISOLanguageName.ToUpper() + "\u2192" + cultureInfoT?.TwoLetterISOLanguageName.ToUpper();
                            if (!Items.ContainsKey(name))
                                Items.Add(name, name);
                        }
                    }
                }
            }
            else
            {
                var list = KeyboardLayoutMethods.GetInputLangs();
                for (var i = 0; i < list.Count; i++)
                {
                    CultureInfo? lang = list[i];
                    CultureInfo? langT;
                    if (list.Count > i + 1)
                    {
                        langT = list[i + 1];

                    }
                    else
                    {
                        langT = list[0];
                    }
                    if (lang?.TwoLetterISOLanguageName != langT?.TwoLetterISOLanguageName)
                    {
                        var name = lang?.TwoLetterISOLanguageName.ToUpper() + "\u2192" +
                                   langT?.TwoLetterISOLanguageName.ToUpper();
                        if (!Items.ContainsKey(name))
                            Items.Add(name, name);
                    }
                }
            }

            if (!string.IsNullOrEmpty(setting))
            {
                foreach (var o in Items)
                {
                    if (o.Value != null && !setting.Contains(o.Value.ToString()!))
                    {
                        if (!SelectedItems.ContainsKey(o.Key))
                            SelectedItems.Add(o.Key, o.Value);
                    }
                }
            }
        }
    }
}
