<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Pivot.Core</name>
    </assembly>
    <members>
        <member name="T:Telerik.Pivot.Core.Aggregates.AggregateError">
            <summary>
            Represents an aggregate error.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AggregateFunctions">
            <summary>
            Describes the supported aggregate functions available for <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Sum">
            <summary>
            Computes the sum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Count">
            <summary>
            Counts items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Average">
            <summary>
            Computes the average.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Max">
            <summary>
            Computes the maximum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Min">
            <summary>
            Computes the minimum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Product">
            <summary>
            Computes the product.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.StdDev">
            <summary>
            Estimates the standard deviation of a population based on a sample.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.StdDevP">
            <summary>
            Estimates the standard deviation of a population based on the entire population.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.Var">
            <summary>
            Estimates the variance based on a sample.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunctions.VarP">
            <summary>
            Estimates the variance based on the entire population.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter">
            <summary>
            Converts instances of other types to and from <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunction"/> instances. 
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunctionConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate">
            <summary>
            Represents an aggregate that counts items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountDoubleAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.IAggregateContext">
            <summary>
            Provides initialization context for <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.IAggregateContext.DataType">
            <summary>
            Gets the type of the data item.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.IAggregateContext.HasCalculatedGroups">
            <summary>
            Gets a value that indicates if there are calculated groups with calculated <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s.
            If there are calculated groups they may store values of types different than the <see cref="P:Telerik.Pivot.Core.Aggregates.IAggregateContext.DataType"/>.
            In that case it is recommended to provide <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> that accumulate and merge <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> convertible to double.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.StringFormatSelector">
            <summary>
            Provides a way to choose a string format for a <see cref="T:Telerik.Pivot.Core.PropertyAggregateDescriptionBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.StringFormatSelector.SelectStringFormat">
            <summary>
            Select a StringFormat suitable to format the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s.
            </summary>
            <returns>A string format.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AverageAggregate">
            <summary>
            Represents an aggregate that computes the average of items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ConstantValueAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate">
            <summary>
            Represents an aggregate that computes the average of items. The sum used to compute the average is stored in a <see cref="T:System.Decimal"/> and the count in a <see cref="T:System.UInt32"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AverageDecimalAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue">
            <summary>
            Represents <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> with double value.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue"/> class.
            </summary>
            <param name="value">The default value.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.DoubleAggregateValue.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MaxIntAggregate">
            <summary>
            Represents an aggregate that computes the maximum. The minimum value is stored in a <see cref="T:System.Int64"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxIntAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxIntAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxIntAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxIntAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxIntAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MinIntAggregate">
            <summary>
            Represents an aggregate that computes the minimum. The minimum value is stored in a <see cref="T:System.Int64"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinIntAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinIntAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinIntAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinIntAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinIntAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.SumIntAggregate">
            <summary>
            Represents an aggregate that computes the sum of items. The sum is aggregated in a <see cref="T:System.Int64"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumIntAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumIntAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumIntAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumIntAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumIntAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate">
            <summary>
            Represents an aggregate that computes the maximum. The minimum value is stored in a <see cref="T:System.Decimal"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxDecimalAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate">
            <summary>
            Represents an aggregate that computes the minimum. The minimum value is stored in a <see cref="T:System.Decimal"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinDecimalAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.CountAggregate">
            <summary>
            Represents an aggregate that counts items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.CountAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AggregateFunction">
            <summary>
            Describes the supported aggregate functions available for <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/>.
            <seealso cref="T:Telerik.Pivot.Core.PropertyAggregateDescriptionBase"/>.
            <seealso cref="T:Telerik.Pivot.Core.PropertyAggregateDescription"/>.
            <seealso cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Aggregates.AggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunction.GetStringFormat(System.Type,System.String)">
            <summary>
            Gets a string format suitable to format the value of the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s produced by that <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunction"/>.
            </summary>
            <param name="dataType">The type of the data items.</param>
            <param name="format">A string format selected by other means. You may keep or discard it.</param>
            <returns>A string.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunction.CreateAggregate(System.Type)">
            <summary>
            Creates an AggregateValue supported by that AggregateFunction.
            </summary>
            <returns>An <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <summary>
            Creates an AggregateValue supported by that AggregateFunction.
            </summary>
            <param name="context">AggregateContext containing information about AggregateValue usage.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.OlapAggregateValue">
            <summary>
            Represents an aggregate that was computed by a analysis server.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.OlapAggregateValue.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.OlapAggregateValue.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.OlapAggregateValue.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.OlapAggregateValue.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.OlapAggregateValue.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MaxAggregate">
            <summary>
            Represents an aggregate that computes the maximum.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MaxAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.MinAggregate">
            <summary>
            Represents an aggregate that computes the minimum.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.MinAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.ProductAggregate">
            <summary>
            Represents an aggregate that computes the product of items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ProductAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ProductAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ProductAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ProductAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.ProductAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.StdDevAggregate">
            <summary>
            Represents an aggregate that estimates the standard deviation of a population based on a sample.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.StdDevAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.StdDevAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.StdDevPAggregate">
            <summary>
            Represents an aggregate that estimates the standard deviation of a population based on the entire population.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.StdDevPAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.StdDevPAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.SumAggregate">
            <summary>
            Represents an aggregate that computes the sum of items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate">
            <summary>
            Represents an aggregate that computes the sum of items. The sum is aggregated in a <see cref="T:System.Decimal"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.SumDecimalAggregate.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.VarAggregate">
            <summary>
            Represents an aggregate that estimates the variance based on a sample.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarAggregate.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Aggregates.VarAggregate"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.VarianceAggregateBase">
            <summary>
            Represents an abstract aggregate class helping in variance estimation.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarianceAggregateBase.AccumulateOverride(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarianceAggregateBase.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarianceAggregateBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.VarPAggregate">
            <summary>
            Represents an aggregate that estimates the variance based on the entire population.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarPAggregate.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Aggregates.VarPAggregate"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarPAggregate.GetValueOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.VarPAggregate.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.AggregateValue">
            <summary>
            Holds a value presentation of an aggregate function accumulated during pivot grouping.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Aggregates.AggregateValue.ErrorAggregateValue">
            <summary>
            Gets an <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> representing error.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.GetValue">
            <summary>
            Gets a presentation friendly value of the results in the current <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>.
            </summary>
            <returns>Returns an object containing a formatted value or error object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.GetValueOverride">
            <summary>
            Gets a presentation friendly value of the results in the current <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> instance to be returned in <see cref="M:Telerik.Pivot.Core.Aggregates.AggregateValue.GetValue"/>.
            If an error occurred during calculations the <see cref="M:Telerik.Pivot.Core.Aggregates.AggregateValue.GetValue"/> will not call <see cref="M:Telerik.Pivot.Core.Aggregates.AggregateValue.GetValueOverride"/> but return the error instead.
            </summary>
            <returns>A result object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.AccumulateOverride(System.Object)">
            <summary>
            Add the <paramref name="value"/> to the results in the current <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> instance.
            </summary>
            <param name="value">The value to accumulate.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.MergeOverride(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <summary>
            Merge the results of an <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> with the results in the current <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> instance.
            </summary>
            <param name="childAggregate">The <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> to merge.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.AggregateValue.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Aggregates.IConvertibleAggregateValue`1">
            <summary>
            Supports conversion of <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> to given <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type to convert to.</typeparam>
        </member>
        <member name="M:Telerik.Pivot.Core.Aggregates.IConvertibleAggregateValue`1.TryConvertValue(`0@)">
            <summary>
            Attempts to convert <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> to given <typeparamref name="T"/>.
            </summary>
            <param name="value">When this method returns, contains the value associated, if conversion is possible;
            otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</param>
            <returns>True if conversion succeeded, otherwise false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.AggregateValueExtensions">
            <summary>
            Extension methods for the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.AggregateValueExtensions.TryConvertValue``1(Telerik.Pivot.Core.Aggregates.AggregateValue,``0@)">
            <summary>
            Tries to convert the given aggregate value to specified type.
            </summary>
            <typeparam name="T">The type to convert to.</typeparam>
            <param name="aggregateValue">The aggregate value to convert.</param>
            <param name="value">When this method returns, contains the value associated, if conversion is possible;
            otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</param>
            <returns>True if conversion succeeded, otherwise false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.AggregateValueExtensions.ConvertOrDefault``1(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <summary>
            Convert the given aggregate value to specified type.
            </summary>
            <typeparam name="T">The type to convert to.</typeparam>
            <param name="aggregateValue">The aggregate value to convert.</param>
            <returns>The value associated, if conversion is possible;
            otherwise, the default value for the type of the value parameter.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.AggregateValueExtensions.ContainsError(System.Collections.Generic.IEnumerable{Telerik.Pivot.Core.Aggregates.AggregateValue})">
            <summary>
            Check if any of the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s contains error.
            </summary>
            <param name="aggregateValues"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.AggregateValueExtensions.IsError(Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <summary>
            Check if the value of the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> is error.
            </summary>
            <param name="aggregateValue">The aggregate value to check.</param>
            <returns>True if the actual value is <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateError"/>, otherwise false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.AverageAggregateFunction">
            <summary>
            Computes the average.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.AverageAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.CreateAggregate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AverageAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.CalculatedAggregateDescription">
            <summary>
            Class that describes the aggregation of items using <see cref="P:Telerik.Pivot.Core.CalculatedAggregateDescription.CalculatedField"/> as the criteria.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedAggregateDescription.CalculatedFieldName">
            <summary>
            Gets or sets the Name of the calculated field used in this <see cref="T:Telerik.Pivot.Core.CalculatedAggregateDescription"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedAggregateDescription.CalculatedField">
            <summary>
            Gets the <see cref="P:Telerik.Pivot.Core.CalculatedAggregateDescription.CalculatedField"/> associated with this <see cref="T:Telerik.Pivot.Core.CalculatedAggregateDescription"/> based on <see cref="P:Telerik.Pivot.Core.CalculatedAggregateDescription.CalculatedFieldName"/>.
            </summary>
            <remarks>
            This property is initialized once ItemsSource is set.
            </remarks>
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedAggregateDescription.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedAggregateDescription.GetValueForItem(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedAggregateDescription.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedAggregateDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedAggregateDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.CountAggregateFunction">
            <summary>
            Counts items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CountAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CountAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.RequiredField">
            <summary>
            Class used to describe fields required in <see cref="T:Telerik.Pivot.Core.CalculatedField"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.RequiredField.ForCalculatedField(System.String)">
            <summary>
            Creates <see cref="T:Telerik.Pivot.Core.RequiredField"/> for calculated field.
            </summary>
            <param name="calculatedFieldName">The name of the calculated field.</param>
            <returns><see cref="T:Telerik.Pivot.Core.RequiredField"/> for given property name.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.RequiredField.ForProperty(System.String)">
            <summary>
            Creates <see cref="T:Telerik.Pivot.Core.RequiredField"/> for property name and sum aggregate function.
            </summary>
            <param name="propertyName">The property name.</param>
            <returns><see cref="T:Telerik.Pivot.Core.RequiredField"/> for given property name.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.RequiredField.ForProperty(System.String,System.Object)">
            <summary>
            Creates <see cref="T:Telerik.Pivot.Core.RequiredField"/> for property name and aggregate function.
            </summary>
            <param name="propertyName">The property name.</param>
            <param name="aggregateFunction">The aggregate function.</param>
            <returns><see cref="T:Telerik.Pivot.Core.RequiredField"/> for given property name and aggregate function.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.RequiredField.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.RequiredField.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.IAggregateDescription">
            <summary>
            Specify the set of properties and methods that a AggregateDescription should implement.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IAggregateDescription.TotalFormat">
            <summary>
            Get the TotalFormat.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IAggregateDescription.DisplayValueAsKpi">
            <summary>
            Gets a value indicating whether aggregate values should be interpreted as KPIs.
            </summary>
            <value>
              <c>true</c> if values will be interpreted as KPIs; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Pivot.Core.LocalAggregateDescription">
            <summary>
            Base class that describes the aggregation of items using a property name as the criteria.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalAggregateDescription.StringFormat">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.LocalAggregateDescription.StringFormatSelector">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalAggregateDescription.GetValueForItem(System.Object)">
            <summary>
            Returns the value that will be passed in the aggregate for given item.
            </summary>
            <param name="item">The item which value will be extracted.</param>
            <returns>Returns the value for given item.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.LocalAggregateDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.MaxAggregateFunction">
            <summary>
            Computes the maximum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.MaxAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MaxAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.MinAggregateFunction">
            <summary>
            Computes the minimum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.MinAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.MinAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.NumericFormatAggregateFunction">
            <summary>
            Base class for generic <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunctions"/> that preserve the meaning of the underlying data.
            It provides a basic functionality to select default string formats.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.NumericFormatAggregateFunction.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.ProductAggregateFunction">
            <summary>
            Computes the product.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ProductAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.ProductAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyAggregateDescriptionBase">
            <summary>
            Base class that describes the aggregation of items using a property name as the criteria.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.PropertyName">
            <summary>
            Gets or sets a value identifying a property on the grouped items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.AggregateFunction">
            <summary>
            Gets or sets the aggregate function that will be used for summary calculation.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.IgnoreNullValues">
            <summary>
            Gets or sets a value that determines whether the <see cref="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.AggregateFunction"/>s of this <see cref="T:Telerik.Pivot.Core.PropertyAggregateDescriptionBase"/> will ignore null values when calculating the result.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.DataType">
            <summary>
            Provides the data type of the aggregate description.
            </summary>  
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.SupportedAggregateFunctions">
            <summary>
            Gets a list of suitable functions for the <see cref="T:Telerik.Pivot.Core.PropertyAggregateDescriptionBase"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.GetValueForItem(System.Object)">
            <summary>
            Returns the value that will be passed in the aggregate for given item.
            </summary>
            <param name="item">The item which value will be extracted.</param>
            <returns>Returns the value for given item.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescriptionBase.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <summary>
            Makes the instance a clone (deep copy) of the specified <see cref="T:Telerik.Pivot.Core.Cloneable"/>.
            </summary>
            <param name="source">The object to clone.</param>
            <remarks>Notes to Inheritors
            If you derive from <see cref="T:Telerik.Pivot.Core.Cloneable"/>, you need to override this method to copy all properties.
            It is essential that all implementations call the base implementation of this method (if you don't call base you should manually copy all needed properties including base properties).
            </remarks>
        </member>
        <member name="T:Telerik.Pivot.Core.StatisticalFormatAggregateFunction">
            <summary>
            Base class for generic statistical <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunctions"/>.
            It provides a basic functionality to select default string formats.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.StatisticalFormatAggregateFunction.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.StdDevAggregateFunction">
            <summary>
            Estimates the standard deviation of a population based on a sample.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.StdDevAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.CreateAggregate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.StdDevPAggregateFunction">
            <summary>
            Estimates the standard deviation of a population based on the entire population.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.StdDevPAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.CreateAggregate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.StdDevPAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.SumAggregateFunction">
            <summary>
            Computes the sum.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.SumAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.CreateAggregate(Telerik.Pivot.Core.Aggregates.IAggregateContext)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SumAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.VarAggregateFunction">
            <summary>
            Estimates the variance based on a sample.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.VarAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.CreateAggregate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.VarPAggregateFunction">
            <summary>
            Estimates the variance based on the entire population.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.VarPAggregateFunction.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.CreateAggregate(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.VarPAggregateFunction.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.IAggregateValues">
            <summary>
            Expose method to get aggregate value based on <see cref="T:Telerik.Pivot.Core.RequiredField"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IAggregateValues.Coordinate">
            <summary>
            Gets the coordinate for which an aggregate value is requested.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IAggregateValues.GetAggregateValue(Telerik.Pivot.Core.RequiredField)">
            <summary>
            Gets an aggregate value for given <see cref="T:Telerik.Pivot.Core.RequiredField"/>.
            </summary>
            <param name="calculatedFieldSettings">The calculated field settings which aggregate value is requested.</param>
            <returns>The aggregate value for this calculated field.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.CalculatedField">
            <summary>
            Represents an abstraction of a calculated field.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedField.Name">
            <summary>
            Gets the name of the calculated field.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedField.DisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedField.RequiredFields">
            <summary>
            Gets all fields used in calculation.
            </summary>
            <returns>Enumerable of all property names used in calculation.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedField.CalculateValue(Telerik.Pivot.Core.IAggregateValues)">
            <summary>
            Gets the calculated value.
            </summary>
            <param name="aggregateValues">Interface used to get summary aggregate values for all properties returned by <see cref="M:Telerik.Pivot.Core.CalculatedField.RequiredFields"/> method.</param>
            <returns>The calculated values.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.RunningTotalSubGroupVariation">
            <summary>
            Specifies which sibling values should be grouped.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Totals.RunningTotalSubGroupVariation.ParentAndSelfNames">
            <summary>
            Totals that have equal names for them and their parent groups are considered siblings.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Totals.RunningTotalSubGroupVariation.GroupDescriptionAndName">
            <summary>
            Totals that have equal names and are generated for the same <see cref="T:Telerik.Pivot.Core.GroupDescriptionBase"/> are considered siblings.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.TotalFormat">
            <summary>
            A base class for all total formats. For internal use. Please refer to one of the <see cref="T:Telerik.Pivot.Core.Totals.SingleTotalFormat"/> or <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> instead.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.TotalFormat.GetStringFormat(System.Type,System.String)">
            <summary>
            Gets a string format suitable to form the produced <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s by this <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/>.
            </summary>
            <param name="dataType">The type of the data items.</param>
            <param name="stringFormat">A string format selected by other means. You may keep or discard it.</param>
            <returns>A string.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.ComparedTo">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that is used for all totals that compare a <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to other <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> from the totals or the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s sibling list.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.ComparedTo.FormatTotals(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.Totals.TotalValue},Telerik.Pivot.Core.IAggregateResultProvider)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.DifferenceFrom">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that is used for all totals that compute a <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> as a difference from other <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> specified by <see cref="P:Telerik.Pivot.Core.Totals.DifferenceFrom.GroupName"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.DifferenceFrom.GroupName">
            <summary>
            Gets or sets the name of the <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> used for comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.DifferenceFrom.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.DifferenceFrom.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.DifferenceFromBase">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that is used for all totals that compute a <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> as a difference from other <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> from the totals or the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s sibling list.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.DifferenceFromBase.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.DifferenceFromNext">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that compute the difference for each TotalValue from its next sibling.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.DifferenceFromNext.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.DifferenceFromPrevious">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that compute the difference for each TotalValue from its previous sibling.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.DifferenceFromPrevious.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.SingleTotalFormat">
            <summary>
            Formats the aggregate value based on its own value and some relative values such as row/column subtotals or grand totals.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.SingleTotalFormat.FormatValue(Telerik.Pivot.Core.Coordinate,Telerik.Pivot.Core.IAggregateResultProvider,System.Int32)">
            <summary>
            Formats the value located at the <paramref name="groups"/> <see cref="T:Telerik.Pivot.Core.Coordinate"/>. The current value could be retrieved from the <paramref name="results"/>.
            </summary>
            <param name="groups">The <see cref="T:Telerik.Pivot.Core.Coordinate"/> for the formatted value.</param>
            <param name="results">The current results in the pivot grouping.</param>
            <param name="aggregateIndex">The index of the aggregate description we are formatting value for.</param>
            <returns>The formatted value.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.Index">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SingleTotalFormat"/> that computes the 'Index' for the pivot grouping values. The index is computed by the following formula: (aggregateValue * grandTotalValue) / (rowTotalValue * columnTotalValue).
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.FormatValue(Telerik.Pivot.Core.Coordinate,Telerik.Pivot.Core.IAggregateResultProvider,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.Index.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentDifferenceFrom">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that is used for all totals that compute a <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> as a difference from other <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> and computes the difference as percent from the previous difference.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.PercentDifferenceFrom.GroupName">
            <summary>
            Gets or sets the name of the <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> used for comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentDifferenceFrom.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentDifferenceFrom.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentDifferenceFromBase">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that is used for all totals that compute a <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> as a difference from other <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> and computes the difference as percent from the previous difference.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentDifferenceFromBase.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentDifferenceFromNext">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> for all 'percent-difference' <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentDifferenceFromNext.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentDifferenceFromPrevious">
            <summary>
            A base class <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that calculate the percent of the difference for each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> of its previous sibling.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentDifferenceFromPrevious.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOf">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that formats each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to show the percent of the group with <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> = <see cref="P:Telerik.Pivot.Core.Totals.PercentOf.GroupName"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.PercentOf.GroupName">
            <summary>
            Gets or sets the name which will be used to find a base value for comparison for all sibling groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOf.Format(System.Nullable{System.Double},System.Nullable{System.Double},System.Int32,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOf.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOf.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfAncestor">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SingleTotalFormat"/> that is base class for all "percent of" formats.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfAncestor.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfAncestor.FormatValue(Telerik.Pivot.Core.Coordinate,Telerik.Pivot.Core.IAggregateResultProvider,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfAncestor.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfBase">
            <summary>
            A base class for all <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that show 'percent-of'.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfBase.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfBase.Format(System.Nullable{System.Double},System.Nullable{System.Double},System.Int32,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfColumnTotal">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that formats each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to show the percent of the column total.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfColumnTotal.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfColumnTotal.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfColumnTotal.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfGrandTotal">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SingleTotalFormat"/> that formats each total as the percent of the grand total.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfGrandTotal.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfGrandTotal.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfGrandTotal.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfGrandTotal.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfNext">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that formats each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to show the percent of the next sibling.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfNext.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfPrevious">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that formats each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to show the percent of the previous sibling.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfPrevious.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentOfRowTotal">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that formats each <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to show the percent of the row total.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfRowTotal.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfRowTotal.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentOfRowTotal.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.PercentRunningTotalsIn">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that computes running totals and then computes the percent of these running totals from the grand total.
            <example>For example if you group by <see cref="T:System.DateTime"/> and use <see cref="T:Telerik.Pivot.Core.Totals.RunningTotalsIn"/> to on that groups the results will show how the values sum up in time. The values will show the percent of the so accumulated values from the grand total.</example>
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentRunningTotalsIn.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentRunningTotalsIn.FormatTotals(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.Totals.TotalValue},Telerik.Pivot.Core.IAggregateResultProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.PercentRunningTotalsIn.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.RankTotals">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that rank totals by sorting the totals using the <see cref="P:Telerik.Pivot.Core.Totals.RankTotals.Comparer"/> and then uses the indices of the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s in the sorted list for <see cref="P:Telerik.Pivot.Core.Totals.TotalValue.FormattedValue"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.RankTotals.SortOrder">
            <summary>
            A <see cref="P:Telerik.Pivot.Core.Totals.RankTotals.SortOrder"/> used to rank the total values.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.RankTotals.Comparer">
            <summary>
            The comparer used to sort the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RankTotals.GetStringFormat(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RankTotals.FormatTotals(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.Totals.TotalValue},Telerik.Pivot.Core.IAggregateResultProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RankTotals.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RankTotals.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RankTotals.SubVariation">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat">
            <summary>
            Formats the aggregate values based on the values for its siblings identified by <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Axis"/> and <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Level"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Axis">
            <summary>
            The axis for which siblings are compared.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Level">
            <summary>
            The level at which siblings are compared.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.FormatTotals(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.Totals.TotalValue},Telerik.Pivot.Core.IAggregateResultProvider)">
            <summary>
            Gets a read only collection of the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s for all siblings at the <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Level"/> and <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Axis"/>. Based on the <see cref="P:Telerik.Pivot.Core.Totals.TotalValue.Value"/>s the <see cref="P:Telerik.Pivot.Core.Totals.TotalValue.FormattedValue"/> should be set.
            </summary>
            <param name="valueFormatters">A read only list of the <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s for all siblings at the <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Level"/> and <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Axis"/>.</param>
            <param name="results">The <see cref="T:Telerik.Pivot.Core.IAggregateResultProvider"/> with the current pivot grouping results.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.SubVariation">
            <summary>
            Gets the type of the variation for the groups deeper than the <see cref="P:Telerik.Pivot.Core.Totals.SiblingTotalsFormat.Level"/>.
            </summary>
            <returns>The <see cref="T:Telerik.Pivot.Core.Totals.RunningTotalSubGroupVariation"/> type.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.RunningTotalsIn">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Totals.SiblingTotalsFormat"/> that computes running totals.
            <example>For example if you group by <see cref="T:System.DateTime"/> and use <see cref="T:Telerik.Pivot.Core.Totals.RunningTotalsIn"/> to on that groups the results will show how the values sum up in time.</example>
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RunningTotalsIn.FormatTotals(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.Totals.TotalValue},Telerik.Pivot.Core.IAggregateResultProvider)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.RunningTotalsIn.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.TotalComparer">
            <summary>
            A base class TotalComparers. One could be used with <see cref="T:Telerik.Pivot.Core.Totals.RankTotals"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Totals.TotalComparer.Compare(Telerik.Pivot.Core.Totals.TotalValue,Telerik.Pivot.Core.Totals.TotalValue)">
            <summary>
            Compares the two <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/>s. The results should be as the result from <see cref="T:System.Collections.Generic.IComparer`1"/>.
            </summary>
            <param name="x">The first <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to compare.</param>
            <param name="y">The second <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> to compare.</param>
            <returns>A signed integer that indicates the relative values of x and y, as shown: Value Meaning Less than zero - x is less than y. Zero - x equals y. Greater than zero - x is greater than y.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Totals.TotalValue">
            <summary>
            Used to format an <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> for a <see cref="T:Telerik.Pivot.Core.Coordinate"/> in a pivot grouping.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.TotalValue.Groups">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Coordinate"/> this <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> is responsible for.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.TotalValue.FormattedValue">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> that should replace the <see cref="P:Telerik.Pivot.Core.Totals.TotalValue.Value"/> in the final result.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Totals.TotalValue.Value">
            <summary>
            Gets and caches the value for <see cref="P:Telerik.Pivot.Core.Totals.TotalValue.Groups"/> from the <see cref="T:Telerik.Pivot.Core.IAggregateResultProvider"/> this <see cref="T:Telerik.Pivot.Core.Totals.TotalValue"/> is generated for.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.AggregateDescriptionBase">
            <summary>
            Represents a base type for aggregate description.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.AggregateDescriptionBase.TotalFormat">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> used to format the generated aggregate values.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.AggregateDescriptionBase.DisplayValueAsKpi">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.AggregateDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyAggregateDescription">
            <summary>
            Describes the aggregation of items using a property name as the criteria.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PropertyAggregateDescription" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyAggregateDescription.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Cloneable">
            <summary>
            Defines an object that has a modifiable state and a read-only state. Classes that derive from <see cref="T:Telerik.Pivot.Core.Cloneable"/> can clone themselves. 
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Cloneable.Clone">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Pivot.Core.Cloneable"/>, making deep copies of the object's values.
            </summary>
            <returns>A clone of the current object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Cloneable.CreateInstanceCore">
            <summary>
            When implemented in a derived class, creates a new instance of the <see cref="T:Telerik.Pivot.Core.Cloneable"/> derived class. 
            </summary>
            <returns>New instance for cloning.</returns>
            <remarks>Do not call this method directly (except when calling base in an implementation). This method is called internally by the <see cref="M:Telerik.Pivot.Core.Cloneable.Clone"/> method whenever a new instance of the <see cref="T:Telerik.Pivot.Core.Cloneable"/> is created.
            Notes to Inheritors.
            Every <see cref="T:Telerik.Pivot.Core.Cloneable"/> derived class must implement this method. A typical implementation is to simply call the default constructor and return the result. 
            </remarks>
        </member>
        <member name="M:Telerik.Pivot.Core.Cloneable.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <summary>
            Makes the instance a clone (deep copy) of the specified <see cref="T:Telerik.Pivot.Core.Cloneable"/>.
            </summary>
            <param name="source">The object to clone.</param>
            <remarks>Notes to Inheritors
            If you derive from <see cref="T:Telerik.Pivot.Core.Cloneable"/>, you may need to override this method to copy all properties.
            It is essential that all implementations call the base implementation of this method (if you don't call base you should manually copy all needed properties including base properties).
            </remarks>
        </member>
        <member name="M:Telerik.Pivot.Core.Cloneable.CloneOrDefault``1(``0)">
            <summary>
            If source is null - returns default(<typeparamref name="T"/>).
            If source is not null makes a copy of type <typeparamref name="T"/>.
            If the copy is from a different type throws appropriate exception.
            </summary>
            <typeparam name="T">The expected copy type.</typeparam>
            <param name="source">The source that is about to be copied.</param>
            <returns>Clone of <paramref name="source"/> of type <typeparamref name="T"/>. If source is null - default(<typeparamref name="T"/>).</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.DataProviderBase">
            <summary>
            Base implementation of <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.
            </summary> 
        </member>
        <member name="E:Telerik.Pivot.Core.DataProviderBase.StatusChanged">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Pivot.Core.DataProviderBase.PropertyChanged">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Pivot.Core.DataProviderBase.PrepareDescriptionForField">
            <inheritdoc />
        </member>
        <member name="E:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#StatusChanged">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.FieldInfos">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.DeferUpdates">
            <summary>
            Gets or sets a value that indicates if changes to this <see cref="T:Telerik.Pivot.Core.IDataProvider"/> will trigger automatic <see cref="M:Telerik.Pivot.Core.DataProviderBase.Refresh"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Status">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.AggregatesPosition">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.AggregatesLevel">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.FieldDescriptionsProvider">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.HasPendingChanges">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Results">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Settings">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.State">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#Status">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#Refresh">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#Results">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#Settings">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#AggregatesPosition">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderBase.Telerik#Pivot#Core#IDataProvider#AggregatesLevel">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.BeginInit">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.EndInit">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.Refresh">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.BlockUntilRefreshCompletes">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.DeferRefresh">
            <summary>
            Enters a defer cycle that you can use to merge changes to the provider and delay automatic refresh.
            </summary>
            <returns>An <see cref="T:System.IDisposable"/> object that you can use to dispose of the calling object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.Invalidate">
            <summary>
            Notify that changes were applied that would alter the pivot results.
            Queues an automatic <see cref="M:Telerik.Pivot.Core.DataProviderBase.Refresh"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.RefreshOverride">
            <summary>
            Recreates the <see cref="P:Telerik.Pivot.Core.DataProviderBase.Results" />.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.OnStatusChanged(Telerik.Pivot.Core.DataProviderStatusChangedEventArgs)">
            <summary>
            Raises the <see cref="E:StatusChanged" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.OnPrepareDescriptionForField(Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs)">
            <summary>
            Raises the <see cref="E:PrepareDescriptionForField" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.OnFieldDescriptionsProviderChanged(Telerik.Pivot.Core.Fields.IFieldDescriptionProvider,Telerik.Pivot.Core.Fields.IFieldDescriptionProvider)">
            <summary>
            Called when FieldDescriptionsProvider is changed.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.CreateFieldDescriptionsProvider">
            <summary>
            Creates an instance of <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> for this <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.OnPropertyChanged(System.String)">
            <summary>
            Raises PropertyChanged event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetAggregateDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Creates and returns an aggregate description suitable for the supplied field description.
            </summary>
            <param name="description">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetGroupDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Creates and returns a group description suitable for the supplied field description.
            </summary>
            <param name="description">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.IGroupDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetFilterDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Returns a filter description suitable for the supplied field description.
            </summary>
            <param name="description">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.FilterDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetAggregateDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetGroupDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetFilterDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.SetAggregateFunctionToAggregateDescription(Telerik.Pivot.Core.IAggregateDescription,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderBase.FreezeCore(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.DataProviderDescriptionType">
            <summary>
            Specifies description type. 
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderDescriptionType.Group">
            <summary>
            Identifies a group description.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderDescriptionType.Filter">
            <summary>
            Identifies a filter description.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderDescriptionType.Aggregate">
            <summary>
            Identifies an aggregate description.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs">
            <summary>
            Event data for the <see cref="E:Telerik.Pivot.Core.IDataProvider.StatusChanged"/> event of all <see cref="T:Telerik.Pivot.Core.IDataProvider"/> types.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs.#ctor(Telerik.Pivot.Core.DataProviderStatus,Telerik.Pivot.Core.DataProviderStatus,System.Boolean,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs" /> class.
            </summary>
            <param name="oldStatus">The old status.</param>
            <param name="newStatus">The new status.</param>
            <param name="resultsChanges">DataProvider results have changed if set to <c>true</c>.</param>
            <param name="error">Exception if available .</param>
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs.Error">
            <summary>
            Gets or sets the error.
            </summary>
            <value>The error.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs.OldStatus">
            <summary>
            Gets the old status.
            </summary>
            <value>
            The new status.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs.NewStatus">
            <summary>
            Gets the new status.
            </summary>
            <value>
            The new status.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.DataProviderStatusChangedEventArgs.ResultsChanged">
            <summary>
            Gets a value indicating whether the results of the data provider have changed.
            </summary>
            <value>
              <c>true</c> if results have changed; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Telerik.Pivot.Core.DataProviderStatus">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.IDataProvider"/> status.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderStatus.Uninitialized">
            <summary>
            The provider is in uninitialized state.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderStatus.Initializing">
            <summary>
            The provider is initializing.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderStatus.Ready">
            <summary>
            The provider is ready for data processing.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderStatus.RetrievingData">
            <summary>
            The provider is currently retrieving data.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DataProviderStatus.Faulted">
            <summary>
            The provider has failed.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.IDataProvider">
            <summary>
            Provides data access for pivot grouping.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IDataProvider.StatusChanged">
            <summary>
            Occurs when the current operation has completed.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IDataProvider.PrepareDescriptionForField">
            <summary>
            Occurs when description should be prepared for a specified field info.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.Status">
            <summary>
            Gets the status of this instance.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.Results">
            <summary>
            Gets the results from the last grouping.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.Settings">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.IPivotSettings"/> instance that is being used.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.FieldInfos">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Fields.IFieldInfoData"/> instance that provided information for all available properties of the data source.
            </summary>
            <value>
            The field information.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.AggregatesPosition">
            <summary>
            Gets or sets a value indicating where the aggregate groups should be positioned.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.AggregatesLevel">
            <summary>
            Gets or sets the position where groups for the aggregates should be placed.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.State">
            <summary>
            Gets the state object that is provided to <see cref="M:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.GetDescriptionsDataAsync(System.Object)"/> method.
            </summary>
            <returns>The object that will be passed to <see cref="M:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.GetDescriptionsDataAsync(System.Object)"/> method.</returns>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.DeferUpdates">
            <summary>
            Gets or sets a property that indicates if changes to the grouping settings would trigger computations immediately when invalidated or on explicit <see cref="M:Telerik.Pivot.Core.IDataProvider.Refresh"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IDataProvider.HasPendingChanges">
            <summary>
            Gets a value that indicates if there are pending changes since the last <see cref="M:Telerik.Pivot.Core.IDataProvider.Refresh"/>.
            The value will be true after a change is applied.
            The value will be false after an automatic or user triggered <see cref="M:Telerik.Pivot.Core.IDataProvider.Refresh"/>.
            The value will be false during any work or download process so even if false <see cref="P:Telerik.Pivot.Core.IDataProvider.Results"/> may not be ready yet.
            In that case you may check <see cref="P:Telerik.Pivot.Core.IDataProvider.Status"/> for additional information.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.Refresh">
            <summary>
            Force recalculation operation.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.BlockUntilRefreshCompletes">
            <summary>
            Block the calling thread until all calculations performed by calling <see cref="M:Telerik.Pivot.Core.IDataProvider.Refresh"/> method completes.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.DeferRefresh">
            <summary>
            Enters a defer cycle that you can use to merge changes to the data provider and delay automatic refresh.
            </summary>
            <returns>An IDisposable object that you can use to dispose of the calling object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.GetAggregateDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Creates and returns an aggregate description suitable for the supplied field description.
            </summary>
            <param name="info">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.GetGroupDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Creates and returns a group description suitable for the supplied field description.
            </summary>
            <param name="info">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.IGroupDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.GetFilterDescriptionForFieldDescription(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Returns a filter description suitable for the supplied field description.
            </summary>
            <param name="info">A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance.</param>
            <returns>An <see cref="T:Telerik.Pivot.Core.FilterDescription"/> instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)">
            <summary>
            Returns a list of suitable functions for the supplied aggregate description.
            </summary>
            <param name="aggregateDescription">The <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/>.</param>
            <returns>A list of possible aggregate functions.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IDataProvider.SetAggregateFunctionToAggregateDescription(Telerik.Pivot.Core.IAggregateDescription,System.Object)">
            <summary>
            Set the <paramref name="aggregateFunction"/> retrieved from <see cref="M:Telerik.Pivot.Core.IDataProvider.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)"/> to the <paramref name="aggregateDescription"/>.
            </summary>
            <param name="aggregateDescription">The <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/>.</param>
            <param name="aggregateFunction">The aggregate function.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.IUnderlyingDataProvider">
            <summary>
            Provides the underlying data behind a pivot grouping.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IUnderlyingDataProvider.GetUnderlyingDataCompleted">
            <summary>
            Occurs when the underlying data extraction has completed.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IUnderlyingDataProvider.GetUnderlyingData(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)">
            <summary>
            Executes underlying data extraction for the specified row and column <see cref="T:Telerik.Pivot.Core.IGroup"/>s.
            </summary>
            <param name="rowGroup">The <see cref="T:Telerik.Pivot.Core.IGroup"/> for rows.</param>
            <param name="columnGroup">The <see cref="T:Telerik.Pivot.Core.IGroup"/> for columns.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.IPivotSettings">
            <summary>
            Represents an interface for controlling pivot settings like group descriptions, aggregate descriptions, etc.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IPivotSettings.DescriptionsChanged">
            <summary>
            An event that notifies some of the
            <see cref="P:Telerik.Pivot.Core.IPivotSettings.FilterDescriptions"/>,
            <see cref="P:Telerik.Pivot.Core.IPivotSettings.RowGroupDescriptions"/>,
            <see cref="P:Telerik.Pivot.Core.IPivotSettings.ColumnGroupDescriptions"/>,
            <see cref="P:Telerik.Pivot.Core.IPivotSettings.AggregateDescriptions"/>,
            <see cref="P:Telerik.Pivot.Core.IPivotSettings.AggregatesLevel"/>
            or <see cref="P:Telerik.Pivot.Core.IPivotSettings.AggregatesPosition"/> has changed.
            Notifications are raised even in <see cref="M:Telerik.Pivot.Core.IPivotSettings.BeginEdit"/> scope.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IPivotSettings.SettingsChanged">
            <summary>
            Notifies when this or one of the children is changed.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.FilterDescriptions">
            <summary>
            Gets the pivot filter descriptions list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.RowGroupDescriptions">
            <summary>
            Gets the pivot row group descriptions list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.ColumnGroupDescriptions">
            <summary>
            Gets the pivot column group descriptions list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.AggregateDescriptions">
            <summary>
            Gets the pivot aggregate descriptions list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.AggregatesLevel">
            <summary>
            Gets or sets the position where groups for the aggregates should be placed.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotSettings.AggregatesPosition">
            <summary>
            Gets or sets a value indicating where the aggregate groups should be positioned.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IPivotSettings.BeginEdit">
            <summary>
            Enters the <see cref="T:Telerik.Pivot.Core.IPivotSettings"/> in a new editing scope.
            Use when applying multiple changes to the pivot settings.
            <example>
            using(pivotSettings.BeginEdit())
            {
                // Apply multiple changes to pivotSettings here.
            }
            </example>
            </summary>
            <returns>An edit scope token that you must <see cref="M:System.IDisposable.Dispose"/> when you are done with the editing.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.LocalDataSourceProvider">
            <summary>
            Provides a pivot grouping access to local source such as an IList of instances of user defined classes.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.LocalDataSourceProvider.GetUnderlyingDataCompleted">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.Culture">
            <summary>
            Gets or sets the CultureInfo used for grouping and formatting.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.EnableHierarchy">
            <summary>
            Gets or sets a boolean value indicating whether nested properties of the objects in the ItemsSource will be displayed in a hierarchy.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.FilterDescriptions">
            <summary>
            Gets a list of <see cref="T:Telerik.Pivot.Core.PropertyFilterDescriptionBase"/> that specified how pivot should filter items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.RowGroupDescriptions">
            <summary>
            Gets a list of <see cref="T:Telerik.Pivot.Core.PropertyGroupDescriptionBase"/> that specified how pivot should group rows.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.ColumnGroupDescriptions">
            <summary>
            Gets a list of <see cref="T:Telerik.Pivot.Core.PropertyGroupDescriptionBase"/> that specify how pivot should group columns.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.AggregateDescriptions">
            <summary>
            Gets a list of <see cref="T:Telerik.Pivot.Core.PropertyAggregateDescription"/> that specify how pivot should aggregate data.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.CalculatedFields">
            <summary>
            Gets a list of <see cref="T:Telerik.Pivot.Core.CalculatedField"/>s that can be used as a calculated aggregate values.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.Results">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.State">
            <inheritdoc />
        </member>
        <member name="F:Telerik.Pivot.Core.LocalDataSourceProvider.ItemsSourceProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Pivot.Core.LocalDataSourceProvider.ItemsSource"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.LocalDataSourceProvider.ItemsSource">
            <summary>
            The item source for the grouping.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.GetUnderlyingData(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.BlockUntilRefreshCompletes">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.SetAggregateFunctionToAggregateDescription(Telerik.Pivot.Core.IAggregateDescription,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.GetAggregateFunctionsForAggregateDescription(Telerik.Pivot.Core.IAggregateDescription)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.RefreshOverride">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.CreateFieldDescriptionsProvider">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.GetFilterDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.GetAggregateDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.LocalDataSourceProvider.GetGroupDescriptionForFieldDescriptionCore(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PivotSettings`3.DescriptionsMap">
            <summary>
            Description position and index map build based on two description snapshots.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotSettings`3.DescriptionsSettingsList`1">
            <summary>
            SettingsNodeCollection with notification rerouting.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs">
            <summary>
            Provides data for the <see cref="E:PrepareDescriptionForField" /> event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs.#ctor(Telerik.Pivot.Core.Fields.IPivotFieldInfo,Telerik.Pivot.Core.IDescriptionBase,Telerik.Pivot.Core.DataProviderDescriptionType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs" /> class.
            </summary>
            <param name="fieldInfo">The field info for which description should be prepared.</param>
            <param name="description">Default description instance.</param>
            <param name="descriptionType">Type of description that should be prepared.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs.FieldInfo">
            <summary>
            Gets the field info for which description should be prepared.
            </summary>
            <value>
            The field info.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs.DescriptionType">
            <summary>
            Gets the type of the description that should be prepared.
            </summary>
            <value>
            The type of the description.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.PrepareDescriptionForFieldEventArgs.Description">
            <summary>
            Gets or sets the description that will be passed to <see cref="T:Telerik.Pivot.Core.IDataProvider"/>. 
            This property is initialized with the default description for the specified field info.
            </summary>
            <value>
            The description.
            </value>
        </member>
        <member name="T:Telerik.Pivot.Core.DescriptionBase">
            <summary>
            Contains mechanisms to access and describe properties of objects used as source in pivot grouping.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DescriptionBase.DisplayName">
            <summary>
            Gets the display-friendly name.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DescriptionBase.CustomName">
            <summary>
            Gets or sets the custom name that will be used as display name.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DescriptionBase.GetDisplayName">
            <summary>
            Gets the display-friendly name.
            </summary>
            <returns>A <see cref="T:System.String"/> name.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DescriptionBase.Telerik#Pivot#Core#IDescriptionBase#Clone">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DescriptionBase.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.ItemsFilterCondition">
            <summary>
            Condition which is used to filter items based on two other conditions. 
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.ItemsFilterCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.DistinctCondition">
            <summary>
            Gets or sets a <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition"/> used to filter the items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.Condition">
            <summary>
            Gets or sets a <see cref="P:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.Condition"/> used to filter the items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ItemsFilterCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.ComparisonCondition">
            <summary>
            A class that filters based on two comparable objects.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ComparisonCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ComparisonCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ComparisonCondition.IgnoreCase">
            <summary>
            Gets or sets the value of ignore case in comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ComparisonCondition.Than">
            <summary>
            Gets or sets the value that the groups would be compared to.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ComparisonCondition.Condition">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ComparisonCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ComparisonCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ComparisonCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.LocalCondition">
            <summary>
            Base class used in filtering.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.LocalCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.Comparison">
            <summary>
            A list of all possible filtering conditions used in <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/>.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.Equals">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value equals the comparison value.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.DoesNotEqual">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value does not equals the comparison value.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.IsGreaterThan">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value is greater than the comparison value.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.IsGreaterThanOrEqualTo">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value is greater than or equal the comparison value.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.IsLessThan">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value is less than the comparison value.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.Comparison.IsLessThanOrEqualTo">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.ComparisonCondition"/> checks if the compared value is less than or equal to the comparison value.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.Condition">
            <summary>
            Base class used in filtering.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.Condition.IsActive">
            <summary>
            Returns a value indicating whether this instance is active and will take part in filtering.
            </summary>
            <value>Is active.</value>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.IntervalComparison">
            <summary>
            A list of all possible filtering conditions used in <see cref="T:Telerik.Pivot.Core.Filtering.IntervalCondition"/>.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.IntervalComparison.IsBetween">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.IntervalCondition"/> that checks if a value is within an interval.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.IntervalComparison.IsNotBetween">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Filtering.IntervalCondition"/> that checks if a value is outside an interval.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.SetCondition">
            <summary>
            Filter that checks if items are included/excluded from a set.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SetCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.SetCondition.Comparison">
            <summary>
            Gets or sets the filter condition.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.SetCondition.Items">
            <summary>
            Gets the set of items used for filtering.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SetCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SetCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SetCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.SetComparison">
            <summary>
            A list of all possible filtering conditions used in <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition"/>.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.SetComparison.Includes">
            <summary>
            Items included in the <see cref="P:Telerik.Pivot.Core.Filtering.SetCondition.Items"/> will pass the condition.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.SetComparison.DoesNotInclude">
            <summary>
            Items that are not included in the <see cref="P:Telerik.Pivot.Core.Filtering.SetCondition.Items"/> will pass the condition.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.TextComparison">
            <summary>
            A list of all possible filtering conditions used in <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/>.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.BeginsWith">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string begins with a specific pattern.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.DoesNotBeginWith">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string does not begin with a specific pattern.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.EndsWith">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string ends with a specific pattern.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.DoesNotEndWith">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string does not end with a specific pattern.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.Contains">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string contains a specific pattern.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Filtering.TextComparison.DoesNotContain">
            <summary>
            <see cref="T:Telerik.Pivot.Core.Filtering.TextComparison"/> that checks if a string does not contain a specific pattern.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.ValueGroupFilter">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter"/> that filter <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> subtotals.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ValueGroupFilter.Condition">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.Filtering.ValueGroupFilter.Condition"/> used to filter the groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.ValueGroupFilter.AggregateIndex">
            <summary>
            Gets or sets the aggregate index to be used in the filtering.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ValueGroupFilter.Filter(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.PivotAxis)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ValueGroupFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ValueGroupFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.IntervalCondition">
            <summary>
            A filters based on the relation between an item and an interval.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.IntervalCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.IntervalCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.IntervalCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.IntervalCondition.IgnoreCase">
            <summary>
            Gets or sets the value of ignore case.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.IntervalCondition.From">
            <summary>
            Gets or sets the start of the interval used in comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.IntervalCondition.To">
            <summary>
            Gets or sets the end of the interval used in comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.IntervalCondition.Condition">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.IntervalCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.IntervalCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.IntervalCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.TextCondition">
            <summary>
            A class that filters based on text matching.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.TextCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.TextCondition"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.TextCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.TextCondition.Pattern">
            <summary>
            Gets or sets the text pattern used in the comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.TextCondition.Comparison">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.TextCondition.IgnoreCase">
            <summary>
            Gets or set a value that indicates if the case of the strings should be ignored.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.TextCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.TextCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.TextCondition.PassesFilter(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.GroupsCountFilter">
            <summary>
            Implements a <see cref="T:Telerik.Pivot.Core.Filtering.GroupFilter"/> that selects a specific number of groups sorted by a given criteria.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsCountFilter.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.GroupsCountFilter.Count">
            <summary>
            Filters groups until that sum of their totals reaches that sum.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsCountFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsCountFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.GroupsPercentFilter">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Filtering.GroupFilter"/> that selects groups until the sum of their aggregates reaches a percent of their total.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsPercentFilter.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.GroupsPercentFilter.Percent">
            <summary>
            A percent of the total to be reached while selecting <see cref="T:Telerik.Pivot.Core.IGroup"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsPercentFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsPercentFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.GroupsSumFilter">
            <summary>
            Implements a <see cref="T:Telerik.Pivot.Core.Filtering.GroupFilter"/> that selects from the groups until sum of their grand totals reaches <see cref="P:Telerik.Pivot.Core.Filtering.GroupsSumFilter.Sum"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsSumFilter.#ctor">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.GroupsSumFilter.Sum">
            <summary>
            Filters groups until that sum of their totals reaches that sum.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsSumFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.GroupsSumFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.SortedGroupsFilter">
            <summary>
            A base class for groups filter based on sorted list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.AggregateIndex">
            <summary>
            Specifies which aggregate description in the grouping would be used as source for comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.Selection">
            <summary>
            Specifies whether the <see cref="F:Telerik.Pivot.Core.SortedListSelection.Top"/> or <see cref="F:Telerik.Pivot.Core.SortedListSelection.Bottom"/> groups would be accepted by the filter.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.Comparer">
            <summary>
            Gets or sets the comparer used to sort for the <see cref="P:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.Selection"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.Filter(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.IGroup},Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.PivotAxis,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SortedGroupsFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.LabelGroupFilter">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter"/> that filter <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their <see cref="P:Telerik.Pivot.Core.IGroup.Name"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.LabelGroupFilter.Condition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.Filtering.LocalCondition"/> used to filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.LabelGroupFilter.Filter(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.PivotAxis)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.LabelGroupFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.LabelGroupFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.GroupFilter">
            <summary>
            A base class for all group filters. For internal use. Please refer to one of the <see cref="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter"/> or <see cref="T:Telerik.Pivot.Core.Filtering.SiblingGroupsFilter"/> instead.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.SiblingGroupsFilter">
            <summary>
            Used for advanced group filtering based on group's siblings.
            Can filters the groups based on count, average values, sorted values etc.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SiblingGroupsFilter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.SiblingGroupsFilter" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SiblingGroupsFilter.Filter(System.Collections.Generic.IReadOnlyList{Telerik.Pivot.Core.IGroup},Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.PivotAxis,System.Int32)">
            <summary>
            Filters the groups within a parent group. Can filter based on count, average values or sorted values.
            </summary>
            <param name="groups">A read only list of all siblings.</param>
            <param name="results">The current aggregate results.</param>
            <param name="axis">Identifies if the groups are in <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <param name="level">The level of the groups.</param>
            <returns>A <see cref="T:System.Collections.Generic.ICollection`1"/> implementation that is used to filter the groups.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter">
            <summary>
            Used to filter groups based on simple values and aggregate results.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SingleGroupFilter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.SingleGroupFilter.Filter(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.PivotAxis)">
            <summary>
            Identifies if a group should be filtered or not.
            </summary>
            <param name="group">The group.</param>
            <param name="results">Results for the current grouping. Could be used for totals lookup.</param>
            <param name="axis">Identifies if the <paramref name="group"/> is positioned in the <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <returns>True if the group should be preserved, False if the group should be removed.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.OlapLabelGroupFilter">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Filtering.SingleGroupFilter"/> that filter <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their <see cref="P:Telerik.Pivot.Core.IGroup.Name"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Filtering.OlapLabelGroupFilter.Condition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.Filtering.LocalCondition"/> used to filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.OlapLabelGroupFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.OlapLabelGroupFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Filtering.ILocalFilterCondition">
            <summary>
            A filter abstraction.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Filtering.ILocalFilterCondition.PassesFilter(System.Object)">
            <summary>
            Determines if an object should be filtered.
            </summary>
            <param name="item">The item.</param>
            <returns>True if the <paramref name="item"/> should be used in the results. False if it should be ignored.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.ISortableGroupDescription">
            <summary>
            Specifies a sorting for a group description.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ISortableGroupDescription.SortOrder">
            <summary>
            Gets or sets a <see cref="P:Telerik.Pivot.Core.ISortableGroupDescription.SortOrder"/> implementation used to sort the groups created by this instance.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ISortableGroupDescription.GroupComparer">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.GroupComparer"/> that will be used for group comparisons.
            </summary>
            <value>A <see cref="T:Telerik.Pivot.Core.GroupComparer"/> implementation.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.IStringFormattableAggregate.StringFormat">
            <summary>
            Gets or sets a general string format to use for this <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/> <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>.
            This format will be used if the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunction"/> or <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> does not alter the meaning of the original data.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IStringFormattableAggregate.StringFormatSelector">
            <summary>
            Gets or sets a <see cref="P:Telerik.Pivot.Core.IStringFormattableAggregate.StringFormatSelector"/> that would provide a proper StringFormat for
            <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateFunction"/> or <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/> that alter the meaning of the original data.
            <see cref="P:Telerik.Pivot.Core.IStringFormattableAggregate.StringFormat"/>.
            <see cref="T:Telerik.Pivot.Core.Totals.TotalFormat"/>.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DrillDown.DrillDownCompletedEventArgs">
            <summary>
            Signals a drill down operation has completed, been canceled or an error occurred.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DrillDown.DrillDownCompletedEventArgs.Status">
            <summary>
            The completion status.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DrillDown.DrillDownCompletedEventArgs.Result">
            <summary>
            The result of an underlying data extraction operation.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DrillDown.DrillDownCompletedEventArgs.InnerExceptions">
            <summary>
            A read-only collection of any Exceptions thrown during underlying data extraction.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DrillDown.DrillDownEngineStatus">
            <summary>
            A pivot drill down operation status.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DrillDown.DrillDownEngineStatus.Completed">
            <summary>
            The provider has successfully completed underlying data extraction.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DrillDown.DrillDownEngineStatus.Faulted">
            <summary>
            The underlying data extraction has failed.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DrillDown.DrillDownEngineStatus.InProgress">
            <summary>
            The underlying data extraction is in progress.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Engine.PivotEngine.GetUniqueKeys(Telerik.Pivot.Core.PivotAxis,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Engine.PivotEngine.GetUniqueFilterItems(System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Engine.PivotEngineCompletedEventArgs">
            <summary>
            Signals a pivot has completed, been canceled or an error occurred.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Engine.PivotEngineCompletedEventArgs.Status">
            <summary>
            The completion status.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Engine.PivotEngineCompletedEventArgs.InnerExceptions">
            <summary>
            A read-only collection of any Exceptions thrown during a pivot grouping.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Engine.PivotEngineStatus">
            <summary>
            A pivot grouping status.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Engine.PivotEngineStatus.Completed">
            <summary>
            The pivot grouping has successfully completed grouping.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Engine.PivotEngineStatus.Faulted">
            <summary>
            The pivot grouping has failed.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Engine.PivotEngineStatus.InProgress">
            <summary>
            The pivot result is working.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.DataRowFieldInfo">
            <summary>
            A DataRow <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> presentation.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DataRowFieldInfo.#ctor(System.Data.DataColumn)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.DataRowFieldInfo"/> class.
            </summary>
            <param name="dataColumn">The data column.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DataRowFieldInfo.GetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DataRowFieldInfo.SetValue(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> that uses PropertyName and <see cref="T:Telerik.Pivot.Core.DateTimeStep"/> to identify a property.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.#ctor(Telerik.Pivot.Core.Fields.PropertyFieldInfo,Telerik.Pivot.Core.DateTimeStep,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo"/> class.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <param name="step">The DateTimeStep.</param>param>
            <param name="propertyName">The PropertyName to identify the property.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.DateTimeStep">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.DateTimeStep"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.PropertyName">
            <summary>
            Gets or sets the PropertyName to identify the associated property.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.GetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DateTimePropertyFieldInfo.SetValue(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.FieldRoles">
            <summary>
            Available roles for an <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/>.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.None">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use as source for aggregate.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.Value">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use as source for aggregate.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.Row">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use for grouping in rows.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.Column">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use for grouping in columns.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.Filter">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use for filtering.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldRoles.All">
            <summary>
            This <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> is best use for filtering.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> that uses <see cref="T:System.ComponentModel.PropertyDescriptor"/> to identify a property, which has child properties.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo.#ctor(System.ComponentModel.PropertyDescriptor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo"/> class.
            </summary>
            <param name="propertyDescriptor">The property descriptor.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo.Children">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo.Parent">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.HierarchicalPropertyDescriptorFieldInfo.GetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.HierarchicalPropertyInfoFieldInfo">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> that has child properties.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.HierarchicalPropertyInfoFieldInfo.#ctor(System.Reflection.PropertyInfo,System.Func{System.Object,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.HierarchicalPropertyInfoFieldInfo"/> class.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <param name="propertyAccess">The property access.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.HierarchicalPropertyInfoFieldInfo.Children">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.HierarchicalPropertyInfoFieldInfo.Parent">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.IFieldInfoExtractor">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/>s provider.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.IFieldInfoExtractor.GetDescriptions">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/>s.
            </summary>
            <returns>The <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/>s.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldInfoHelper.GetRoleForType(System.Type)">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Fields.FieldRoles"/> for the specified type.
            </summary>
            <param name="fieldType">Field type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.IHierarchicalFieldInfo">
            <summary>
            Represents an abstraction of a property info with child properties.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IHierarchicalFieldInfo.Children">
            <summary>
            Gets the children of this property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IHierarchicalFieldInfo.Parent">
            <summary>
            Gets the parent of this property.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo">
            <summary>
            Represents an abstraction of a property info.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.Name">
            <summary>
            Gets name of the property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.DisplayName">
            <summary>
            Gets the display-friend name of the property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.DataType">
            <summary>
            Gets the data type of the property.
            </summary>
            <value>
            The <see cref="T:System.Type"/> of the data.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.PreferredRole">
            <summary>
            Gets the preferred role of this property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.AllowedRoles">
            <summary>
            Gets the allowed roles of this property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IPivotFieldInfo.AutoGenerateField">
            <summary>
            Gets or sets a value that indicates whether UI should be generated automatically in order to display this field.
            </summary>
            <value>True if field should be generated automatically.</value>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.PropertyFieldInfo">
            <summary>
            Represents an abstraction of a property info that can set and get values.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyFieldInfo.GetValue(System.Object)">
            <summary>
            Gets the value of the property.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyFieldInfo.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value of the property.
            </summary>
            <param name="item">The item.</param>
            <param name="fieldValue">The field value.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.PivotFieldInfo">
            <summary>
            Represents an abstraction of a property info.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PivotFieldInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.PivotFieldInfo" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.DisplayName">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.DataType">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.PreferredRole">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.AllowedRoles">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PivotFieldInfo.AutoGenerateField">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PivotFieldInfo.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PivotFieldInfo.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> that uses <see cref="P:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo.PropertyDescriptor"/> to identify a property.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo.#ctor(System.ComponentModel.PropertyDescriptor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo"/> class.
            </summary>
            <param name="propertyDescriptor">The property descriptor.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo.PropertyDescriptor">
            <summary>
            Gets the property descriptor for this field info.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo.GetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyDescriptorFieldInfo.SetValue(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> that uses <see cref="T:System.Func`2"/> for property access.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.#ctor(System.Reflection.PropertyInfo,System.Func{System.Object,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo"/> class.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <param name="propertyAccess">The property access.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo"/> class.
            </summary>
            <param name="propertyInfo">The property info.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.PropertyInfo">
            <summary>
            Gets the <see cref="P:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.PropertyInfo"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.PropertyAccess">
            <summary>
            Gets the <see cref="T:System.Func`2"/> for property access.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.GetValue(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.PropertyInfoFieldInfo.SetValue(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.FieldInfoNode">
            <summary>
            Represents a node that is associated with <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo" /> instance.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldInfoNode.#ctor(Telerik.Pivot.Core.Fields.IPivotFieldInfo,Telerik.Pivot.Core.Fields.ContainerNodeRole)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.FieldInfoNode"/> class.
            </summary>
            <param name="info">The <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> associated with this node.</param>
            <param name="role">The role.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldInfoNode.#ctor(Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.FieldInfoNode"/> class.
            </summary>
            <param name="info">The <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> associated with this node.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.FieldInfoNode.FieldInfo">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.ContainerNodeRole">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.ContainerNode"/> role.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Dimension">
            <summary>
            Dimension.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Measure">
            <summary>
            A measure item.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Folder">
            <summary>
            A folder in hierarchy.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Kpi">
            <summary>
            Kpi.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.Selectable">
            <summary>
            Selectable.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.ContainerNodeRole.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.ContainerNode">
            <summary>
            Represents a node in <see cref="T:Telerik.Pivot.Core.Fields.FieldInfoNode"/> hierarchy.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.ContainerNode.#ctor(System.String,System.String,Telerik.Pivot.Core.Fields.ContainerNodeRole)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.ContainerNode"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.ContainerNode.#ctor(System.String,Telerik.Pivot.Core.Fields.ContainerNodeRole)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.ContainerNode"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNode.Name">
            <summary>
            Gets a string that can be used as an identifier of this instance.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNode.Caption">
            <summary>
            Gets the display name.
            </summary>
            <value>The display name.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNode.Children">
            <summary>
            Gets the children.
            </summary>
            <value>The children.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNode.HasChildren">
            <summary>
            Value indicating whether this instance has child nodes.
            </summary>
            <value>True, if there are child nodes.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNode.Role">
            <summary>
            Gets or sets the role.
            </summary>
            <value>The role.</value>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.ContainerNodeEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.AddingContainerNode"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.ContainerNodeEventArgs.#ctor(Telerik.Pivot.Core.Fields.ContainerNode,Telerik.Pivot.Core.Fields.IPivotFieldInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.ContainerNodeEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNodeEventArgs.ContainerNode">
            <summary>
            Gets or sets the value for the current ContainerNode.
            </summary>
            <value>The error.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.ContainerNodeEventArgs.ContainerInfo">
            <summary>
            Gets the value of the IPivotFieldInfo associated with the current ContainerNode .
            </summary>
            <value>The error.</value>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.DataTableFieldDescriptionsExtractor">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldInfoExtractor"/> implementation for <see cref="T:System.Data.DataTable"/> source.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DataTableFieldDescriptionsExtractor.#ctor(System.Data.DataTable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.DataTableFieldDescriptionsExtractor"/> class.
            </summary>
            <param name="table">The table.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.DataTableFieldDescriptionsExtractor.GetDescriptions">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.EnumerableFieldDescriptionsExtractor">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldInfoExtractor"/> for <see cref="T:System.Collections.IEnumerable"/> source.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.EnumerableFieldDescriptionsExtractor.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.EnumerableFieldDescriptionsExtractor"/> class.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.EnumerableFieldDescriptionsExtractor.GetDescriptions">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase">
            <summary>
            A base class for various implementations of <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase.GetDescriptionsDataAsyncCompleted">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase.IsBusy">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase.GetDescriptionsDataAsync(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldDescriptionProviderBase.OnDescriptionsDataCompleted(Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs)">
            <summary>
            Raise GetDescriptionsDataAsyncCompleted event.
            </summary>
            <param name="args">The event args used to invoke the event.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.FieldDescriptionProviderState">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> state.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldDescriptionProviderState.Uninitialized">
            <summary>
            The provider's initialization is pending.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldDescriptionProviderState.Initializing">
            <summary>
            The provider is initializing.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.Fields.FieldDescriptionProviderState.Initialized">
            <summary>
            The provider has completed initialization.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.FieldInfoData">
            <summary>
            Provides information about properties/fields of items that are used by a <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldInfoData.#ctor(Telerik.Pivot.Core.Fields.ContainerNode)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.FieldInfoData" /> class.
            </summary>
            <param name="root">The root.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.FieldInfoData.RootFieldInfo">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.FieldInfoData.GetFieldDescriptionByMember(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> for a generic ItemsSource.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.AddingContainerNode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.GetDescriptionsDataAsync(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.GenerateDescriptionsData">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalDataSourceFieldDescriptionsProvider.GetDescriptions(Telerik.Pivot.Core.Fields.IFieldInfoExtractor)">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> for the itemsSource.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1"/> with all <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/>s for this provider.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.GetDescriptionsDataAsyncCompleted"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs.#ctor(System.Exception,System.Object,Telerik.Pivot.Core.Fields.IFieldInfoData)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs.Error">
            <summary>
            Gets a value indicating which error occurred during an operation.
            </summary>
            <value>The error.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs.State">
            <summary>
            Gets the unique identifier for the asynchronous operation.
            </summary>
            <value>Identifier.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs.DescriptionsData">
            <summary>
            Provides information about available fields/properties. 
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider">
            <summary>
            Handles creation and lookup of <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instances.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.GetDescriptionsDataAsyncCompleted">
            <summary>
            Occurs when an asynchronous GetDescriptionsData operation completes.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.IsBusy">
            <summary>
            Gets whether a GetDescriptionsData request is in progress.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider.GetDescriptionsDataAsync(System.Object)">
            <summary>
            Retrieves information about all available field descriptions.
            This method does not block the calling thread.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.IFieldInfoData">
            <summary>
            Interface used to provide <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> for specific data source.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.IFieldInfoData.RootFieldInfo">
            <summary>
            Gets the root node of the hierarchy of <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instances.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.IFieldInfoData.GetFieldDescriptionByMember(System.String)">
            <summary>
            Gets a <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instance by name.
            </summary>
            <param name="name">Name of a description.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase">
            <summary>
            A base class for various FieldInfo classes presenting local sources. An implementation of <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.CurrentState">
            <summary>
            Gets the object which FieldDescriptions are generated.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.EnableHierarchy">
            <summary>
            Gets or sets a boolean value indicating whether field descriptions will be generated for nested properties of the objects.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.GenerateDescriptionsData">
            <summary>
            Retrieves the DescriptionsData for data source.
            </summary>
            <returns>DescriptionsData instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.GetFieldDescriptionHierarchy(System.Collections.Generic.IEnumerable{Telerik.Pivot.Core.Fields.IPivotFieldInfo})">
            <summary>
            Gets the field description hierarchy.
            </summary>
            <param name="fieldInfos">Collection of <see cref="T:Telerik.Pivot.Core.Fields.IPivotFieldInfo"/> instances.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.GetDescriptionsDataAsync(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Fields.LocalFieldDescriptionsProviderBase.OnDescriptionsDataCompleted(Telerik.Pivot.Core.Fields.GetDescriptionsDataCompletedEventArgs)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.ObjectComparer">
            <summary>
            Exposes a method that compares two objects.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.ObjectComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown
            in the following table.Value Meaning Less than zero x is less than y. Zero
            x equals y. Greater than zero x is greater than y.
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.SetConditionHashCollection">
            <summary>
            Represents collection IList container for SetCondition and OlapSetCondition items, <see cref="T:Telerik.Pivot.Core.Filtering.SetCondition"/>
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.SetConditionHashCollection" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.SetConditionHashCollection" /> class.
            </summary>
            <param name="items">The items to add to the <see cref="T:Telerik.Pivot.Core.SetConditionHashCollection"/></param>
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.IsFixedSize">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.Count">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.IsSynchronized">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.SyncRoot">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.SetConditionHashCollection.Item(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.Add(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.Clear">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.Contains(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.IndexOf(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.Insert(System.Int32,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.Remove(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.RemoveAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.CopyTo(System.Array,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SetConditionHashCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.IDescriptionsReferencing.TrackDescriptions(Telerik.Pivot.Core.IDescriptionIndexMap)">
            <summary>
            Try to find where did your description indices go using the <paramref name="map"/> generated due to a change in the <see cref="T:Telerik.Pivot.Core.IPivotSettings"/>.
            </summary>
            <param name="map">A map that tracks the description index changes.</param>
            <returns>
            True if tracking is fine.
            False to indicate a missing description or index error leading the <see cref="T:Telerik.Pivot.Core.IDescriptionsReferencing"/> to unusable state.
            Please note that if you return false parents may set the property holding this instance to null, to a default value or to reset the instance settings.
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.HourGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.HourGroup.Hour"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.HourGroup" /> struct.
            </summary>
            <param name="hour">The hour which this HourGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.HourGroup" /> struct.
            </summary>
            <param name="hour">The hour which this HourGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.HourGroup.Hour">
            <summary>
            Gets the Hour this <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.Equals(Telerik.Pivot.Core.Groups.HourGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.CompareTo(Telerik.Pivot.Core.Groups.HourGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_LessThan(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_Equality(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same hour group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.HourGroup.op_Inequality(Telerik.Pivot.Core.Groups.HourGroup,Telerik.Pivot.Core.Groups.HourGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same hour group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.MinuteGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.MinuteGroup.Minute"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup" /> struct.
            </summary>
            <param name="minute">The minute which this MinuteGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup" /> struct.
            </summary>
            <param name="minute">The minute which this MinuteGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.MinuteGroup.Minute">
            <summary>
            Gets the Minute this <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.Equals(Telerik.Pivot.Core.Groups.MinuteGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.CompareTo(Telerik.Pivot.Core.Groups.MinuteGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_LessThan(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_Equality(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same Minute group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MinuteGroup.op_Inequality(Telerik.Pivot.Core.Groups.MinuteGroup,Telerik.Pivot.Core.Groups.MinuteGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same minute group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.SecondGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.SecondGroup.Second"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup" /> struct.
            </summary>
            <param name="second">The second which this SecondGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup" /> struct.
            </summary>
            <param name="second">The second which this SecondGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.SecondGroup.Second">
            <summary>
            Gets the Second this <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.Equals(Telerik.Pivot.Core.Groups.SecondGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.CompareTo(Telerik.Pivot.Core.Groups.SecondGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_LessThan(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_Equality(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same Second group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.SecondGroup.op_Inequality(Telerik.Pivot.Core.Groups.SecondGroup,Telerik.Pivot.Core.Groups.SecondGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same second group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.WeekGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.WeekGroup.Week"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup" /> struct.
            </summary>
            <param name="week">The week which this WeekGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup" /> struct.
            </summary>
            <param name="week">The week which this WeekGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.WeekGroup.Week">
            <summary>
            Gets the Week this <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.Equals(Telerik.Pivot.Core.Groups.WeekGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.CompareTo(Telerik.Pivot.Core.Groups.WeekGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_LessThan(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_Equality(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same week group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.WeekGroup.op_Inequality(Telerik.Pivot.Core.Groups.WeekGroup,Telerik.Pivot.Core.Groups.WeekGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same week group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.DayGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.DayGroup.Day"/> and <see cref="P:Telerik.Pivot.Core.Groups.DayGroup.Month"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.DayGroup" /> struct.
            </summary>
            <param name="month">The month which this DayGroup will represents.</param>
            <param name="day">The day which this DayGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.#ctor(System.Int32,System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.DayGroup" /> struct.
            </summary>
            <param name="month">The month which this DayGroup will represents.</param>
            <param name="day">The day which this DayGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.DayGroup.Day">
            <summary>
            Gets the Day this <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> represents.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.DayGroup.Month">
            <summary>
            Gets the Month this <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.Equals(Telerik.Pivot.Core.Groups.DayGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.CompareTo(Telerik.Pivot.Core.Groups.DayGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_LessThan(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_Equality(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same day group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DayGroup.op_Inequality(Telerik.Pivot.Core.Groups.DayGroup,Telerik.Pivot.Core.Groups.DayGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.DayGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same day group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.MonthGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.MonthGroup.Month"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup" /> struct.
            </summary>
            <param name="month">The month.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup" /> struct.
            </summary>
            <param name="month">The month.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.MonthGroup.Month">
            <summary>
            Gets the Month this <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.Equals(Telerik.Pivot.Core.Groups.MonthGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.CompareTo(Telerik.Pivot.Core.Groups.MonthGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_LessThan(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_Equality(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same month group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.MonthGroup.op_Inequality(Telerik.Pivot.Core.Groups.MonthGroup,Telerik.Pivot.Core.Groups.MonthGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same month group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.QuarterGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.QuarterGroup.Quarter"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup" /> struct.
            </summary>
            <param name="quarter">The quarter which this QuarterGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup" /> struct.
            </summary>
            <param name="quarter">The quarter which this QuarterGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.QuarterGroup.Quarter">
            <summary>
            Gets the Quarter this <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.Equals(Telerik.Pivot.Core.Groups.QuarterGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.CompareTo(Telerik.Pivot.Core.Groups.QuarterGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_LessThan(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_Equality(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same quarter group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.QuarterGroup.op_Inequality(Telerik.Pivot.Core.Groups.QuarterGroup,Telerik.Pivot.Core.Groups.QuarterGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same quarter group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.YearGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping by <see cref="T:System.DateTime"/>.
            The <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> contains the items with <see cref="T:System.DateTime"/> values with the same <see cref="P:Telerik.Pivot.Core.Groups.YearGroup.Year"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.YearGroup" /> struct.
            </summary>
            <param name="year">The year which this YearGroup will represents.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.#ctor(System.Int32,System.Globalization.CultureInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.YearGroup" /> struct.
            </summary>
            <param name="year">The year which this YearGroup will represents.</param>
            <param name="culture">The culture.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.YearGroup.Year">
            <summary>
            Gets the Year this <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> represents.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.Equals(Telerik.Pivot.Core.Groups.YearGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.CompareTo(Telerik.Pivot.Core.Groups.YearGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_LessThan(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_Equality(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same year group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.YearGroup.op_Inequality(Telerik.Pivot.Core.Groups.YearGroup,Telerik.Pivot.Core.Groups.YearGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.YearGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same year group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Groups.DoubleGroup">
            <summary>
            Used for <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> values of <see cref="T:Telerik.Pivot.Core.IGroup"/>s that are grouping in ranges by numeric values.
            The <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> contains the items with <see cref="T:System.Double"/> values in range from <see cref="P:Telerik.Pivot.Core.Groups.DoubleGroup.Start"/> to <see cref="P:Telerik.Pivot.Core.Groups.DoubleGroup.End"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup" /> struct.
            </summary>
            <param name="start">The start value which this group represents.</param>
            <param name="end">The end value which this group represents.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.DoubleGroup.Start">
            <summary>
            Gets the lower limit for values in this <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Groups.DoubleGroup.End">
            <summary>
            Gets the upper limit for values in this <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.Equals(Telerik.Pivot.Core.Groups.DoubleGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.CompareTo(Telerik.Pivot.Core.Groups.DoubleGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_LessThan(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> is less than another specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_GreaterThan(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> is greater than another specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_LessThanOrEqual(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> is less than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is less than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_GreaterThanOrEqual(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether one specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> is greater than or equal to another specified <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> is greater than or equal to <paramref name="right"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_Equality(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> are equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> represent the same double group; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Groups.DoubleGroup.op_Inequality(Telerik.Pivot.Core.Groups.DoubleGroup,Telerik.Pivot.Core.Groups.DoubleGroup)">
            <summary>
            Determines whether two specified instances of <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> are not equal.
            </summary>
            <param name="left">The first object to compare.</param>
            <param name="right">The second object to compare.</param>
            <returns>true if <paramref name="left"/> and <paramref name="right"/> do not represent the same double group; otherwise, false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.IAggregateSummaryValues">
            <summary>
            Expose method to get aggregate value based on group name.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IAggregateSummaryValues.Coordinate">
            <summary>
            Gets the coordinate for which an aggregate value is requested.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IAggregateSummaryValues.GetAggregateValue(System.Object)">
            <summary>
            Gets an aggregate value for given group name.
            </summary>
            <param name="groupName">The name of the group which aggregate value is requested.</param>
            <returns>The aggregate value for this property name.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.CalculatedItem">
            <summary>
            Represents an abstraction of a calculated item.
            </summary>
            <remarks>
            These items are added to GroupDescription and they create a new group with summary returned by the <see cref="M:Telerik.Pivot.Core.CalculatedItem.GetValue(Telerik.Pivot.Core.IAggregateSummaryValues)"/> method.
            </remarks>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedItem.SolveOrder">
            <summary>
            Gets or sets the solve order of the calculated item.
            </summary>
            <remarks>
            The summary for coordinate participating in calculated items in both row and column will be calculated based on the calculated item with larger solve order.
            </remarks>
        </member>
        <member name="P:Telerik.Pivot.Core.CalculatedItem.GroupName">
            <summary>
            Gets or sets the name of groups generated for this calculated item.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedItem.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CalculatedItem.GetValue(Telerik.Pivot.Core.IAggregateSummaryValues)">
            <summary>
            Gets the value for this calculated item.
            </summary>
            <param name="aggregateSummaryValues">Interface used to get aggregate value based on group name.</param>
            <returns><see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> for this calculated item.</returns>
        </member>
        <member name="P:Telerik.Pivot.Core.IDoubleGroupDescription.Step">
            <summary>
            Gets or sets the size of the generated <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>s.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.OlapGroupComparer">
            <summary>
            Used for <see cref="T:Telerik.Pivot.Core.IGroup"/> comparison based on their <see cref="P:Telerik.Pivot.Core.IGroup.Name"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.OlapGroupComparer.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.OlapGroupComparer.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.OlapGroupComparer.CompareGroups(Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.PivotAxis)">
            <summary>
            Compares two <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their SortKeys.
            </summary>
            <param name="results">The current aggregate results.</param>
            <param name="left">The first <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="right">The second <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="axis">Identifies if the groups are in <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown in the following table.
            <para>Value Meaning Less than zero x is less than y.</para>
            <para>Zero x equals y.</para>
            <para>Greater than zero x is greater than y.</para>
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.IEditable">
            <summary>
            Specifies that this object supports a simple, transacted notification for change initialization.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IEditable.BeginEdit">
            <summary>
            Signals the object that initialization is starting and retrieves a scope token.
            Dispose the <see cref="T:System.IDisposable"/> to exit the edit scope.
            </summary>
            <returns>An edit scope token.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.INamed">
            <summary>
            Specifies that the object has a display friendly name to be used for UI purposes.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.INamed.DisplayName">
            <summary>
            Gets the display-friendly name.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.IObservableServiceProvider">
            <summary>
            An <see cref="T:System.IServiceProvider"/> that supports change notification.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.IObservableServiceProvider.ServicesChanged">
            <summary>
            Raised when new services are available or previously available services were lost.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.IDescriptionBase">
            <summary>
            Base interface for describing FilterDescription, GroupDescription and AggregateDescription.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IDescriptionBase.GetUniqueName">
            <summary>
            Returns the member name for this description.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IDescriptionBase.Clone">
            <summary>
            Creates a clone of this instance.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DateTimeGroupDescription">
            <summary>
            Used for <see cref="T:System.DateTime"/> values to group items, provide well known groups, sort and filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.DateTimeGroupDescription"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DateTimeGroupDescription.Step">
            <summary>
            Gets or sets the step of the grouping.
            Items will be put in an <see cref="T:Telerik.Pivot.Core.IGroup"/> with a <see cref="P:Telerik.Pivot.Core.IGroup.Name"/> based on the <see cref="T:Telerik.Pivot.Core.DateTimeStep"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.WeekGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.HourGroup"/> - not auto generated by default.
            <seealso cref="T:Telerik.Pivot.Core.Groups.MinuteGroup"/> - not auto generated by default.
            <seealso cref="T:Telerik.Pivot.Core.Groups.SecondGroup"/> - not auto generated by default.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.GroupNameFromItem(System.Object,System.Int32)">
            <summary>
            Names a group that would contain the <paramref name="item"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.DayGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.MonthGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.QuarterGroup"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.YearGroup"/>.
            </summary>
            <param name="item">The item to group.</param>
            <param name="level">The level of grouping for this <see cref="T:Telerik.Pivot.Core.GroupDescription"/>.</param>
            <returns>A name for the group that would contain the <paramref name="item"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.GetAllNames(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DateTimeGroupDescription.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.DateTimeStep">
            <summary>
            Grouping steps for groups based on <see cref="T:System.DateTime"/> values.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Year">
            <summary>
            Group by year.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Quarter">
            <summary>
            Group by quarters.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Month">
            <summary>
            Group by months.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Week">
            <summary>
            Group by weeks.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Day">
            <summary>
            Group by month and day.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Hour">
            <summary>
            Group by hour.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Minute">
            <summary>
            Group by minute.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.DateTimeStep.Second">
            <summary>
            Group by second.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DoubleGroupDescription">
            <summary>
            Used for numeric values to group items, provide well known groups, sort and filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DoubleGroupDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.DoubleGroupDescription" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DoubleGroupDescription.Step">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.DoubleGroupDescription.GetAllNames(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DoubleGroupDescription.GroupNameFromItem(System.Object,System.Int32)">
            <summary>
            Names a group that would contain the <paramref name="item"/>.
            <seealso cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/>.
            </summary>
            <param name="item">The item to group.</param>
            <param name="level">The level of grouping for this <see cref="T:Telerik.Pivot.Core.GroupDescription"/>.</param>
            <returns>A <see cref="T:Telerik.Pivot.Core.Groups.DoubleGroup"/> name for the group that would contain the <paramref name="item"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.DoubleGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DoubleGroupDescription.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.GroupDescription">
            <summary>
            Used to group items, provide well known groups, sort and filter the groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupDescription.ShowGroupsWithNoData">
            <summary>
            Gets or sets value that indicates whether well known groups should be created even if there are no items for them.
            <example>Grouping by days may require groups for the empty days in the current month.</example>
            <example>Grouping by persons may require groups all persons even if they do not contain any items within the current context.</example>
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupDescription.AutoShowSubTotals">
            <summary>
            Gets or sets value that indicates whether the subtotals for the GroupDescription should be displayed when possible or not.
            The default value is true - the subtotals are displayed when possible (depending on the subtotals position properties of RadPivotGrid).
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupDescription.GroupFilter">
            <summary>
            Gets a <see cref="P:Telerik.Pivot.Core.GroupDescription.GroupFilter"/> implementation for this instance that would be used to filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupDescription.GetAllNames(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
            Returns all possible group keys for this instance.
            </summary>
            <param name="uniqueNames">Enumeration of all unique group keys that were discovered after grouping.</param>
            <param name="parentGroupNames">Enumeration of all parent groups.</param>
            <returns>Returns all possible group keys for this instance.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.GroupDescriptionBase">
            <summary>
            Base class for GroupDescription.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupDescriptionBase.GroupComparer">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.GroupDescriptionBase.SortOrder">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.GroupDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.IGroupDescription">
            <summary>
            Interface that describe GroupDescription.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroupDescription.SortOrder">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.SortOrder"/> that will be used for group sorting.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroupDescription.GroupComparer">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.GroupComparer"/> that will be used for group comparisons.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyGroupDescription">
            <summary>
            Used to group items, provide well known groups, sort and filter the groups for a <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/> based on the item's <see cref="P:Telerik.Pivot.Core.PropertyGroupDescriptionBase.PropertyName"/> value.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PropertyGroupDescription" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescription.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyGroupDescriptionBase">
            <summary>
            Base class used to group items, provide well known groups, sort and filter the groups for a <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/> based on the item's <see cref="P:Telerik.Pivot.Core.PropertyGroupDescriptionBase.PropertyName"/> value.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyGroupDescriptionBase.PropertyName">
            <summary>
            Gets or sets a value identifying a property on the grouped items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyGroupDescriptionBase.Culture">
            <summary>
            Gets the CultureInfo from the LocalDataSourceProvider.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyGroupDescriptionBase.CalculatedItems">
            <summary>
            Gets the collection of calculated items that are used to initialize a group with a set of subgroups and summarized value.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.GroupNameFromItem(System.Object,System.Int32)">
            <summary>
            Return a name for group that would contain the <paramref name="item"/>.
            </summary>
            <param name="item">The item to group.</param>
            <param name="level">The level of grouping for this <see cref="T:Telerik.Pivot.Core.GroupDescription"/>.</param>
            <returns>A name for the group that would contain the <paramref name="item"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.GetAllNames(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <summary>
            Makes the instance a clone (deep copy) of the specified <see cref="T:Telerik.Pivot.Core.Cloneable"/>.
            </summary>
            <param name="source">The object to clone.</param>
            <remarks>Notes to Inheritors
            If you derive from <see cref="T:Telerik.Pivot.Core.Cloneable"/>, you need to override this method to copy all properties.
            It is essential that all implementations call the base implementation of this method (if you don't call base you should manually copy all needed properties including base properties).
            </remarks>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyGroupDescriptionBase.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.DefaultAggregateValueComparer">
            <summary>
            The default implementation for an <see cref="T:System.Collections.Generic.IComparer`1"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DefaultAggregateValueComparer.Compare(Telerik.Pivot.Core.Aggregates.AggregateValue,Telerik.Pivot.Core.Aggregates.AggregateValue)">
            <summary>
            Compares two AggregateValues and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <param name="x">The first AggregateValues to compare.</param>
            <param name="y">The second AggregateValues to compare.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown in the following table:
            Value Meaning Less than zero - x is less than y.
            Zero - x equals y.
            Greater than zero - x is greater than y.
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.SortedListSelection">
            <summary>
            Identifies a location of a subset of items in a sorted list.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.SortedListSelection.Top">
            <summary>
            Identifies the items in the beginning of a sorted list.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.SortedListSelection.Bottom">
            <summary>
            Identifies the items at the bottom of a sorted list.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.DefaultComparer">
            <summary>
            Implements a default comparison for AggregateValues.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DefaultComparer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.DefaultComparer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.DefaultComparer.IgnoreCase">
            <summary>
            Gets or sets if the string comparisons will ignore case.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.DefaultComparer.Compare(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DefaultComparer.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.DefaultComparer.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.GrandTotalComparer">
            <summary>
            Used for <see cref="T:Telerik.Pivot.Core.IGroup"/> comparison based on their grand totals.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GrandTotalComparer.AggregateIndex">
            <summary>
            Gets or sets a value that indicates the aggregate's grand total to be used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GrandTotalComparer.CompareGroups(Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.PivotAxis)">
            <summary>
            Compares two <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their grand totals.
            </summary>
            <param name="results">The current aggregate results.</param>
            <param name="left">The first <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="right">The second <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="axis">Identifies if the groups are in <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown in the following table.
            <para>Value Meaning Less than zero x is less than y.</para>
            <para>Zero x equals y.</para>
            <para>Greater than zero x is greater than y.</para>
            </returns>
        </member>
        <member name="M:Telerik.Pivot.Core.GrandTotalComparer.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.GrandTotalComparer.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.GroupComparer">
            <summary>
            A base class for <see cref="T:Telerik.Pivot.Core.IGroup"/> comparers.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupComparer.CompareGroups(Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.PivotAxis)">
            <summary>
            Compares two <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on the current aggregate results.
            </summary>
            <param name="results">The current aggregate results.</param>
            <param name="left">The first <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="right">The second <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="axis">Identifies if the groups are in <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown in the following table.
            <para>Value Meaning Less than zero x is less than y.</para>
            <para>Zero x equals y.</para>
            <para>Greater than zero x is greater than y.</para>
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.GroupNameComparer">
            <summary>
            Used for <see cref="T:Telerik.Pivot.Core.IGroup"/> comparison based on their <see cref="P:Telerik.Pivot.Core.IGroup.Name"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupNameComparer.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.GroupNameComparer.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.GroupNameComparer.CompareGroups(Telerik.Pivot.Core.IAggregateResultProvider,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.PivotAxis)">
            <summary>
            Compares two <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their <see cref="P:Telerik.Pivot.Core.IGroup.Name"/>s.
            </summary>
            <param name="results">The current aggregate results.</param>
            <param name="left">The first <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="right">The second <see cref="T:Telerik.Pivot.Core.IGroup"/> to compare.</param>
            <param name="axis">Identifies if the groups are in <see cref="F:Telerik.Pivot.Core.PivotAxis.Rows"/> or <see cref="F:Telerik.Pivot.Core.PivotAxis.Columns"/>.</param>
            <returns>
            A signed integer that indicates the relative values of x and y, as shown in the following table.
            <para>Value Meaning Less than zero x is less than y.</para>
            <para>Zero x equals y.</para>
            <para>Greater than zero x is greater than y.</para>
            </returns>
        </member>
        <member name="T:Telerik.Pivot.Core.SortOrder">
            <summary>
            Specifies how items are sorted. 
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.SortOrder.Ascending">
            <summary>
            Items are sorted in ascending order.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.SortOrder.Descending">
            <summary>
            Rows are sorted in descending order.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.SortOrder.None">
            <summary>
            Rows are sorted in Data source order.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IndexToValueTable`1.IndexCount">
            <summary>
            Total number of indices represented in the table.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IndexToValueTable`1.IsEmpty">
            <summary>
            Returns true if the table is empty.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IndexToValueTable`1.RangeCount">
            <summary>
            Returns the number of index ranges in the table.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.AddValue(System.Int32,`0)">
            <summary>
            Add a value with an associated index to the table.
            </summary>
            <param name="index">Index where the value is to be added or updated.</param>
            <param name="value">Value to add.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.AddValues(System.Int32,System.Int32,`0)">
            <summary>
            Add multiples values with an associated start index to the table.
            </summary>
            <param name="startIndex">Index where first value is added.</param>
            <param name="count">Total number of values to add (must be greater than 0).</param>
            <param name="value">Value to add.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.Clear">
            <summary>
            Clears the index table.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.Contains(System.Int32)">
            <summary>
            Returns true if the given index is contained in the table.
            </summary>
            <param name="index">Index to search for.</param>
            <returns>True if the index is contained in the table.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.ContainsAll(System.Int32,System.Int32)">
            <summary>
            Returns true if the entire given index range is contained in the table.
            </summary>
            <param name="startIndex">Beginning of the range.</param>
            <param name="endIndex">End of the range.</param>
            <returns>True if the entire index range is present in the table.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.ContainsIndexAndValue(System.Int32,`0)">
            <summary>
            Returns true if the given index is contained in the table with the the given value.
            </summary>
            <param name="index">Index to search for.</param>
            <param name="value">Value expected.</param>
            <returns>True if the given index is contained in the table with the the given value.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.Copy">
            <summary>
            Returns a copy of this IndexToValueTable.
            </summary>
            <returns>Copy of this IndexToValueTable.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetIndexCount(System.Int32,System.Int32,`0)">
            <summary>
            Returns the inclusive index count between lowerBound and upperBound of all indexes with the given value.
            </summary>
            <param name="lowerBound">LowerBound criteria.</param>
            <param name="upperBound">UpperBound criteria.</param>
            <param name="value">Value to look for.</param>
            <returns>Number of indexes contained in the table between lowerBound and upperBound (inclusive).</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetIndexCount(System.Int32,System.Int32)">
            <summary>
            Returns the inclusive index count between lowerBound and upperBound.
            </summary>
            <param name="lowerBound">LowerBound criteria.</param>
            <param name="upperBound">UpperBound criteria.</param>
            <returns>Number of indexes contained in the table between lowerBound and upperBound (inclusive).</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetIndexCountBeforeGap(System.Int32,System.Int32)">
            <summary>
            Returns the number indexes in this table after a given startingIndex but before.
            reaching a gap of indexes of a given size.
            </summary>
            <param name="startingIndex">Index to start at.</param>
            <param name="gapSize">Size of index gap.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetIndexes">
            <summary>
            Returns an enumerator that goes through the indexes present in the table.
            </summary>
            <returns>An enumerator that enumerates the indexes present in the table.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetIndexes(System.Int32)">
            <summary>
            Returns all the indexes on or after a starting index.
            </summary>
            <param name="startIndex">Start index.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetNthIndex(System.Int32)">
            <summary>
            Return the index of the Nth element in the table.
            </summary>
            <param name="n">N.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetValueAt(System.Int32)">
            <summary>
            Returns the value at a given index or the default value if the index is not in the table.
            </summary>
            <param name="index">Index to search for.</param>
            <returns>The value at the given index or the default value if index is not in the table.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.GetValueAt(System.Int32,System.Boolean@)">
            <summary>
            Returns the value at a given index or the default value if the index is not in the table.
            </summary>
            <param name="index">Index to search for.</param>
            <param name="found">Set to true by the method if the index was found; otherwise, false.</param>
            <returns>The value at the given index or the default value if index is not in the table.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.IndexOf(System.Int32)">
            <summary>
            Returns an index's index within this table.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.InsertIndex(System.Int32)">
            <summary>
            Inserts an index at the given location. This does not alter values in the table.
            </summary>
            <param name="index">Index location to insert an index.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.InsertIndexAndValue(System.Int32,`0)">
            <summary>
            Inserts an index into the table with the given value .
            </summary>
            <param name="index">Index to insert.</param>
            <param name="value">Value for the index.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.InsertIndexes(System.Int32,System.Int32)">
            <summary>
            Inserts multiple indexes into the table. This does not alter Values in the table.
            </summary>
            <param name="startIndex">First index to insert.</param>
            <param name="count">Total number of indexes to insert.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.InsertIndexesAndValues(System.Int32,System.Int32,`0)">
            <summary>
            Inserts multiple indexes into the table with the given value.
            </summary>
            <param name="startIndex">Index to insert first value.</param>
            <param name="count">Total number of values to insert. (must be greater than 0).</param>
            <param name="value">Value to insert.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveIndex(System.Int32)">
            <summary>
            Removes an index from the table. This does not alter Values in the table.
            </summary>
            <param name="index">Index to remove.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveIndexAndValue(System.Int32)">
            <summary>
            Removes a value and its index from the table.
            </summary>
            <param name="index">Index to remove.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveIndexes(System.Int32,System.Int32)">
            <summary>
            Removes multiple indexes from the table.  This does not alter Values in the table.
            </summary>
            <param name="startIndex">First index to remove.</param>
            <param name="count">Total number of indexes to remove.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveIndexesAndValues(System.Int32,System.Int32)">
            <summary>
            Removes multiple values and their indexes from the table.
            </summary>
            <param name="startIndex">First index to remove.</param>
            <param name="count">Total number of indexes to remove.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveValue(System.Int32)">
            <summary>
            Removes a value from the table at the given index. This does not alter other indexes in the table.
            </summary>
            <param name="index">Index where value should be removed.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.IndexToValueTable`1.RemoveValues(System.Int32,System.Int32)">
            <summary>
            Removes multiple values from the table. This does not alter other indexes in the table.
            </summary>
            <param name="startIndex">First index where values should be removed.</param>
            <param name="count">Total number of values to remove.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.FuncExtensions">
            <summary>
            Holds extension methods for function delegates.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.FuncExtensions.ToUntypedFunc``2(System.Func{``0,``1})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <typeparam name="T">The type of the parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function for the given <paramref name="func"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.FuncExtensions.ToUntypedTwoParameterFunc``3(System.Func{``0,``1,``2})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function for the given <paramref name="func"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.FuncExtensions.ToUntypedBooleanFunc``1(System.Func{``0,System.Boolean})">
            <summary>
            Converts the given function to untyped one.
            </summary>
            <param name="func">The function.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.FuncExtensions.ToTypedResultFunc``2(System.Func{``0,``1})">
            <summary>
            Converts the given function to an untyped one that has a strongly-typed return value.
            </summary>
            <typeparam name="T">The type of the parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the return value of the function.</typeparam>
            <param name="func">The function that will be converted.</param>
            <returns>Untyped function with a strongly-typed return value for the given <paramref name="func"/>.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotDynamicClass">
            <summary>
            Base dynamic class .
            </summary>
            <exclude />
            <excludeToc />
        </member>
        <member name="M:Telerik.Pivot.Core.PivotDynamicClass.ToString">
            <exclude />
            <excludeToc />
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotDynamicProperty.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PivotDynamicProperty" /> class.
            </summary>
            <param name="name">The name.</param>
            <param name="type">The type.</param>
            <exclude />
            <excludeToc />
        </member>
        <member name="P:Telerik.Pivot.Core.PivotDynamicProperty.Name">
            <summary>
            Gets the name.
            </summary>
            <exclude />
            <excludeToc />
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotDynamicProperty.Type">
            <summary>
            Gets the type.
            </summary>
            <exclude />
            <excludeToc />
            <value>The type.</value>
        </member>
        <member name="T:Telerik.Pivot.Core.ExpandBehaviorParameters">
            <summary>
            Specify parameters for <see cref="T:Telerik.Pivot.Core.GroupsExpandBehavior"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ExpandBehaviorParameters.Item">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.IGroup"/> specified as parameter.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.GroupsExpandBehavior">
            <summary>
            Specify the expanded state of <see cref="T:Telerik.Pivot.Core.IGroup"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupsExpandBehavior.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.GroupsExpandBehavior"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupsExpandBehavior.UpToLevel">
            <summary>
            Specify the default state for <see cref="T:Telerik.Pivot.Core.IGroup"/>s up to given level (excluding).
            The default is <see cref="F:System.Int32.MaxValue"/>
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.GroupsExpandBehavior.Expanded">
            <summary>
            Specify the default state for <see cref="T:Telerik.Pivot.Core.IGroup"/>s up to given <see cref="P:Telerik.Pivot.Core.GroupsExpandBehavior.UpToLevel"/> (excluding).
            The default is true.
            <remarks>
            When true groups up to the set level are expanded, all groups with level greater than or equal are collapsed.
            When false groups up to the set level are collapsed, all groups with level greater than or equal are expanded.
            </remarks>
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.GroupsExpandBehavior.IsExpanded(Telerik.Pivot.Core.ExpandBehaviorParameters)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.IItemExpandBehavior`1">
            <summary>
            Provides method that specify the expand/collapse state of item.
            </summary>
            <typeparam name="T">The type of item.</typeparam>
        </member>
        <member name="M:Telerik.Pivot.Core.IItemExpandBehavior`1.IsExpanded(`0)">
            <summary>
            Gets if item is expanded.
            </summary>
            <param name="parameter">The item which expanded state will be queried.</param>
            <returns>True if item is expanded, otherwise false.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapFilterDescription">
            <summary>
            Represents a filter description for a hierarchy.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFilterDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapFilterDescription" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapFilterDescription.Levels">
            <summary>
            Gets the levels collection of this instance.
            </summary>
            <value>The levels.</value>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFilterDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase">
            <summary>
            Base class for OLAP filter descriptions.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.MemberName">
            <summary>
            Gets or sets the dimension unique name used for grouping.
            </summary>
            <value>The dimension unique name.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.Condition">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.Condition"/> used to filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.GetUniqueName">
            <summary>
            
            </summary>
            <inheritdoc />
            <returns></returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFilterDescriptionBase.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapGroupDescription">
            <summary>
            Used to specify grouping parameters for OLAP data sources.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapGroupDescription" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapGroupDescription.Levels">
            <summary>
            Gets or sets the levels collection of this instance. Setting this property will create a clone of the provided <see cref="T:Telerik.Pivot.Core.Olap.GenericDescriptionCollection`1"/> value. The setter is implemented to support deserialization.
            </summary>
            <value>The levels.</value>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase">
            <summary>
            Used to specify grouping parameters for OLAP data sources.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase.MemberName">
            <summary>
            Gets or sets the dimension unique name used for grouping.
            </summary>
            <value>The dimension unique name.</value>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase.GetAllNames(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object})">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupDescriptionBase.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.GenericDescriptionCollection`1">
            <summary>
            A collection that stores the children of a hierarchical description.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapLevelFilterDescription">
            <summary>
            Represents a filter description for a level of a hierarchy.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapLevelFilterDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapLevelFilterDescription" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapLevelGroupDescription">
            <summary>
            Used to specify grouping parameters for a level of an OLAP hierarchy.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.MemberDistinctValue">
            <summary>
            Represents an OLAP distinct value.
            This is for internal use only and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.MemberDistinctValue.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.MemberDistinctValue" /> class.
            </summary>
            <param name="uniqueName">Name of the unique.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.MemberDistinctValue.Caption">
            <summary>
            Gets or sets the caption.
            </summary>
            <value>The caption.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.MemberDistinctValue.UniqueName">
            <summary>
            Gets or sets the name of the unique.
            </summary>
            <value>The name of the unique.</value>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.MemberDistinctValue.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.MemberDistinctValue.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.MemberDistinctValue.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.MemberDistinctValue.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition">
            <summary>
            Condition which is used to filter items based on two other conditions. 
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.DistinctCondition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.Olap.OlapSetCondition"/> used to filter the items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.Condition">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.Condition"/> used to filter the items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapItemsFilterCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapComparisonCondition">
            <summary>
            A class that filters based on two comparable objects.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapComparisonCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapComparisonCondition.IgnoreCase">
            <summary>
            Gets or sets the value of ignore case.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapComparisonCondition.Than">
            <summary>
            Gets or sets the value that the groups would be compared to.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapComparisonCondition.Condition">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapComparisonCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapComparisonCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapIntervalCondition">
            <summary>
            A filters based on the relation between an item and an interval.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapIntervalCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapIntervalCondition.IgnoreCase">
            <summary>
            Gets or sets the value of ignore case.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapIntervalCondition.From">
            <summary>
            Gets or sets the start of the interval used in comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapIntervalCondition.To">
            <summary>
            Gets or sets the end of the interval used in comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapIntervalCondition.Condition">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapIntervalCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapIntervalCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapSetCondition">
            <summary>
            Filter that checks if items are included/excluded from a set.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapSetCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapSetCondition" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapSetCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapSetCondition.Comparison">
            <summary>
            Gets or sets the filter condition.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapSetCondition.Items">
            <summary>
            Gets the set of items used for filtering. 
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapSetCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapSetCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapTextCondition">
            <summary>
            A class that filters based on text matching.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapTextCondition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapTextCondition"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapTextCondition.IsActive">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapTextCondition.Pattern">
            <summary>
            Gets or sets the text pattern used in the comparison.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapTextCondition.Comparison">
            <summary>
            Gets or sets the condition used in the comparison.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapTextCondition.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapTextCondition.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapValueGroupFilter">
            <summary>
            A <see cref="T:Telerik.Pivot.Core.Filtering.GroupFilter"/> that filters <see cref="T:Telerik.Pivot.Core.IGroup"/>s based on their <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> subtotals.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapValueGroupFilter.Condition">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapValueGroupFilter.Condition"/> used to filter the groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapValueGroupFilter.AggregateIndex">
            <summary>
            Gets or sets the aggregate index to be used in the filtering.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapValueGroupFilter.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapValueGroupFilter.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapAggregateResultProvider.Root">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapAggregateResultProvider.GetAggregateResult(System.Int32,Telerik.Pivot.Core.Coordinate)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapCondition">
            <summary>
            Represents an OLAP filter condition that is used with <see cref="T:Telerik.Pivot.Core.Olap.OlapFilterDescription"/>
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapDataProvider">
            <summary>
            Base class for Olap data providers.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapDataProvider.EnableLoadOnDemand">
            <summary>
            Gets or sets a boolean value indicating whether the groups will start collapsed and the hierarchy data will be loaded as they are expanded.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapDataProvider.DistinctValuesLimit">
            <summary>
            Gets or sets the maximum count of distinct values to be loaded.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.MeasureGroupSchemaElement">
            <summary>
            Holds required elements for MeasureGroups. Properties for DESCRIPTION and IS_WRITE_ENABLED can be added in the future if they are required.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapAggregateDescription">
            <summary>
            Used to specify aggregation parameters for OLAP data sources.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapAggregateDescription.MemberName">
            <summary>
            Gets or sets cube measure name that is used for aggregation.
            </summary>
            <value>The name of the field.</value>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapAggregateDescription.DisplayValueAsKpi">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapAggregateDescription.DataType">
            <summary>
            Provides the data type of the aggregate description.
            </summary>  
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapAggregateDescription.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapAggregateDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapAggregateDescription.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapAggregateDescription.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapCommunicationException">
            <summary>
            Exception type that represents OLAP errors.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapCommunicationException.#ctor(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapCommunicationException.#ctor(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapCommunicationException.#ctor">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapGroupName">
            <summary>
            A class that represents an olap group name.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapGroupName"/> class by provided caption and a group key object.
            </summary>
            <param name="groupCaption">The group caption used to display the group in UI. Sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupCaption"/> property.</param>
            <param name="groupKey">The group key used to identify and compare the group with other groups. Sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupKey"/> property.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Olap.OlapGroupName"/> class by provided group name.
            </summary>
            <param name="groupName">The group caption used to display the group in UI and compare the group with other groups. Sets the <see cref="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupCaption"/> and <see cref="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupKey"/> properties.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupKey">
            <summary>
            Gets or sets the GroupCaption. Use setter only on groups created by you.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapGroupName.GroupCaption">
            <summary>
            Gets or sets the GroupCaption. Use setter only on groups created by you.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapGroupName.SortKeys">
            <summary>
            Gets the keys based on which Sorting is applied.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapGroupName.CompareTo(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapPivotResults">
            <summary>
            Pivot results supplied by Olap data providers.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapPivotResults.RowGroupDescriptions">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapPivotResults.ColumnGroupDescriptions">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapPivotResults.AggregateDescriptions">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapPivotResults.FilterDescriptions">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapPivotResults.Root">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapPivotResults.GetUniqueKeys(Telerik.Pivot.Core.PivotAxis,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapPivotResults.GetUniqueFilterItems(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapPivotResults.GetAggregateResult(System.Int32,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapTupleProcessor.AssignRaggedBottomLevels(System.Collections.Generic.IList{Telerik.Pivot.Core.Olap.IOlapTuple})">
            <summary>
            Traverses all tuples and fills raggedBottomLevels with bottom level TraversalStates that are not with index = hierarchy.TotalLevels - 1. Used for ragged hierarchies.
            </summary>
            <param name="tuples"></param>
        </member>
        <member name="T:Telerik.Pivot.Core.Olap.OlapFieldDescriptionsProviderBase">
            <summary>
            An <see cref="T:Telerik.Pivot.Core.Fields.IFieldDescriptionProvider"/> for Olap data sources.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapFieldDescriptionsProviderBase.CurrentRequestInfo">
            <summary>
            Gets the object for which FieldDescriptions are generated.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Olap.OlapFieldDescriptionsProviderBase.Data">
            <summary>
            Gets the loaded data.
            </summary>
            <value>The data.</value>
        </member>
        <member name="M:Telerik.Pivot.Core.Olap.OlapFieldDescriptionsProviderBase.GetDescriptionsDataAsync(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PivotChartItem">
            <summary>
            Defines a pivot chart item with Value, full path from rows and full path from columns.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.PivotChartItem.PropertyChanged">
            <inheritdoc/>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartItem.Value">
            <summary>
            Gets the value of the specific cell.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartItem.NameX">
            <summary>
            Gets the full path as string of the specific pivot cell ( semicolon delimited ) on rows or columns hierarchy that will represent the X axis in a chart.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartItem.NameY">
            <summary>
            Gets the full path as string of the specific pivot cell ( semicolon delimited ) on rows or columns hierarchy that will represent the Y axis in a chart.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItem.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItem.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItem.Equals(Telerik.Pivot.Core.PivotChartItem)">
            <summary>Determines whether the specified <see cref="T:Telerik.Pivot.Core.PivotChartItem"/> is equal
            to the current <see cref="T:Telerik.Pivot.Core.PivotChartItem"/>.</summary>
            <returns>true if the specified <see cref="T:Telerik.Pivot.Core.PivotChartItem"/> is equal to the
            current <see cref="T:Telerik.Pivot.Core.PivotChartItem"/>; otherwise, false.</returns>
            <param name="other">The <see cref="T:Telerik.Pivot.Core.PivotChartItem"/> to compare with the current
            <see cref="T:Telerik.Pivot.Core.PivotChartItem"/>. </param>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotChartItemsCollection">
            <summary>
            Represents a collection of <see cref="T:Telerik.Pivot.Core.PivotChartItem"/> with name of the collection.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItemsCollection.#ctor(System.String,System.Collections.Generic.IEnumerable{Telerik.Pivot.Core.PivotChartItem})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/> class.
            </summary>
            <param name="name">The name of the collection.</param>
            <param name="items">The items of the collection.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartItemsCollection.Items">
            <summary>
            Gets a collection of <see cref="T:Telerik.Pivot.Core.PivotChartItem"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartItemsCollection.Name">
            <summary>
            Gets the Name.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItemsCollection.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItemsCollection.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartItemsCollection.Equals(Telerik.Pivot.Core.PivotChartItemsCollection)">
            <summary>Determines whether the specified <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/> is equal
            to the current <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/>.</summary>
            <returns>true if the specified <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/> is equal to the
            current <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/>; otherwise, false.</returns>
            <param name="other">The <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/> to compare with the current
            <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/>. </param>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotChartTotalsPosition">
            <summary>
            Defines the placement of totals.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartTotalsPosition.Left">
            <summary>
            Totals are placed to the left.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartTotalsPosition.Right">
            <summary>
            Totals are placed to the right.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartTotalsPosition.None">
            <summary>
            Totals are not displayed.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotChartViewModel">
            <summary>
            ViewModel class for PivotChart. Provides propertyChange notification.
            Provides property <see cref="P:Telerik.Pivot.Core.PivotChartViewModel.SeriesSource"/> that can be used as a Source of a ChartSeriesProvider/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartViewModel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.PivotChartViewModel"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.PivotChartViewModel.Completed">
            <summary>
            Occurs when the current operation has completed.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.IsReady">
            <summary>
            Gets a property that indicates whether the <see cref="T:Telerik.Pivot.Core.PivotChartViewModel"/> is ready ( is not busy ).
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartViewModel.FreezeCore(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartViewModel.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.SelectedAxisProperty">
            <summary>
            Identifies the SelectedAxis dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.ColumnsSubTotalsPositionProperty">
            <summary>
            Identifies the ColumnsSubTotalsPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.ColumnGrandTotalsPositionProperty">
            <summary>
            Identifies the ColumnGrandTotalsPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.RowsSubTotalsPositionProperty">
            <summary>
            Identifies the RowsSubTotalsPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.RowGrandTotalsPositionProperty">
            <summary>
            Identifies the RowGrandTotalsPosition dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.DataProviderProperty">
            <summary>
            Identifies the DataProvider dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotChartViewModel.SeriesSourceProperty">
            <summary>
            Identifies the SeriesSource readonly dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.DataProvider">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.IDataProvider"/> data provider.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.SelectedAxis">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.PivotAxis"/> which will be used as the Y axis ( the value axis ) when generating the <see cref="P:Telerik.Pivot.Core.PivotChartViewModel.SeriesSource"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.ColumnsSubTotalsPosition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.PivotChartTotalsPosition"/> of the columns sub-totals position.
            By default is set to None.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.ColumnGrandTotalsPosition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.PivotChartTotalsPosition"/> of the column grand-totals position.
            By default is set to None.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.RowsSubTotalsPosition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.PivotChartTotalsPosition"/> of the rows sub-totals position.
            By default is set to None.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.RowGrandTotalsPosition">
            <summary>
            Gets or sets the <see cref="T:Telerik.Pivot.Core.PivotChartTotalsPosition"/> of the row grand-totals position.
            By default is set to None.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotChartViewModel.SeriesSource">
            <summary>
            Gets a ReadOnlyCollection of <see cref="T:Telerik.Pivot.Core.PivotChartItemsCollection"/> generated from the <see cref="T:Telerik.Pivot.Core.PivotChartViewModel"/> that can be used as a Source of a ChartSeriesProvider/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PivotChartViewModel.OnPropertyChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            The callback method invoked when a <see cref="T:Telerik.Pivot.Core.PivotChartViewModel"/> property changes.
            </summary>
            <param name="dependencyObject">The <see cref="T:System.Windows.DependencyObject"/> dependency object.</param>
            <param name="dependencyPropertyChangedEventArgs">The <see cref="T:System.Windows.DependencyPropertyChangedEventArgs"/> event arguments.</param>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotSerializationHelper">
            <summary>
            Provides all known types necessary for serializing <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/>.
            Use this class to extract these knownTypes and concatenate them with your knownTypes, so you can pass them to <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> for example.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PivotSerializationHelper.KnownTypes">
            <summary>
            Gets known types in <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/> to use with serializer such as <see cref="T:System.Runtime.Serialization.DataContractSerializer"/>.
            </summary>
            <returns>An enumeration with the known serializable classes for the <see cref="T:Telerik.Pivot.Core.LocalDataSourceProvider"/> <see cref="T:Telerik.Pivot.Core.IDataProvider"/>.</returns>
        </member>
        <member name="P:Telerik.Pivot.Core.ViewModels.PivotViewModel.GrandTotalText">
            <summary>
            Gets or sets the name for grand total groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ViewModels.PivotViewModel.ValuesGroupText">
            <summary>
            Gets or sets the name of the description for value groups.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ViewModels.PivotViewModel.GrandTotalGroupNameFormat">
            <summary>
            Gets or sets a string value used to format the name of a grand total group created for aggregate description.
            The indexed parameter {0} is the display name of the aggregate description.
            Default value: "Total {0}"
            By default for the 'Sum of Quantity' aggregate description the grand total group would be named 'Sum of Quantity Total'.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ViewModels.PivotViewModel.SubTotalGroupNameFormat">
            <summary>
            Gets or sets a string value used to format the name of a sub total group.
            The indexed parameter {0} is the name of the group for which a sub total is created.
            Default value: "{0} Total".
            By default for the 'Copy holder' group the sub total group would be named 'Copy holder Total'.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.ViewModels.PivotViewModel.AggregateGroupNameFormat">
            <summary>
            Gets or sets a string value used to format the name of a sub total group.
            The indexed parameter {0} is the name of the group for which a sub total is created.
            The indexed parameter {1} is the display name of the aggregate description.
            Default value: "{0} {1}"
            By default for the 'Copy holder' group the aggregate for 'Sum of Quantity' will be named 'Copy holder Sum of Quantity'.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.SettingsNode">
            <summary>
            Base class that support <see cref="M:Telerik.Pivot.Core.Cloneable.Clone"/> Clone and <see cref="T:System.ComponentModel.INotifyPropertyChanged"/>.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.SettingsNode.SettingsChanged">
            <summary>
            Invoked when this or one of the children is changed.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.SettingsNode.ServicesChanged">
            <summary>
            Invoked when new services are available or existing services are removed.
            </summary>
        </member>
        <member name="E:Telerik.Pivot.Core.SettingsNode.PropertyChanged">
            <summary>
            Invoked when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.SettingsNode.Parent">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.SettingsNode"/> this <see cref="T:Telerik.Pivot.Core.SettingsNode"/> is used in.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.NotifyServicesChanged">
            <summary>
            Raises the <see cref="E:Telerik.Pivot.Core.SettingsNode.ServicesChanged"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.NotifySettingsChanged(Telerik.Pivot.Core.SettingsChangedEventArgs)">
            <summary>
            Will recursively notify all <see cref="T:Telerik.Pivot.Core.SettingsNode"/> for a settings change.
            </summary>
            <param name="args"><see cref="T:Telerik.Pivot.Core.SettingsChangedEventArgs"/> that contain information about the change.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.OnSettingsChanged(Telerik.Pivot.Core.SettingsChangedEventArgs)">
            <summary>
            Invoked when a SettingsChangedEventArgs reaches the <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.
            </summary>
            <param name="args">The <see cref="T:Telerik.Pivot.Core.SettingsChangedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.BeginEdit">
            <summary>
            Enters the <see cref="T:Telerik.Pivot.Core.SettingsNode"/> in a new editing scope. Use when applying multiple changes.
            If child <see cref="T:Telerik.Pivot.Core.SettingsNode"/> are changed, notifications will be accumulated in this <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.
            <example>
            using(settingsNode.BeginEdit())
            {
                // Apply multiple changes here.
            }
            </example>
            </summary>
            <returns>An edit scope token that you must <see cref="M:System.IDisposable.Dispose"/> when you are done with the editing.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.BeginInit">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.EndInit">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.GetService(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.OnPropertyChanged(System.String)">
            <summary>
            Raises this object's <see cref="E:Telerik.Pivot.Core.SettingsNode.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">The property that has a new value.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.RemoveSettingsChild(Telerik.Pivot.Core.SettingsNode)">
            <summary>
            Unsets the parent initiated with <see cref="M:Telerik.Pivot.Core.SettingsNode.AddSettingsChild(Telerik.Pivot.Core.SettingsNode)"/>.
            This <see cref="T:Telerik.Pivot.Core.SettingsNode"/> will no longer receive change notifications from the <paramref name="child"/>.
            </summary>
            <param name="child">The nested <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.AddSettingsChild(Telerik.Pivot.Core.SettingsNode)">
            <summary>
            Set this <see cref="T:Telerik.Pivot.Core.SettingsNode"/> as parent of the <paramref name="child"/> and becomes a target for the <paramref name="child"/>'s change notifications.
            </summary>
            <param name="child">The nested <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.</param>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.GetServiceOverride(System.Type)">
            <summary>
            Provides services available by this SettingsNode.
            Other services may be available in its <see cref="P:Telerik.Pivot.Core.SettingsNode.Parent"/> <see cref="T:Telerik.Pivot.Core.SettingsNode"/>s.
            The default implementation returns this <see cref="T:Telerik.Pivot.Core.SettingsNode"/> if the desired service type is assignable from the type of this.
            The <see cref="M:Telerik.Pivot.Core.SettingsNode.GetService(System.Type)"/> implementation of <see cref="T:System.IServiceProvider"/> would query the service on the local node and if not available would query up the <see cref="P:Telerik.Pivot.Core.SettingsNode.Parent"/> nodes.
            </summary>
            <param name="serviceType">The type of the requested service.</param>
            <returns>A service instance if available, null otherwise.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.OnEnteredEditScope">
            <summary>
            Override to provide custom behavior for derived classes when editing begins.
            <see cref="T:Telerik.Pivot.Core.SettingsNode"/> is already in edit mode and changes within the method body will be accumulated and released upon exit.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNode.OnExitingEditScope">
            <summary>
            Override to provide custom behavior for derived classes when finishing editing.
            <see cref="T:Telerik.Pivot.Core.SettingsNode"/> is still in edit mode and changes within the method body will be accumulated and released upon exit.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.PivotAxis">
            <summary>
            Identifies a pivot grouping axis - rows or columns.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotAxis.Rows">
            <summary>
            Identifies the Rows.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.PivotAxis.Columns">
            <summary>
            Identifies the Columns.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.Coordinate">
            <summary>
            An unique point determined by two <see cref="T:Telerik.Pivot.Core.IGroup"/>s - the <see cref="P:Telerik.Pivot.Core.Coordinate.RowGroup"/> and the <see cref="P:Telerik.Pivot.Core.Coordinate.ColumnGroup"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.#ctor(Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.Coordinate" /> struct.
            </summary>
            <param name="rowGroup">The row group.</param>
            <param name="columnGroup">The column group.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.Coordinate.RowGroup">
            <summary>
            Get the RowGroup.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.Coordinate.ColumnGroup">
            <summary>
            Gets the ColumnGroup.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.Equals(Telerik.Pivot.Core.Coordinate)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.op_Equality(Telerik.Pivot.Core.Coordinate,Telerik.Pivot.Core.Coordinate)">
            <summary>
            Compares two instances of <see cref="T:Telerik.Pivot.Core.Coordinate"/> for equality. 
            </summary>
            <param name="left">The first instance of <see cref="T:Telerik.Pivot.Core.Coordinate"/> to compare.</param>
            <param name="right">The second instance of <see cref="T:Telerik.Pivot.Core.Coordinate"/> to compare.</param>
            <returns>true if the instances of <see cref="T:Telerik.Pivot.Core.Coordinate"/> are equal; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.Coordinate.op_Inequality(Telerik.Pivot.Core.Coordinate,Telerik.Pivot.Core.Coordinate)">
            <summary>
            Evaluates two instances of <see cref="T:Telerik.Pivot.Core.Coordinate"/> to determine inequality. 
            </summary>
            <param name="left">The first instance of <see cref="T:Telerik.Pivot.Core.Coordinate"/> to compare.</param>
            <param name="right">The second instance of <see cref="T:Telerik.Pivot.Core.Coordinate"/> to compare.</param>
            <returns>false if <paramref name="left"/> is equal to <paramref name="right"/>; otherwise, true.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.GroupType">
            <summary>
            Possible IGroup types.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.GroupType.BottomLevel">
            <summary>
            The group has no children and usually an aggregate value is available for it.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.GroupType.GrandTotal">
            <summary>
            The group has aggregated values for all other groups.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.GroupType.Subheading">
            <summary>
            The group contains other groups. Aggregate values may or may not be available.
            </summary>
        </member>
        <member name="F:Telerik.Pivot.Core.GroupType.Subtotal">
            <summary>
            The group contains no subgroups. The aggregate values for this groups parent could be retrieved using this group.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.IAggregateResultProvider">
            <summary>
            This interface provides access to the <see cref="T:Telerik.Pivot.Core.IGroup"/>s and intermediate <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s accumulated during a pivot grouping process.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IAggregateResultProvider.Root">
            <summary>
            A coordinate with the GrandTotal root <see cref="T:Telerik.Pivot.Core.IGroup"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IAggregateResultProvider.GetAggregateResult(System.Int32,Telerik.Pivot.Core.Coordinate)">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> for the <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/> at index <paramref name="aggregateIndex"/> for the row and column <see cref="T:Telerik.Pivot.Core.IGroup"/>s defined by <paramref name="groups"/>.
            </summary>
            <param name="aggregateIndex">The index of the <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/> for which an <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> should be retrieved.</param>
            <param name="groups">A <see cref="T:Telerik.Pivot.Core.Coordinate"/> of the <see cref="T:Telerik.Pivot.Core.IGroup"/>s we want to retrieve value for.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pivot.Core.IGroup">
            <summary>
            A pivot group abstraction.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.Name">
            <summary>
            Gets the name of this group.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.Groups">
            <summary>
            <see cref="T:Telerik.Pivot.Core.IGroup"/>s contained within this <see cref="T:Telerik.Pivot.Core.IGroup"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.HasGroups">
            <summary>
            Gets a value that indicates if the <see cref="P:Telerik.Pivot.Core.IGroup.Groups"/> is empty.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Pivot.Core.IGroup"/>. This instance would be in its parent's <see cref="P:Telerik.Pivot.Core.IGroup.Groups"/> list.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.Type">
            <summary>
            Gets the type of the group.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IGroup.Level">
            <summary>
            Gets the level of the <see cref="T:Telerik.Pivot.Core.IGroup"/>.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.IPivotResults">
            <summary>
            This interface provides access to the <see cref="T:Telerik.Pivot.Core.IGroup"/>s and <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s accumulated during a pivot grouping process.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotResults.RowGroupDescriptions">
            <summary>
            A read-only collection of the <see cref="T:Telerik.Pivot.Core.GroupDescription"/>s used to generate the <see cref="P:Telerik.Pivot.Core.IAggregateResultProvider.Root"/>'s <see cref="P:Telerik.Pivot.Core.Coordinate.RowGroup"/> <see cref="T:Telerik.Pivot.Core.IGroup"/>s tree.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotResults.ColumnGroupDescriptions">
            <summary>
            A read-only collection of the <see cref="T:Telerik.Pivot.Core.GroupDescription"/>s used to generate the <see cref="P:Telerik.Pivot.Core.IAggregateResultProvider.Root"/>'s <see cref="P:Telerik.Pivot.Core.Coordinate.ColumnGroup"/> <see cref="T:Telerik.Pivot.Core.IGroup"/>s tree.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotResults.AggregateDescriptions">
            <summary>
            A read-only collection of the <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/> used to generate the the available <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/>s for the <see cref="M:Telerik.Pivot.Core.IPivotResults.GetAggregateResult(System.Int32,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.IPivotResults.FilterDescriptions">
            <summary>
            A read-only collection of the <see cref="T:Telerik.Pivot.Core.FilterDescription"/> used to filter the items.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IPivotResults.GetAggregateResult(System.Int32,Telerik.Pivot.Core.IGroup,Telerik.Pivot.Core.IGroup)">
            <summary>
            Gets the AggregateValue for the <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/> at index <paramref name="aggregateIndex"/> for the <paramref name="row"/> and <paramref name="column"/> <see cref="T:Telerik.Pivot.Core.IGroup"/>s.
            </summary>
            <param name="aggregateIndex">The index of the <see cref="T:Telerik.Pivot.Core.AggregateDescriptionBase"/> for which a value is retrieved.</param>
            <param name="row">An <see cref="T:Telerik.Pivot.Core.IGroup"/> from the <see cref="P:Telerik.Pivot.Core.IAggregateResultProvider.Root"/>'s <see cref="P:Telerik.Pivot.Core.Coordinate.RowGroup"/> tree.</param>
            <param name="column">An <see cref="T:Telerik.Pivot.Core.IGroup"/> from the <see cref="P:Telerik.Pivot.Core.IAggregateResultProvider.Root"/>'s <see cref="P:Telerik.Pivot.Core.Coordinate.ColumnGroup"/> tree.</param>
            <returns>Null or <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> if it is available.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IPivotResults.GetUniqueKeys(Telerik.Pivot.Core.PivotAxis,System.Int32)">
            <summary>
            Returns the unique keys generated for the GroupDescription located at <see ref="axis" /> at index <see ref="index" />.
            </summary>
            <param name="axis">The axis.</param>
            <param name="index">The GroupDescription index.</param>
            <returns>The unique keys.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IPivotResults.GetUniqueFilterItems(System.Int32)">
            <summary>
            Returns the unique items generated for FilterDescription located at index <paramref name="filterIndex"/>.
            </summary>
            <param name="filterIndex">The FilterDescription index.</param>
            <returns>The unique items.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.NullValue">
            <summary>
            Object that represents Group with null value.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.NullValue.Instance">
            <summary>
            Gets the singleton instance of NullValue class.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.NullValue.ToString">
            <summary>
            Overrides the string representation.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.NullValue.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.NullValue.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.CellAggregateValue">
            <summary>
            Contains the value of an <see cref="T:Telerik.Pivot.Core.Aggregates.AggregateValue"/> and the <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/> that produced it.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CellAggregateValue.RowGroup">
            <summary>
            Gets the row <see cref="T:Telerik.Pivot.Core.IGroup"/> for which this values is generated.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CellAggregateValue.ColumnGroup">
            <summary>
            Gets the column <see cref="T:Telerik.Pivot.Core.IGroup"/> for which this values is generated.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CellAggregateValue.Description">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/> which produced the <see cref="P:Telerik.Pivot.Core.CellAggregateValue.Value"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CellAggregateValue.Value">
            <summary>
            Gets the value for the  some <see cref="T:Telerik.Pivot.Core.IGroup"/>s produced by the <see cref="P:Telerik.Pivot.Core.CellAggregateValue.Description"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.CellAggregateValue.FormattedValue">
            <summary>
            Gets the string representation of the value with the <see cref="T:Telerik.Pivot.Core.IAggregateDescription"/>'s string formats applied.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.CellAggregateValue.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CellAggregateValue.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CellAggregateValue.Equals(Telerik.Pivot.Core.CellAggregateValue)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.CellAggregateValue.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.IHierarchyAdapter">
            <summary>
            Describes a hierarchy.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.IHierarchyAdapter.GetItems(System.Object)">
            <summary>
            Get an enumeration with the child items of the provided <paramref name="item"/>.
            </summary>
            <param name="item">The item children are requested for.</param>
            <returns>The children of the <paramref name="item"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.IHierarchyAdapter.GetItemAt(System.Object,System.Int32)">
            <summary>
            Gets a child of <paramref name="item"/> at the <paramref name="index"/>.
            </summary>
            <param name="item">The item child is requested for.</param>
            <param name="index">The index of the requested child.</param>
            <returns>The child of <paramref name="item"/> at <paramref name="index"/>.</returns>
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyFilterDescriptionBase">
            <summary>
            Report <see cref="T:Telerik.Pivot.Core.FilterDescription"/> implementation.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyFilterDescriptionBase.PropertyName">
            <summary>
            Gets or sets a value identifying a property on the grouped items.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.PropertyFilterDescriptionBase.Condition">
            <summary>
            Gets or sets the <see cref="P:Telerik.Pivot.Core.PropertyFilterDescriptionBase.Condition"/> used to filter the groups.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.ExtractValue(System.Object)">
            <summary>
            Return a value the <paramref name="item"/>.
            </summary>
            <param name="item">The item.</param>
            <returns>A name for the group that would contain the <paramref name="item"/>.</returns>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.GetFilterItem(System.Object)">
            <summary>
            Gets the item that is used in filtering for the provided <paramref name="fact"/>.
            </summary>
            <param name="fact">The data to be filtered.</param>
            <returns>The value used for filtering.</returns>
            <seealso cref="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.PassesFilter(System.Object)"/>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.PassesFilter(System.Object)">
            <summary>
            Checks if a value generated from <see cref="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.GetFilterItem(System.Object)"/> passes the filter.
            </summary>
            <param name="value">The value to filter.</param>
            <returns>True if the <paramref name="value"/> passes the filter; otherwise - false.</returns>
            <seealso cref="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.GetFilterItem(System.Object)"/>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.CloneCore(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <summary>
            Makes the instance a clone (deep copy) of the specified <see cref="T:Telerik.Pivot.Core.Cloneable"/>.
            </summary>
            <param name="source">The object to clone.</param>
            <remarks>Notes to Inheritors
            If you derive from <see cref="T:Telerik.Pivot.Core.Cloneable"/>, you need to override this method to copy all properties.
            It is essential that all implementations call the base implementation of this method (if you don't call base you should manually copy all needed properties including base properties).
            </remarks>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.GetDisplayName">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescriptionBase.GetUniqueName">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.PropertyFilterDescription">
            <summary>
            Report <see cref="T:Telerik.Pivot.Core.FilterDescription"/> implementation.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescription.CreateInstanceCore">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.PropertyFilterDescription.CloneOverride(Telerik.Pivot.Core.Cloneable)">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Pivot.Core.FilterDescription">
            <summary>
            Base class for <see cref="T:Telerik.Pivot.Core.FilterDescription"/>.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.SettingsChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Telerik.Pivot.Core.SettingsNode.SettingsChanged"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsChangedEventArgs.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.SettingsChangedEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Pivot.Core.SettingsChangedEventArgs.OriginalSource">
            <summary>
            Gets the <see cref="T:Telerik.Pivot.Core.SettingsNode"/> from which the change originated.
            </summary>
        </member>
        <member name="T:Telerik.Pivot.Core.SettingsNodeCollection`1">
            <summary>
            A Collection of <see cref="T:Telerik.Pivot.Core.SettingsNode"/> items. Tunnels events from the items to the <see cref="P:Telerik.Pivot.Core.SettingsNodeCollection`1.Parent"/>.
            Does not raises <see cref="E:Telerik.Pivot.Core.SettingsNode.SettingsChanged" /> on collection change.
            </summary>
            <typeparam name="T">A class that inherits the <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.</typeparam>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.#ctor(Telerik.Pivot.Core.SettingsNode)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Pivot.Core.SettingsNodeCollection`1"/> class.
            </summary>
            <param name="parent">The parent <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.</param>
        </member>
        <member name="P:Telerik.Pivot.Core.SettingsNodeCollection`1.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Pivot.Core.SettingsNode"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.SetItem(System.Int32,`0)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.RemoveItem(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.InsertItem(System.Int32,`0)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.ClearItems">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Pivot.Core.SettingsNodeCollection`1.NotifyChange(Telerik.Pivot.Core.SettingsChangedEventArgs)">
            <summary>
            Notifies the Parent <see cref="T:Telerik.Pivot.Core.SettingsNode"/> for a change.
            </summary>
            <param name="settingsEventArgs">The <see cref="T:Telerik.Pivot.Core.SettingsChangedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Diagnostics.Pivot.IPivotTraceWriter">
            <summary>
            Represents a trace writer for RadPivotGrid-related information.
            </summary>
        </member>
        <member name="M:Telerik.Diagnostics.Pivot.IPivotTraceWriter.WriteLine(System.String)">
            <summary>
            Writes a trace text.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="T:Telerik.Diagnostics.Pivot.PivotTrace">
            <summary>
            Exposes RadPivotGrid-related trace infrastructure.
            </summary>
        </member>
        <member name="M:Telerik.Diagnostics.Pivot.PivotTrace.SetTraceWriter(Telerik.Diagnostics.Pivot.IPivotTraceWriter)">
            <summary>
            Sets a trace writer to be used for writing trace messages.
            </summary>
            <param name="newWriter">The new writer.</param>
        </member>
        <member name="T:Telerik.Diagnostics.Pivot.StringTraceWriter">
            <summary>
            Trace writer that writes all trace messages to a string.
            </summary>
        </member>
        <member name="M:Telerik.Diagnostics.Pivot.StringTraceWriter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Diagnostics.Pivot.StringTraceWriter" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Diagnostics.Pivot.StringTraceWriter.WriteLine(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Diagnostics.Pivot.StringTraceWriter.ToString">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Diagnostics.Pivot.TelerikPivotTraceSources">
            <summary>
            Provides debug tracing support that is specifically targeted for applications that use Telerik pivot components.
            </summary>
        </member>
        <member name="P:Telerik.Diagnostics.Pivot.TelerikPivotTraceSources.DataProviderSource">
            <summary>
            Gets a trace source for data providers.
            </summary>
        </member>
    </members>
</doc>
