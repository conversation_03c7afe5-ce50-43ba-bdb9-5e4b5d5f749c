﻿using Everylang.App.OCR.OcrEngine;
using Everylang.Common.LogManager;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace Everylang.App.OCR
{
    internal class OcrWorker
    {

        internal async Task<string> ReadImage(Image image, List<string> langs)
        {
            var langList = OcrManager.Instance.LangDic.Where(pair => langs.Contains(pair.Key)).Select(x => x.Value).ToList();
            var res = await GetText(image, langList);
            if (res == "")
            {
                res = await GetText(InvertImage(image), langList);
            }
            return res.Trim();
        }

        private async Task<string> GetText(Image image, List<string> langList)
        {
            try
            {
                return await image.ConvertToPix()
                    .GetTextAndEnsureData(dataPath: OcrManager.Instance.DirTessdata,
                        language: String.Join("+", langList));

            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

            return "";
        }

        private Image InvertImage(Image image)
        {
            using Bitmap bmpSource = new Bitmap(image);
            Bitmap bmpDest = new Bitmap(bmpSource.Width, bmpSource.Height);
            for (int x = 0; x < bmpSource.Width; x++)
            {
                for (int y = 0; y < bmpSource.Height; y++)
                {
                    Color clrPixel = bmpSource.GetPixel(x, y);
                    clrPixel = Color.FromArgb(255 - clrPixel.R, 255 - clrPixel.G, 255 - clrPixel.B);
                    bmpDest.SetPixel(x, y, clrPixel);
                }
            }

            return bmpDest;
        }
    }
}
