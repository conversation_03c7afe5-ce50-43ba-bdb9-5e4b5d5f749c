<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.SyntaxEditor</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer">
            <summary>Exposes <see cref="T:Telerik.Windows.Controls.RadSyntaxEditor"/> type to UI Automation.</summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the control pattern for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />.
            </summary>
            <param name="patternInterface">A value from the enumeration.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetChildren" />.
            </summary>
            <returns>
            A list of child <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> elements.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetItemStatusCore">
            <summary>
            Gets a string that communicates the visual status of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetItemStatus" />.
            </summary>
            <returns>
            The string that contains the <see cref="P:System.Windows.Automation.AutomationProperties.ItemStatus" /> that is returned by <see cref="M:System.Windows.Automation.AutomationProperties.GetItemStatus(System.Windows.DependencyObject)" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Gets the control type for the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType" />.
            </summary>
            <returns>
            The <see cref="F:System.Windows.Automation.Peers.AutomationControlType.Document" /> enumeration value.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetClassNameCore">
            <summary>
            Gets the name of the <see cref="T:System.Windows.UIElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.UIElementAutomationPeer" />. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetHelpTextCore">
            <summary>
            Gets the string that describes the functionality of the <see cref="T:System.Windows.ContentElement" /> that is associated with this <see cref="T:System.Windows.Automation.Peers.ContentElementAutomationPeer" />. Called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetHelpText" />.
            </summary>
            <returns>
            The help text, usually from the <see cref="T:System.Windows.Controls.ToolTip" />, or <see cref="F:System.String.Empty" /> if there is no help text.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadSyntaxEditorAutomationPeer.GetNameCore">
            <summary>
            Gets the text label of the <see cref="T:System.Windows.ContentElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.ContentElementAutomationPeer"/>. Called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetName"/>.
            </summary>
            <returns>
            The text label of the element that is associated with this automation peer.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs">
            <summary>
            Class CommandErrorEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
            <seealso cref="T:System.EventArgs" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs"/> class.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs.Exception">
            <summary>
            Gets the exception.
            </summary>
            <value>The exception.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs.Handled">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs"/> is handled.
            </summary>
            <value><c>true</c> if handled; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs">
            <summary>
            Class CommandExecutedEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs.#ctor(Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs"/> class.
            </summary>
            <param name="command">The command.</param>
            <param name="commandParameter">The command parameter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs.#ctor(Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs"/> class.
            </summary>
            <param name="command">The command.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs.Command">
            <summary>
            Gets the command.
            </summary>
            <value>The command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs.CommandParameter">
            <summary>
            Gets the command parameter.
            </summary>
            <value>The command parameter.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs">
            <summary>
            Class CommandExecutingEventArgs.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs.#ctor(Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs"/> class.
            </summary>
            <param name="command">The command.</param>
            <param name="commandParameter">The command parameter.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs.Canceled">
            <summary>Gets a value indicating whether this <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs"/> is canceled.</summary>
            <value>
            <c>true</c> if canceled; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs.Command">
            <summary>
            Gets the command.
            </summary>
            <value>The command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs.CommandParameter">
            <summary>
            Gets the command parameter.
            </summary>
            <value>The command parameter.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs.Cancel">
            <summary>
            Cancels this instance.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities">
            <summary>
            Class KeyBindingCollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.Add(System.Windows.Input.InputBinding)">
            <summary>
            Adds the specified input binding.
            </summary>
            <param name="inputBinding">The input binding.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.AddRange(System.Collections.Generic.IEnumerable{System.Windows.Input.InputBinding})">
            <summary>
            Adds the range.
            </summary>
            <param name="inputBindings">The input bindings.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.RegisterCommand(System.Windows.Input.ICommand,System.Windows.Input.Key,System.Windows.Input.ModifierKeys,System.Object)">
            <summary>
            Registers the command.
            </summary>
            <param name="command">The command.</param>
            <param name="key">The key.</param>
            <param name="modifierKeys">The modifier keys.</param>
            <param name="commandParameter">The command parameter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.UnregisterCommand(System.Windows.Input.ICommand)">
            <summary>
            Unregisters the command.
            </summary>
            <param name="command">The command.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.KeyBindingUtilities.SetInputBindings(System.Windows.Input.InputBindingCollection)">
            <summary>
            Sets the input bindings.
            </summary>
            <param name="inputBindings">The input bindings.</param>
            <exception cref="T:System.ArgumentNullException">InputBindings.</exception>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.MoveCaretCommand">
            <summary>
            Class MoveCaretCommand.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.MoveCaretCommand.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.MoveCaretCommand"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.MoveCaretCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether this instance [can execute override] the specified parameter.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns><c>true</c> if this instance [can execute override] the specified parameter; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.MoveCaretCommand.ExecuteOverride(System.Object)">
            <summary>
            Executes the override.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType">
            <summary>
            Caret movement types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToPreviousCharacter">
            <summary>
            MoveToPreviousCharacter type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToNextCharacter">
            <summary>
            MoveToNextCharacter type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveLineUp">
            <summary>
            MoveLineUp type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveLineDown">
            <summary>
            MoveLineDown type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MovePageUp">
            <summary>
            MovePageUp type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MovePageDown">
            <summary>
            MovePageDown type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToNextWord">
            <summary>
            MoveToNextWord type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToPreviousWord">
            <summary>
            MoveToPreviousWord type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToHome">
            <summary>
            MoveToHome type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToLineEnd">
            <summary>
            MoveToLineEnd type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToStartOfDocument">
            <summary>
            MoveToStartOfDocument type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Commands.CaretMovementType.MoveToEndOfDocument">
            <summary>
            MoveToEndOfDocument type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase">
            <summary>
            Class SyntaxEditorCommandBase.
            Implements the <see cref="T:System.Windows.Input.ICommand" />
            </summary>
            <seealso cref="T:System.Windows.Input.ICommand" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.CanExecuteChanged">
            <summary>
            CanExecuteChanged event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.AssociatedSyntaxEditor">
            <summary>
            Gets the associated code editor.
            </summary>
            <value>The associated code editor.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.CanExecute(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in its current state.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require data to be passed, this object can be set to null.</param>
            <returns>true if this command can be executed; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require data to be passed, this object can be set to null.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether this instance [can execute override] the specified parameter.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns><c>true</c> if this instance [can execute override] the specified parameter; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.ExecuteOverride(System.Object)">
            <summary>
            Executes the override.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase.OnCanExecuteChanged">
            <summary>
            Called when [can execute changed].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands">
            <summary>
            Class SyntaxEditorCommands.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.PropertyChanged">
            <summary>
            Property changed event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.OpenFindDialogCommand">
            <summary>
            Gets the open find dialog command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.CloseFindDialogCommand">
            <summary>
            Gets the close find dialog command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.NavigateNextMatchCommand">
            <summary>
            Gets the navigate next match command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.NavigatePreviousMatchCommand">
            <summary>
            Gets the navigate previous match command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.HighlightAllMatchesCommand">
            <summary>
            Gets the highlight all matches command.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.BackspaceCommand">
            <summary>
            Gets the backspace command.
            </summary>
            <value>The backspace command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.CodeCompletionCommand">
            <summary>
            Gets the code completion command.
            </summary>
            <value>The code completion command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.CopyCommand">
            <summary>
            Gets the copy command.
            </summary>
            <value>The copy command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.CutCommand">
            <summary>
            Gets the cut command.
            </summary>
            <value>The cut command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.DeleteCommand">
            <summary>
            Gets the delete command.
            </summary>
            <value>The delete command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.DeleteFullLineCommand">
            <summary>
            Gets the delete full line command.
            </summary>
            <value>The delete full line command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.DeleteWordToLeftCommand">
            <summary>
            Gets the delete word to left command.
            </summary>
            <value>The delete word to left command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.DeleteWordToRightCommand">
            <summary>
            Gets the delete word to right command.
            </summary>
            <value>The delete word to right command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.IndentCommand">
            <summary>
            Gets the indent command.
            </summary>
            <value>The indent command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.MoveCaretCommand">
            <summary>
            Gets the move caret command.
            </summary>
            <value>The move caret command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.PasteCommand">
            <summary>
            Gets the paste command.
            </summary>
            <value>The paste command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.RedoCommand">
            <summary>
            Gets the redo command.
            </summary>
            <value>The redo command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.SelectAllCommand">
            <summary>
            Gets the select all command.
            </summary>
            <value>The select all command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.ToggleInsertModeCommand">
            <summary>
            Gets the toggle insert mode command.
            </summary>
            <value>The toggle insert mode command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.UndoCommand">
            <summary>
            Gets the undo command.
            </summary>
            <value>The undo command.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.UnindentCommand">
            <summary>
            Gets the unindent command.
            </summary>
            <value>The unindent command.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommands.OnPropertyChanged(System.String)">
            <summary>
            Called when a property is changed.
            </summary>
            <param name="name"></param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand">
            <summary>
            Class SyntaxEditorDelegateCommand.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorCommandBase" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand.#ctor(System.Action{Telerik.Windows.Controls.RadSyntaxEditor,System.Object},Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand"/> class.
            </summary>
            <param name="commandAction">The command action.</param>
            <param name="editor">The editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether this instance [can execute override] the specified parameter.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns><c>true</c> if this instance [can execute override] the specified parameter; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand.ExecuteOverride(System.Object)">
            <summary>
            Executes the override.
            </summary>
            <param name="parameter">The parameter.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorEditingCommand">
            <summary>
            Class SyntaxEditorEditingCommand.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorDelegateCommand" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorEditingCommand.#ctor(System.Action{Telerik.Windows.Controls.RadSyntaxEditor,System.Object},Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorEditingCommand"/> class.
            </summary>
            <param name="commandAction">The command action.</param>
            <param name="editor">The editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Commands.SyntaxEditorEditingCommand.CanExecuteOverride(System.Object)">
            <summary>
            Determines whether this instance [can execute override] the specified parameter.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns><c>true</c> if this instance [can execute override] the specified parameter; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager">
            <summary>
            Class FoldingManager.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.Editor">
            <summary>
            Gets the editor.
            </summary>
            <value>The editor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.FoldingRegions">
            <summary>
            Gets the folding regions.
            </summary>
            <value>The folding regions.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.IsUpdateInProgress">
            <summary>
            Gets a value indicating whether this instance is update in progress.
            </summary>
            <value><c>true</c> if this instance is update in progress; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.BeginUpdate">
            <summary>
            Begins the update.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.ClearFoldings">
            <summary>
            Clears the foldings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.CreateFolding(System.Int32,System.Int32,System.Boolean,System.String,System.Object)">
            <summary>
            Creates the folding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.EndUpdate">
            <summary>
            Ends the update.
            </summary>
            <exception cref="T:System.InvalidOperationException">There is no active update to end.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.GetFoldingsContainingIndex(System.Int32)">
            <summary>
            Gets the index of the foldings containing.
            </summary>
            <param name="index">The index.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.GetFoldingsContainingLine(System.Int32)">
            <summary>
            Gets the foldings containing line.
            </summary>
            <param name="lineNumber">The line number.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.GetFoldingsIntersectingSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the foldings intersecting span.
            </summary>
            <param name="span">The span.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.RemoveFolding(Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion)">
            <summary>
            Removes the folding.
            </summary>
            <param name="foldingRegion">The folding region.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingManager.UnfoldAllRegionsContaningIndex(System.Int32)">
            <summary>
            Unfolds the index of all regions containing.
            </summary>
            <param name="index">The index.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion">
            <summary>
            Class FoldingRegion.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.FirstLineNumber">
            <summary>
            Gets the first line number.
            </summary>
            <value>The first line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.FoldedToolTipContent">
            <summary>
            Gets or sets the content of the folded tool tip.
            </summary>
            <value>The content of the folded tool tip.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.IsFolded">
            <summary>
            Gets or sets a value indicating whether this instance is folded.
            </summary>
            <value><c>true</c> if this instance is folded; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.LastLineNumber">
            <summary>
            Gets the last line number.
            </summary>
            <value>The last line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.Span">
            <summary>
            Gets the span.
            </summary>
            <value>The span.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.Title">
            <summary>
            Gets or sets the title.
            </summary>
            <value>The title.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to this instance.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Folding.FoldingRegion.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Indentation.DefaultIndentProvider">
            <summary>
            Class DefaultIndentProvider.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Indentation.ISmartIndentProvider" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.Indentation.ISmartIndentProvider" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Indentation.DefaultIndentProvider.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Indentation.DefaultIndentProvider"/> class.
            </summary>
            <param name="associatedEditor">The associated editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Indentation.DefaultIndentProvider.GetDesiredIndentation(Telerik.Windows.SyntaxEditor.Core.Text.TextSnapshotLine)">
            <summary>
            Gets the desired indentation.
            </summary>
            <param name="line">The line.</param>
            <returns>System.Nullable&lt;System.Int32&gt;.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Indentation.ISmartIndentProvider">
            <summary>
            Interface ISmartIndentProvider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Indentation.ISmartIndentProvider.GetDesiredIndentation(Telerik.Windows.SyntaxEditor.Core.Text.TextSnapshotLine)">
            <summary>
            Gets the desired indentation.
            </summary>
            <param name="line">The line.</param>
            <returns>System.Nullable&lt;System.Int32&gt;.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.DarkPalette">
            <summary>
            Represents a 'dark' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> suitable for dark themes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.DarkPalette.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.DarkPalette"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.DarkPalette.PaletteName">
            <summary>
            Gets the palette name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.LightPalette">
            <summary>
            Represents a 'light' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> suitable for light themes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.LightPalette.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.LightPalette"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.LightPalette.PaletteName">
            <summary>
            Gets the palette name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralPalette">
            <summary>
            Represents a 'neutral' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> suitable for the high contrast and transparent themes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralPalette.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralPalette"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralPalette.PaletteName">
            <summary>
            Gets the palette name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralDarkPalette">
            <summary>
            Represents a 'neutral' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> suitable for the high contrast and dark themes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralDarkPalette.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralDarkPalette"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.NeutralDarkPalette.PaletteName">
            <summary>
            Gets the palette name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette">
            <summary>
            Represents palette of colors for coloring the SyntaxEditor syntax-related words.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.KeywordColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.KeywordColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.PreprocessorKeywordColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.PreprocessorKeywordColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.CommentColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.CommentColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.StringColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.StringColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.IdentifierColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.IdentifierColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.OperatorColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.OperatorColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlAttributeColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlAttributeColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlElementColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlElementColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCommentColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCommentColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlContentColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlContentColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlStringColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlStringColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlTagColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlTagColor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCharacterDataColorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCharacterDataColor"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.KeywordColor">
            <summary>
            Keyword color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.PreprocessorKeywordColor">
            <summary>
            PreprocessorKeyword color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.CommentColor">
            <summary>
            Comment color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.IdentifierColor">
            <summary>
            Identifier color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.OperatorColor">
            <summary>
            Operator color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.StringColor">
            <summary>
            String color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlAttributeColor">
            <summary>
            XmlAttribute color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlElementColor">
            <summary>
            XmlElement color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCommentColor">
            <summary>
            XmlComment color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlContentColor">
            <summary>
            XmlContent color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlStringColor">
            <summary>
            XmlString color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlTagColor">
            <summary>
            XmlTag color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.XmlCharacterDataColor">
            <summary>
            XmlCharacterData color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.PaletteName">
            <summary>
            Gets the palette name.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette.CreateInstanceCore">
            <summary>
            Creates new instance of <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> class.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter">
            <summary>
            Type converter class for <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether the type converter can convert an object to the specified type.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="destinationType">The type you want to convert to.</param>
            <returns>
            True if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether the type converter can convert an object from the specified type to the type of this converter.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="sourceType">The type you want to convert from.</param>
            <returns>
            True if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts from the specified value to the intended conversion type of the converter.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The value to convert to the type of this converter.</param>
            <returns>The converted value.</returns>
            <exception cref="T:System.NotImplementedException">
            <see cref="M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)"/> not implemented in base <see cref="T:System.ComponentModel.TypeConverter"/>.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the specified value object to the specified type.
            </summary>
            <param name="context">An object that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The object to convert.</param>
            <param name="destinationType">The type to convert the object to.</param>
            <returns>The converted object.</returns>
            <exception cref="T:System.NotImplementedException">
            <see cref="M:System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)"/>  not implemented in base <see cref="T:System.ComponentModel.TypeConverter"/>.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Returns whether this object supports a standard set of values that can
            be picked from a list, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <returns>
            Returns true if <see cref="M:System.ComponentModel.TypeConverter.GetStandardValues" />
            should be called to find a common set of values the object supports; otherwise,
            false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPaletteConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Returns a collection of standard values for the data type this type
            converter is designed for when provided with a format context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context that can be used to extract additional information
            about the environment from which this converter is invoked. This parameter or
            properties of this parameter can be null.</param>
            <returns>
            A <see cref="T:System.ComponentModel.TypeConverter.StandardValuesCollection" />
            that holds a standard set of valid values, or null if the data type does not
            support a standard set of values.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes">
            <summary>
            Class for providing instances of the built-in <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/>s.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.LightPaletteName">
            <summary>
            The name of the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Light"/> palette.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.DarkPaletteName">
            <summary>
            The name of the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Dark"/> palette.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.NeutralPaletteName">
            <summary>
            The name of the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Neutral"/> palette.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.NeutralDarkPaletteName">
            <summary>
            The name of the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.NeutralDark"/> palette.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Light">
            <summary>
            Represents a 'light' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Dark">
            <summary>
            Represents a 'dark' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.Neutral">
            <summary>
            Represents a 'neutral' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxPalettes.NeutralDark">
            <summary>
            Represents a 'neutral dark' <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette"/> instance.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger">
            <summary>
            Tagger class responsible for creating collapsible (folding) regions in code document.
            Collapsible section is defined with opening and closing bracket symbols.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.OpeningBracket">
            <summary>
            Gets or sets the opening section symbol.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.ClosingBracket">
            <summary>
            Gets or sets the closing section symbol.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.BuildBracketFoldings(System.String)">
            <summary>
            Rebuilds the bracket folding sections.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.GetFoldingRegionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the folding region's tooltip content.
            </summary>
            <param name="foldingSpan">The span starting from the regions' start text and ending after the regions' end text.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.BracketFoldingTagger.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition">
            <summary>
            Defines the start and end strings of a collapsible (folding) code section. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition"/> class.
            </summary>
            <param name="startText">The defining start text.</param>
            <param name="endText">The defining end text.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition.StartText">
            <summary>
            Gets or sets the defining start text of the region definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition.EndText">
            <summary>
            Gets or sets the defining end text of the region definition.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch">
            <summary>
            Represents a folding start or end match structure. It keeps the index of the match and the start/end tag (keyword) which is matched in the document.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.Index">
            <summary>
            Gets the index of the folding match.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.Tag">
            <summary>
            Gets the tag (keyword) of the folding match. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.IsStart">
            <summary>
            Gets a value indicating whether the folding match is start or end of a folding region.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.IsUsed">
            <summary>
            Gets or sets a value indicating whether the folding match is already used in a folding region.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_Equality(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the == operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_Inequality(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the != operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_LessThan(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the &lt; operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_GreaterThan(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the &gt; operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_LessThanOrEqual(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the &lt;= operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.op_GreaterThanOrEqual(Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch,Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch)">
            <summary>
            Implements the &gt;= operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.CompareTo(System.Object)">
            <summary>
             Compares the current instance with another object of the same type and returns
             an integer that indicates whether the current instance precedes, follows, or
             occurs in the same position in the sort order as the other object.
             </summary>
            <param name="obj">An object to compare with this instance.</param>
            <returns>A value that indicates the relative order of the objects being compared.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to this instance.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase">
            <summary>
            Base class for collecting folding (collapsible) sections in document.
            Override this class and add ExpandableRegionDefinitions to define the start / end matching rules of a collapsible section.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase"/> class.
            </summary>
            <param name="editor">The syntax editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.FoldingRegionDefinitions">
            <summary>
            The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionDefinition"/>s that this tagger will use for matching the collapsible regions in the document.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.IsMatchingCaseSensitive">
            <summary>
            Determines whether the matching between the start and end folding tags is case sensitive.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.RegionsStartToEndMap">
            <summary>
            Contains the start / end matching pairs key-value pairs.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.FoldingRegionTags">
            <summary>
            List of all <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.FoldingRegionTag"/>s wrapped in TagSpans.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets the tags.
            </summary>
            <param name="spans">The spans.</param>
            <returns>Collection of folding tag spans.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.BuildStartRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching start of collapsible folding section.
            Default implementation is to join all start strings of provided collapsible sections.
            Example for start words Start and Move, pattern is "Start|Move".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The start block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.BuildEndRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching end of collapsible folding section.
            Default implementation is to join all end strings of provided collapsible sections.
            Example for end words End and Finish, pattern is "End|Finish".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The end block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.GetFoldingRegionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the folding region's tooltip content.
            </summary>
            <param name="foldingSpan">The span starting from the regions' start text and ending after the regions' end text.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.CoerceFoldingSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Coerces the default provided folding span - span starting from the start of the region's start text and ending in the end of the region's end text.
            </summary>
            <param name="defaultFoldingSpan">The default provided folding span.</param>
            <returns>The coerced folding span.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.ProcessNotMatchedTags(System.Collections.Generic.IEnumerable{Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch},System.Collections.Generic.IEnumerable{Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingRegionMatch})">
            <summary>
            Tries to match the not matched start and end tags left from the standard stack matching algorithm.
            </summary>
            <param name="startTags">The not matched start tags.</param>
            <param name="endTags">The not matched end tags.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.InvalidateFoldingRegions">
            <summary>
            Clears all collapsible (folding) regions and rebuilds them.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.FoldingTaggerBase.RefreshStartEndMap">
            <summary>
            Prepares the start / end matches for use as key-value pairs.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger">
            <summary>
            Tagger class responsible for creating collapsible (folding) regions in JavaScript code document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <param name="editor">The syntax editor.</param>
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger.BuildCommentsSectionFoldedRegions">
            <summary>
            Builds the multiline comment folding regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptFoldingTagger.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger">
            <summary>
            Tagger class responsible for creating collapsible (folding) regions in VisualBasic code document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger"/> class.
            </summary>
            <param name="editor">The syntax editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.BuildStartRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching start of collapsible folding section.
            Default implementation is to join all start strings of Visual Basic collapsible sections.
            Start strings pattern is excluding End words.
            Example for start words Sub, Get and Class, pattern is "\\b(?&lt;!End )Sub\\b|\\b(?&lt;!End )Get\\b(?&lt;!End )Class\\b".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The start block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.BuildEndRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching end of collapsible folding section.
            Default implementation is to join all end strings of provided collapsible sections.
            Example for end words End and Finish, pattern is "End|Finish".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The end block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.CoerceFoldingSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Coerces the default provided folding span - span starting from the start of the region's start text and ending in the end of the region's end text.
            </summary>
            <param name="defaultFoldingSpan">The default provided folding span.</param>
            <returns>The coerced folding span.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.BuildImportsFoldingRegion">
            <summary>
            Builds the 'Imports' folding region.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.BuildCommentsSectionFoldedRegions">
            <summary>
            Builds the multiline comment folding regions.
            Linear iteration of lines, linear iteration of single line because Regex matching slows down the performance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.GetImportsToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the Imports folding region's tooltip content.
            </summary>
            <param name="importsSpan">Span containing the imports section.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.CoerceImportsSectionSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Coerces the default Imports folding region's span.
            </summary>
            <param name="defaultImportsSpan">Default Imports region span is from the end of the first Import word to the end of the line containing the last Imports word.</param>
            <returns>The coerced span.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.VisualBasicFoldingTagger.GetFoldingRegionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the folding region's tooltip content.
            </summary>
            <param name="foldingSpan">The span starting from the regions' start text and ending after the regions' end text.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger">
            <summary>
            Tagger class responsible for creating collapsible (folding) regions in XML, XAML and HTML code documents.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <param name="editor">The syntax editor.</param>
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.BuildStartRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching start of collapsible folding section.
            Default implementation is to join all start strings of provided collapsible sections.
            Example for start words Start and Move, pattern is "Start|Move".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The start block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.BuildEndRegionBlockPattern(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Prepares patters for matching end of collapsible folding section.
            Default implementation is to join all end strings of provided collapsible sections.
            Example for end words End and Finish, pattern is "End|Finish".
            </summary>
            <param name="startToEndMap">Pairs of start/end collapsible section words.</param>
            <returns>The end block pattern.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.CoerceFoldingSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Coerces the default provided folding span - span starting from the start of the region's start text and ending in the end of the region's end text.
            </summary>
            <param name="defaultFoldingSpan">The default provided folding span.</param>
            <returns>The coerced folding span.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.GetFoldingRegionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the folding region's tooltip content.
            </summary>
            <param name="foldingSpan">The span starting from the regions' start text and ending after the regions' end text.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlFoldingTagger.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger">
            <summary>
            Class defining T-SQL (Transact-SQL) programming language keywords and comments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.StringMatchingRegex">
            <summary>
            Gets or sets the regex pattern used to match strings in the documents which this tagger recognizes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.GetWordsToClassificationTypes">
            <summary>
            Gets the words to classification types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.RebuildMultilineTags">
            <summary>
            Rebuilds the MultilineTags collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.GetCharType(System.Char)">
            <summary>
            Gets the type of the SQL char.
            3 - SQL commenting char, 2 - punctuation or symbol, 1 - whitespace, 0 - letter and all other chars.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.SqlTagger.OnWordSplit(System.Int32,System.String)">
            <summary>
            Called when a word is split during processing of a line string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger">
            <summary>
            Tagger class responsible for creating collapsible (folding) regions in CSharp code document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <param name="editor">The syntax editor.</param>
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.RebuildFoldingRegions">
            <summary>
            Rebuilds the list of all collapsible (folding) regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.BuildUsingSectionFoldedRegion">
            <summary>
            Builds the 'usings' folding region.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.BuildCommentsSectionFoldedRegions">
            <summary>
            Builds the multiline comment folding regions.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.BuildUsingSectionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the 'usings' folding region's tooltip content.
            </summary>
            <param name="usingSpan">Span containing the usings section.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.CoerceUsingSectionSpan(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Coerces the default 'usings' folding region's span.
            </summary>
            <param name="defaultUsingSpan">Default 'usings' region span is from the end of the first using word to the end of the line containing the last using word.</param>
            <returns>The coerced span.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.GetFoldingRegionTitle(System.String,Telerik.Windows.SyntaxEditor.Core.Text.Span,System.String)">
            <summary>
            Gets the text displayed in the collapsed folding section.
            </summary>
            <param name="inputValue">The document string.</param>
            <param name="span">The span defining the folded region.</param>
            <param name="startText">The start text of the collapsible (folding) section.</param>
            <returns>The region title.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.CSharpFoldingTagger.GetFoldingRegionToolTipContent(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Gets the folding region's tooltip content.
            </summary>
            <param name="foldingSpan">The span starting from the regions' start text and ending after the regions' end text.</param>
            <returns>The tooltip content.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger">
            <summary>
            Class defining JavaScript programming language keywords and comments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.StringMatchingRegex">
            <summary>
            Gets or sets the regex pattern used to match strings in the documents which this tagger recognizes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.GetWordsToClassificationTypes">
            <summary>
            Gets the words to classification types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.RebuildMultilineTags">
            <summary>
            Rebuilds the MultilineTags collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.GetCharType(System.Char)">
            <summary>
            Gets the type of the JavaScript char.
            3 - JavaScript commenting char, 2 - punctuation or symbol, 1 - whitespace, 0 - letter and all other chars.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.JavaScriptTagger.OnWordSplit(System.Int32,System.String)">
            <summary>
            Called when a word is split during processing of a line string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper">
            <summary>
            Class defining XML main language main parts as text format definitions.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlAttribute">
            <summary>
            XML attribute classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlAttributeFormatDefinition">
            <summary>
            XML attribute format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlComment">
            <summary>
            XML comment classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlCommentFormatDefinition">
            <summary>
            XML comment format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlContent">
            <summary>
            XML content classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlContentFormatDefinition">
            <summary>
            XML content format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlElement">
            <summary>
            XML element classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlElementFormatDefinition">
            <summary>
            XML element format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlString">
            <summary>
            XML string classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlStringFormatDefinition">
            <summary>
            XML string format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlTag">
            <summary>
            XML tag classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlTagFormatDefinition">
            <summary>
            XML tag format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlCharacterData">
            <summary>
            XML character data classification type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.XmlCharacterDataFormatDefinition">
            <summary>
            XML character data format definition.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlSyntaxHighlightingHelper.GetXmlClassificationType(System.String)">
            <summary>
            Gets the XML classification type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger">
            <summary>
            Class for tagging XML language words in a text.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.EnableMultilineTags">
            <summary>
            Gets or sets a value indicating whether multiline tags are collected by this tagger instance.
            The method <see cref="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.RebuildMultilineTags"/> process all document and collects Multiline tags in the  <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.MultilineTags"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.MultilineTags">
            <summary>
            The list of multiline <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationTag"/>s wrapped in TagSpans.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets the tags.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.RebuildMultilineTags">
            <summary>
            Rebuilds the MultilineTags collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Taggers.XmlTagger.InvalidateMultilineTags">
            <summary>
            Clears all multiline tags and rebuilds them.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightMode">
            <summary>
            Determines which part of a span will be highlighted.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightMode.TextOnly">
            <summary>
            Highlights only the text portion of the span.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightMode.LineStartToTextEnd">
            <summary>
            Highlights from the beginning of the line to the end of the text portion of the span.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger">
            <summary>
            Class defining C# programming language keywords and comments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.StringMatchingRegex">
            <summary>
            Gets or sets the regex pattern used to match strings in the documents which this tagger recognizes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.GetWordsToClassificationTypes">
            <summary>
            Gets the words to classification types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.RebuildMultilineTags">
            <summary>
            Rebuilds the MultilineTags collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.GetCharType(System.Char)">
            <summary>
            Gets the type of the CSharp char.
            3 - CSharp commenting char, 2 - punctuation or symbol, 1 - whitespace, 0 - preprocessor, letter and all other chars.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.CSharpTagger.OnWordSplit(System.Int32,System.String)">
            <summary>
            Called when a word is split during processing of a line string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger">
            <summary>
            Class LineHighlightTagger which prepares collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.TextHighlightTag"/>s for a collection of lines.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger.LineHighlightFormatDefinition">
            <summary>
            The line highlight format definition.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor,Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger.HighlightMode">
            <summary>
            Determines what part of the line will be highlighted by the tagger.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets a collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.TextHighlightTag"/> tags.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.LineHighlightTagger.HighlightLines(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Updates the highlighted lines.
            </summary>
            <param name="lines">The lines to highlight.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger">
            <summary>
            Class TextSearchUnderlineTagger prepares collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.UnderlineTag"/>s for all occurrences of a given search word.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger.ErrorUnderlineDefinition">
            <summary>
            The error underline definition.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger.WarningUnderlineDefinition">
            <summary>
            The warning underline definition.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor,Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger"/> class.
            </summary>
            <param name="editor">The editor.</param>
            <param name="definitionKey">The ITextFormatDefinitionKey.</param>
            
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger.UpdateSearchWord(System.String)">
            <summary>
            Updates the search word.
            </summary>
            <param name="newSearchWord">The search word.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchUnderlineTagger.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets the tags.
            </summary>
            <param name="spans">The spans.</param>
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger">
            <summary>
            Class TextSearchHighlightTagger which prepares collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.TextHighlightTag"/>s for all occurrences of a given search word.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.SearchFormatDefinition">
            <summary>
            The search format definition.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.SelectedWordFormatDefinition">
            <summary>
            The selected word format definition.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.#ctor(Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor,Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.Comparison">
            <summary>
            Gets or sets the search comparison type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets a collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.TextHighlightTag"/> tags.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger.UpdateSearchWord(System.String)">
            <summary>
            Updates the search word.
            </summary>
            <param name="newSearchWord">The search word.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger">
            <summary>
            Class defining Visual Basic programming language keywords and comments.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger"/> class.
            </summary>
            <param name="editor">The code editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.StringMatchingRegex">
            <summary>
            Gets or sets the regex pattern used to match strings in the documents which this tagger recognizes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.GetWordsToClassificationTypes">
            <summary>
            Gets the words to classification types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.SplitIntoWords(System.String)">
            <summary>
            Splits the given string into collection of words.
            </summary>
            <param name="value">The given string to split.</param>
            <returns>List of the words.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.GetCharType(System.Char)">
            <summary>
            Gets the type of the VisualBasic char.
            3 - VB commenting char, 2 - punctuation or symbol, 1 - whitespace, 0 - preprocessor, letter and all other chars.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.VisualBasicTagger.OnWordSplit(System.Int32,System.String)">
            <summary>
            Called when a word is split during processing of a line string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase">
            <summary>
            Base tagger class for classification of words.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.EnableMultilineTags">
            <summary>
            Gets or sets a value indicating whether multiline tags are collected by this tagger instance.
            The method <see cref="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.RebuildMultilineTags"/> process all document and collects Multiline tags in the  <see cref="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.MultilineTags"/> property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.MultilineTags">
            <summary>
            The list of multiline <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationTag"/>s wrapped in TagSpans.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.StringMatchingRegex">
            <summary>
            Gets or sets the regex pattern used to match strings in the documents which this tagger recognizes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.GetTags(Telerik.Windows.SyntaxEditor.Core.Text.NormalizedSnapshotSpanCollection)">
            <summary>
            Gets the tags.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.AddWord(System.String,Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationType)">
            <summary>
            Adds a new word-type pair in the dictionary.
            </summary>
            <param name="word"></param>
            <param name="classificationType"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.TryRemoveWord(System.String)">
            <summary>
            Tries to removes a given word from the dictionary.
            </summary>
            <param name="word"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.GetCharType(System.Char)">
            <summary>
            Defines the different char types which is the essence of splitting words.
            A word is considered sequence of equally typed chars.
            1 - whitespace, 2 - punctuation or symbol, 0 - letters and all other characters.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.RebuildMultilineTags">
            <summary>
            Rebuilds the MultilineTags collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.InvalidateMultilineTags">
            <summary>
            Clears all multiline tags and rebuilds them.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.SplitIntoWords(System.String)">
            <summary>
            Splits the given string into collection of words.
            </summary>
            <param name="value">The given string to split.</param>
            <returns>List of the words.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.OnWordSplit(System.Int32,System.String)">
            <summary>
            Called when a word is split during processing of a line string.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.TryGetClassificationType(System.String,Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationType@)">
            <summary>
            Tries to get the classification type for the given string word.
            </summary>
            <param name="word">The string word.</param>
            <param name="classificationType">The result classification type.</param>
            <returns>Returns true if classification type is found, otherwise - false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.WordTaggerBase.GetWordsToClassificationTypes">
            <summary>
            Gets the words to classification types.
            </summary>
            <returns>Dictionary&lt;System.String, ClassificationType&gt;.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1">
            <summary>
            A margin which can be added to the RadSyntaxEditor and holds indicators for each line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1"/> class.
            </summary>
            <param name="syntaxEditor">The RadSyntaxEditor instance.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.MeasureOverride(System.Windows.Size)">
            <summary>
            Called when the margin is measured.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the user interface of the margin.
            </summary>
            <param name="updateContext">The UIUpdateContext.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.UpdateIndicator(`0,System.Int32)">
            <summary>
            Called when an indicator needs to be updated.
            </summary>
            <param name="indicator">The indicator.</param>
            <param name="lineNumber">The line number the indicator is placed on.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.UpdateMarginPropertiesCache">
            <summary>
            Updates the properties of the margin which need to be cached.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMargin`1.MarginPropertiesChanged">
            <summary>
            Determines whether any of the properties of the margin which require a redraw has changed.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase">
            <summary>
            A base class for margins which can be added to the RadSyntaxEditor and holds indicators for each line.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.IndicatorBrushProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.IndicatorBrush"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.IndicatorsProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.Indicators"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase"/> class.
            </summary>
            <param name="syntaxEditor">The RadSyntaxEditor instance.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.IndicatorBrush">
            <summary>
            The brush used to color the indicators.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.Indicators">
            <summary>
            A collection of indicators currently added to the margin.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.Canvas">
            <summary>
            The canvas on which the indicators are drawn.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.IndicatorsMarginBase.HandleMouseLeftButtonDown(System.Windows.Point)">
            <summary>
            Handles mouse left button.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.LineWrapIndicatorMargin.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.LineWrapIndicatorMargin"/> class.
            </summary>
            <param name="syntaxEditor">The RadSyntaxEditor instance.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.LineWrapIndicatorMargin.UpdateIndicator(System.Windows.Shapes.Path,System.Int32)">
            <summary>
            Called when an indicator needs to be updated. This can happen when the indicator is
            first created, when it is brought inside or outside of the viewport or when
            the EditorFontSize property of the RadSyntaxEditor or the IndicatorBrush property
            of the margin change.
            </summary>
            <param name="path">The Path to update.</param>
            <param name="lineNumber">The line number the indicator is placed on.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.LineWrapIndicatorMargin.HandleMouseLeftButtonDown(System.Windows.Point)">
            <summary>
            Handles mouse left button.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin">
            <summary>
            A margin which can be added to the RadSyntaxEditor control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin"/> class.
            </summary>
            <param name="editor">The RadSyntaxEditor instance.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin.Editor">
            <summary>
            The RadSyntaxEditor instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin.UpdateUI">
            <summary>
            Called when the UI of the margin needs to be updated.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin.UpdateUI(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Called when the UI of the margin needs to be updated.
            </summary>
            <param name="updateContext">The UIUpdateContext.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargin.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the user interface of the margin.
            </summary>
            <param name="updateContext"></param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMarginsCollection">
            <summary>
            Class SyntaxEditorMarginsCollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMarginsCollection.UpdateUI(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins">
            <summary>
            Class SyntaxEditorMargins.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.Bottom">
            <summary>
            Gets the bottom.
            </summary>
            <value>The bottom.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.Left">
            <summary>
            Gets the left.
            </summary>
            <value>The left.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.Right">
            <summary>
            Gets the right.
            </summary>
            <value>The right.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.ScrollableBottom">
            <summary>
            Gets the scrollable bottom.
            </summary>
            <value>The scrollable bottom.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.ScrollableLeft">
            <summary>
            Gets the scrollable left.
            </summary>
            <value>The scrollable left.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.ScrollableRight">
            <summary>
            Gets the scrollable right.
            </summary>
            <value>The scrollable right.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.ScrollableTop">
            <summary>
            Gets the scrollable top.
            </summary>
            <value>The scrollable top.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.Top">
            <summary>
            Gets the top.
            </summary>
            <value>The top.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.ClearAll">
            <summary>
            Clears all margins.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Margins.SyntaxEditorMargins.UpdateUI(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs">
            <summary>
            Class TextSearchedEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
            <seealso cref="T:System.EventArgs" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs.#ctor(System.String,System.Nullable{Telerik.Windows.SyntaxEditor.Core.Text.Span},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs"/> class.
            </summary>
            <param name="searchText">The searched text.</param>
            <param name="foundSpan">The found span.</param>
            <param name="isSearchForward">Is search forward.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs.FoundSpan">
            <summary>
            Gets the found span object.
            </summary>
            <value>The text.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs.SearchText">
            <summary>
            Gets the search text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs.IsSearchForward">
            <summary>
            Gets the search direction.
            </summary>
            <value>The text.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter">
            <summary>
            Editing, Scrolling, Layers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Dispose">
            <summary>
            Called when instance is disposed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.MaxSize">
            <summary>
            The maximum size constant.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.DragMargin">
            <summary>
            The drag margin.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.ViewportChanged">
            <summary>
            Occurs when [viewport changed].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Caret">
            <summary>
            Gets the caret.
            </summary>
            <value>The caret.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.CaretDisplayMode">
            <summary>
            Gets or sets the caret display mode.
            </summary>
            <value>The caret display mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Telerik#Windows#Controls#SyntaxEditor#UI#ISyntaxEditorPresenter#Editor">
            <summary>
            Gets the editor.
            </summary>
            <value>The editor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.HorizontalScrollOffset">
            <summary>
            Gets or sets the horizontal scroll offset.
            </summary>
            <value>The horizontal scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Telerik#Windows#Controls#SyntaxEditor#UI#ISyntaxEditorPresenter#HorizontalScrollOffset">
            <summary>
            Gets the horizontal scroll offset.
            </summary>
            <value>The horizontal scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.IsPresenterFocused">
            <summary>
            Gets a value indicating whether this instance is presenter focused.
            </summary>
            <value><c>true</c> if this instance is presenter focused; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Owner">
            <summary>
            Gets or sets the owner.
            </summary>
            <value>The owner.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.VerticalScrollOffset">
            <summary>
            Gets or sets the vertical scroll offset.
            </summary>
            <value>The vertical scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Telerik#Windows#Controls#SyntaxEditor#UI#ISyntaxEditorPresenter#VerticalScrollOffset">
            <summary>
            Gets the vertical scroll offset.
            </summary>
            <value>The vertical scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.Viewport">
            <summary>
            Gets the viewport.
            </summary>
            <value>The viewport.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.FocusCaret">
            <summary>
            Focuses the caret.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.GetDocumentPointFromPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Gets the document point from position.
            </summary>
            <param name="position">The position.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.GetDocumentPointFromViewPoint(System.Windows.Point)">
            <summary>
            Gets the document point from view point.
            </summary>
            <param name="point">The point.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.GetPositionFromViewPoint(System.Windows.Point)">
            <summary>
            Gets the position from view point.
            </summary>
            <param name="point">The position.</param>
            <returns>CaretPosition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.GetViewPointFromDocumentPoint(System.Windows.Point)">
            <summary>
            Gets the view point from document point.
            </summary>
            <param name="point">The point.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.GetViewPointFromPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Gets the view point from position.
            </summary>
            <param name="position">The position.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.HideDropMarker">
            <summary>
            Hides the drop marker.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.InvalidateLayout(System.Boolean)">
            <summary>
            Invalidates the layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.MoveCaretToPositionInView(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,System.Windows.Point,System.Boolean)">
            <summary>
            Moves the caret to position in view.
            </summary>
            <param name="caretPosition">The caret position.</param>
            <param name="positionInView">The position in view.</param>
            <param name="moveToNextIfOutOfBox">If set to <c>true</c> [move to next if out of box].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.MoveDropMarker(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Moves the drop marker.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.SetHorizontalOffset(System.Double)">
            <summary>
            Sets the horizontal offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.SetVerticalOffset(System.Double)">
            <summary>
            Sets the vertical offset.
            </summary>
            <param name="offset">The offset.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.ShowDropMarker(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Shows the drop marker.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.UpdateScrollBar(System.Double,System.Double,System.Windows.Controls.Primitives.ScrollBar,System.Windows.Controls.ScrollBarVisibility)">
            <summary>
            Updates the scroll bar.
            </summary>
            <param name="viewportSize">Size of the viewport.</param>
            <param name="max">The maximum.</param>
            <param name="scrollBar">The scroll bar.</param>
            <param name="suggestedVisibility">The suggested visibility.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.ArrangeOverride(System.Windows.Size)">
            <summary>
            When overridden in a derived class, positions child elements and determines a size for a <see cref="T:System.Windows.FrameworkElement" /> derived class.
            </summary>
            <param name="arrangeBounds">The final area within the parent that this element should use to arrange itself and its children.</param>
            <returns>The actual size used.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.InvalidateAndUpdateLayout">
            <summary>
            Invalidates the and update layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.InvalidateLayout">
            <summary>
            Invalidates the layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.MeasureOverride(System.Windows.Size)">
            <summary>
            When overridden in a derived class, measures the size in layout required for child elements and determines a size for the <see cref="T:System.Windows.FrameworkElement" />-derived class.
            </summary>
            <param name="constraint">The available size that this element can give to child elements. Infinity can be specified as a value to indicate that the element will size to whatever content is available.</param>
            <returns>The size that this element determines it needs during layout, based on its calculations of child element sizes.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Invoked whenever an unhandled <see cref="E:System.Windows.UIElement.GotFocus" /> event reaches this element in its route.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.UIElement.MouseLeftButtonDown" /> routed event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the left mouse button was pressed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.UIElement.MouseLeftButtonUp" /> routed event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the left mouse button was released.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseMove" /> attached event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnMouseRightButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.UIElement.MouseRightButtonDown" /> routed event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the right mouse button was pressed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter.OnViewportChanged">
            <summary>
            Called when [viewport changed].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties">
            <summary>
            Represents properties class for SyntaxEditor.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.TabSizePropertyName">
            <summary>
            The tab size property name.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.KeepTabs">
            <summary>
            Gets or sets a value indicating whether [keep tabs].
            </summary>
            <value><c>true</c> if [keep tabs]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.ShowEndOfLine">
            <summary>
            Gets or sets a value indicating whether [show end of line].
            </summary>
            <value><c>true</c> if [show end of line]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.ShowSpaces">
            <summary>
            Gets or sets a value indicating whether [show spaces].
            </summary>
            <value><c>true</c> if [show spaces]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.ShowTabs">
            <summary>
            Gets or sets a value indicating whether [show tabs].
            </summary>
            <value><c>true</c> if [show tabs]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.TabSize">
            <summary>
            Gets or sets the size of the tab.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.TabString">
            <summary>
            Gets the tab string.
            </summary>
            <value>The tab string.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.IsWordWrapEnabled">
            <summary>
            Gets or sets a value indicating whether the word wrap mode is enabled.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorProperties.OnPropertyChanged(System.String)">
            <summary>
            Called when [property changed].
            </summary>
            <param name="propertyName">Name of the property.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton">
            <summary>
            Folded region button implementation class..
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.TextProperty">
            <summary>
            Registers the text dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.DoubleClick">
            <summary>
            Occurs on mouse double click.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.Text">
            <summary>
            Gets or sets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.OnDoubleClick">
            <summary>
            Called when [double click].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.FoldedRegionButton.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.UIElement.MouseLeftButtonDown" /> routed event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the left mouse button was pressed.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.FoldingToggleButton">
            <summary>
            Represents a folding toggle button.
            </summary>
            <seealso cref="T:System.Windows.Controls.Primitives.ToggleButton" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.FoldingToggleButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.FoldingToggleButton"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.FoldingToggleButton.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox">
            <summary>
            Represents a CaretTextBox control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.TextInserted">
            <summary>
            Raises when text is inserted.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.Text">
            <summary>
            Gets or sets the text.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.OnMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled System.Windows.UIElement.MouseLeftButtonUp routed event
            reaches an element in its route that is derived from this class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.OnTextInput(System.Windows.Input.TextCompositionEventArgs)">
            <summary>
            Invoked when an unhandled System.Windows.Input.TextCompositionManager.TextInput
            attached event reaches an element in its route that is derived from this class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.OnTextInserted(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs)">
            <summary>
            Invoked when text is being inserted.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretTextBox.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called when the <see cref="E:System.Windows.UIElement.KeyDown" /> occurs.
            </summary>
            <param name="e">The event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs">
            <summary>
            Class TextInsertedEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
            <seealso cref="T:System.EventArgs" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs"/> class.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs.Text">
            <summary>
            Gets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup">
            <summary>
            Class CompletionListPopup.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup"/> class.
            </summary>
            <param name="syntaxEditor">The code editor.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.TextInserting">
            <summary>
            Occurs when text is being inserted from completion list to syntax editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.RepositionOnCaretPositionChanged">
            <summary>
            Gets a value indicating whether [reposition on caret position changed].
            </summary>
            <value><c>true</c> if [reposition on caret position changed]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.Presenter">
            <summary>
            Gets the presenter.
            </summary>
            <value>The presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.HasItems">
            <summary>
            Indicates whether the CompletionListPresenter has items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.SelectedCompletionItem">
            <summary>
            Gets the selected completion item.
            </summary>
            <value>The selected completion item.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.AttachToEditor">
            <summary>
            Attaches to editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.DetachFromEditor">
            <summary>
            Detaches from editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Invoked whenever an unhandled <see cref="E:System.Windows.UIElement.GotFocus" /> event reaches this element in its route.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.HandleSyntaxEditorKeyDown(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the code editor key down.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase">
            <summary>
            Base class for IntelliPrompt implementations..
            </summary>
            <seealso cref="T:System.Windows.Controls.Primitives.Popup" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase"/> class.
            </summary>
            <param name="syntaxEditor">The code editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.CloseOnLostFocus">
            <summary>
            Gets a value indicating whether [close on lost focus].
            </summary>
            <value><c>true</c> if [close on lost focus]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Editor">
            <summary>
            Gets the editor.
            </summary>
            <value>The editor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.EndPosition">
            <summary>
            Gets the end position.
            </summary>
            <value>The end position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.IsAutoClose">
            <summary>
            Gets or sets a value indicating whether this instance is automatic close.
            </summary>
            <value><c>true</c> if this instance is automatic close; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.IsVisible">
            <summary>
            Gets whether this instance is open.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.OpacityWhenCtrlPressed">
            <summary>
            Gets or sets the opacity when control pressed.
            </summary>
            <value>The opacity when control pressed.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.RepositionOnCaretPositionChanged">
            <summary>
            Gets a value indicating whether [reposition on caret position changed].
            </summary>
            <value><c>true</c> if [reposition on caret position changed]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.StartPosition">
            <summary>
            Gets the start position.
            </summary>
            <value>The start position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.HasItems">
            <summary>
            Indicates whether the child presenter of this instance has items.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.HasCalledClose">
            <summary>
            Gets a value indicating whether this instance has called close.
            </summary>
            <value><c>true</c> if this instance has called close; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.IsSelecting">
            <summary>
            Gets or sets a value indicating whether the control is currently performing selection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Close">
            <summary>
            Closes this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Show">
            <summary>
            Shows this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Show(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Shows the specified start position.
            </summary>
            <param name="caretStartPosition">The start position.</param>
            <param name="caretEndPositions">The end position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.ArrangeOverride(System.Windows.Size)">
            <summary>
            When overridden in a derived class, positions child elements and determines a size for a <see cref="T:System.Windows.FrameworkElement" /> derived class.
            </summary>
            <param name="finalSize">The final area within the parent that this element should use to arrange itself and its children.</param>
            <returns>The actual size used.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.AttachToEditor">
            <summary>
            Attaches to editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.AutoClose">
            <summary>
            Automatics the close.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.DetachFromEditor">
            <summary>
            Detaches from editor.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.HandleSyntaxEditorKeyDown(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the code editor key down.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.HandleSyntaxEditorKeyUp(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the code editor key up.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase.Reset">
            <summary>
            Resets this instance.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CollapseIfSingleItemConverter">
            <summary>
            Class CollapseIfSingleItemConverter.
            Implements the <see cref="T:System.Windows.Data.IValueConverter" />
            </summary>
            <seealso cref="T:System.Windows.Data.IValueConverter" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CollapseIfSingleItemConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns null, the valid null value is used.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CollapseIfSingleItemConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns null, the valid null value is used.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup">
            <summary>
            Class OverloadListPopup.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPromptBase" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup"/> class.
            </summary>
            <param name="syntaxEditor">The code editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.Presenter">
            <summary>
            Gets the presenter.
            </summary>
            <value>The presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.HasItems">
            <summary>
            Indicates whether the OverloadListPresenter has items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.HandleSyntaxEditorKeyDown(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the code editor key down.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.Reset">
            <summary>
            Resets this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPopup.AttachToEditor">
            <summary>
            Attaches to editor.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.Interaction">
            <summary>
            Class Interaction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.Interaction.BehaviorProperty">
            <summary>
            Registers the Behavior attached property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.Interaction.SetBehavior(Telerik.Windows.Controls.RadSyntaxEditor,Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior)">
            <summary>
            Sets the behavior.
            </summary>
            <param name="editor">The editor.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.Interaction.GetBehavior(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Gets the behavior.
            </summary>
            <param name="editor">The editor.</param>
            <returns>RadSyntaxEditorBehavior.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior">
            <summary>
            Class RadSyntaxEditorBehavior.
            Implements the <see cref="T:System.Windows.DependencyObject" />
            </summary>
            <seealso cref="T:System.Windows.DependencyObject" />
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.AssociatedEditorChanged">
            <summary>
            The associated editor changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.AssociatedEditor">
            <summary>
            Gets the associated editor.
            </summary>
            <value>The associated editor.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.Attach(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Attaches the specified editor.
            </summary>
            <param name="editor">The editor.</param>
            <exception cref="T:System.InvalidOperationException">Cannot host behavior multiple times.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.Detach">
            <summary>
            Detaches this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.OnAttached">
            <summary>
            Called when [attached].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Interactivity.RadSyntaxEditorBehavior.OnDetaching">
            <summary>
            Called when [detaching].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult">
            <summary>
            Class CompletionFilterResult.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult"/> class.
            </summary>
            <param name="exactMatch">The exact match.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo,Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult"/> class.
            </summary>
            <param name="exactMatch">The exact match.</param>
            <param name="closestMatch">The closest match.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult.ClosestMatch">
            <summary>
            Gets or sets the closest match.
            </summary>
            <value>The closest match.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionFilterResult.ExactMatch">
            <summary>
            Gets or sets the exact match.
            </summary>
            <value>The exact match.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo">
            <summary>
            Class CompletionInfo.
            Implements the <see cref="T:System.ComponentModel.INotifyPropertyChanged" />
            </summary>
            <seealso cref="T:System.ComponentModel.INotifyPropertyChanged" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo"/> class.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo"/> class.
            </summary>
            <param name="text">The text.</param>
            <param name="description">The description.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.#ctor(System.String,System.Windows.Media.ImageSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo"/> class.
            </summary>
            <param name="text">The text.</param>
            <param name="icon">The icon.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.#ctor(System.String,System.String,System.Windows.Media.ImageSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo"/> class.
            </summary>
            <param name="text">The text.</param>
            <param name="description">The description.</param>
            <param name="icon">The icon.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.#ctor(System.String,System.String,System.String,System.Windows.Media.ImageSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo"/> class.
            </summary>
            <param name="text">The text.</param>
            <param name="description">The description.</param>
            <param name="insertionText">The insertion text.</param>
            <param name="icon">The icon.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.Icon">
            <summary>
            Gets the icon.
            </summary>
            <value>The icon.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.InsertionText">
            <summary>
            Gets the insertion text.
            </summary>
            <value>The insertion text.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.IsClosestMatch">
            <summary>
            Gets or sets a value indicating whether this instance is closest match.
            </summary>
            <value><c>true</c> if this instance is closest match; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfo.Text">
            <summary>
            Gets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfoCollection">
            <summary>
            Class CompletionInfoCollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.CompletionInfoCollection.Filter(System.String)">
            <summary>
            Filters the specified filter.
            </summary>
            <param name="filterParameter">The filter.</param>
            <returns>CompletionFilterResult.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.ICompletionInfoCollection">
            <summary>
            Interface ICompletionInfoCollection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Completion.ICompletionInfoCollection.Filter(System.String)">
            <summary>
            Filters the specified filter.
            </summary>
            <param name="filterParameter">The filter.</param>
            <returns>CompletionFilterResult.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfoCollection">
            <summary>
            Class OverloadInfoCollection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo">
            <summary>
            Class OverloadInfo.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo"/> class.
            </summary>
            <param name="signature">The signature.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.#ctor(System.Windows.Documents.Span)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo"/> class.
            </summary>
            <param name="signature">The signature.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo"/> class.
            </summary>
            <param name="signature">The signature.</param>
            <param name="description">The description.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.#ctor(System.Windows.Documents.Span,System.Windows.Documents.Span)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo"/> class.
            </summary>
            <param name="signature">The signature.</param>
            <param name="description">The description.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.SignatureUIElement">
            <summary>
            Gets the signature UI element.
            TODO: Remove the need of this property.
            </summary>
            <value>The signature UI element.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.DescriptionUIElement">
            <summary>
            Gets the description UI element.
            TODO: Remove the need of this property.
            </summary>
            <value>The description UI element.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.Signature">
            <summary>
            Gets the signature.
            </summary>
            <value>The signature.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.Overloading.OverloadInfo.Description">
            <summary>
            Gets the description.
            </summary>
            <value>The description.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.SyntaxEditorIntelliPrompts">
            <summary>
            Represents IntelliPrompts information.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.SyntaxEditorIntelliPrompts.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.SyntaxEditorIntelliPrompts"/> class.
            </summary>
            <param name="associatedEditor">The associated editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.SyntaxEditorIntelliPrompts.CompletionListWindow">
            <summary>
            Gets or sets the completion list window.
            </summary>
            <value>The completion list window.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.IntelliPrompt.SyntaxEditorIntelliPrompts.OverloadListWindow">
            <summary>
            Gets or sets the overload list window.
            </summary>
            <value>The overload list window.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter">
            <summary>
            Represents UI control for completion list.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.CompletionListItemsProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.CompletionListItems"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.SelectedItemProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.SelectedItem"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter" /> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.SelectedItemPropertyChanged">
            <summary>
            Occurs when [selected item property changed].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.CompletionListItems">
            <summary>
            Gets or sets the completion list items.
            </summary>
            <value>The completion list items.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.IsMeasured">
            <summary>
            Gets a value indicating whether this instance is measured.
            </summary>
            <value><c>true</c> if this instance is measured; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.SelectedItem">
            <summary>
            Gets or sets the selected item.
            </summary>
            <value>The selected item.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.DecreaseSelectedIndex">
            <summary>
            Decreases the index of the selected.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.Filter(System.String)">
            <summary>
            Filters the specified filter.
            </summary>
            <param name="filterParameter">The filter.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.IncreaseSelectedIndex">
            <summary>
            Increases the index of the selected.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.UpdateToolTip(System.Boolean)">
            <summary>
            Updates the tool tip.
            </summary>
            <param name="tryOpenToolTip">The try open tool tip.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPresenter.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter">
            <summary>
            Class OverloadListPresenter represents UI presenter for overload lists.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OverloadListItemsProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OverloadListItems"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedIndexTextProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedIndexText"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedItemProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedItem"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedItemPropertyChanged">
            <summary>
            Occurs when [selected item property changed].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OverloadListItems">
            <summary>
            Gets or sets the overload list items.
            </summary>
            <value>The overload list items.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedIndexText">
            <summary>
            Gets the selected index text.
            </summary>
            <value>The selected index text.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedItem">
            <summary>
            Gets the selected item.
            </summary>
            <value>The selected item.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.SelectedIndex">
            <summary>
            Gets the index of the selected.
            </summary>
            <value>The index of the selected.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.DecreaseSelectedIndex">
            <summary>
            Decreases the index of the selected.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.IncreaseSelectedIndex">
            <summary>
            Increases the index of the selected.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.UIElement.MouseLeftButtonDown" /> routed event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the left mouse button was pressed.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.OverloadListPresenter.OnSelectedItemPropertyChanged">
            <summary>
            Called when [selected item property changed].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition">
            <summary>
            Represents a CaretPosition information.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.SyntaxEditor.Core.Text.TextDocument)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="document">The document.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.SyntaxEditor.Core.Text.TextDocument,Telerik.Windows.SyntaxEditor.Core.Text.PositionAnchoringType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="document">The document.</param>
            <param name="anchoringType">Type of the anchoring.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.SyntaxEditor.Core.Text.TextDocument,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="document">The document.</param>
            <param name="lineNumber">The line number.</param>
            <param name="columnNumber">The column number.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.SyntaxEditor.Core.Text.PositionAnchoringType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="position">The position.</param>
            <param name="anchoringType">Type of the anchoring.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.#ctor(Telerik.Windows.SyntaxEditor.Core.Text.TextDocument,System.Int32,System.Int32,Telerik.Windows.SyntaxEditor.Core.Text.PositionAnchoringType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition"/> class.
            </summary>
            <param name="document">The document.</param>
            <param name="lineNumber">The line number.</param>
            <param name="columnNumber">The column number.</param>
            <param name="anchoringType">Type of the anchoring.</param>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.PositionChanged">
            <summary>
            PositionChanged event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.PositionChanging">
            <summary>
            PositionChanging event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.AnchoringType">
            <summary>
            Gets the type of the anchoring.
            </summary>
            <value>The type of the anchoring.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.ColumnNumber">
            <summary>
            Gets the column number.
            </summary>
            <value>The column number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.DisplayColumnNumber">
            <summary>
            Gets the display column number.
            </summary>
            <value>The display column number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.DisplayLineNumber">
            <summary>
            Gets the display line number.
            </summary>
            <value>The display line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Index">
            <summary>
            Gets the index.
            </summary>
            <value>The index.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.IsAnchored">
            <summary>
            Gets a value indicating whether this instance is anchored.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.LastTrackedColumn">
            <summary>
            Gets or sets the last tracked column.
            NOTE: Used for up/down in non-wrapped scenarios.
            </summary>
            <value>The last tracked column.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.LineNumber">
            <summary>
            Gets the line number.
            </summary>
            <value>The line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.TabSize">
            <summary>
            Gets or sets the size of the tab.
            </summary>
            <value>The size of the tab.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.ConvertFromIndex(Telerik.Windows.SyntaxEditor.Core.Text.TextDocument,System.Int32)">
            <summary>
            Converts from index.
            </summary>
            <param name="document">The document.</param>
            <param name="index">The index.</param>
            <returns>CaretPosition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.ConvertToIndex(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Converts to index.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_Inequality(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the != operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_LessThan(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the &lt; operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_LessThanOrEqual(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the &lt;= operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_Equality(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the == operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_GreaterThan(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the &gt; operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.op_GreaterThanOrEqual(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Implements the &gt;= operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.CompareTo(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Compares the current object with another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>A value that indicates the relative order of the objects being compared. The return value has the following meanings: Value Meaning Less than zero This object is less than the <paramref name="other" /> parameter.Zero This object is equal to <paramref name="other" />. Greater than zero This object is greater than <paramref name="other" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to this instance.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Equals(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.IsAtWordStart">
            <summary>
            Determines whether [is at word start].
            </summary>
            <returns><c>true</c> if [is at word start]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveLineDown">
            <summary>
            Moves the line down.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveLineUp">
            <summary>
            Moves the line up.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToCurrentWordEnd">
            <summary>
            Moves to current word end.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToCurrentWordStart">
            <summary>
            Moves to current word start.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToEndOfDocument">
            <summary>
            Moves to end of document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToHome">
            <summary>
            Moves to home.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToIndex(System.Int32)">
            <summary>
            Moves to index.
            </summary>
            <param name="indexParameter">The index.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToLine(System.Int32)">
            <summary>
            Moves to line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToLineEnd">
            <summary>
            Moves to line end.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToLineStart">
            <summary>
            Moves to line start.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToNextCharacter">
            <summary>
            Moves to next character.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToNextWord">
            <summary>
            Moves to next word.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Moves to position.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToPreviousCharacter">
            <summary>
            Moves to previous character.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToPreviousWord">
            <summary>
            Moves to previous word.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.MoveToStartOfDocument">
            <summary>
            Moves to start of document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>A <see cref="T:System.String" /> that represents this instance.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.OnPositionChanged">
            <summary>
            Called when [position changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition.OnPositionChanging">
            <summary>
            Called when [position changing].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker">
            <summary>
            Represents a DropPositionMarker control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker.IsDropAllowed">
            <summary>
            Sets a value indicating whether dropping is allowed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.DropPositionMarker.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter">
            <summary>
            SyntaxEditorPresenter behavior definition.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.ViewportChanged">
            <summary>
            Occurs when [viewport changed].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.Editor">
            <summary>
            Gets the editor.
            </summary>
            <value>The editor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.HorizontalScrollOffset">
            <summary>
            Gets the horizontal scroll offset.
            </summary>
            <value>The horizontal scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.VerticalScrollOffset">
            <summary>
            Gets the vertical scroll offset.
            </summary>
            <value>The vertical scroll offset.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.Viewport">
            <summary>
            Gets the viewport.
            </summary>
            <value>The viewport.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.GetDocumentPointFromPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Gets the document point from position.
            </summary>
            <param name="position">The position.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.GetDocumentPointFromViewPoint(System.Windows.Point)">
            <summary>
            Gets the document point from view point.
            </summary>
            <param name="point">The point.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.GetPositionFromViewPoint(System.Windows.Point)">
            <summary>
            Gets the position from view point.
            </summary>
            <param name="point">The point.</param>
            <returns>CaretPosition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.GetViewPointFromDocumentPoint(System.Windows.Point)">
            <summary>
            Gets the view point from document point.
            </summary>
            <param name="point">The point.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.GetViewPointFromPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Gets the view point from position.
            </summary>
            <param name="position">The position.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.HideDropMarker">
            <summary>
            Hides the drop marker.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.MoveCaretToPositionInView(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,System.Windows.Point,System.Boolean)">
            <summary>
            Moves the caret to position in view.
            </summary>
            <param name="caretPosition">The caret position.</param>
            <param name="positionInView">The position in view.</param>
            <param name="moveToNextIfOutOfBox">If set to <c>true</c> [move to next if out of box].</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.MoveDropMarker(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Moves the drop marker.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.ISyntaxEditorPresenter.ShowDropMarker(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Shows the drop marker.
            </summary>
            <param name="position">The position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.KeyboardSelectionHandler.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer">
            <summary>
            Class FoldingUILayer.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.FoldingUILayer.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI override.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer">
            <summary>
            Class PooledUILayer.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.AddElementToPool(System.Object)">
            <summary>
            Adds the element to pool.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.CanRecycle(System.Object)">
            <summary>
            Determines whether this instance can recycle the specified element.
            </summary>
            <param name="element">The element.</param>
            <returns><c>true</c> if this instance can recycle the specified element; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.GetElementFromPool``1">
            <summary>
            Gets the element from pool.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1">
            <summary>
            Class TagBasedUILayer.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer" />
            </summary>
            <typeparam name="TTag">The type of the t tag.</typeparam>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PooledUILayer" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1.Tagger">
            <summary>
            Gets the tagger.
            </summary>
            <value>The tagger.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1.OnTextFormatDefinitionsRegistryChanged">
            <summary>
            Called when [text format definitions registry changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI override.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext,Telerik.Windows.SyntaxEditor.Core.Text.TextSnapshotSpan)">
            <summary>
            Updates the UI override.
            </summary>
            <param name="updateContext">The update context.</param>
            <param name="changedSnapshotSpan">The changed snapshot span.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers">
            <summary>
            Class PredefinedUILayers.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.Text">
            <summary>
            Text layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.Selection">
            <summary>
            Selection layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.Folding">
            <summary>
            Folding layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.TextUnderline">
            <summary>
            TextUnderline layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.TextHighlight">
            <summary>
            TextHighlight layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.TextBorder">
            <summary>
            TextBorder layer name.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.PredefinedUILayers.TextToolTip">
            <summary>
            TextToolTip layer name.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextBorderUILayer">
            <summary>
            Class TextBorderUILayer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextBorderUILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextBorderUILayer.GetLinePartUIElement(Telerik.Windows.SyntaxEditor.Core.Tagging.TextBorderTag,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
            <param name="tag">The tag.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
            <returns>System.Windows.FrameworkElement.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextBorderUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1">
            <summary>
            Class LineBasedUILayer represents line based layer abstraction.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1" />
            </summary>
            <typeparam name="TTag">The type of the t tag.</typeparam>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TagBasedUILayer`1" />
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.ElementToSnapshotSpanCache">
            <summary>
            The UI element to snapshot span cache.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.NormalizeByTextFormatDefinitionPriority(Telerik.Windows.SyntaxEditor.Core.Text.Span,System.Collections.Generic.IEnumerable{Telerik.Windows.SyntaxEditor.Core.Tagging.TagSpan{`0}},Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Normalizes the by text format definition priority.
            </summary>
            <param name="currentLineSpan">The current line span.</param>
            <param name="tags">The tags.</param>
            <param name="updateContext">The update context.</param>
            <returns>IEnumerable&lt;TagSpan&lt;TTag&gt;&gt;.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.ArrangeLinePartUIElement(System.Windows.FrameworkElement,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Arranges the line part UI element.
            </summary>
            <param name="uiElement">The UI element.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.GetLinePartUIElement(`0,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
            <param name="tag">The tag.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
            <returns>System.Windows.FrameworkElement.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.GetLineUIElements(Telerik.Windows.SyntaxEditor.Core.Text.Span,System.Collections.Generic.IEnumerable{Telerik.Windows.SyntaxEditor.Core.Tagging.TagSpan{`0}},Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line UI elements.
            </summary>
            <param name="currentLineSpan">The current line span.</param>
            <param name="tags">The tags.</param>
            <param name="updateContext">The update context.</param>
            <returns>IEnumerable&lt;FrameworkElement&gt;.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.OnTextFormatDefinitionsRegistryChanged">
            <summary>
            Called when [text format definitions registry changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.LineBasedUILayer`1.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext,Telerik.Windows.SyntaxEditor.Core.Text.TextSnapshotSpan)">
            <summary>
            Updates the UI override.
            </summary>
            <param name="updateContext">The update context.</param>
            <param name="changedSnapshotSpan">The changed snapshot span.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer">
            <summary>
            Class TextToolTipUILayer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer.GetLinePartUIElement(Telerik.Windows.SyntaxEditor.Core.Tagging.ToolTipTag,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
            <param name="tag">The tag.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
            <returns>FrameworkElement.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextToolTipUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayersBuilder">
            <summary>
            Class UILayersBuilder.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayersBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayersBuilder"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayersBuilder.BuildUILayers(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack)">
            <summary>
            Builds the UI layers.
            </summary>
            <param name="uiLayers">The UI layers.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration">
            <summary>
            UnderlineDecoration behavior.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration.CreateUnderline(System.Windows.Rect,System.Windows.Media.Brush)">
            <summary>
            Creates the underline.
            </summary>
            <param name="rect">The rectangle.</param>
            <param name="brush">The brush.</param>
            <returns>FrameworkElement.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.LineUnderlineDecoration">
            <summary>
            Class LineUnderlineDecoration.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration" />
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.LineUnderlineDecoration.StrokeDashArray">
            <summary>
            Gets the stroke dash array.
            </summary>
            <value>The stroke dash array.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.LineUnderlineDecoration.CreateUnderline(System.Windows.Rect,System.Windows.Media.Brush)">
            <summary>
            Creates the underline.
            </summary>
            <param name="rect">The rectangle.</param>
            <param name="brush">The brush.</param>
            <returns>FrameworkElement.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.NoneUnderlineDecoration">
            <summary>
            Class NoneUnderlineDecoration representing empty underline.
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.NoneUnderlineDecoration.CreateUnderline(System.Windows.Rect,System.Windows.Media.Brush)">
            <summary>
            Creates the underline.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations">
            <summary>
            Class UnderlineDecorations.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.None">
            <summary>
            Empty underline decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.Line">
            <summary>
            Line decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.Wave">
            <summary>
            Wave decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.DotLine">
            <summary>
            DotLine decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.DashLine">
            <summary>
            DashLine decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.DotDashLine">
            <summary>
            DotDashLine decoration.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.UnderlineDecorations.DotDotDashLine">
            <summary>
            DotDotDashLine decoration.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.WaveUnderlineDecoration">
            <summary>
            Class WaveUnderlineDecoration.
            Implements the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration" />
            </summary>
            <seealso cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.WaveUnderlineDecoration.CreateUnderline(System.Windows.Rect,System.Windows.Media.Brush)">
            <summary>
            Creates the underline.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextHighlightUILayer">
            <summary>
            Class TextHighlightUILayer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextHighlightUILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextHighlightUILayer.GetLinePartUIElement(Telerik.Windows.SyntaxEditor.Core.Tagging.TextHighlightTag,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
            <param name="tag">The tag.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
            <returns>FrameworkElement.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextHighlightUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer">
            <summary>
            Text UI layer class serving for formatting <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationTag"/> text matches.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer.Name">
            <summary>
            Gets the name of the layer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext,Telerik.Windows.SyntaxEditor.Core.Text.TextSnapshotSpan)">
            <summary>
            Updates the UI override.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer.GetLineUIElements(Telerik.Windows.SyntaxEditor.Core.Text.Span,System.Collections.Generic.IEnumerable{Telerik.Windows.SyntaxEditor.Core.Tagging.TagSpan{Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationTag}},Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line UI elements.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer.GetLinePartUIElement(Telerik.Windows.SyntaxEditor.Core.Tagging.ClassificationTag,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer">
            <summary>
            Class TextUnderlineUILayer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer.GetLinePartUIElement(Telerik.Windows.SyntaxEditor.Core.Tagging.UnderlineTag,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Gets the line part UI element.
            </summary>
            <param name="tag">The tag.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
            <returns>System.Windows.FrameworkElement.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer.ArrangeLinePartUIElement(System.Windows.FrameworkElement,Telerik.Windows.SyntaxEditor.Core.Text.Span,Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Arranges the line part UI element.
            </summary>
            <param name="uiElement">The UI element.</param>
            <param name="span">The span.</param>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer.CanRecycle(System.Object)">
            <summary>
            Determines whether this instance can recycle the specified element.
            </summary>
            <param name="element">The element.</param>
            <returns><c>true</c> if this instance can recycle the specified element; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.TextUnderlineUILayer.ResetPooledElementProperties(System.Object)">
            <summary>
            Resets the pooled element properties.
            </summary>
            <param name="element">The element.</param>
            <exception cref="T:System.InvalidOperationException">Underlines cannot be recycled.</exception>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer">
            <summary>
            UI Layer abstraction.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.Container">
            <summary>
            Gets the container.
            </summary>
            <value>The container.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.AssociatedEditor">
            <summary>
            Gets the associated editor.
            </summary>
            <value>The associated editor.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.UpdateUI">
            <summary>
            Updates the UI.
            </summary>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.UpdateUI(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.OnAssociatedEditorChanged(Telerik.Windows.Controls.RadSyntaxEditor,Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Called when [associated editor changed].
            </summary>
            <param name="oldEditor">The old editor.</param>
            <param name="newEditor">The new editor.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.TranslateAndScale(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Translates the and scale.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer.UpdateUIOverride(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI override.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack">
            <summary>
            Represents the stack of UI layers.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <value>The count.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.
            </summary>
            <value><c>true</c> if this instance is read only; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.System#Collections#Generic#ICollection{Telerik#Windows#Controls#SyntaxEditor#UI#Layers#UILayer}#Add(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Adds the specified layer.
            </summary>
            <param name="layer">The layer.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.AddAfter(System.String,Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Adds the after.
            </summary>
            <param name="presentedLayerName">Name of the presented layer.</param>
            <param name="layer">The layer.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.AddBefore(System.String,Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Adds the before.
            </summary>
            <param name="presentedLayerName">Name of the presented layer.</param>
            <param name="layer">The layer.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.AddFirst(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Adds the first.
            </summary>
            <param name="layer">The layer.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.AddLast(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Adds the last.
            </summary>
            <param name="layer">The layer.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.ClearLayersChildren">
            <summary>
            Clears the layers children.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Contains(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Determines whether this instance contains the object.
            </summary>
            <param name="item">The layer.</param>
            <returns><c>true</c> if [contains] [the specified layer]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Contains(System.String)">
            <summary>
            Determines whether this instance contains the object.
            </summary>
            <param name="layerName">Name of the layer.</param>
            <returns><c>true</c> if [contains] [the specified layer name]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.CopyTo(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.GetLayerByName(System.String)">
            <summary>
            Gets the name of the layer by.
            </summary>
            <param name="layerName">Name of the layer.</param>
            <returns>UILayer.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Remove(Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayer)">
            <summary>
            Removes the specified layer.
            </summary>
            <param name="item">The layer.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.Remove(System.String)">
            <summary>
            Removes the specified layer name.
            </summary>
            <param name="layerName">Name of the layer.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.UpdateUI">
            <summary>
            Updates the UI.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Layers.UILayerStack.UpdateUI(Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext)">
            <summary>
            Updates the UI.
            </summary>
            <param name="updateContext">The update context.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs">
            <summary>
            Class PreviewSyntaxEditorInputEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
            <seealso cref="T:System.EventArgs" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs"/> class.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs.Text">
            <summary>
            Gets or sets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs.Handled">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs"/> is handled.
            </summary>
            <value><c>true</c> if handled; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchSelectionMarker">
            <summary>
            Represents a TouchSelectionMarker control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchSelectionMarker.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchSelectionMarker"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchSelectionMarker.SetPosition(System.Windows.Point)">
            <summary>
            Sets the position of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchSelectionMarker"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl">
            <summary>
            Represents a TouchCopyControl control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.CopyCommandProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.CopyCommand"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.CopyCommand">
            <summary>
            Gets or sets the copy command.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.Hide">
            <summary>
            Hides the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.SetPosition(System.Windows.Point)">
            <summary>
            Sets the position of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl.Show">
            <summary>
            Shows the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TouchCopyControl"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UIChangesInfo.#ctor(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.UIChangesInfo"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext">
            <summary>
            Class UIUpdateContext.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.Editor">
            <summary>
            Gets the editor.
            </summary>
            <value>The editor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.FirstVisibleColumnNumber">
            <summary>
            Gets the first visible column number.
            </summary>
            <value>The first visible column number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.FirstVisibleLineNumber">
            <summary>
            Gets the first visible line number.
            </summary>
            <value>The first visible line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.IsValid">
            <summary>
            Returns true if ... is valid.
            </summary>
            <value><c>true</c> if this instance is valid; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.LastVisibleColumnNumber">
            <summary>
            Gets the last visible column number.
            </summary>
            <value>The last visible column number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.LastVisibleLineNumber">
            <summary>
            Gets the last visible line number.
            </summary>
            <value>The last visible line number.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.Viewport">
            <summary>
            Gets the viewport.
            </summary>
            <value>The viewport.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UIUpdateContext.VisibleLineNumbers">
            <summary>
            Collection of numbers of the visible lines.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.MouseSelectionHandler.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.MouseSelectionHandler.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.MouseSelectionHandler.CheckMultipleClick">
            <summary>
            Returns true if multiple click.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Pen">
            <summary>
            Represents a pen information.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.BrushProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.Brush"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.ThicknessProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.Thickness"/> property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Pen"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.#ctor(System.Windows.Media.Brush,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Pen"/> class.
            </summary>
            <param name="brush">The brush.</param>
            <param name="uniformLength">Length of the uniform.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.#ctor(System.Windows.Media.Brush,System.Windows.Thickness)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Pen"/> class.
            </summary>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.Brush">
            <summary>
            Gets or sets the brush.
            </summary>
            <value>The brush.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Pen.Thickness">
            <summary>
            Gets or sets the thickness.
            </summary>
            <value>The thickness.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs">
            <summary>
            PreviewSyntaxEditorKeyEventArgs class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs.#ctor(System.Windows.Input.KeyEventArgs)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs"/> class.
            </summary>
            <param name="originalArgs"></param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs.Key">
            <summary>
            Gets the key of the original args.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs.OriginalArgs">
            <summary>
            Gets the original args.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs">
            <summary>
            Class PreviewSyntaxEditorMouseButtonEventArgs.
            Implements the <see cref="T:System.EventArgs" />
            </summary>
            <seealso cref="T:System.EventArgs" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs.#ctor(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs"/> class.
            </summary>
            <param name="originalArgs">The <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> Instance containing the event data.</param>
            <exception cref="T:System.ArgumentNullException">OriginalArgs.</exception>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs.OriginalArgs">
            <summary>
            Gets the original arguments.
            </summary>
            <value>The original arguments.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState">
            <summary>
            Class SelectionState.
            Implements the <see cref="T:System.IDisposable" />
            </summary>
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.Selection,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.EndPosition">
            <summary>
            Gets the end position.
            </summary>
            <value>The end position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.Mode">
            <summary>
            Gets the mode.
            </summary>
            <value>The mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.StartPosition">
            <summary>
            Gets the start position.
            </summary>
            <value>The start position.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition">
            <summary>
            Represents formatting of a text portion.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(System.Windows.Media.Brush)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(System.Windows.Media.Brush,System.Windows.Media.Brush)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(System.Windows.Media.Brush,System.Windows.FontWeight,System.Windows.FontStyle)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(Telerik.Windows.Controls.SyntaxEditor.UI.Pen)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(System.Windows.Media.Brush,System.Windows.Media.Brush,Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo,Telerik.Windows.Controls.SyntaxEditor.UI.Pen)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.#ctor(System.Windows.Media.Brush,System.Windows.Media.Brush,Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo,Telerik.Windows.Controls.SyntaxEditor.UI.Pen,System.Windows.FontWeight,System.Windows.FontStyle)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Background">
            <summary>
            Gets the background of the text format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Border">
            <summary>
            Gets the border of the text format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Foreground">
            <summary>
            Gets the foreground of the text format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Underline">
            <summary>
            Gets the underline of the text format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.FontWeight">
            <summary>
            Gets the font weight of the text format definition.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.FontStyle">
            <summary>
            Gets the font style of the text format definition.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.op_Inequality(Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>Implements the != operator.</summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.op_Equality(Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>Implements the == operator.</summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Equals(System.Object)">
            <summary>
            Determines whether two object instances are equal.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.Equals(Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>Determines whether this instance is equal to other  <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition"/> instance.</summary>
            <param name="other"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition.GetHashCode">
            <summary>
            Gets the hash code of this instance.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack">
            <summary>
            Represents a collection of <see cref="T:Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.#ctor(Telerik.Windows.Controls.SyntaxEditor.Palettes.SyntaxEditorPalette)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Changed">
            <summary>
            Occurs when [changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddAfter(System.String,System.String,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the after.
            </summary>
            <param name="presentedFormatDefinitionName">Name of the presented format definition.</param>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <param name="formatDefinition">The format definition.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddAfter(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the after.
            </summary>
            <param name="presentedFormatDefinitionKey">The presented format definition key.</param>
            <param name="formatDefinitionKey">The format definition key.</param>
            <param name="formatDefinition">The format definition.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddBefore(System.String,System.String,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the before.
            </summary>
            <param name="presentedFormatDefinitionName">Name of the presented format definition.</param>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <param name="formatDefinition">The format definition.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddBefore(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the before.
            </summary>
            <param name="presentedFormatDefinitionKey">The presented format definition key.</param>
            <param name="formatDefinitionKey">The format definition key.</param>
            <param name="formatDefinition">The format definition.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddFirst(System.String,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the first.
            </summary>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <param name="formatDefinition">The format definition.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddFirst(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the first.
            </summary>
            <param name="formatDefinitionKey">The format definition key.</param>
            <param name="formatDefinition">The format definition.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddLast(System.String,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the last.
            </summary>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <param name="formatDefinition">The format definition.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.AddLast(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey,Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinition)">
            <summary>
            Adds the last.
            </summary>
            <param name="formatDefinitionKey">The format definition key.</param>
            <param name="formatDefinition">The format definition.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Contains(System.String)">
            <summary>
            Determines whether this instance contains the object.
            </summary>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <returns><c>true</c> if [contains] [the specified format definition name]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Contains(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Determines whether this instance contains the object.
            </summary>
            <param name="formatDefinitionKey">The format definition key.</param>
            <returns><c>true</c> if [contains] [the specified format definition key]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.GetTextFormatDefinition(System.String)">
            <summary>
            Gets the text format definition.
            </summary>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <returns>TextFormatDefinition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.GetTextFormatDefinition(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Gets the text format definition.
            </summary>
            <param name="formatDefinitionKey">The format definition key.</param>
            <returns>TextFormatDefinition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Remove(System.String)">
            <summary>
            Removes the specified format definition name.
            </summary>
            <param name="formatDefinitionName">Name of the format definition.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.Remove(Telerik.Windows.SyntaxEditor.Core.Tagging.ITextFormatDefinitionKey)">
            <summary>
            Removes the specified format definition key.
            </summary>
            <param name="formatDefinitionKey">The format definition key.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.TextFormatDefinitionStack.OnChanged">
            <summary>
            Called when [changed].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo">
            <summary>
            Class Underline.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.None">
            <summary>
            Empty Underline instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.#ctor(System.Windows.Media.Brush,Telerik.Windows.Controls.SyntaxEditor.UI.Layers.Underline.IUnderlineDecoration)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo"/> class.
            </summary>
            <param name="brush">The brush.</param>
            <param name="decoration">The decoration.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.Brush">
            <summary>
            Gets the brush.
            </summary>
            <value>The brush.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.Decoration">
            <summary>
            Gets the decoration.
            </summary>
            <value>The decoration.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.op_Inequality(Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo,Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo)">
            <summary>
            Implements the != operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.op_Equality(Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo,Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo)">
            <summary>
            Implements the == operator.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>The result of the operator.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.CreateUnderline(System.Windows.Rect)">
            <summary>
            Creates the underline.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to this instance.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.UnderlineInfo.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextSelectionMode">
            <summary>
            Enum TextSelectionMode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.TextSelectionMode.Simple">
            <summary>
            Simple text selection mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.TextSelectionMode.Rectangle">
            <summary>
            Rectangle text selection mode.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Selection">
            <summary>
            Represents a selection information.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SelectionChanged">
            <summary>
            Occurs when [selection changed].
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SelectionChanging">
            <summary>
            Occurs when [selection changing].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.ActivePosition">
            <summary>
            Gets the active position.
            </summary>
            <value>The active position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.AnchoredPosition">
            <summary>
            Gets the anchored position.
            </summary>
            <value>The anchored position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Document">
            <summary>
            Gets the document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.EndPosition">
            <summary>
            Gets the end position.
            </summary>
            <value>The end position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.IsEmpty">
            <summary>
            Gets a value indicating whether this instance is empty.
            </summary>
            <value><c>true</c> if this instance is empty; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.IsReversed">
            <summary>
            Gets a value indicating whether this instance is reversed.
            </summary>
            <value><c>true</c> if this instance is reversed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Mode">
            <summary>
            Gets or sets the mode.
            </summary>
            <value>The mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SelectedSpans">
            <summary>
            Gets the selected spans.
            </summary>
            <value>The selected spans.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.StartPosition">
            <summary>
            Gets the start position.
            </summary>
            <value>The start position.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.ContainsPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,System.Boolean)">
            <summary>
            Determines whether the specified position contains position.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.CreateSelectionState(System.Boolean)">
            <summary>
            Creates the state of the selection.
            </summary>
            <param name="anchorPositions">If set to <c>true</c> [anchor positions].</param>
            <returns>SelectionState.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.GetSelectedText">
            <summary>
            Gets the selected text.
            </summary>
            <returns>System.String.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.RestoreSelectionState(Telerik.Windows.Controls.SyntaxEditor.UI.SelectionState)">
            <summary>
            Restores the state of the selection.
            </summary>
            <param name="selectionState">State of the selection.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Select(Telerik.Windows.SyntaxEditor.Core.Text.Span)">
            <summary>
            Selects the specified span.
            </summary>
            <param name="span">The span.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Select(Telerik.Windows.SyntaxEditor.Core.Text.Span,System.Boolean)">
            <summary>
            Selects the specified span.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Select(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition,Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Selects the specified start position.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SelectAll">
            <summary>
            Selects all.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SetSelectionEnd(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Sets the selection end.
            </summary>
            <param name="endPosition">The end position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.SetSelectionStart(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Sets the selection start.
            </summary>
            <param name="startPosition">The start position.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>A <see cref="T:System.String" /> that represents this instance.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.OnSelectionChanged">
            <summary>
            Called when [selection changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Selection.OnSelectionChanging">
            <summary>
            Called when [selection changing].
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.CaretDisplayMode">
            <summary>
            Caret display mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CaretDisplayMode.Normal">
            <summary>
            Normal display mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CaretDisplayMode.Block">
            <summary>
            Block display mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CaretDisplayMode.HalfBlock">
            <summary>
            Half block display mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.CaretDisplayMode.QuarterBlock">
            <summary>
            Quarter block display mode.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.Caret">
            <summary>
            Represents a caret for code editor.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.CaretBrushProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.CaretBrush"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Caret"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.TextInserted">
            <summary>
            Occurs when text is inserted.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.UpdateExecuted">
            <summary>
            Occurs when [update executed].
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.CaretBrush">
            <summary>
            Gets or sets the brush used for the carets.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.DisplayMode">
            <summary>
            Caret display mode.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.ShouldInvertForeColor">
            <summary>
            Gets or sets a value indicating whether [should invert fore color].
            </summary>
            <value><c>true</c> if [should invert fore color]; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.Focus">
            <summary>
            Tries to focus the caret.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.Hide">
            <summary>
            Hides the caret.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.SetFontSize(System.Double)">
            <summary>
            Sets the font size of the caret.
            </summary>
            <param name="fontSize"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.Show">
            <summary>
            Shows the caret.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.UpdateUI">
            <summary>
            Updates the UI.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Invoked whenever an unhandled <see cref="E:System.Windows.UIElement.GotFocus" /> event reaches this element in its route.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.Caret.OnTextInserted(System.Object,Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs)">
            <summary>
            Handles the <see cref="E:TextInserted" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.Input.TextInsertedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState">
            <summary>
            Class SyntaxEditorHistoryState.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.#ctor(Telerik.Windows.Controls.RadSyntaxEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState"/> class.
            </summary>
            <param name="editor">The editor.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.CaretPositionIndex">
            <summary>
            Gets the index of the caret position.
            </summary>
            <value>The index of the caret position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.SelectionStartIndex">
            <summary>
            Gets the start index of the selection.
            </summary>
            <value>The start index of the selection.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.SelectionEndIndex">
            <summary>
            Gets the end index of the selection.
            </summary>
            <value>The end index of the selection.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.IsSelectionReversed">
            <summary>
            Gets a value indicating whether this instance is selection reversed.
            </summary>
            <value><c>true</c> if this instance is selection reversed; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.SelectionMode">
            <summary>
            Gets the selection mode.
            </summary>
            <value>The selection mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState.IsLayoutInvalidationSuspended">
            <summary>
            Gets a value indicating whether this instance is layout invalidation suspended.
            </summary>
            <value><c>true</c> if this instance is layout invalidation suspended; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs">
            <summary>
            Event args used in the <seealso cref="E:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup.TextInserting"/> event.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs.Cancel">
            <summary>
            Gets or sets a value indication whether the text insertion should be cancelled.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs.SpanToReplace">
            <summary>
            Gets or sets the span which will be replaced with the inserted text.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.SyntaxEditor.CompletionListTextInsertingEventArgs.TextToInsert">
            <summary>
            Gets or sets the text that is selected from the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.CompletionListPopup"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Measurement.RadTextMeasurer.ContainsIMESymbols(Telerik.Windows.SyntaxEditor.Core.Text.DataStructures.IRope)">
            <summary>
            Avoid calling this method in measure phase as it might hurt performance with long text.
            Users should use <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.UseMonospacedFontOptimization" /> set to false when 
            <see cref="F:System.Globalization.UnicodeCategory.OtherLetter" /> chars are used in the document. Such chars are typically used in Japanese, Chinese, Korean etc.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper">
            <summary>
            Class KeyboardHelper.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsModifierPressed(System.Windows.Input.ModifierKeys,System.Windows.Input.ModifierKeys)">
            <summary>
            Determines whether [is modifier pressed] [the specified modifiers].
            </summary>
            <param name="modifiers">The modifiers.</param>
            <param name="modifier">The modifier.</param>
            <returns><c>true</c> if [is modifier pressed] [the specified modifiers]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsShiftPressed(System.Windows.Input.ModifierKeys)">
            <summary>
            Determines whether [is shift pressed] [the specified modifiers].
            </summary>
            <param name="modifiers">The modifiers.</param>
            <returns><c>true</c> if [is shift pressed] [the specified modifiers]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsModifierPressed(System.Windows.Input.ModifierKeys)">
            <summary>
            Determines whether [is modifier pressed] [the specified modifier].
            </summary>
            <param name="modifier">The modifier.</param>
            <returns><c>true</c> if [is modifier pressed] [the specified modifier]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsAltKey(System.Windows.Input.Key)">
            <summary>
            Determines whether [is alt key] [the specified key].
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if [is alt key] [the specified key]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsCtrlKey(System.Windows.Input.Key)">
            <summary>
            Determines whether [is control key] [the specified key].
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if [is control key] [the specified key]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsShiftPressed">
            <summary>
            Determines whether [is shift pressed].
            </summary>
            <returns><c>true</c> if [is shift pressed]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsAltPressed">
            <summary>
            Determines whether [is alt pressed].
            </summary>
            <returns><c>true</c> if [is alt pressed]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.SyntaxEditor.Utilities.KeyboardHelper.IsCtrlPressed">
            <summary>
            Determines whether [is control or mac pressed].
            </summary>
            <returns><c>true</c> if [is control or mac pressed]; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.RadSyntaxEditor">
            <summary>
            Class RadSyntaxEditor.
            Implements the <see cref="T:Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor" /></summary>
            <seealso cref="T:Telerik.Windows.SyntaxEditor.Core.Editor.ITextDocumentEditor" />
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.SearchPanelOpened">
            <summary>
            Occurs when the search panel is opened.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.SearchPanelClosed">
            <summary>
            Occurs when the search panel is closed.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Find(System.String,System.Int32)">
            <summary>
            Finds the specified search text.
            </summary>
            <param name="searchText">The search text.</param>
            <param name="startIndex">The start index.</param>
            <returns>System.Nullable&lt;Span&gt;.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Find(System.String,System.Int32,System.Boolean)">
            <summary>
            Finds the specified search text.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="startIndex">The start index to search in the document.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <returns>Nullable Span which, when not null, indicates the found text.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Find(System.String,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Finds the specified search text.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="startIndex">The start index to search in the document.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <returns>Nullable Span which, when not null, indicates the found text.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Find(System.String,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Finds the specified search text.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="startIndex">The start index to search in the document.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <param name="matchWord">If set to <c>true</c>, indicates that the search finds whole words only.</param>
            <returns>Nullable Span which, when not null, indicates the found text.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindPrevious(System.String,System.Int32,System.Boolean)">
            <summary>
            Finds the specified search text before the current index.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="startIndex">The start index to search in the document.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <returns>Nullable Span which, when not null, indicates the found text.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindPrevious(System.String,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Finds the specified search text before the current index.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="startIndex">The start index to search in the document.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <param name="matchWord">If set to <c>true</c>, indicates that the search finds whole words only.</param>
            <returns>Nullable Span which, when not null, indicates the found text.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindAll(System.String)">
            <summary>
            Finds all spans with the given searchText.
            </summary>
            <param name="searchText">The search text.</param>
            <returns>List of all matched spans.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindAll(System.String,System.Boolean)">
            <summary>
            Finds all spans with the given searchText or regex pattern.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <returns>List of all matched spans.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindAll(System.String,System.Boolean,System.Boolean)">
            <summary>
            Finds all spans with the given searchText or regex pattern.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <returns>List of all matched spans.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.FindAll(System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Finds all spans with the given searchText or regex pattern.
            </summary>
            <param name="searchText">The search text or regex pattern.</param>
            <param name="matchCase">Indicates whether the search is case-sensitive.</param>
            <param name="useRegularExpression">If set to <c>true</c> indicates that the search text will be used as regular expression search pattern.</param>
            <param name="matchWord">If set to <c>true</c>, indicates that the search finds whole words only.</param>
            <returns>List of all matched spans.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.NavigateNextMatch(System.String)">
            <summary>
            Navigates to the next matched text in the editor.
            </summary>
            <param name="searchText">The search text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.NavigatePreviousMatch(System.String)">
            <summary>
            Navigates to the next matched text in the editor.
            </summary>
            <param name="searchText">The search text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.HighlightAllMatches(System.String)">
            <summary>
            Tries to highlight all span matches via all registered <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Tagging.Taggers.TextSearchHighlightTagger"/>s.
            </summary>
            <param name="searchText"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OpenFindDialog(System.String)">
            <summary>
            Opens the find dialog with the specified searchText in the search textbox.
            </summary>
            <param name="searchText">The search text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.CloseFindDialog">
            <summary>
            Closes the find dialog.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnSearchPanelOpened">
            <summary>
            Called when search panel is opened.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnSearchPanelClosed">
            <summary>
            Called when search panel is closed.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.AllowScalingProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.AllowScaling"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.HorizontalScrollBarVisibilityProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.HorizontalScrollBarVisibility"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.IsCaretVisibleProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.IsCaretVisible"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.IsSelectionEnabledProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.IsSelectionEnabled"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.ScaleFactorProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.ScaleFactor"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.SelectionFillProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.SelectionFill"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.SelectionStrokeProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.SelectionStroke"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.PaletteProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.Palette"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.CaretBrushProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.CaretBrush"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.VerticalScrollBarVisibilityProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.VerticalScrollBarVisibility"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontFamilyProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontFamily"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontSizeProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontSize"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.AutoScrollToCaretOnTextChangeProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.AutoScrollToCaretOnTextChange"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.SearchPanelWidthProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.SearchPanelWidth"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.ShouldTaggersProcessEntireLinesProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.ShouldTaggersProcessEntireLines"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.IsWordWrapEnabledProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.IsWordWrapEnabled"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.RadSyntaxEditor"/> class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.CommandError">
            <summary>
            Occurs when command error is produced.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.CommandExecuted">
            <summary>
            Occurs when command is executed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.CommandExecuting">
            <summary>
            Occurs when command is executing.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.DocumentChanged">
            <summary>
            Occurs when document has been changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.DocumentChanging">
            <summary>
            Occurs when document is being changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.IsReadOnlyChanged">
            <summary>
            Occurs when the readonly state has changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.LayoutPropertiesChanged">
            <summary>
            Occurs when layout properties are changed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.PreviewSyntaxEditorInput">
            <summary>
            Occurs when text input in the syntax editor is starting.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.PreviewSyntaxEditorKeyDown">
            <summary>
            Occurs when key down is pressed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.PreviewSyntaxEditorKeyUp">
            <summary>
            Occurs when a key is released.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.PreviewSyntaxEditorMouseLeftButtonDown">
            <summary>
            Occurs when mouse left button down is pressed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.PreviewSyntaxEditorMouseRightButtonDown">
            <summary>
            Occurs when mouse right button down is pressed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.ScaleFactorChanged">
            <summary>
            Occurs when scale factor changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.ViewportChanged">
            <summary>
            Occurs when the viewport changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.SelectionChanged">
            <summary>
            Occurs when the selection of the control changes.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.TextSearched">
            <summary>
            Occurs when a text is searched in the control.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.FoldingChanged">
            <summary>
            Occurs when folding region is created, removed, opened or closed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.AllowScaling">
            <summary>
            Gets or sets a value indicating whether [allow scaling].
            </summary>
            <value><c>true</c> if [allow scaling]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontFamily">
            <summary>
            Gets or sets a value indicating the font family of the text editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.EditorFontSize">
            <summary>
            Gets or sets a value indicating the font size of the text editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.SearchPanelWidth">
            <summary>
            Gets or sets a value indicating the width of the search panel.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.ShouldTaggersProcessEntireLines">
            <summary>
            Gets or sets whether taggers are given the entire lines of text (true) or 
            only the currently visible in the viewport part of the text (false) for processing.
            Default value is false.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsWordWrapEnabled">
            <summary>
            Gets or sets a value indicating whether the word wrapping mode is enabled.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.CaretDisplayMode">
            <summary>
            Gets or sets the caret display mode.
            </summary>
            <value>The caret display mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.CaretPosition">
            <summary>
            Gets the caret position.
            </summary>
            <value>The caret position.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Commands">
            <summary>
            Gets the commands.
            </summary>
            <value>The commands.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Document">
            <summary>
            Gets or sets the text document.
            </summary>
            <value>The document.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.EditorPresenter">
            <summary>
            Gets the editor presenter.
            </summary>
            <value>The editor presenter.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.FoldingManager">
            <summary>
            Gets the folding manager.
            </summary>
            <value>The folding manager.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.HorizontalScrollBar">
            <summary>
            Gets the horizontal scroll bar.
            </summary>
            <value>The horizontal scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.HorizontalScrollBarVisibility">
            <summary>
            Gets or sets the horizontal scroll bar visibility.
            </summary>
            <value>The horizontal scroll bar visibility.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsCaretVisible">
            <summary>
            Gets or sets a value indicating whether this instance is caret visible.
            </summary>
            <value><c>true</c> if this instance is caret visible; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsSelectionEnabled">
            <summary>
            Gets or sets a value indicating whether this instance is selection enabled.
            </summary>
            <value><c>true</c> if this instance is selection enabled; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.KeyBindings">
            <summary>
            Gets the key bindings.
            </summary>
            <value>The key bindings.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Margins">
            <summary>
            Gets the margins.
            </summary>
            <value>The margins.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Properties">
            <summary>
            Gets the properties.
            </summary>
            <value>The properties.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.ScaleFactor">
            <summary>
            Gets or sets the scale factor.
            </summary>
            <value>The scale factor.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Selection">
            <summary>
            Gets the selection.
            </summary>
            <value>The selection.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.SelectionFill">
            <summary>
            Gets or sets the selection fill.
            </summary>
            <value>The selection fill.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.SelectionStroke">
            <summary>
            Gets or sets the selection stroke.
            </summary>
            <value>The selection stroke.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Palette">
            <summary>
            Gets or sets the palette of syntax elements' colors used for this <see cref="T:Telerik.Windows.Controls.RadSyntaxEditor"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.CaretBrush">
            <summary>
            Gets or sets the brush of the active caret in the editor.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.TaggersRegistry">
            <summary>
            Gets the taggers registry.
            </summary>
            <value>The taggers registry.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.TextFormatDefinitions">
            <summary>
            Gets the text format definitions.
            </summary>
            <value>The text format definitions.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.UILayersBuilder">
            <summary>
            Gets or sets the UI layers builder.
            </summary>
            <value>The UI layers builder.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.UseMonospacedFontOptimization">
            <summary>
            Gets or sets a value indicating whether [use monospaced font optimization].
            </summary>
            <value><c>true</c> if [use monospaced font optimization]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.UseShiftKeyInSelection">
            <summary>
            Gets or sets a value indicating whether [use shift key in selection].
            </summary>
            <value><c>true</c> if [use shift key in selection]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.VerticalScrollBar">
            <summary>
            Gets the vertical scroll bar.
            </summary>
            <value>The vertical scroll bar.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.VerticalScrollBarVisibility">
            <summary>
            Gets or sets the vertical scroll bar visibility.
            </summary>
            <value>The vertical scroll bar visibility.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.AutoScrollToCaretOnTextChange">
            <summary>
            Gets or sets a value indicating whether the document is automatically scrolled to the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.CaretPosition"/> when the text inside the document is changed.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.Viewport">
            <summary>
            Gets the viewport.
            </summary>
            <value>The viewport.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.TouchMarkerDragDistance">
            <summary>
            Gets or sets a value indicating the touch drag area around a touch selection marker.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Copy">
            <summary>
            Copies this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.GetLineBoundingRectangleByLineNumber(System.Int32,System.Boolean)">
            <summary>
            Gets the line bounding rectangle by line number.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.GetLinePartBoundingRectangle(Telerik.Windows.SyntaxEditor.Core.Text.Span,System.Boolean)">
            <summary>
            Gets the line part bounding rectangle.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.GetPointFromPosition(Telerik.Windows.Controls.SyntaxEditor.UI.CaretPosition)">
            <summary>
            Gets the point from position.
            </summary>
            <param name="position">The position.</param>
            <returns>Point.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.GetPositionFromPoint(System.Windows.Point)">
            <summary>
            Gets the position from point.
            </summary>
            <param name="point">The point.</param>
            <returns>CaretPosition.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.GotoLine(System.Int32,System.Boolean)">
            <summary>
            Navigates to the line by number.
            </summary>
            <param name="lineNumber">The line number.</param>
            <param name="focusSyntaxEditor">Specifies if the SyntaxEditor should be focused after navigating to the given line.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.InvalidateEditorLayout">
            <summary>
            Invalidates the editor layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.InvalidateEditorLayout(System.Boolean)">
            <summary>
            Invalidates the editor layout.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.MoveCurrentLineToBottom">
            <summary>
            Moves the current line to bottom.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.MoveCurrentLineToTop">
            <summary>
            Moves the current line to top.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.PageDown">
            <summary>
            Pages down.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.PageUp">
            <summary>
            Pages up.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.SelectAll">
            <summary>
            Selects all.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.UpdateSelection">
            <summary>
            Updates the selection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ZoomIn(System.Double)">
            <summary>
            Zooms in.
            </summary>
            <param name="zoomFactor">The zoom increase factor. Should be a number greater than 1. Default value is 1.1.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ZoomOut(System.Double)">
            <summary>
            Zooms out.
            </summary>        
            <param name="zoomFactor">The zoom decrease factor. Should be a number less than 1. Default value is 0.91.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ZoomTo(System.Double)">
            <summary>
            Zooms to particular zoom level.
            </summary>
            <param name="zoomLevel">The zoom level.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">ZoomLevel must be non negative.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Telerik#Windows#Controls#IThemable#ResetTheme">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnMouseWheelZoom(System.Double)">
            <summary>
            Called when mouse wheel zoom is requested.
            </summary>
            <param name="delta">The mouse wheel delta.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnCommandError(Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs)">
            <summary>
            Handles the <see cref="E:CommandError" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandErrorEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnCommandExecuted(Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs)">
            <summary>
            Handles the <see cref="E:CommandExecuted" /> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnCommandExecuting(Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs)">
            <summary>
            Handles the <see cref="E:CommandExecuting" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.Commands.CommandExecutingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPreviewSyntaxEditorMouseLeftButtonDown(Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs)">
            <summary>
            Handles the <see cref="E:PreviewSyntaxEditorMouseLeftButtonDown" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPreviewSyntaxEditorMouseRightButtonDown(Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs)">
            <summary>
            Handles the <see cref="E:PreviewSyntaxEditorMouseRightButtonDown" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorMouseButtonEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnDocumentChanged">
            <summary>
            Called when [document changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnDocumentChanging">
            <summary>
            Called when [document changing].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            Invoked whenever an unhandled <see cref="E:System.Windows.UIElement.GotFocus" /> event reaches this element in its route.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnIsReadOnlyChanged(System.EventArgs)">
            <summary>
            Handles the <see cref="E:IsReadOnlyChanged" /> event.
            </summary>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnLayoutPropertiesChanged">
            <summary>
            Called when [layout properties changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPreviewSyntaxEditorInput(Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs)">
            <summary>
            Handles the <see cref="E:PreviewSyntaxEditorInput" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorInputEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPreviewSyntaxEditorKeyDown(Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the <see cref="E:PreviewSyntaxEditorKeyDown" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPreviewSyntaxEditorKeyUp(Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs)">
            <summary>
            Handles the <see cref="E:PreviewSyntaxEditorKeyUp" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.PreviewSyntaxEditorKeyEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnScaleFactorChanged">
            <summary>
            Called when [scale factor changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnViewportChanged">
            <summary>
            Called when [viewport changed].
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnSelectionChanged">
            <summary>
            Called when the selection changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.InitializeTouch(Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter)">
            <summary>
            Called when touch manager methods are being initialized for the <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorPresenter" />.
            </summary>
            <param name="presenter"></param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnTextSearched(Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs)">
            <summary>
            Handles the <see cref="E:TextSearched" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Controls.SyntaxEditor.UI.TextSearchedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnCreateAutomationPeer">
            <summary>
            Returns class-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementations for the Windows Presentation Foundation (WPF) infrastructure.
            </summary>
            <returns>
            The type-specific <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> implementation.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnPropertyChanged(System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Occurs when property changes.
            </summary>
            <param name="e"></param>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.AcceptsReturnProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.AcceptsReturn"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.IndentProviderProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.IndentProvider"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.InsertModeProperty">
            <summary>
            Registers the <see cref="T:Telerik.Windows.Controls.InsertMode"/> property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadSyntaxEditor.IsReadOnlyProperty">
            <summary>
            Registers the <see cref="P:Telerik.Windows.Controls.RadSyntaxEditor.IsReadOnly"/> property.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.DocumentContentChanged">
            <summary>
            DocumentContentChanged event.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadSyntaxEditor.DocumentContentChanging">
            <summary>
            DocumentContentChanging event.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.AcceptsReturn">
            <summary>
            Gets or sets a value indicating whether [accepts return].
            </summary>
            <value><c>true</c> if [accepts return]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.CurrentInsertMode">
            <summary>
            Gets or sets the current insert mode.
            </summary>
            <value>The current insert mode.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IndentProvider">
            <summary>
            Gets or sets the indent provider.
            </summary>
            <value>The indent provider.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IntelliPrompts">
            <summary>
            Gets the intelli prompts.
            </summary>
            <value>The intelli prompts.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsInUndoGroup">
            <summary>
            Gets a value indicating whether this instance is in undo group.
            </summary>
            <value><c>true</c> if this instance is in undo group; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsOverwriteModeEnabled">
            <summary>
            Gets or sets a value indicating whether this instance is overwrite mode enabled.
            </summary>
            <value><c>true</c> if this instance is overwrite mode enabled; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadSyntaxEditor.IsReadOnly">
            <summary>
            Gets or sets a value indicating whether this instance is read only.
            </summary>
            <value><c>true</c> if this instance is read only; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Backspace">
            <summary>
            Backspaces this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.BeginUndoGroup">
            <summary>
            Begins the undo group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.CancelUndoGroup">
            <summary>
            Cancels the undo group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.CompleteCode">
            <summary>
            Completes the code.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.CreateSyntaxEditorHistoryState">
            <summary>
            Creates the state of the code editor history.
            </summary>
            <returns>SyntaxEditorHistoryState.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Cut">
            <summary>
            Cuts this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.DecreaseLineIndent">
            <summary>
            Decreases the line indent.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Delete">
            <summary>
            Deletes this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.DeleteFullLine">
            <summary>
            Deletes the full line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.DeleteWordToLeft">
            <summary>
            Deletes the word to left.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.DeleteWordToRight">
            <summary>
            Deletes the word to right.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.EndUndoGroup">
            <summary>
            Ends the undo group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.IncreaseLineIndent">
            <summary>
            Increases the line indent.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Indent">
            <summary>
            Indents this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Insert(System.String)">
            <summary>
            Inserts the specified text.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.InsertNewLine">
            <summary>
            Inserts the new line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.InsertTab">
            <summary>
            Inserts the tab.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Paste">
            <summary>
            Pastes this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Redo">
            <summary>
            Redoes this instance.
            </summary>
            <returns>System.Boolean.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ReplaceAllMatches(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Replaces all matches.
            </summary>
            <param name="searchText">The search text.</param>
            <param name="replaceText">The replace text.</param>
            <param name="matchCase">The match case.</param>
            <param name="useRegularExpression">The use regular expression.</param>
            <returns>System.Int32.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ReplaceAllMatches(System.String,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Replaces all matches.
            </summary>
            <param name="searchText">The search text.</param>
            <param name="replaceText">The replace text.</param>
            <param name="matchCase">The match case.</param>
            <param name="useRegularExpression">The use regular expression.</param>
            <param name="matchWord">The match word.</param>
            <returns>System.Int32.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ReplaceNextMatch(System.String,System.Int32,System.String,System.Boolean,System.Boolean)">
            <summary>
            Replaces the next match.
            </summary>
            <param name="searchText">The search text.</param>
            <param name="startIndex">The start index.</param>
            <param name="replaceText">The replace text.</param>
            <param name="matchCase">The match case.</param>
            <param name="useRegularExpression">The use regular expression.</param>
            <returns>System.Boolean.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.RestoreSyntaxEditorHistoryState(Telerik.Windows.Controls.SyntaxEditor.UI.SyntaxEditorHistoryState)">
            <summary>
            Restores the state of the code editor history.
            </summary>
            <param name="historyState">State of the history.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.ToggleInsertMode">
            <summary>
            Toggles the insert mode.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Undo">
            <summary>
            Undoes this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Unindent">
            <summary>
            Unindents this instance.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnDocumentContentChanged(Telerik.Windows.SyntaxEditor.Core.Text.TextContentChangedEventArgs)">
            <summary>
            Handles the <see cref="E:DocumentContentChanged" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.SyntaxEditor.Core.Text.TextContentChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.OnDocumentContentChanging(Telerik.Windows.SyntaxEditor.Core.Text.TextContentChangingEventArgs)">
            <summary>
            Handles the <see cref="E:DocumentContentChanging" /> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.SyntaxEditor.Core.Text.TextContentChangingEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadSyntaxEditor.MeasureOverride(System.Windows.Size)">
            <summary>
            Called when control is measured.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.InsertMode">
            <summary>
            Enum InsertMode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.InsertMode.Insert">
            <summary>
            Insert mode.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.InsertMode.Overwrite">
            <summary>
            Overwrite mode.
            </summary>
        </member>
    </members>
</doc>
