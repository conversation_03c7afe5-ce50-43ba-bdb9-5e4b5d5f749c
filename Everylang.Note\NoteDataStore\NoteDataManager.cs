﻿using Everylang.Common.LogManager;
using Everylang.Note.SettingsApp;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Everylang.Note.NoteDataStore
{
    public static class NoteDataManager
    {
        private static bool _isLoading;
        private static ObservableCollection<NoteDataModel>? _notesList;

        public static ObservableCollection<NoteDataModel> NotesList
        {
            get
            {
                if (_notesList == null)
                {
                    _notesList = new ObservableCollection<NoteDataModel>(GetAllData());
                }
                return _notesList;
            }
        }

        public static void ShowAllNotes()
        {
            foreach (var noteDataModel in NotesList)
            {
                noteDataModel.IsSelected = false;
                if (noteDataModel.IsVisible)
                {
                    noteDataModel.NoteForm?.Hide();
                    noteDataModel.NoteForm?.Show();
                }
            }
        }

        public static List<NoteDataModel> GetAllData()
        {
            _isLoading = true;
            var noteList = new List<NoteDataModel>();
            try
            {
                var db = SettingsMiminoteManager.LiteDb;
                {
                    var collection = db.GetCollection("NoteDataModel");
                    var sd = collection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        NoteDataModel dataModel = new NoteDataModel();
                        dataModel.Id = bsonDocument["_id"].AsObjectId;
                        dataModel.NoteName = bsonDocument["NoteName"].AsString;
                        dataModel.Text = bsonDocument["Text"].AsString;
                        dataModel.Color = bsonDocument["Color"].AsString;
                        dataModel.LocationX = bsonDocument["LocationX"].AsInt32;
                        dataModel.LocationY = bsonDocument["LocationY"].AsInt32;
                        dataModel.Height = bsonDocument["Height"].AsInt32;
                        dataModel.Width = bsonDocument["Width"].AsInt32;
                        dataModel.TextSize = bsonDocument["TextSize"].AsInt32;
                        dataModel.DateTimeLastEdit = bsonDocument["DateTimeLastEdit"].AsString;
                        dataModel.IsCheckList = bsonDocument["IsCheckList"].AsBoolean;
                        dataModel.IsDeleted = bsonDocument["IsDeleted"].AsBoolean;
                        dataModel.IsArchived = bsonDocument["IsArchived"].AsBoolean;
                        dataModel.DateTimeDeleted = bsonDocument["DateTimeDeleted"].AsDateTime;
                        if (!bsonDocument["Tags"].IsNull) dataModel.Tags = bsonDocument["Tags"].AsString;
                        dataModel.CheckListCollection = new ObservableCollection<CheckListDataModel>();
                        noteList.Add(dataModel);

                        var collectionCheckList = db.GetCollection("CheckListDataModel");
                        var sdCheckList = collectionCheckList.Find(Query.EQ("IdNote", dataModel.Id));
                        foreach (var bsonDocumentCheckList in sdCheckList)
                        {
                            CheckListDataModel dataModelCheckList = new CheckListDataModel();
                            dataModelCheckList.IdNote = bsonDocumentCheckList["IdNote"].AsObjectId;
                            dataModelCheckList.IsSelectedItem = bsonDocumentCheckList["IsSelectedItem"].AsBoolean;
                            dataModelCheckList.Text = bsonDocumentCheckList["Text"].AsString;
                            dataModel.CheckListCollection.Add(dataModelCheckList);
                        }

                        dataModel.IsLoaded = true;
                    }
                }

            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            _isLoading = false;
            return noteList;
        }

        public static void AddNewData(NoteDataModel dataModel)
        {
            try
            {
                var db = SettingsMiminoteManager.LiteDb;
                {
                    var collection = db.GetCollection("NoteDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    bsonDocument["_id"] = dataModel.Id;
                    bsonDocument["NoteName"] = dataModel.NoteName;
                    bsonDocument["Text"] = dataModel.Text;
                    bsonDocument["Color"] = dataModel.Color;
                    bsonDocument["LocationX"] = dataModel.LocationX;
                    bsonDocument["LocationY"] = dataModel.LocationY;
                    bsonDocument["Height"] = dataModel.Height;
                    bsonDocument["Width"] = dataModel.Width;
                    bsonDocument["TextSize"] = dataModel.TextSize;
                    bsonDocument["DateTimeLastEdit"] = dataModel.DateTimeLastEdit;
                    bsonDocument["DateTimeDeleted"] = dataModel.DateTimeDeleted;
                    bsonDocument["IsCheckList"] = dataModel.IsCheckList;
                    bsonDocument["IsDeleted"] = dataModel.IsDeleted;
                    bsonDocument["IsArchived"] = dataModel.IsArchived;
                    bsonDocument["Tags"] = dataModel.Tags;
                    collection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

        }

        public static void AddNewDataReindex()
        {
            try
            {
                var db = SettingsMiminoteManager.LiteDb;
                foreach (var dataModel in NotesList)
                {
                    var collection = db.GetCollection("NoteDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    bsonDocument["_id"] = ObjectId.NewObjectId();
                    bsonDocument["Id"] = dataModel.Id;
                    bsonDocument["NoteName"] = dataModel.NoteName;
                    bsonDocument["Text"] = dataModel.Text;
                    bsonDocument["Color"] = dataModel.Color;
                    bsonDocument["LocationX"] = dataModel.LocationX;
                    bsonDocument["LocationY"] = dataModel.LocationY;
                    bsonDocument["Height"] = dataModel.Height;
                    bsonDocument["Width"] = dataModel.Width;
                    bsonDocument["TextSize"] = dataModel.TextSize;
                    bsonDocument["DateTimeLastEdit"] = dataModel.DateTimeLastEdit;
                    bsonDocument["IsCheckList"] = dataModel.IsCheckList;
                    bsonDocument["DateTimeDeleted"] = dataModel.DateTimeDeleted;
                    bsonDocument["IsDeleted"] = dataModel.IsDeleted;
                    bsonDocument["IsArchived"] = dataModel.IsArchived;
                    bsonDocument["Tags"] = dataModel.Tags;
                    collection.Insert(bsonDocument);

                    var collectionCheckList = db.GetCollection("CheckListDataModel");
                    if (dataModel.CheckListCollection != null)
                        for (int i = 0; i < dataModel.CheckListCollection.Count; i++)
                        {
                            if (dataModel.CheckListCollection[i].IsDopItem) continue;
                            var checkListDataModel = dataModel.CheckListCollection[i];
                            BsonDocument bsonDocumentCheckList = new BsonDocument();
                            bsonDocumentCheckList["_id"] = ObjectId.NewObjectId();
                            bsonDocumentCheckList["Id"] = checkListDataModel.IdNote;
                            bsonDocumentCheckList["Text"] = checkListDataModel.Text;
                            bsonDocumentCheckList["IsSelectedItem"] = checkListDataModel.IsSelectedItem;
                            collectionCheckList.Insert(bsonDocumentCheckList);
                        }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

        }

        public static void UpdateData(NoteDataModel? dataModel)
        {
            try
            {
                if (dataModel == null) return;
                if (_isLoading)
                {
                    return;
                }
                var db = SettingsMiminoteManager.LiteDb;
                var collection = db.GetCollection("NoteDataModel");
                var bsonDocument = collection.FindById(dataModel.Id);
                bsonDocument["NoteName"] = dataModel.NoteName;
                bsonDocument["Text"] = dataModel.Text;
                bsonDocument["Color"] = dataModel.Color;
                bsonDocument["LocationX"] = dataModel.LocationX;
                bsonDocument["LocationY"] = dataModel.LocationY;
                bsonDocument["DateTimeDeleted"] = dataModel.DateTimeDeleted;
                bsonDocument["Height"] = dataModel.Height;
                bsonDocument["Width"] = dataModel.Width;
                bsonDocument["TextSize"] = dataModel.TextSize;
                bsonDocument["DateTimeLastEdit"] = dataModel.DateTimeLastEdit;
                bsonDocument["IsCheckList"] = dataModel.IsCheckList;
                bsonDocument["IsDeleted"] = dataModel.IsDeleted;
                bsonDocument["IsArchived"] = dataModel.IsArchived;
                bsonDocument["Tags"] = dataModel.Tags;
                collection.Update(bsonDocument);

                var collectionCheckList = db.GetCollection("CheckListDataModel");
                collectionCheckList.DeleteMany(Query.EQ("IdNote", dataModel.Id));
                if (dataModel.CheckListCollection != null)
                    for (int i = 0; i < dataModel.CheckListCollection.Count; i++)
                    {
                        if (dataModel.CheckListCollection[i].IsDopItem) continue;
                        var checkListDataModel = dataModel.CheckListCollection[i];
                        BsonDocument bsonDocumentCheckList = new BsonDocument();
                        bsonDocumentCheckList["_id"] = ObjectId.NewObjectId();
                        bsonDocumentCheckList["IdNote"] = checkListDataModel.IdNote;
                        bsonDocumentCheckList["Text"] = checkListDataModel.Text;
                        bsonDocumentCheckList["IsSelectedItem"] = checkListDataModel.IsSelectedItem;
                        collectionCheckList.Insert(bsonDocumentCheckList);
                    }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        public static void DelData(NoteDataModel? dataModel)
        {
            try
            {
                if (dataModel != null)
                {
                    var db = SettingsMiminoteManager.LiteDb;
                    var collection = db.GetCollection("NoteDataModel");
                    collection.Delete(dataModel.Id);
                    var collectionCheckList = db.GetCollection("CheckListDataModel");
                    collectionCheckList.DeleteMany(Query.EQ("IdNote", dataModel.Id));
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
