﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System;
using System.Collections.Specialized;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Threading;
using Application = System.Windows.Application;

namespace Everylang.App.Clipboard
{
    internal static class ClipboardMonitorWorker
    {
        private static bool _started;
        internal static bool Pause { get; set; }
        internal static bool IgnoreLast { get; set; }
        internal delegate void OnClipboardChangeEventHandler(ClipboardDataObject dataObject);
        internal static event OnClipboardChangeEventHandler? OnClipboardChange;
        private static WindowClipboardMonitor? _clipboardMonitor;

        internal static void Start()
        {
            if (!_started && SettingsManager.Settings.ClipboardHistoryIsOn)
            {
                if (Application.Current.MainWindow != null)
                {
                    _clipboardMonitor = new WindowClipboardMonitor(Application.Current.MainWindow);
                }
                if (_clipboardMonitor != null)
                {
                    _clipboardMonitor.ClipboardChanged += () =>
                    {
                        DispatcherHelper.RunAsStaThread(ClipChanged);
                    };
                }
                _started = true;
            }
        }

        internal static void Stop()
        {
            if (_started)
            {
                _started = false;
                _clipboardMonitor?.Dispose();
            }
        }

        private static string? _lastText;

        private static void ClipChanged()
        {
            try
            {
                if (SettingsManager.IsStopWorking || Pause || IgnoreLast)
                {
                    IgnoreLast = false;
                    return;
                }
                if (SettingsManager.Settings.ClipboardSountIsOn && !SettingsManager.IsClipboardSoundOff)
                {
                    SoundManager.PlayForClipboard();
                }

                SettingsManager.IsClipboardSoundOff = false;
                IDataObject? iData = System.Windows.Forms.Clipboard.GetDataObject();
                if (iData != null)
                {
                    var dataClipObject = (DataObject)iData;
                    string[] dataFormats = dataClipObject.GetFormats(true);

                    ClipboardDataObject dataObject = new ClipboardDataObject();
                    if (dataFormats.Contains(DataFormats.Text) || dataFormats.Contains(DataFormats.UnicodeText))
                    {
                        dataObject.TextData = dataClipObject.GetData(DataFormats.UnicodeText, true) as string;
                        if (dataObject.TextData == null)
                        {
                            // OemText can be retrieved as CF_TEXT.
                            dataObject.TextData = dataClipObject.GetData(DataFormats.Text, true) as string;
                        }
                        dataObject.TextPlain = dataObject.TextData;
                    }
                    if (dataFormats.Contains(DataFormats.Html))
                    {
                        dataObject.TextData = dataClipObject.GetData(DataFormats.Html, true) as string;
                        dataObject.IsHtml = true;
                    }
                    if (dataFormats.Contains(DataFormats.Rtf))
                    {
                        dataObject.TextData = dataClipObject.GetData(DataFormats.Rtf, true) as string;
                        dataObject.IsHtml = false;
                        dataObject.IsRtf = true;
                    }
                    if (dataFormats.Contains(DataFormats.Bitmap) && string.IsNullOrEmpty(dataObject.TextPlain) && SettingsManager.Settings.ClipboardSaveImage)
                    {
                        dataObject.ImageData = dataClipObject.GetData(DataFormats.Bitmap, true) as Image;
                        using (var stream = new MemoryStream())
                        {
                            dataObject.ImageData?.Save(stream, ImageFormat.Png);
                            stream.Position = 0;
                            using (var sha1 = SHA1.Create())
                            {
                                dataObject.TextPlain = Convert.ToBase64String(sha1.ComputeHash(stream));
                                string invalid = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
                                foreach (char c in invalid)
                                {
                                    dataObject.TextPlain = dataObject.TextPlain.Replace(c.ToString(), "");
                                }
                            }
                        }
                        dataObject.TextPlain = dataObject.TextPlain.Replace("=", "");
                        dataObject.IsImage = true;
                    }
                    if (dataFormats.Contains(DataFormats.FileDrop) && SettingsManager.Settings.ClipboardSaveFilePath)
                    {
                        dataObject.TextData = GetFileDropList();
                        dataObject.TextPlain = dataObject.TextData;
                        dataObject.IsFiles = true;
                    }
                    if (!dataObject.IsHtml && !dataObject.IsRtf && !dataObject.IsFiles && dataObject.TextPlain != null)
                    {
                        dataObject.IsPlainText = true;
                    }

                    if (dataObject.IsOk && _lastText != dataObject.TextPlain)
                    {
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            (ThreadStart)delegate
                            {
                                if (!Pause)
                                {
                                    if (OnClipboardChange != null)
                                        OnClipboardChange(dataObject);
                                }
                            });
                    }

                    _lastText = dataObject.TextPlain;


                }
            }
            catch (COMException)
            {

            }
            catch
            {
                // ignored
            }
        }

        internal static string GetFileDropList()
        {
            try
            {
                StringCollection? returnList = null;
                if (System.Windows.Forms.Clipboard.ContainsFileDropList())
                {
                    returnList = System.Windows.Forms.Clipboard.GetFileDropList();
                }
                string result = "";
                if (returnList != null)
                    foreach (var st in returnList)
                    {
                        result += st + Environment.NewLine;
                    }
                return result.Trim();
            }
            catch (COMException)
            {

            }
            catch
            {
                // ignored
            }

            return "";
        }
    }
}
