﻿using Everylang.App.Data.DataStore;
using Everylang.App.SwitcherLang;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;

namespace Everylang.App.Data.DataModel
{

    internal class SettingsDataModel
    {
        internal bool DontSave { get; set; }

        private string _snippetsShowAllShortcut = "";
        private string _snippetsAddNewShortcut = "";
        private int _snippetsMethodPastByKey;
        private bool _snippetsIsCaseSensitive;
        private bool _snippetsWithOtherLayout;
        private bool _snippetsIsShowTipWindow;
        private bool _snippetsIsOn;
        private bool _smartClickShowOnDoubleMiddle;
        private bool _smartClickMiniIsOn;
        private int _smartClickMiniSizeIcon;
        private double _smartClickMiniPosX;
        private double _smartClickMiniPosY;
        private string _smartClickCheckedItems = "";
        private string _smartClickShortcut = "";
        private bool _smartClickShowOnPressHotKeys;
        private bool _smartClickShowOnPressLeftAndRightMouseButtons;
        private int _smartClickSearchService;
        private bool _smartClickIsOn;
        private string _diaryShowShortcut = "";
        private bool _diaryIsOn;
        private string _diaryPassword = "";
        private int _maxDiaryItems;
        private bool _isSaveOneWordSentences;
        private bool _clipboardSaveFilePath;
        private bool _clipboardSaveImage;
        private int _clipboardMaxClipboardItems;
        private bool _clipboardPasteByCtrlPlusIndexIsOn;
        private bool _clipboardSountIsOn;
        private string _clipboardPasteRoundShortcut = "";
        private bool _clipboardPasteRoundIsOn;
        private string _clipboardPasteWithoutFormattingShortcut = "";
        private bool _clipboardReplaceWithoutChangeClipboard;
        private string _clipboardShowHistoryShortcut = "";
        private bool _clipboardHistoryIsOn;
        private bool _clipboardPasteWithoutFormattingShortcutIsOn;
        private bool _isHideIndicateInFullScreenApp;
        private bool _langFlagIsIndicateCapsLockState;
        private bool _langFlagIsIndicateNumLockState;
        private int _langFlagOpacityIcon;
        private int _langFlagSizeIcon;
        private double _langInfoLargeWindowPosX;
        private double _langInfoLargeWindowPosY;
        private int _langFlagPosMouseY;
        private int _langFlagPosMouseX;
        private int _langFlagPosCarretY;
        private int _langFlagPosCarretX;
        private bool _langFlagShowIcons;
        private bool _langFlagShowInTray;
        private bool _langFlagShowLargeWindow;
        private bool _langFlagShowForCaret;
        private bool _langFlagShowForMouse;
        private bool _autoSwitcherAddRule;
        private int _autoSwitcherCountCheckRule;
        private bool _autoSwitcherOnlyAfterSeparator;
        private bool _autoSwitcherAfterPause;
        private bool _autoSwitcherSwitchTextLangAfterPressEnter;
        private bool _autoSwitcherNotSwitchTextLangWithAllUpperCaseLetters;
        private bool _autoSwitcherFixTwoUpperCaseLettersInStart;
        private bool _autoSwitcherFixWrongUpperCase;
        private bool _autoSwitcherIsSwitchOneLetter;
        private string _autoSwitcherNotTrueListOfLang = "";
        private bool _autoSwitcherIsOn;
        private int _switcherSwitchLangByNonStandartKey;
        private int _switcherSwitchMethod;
        private string _switcherLangAndKeysForSwitch = "";
        private bool _switcherSwitchLangByCtrlPlusNumberIsOn;
        private bool _switcherSountIsOn;
        private bool _autoSwitcherShowAcceptWindow;
        private bool _autoSwitcherDisableAutoSwitchAfterManualSwitchw;
        private string _switcherSwitchTextLangForAllLineShortcut = "";
        private string _switcherSwitchTextLangShortcut = "";
        private string _switcherNotTrueListOfLang = "";
        private string _converterShortcutCapsOpenWindow = "";
        private string _converterShortcutCapsInvert = "";
        private string _converterShortcutCapsUp = "";
        private string _converterShortcutCapsDown = "";
        private string _converterFirstLetterToDown = "";
        private string _converterFirstLetterToUp = "";
        private string _switcherShortcutSwitchLangSelectedTextShortcut = "";
        private bool _switcherLeaveTextSelectedAfterSwitch;
        private bool _switcherIsOn;
        private bool _spellCheckIsOn;
        private bool _spellCheckCloseByTimer;
        private bool _spellCheckWhileTyping;
        private bool _spellCheckWhileTypingSoundOn;
        private bool _spellCheckWhileTypingUseNumber;
        private string _spellCheckShortcut = "";
        private string _translateFontSize = "";
        private string _translateFontFamily = "";
        private bool _translateHistoryIsOn;
        private bool _translateIsOn;
        private string _translateShowMiniFormShortcut = "";
        private bool _translateShowMiniFormDoubleCtrl;
        private bool _translateShowMiniFormAlwaysWhenSelectText;
        private int _translateProvider;
        private string? _translateLangTo = "";
        private string? _translateLangFrom = "";
        private string _translateFavoriteLanguages = "";
        private bool _translateOnlyFavoriteLanguages;
        private DateTime _licEvaluateStart;
        private string _licEvaluateCode = "";
        private bool _licIsEvaluate;
        private bool _licIsActive;
        private string _licEmail = "";
        private string _licCode = "";
        private string? _proxyPassword = "";
        private string? _proxyUserName = "";
        private string? _proxyPort = "";
        private string? _proxyServer = "";
        private string _clipboardFastActionWindowSize = "";
        private string _diaryFastActionWindowSize = "";
        private string _snippetsActionWindowSize = "";
        private bool _snippetsIsEnabledCountUsage;
        private bool _snippetsIsEnabledSortingAlphabet;
        private DateTime _themeTimeStartNight;
        private DateTime _themeTimeEndNight;
        private string _functionOrder = "";
        private bool _isUseNightTheme;
        private bool _isStopWorkingFullScreen;
        private bool _proxyUseIE;
        private string _currentVersion = "";
        private bool _isCheckUpdateBeta;
        private bool _isCheckedStartAsAdmin;
        private bool _isCheckUpdate;
        private string? _appUILang = "";
        private string _appUITheme = "";
        private string _appUIAccent = "";
        private string _appFont = "";
        private int _appFontSize;
        private string _openMainWindowShortcut = "";
        private string _stopWorkingShortcut = "";
        private bool _mainFormMinimizeToTray;
        private bool _canClose;
        private string? _lastVersionForCheckHash = "";
        private string? _hashFromSite = "";
        private string _convertExpresionShortcut = "";
        private string _converterOpenWindowShortcut = "";
        private string _convertTransliterationShortcut = "";
        private string _convertEncloseTextQuotationMarksShortcut = "";
        private string _converterShortcutCamelCase = "";
        private string _converterShortcutSnakeCase = "";
        private string _converterShortcutKebabCase = "";
        private string _converterShortcutPascalCase = "";
        private string _converterShortcutReplaceSelText = "";
        private string _converterFramesList = "";
        private string _soundForLangSwitch = "";
        private string _soundForSpellCheck = "";
        private string _soundForClipboard = "";
        private int _soundVolumeForClipboard;
        private int _soundVolumeForSpellCheck;
        private int _soundVolumeForLangSwitch;
        private string _ocrShortcut = "";


        internal SettingsDataModel()
        {
            DontSave = true;
            HashFromSite = "";
            LastVersionForCheckHash = "";
            SetDefaultTranslateSettings();


            CurrentVersion = "";
            MainFormMinimizeToTray = false;
            CanClose = true;
            AppUILang = "";
            AppUITheme = "";
            AppUIAccent = "";
            AppFont = SystemFonts.DefaultFont.FontFamily.Name;
            AppFontSize = 14;
            OpenMainWindowShortcut = "shortcutWin+Insert";
            StopWorkingShortcut = "";
            IsCheckUpdate = true;
            IsCheckUpdateBeta = false;
            IsCheckedStartAsAdmin = false;
            ProxyUseIE = true;
            ThemeTimeStartNight = DateTime.MinValue.AddHours(20);
            ThemeTimeEndNight = DateTime.MinValue.AddHours(7);
            IsUseNightTheme = false;
            IsStopWorkingFullScreen = false;
            ProxyServer = "";
            ProxyPort = "";
            ProxyUserName = "";
            ProxyPassword = "";
            FunctionOrder = "0 1 2 3 4 5";

            LicCode = "";
            LicEmail = "";
            LicIsEvaluate = false;
            LicEvaluateStart = DateTime.MaxValue;
            OcrLangsListStr = "";

            SetDefaultSpellCheckSettings();
            SetDefaultSwitcherSettings();
            SetDefaultAutoSwitcherSettings();
            SetDefaultLangFlagSettings();
            SetDefaultClipboardSettings();
            SetDefaultDiarySettings();
            SetDefaultSmartClickSettings();
            SetDefaultSnippetsSettings();
            SetDefaultConvertSettings();
            SetDefaultSoundSettings();
            SetDefaultOcrSettings();

            DontSave = false;

        }

        internal void SetDefaultSnippetsSettings()
        {
            SnippetsIsOn = true;
            SnippetsIsShowTipWindow = true;
            SnippetsWithOtherLayout = true;
            SnippetsIsCaseSensitive = false;
            SnippetsMethodPastByKey = 0;
            SnippetsShowAllShortcut = "shortcutCtrl+Alt+A";
            SnippetsAddNewShortcut = "";
            SnippetsIsEnabledCountUsage = true;
            SnippetsIsEnabledSortingAlphabet = false;
        }

        internal void SetDefaultSmartClickSettings()
        {
            SmartClickIsOn = false;
            SmartClickSearchService = 0;
            SmartClickShowOnPressLeftAndRightMouseButtons = false;
            SmartClickShowOnPressHotKeys = false;
            SmartClickShortcut = "";
            SmartClickShowOnDoubleMiddle = false;
            SmartClickMiniIsOn = false;
            SmartClickMiniPosX = 15;
            SmartClickMiniPosY = 15;
            SmartClickMiniSizeIcon = 15;
            SmartClickCheckedItems = "UniConverter,UniCase,UniLink,UniLinkTranslate,UniLinkShorter,UniTranslate,UniSpellCheck,UniCopy,UniPaste,UniPasteUnf,UniSearch,UniEmail,Unisnippets,UniClipboardHistory,UniDiaryHistory,OcrHeader";
        }

        internal void SetDefaultDiarySettings()
        {
            DiaryIsOn = true;
            DiaryPassword = "";
            MaxDiaryItems = 300;
            DiaryShowShortcut = "shortcutCtrl+Alt+D";
            IsSaveOneWordSentences = false;
        }

        internal void SetDefaultClipboardSettings()
        {
            ClipboardPasteWithoutFormattingShortcutIsOn = true;
            ClipboardHistoryIsOn = true;
            ClipboardShowHistoryShortcut = "shortcutCtrl+Alt+V";
            ClipboardPasteWithoutFormattingShortcut = "shortcutCtrl+Shift+V";
            ClipboardPasteRoundIsOn = true;
            ClipboardPasteRoundShortcut = "shortcutCtrl+Shift+~";
            ClipboardPasteByCtrlPlusIndexIsOn = false;
            ClipboardSaveFilePath = true;
            ClipboardSaveImage = true;
            ClipboardReplaceWithoutChangeClipboard = false;
            ClipboardMaxClipboardItems = 300;
            ClipboardSountIsOn = false;
        }

        internal void SetDefaultLangFlagSettings()
        {
            LangFlagShowForMouse = false;
            LangFlagShowForCaret = true;
            LangFlagShowInTray = true;
            LangFlagShowLargeWindow = false;
            LangFlagShowIcons = true;
            LangFlagPosCarretX = 17;
            LangFlagPosCarretY = 17;
            LangFlagPosMouseX = 15;
            LangFlagPosMouseY = 15;
            LangFlagOpacityIcon = 70;
            LangFlagSizeIcon = 10;
            LangInfoLargeWindowPosX = 0;
            LangInfoLargeWindowPosY = 0;
            //IndicateCurrentLangInKeyboardLed = false;
            LangFlagIsIndicateCapsLockState = true;
            LangFlagIsIndicateNumLockState = false;
            IsHideIndicateInFullScreenApp = true;
        }

        internal void SetDefaultAutoSwitcherSettings()
        {
            AutoSwitcherIsOn = true;
            AutoSwitcherFixTwoUpperCaseLettersInStart = false;
            AutoSwitcherFixWrongUpperCase = false;
            AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters = false;
            AutoSwitcherSwitchTextLangAfterPressEnter = false;
            AutoSwitcherAddRule = true;
            AutoSwitcherCountCheckRule = 2;
            AutoSwitcherOnlyAfterSeparator = false;
            AutoSwitcherAfterPause = true;
            AutoSwitcherNotTrueListOfLang = "";
        }

        internal void SetDefaultSwitcherSettings()
        {
            SwitcherShortcutSwitchLangSelectedTextShortcut = "shortcutCtrl+~";
            SwitcherLeaveTextSelectedAfterSwitch = false;
            SwitcherIsOn = true;
            SwitcherSwitchTextLangShortcut = "doubleRightShift";
            SwitcherSwitchTextLangForAllLineShortcut = "doubleInsert";
            SwitcherSountIsOn = true;
            AutoSwitcherShowAcceptWindow = false;
            AutoSwitcherDisableAutoSwitchAfterManualSwitch = true;
            SwitcherSwitchLangByCtrlPlusNumberIsOn = false;
            SwitcherSwitchLangByNonStandartKey = 0;
            SwitcherSwitchMethod = 0;
            SwitcherNotTrueListOfLang = "";
            SwitcherLangAndKeysForSwitch = "";
        }

        internal void SetDefaultSpellCheckSettings()
        {
            SpellCheckShortcut = "shortcutCtrl+F7";
            SpellCheckCloseByTimer = true;
            SpellCheckIsOn = true;
            SpellCheckWhileTyping = false;
            SpellCheckWhileTypingSoundOn = true;
            SpellCheckWhileTypingUseNumber = false;
        }

        internal void SetDefaultConvertSettings()
        {
            ConverterExpresionShortcut = "";
            ConverterOpenWindowShortcut = "shortcutCtrl+~";
            ConverterShortcutCapsOpenWindow = "shortcutAlt+~";
            ConverterShortcutCapsInvert = "";
            ConverterShortcutCapsUp = "";
            ConverterShortcutCapsDown = "";
            ConverterFirstLetterToDown = "";
            ConverterFirstLetterToUp = "";
            ConverterTransliterationShortcut = "";
            ConverterEncloseTextQuotationMarksShortcut = "";
            ConverterShortcutCamelCase = "";
            ConverterShortcutPascalCase = "";
            ConverterShortcutKebabCase = "";
            ConverterShortcutSnakeCase = "";
            ConverterShortcutReplaceSelText = "";
            ConverterFramesList = "\"‽\"¿(‽)";
        }

        internal void SetDefaultTranslateSettings()
        {
            TranslateOnlyFavoriteLanguages = false;
            TranslateFavoriteLanguages = "";
            TranslateLangFrom = "";
            TranslateLangTo = "";
            TranslateProvider = 0;
            TranslateShowMiniFormAlwaysWhenSelectText = false;
            TranslateShowMiniFormDoubleCtrl = false;
            TranslateShowMiniFormShortcut = "doubleLeftOrRightCtrl";
            TranslateIsOn = true;
            TranslateHistoryIsOn = true;
            TranslateFontFamily = "Segoe";
            TranslateFontSize = "14";
        }

        internal void SetDefaultSoundSettings()
        {
            SoundForLangSwitch = "Suled";
            SoundForSpellCheck = "Honond";
            SoundForClipboard = "Untos";
            SoundVolumeForLangSwitch = 50;
            SoundVolumeForSpellCheck = 50;
            SoundVolumeForClipboard = 50;
        }

        internal void SetDefaultOcrSettings()
        {
            OcrShortcut = "";
            OcrLangsList = new List<string>() { "English" };
        }

        internal ObjectId Id { get; set; }

        internal string? HashFromSite
        {
            get { return _hashFromSite; }
            set
            {
                if (value != null) _hashFromSite = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string? LastVersionForCheckHash
        {
            get { return _lastVersionForCheckHash; }
            set
            {
                if (value != null) _lastVersionForCheckHash = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal bool MainFormMinimizeToTray
        {
            get { return _mainFormMinimizeToTray; }
            set { _mainFormMinimizeToTray = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool CanClose
        {
            get { return _canClose; }
            set { _canClose = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }


        internal string? AppUILang
        {
            get { return _appUILang; }
            set
            {
                if (value != null) _appUILang = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string AppFont
        {
            get { return _appFont; }
            set
            {
                if (value != null) _appFont = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal int AppFontSize
        {
            get { return _appFontSize; }
            set
            {
                if (value != null) _appFontSize = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string AppUIThemeNew
        {
            get
            {
                if (_appUITheme == "BaseLight")
                {
                    return "Light";
                }
                if (_appUITheme == "CustomDark")
                {
                    return "Dark";
                }
                return _appUITheme;
            }
            set
            {
                if (value != null)
                {
                    if (value == "Light")
                    {
                        _appUITheme = "BaseLight";
                    }
                    else if (value == "Dark")
                    {
                        _appUITheme = "CustomDark";
                    }
                    else
                    {
                        _appUITheme = value;
                    }
                }
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string AppUIThemeCurrent { get; set; }

        internal string AppUITheme
        {
            get
            {
                return _appUITheme;
            }
            set
            {
                if (value != null) _appUITheme = value;
            }
        }

        internal string AppUIAccent
        {
            get { return _appUIAccent; }
            set
            {
                if (value != null) _appUIAccent = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string OpenMainWindowShortcut
        {
            get { return _openMainWindowShortcut; }
            set
            {
                if (value != null) _openMainWindowShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string StopWorkingShortcut
        {
            get { return _stopWorkingShortcut; }
            set
            {
                if (value != null) _stopWorkingShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool IsCheckUpdate
        {
            get { return _isCheckUpdate; }
            set { _isCheckUpdate = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool IsCheckUpdateBeta
        {
            get { return _isCheckUpdateBeta; }
            set { _isCheckUpdateBeta = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool IsCheckedStartAsAdmin
        {
            get { return _isCheckedStartAsAdmin; }
            set { _isCheckedStartAsAdmin = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string CurrentVersion
        {
            get { return _currentVersion; }
            set
            {
                if (value != null) _currentVersion = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal DateTime ThemeTimeStartNight
        {
            get { return _themeTimeStartNight; }
            set { _themeTimeStartNight = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal DateTime ThemeTimeEndNight
        {
            get { return _themeTimeEndNight; }
            set { _themeTimeEndNight = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool IsUseNightTheme
        {
            get { return _isUseNightTheme; }
            set { _isUseNightTheme = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string FunctionOrder
        {
            get { return _functionOrder; }
            set
            {
                if (value != null) _functionOrder = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool IsStopWorkingFullScreen
        {
            get { return _isStopWorkingFullScreen; }
            set { _isStopWorkingFullScreen = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ProxyUseIE
        {
            get { return _proxyUseIE; }
            set { _proxyUseIE = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string? ProxyServer
        {
            get { return _proxyServer; }
            set
            {
                if (value != null) _proxyServer = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string? ProxyPort
        {
            get { return _proxyPort; }
            set
            {
                if (value != null) _proxyPort = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string? ProxyUserName
        {
            get { return _proxyUserName; }
            set
            {
                if (value != null) _proxyUserName = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string? ProxyPassword
        {
            get { return _proxyPassword; }
            set
            {
                if (value != null) _proxyPassword = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string LicCode
        {
            get { return _licCode; }
            set
            {
                if (value != null) _licCode = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string LicEmail
        {
            get { return _licEmail; }
            set
            {
                if (value != null) _licEmail = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool LicIsActive
        {
            get { return _licIsActive; }
            set { _licIsActive = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LicIsEvaluate
        {
            get { return _licIsEvaluate; }
            set { _licIsEvaluate = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string LicEvaluateCode
        {
            get { return _licEvaluateCode; }
            set
            {
                if (value != null) _licEvaluateCode = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal DateTime LicEvaluateStart
        {
            get { return _licEvaluateStart; }
            set { _licEvaluateStart = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string ClipboardFastActionWindowSize
        {
            get { return _clipboardFastActionWindowSize; }
            set
            {
                if (value != null) _clipboardFastActionWindowSize = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string DiaryActionWindowSize
        {
            get { return _diaryFastActionWindowSize; }
            set
            {
                if (value != null) _diaryFastActionWindowSize = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SnippetsActionWindowSize
        {
            get { return _snippetsActionWindowSize; }
            set
            {
                if (value != null) _snippetsActionWindowSize = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal string? TranslateLangFrom
        {
            get { return _translateLangFrom; }
            set
            {
                if (value != null) _translateLangFrom = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string? TranslateLangTo
        {
            get { return _translateLangTo; }
            set
            {
                if (value != null) _translateLangTo = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string TranslateFavoriteLanguages
        {
            get { return _translateFavoriteLanguages; }
            set
            {
                if (value != null) _translateFavoriteLanguages = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool TranslateOnlyFavoriteLanguages
        {
            get { return _translateOnlyFavoriteLanguages; }
            set { _translateOnlyFavoriteLanguages = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int TranslateProvider
        {
            get { return _translateProvider; }
            set { _translateProvider = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool TranslateShowMiniFormAlwaysWhenSelectText
        {
            get { return _translateShowMiniFormAlwaysWhenSelectText; }
            set { _translateShowMiniFormAlwaysWhenSelectText = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool TranslateShowMiniFormDoubleCtrl
        {
            get { return _translateShowMiniFormDoubleCtrl; }
            set { _translateShowMiniFormDoubleCtrl = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string TranslateShowMiniFormShortcut
        {
            get { return _translateShowMiniFormShortcut; }
            set
            {
                if (value != null) _translateShowMiniFormShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool TranslateIsOn
        {
            get { return _translateIsOn; }
            set { _translateIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool TranslateHistoryIsOn
        {
            get { return _translateHistoryIsOn; }
            set { _translateHistoryIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string TranslateFontFamily
        {
            get { return _translateFontFamily; }
            set
            {
                if (value != null) _translateFontFamily = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string TranslateFontSize
        {
            get { return _translateFontSize; }
            set
            {
                if (value != null) _translateFontSize = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal string SpellCheckShortcut
        {
            get { return _spellCheckShortcut; }
            set
            {
                if (value != null) _spellCheckShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool SpellCheckCloseByTimer
        {
            get { return _spellCheckCloseByTimer; }
            set { _spellCheckCloseByTimer = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SpellCheckWhileTyping
        {
            get { return _spellCheckWhileTyping; }
            set { _spellCheckWhileTyping = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SpellCheckWhileTypingSoundOn
        {
            get { return _spellCheckWhileTypingSoundOn; }
            set { _spellCheckWhileTypingSoundOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SpellCheckWhileTypingUseNumber
        {
            get { return _spellCheckWhileTypingUseNumber; }
            set { _spellCheckWhileTypingUseNumber = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }


        internal bool SpellCheckIsOn
        {
            get { return _spellCheckIsOn; }
            set { _spellCheckIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }


        internal bool SwitcherIsOn
        {
            get { return _switcherIsOn; }
            set { _switcherIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SwitcherLeaveTextSelectedAfterSwitch
        {
            get { return _switcherLeaveTextSelectedAfterSwitch; }
            set { _switcherLeaveTextSelectedAfterSwitch = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string SwitcherShortcutSwitchLangSelectedTextShortcut
        {
            get { return _switcherShortcutSwitchLangSelectedTextShortcut; }
            set
            {
                if (value != null) _switcherShortcutSwitchLangSelectedTextShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SwitcherSwitchTextLangShortcut
        {
            get { return _switcherSwitchTextLangShortcut; }
            set
            {
                if (value != null) _switcherSwitchTextLangShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SwitcherSwitchTextLangForAllLineShortcut
        {
            get { return _switcherSwitchTextLangForAllLineShortcut; }
            set
            {
                if (value != null) _switcherSwitchTextLangForAllLineShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool SwitcherSountIsOn
        {
            get { return _switcherSountIsOn; }
            set { _switcherSountIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherShowAcceptWindow
        {
            get { return _autoSwitcherShowAcceptWindow; }
            set { _autoSwitcherShowAcceptWindow = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherDisableAutoSwitchAfterManualSwitch
        {
            get { return _autoSwitcherDisableAutoSwitchAfterManualSwitchw; }
            set { _autoSwitcherDisableAutoSwitchAfterManualSwitchw = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SwitcherSwitchLangByCtrlPlusNumberIsOn
        {
            get { return _switcherSwitchLangByCtrlPlusNumberIsOn; }
            set { _switcherSwitchLangByCtrlPlusNumberIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int SwitcherSwitchLangByNonStandartKey
        {
            get { return _switcherSwitchLangByNonStandartKey; }
            set { _switcherSwitchLangByNonStandartKey = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int SwitcherSwitchMethod
        {
            get { return _switcherSwitchMethod; }
            set { _switcherSwitchMethod = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string SwitcherLangAndKeysForSwitch
        {
            get { return _switcherLangAndKeysForSwitch; }
            set
            {
                if (value != null) _switcherLangAndKeysForSwitch = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SwitcherNotTrueListOfLang
        {
            get { return _switcherNotTrueListOfLang; }
            set
            {
                if (value != null) _switcherNotTrueListOfLang = value;
                if (_switcherNotTrueListOfLang.Contains(","))
                {
                    var lanAr = _switcherNotTrueListOfLang.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    _switcherNotTrueListOfLang = "";
                    if (lanAr.Length > 0)
                    {
                        var newArr = new List<string>();
                        foreach (CultureInfo? lang in KeyboardLayoutMethods.GetInputLangs())
                        {
                            foreach (CultureInfo? langT in KeyboardLayoutMethods.GetInputLangs())
                            {
                                if (lang.TwoLetterISOLanguageName != langT.TwoLetterISOLanguageName)
                                {
                                    var name = lang.TwoLetterISOLanguageName.ToUpper() + "\u2192" + langT.TwoLetterISOLanguageName.ToUpper();
                                    newArr.Add(name);
                                }
                            }

                        }
                        foreach (var o in newArr)
                        {
                            bool isCon = false;
                            foreach (var s in lanAr)
                            {
                                if (o.Contains(s.ToUpper()))
                                {
                                    isCon = true;
                                    break;

                                }
                            }
                            if (!isCon)
                            {
                                _switcherNotTrueListOfLang += o + ";";
                            }
                        }

                    }

                }
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal bool AutoSwitcherIsOn
        {
            get { return _autoSwitcherIsOn; }
            set { _autoSwitcherIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string AutoSwitcherNotTrueListOfLang
        {
            get { return _autoSwitcherNotTrueListOfLang; }
            set
            {
                if (value != null) _autoSwitcherNotTrueListOfLang = value;
                if (_autoSwitcherNotTrueListOfLang.Contains(","))
                {
                    var lanAr = _autoSwitcherNotTrueListOfLang.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    _autoSwitcherNotTrueListOfLang = "";
                    if (lanAr.Length > 0)
                    {

                        var newArr = new List<string>();
                        foreach (CultureInfo? lang in KeyboardLayoutMethods.GetInputLangs())
                        {
                            foreach (CultureInfo? langT in KeyboardLayoutMethods.GetInputLangs())
                            {
                                if (lang.TwoLetterISOLanguageName != langT.TwoLetterISOLanguageName)
                                {
                                    var name = lang.TwoLetterISOLanguageName.ToUpper() + "\u2192" + langT.TwoLetterISOLanguageName.ToUpper();
                                    newArr.Add(name);
                                }
                            }
                        }
                        foreach (var o in newArr)
                        {
                            bool isCon = false;
                            foreach (var s in lanAr)
                            {
                                if (o.Contains(s.ToUpper()))
                                {
                                    isCon = true;
                                    break;

                                }
                            }
                            if (!isCon)
                            {
                                _autoSwitcherNotTrueListOfLang += o + ";";
                            }
                        }
                    }
                }
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool AutoSwitcherIsSwitchOneLetter
        {
            get { return _autoSwitcherIsSwitchOneLetter; }
            set { _autoSwitcherIsSwitchOneLetter = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherFixTwoUpperCaseLettersInStart
        {
            get { return _autoSwitcherFixTwoUpperCaseLettersInStart; }
            set { _autoSwitcherFixTwoUpperCaseLettersInStart = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherFixWrongUpperCase
        {
            get { return _autoSwitcherFixWrongUpperCase; }
            set { _autoSwitcherFixWrongUpperCase = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters
        {
            get { return _autoSwitcherNotSwitchTextLangWithAllUpperCaseLetters; }
            set { _autoSwitcherNotSwitchTextLangWithAllUpperCaseLetters = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherSwitchTextLangAfterPressEnter
        {
            get { return _autoSwitcherSwitchTextLangAfterPressEnter; }
            set { _autoSwitcherSwitchTextLangAfterPressEnter = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherAddRule
        {
            get { return _autoSwitcherAddRule; }
            set { _autoSwitcherAddRule = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int AutoSwitcherCountCheckRule
        {
            get { return _autoSwitcherCountCheckRule; }
            set { _autoSwitcherCountCheckRule = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherOnlyAfterSeparator
        {
            get { return _autoSwitcherOnlyAfterSeparator; }
            set { _autoSwitcherOnlyAfterSeparator = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool AutoSwitcherAfterPause
        {
            get { return _autoSwitcherAfterPause; }
            set { _autoSwitcherAfterPause = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagShowForMouse
        {
            get { return _langFlagShowForMouse; }
            set { _langFlagShowForMouse = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagShowForCaret
        {
            get { return _langFlagShowForCaret; }
            set { _langFlagShowForCaret = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagShowInTray
        {
            get { return _langFlagShowInTray; }
            set { _langFlagShowInTray = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagShowLargeWindow
        {
            get { return _langFlagShowLargeWindow; }
            set { _langFlagShowLargeWindow = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagShowIcons
        {
            get { return _langFlagShowIcons; }
            set { _langFlagShowIcons = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagPosCarretX
        {
            get { return _langFlagPosCarretX; }
            set { _langFlagPosCarretX = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagPosCarretY
        {
            get { return _langFlagPosCarretY; }
            set { _langFlagPosCarretY = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagPosMouseX
        {
            get { return _langFlagPosMouseX; }
            set { _langFlagPosMouseX = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagPosMouseY
        {
            get { return _langFlagPosMouseY; }
            set { _langFlagPosMouseY = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagOpacityIcon
        {
            get { return _langFlagOpacityIcon; }
            set { _langFlagOpacityIcon = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int LangFlagSizeIcon
        {
            get { return _langFlagSizeIcon; }
            set { _langFlagSizeIcon = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal double LangInfoLargeWindowPosX
        {
            get { return _langInfoLargeWindowPosX; }
            set { _langInfoLargeWindowPosX = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal double LangInfoLargeWindowPosY
        {
            get { return _langInfoLargeWindowPosY; }
            set { _langInfoLargeWindowPosY = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        //internal bool IndicateCurrentLangInKeyboardLed
        //{
        //    get { return _indicateCurrentLangInKeyboardLed; }
        //    set { _indicateCurrentLangInKeyboardLed = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        //}

        internal bool IsHideIndicateInFullScreenApp
        {
            get { return _isHideIndicateInFullScreenApp; }
            set { _isHideIndicateInFullScreenApp = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagIsIndicateCapsLockState
        {
            get { return _langFlagIsIndicateCapsLockState; }
            set { _langFlagIsIndicateCapsLockState = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool LangFlagIsIndicateNumLockState
        {
            get { return _langFlagIsIndicateNumLockState; }
            set { _langFlagIsIndicateNumLockState = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }


        internal bool ClipboardPasteWithoutFormattingShortcutIsOn
        {
            get { return _clipboardPasteWithoutFormattingShortcutIsOn; }
            set { _clipboardPasteWithoutFormattingShortcutIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ClipboardHistoryIsOn
        {
            get { return _clipboardHistoryIsOn; }
            set { _clipboardHistoryIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string ClipboardShowHistoryShortcut
        {
            get { return _clipboardShowHistoryShortcut; }
            set
            {
                if (value != null) _clipboardShowHistoryShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ClipboardPasteWithoutFormattingShortcut
        {
            get { return _clipboardPasteWithoutFormattingShortcut; }
            set
            {
                if (value != null) _clipboardPasteWithoutFormattingShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool ClipboardPasteRoundIsOn
        {
            get { return _clipboardPasteRoundIsOn; }
            set { _clipboardPasteRoundIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ClipboardReplaceWithoutChangeClipboard
        {
            get { return _clipboardReplaceWithoutChangeClipboard; }
            set { _clipboardReplaceWithoutChangeClipboard = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string ClipboardPasteRoundShortcut
        {
            get { return _clipboardPasteRoundShortcut; }
            set
            {
                if (value != null) _clipboardPasteRoundShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool ClipboardPasteByCtrlPlusIndexIsOn
        {
            get { return _clipboardPasteByCtrlPlusIndexIsOn; }
            set { _clipboardPasteByCtrlPlusIndexIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ClipboardSaveFilePath
        {
            get { return _clipboardSaveFilePath; }
            set { _clipboardSaveFilePath = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ClipboardSaveImage
        {
            get { return _clipboardSaveImage; }
            set { _clipboardSaveImage = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int ClipboardMaxClipboardItems
        {
            get { return _clipboardMaxClipboardItems; }
            set { _clipboardMaxClipboardItems = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool ClipboardSountIsOn
        {
            get { return _clipboardSountIsOn; }
            set { _clipboardSountIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string ConverterOpenWindowShortcut
        {
            get { return _converterOpenWindowShortcut; }
            set
            {
                if (value != null) _converterOpenWindowShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterExpresionShortcut
        {
            get { return _convertExpresionShortcut; }
            set
            {
                if (value != null) _convertExpresionShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutCapsOpenWindow
        {
            get { return _converterShortcutCapsOpenWindow; }
            set
            {
                if (value != null) _converterShortcutCapsOpenWindow = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutCapsInvert
        {
            get { return _converterShortcutCapsInvert; }
            set
            {
                if (value != null) _converterShortcutCapsInvert = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutCapsUp
        {
            get { return _converterShortcutCapsUp; }
            set
            {
                if (value != null) _converterShortcutCapsUp = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutCapsDown
        {
            get { return _converterShortcutCapsDown; }
            set
            {
                if (value != null) _converterShortcutCapsDown = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterFirstLetterToDown
        {
            get { return _converterFirstLetterToDown; }
            set
            {
                if (value != null) _converterFirstLetterToDown = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterFirstLetterToUp
        {
            get { return _converterFirstLetterToUp; }
            set
            {
                if (value != null) _converterFirstLetterToUp = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterTransliterationShortcut
        {
            get { return _convertTransliterationShortcut; }
            set
            {
                if (value != null) _convertTransliterationShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterEncloseTextQuotationMarksShortcut
        {
            get { return _convertEncloseTextQuotationMarksShortcut; }
            set
            {
                if (value != null) _convertEncloseTextQuotationMarksShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutCamelCase
        {
            get { return _converterShortcutCamelCase; }
            set
            {
                if (value != null) _converterShortcutCamelCase = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutPascalCase
        {
            get { return _converterShortcutPascalCase; }
            set
            {
                if (value != null) _converterShortcutPascalCase = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutKebabCase
        {
            get { return _converterShortcutKebabCase; }
            set
            {
                if (value != null) _converterShortcutKebabCase = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutSnakeCase
        {
            get { return _converterShortcutSnakeCase; }
            set
            {
                if (value != null) _converterShortcutSnakeCase = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterShortcutReplaceSelText
        {
            get { return _converterShortcutReplaceSelText; }
            set
            {
                if (value != null) _converterShortcutReplaceSelText = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string ConverterFramesList
        {
            get { return _converterFramesList; }
            set
            {
                if (value != null) _converterFramesList = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool DiaryIsOn
        {
            get { return _diaryIsOn; }
            set { _diaryIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string DiaryPassword
        {
            get { return _diaryPassword; }
            set
            {
                if (value != null) _diaryPassword = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal int MaxDiaryItems
        {
            get { return _maxDiaryItems; }
            set
            {
                _maxDiaryItems = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal bool IsSaveOneWordSentences
        {
            get { return _isSaveOneWordSentences; }
            set { _isSaveOneWordSentences = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string DiaryShowShortcut
        {
            get { return _diaryShowShortcut; }
            set
            {
                if (value != null) _diaryShowShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }


        internal bool SmartClickIsOn
        {
            get { return _smartClickIsOn; }
            set { _smartClickIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int SmartClickSearchService
        {
            get { return _smartClickSearchService; }
            set { _smartClickSearchService = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SmartClickShowOnPressLeftAndRightMouseButtons
        {
            get { return _smartClickShowOnPressLeftAndRightMouseButtons; }
            set { _smartClickShowOnPressLeftAndRightMouseButtons = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SmartClickShowOnPressHotKeys
        {
            get { return _smartClickShowOnPressHotKeys; }
            set { _smartClickShowOnPressHotKeys = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string SmartClickShortcut
        {
            get { return _smartClickShortcut; }
            set
            {
                if (value != null) _smartClickShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool SmartClickShowOnDoubleMiddle
        {
            get { return _smartClickShowOnDoubleMiddle; }
            set { _smartClickShowOnDoubleMiddle = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SmartClickMiniIsOn
        {
            get { return _smartClickMiniIsOn; }
            set { _smartClickMiniIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int SmartClickMiniSizeIcon
        {
            get { return _smartClickMiniSizeIcon; }
            set { _smartClickMiniSizeIcon = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal double SmartClickMiniPosX
        {
            get { return _smartClickMiniPosX; }
            set { _smartClickMiniPosX = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal double SmartClickMiniPosY
        {
            get { return _smartClickMiniPosY; }
            set { _smartClickMiniPosY = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string SmartClickCheckedItems
        {
            get { return _smartClickCheckedItems; }
            set { _smartClickCheckedItems = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SnippetsIsOn
        {
            get { return _snippetsIsOn; }
            set { _snippetsIsOn = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SnippetsIsShowTipWindow
        {
            get { return _snippetsIsShowTipWindow; }
            set { _snippetsIsShowTipWindow = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SnippetsWithOtherLayout
        {
            get { return _snippetsWithOtherLayout; }
            set { _snippetsWithOtherLayout = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal bool SnippetsIsCaseSensitive
        {
            get { return _snippetsIsCaseSensitive; }
            set { _snippetsIsCaseSensitive = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal int SnippetsMethodPastByKey
        {
            get { return _snippetsMethodPastByKey; }
            set { _snippetsMethodPastByKey = value; if (!DontSave) SettingsDataManager.SaveSettings(this); }
        }

        internal string SnippetsShowAllShortcut
        {
            get { return _snippetsShowAllShortcut; }
            set
            {
                if (value != null) _snippetsShowAllShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SnippetsAddNewShortcut
        {
            get { return _snippetsAddNewShortcut; }
            set
            {
                if (value != null) _snippetsAddNewShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool SnippetsIsEnabledCountUsage
        {
            get { return _snippetsIsEnabledCountUsage; }
            set
            {
                if (value != null) _snippetsIsEnabledCountUsage = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal bool SnippetsIsEnabledSortingAlphabet
        {
            get { return _snippetsIsEnabledSortingAlphabet; }
            set
            {
                if (value != null) _snippetsIsEnabledSortingAlphabet = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SoundForLangSwitch
        {
            get { return _soundForLangSwitch; }
            set
            {
                if (value != null) _soundForLangSwitch = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SoundForSpellCheck
        {
            get { return _soundForSpellCheck; }
            set
            {
                if (value != null) _soundForSpellCheck = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string SoundForClipboard
        {
            get { return _soundForClipboard; }
            set
            {
                if (value != null) _soundForClipboard = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal int SoundVolumeForSpellCheck
        {
            get { return _soundVolumeForSpellCheck; }
            set
            {
                _soundVolumeForSpellCheck = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal int SoundVolumeForLangSwitch
        {
            get { return _soundVolumeForLangSwitch; }
            set
            {
                _soundVolumeForLangSwitch = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal int SoundVolumeForClipboard
        {
            get { return _soundVolumeForClipboard; }
            set
            {
                _soundVolumeForClipboard = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string OcrShortcut
        {
            get { return _ocrShortcut; }
            set
            {
                if (value != null) _ocrShortcut = value;
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal List<string> OcrLangsList
        {
            get { return OcrLangsListStr.Split('|').ToList(); }
            set
            {
                if (value != null)
                {
                    OcrLangsListStr = "";
                    foreach (var s in value)
                    {
                        OcrLangsListStr += s + "|";
                    }
                    OcrLangsListStr = OcrLangsListStr.Trim('|');
                }
                if (!DontSave) SettingsDataManager.SaveSettings(this);
            }
        }

        internal string OcrLangsListStr { get; set; }


        internal bool SwitcherSwitchTextLangByBreak { get; set; }
        internal bool SwitcherSwitchTextLangByDoubleShift { get; set; }
        internal bool SwitcherSwitchTextLangByDoubleScrollLock { get; set; }
        internal bool SwitcherSwitchTextLangForAllLineByInsert { get; set; }

    }
}
