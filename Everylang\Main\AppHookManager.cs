﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using NHotkey;
using System;

namespace Everylang.App.Main
{
    internal sealed class AppHookManager : IDisposable
    {
        private static AppHookManager? _instance;

        internal event EventHandler? KeyCombinationPressedMain;

        internal static AppHookManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new AppHookManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.OpenMainWindowShortcut), SettingsManager.Settings.OpenMainWindowShortcut, PressedMain);
            ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.StopWorkingShortcut), SettingsManager.Settings.StopWorkingShortcut, StopWorking);
        }

        internal void StopWorking(object? sender, HotkeyEventArgs hotkeyEventArgs)
        {
            SettingsManager.IsStopWorkingAll = !SettingsManager.IsStopWorkingAll;
            SettingsManager.IsStopWorking = SettingsManager.IsStopWorkingAll;
            GlobalEventsApp.OnEventStopWorking();
        }

        internal void PressedMain(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            KeyCombinationPressedMain?.Invoke(this, EventArgs.Empty);
        }

        internal void Stop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.OpenMainWindowShortcut));
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        public void Dispose()
        {
            Stop();
        }


    }


}