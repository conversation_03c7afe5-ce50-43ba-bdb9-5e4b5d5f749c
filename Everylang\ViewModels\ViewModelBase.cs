﻿using Everylang.App.Annotations;
using Everylang.App.Callback;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Everylang.App.ViewModels
{
    /// <summary>
    /// Base class for all ViewModel classes in the application.
    /// It provides support for property change notifications 
    /// and has a DisplayName property.  This class is abstract.
    /// </summary>
    public abstract class ViewModelBase : INotifyPropertyChanged
    {

        public event PropertyChangedEventHandler? PropertyChanged;

        [NotifyPropertyChangedInvocator]
        protected internal virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            var e = new PropertyChangedEventArgs(propertyName);
            if (e.PropertyName != null) GlobalEventsApp.OnEventPropPro(this, this.GetType().Name, e.PropertyName);
            PropertyChanged?.Invoke(this, e);
        }
    }
}