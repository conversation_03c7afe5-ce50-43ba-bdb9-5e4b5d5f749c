﻿using Everylang.App.Converter;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using Telerik.Windows;
using Telerik.Windows.Controls;
using Vanara.PInvoke;
using Application = System.Windows.Application;
using MousePosition = Everylang.App.Utilities.MousePosition;


namespace Everylang.App.View.Controls.Converter
{
    /// <summary>
    /// Interaction logic for SmartClickWindow.xaml
    /// </summary>
    internal partial class ConverterWindow
    {

        private bool _isShowing;
        private System.Timers.Timer _timer;
        internal string? SourceText { get; set; }

        internal ConverterWindow()
        {
            InitializeComponent();
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;
                //activate the popup
                User32.SetActiveWindow(handle);
            }
        }

        private void MouseOverHide(GlobalMouseEventArgs e)
        {
            if (_isShowing && !IsMouseOver)
            {
                Hide();
                _isShowing = false;
            }
        }

        internal void Hide()
        {
            if (IsOpen)
            {
                if (_timer != null) _timer.Close();
                HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
                HookCallBackMouseDown.CallbackEventHandler -= MouseOverHide;
                IsOpen = false;
            }
        }

        private int _lastSelected;

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (RadialMenuMu.Items.Count == 0)
            {
                Hide();
            }
            _timer.Stop();
            if (e.KeyCode == VirtualKeycodes.Esc)
            {
                Hide();
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.Enter && RadialMenuMu.Items.FirstOrDefault(x => x.IsSelected) is var selItem)
            {
                if (selItem != null)
                {
                    selItem.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, selItem));

                }
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.LeftArrow || e.KeyCode == VirtualKeycodes.UpArrow)
            {
                foreach (var radRadialMenuItem in RadialMenuMu.Items)
                {
                    radRadialMenuItem.IsSelected = false;
                }
                _lastSelected -= 1;
                if (_lastSelected < 0)
                {
                    var item = RadialMenuMu.Items.Last();
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                else
                {
                    var item = RadialMenuMu.Items[_lastSelected];
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                e.Handled = true;
            }
            if (e.KeyCode == VirtualKeycodes.RightArrow || e.KeyCode == VirtualKeycodes.DownArrow)
            {
                foreach (var radRadialMenuItem in RadialMenuMu.Items)
                {
                    radRadialMenuItem.IsSelected = false;
                }
                _lastSelected += 1;
                if (_lastSelected > RadialMenuMu.Items.Count - 1)
                {
                    var item = RadialMenuMu.Items.First();
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }
                else
                {
                    var item = RadialMenuMu.Items[_lastSelected];
                    item.IsSelected = true;
                    _lastSelected = RadialMenuMu.Items.IndexOf(item);
                }

                e.Handled = true;
            }
            //if (_isKeyboard)
            {
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_1 || e.KeyCode == VirtualKeycodes.Numpad_1)
                {
                    if (RadialMenuMu.Items.Count > 0)
                    {
                        var item = RadialMenuMu.Items[0];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_2 || e.KeyCode == VirtualKeycodes.Numpad_2)
                {
                    if (RadialMenuMu.Items.Count > 1)
                    {
                        var item = RadialMenuMu.Items[1];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }

                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_3 || e.KeyCode == VirtualKeycodes.Numpad_3)
                {
                    if (RadialMenuMu.Items.Count > 2)
                    {
                        var item = RadialMenuMu.Items[2];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_4 || e.KeyCode == VirtualKeycodes.Numpad_4)
                {
                    if (RadialMenuMu.Items.Count > 3)
                    {
                        var item = RadialMenuMu.Items[3];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_5 || e.KeyCode == VirtualKeycodes.Numpad_5)
                {
                    if (RadialMenuMu.Items.Count > 4)
                    {
                        var item = RadialMenuMu.Items[4];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_6 || e.KeyCode == VirtualKeycodes.Numpad_6)
                {
                    if (RadialMenuMu.Items.Count > 5)
                    {
                        var item = RadialMenuMu.Items[5];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_7 || e.KeyCode == VirtualKeycodes.Numpad_7)
                {
                    if (RadialMenuMu.Items.Count > 6)
                    {
                        var item = RadialMenuMu.Items[6];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_8 || e.KeyCode == VirtualKeycodes.Numpad_8)
                {
                    if (RadialMenuMu.Items.Count > 7)
                    {
                        var item = RadialMenuMu.Items[7];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.Alphanumeric_9 || e.KeyCode == VirtualKeycodes.Numpad_9)
                {
                    if (RadialMenuMu.Items.Count > 8)
                    {
                        var item = RadialMenuMu.Items[8];
                        item.RaiseEvent(new RadRoutedEventArgs(RadRadialMenuItem.ClickEvent, item));
                        e.Handled = true;
                    }
                }
            }
        }

        internal async void ShowWindow()
        {
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += MouseOverHide;
            Opened += OnOpened;
            _timer = new System.Timers.Timer();
            _timer.Elapsed += (_, _) =>
            {
                if (Application.Current != null && Application.Current.Dispatcher != null)
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)Hide);
            };
            _lastSelected = -1;
            RadialMenuMu.Items.Clear();
            AppendRadRadialMenuItem("UniEnclose");
            AppendRadRadialMenuItem("UniConvertExpressions");
            AppendRadRadialMenuItem("UniTranslit");
            AppendRadRadialMenuItem("ConverterReplaceSelText");
            AppendRadRadialMenuItem("UniCamelCase");
            AppendRadRadialMenuItem("UniSnakeCase");
            AppendRadRadialMenuItem("UniKebabCase");
            AppendRadRadialMenuItem("UniPascalCase");

            IsOpen = true;
            _isShowing = true;
            RadialMenuMu.IsOpen = true;
            await Task.Delay(50);
            var pt = MousePosition.GetMousePoint();
            HorizontalOffset = pt.X - Width / 2;
            VerticalOffset = pt.Y - Height / 2;
            for (int i = 0; i < RadialMenuMu.Items.Count; i++)
            {
                var radRadialMenuItem = RadialMenuMu.Items[i];
                TextBlock? textBlock = FindChild<TextBlock>(radRadialMenuItem, "TextBlockNumber");
                if (textBlock != null)
                {
                    textBlock.Text = "" + (i + 1).ToString();
                    textBlock.Margin = new Thickness(5, 0, 0, 0);
                    textBlock.VerticalAlignment = VerticalAlignment.Center;
                    textBlock.Visibility = Visibility.Visible;
                }
            }
        }

        private RadRadialMenuItem? AppendRadRadialMenuItem(string name)
        {
            var ctrl = FindResource(name) as RadRadialMenuItem;
            if (ctrl != null)
            {
                RadialMenuMu.Items.Add(ctrl);
            }

            return ctrl;
        }



        internal static T? FindChild<T>(DependencyObject? parent, string childName) where T : DependencyObject
        {
            // Confirm parent and childName are valid. 
            if (parent == null) return null;

            T? foundChild = null;

            int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childrenCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                // If the child is not of the request child type child
                T? childType = child as T;
                if (childType == null)
                {
                    // recursively drill down the tree
                    foundChild = FindChild<T>(child, childName);

                    // If the child is found, break so we do not overwrite the found child. 
                    if (foundChild != null) break;
                }
                else if (!string.IsNullOrEmpty(childName))
                {
                    // If the child's name is set for search
                    if (child is FrameworkElement frameworkElement && frameworkElement.Name == childName)
                    {
                        // if the child's name is of the request name
                        foundChild = (T)child;
                        break;
                    }
                }
                else
                {
                    // child element found.
                    foundChild = (T)child;
                    break;
                }
            }

            return foundChild;
        }

        private void ButtonClickConvertExpressions(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterExpresion.ConvertExpresion();
        }

        private void ButtonClickTranslit(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertTransliteration();
        }

        private void ButtonClickEnclose(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertEncloseTextQuotationMarks();
        }
        private void ButtonClickSearchAndReplace(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.RunReplaceInSelTextWindow();
        }

        private void ButtonClickCamelCase(object sender, RoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertCamelCase();
        }

        private void ButtonClickSnakeCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertSnakeCase();
        }

        private void ButtonClickKebabCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertKebabCase();
        }

        private void ButtonClickPascalCase(object sender, RadRoutedEventArgs e)
        {
            Hide();
            ConverterVarious.ConvertPascalCase();
        }

        private async void EventSetter_OnHandler(object sender, RoutedEventArgs e)
        {
            if (_isNavigated)
            {
                _isNavigated = false;
                return;
            }
            await Task.Delay(500);
            Hide();
        }

        private bool _isNavigated;
        private void RadialMenuMu_OnNavigated(object sender, RadRoutedEventArgs e)
        {
            _isNavigated = true;
        }
    }
}
