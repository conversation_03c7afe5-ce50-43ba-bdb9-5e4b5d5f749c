﻿using System;
using System.Threading;

namespace Everylang.App.Utilities
{
    internal class DispatcherHelper
    {
        internal static void RunAsStaThread(Action goForIt)
        {
            AutoResetEvent @event = new AutoResetEvent(false);
            Thread thread = new Thread(
                () =>
                {
                    goForIt();
                    @event.Set();
                });
            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
            @event.WaitOne();
        }

    }
}
