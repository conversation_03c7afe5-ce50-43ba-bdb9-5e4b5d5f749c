﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data;
using Everylang.App.HookManager;
using Everylang.App.LangFlag;
using Everylang.App.Main;
using Everylang.App.Miminote;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.Common.CommonWindow;
using Everylang.App.View.MainControls;
using Everylang.App.ViewModels;
using Everylang.Common.Localization;
using H.NotifyIcon.Core;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Windows.Interop;
using System.Windows.Media;
using Telerik.Windows.Controls;
using WpfScreenHelper;
using WpfScreenHelper.Enum;
using Window = System.Windows.Window;

namespace Everylang.App.View
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow
    {
        bool _isLoaded;

        private TranslatorView _translatorView;
        ClipboardView _clipboardView;
        DiaryView _diaryView;
        NotesView _notesView;
        SnippetsView _snippetsView;
        OcrView _ocrView;
        SettingsView _settingsView;

        public MainWindow()
        {
            RenderOptions.ProcessRenderMode = RenderMode.SoftwareOnly;
            StyleManager.ApplicationTheme = new Windows11Theme();
            LocalizationManager.Manager = new LocalizationManager()
            {
                ResourceManager = LangResource.ResourceManager
            };
            LocalizationManager.DefaultCulture = CultureInfo.CurrentCulture;
            LocalizationManager.UseDynamicLocalization = true;
            if (string.IsNullOrEmpty(SettingsManager.Settings.AppUILang))
            {
                InitLang();
            }
            else
            {
                LocalizationManager.Manager.Culture = new CultureInfo(SettingsManager.Settings.AppUILang);
            }

            InitializeComponent();

            //var dataContext = (SplashScreenDataContext)RadSplashScreenManager.SplashScreenDataContext;
            //dataContext.Content = LocalizationManager.GetString("Loading") + "...";
            //dataContext.Footer = $@"Everylang {Assembly.GetExecutingAssembly().GetName().Version.ToString(3)}";
            //dataContext.ImagePath = "pack://application:,,,/Everylang;component/Resources/logoSmall.png";
            //RadSplashScreenManager.Show();
            AppContol.FirstStart();

            this.WindowStateChanged += WindowStateChangedHandler;
            //GlobalEventsApp.EventMinimizeToTray += MinimizeToTrayHandler;
            GlobalEventsApp.EventUpdateAvailable += UpdateAvailableHandler;
            GlobalEventsApp.EventErrorRegisterHotkey += ErrorRegisterHotkey;
            GlobalEventsApp.EventLangLayoutChanged += LangLayoutChanged;
            GlobalEventsApp.EventCapsLock += CapsLockChanged;
            GlobalEventsApp.EventNumLock += NumLockChanged;

            GlobalEventsApp.EventClipboardView += ClipboardViewOpen;
            GlobalEventsApp.EventOcrMainWindow += OpenOcr;
            GlobalEventsApp.EventSnippetsView += SnippetsViewOpen;
            GlobalEventsApp.EventAddNewSnippetsUpdate += SnippetsUpdate;
            GlobalEventsApp.EventDiaryView += DiaryViewCall;
            GlobalEventsApp.EventDown += Close;

            GlobalEventsApp.EventGoToMainWindowTranslate += GoToMainWindowTranslate;
            GlobalEventsApp.EventStopWorking += EventStopWorking;

            GlobalEventsApp.EventOpenSettingsDiary += OpenSettingsDiary;
            GlobalEventsApp.EventOpenSettingsClipboard += OpenSettingsClipboard;
            GlobalEventsApp.EventOpenSettingsTranslation += OpenSettingsTranslation;

            GlobalEventsApp.EventOpenListDiary += OpenListDiary;
            GlobalEventsApp.EventOpenListClipboard += OpenListClipboard;

            GlobalEventsApp.EventOpenSnippetsList += OpenSnippetsList;
            GlobalEventsApp.EventOpenSnippetsSettings += OpenSnippetsSettings;

            GlobalEventsApp.EventProSendEarlyActive += SendEarlyActive;
            GlobalEventsApp.EventProExp += Exp;

            GlobalEventsApp.EventProStartEv += StartEv;
            GlobalEventsApp.EventProLastDay += LastDay;

            Everylang.Note.Callbacks.CallBack.MiminoteCallBackOpenSettings += NoteOpenSettings;
            Everylang.Note.Callbacks.CallBack.MiminoteCallBackOpenNotesList += NoteOpenNotesList;

            GlobalEventsApp.OnEventStart();

            GlobalEventsApp.EventRestart += Restart;
            AppHookManager.Instance.KeyCombinationPressedMain += KeyCombinationPressedMain;
            GlobalEventsApp.EventPro += _ =>
            {
                NotifyIconMain.ToolTipText = VMContainer.Instance.MainWindowViewModel.TitleName;
            };
            GlobalEventsApp.EventFunctionOrder += SetFunctionOrder;

            SetFunctionOrder();
        }

        private void InitLang()
        {
            var langs = LangResource.ResourceManager.GetResourceSet(CultureInfo.CurrentCulture, true, true);
            var languages = new List<string>()
            {
                "en",
                "ru",
                "uk",
                "es",
                "it",
                "de",
                "fr",
                "cs",
                "pl",
            };
            if (string.IsNullOrEmpty(SettingsManager.Settings.AppUILang))
            {
                var currentCulture = CultureInfo.CurrentCulture.TwoLetterISOLanguageName.ToLower();
                var selectCulture = languages.FirstOrDefault(x => x == currentCulture);
                if (selectCulture != null)
                {
                    if (!string.IsNullOrEmpty(selectCulture))
                    {
                        LocalizationManager.Manager.Culture = new CultureInfo(selectCulture);
                        SettingsManager.Settings.AppUILang = selectCulture;
                    }
                }
                else
                {
                    SettingsManager.Settings.AppUILang = "en";
                    LocalizationManager.Manager.Culture = new CultureInfo("en");
                }
            }
        }

        private void SetFunctionOrder()
        {
            var fList = SettingsManager.Settings.FunctionOrder.Split().ToList();
            var objects = new Dictionary<int, object>();
            RadNavigationViewItem? settingsItem = null;
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() != "-1")
                {
                    objects.Add(fList.IndexOf(((RadNavigationViewItem)item).Tag.ToString()!), item);
                }
                else
                {
                    settingsItem = item as RadNavigationViewItem;
                }
            }
            MuNavigationView.Items.Clear();
            var orderedList = objects.OrderBy(x => x.Key);
            foreach (var keyValuePair in orderedList)
            {
                MuNavigationView.Items.Add(keyValuePair.Value);
            }

            if (settingsItem != null) MuNavigationView.Items.Add(settingsItem);
        }

        private void RadWindow_Loaded(object sender, RoutedEventArgs e)
        {
            this.ParentOfType<Window>().ContentRendered += MainWindowContentRendered;
            Top = -10000;
        }

        private async void MainWindowContentRendered(object? sender, EventArgs e)
        {

            AppContol.Start();

            _settingsView = new SettingsView();
            _clipboardView = new ClipboardView();
            _diaryView = new DiaryView();
            _notesView = new NotesView();
            _snippetsView = new SnippetsView();
            _ocrView = new OcrView();

            //RadSplashScreenManager.Close();

            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.SelectedIndex = 0;

            var currentVersion = Assembly.GetExecutingAssembly().GetName().Version;
            if (SettingsManager.Settings.CurrentVersion != "")
            {
                currentVersion = new Version(SettingsManager.Settings.CurrentVersion);
            }

            SettingsManager.Settings.CurrentVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? string.Empty;
            this.ParentOfType<Window>().Activate();
            WindowState = WindowState.Normal;

            this.ParentOfType<Window>().SetWindowPosition(WindowPositions.Center, WpfScreenHelper.Screen.FromPoint(MousePosition.Position()));

            if (SettingsManager.Settings.MainFormMinimizeToTray) WindowState = WindowState.Minimized;

            try
            {
                if (File.Exists(Path.Combine(Path.GetTempPath(), "EveryLang.exe")))
                {
                    File.Delete(Path.Combine(Path.GetTempPath(), "EveryLang.exe"));
                }
            }
            catch
            {
                // ignore
            }

            _isLoaded = true;

            await Task.Delay(2000);

            var appVersion = Assembly.GetExecutingAssembly().GetName().Version;

            if (appVersion > currentVersion)
            {
                NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("AppUpdated"), NotificationIcon.Info);
            }

            if (SettingsManager.IsOneStart)
            {
                StartUp.CreateStartUp();
                NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("SystemTrayHide"), NotificationIcon.Info);
            }

            if (!SettingsManager.Settings.IsCheckedStartAsAdmin && !SettingsManager.IsAdministrator())
            {
                NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("AppNotAsAdmin"), NotificationIcon.Warning);
            }
            if (KeyboardLayoutMethods.GetInputLangs().Count != InputLanguage.InstalledInputLanguages.Count)
            {
                await Task.Delay(2000);
                NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("InputLanguagesError"), NotificationIcon.Warning);
            }
        }



        private void KeyCombinationPressedMain(object? sender, EventArgs eventArgs)
        {
            if (WindowState != WindowState.Minimized)
            {
                WindowState = WindowState.Minimized;
                return;
            }
            WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
        }

        private void Restart(bool afterError = false)
        {
            try
            {
                CommonHookListener.Stop();
                ClipboardMonitorWorker.Stop();
                LangInfoManager.Stop();
                GlobalShellHook.Stop();
                Updater.Stop();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            if (!afterError)
            {
                System.Windows.Forms.Application.Restart();
            }
            Environment.Exit(0);
        }

        private void StartEv()
        {
            RadWindow.Alert(LocalizationManager.GetString("FirstStartWithEvaluate"));
        }

        private void LastDay()
        {
            NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("ProBlockedExpToDays"), NotificationIcon.Warning);
        }

        private void Exp()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            RadWindow.Alert(LocalizationManager.GetString("ProBlockedExp"));
        }


        private void SendEarlyActive(string text)
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            if (text.ToLower().Contains("blockedbyearlyactivation"))
            {
                RadWindow.Alert(LocalizationManager.GetString("ProBlockedByEarlyActivation") + Environment.NewLine + SettingsManager.LicenseModel?.ErrorConnect);
            }
            if (text.ToLower().Contains("blockedbymonthactivatelimit"))
            {
                RadWindow.Alert(LocalizationManager.GetString("ProBlockedByMonthActivateLimit") + Environment.NewLine + SettingsManager.LicenseModel?.ErrorConnect);
            }
            if (text.ToLower().Contains("licenseblocked"))
            {
                RadWindow.Alert(LocalizationManager.GetString("ProBlocked") + Environment.NewLine + SettingsManager.LicenseModel?.ErrorConnect);
            }

        }

        private void OpenSnippetsList()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "4")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }
        }

        private void OpenSnippetsSettings()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            _settingsView.MuNavigationView.SelectedIndex = 7;
        }

        private void OpenListDiary()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "2")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }
        }

        private void OpenListClipboard()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "1")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }
        }

        private void NoteOpenNotesList()
        {
            WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "3")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }
        }

        private void NoteOpenSettings()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            _settingsView.MuNavigationView.SelectedIndex = 8;
        }

        private void OpenSettingsDiary()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            _settingsView.MuNavigationView.SelectedIndex = 11;
        }

        private void OpenSettingsClipboard()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            _settingsView.MuNavigationView.SelectedIndex = 6;
        }

        private void OpenSettingsTranslation()
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            _settingsView.MuNavigationView.SelectedIndex = 2;
        }

        private void GoToMainWindowTranslate(string s)
        {
            WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "0")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }
            VMContainer.Instance.TranslationMainViewModel.SourceText = s;
            _translatorView.richTextBoxSourceText.Focus();
            VMContainer.Instance.TranslationMainViewModel.CommandTranslate(null);
        }

        private void OpenOcr(Bitmap obj)
        {
            WindowState = WindowState.Normal;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            foreach (var item in MuNavigationView.Items)
            {
                if (item != null && ((RadNavigationViewItem)item).Tag.ToString() == "5")
                {
                    MuNavigationView.SelectedItem = item;
                    break;
                }
            }

            _ocrView?.LoadImage(obj);

        }

        private void SnippetsUpdate()
        {
            _snippetsView.LoadData("");
        }

        private void SnippetsViewOpen()
        {
            FastActionCommonWindow.Instance?.Show(2);
        }

        private void ClipboardViewOpen()
        {
            FastActionCommonWindow.Instance?.Show(0);
        }

        private void DiaryViewCall()
        {
            FastActionCommonWindow.Instance?.Show(1);
        }

        private void CapsLockChanged(StatusCapsLockButton statusCapsLockButton)
        {
            if (statusCapsLockButton == StatusCapsLockButton.CapsLockOff)
            {
                NotifyIconCapsLock.Visibility = Visibility.Hidden;
            }

            if (statusCapsLockButton == StatusCapsLockButton.CapsLockOn)
            {
                NotifyIconCapsLock.Icon = Everylang.App.Resources.Resource.capslock_on;
                NotifyIconCapsLock.Visibility = Visibility.Visible;
            }
        }

        private void NumLockChanged(StatusNumLockButton statusNumLockButton)
        {
            switch (statusNumLockButton)
            {
                case StatusNumLockButton.NumLockOff:
                    NotifyIconNumLock.Visibility = Visibility.Hidden;
                    return;
                case StatusNumLockButton.NumLockStateOff:
                    NotifyIconNumLock.Visibility = Visibility.Visible;
                    NotifyIconNumLock.Icon = Everylang.App.Resources.Resource.numlock_off;
                    NotifyIconNumLock.ToolTipText = LocalizationManager.GetString("StatusButtonNumLockIsOff");
                    return;
                case StatusNumLockButton.NumLockStateOn:
                    NotifyIconNumLock.Visibility = Visibility.Visible;
                    NotifyIconNumLock.Icon = Everylang.App.Resources.Resource.numlock_on;
                    NotifyIconNumLock.ToolTipText = LocalizationManager.GetString("StatusButtonNumLockIsOn");
                    break;
            }
        }

        private void LangLayoutChanged(Icon? icon)
        {
            if (icon == null)
            {

                try
                {
                    NotifyIconMain.Icon = Everylang.App.Resources.Resource.i11;
                    //Uri iconUri = new Uri("pack://application:,,,/EveryLang;component/Resources/i11.ico");
                    //NotifyIconMain.IconSource = new BitmapImage(iconUri);
                }
                catch
                {
                    // ignore
                }
            }
            else
            {
                NotifyIconMain.Icon = icon;
            }
        }

        private void ErrorRegisterHotkey(string obj)
        {
            if (!_isLoaded)
            {
                return;
            }
            NotifyIconMain.ShowNotification("EveryLang", LocalizationManager.GetString("ErrorHotkey"), NotificationIcon.Error);
        }

        private void UpdateAvailableHandler(string version, bool isBeta)
        {
            NotifyIconMain.TrayBalloonTipClicked += NotifyIconMainOnTrayBalloonTipClicked;
            NotifyIconMain.TrayBalloonTipClosed += NotifyIconMainOnTrayBalloonTipClosed;
            var message = LocalizationManager.GetString("AboutSettingsUpdateAvailable") + $" {version}" + Environment.NewLine +
                          LocalizationManager.GetString("AboutSettingsUpdatePressForUpdate");
            NotifyIconMain.ShowNotification("EveryLang", message, NotificationIcon.Info);
        }

        private void NotifyIconMainOnTrayBalloonTipClosed(object sender, RoutedEventArgs e)
        {
            NotifyIconMain.TrayBalloonTipClicked -= NotifyIconMainOnTrayBalloonTipClicked;
            NotifyIconMain.TrayBalloonTipClosed -= NotifyIconMainOnTrayBalloonTipClosed;
        }

        private void NotifyIconMainOnTrayBalloonTipClicked(object sender, RoutedEventArgs e)
        {
            Process.Start("https://everylang.net/download");
        }

        private void WindowStateChangedHandler(object? sender, EventArgs e)
        {
            if (SettingsManager.Settings.MainFormMinimizeToTray)
            {
                var minimized = WindowState == WindowState.Minimized;
                if (minimized)
                {
                    var window = this.ParentOfType<Window>();
                    if (window != null)
                    {
                        window.ShowInTaskbar = false;
                        window.Hide();
                    }

                    //RadWindowInteropHelper.SetShowInTaskbar(this, false);
                }
            }

            if (WindowState == WindowState.Normal || WindowState == WindowState.Maximized)
            {
                var window = this.ParentOfType<Window>();
                if (window != null) window.ShowInTaskbar = true;
                if (MuNavigationView.SelectedIndex == -1)
                {
                    MuNavigationView.SelectedIndex = 0;
                }
                //RadWindowInteropHelper.SetShowInTaskbar(this, true);
            }
        }



        private void NavigationViewOnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (MuNavigationView.SelectedItem == null)
            {
                return;
            }

            _ocrView = new OcrView();
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "0")
            {
                _translatorView = new TranslatorView();
                MuNavigationView.Content = _translatorView;
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("TranslationTab");
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "1")
            {
                MuNavigationView.Content = _clipboardView;
                _clipboardView.SearchTextBox.Text = "";
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("ClipboardTab");
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "2")
            {
                MuNavigationView.Content = _diaryView;
                _diaryView.PasswordBoxMu.Text = "";
                _diaryView.SearchTextBox.Text = "";
                _diaryView.Show();
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("DiareTab");
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "3")
            {
                MuNavigationView.Content = _notesView;
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("NoteTab");
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "4")
            {
                MuNavigationView.Content = _snippetsView;
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("AutochangeTab");
                _snippetsView.LoadItems();
            }
            if (((RadNavigationViewItem)MuNavigationView.SelectedItem).Tag.ToString() == "5")
            {
                MuNavigationView.Content = _ocrView;
                VMContainer.Instance.MainWindowViewModel.CurrentService =
                    LocalizationManager.GetString("OcrTab");
            }

        }

        private void ShowSettingsOnClick(object sender, RoutedEventArgs? e)
        {
            MuNavigationView.SelectedIndex = -1;
            MuNavigationView.Content = _settingsView;
            VMContainer.Instance.MainWindowViewModel.CurrentService =
                LocalizationManager.GetString("Settings");
        }

        private void SettingsTrayOnClick(object sender, RoutedEventArgs routedEventArgs)
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().ShowInTaskbar = true;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
            ShowSettingsOnClick(this, null);
        }

        private void NotifyIcon_OnTrayIconMouseDoubleClick(object sender, RoutedEventArgs routedEventArgs)
        {
            this.WindowState = WindowState.Normal;
            this.ParentOfType<Window>().ShowInTaskbar = true;
            this.ParentOfType<Window>().Show();
            this.ParentOfType<Window>().Activate();
        }

        private void DisableApp(object sender, RoutedEventArgs routedEventArgs)
        {
            SettingsManager.IsStopWorkingAll = !SettingsManager.IsStopWorkingAll;
            SettingsManager.IsStopWorking = SettingsManager.IsStopWorkingAll;
            EventStopWorking();
            VMContainer.Instance.MainWindowViewModel.OnPropertyChanged(nameof(VMContainer.Instance.MainWindowViewModel.IsStopWorking));
        }

        private void EnableApp(object sender, RoutedEventArgs e)
        {
            DisableApp(sender, e);
        }

        private void EventStopWorking()
        {
            if (SettingsManager.IsStopWorking)
            {
                try
                {
                    NotifyIconMain.Icon = Everylang.App.Resources.Resource.i13;
                    // Uri iconUri = new Uri("pack://application:,,,/EveryLang;component/Resources/i13.ico");
                    // NotifyIcon.IconSource = new BitmapImage(iconUri);
                    VMContainer.Instance.MainWindowViewModel.TitleName =
                        LocalizationManager.GetString("WorkingOffTitle");
                    GlobalLangChangeHook.Stop();
                }
                catch (Exception)
                {
                    // ignore
                }
            }
            else
            {
                try
                {
                    VMContainer.Instance.MainWindowViewModel.TitleName = "";
                    NotifyIconMain.Icon = Everylang.App.Resources.Resource.i11;
                    // Uri iconUri = new Uri("pack://application:,,,/EveryLang;component/Resources/i11.ico");
                    // NotifyIcon.IconSource = new BitmapImage(iconUri);
                    GlobalLangChangeHook.Start();
                }
                catch
                {
                    // ignore
                }
            }
        }

        private void ExitTrayOnClick(object sender, EventArgs e)
        {
            this.Close();
        }

        private void MainWindow_OnPreviewClosed(object sender, WindowPreviewClosedEventArgs e)
        {
            RadWindow.Confirm(new DialogParameters()
            {
                Content = LocalizationManager.GetString("CloseQuestion"),
                Closed = (_, args) =>
                {
                    e.Cancel = !args.DialogResult;
                    if (args.DialogResult == true)
                    {
                        try
                        {
                            CommonHookListener.Stop();
                            ClipboardMonitorWorker.Stop();
                            LangInfoManager.Stop();
                            GlobalShellHook.Stop();
                            Updater.Stop();
                            WindowsHelperAccessibilityKeys.AllowAccessibilityShortcutKeys(true);
                            NotifyIconMain.Dispose();
                            NotifyIconCapsLock.Dispose();
                            MiminoteManager.Stop();
                            if (SettingsManager.DbError)
                            {
                                SettingsManager.DbError = false;
                                //SettingsManager.RestoreAllSettings();
                            }

                            DataBaseManager.LiteDb.Dispose();

                        }
                        catch
                        {
                            // ignore
                        }
                    }
                }
            });
        }



    }
}
