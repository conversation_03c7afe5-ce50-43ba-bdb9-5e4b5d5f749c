﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    class ProgramsSetLayoutManager
    {
        internal static IEnumerable<ProgramsSetLayoutDataModel> GetAllData()
        {
            var collection = new List<ProgramsSetLayoutDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsSetLayoutDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        ProgramsSetLayoutDataModel dataModel = new ProgramsSetLayoutDataModel();

                        if (!bsonDocument["_id"].IsNull) dataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["Lang"].IsNull) dataModel.Lang = bsonDocument["Lang"].AsString;
                        if (!bsonDocument["Program"].IsNull) dataModel.Program = bsonDocument["Program"].AsString;
                        if (!bsonDocument["Title"].IsNull) dataModel.Title = bsonDocument["Title"].AsString;
                        collection.Add(dataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return collection;
        }

        internal static void AddData(ProgramsSetLayoutDataModel? programsExceptionsData)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsSetLayoutDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    programsExceptionsData.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = programsExceptionsData.Id;
                    bsonDocument["Lang"] = programsExceptionsData.Lang;
                    bsonDocument["Program"] = programsExceptionsData.Program;
                    bsonDocument["Title"] = programsExceptionsData.Title;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllData()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("ProgramsSetLayoutDataModel"))
                    {
                        db.DropCollection("ProgramsSetLayoutDataModel");
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void UpdateData(ProgramsSetLayoutDataModel? programsExceptionsData)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsSetLayoutDataModel");
                    var bsonDocument = schemelessCollection.FindById(programsExceptionsData.Id);
                    if (bsonDocument != null)
                    {
                        bsonDocument["Lang"] = programsExceptionsData.Lang;
                        bsonDocument["Program"] = programsExceptionsData.Program;
                        bsonDocument["Title"] = programsExceptionsData.Title;
                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelData(ProgramsSetLayoutDataModel? programsExceptionsData)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ProgramsSetLayoutDataModel");
                    if (programsExceptionsData.Id != null) schemelessCollection.Delete(programsExceptionsData.Id);
                    else
                    {
                        if (programsExceptionsData.Title != "")
                        {
                            schemelessCollection.DeleteMany(Query.EQ("Title", programsExceptionsData.Title));
                        }
                        else
                        {
                            schemelessCollection.DeleteMany(Query.EQ("Program", programsExceptionsData.Program));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
