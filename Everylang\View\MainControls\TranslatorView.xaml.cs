﻿using Everylang.App.Clipboard;
using Everylang.App.ViewModels;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Telerik.Windows.Controls;

namespace Everylang.App.View.MainControls
{
    /// <summary>
    /// Interaction logic for TranslatorView.xaml
    /// </summary>
    internal partial class TranslatorView
    {
        internal static readonly DependencyProperty ViewOnlyFavoriteLanguagesProperty =
            DependencyProperty.Register("ViewOnlyFavoriteLanguages",
                typeof(bool),
                typeof(TranslatorView));

        internal bool ViewOnlyFavoriteLanguages
        {
            get { return (bool)GetValue(ViewOnlyFavoriteLanguagesProperty); }
            set { SetValue(ViewOnlyFavoriteLanguagesProperty, value); }
        }

        internal TranslatorView()
        {
            InitializeComponent();
        }

        private void CommandCopyrichTextBoxSourceText_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(richTextBoxSourceText.Selection.Text);

        }

        private void CommandCopyrichTextBoxResultText_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(richTextBoxResultText.Selection.Text);
        }

        private void CommandPasteTextBoxSourceText_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(richTextBoxSourceText.Text))
            {
                richTextBoxSourceText.Text = ClipboardOperations.GetText();
                if (!string.IsNullOrEmpty(richTextBoxSourceText.Text))
                {
                    VMContainer.Instance.TranslationMainViewModel.CommandTranslate(null);
                }
            }
            else
            {
                richTextBoxSourceText.Paste();
            }
        }

        private void CommandCutrichTextBoxSourceText_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(richTextBoxSourceText.Selection.Text);
            richTextBoxSourceText.Selection.Text = "";
        }

        private void CommandCutrichTextBoxResultText_Executed(object sender, ExecutedRoutedEventArgs e)
        {
            ClipboardOperations.SetText(richTextBoxResultText.Selection.Text);
            richTextBoxResultText.Selection.Text = "";
        }

        private void ListBoxHist_OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is RadListBox listBox)
            {
                if (listBox.SelectedValue != null)
                {
                    VMContainer.Instance.TranslationMainViewModel.HistText = (string)listBox.SelectedValue;
                    RadSplitButtonTranslate.IsOpen = false;
                }
            }
        }

        private async void UserControl_IsVisibleChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (IsVisible)
            {
                await Task.Delay(100);
                richTextBoxSourceText.Focus();
            }
        }

        private void RichTextBoxSourceText_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            richTextBoxSourceText.Focus();
        }

        private void DropDownButtonFromOnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            DropDownButtonFrom.IsOpen = false;
            DropDownButtonTo.IsOpen = false;
            ViewOnlyFavoriteLanguages = false;
        }

        private void DropDownButtonFromLoadMoreClick(object sender, RoutedEventArgs e)
        {
            ViewOnlyFavoriteLanguages = !ViewOnlyFavoriteLanguages;
        }

        private void DropDownButtonFrom_OnDropDownClosed(object sender, RoutedEventArgs e)
        {
            ViewOnlyFavoriteLanguages = false;
        }
    }
}
