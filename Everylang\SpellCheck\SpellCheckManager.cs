﻿using Everylang.App.Callback;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using NHotkey;
using System;

namespace Everylang.App.SpellCheck
{
    class SpellCheckManager : IDisposable
    {
        private static SpellCheckManager? _instance;
        internal event Action? KeyCombinationPressedSpellCheck;

        internal static SpellCheckManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SpellCheckManager();
                }
                return _instance;
            }
        }

        internal void Start()
        {
            if (SettingsManager.Settings.SpellCheckIsOn)
            {
                StartSpellCheck();
            }
            KeyCombinationPressedSpellCheck += KeyCombinationPressedSpellCheckHandler;
            GlobalEventsApp.EventSpellCheckForMain += ShowSpellCheckWindow;
        }

        private void ShowSpellCheckWindow(string text, bool isMain)
        {
            SpellCheckShowWorker.ShowSpellCheckWindow(text, isMain);
        }

        private void KeyCombinationPressedSpellCheckHandler()
        {
            SpellCheckShowWorker.ShowSpellAfterKeyPress();
        }

        private void StartSpellCheck()
        {
            if (SettingsManager.Settings.SpellCheckIsOn)
            {
                ShortcutManager.RegisterShortcut(nameof(SettingsManager.Settings.SpellCheckShortcut), SettingsManager.Settings.SpellCheckShortcut, PressedSpellCheck);
            }
        }


        internal void PressedSpellCheck(object? obj, HotkeyEventArgs hotkeyEventArgs)
        {
            KeyCombinationPressedSpellCheck?.Invoke();
        }


        internal void Stop()
        {
            SpellCheckStop();
        }

        internal void Restart()
        {
            Stop();
            Start();
        }

        private void SpellCheckStop()
        {
            ShortcutManager.RemoveShortcut(nameof(SettingsManager.Settings.SpellCheckShortcut));
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
