﻿using Everylang.App.Clipboard;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.Common.LogManager;
using Humanizer;
using org.mariuszgromada.math.mxparser;
using System;
using System.Globalization;

namespace Everylang.App.Converter
{
    internal class ConverterExpresion
    {
        internal static void ConvertExpresion()
        {
            string? text = ClipboardOperations.GetSelectionText();
            if (text?.Trim().Replace("\r\n", "") != "")
            {
                var result = ConvertString(text);
                if (!string.IsNullOrEmpty(result))
                {
                    SendText.SendStringByPaste(result, false);
                }
            }
        }

        static string ConvertString(string? text)
        {
            try
            {
                CultureInfo culture;
                culture = CultureInfo.GetCultureInfo(KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(IntPtr.Zero));
                if (int.TryParse(text, out var dig))
                {
                    return ConvertNumbers(dig, culture);
                }

                if (DateTime.TryParse(text, out var dateTime))
                {
                    return ConvertDateTime(dateTime, culture);
                }
                Expression e = new Expression(text);
                if (e.checkSyntax())
                {
                    return e.calculate().ToString(CultureInfo.CurrentCulture);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            return "";
        }

        private static string ConvertNumbers(int number, CultureInfo cultureInfo)
        {
            return number.ToWords(cultureInfo);
        }

        private static string ConvertDateTime(DateTime dateTime, CultureInfo cultureInfo)
        {
            string dateTimeFormat = cultureInfo.DateTimeFormat.LongDatePattern;
            if (Math.Abs(dateTime.TimeOfDay.TotalSeconds) > 0.0001)
            {
                dateTimeFormat = cultureInfo.DateTimeFormat.FullDateTimePattern;
            }
            string format = GetDatePatternWithoutWeekday(cultureInfo, dateTimeFormat);
            string res = dateTime.ToString(format, cultureInfo);
            return res;
        }

        private static string GetDatePatternWithoutWeekday(CultureInfo cultureInfo, string longPattern)
        {
            string[] patterns = cultureInfo.DateTimeFormat.GetAllDateTimePatterns();

            string acceptablePattern = String.Empty;

            foreach (string pattern in patterns)
            {
                if (longPattern.Contains(pattern) && !pattern.Contains("ddd") && !pattern.Contains("dddd"))
                {
                    if (pattern.Length > acceptablePattern.Length)
                    {
                        acceptablePattern = pattern;
                    }
                }
            }

            if (String.IsNullOrEmpty(acceptablePattern))
            {
                return longPattern;
            }
            return acceptablePattern;
        }


    }
}
