﻿using System;
using System.IO;

namespace Everylang.App.OCR.OcrEngine
{
    internal class TemporaryFile : IDisposable
    {
        private FileInfo ThisFile { get; }

        internal string FullPath => ThisFile.FullName;

        internal TemporaryFile()
        {
            ThisFile = new FileInfo(Path.GetTempFileName());
        }

        internal FileStream OpenReadStream() => ThisFile.OpenRead();

        internal FileStream OpenWriteStream() => ThisFile.OpenWrite();

        public void Dispose()
        {
            if (ThisFile?.Exists ?? false) ThisFile.Delete();
        }
    }
}