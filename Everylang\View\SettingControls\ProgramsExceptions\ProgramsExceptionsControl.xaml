﻿<UserControl x:Class="Everylang.App.View.SettingControls.ProgramsExceptions.ProgramsExceptionsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
             mc:Ignorable="d" x:ClassModifier="internal"
             DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <Style TargetType="TextBlock" x:Key="TextBlockStyle">
            <Style.Triggers>
                <DataTrigger Binding="{Binding ProgramsExceptionsViewModel.AddNewNotStarted}" Value="False">
                    <Setter Property="Visibility" Value="Visible" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ProgramsExceptionsViewModel.AddNewNotStarted}" Value="True">
                    <Setter Property="Visibility" Value="Hidden" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <telerik:RadButton IsBackgroundVisible="False" Focusable="False" Grid.ZIndex="1" Padding="10" MinHeight="0"
                           HorizontalAlignment="Right" VerticalAlignment="Top" Margin="2" Click="HelpOpenClick"
                           CornerRadius="2,2,2,2">
            <wpf:MaterialIcon Width="15" Height="15" Kind="Help" />
        </telerik:RadButton>

        <StackPanel Grid.Row="0"  Margin="20,10,0,0" >
            <TextBlock  FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=ProgramsExceptionsHeader}" />

            <TextBlock Margin="0,3,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsProgramsList}" />
            <telerik:RadComboBox BorderBrush="{telerik:Windows11Resource ResourceKey=StrokeBrush}"  Focusable="False" Margin="0,3,20,0"  MaxDropDownHeight="300" HorizontalAlignment="Stretch" ItemsSource="{Binding Path=ProgramsExceptionsViewModel.ProgramsList, IsAsync=True}" SelectedItem="{Binding Path=ProgramsExceptionsViewModel.ProgramsListCurrent}">
                <telerik:RadComboBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Image Width="18" Height="18" Stretch="Fill" Source="{Binding Icon}" />
                            <TextBlock Margin="10,0,0,0" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="13" Text="{Binding Name}" />
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadComboBox.ItemTemplate>
                <telerik:RadComboBox.ItemContainerStyle>
                    <Style TargetType="telerik:RadComboBoxItem" BasedOn="{StaticResource RadComboBoxItemStyle}">
                        <Setter Property="Padding" Value="3" />
                        <Setter Property="Margin" Value="0" />
                        <Setter Property="MinHeight" Value="0" />
                        <Setter Property="Height" Value="25" />
                    </Style>
                </telerik:RadComboBox.ItemContainerStyle>
            </telerik:RadComboBox>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsExceptionsViewModel.IsProgramsListCurrentSelected}"  Margin="0,0,0,0" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNew}" Command="{Binding Path=ProgramsExceptionsViewModel.AddNewFromListCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewExeFile}" Command="{Binding Path=ProgramsExceptionsViewModel.AddNewExeFileCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewFilesFromFolder}" Command="{Binding Path=ProgramsExceptionsViewModel.AddNewFilesFromFolderCommand}" />
            </StackPanel>
            <TextBlock Margin="0,5,0,0" FontSize="14" Text="{telerik:LocalizableResource Key=ProgramsExceptionsFromPoint}" />
            <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsExceptionsViewModel.AddNewNotStarted}"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNew}" Command="{Binding Path=ProgramsExceptionsViewModel.AddNewCommand}" />
                <telerik:RadButton Focusable="False" IsEnabled="{Binding Path=ProgramsExceptionsViewModel.AddNewNotStarted}" Margin="10,0,0,0"  HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewTitle}" Command="{Binding Path=ProgramsExceptionsViewModel.AddNewTitleCommand}" />
                <TextBlock Style="{DynamicResource ResourceKey=TextBlockStyle}" VerticalAlignment="Center" Margin="5,0,0,0" FontSize="13" Text="{telerik:LocalizableResource Key=ProgramsExceptionsAddNewHelp}" />
            </StackPanel>
        </StackPanel>
        <telerik:RadListBox Focusable="False" Grid.Row="1" Margin="20,3,20,0" HorizontalAlignment="Stretch"
                            ItemsSource="{Binding ProgramsExceptionsViewModel.ProgramsExceptionsList}"
                            SelectedItem="{Binding ProgramsExceptionsViewModel.SelectedProgram, Mode=TwoWay}">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="{Binding ProgramTitle}" MinHeight="0" TextTrimming="CharacterEllipsis"
                                   MaxWidth="430" ToolTip="{Binding ProgramTitle}" HorizontalAlignment="Left" FontSize="13"
                                   VerticalAlignment="Center" />
                        <telerik:RadDropDownButton Grid.Column="1"
                                                   MinHeight="0"
                                                   Padding="5,6,0,6"
                                                   Margin="3,0,0,0"
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Right"
                                                   Focusable="False"
                                                   ToolTip="{telerik:LocalizableResource Key=ProgramsExceptionsCurrentInfoTooltip}"
                                                   Content="{telerik:LocalizableResource Key=ProgramsExceptionsCurrentInfo}"
                                                   CloseOnPopupMouseLeftButtonUp="False"
                                                   CloseOnEscape="True"
                                                   DropDownClosed="RadDropDownButton_OnDropDownClosed">
                            <telerik:RadDropDownButton.DropDownContent>
                                <StackPanel Orientation="Vertical" Margin="0,0,0,0">
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnAutoSwitch}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnAutoSwitch}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnLayoutSwitcher}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnLayoutSwitcher}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnDiary}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnDiary}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnConverter}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnConverter}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnHotKeys}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnHotKeys}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnClipboard}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnClipboard}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnClipboardImage}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnClipboardImage}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnLayoutFlag}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnLayoutFlag}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnSmartClick}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnSmartClick}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnAutochange}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=ProgramsExceptionsIsOnAutochange}" />
                                    </CheckBox>
                                    <CheckBox MinHeight="0" Focusable="False" FontSize="14" 
                                              IsChecked="{Binding Path=IsOnSpellCheckWhileTyping}">
                                        <TextBlock FontSize="13"
                                                   Text="{telerik:LocalizableResource Key=SpellcheckingSettingsWhileTyping}" />
                                    </CheckBox>
                                </StackPanel>
                            </telerik:RadDropDownButton.DropDownContent>
                        </telerik:RadDropDownButton>
                    </Grid>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
        </telerik:RadListBox>
        <StackPanel Grid.Row="2" Margin="20,0,0,10" >
            <StackPanel Orientation="Horizontal" Margin="0,3,0,0" IsEnabled="{Binding Path=ProgramsExceptionsViewModel.IsSelected}">
                <telerik:RadButton Focusable="False" Width="100" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=ProgramsExceptionsDelete}" Command="{Binding Path=ProgramsExceptionsViewModel.DeleteSelectionCommand}" />
                <telerik:RadButton Focusable="False" Margin="10,0,0,0" Width="100" HorizontalAlignment="Left" Content="{telerik:LocalizableResource Key=Edit}" Command="{Binding Path=ProgramsExceptionsViewModel.EditSelectionCommand}" />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
