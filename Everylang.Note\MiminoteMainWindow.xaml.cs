﻿using Everylang.Note.Callbacks;
using Everylang.Note.NoteDataStore;
using Everylang.Note.SettingsApp;
using LiteDB;
using MaterialDesignColors;
using Microsoft.WindowsAPICodePack.Taskbar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Interop;
using System.Windows.Media.Imaging;

namespace Everylang.Note
{
    /// <summary>
    /// Interaction logic for MiminoteMainWindow.xaml
    /// </summary>
    public partial class MiminoteMainWindow : Window
    {
        private readonly bool _isNotClose;
        private NotifyIcon? _notifyIcon;


        public MiminoteMainWindow(bool isNotClose)
        {
            _isNotClose = isNotClose;
            InitializeComponent();
            CallBack.MiminoteCallBackAddNewNote += AddNewNote;
            CallBack.MiminoteCallBackCloseNote += CloseNote;
            CallBack.MiminoteCallBackToArchiveNote += ToArchiveNote;
        }

        private void MainWindow_OnLoaded(object sender, RoutedEventArgs e)
        {
            Left = -50000;
            Top = -50000;

            DeleteAllDeleted();

            LoadAllNotes();


            if (TaskbarManager.IsPlatformSupported)
            {
                TaskbarManager.Instance.SetApplicationIdForSpecificWindow(
                    new WindowInteropHelper(this).Handle, "Everynote");
            }

            Icon = new BitmapImage(new Uri("pack://application:,,,/Everylang.Note;component/Resources/favicon.ico"));
            ImageX.Source = new BitmapImage(new Uri("pack://application:,,,/Everylang.Note;component/Resources/taskbar.png"));

        }

        private void DeleteAllDeleted()
        {
            var deletedNotes = new List<NoteDataModel>();
            foreach (var noteDataModel in NoteDataManager.NotesList)
            {
                if (noteDataModel.IsDeleted)
                {
                    NoteDataManager.DelData(noteDataModel);
                    deletedNotes.Add(noteDataModel);
                }
            }

            if (deletedNotes.Any())
            {
                foreach (var noteDataModel in deletedNotes)
                {
                    NoteDataManager.NotesList.Remove(noteDataModel);
                }
            }
        }

        private void LoadAllNotes()
        {
            SettingsMiminoteManager.OwnerWindow = this;
            foreach (var noteDataModel in NoteDataManager.NotesList)
            {
                if (noteDataModel.IsVisible) noteDataModel.Show();
            }
            if (NoteDataManager.NotesList.Count == 0 && !_isNotClose)
            {
                Close();
            }
        }

        private void ToArchiveNote(NoteDataModel? note)
        {
            if (note != null)
            {
                note.IsArchived = true;
                NoteDataManager.UpdateData(note);
            }
        }

        private void CloseNote(NoteDataModel? note)
        {
            if (note == null) return;
            if (!note.IsDeleted)
            {
                note.IsDeleted = true;
                NoteDataManager.DelData(note);
                NoteDataManager.NotesList.Remove(note);
            }
            if (!NoteDataManager.NotesList.Any(x => x != null && x.IsVisible))
            {
                Close();
            }
        }

        readonly Random _random = new();

        private void AddNewNote()
        {
            var screenWidth = Screen.FromPoint(System.Windows.Forms.Cursor.Position).WorkingArea.Width;
            var screenHeight = Screen.FromPoint(System.Windows.Forms.Cursor.Position).WorkingArea.Height;
            bool back = false;
            var lastCallNote = NoteDataManager.NotesList.FirstOrDefault(x => x != null && x.IsCallNewNote);
            var lastNote = NoteDataManager.NotesList.LastOrDefault(x => x != null && x.IsVisible);

            NoteDataModel noteDataModel = new NoteDataModel();
            noteDataModel.Id = ObjectId.NewObjectId();
            noteDataModel.Color = "";
            noteDataModel.Text = "";
            noteDataModel.NoteName = "";
            noteDataModel.Width = 250;
            noteDataModel.Height = 250;
            noteDataModel.TextSize = (int)SettingsMiminoteManager.AppSettings.SizeFontDefault;
            var colors = new SwatchesProvider().Swatches.ToList();
            noteDataModel.Color = colors[_random.Next(0, 18)].Name;
            if (lastCallNote != null && lastCallNote.NoteForm != null)
            {
                noteDataModel.LocationY = (int)(lastCallNote.NoteForm.Top);
                noteDataModel.LocationX = (int)(lastCallNote.NoteForm.Left + lastCallNote.NoteForm.Width + 10);
                if (screenWidth < noteDataModel.LocationX + noteDataModel.Width * 3)
                {
                    noteDataModel.LocationX = (int)(lastCallNote.NoteForm.Left - noteDataModel.Width - 30);
                    back = true;
                }

            }
            else
            {
                if (lastNote == null || lastNote.NoteForm == null)
                {

                    noteDataModel.LocationX = screenWidth / 2 - 100;
                    noteDataModel.LocationY = screenHeight / 2 - 100;
                }
                else
                {
                    noteDataModel.LocationY = (int)lastNote.NoteForm.Top;
                    noteDataModel.LocationX = (int)(lastNote.NoteForm.Left + lastNote.NoteForm.Width + 10);
                    if (screenWidth < noteDataModel.LocationX + noteDataModel.Width * 3)
                    {
                        noteDataModel.LocationX = (int)(lastNote.NoteForm.Left - noteDataModel.Width - 30);
                        back = true;
                    }

                }
            }

            SetCascade(noteDataModel, back, screenHeight / 2 < noteDataModel.LocationY);
            noteDataModel.IsLoaded = true;
            noteDataModel.Show();
            NoteDataManager.NotesList.Add(noteDataModel);
            NoteDataManager.AddNewData(noteDataModel);

        }

        public void ShowAll()
        {
            NoteDataManager.ShowAllNotes();
        }

        private void SetCascade(NoteDataModel noteDataModel, bool back, bool down)
        {
            var lastLocationCallNote = NoteDataManager.NotesList.FirstOrDefault(x => x != null && x.LocationX == noteDataModel.LocationX && x.LocationY == noteDataModel.LocationY);
            if (lastLocationCallNote != null)
            {

                var screenWidth = Screen.FromPoint(System.Windows.Forms.Cursor.Position).WorkingArea.Width;
                var screenHeight = Screen.FromPoint(System.Windows.Forms.Cursor.Position).WorkingArea.Height;
                if (back)
                {
                    noteDataModel.LocationX -= 30;
                }
                else
                {
                    noteDataModel.LocationX += 30;
                }
                if (down)
                {
                    noteDataModel.LocationY -= 30;
                }
                else
                {
                    noteDataModel.LocationY += 30;
                }


                if (screenWidth < noteDataModel.LocationX + noteDataModel.Width)
                {
                    back = true;
                    noteDataModel.LocationX -= 60;
                }
                if (screenHeight < noteDataModel.LocationY + noteDataModel.Height)
                {
                    back = true;
                    noteDataModel.LocationY -= 60;
                }
                if (noteDataModel.LocationX < 0)
                {
                    back = false;
                    noteDataModel.LocationX += 60;
                }
                if (noteDataModel.LocationY < 0)
                {
                    back = false;
                    noteDataModel.LocationY += 60;
                }
                if (NoteDataManager.NotesList.FirstOrDefault(x => x != null && x.LocationX == noteDataModel.LocationX && x.LocationY == noteDataModel.LocationY) != null)
                {
                    SetCascade(noteDataModel, back, down);
                }
            }

        }

        private void MainWindow_OnClosing(object sender, CancelEventArgs e)
        {
            CallBack.MiminoteCallBackAddNewNote -= AddNewNote;
            CallBack.MiminoteCallBackCloseNote -= CloseNote;
            CallBack.MiminoteCallBackToArchiveNote -= ToArchiveNote;
            if (_notifyIcon != null) _notifyIcon.Dispose();
            foreach (var noteDataModel in NoteDataManager.NotesList)
            {
                if (noteDataModel != null && noteDataModel.IsVisible) noteDataModel.CloseForm();
            }
        }


    }
}
