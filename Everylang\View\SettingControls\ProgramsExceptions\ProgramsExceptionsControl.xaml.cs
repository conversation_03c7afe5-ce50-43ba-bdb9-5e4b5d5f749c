﻿using Everylang.App.Data.DataModel;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.ProgramsExceptions
{
    /// <summary>
    /// Interaction logic for ProgramsExceptionsControl.xaml
    /// </summary>
    internal partial class ProgramsExceptionsControl
    {
        internal ProgramsExceptionsControl()
        {
            InitializeComponent();
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void RadDropDownButton_OnDropDownClosed(object sender, RoutedEventArgs e)
        {
            RadDropDownButton dropDownButton = (RadDropDownButton)sender;
            VMContainer.Instance.ProgramsExceptionsViewModel.SelectedProgram = (ProgramsExceptionsDataModel)dropDownButton.DataContext;
            VMContainer.Instance.ProgramsExceptionsViewModel.UpdateSelectedProgramInDb();
        }
    }
}
