﻿using Everylang.App.Utilities.Automation.UIA3;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.EventHandlers;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading;


namespace Everylang.App.HookManager
{
    internal class GlobalFocusChanged
    {
        internal event Action<Rectangle, bool, bool>? FocusChangedEvent;

        private Action<AutomationElement>? _focusChangedAction;

        private FocusChangedEventHandlerBase? _focusChangedEventHandlerBase;

        private bool _isPassword;
        private bool _isEditControl;
        private Rectangle _controlRect;

        internal void Start(CancellationToken token)
        {
            _focusChangedAction = FocusChanged;
            _focusChangedEventHandlerBase = AutomationUi.Uia3Automation.RegisterFocusChangedEvent(_focusChangedAction);
            while (!token.IsCancellationRequested)
            {
                Thread.Sleep(100);
            }
            AutomationUi.Uia3Automation.UnregisterFocusChangedEvent(_focusChangedEventHandlerBase);
        }

        private int _currentFocusedElementHash;

        private void FocusChanged(AutomationElement automationElement)
        {
            try
            {
                var hashCode = automationElement.GetHashCode();
                if (hashCode != _currentFocusedElementHash)
                {
                    _currentFocusedElementHash = hashCode;
                    if (Equals(automationElement.ControlType, FlaUI.Core.Definitions.ControlType.Edit))
                    {
                        _controlRect = automationElement.BoundingRectangle;
                        _isEditControl = true;
                    }
                    else
                    {
                        _isEditControl = false;
                    }
                    _isPassword = automationElement.Properties.IsPassword;
                    //Logger.Debug.Log($"FocusChanged IsPassword={_isPassword}");
                    FocusChangedEvent?.Invoke(_controlRect, _isEditControl, _isPassword);

                }
            }
            catch (COMException)
            {
            }
            catch (Win32Exception)
            {
            }
            catch
            {
                // ignored
            }
        }
    }
}
