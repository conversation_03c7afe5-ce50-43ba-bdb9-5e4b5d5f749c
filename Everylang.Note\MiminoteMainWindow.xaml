﻿<Window x:Class="Everylang.Note.MiminoteMainWindow" 
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Left="0" 
        Top="0" 
        Width="200" 
        Height="200" 
        Title="Everynote" 
        WindowStyle="None" 
        ResizeMode="CanMinimize" 
        WindowState="Normal" 
        AllowsTransparency="True" 
        ShowInTaskbar="True" 
        Background="{x:Null}" 
        Icon="Resources/favicon.ico"
        Loaded="MainWindow_OnLoaded" 
        Closing="MainWindow_OnClosing">
    <Grid>
        <Image Name="ImageX" Source="Resources/taskbar.png" Width="200" Height="200"></Image>
    </Grid>
</Window>