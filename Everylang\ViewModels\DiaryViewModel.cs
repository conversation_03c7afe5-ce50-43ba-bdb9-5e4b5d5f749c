﻿using Everylang.App.Callback;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;

namespace Everylang.App.ViewModels
{
    public class DiaryViewModel : ViewModelBase
    {
        public RadObservableCollection<DiaryDataModel> AllDiaryItems { get; set; }

        internal List<DiaryDataModel> SelectedItems { get; set; }

        public DelegateCommand DeleteSelectedCommand
        {
            get;
            private set;
        }

        public DelegateCommand ClearAllCommand
        {
            get;
            private set;
        }
        public DiaryViewModel()
        {
            DeleteSelectedCommand = new DelegateCommand(DeleteSelected);
            ClearAllCommand = new DelegateCommand(ClearAll);
            AllDiaryItems = new RadObservableCollection<DiaryDataModel>();
            SelectedItems = new List<DiaryDataModel>();
            GlobalEventsApp.EventAddDiary += AddNewDiaryData;
            GetAllDiaryDataFromDb();
        }

        public string DiaryShortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.DiaryShowShortcut);
            }
            set { SettingsManager.Settings.DiaryShowShortcut = value; }
        }

        internal void ClearAll(object? o)
        {
            AllDiaryItems.Clear();
            DiaryManager.ClearAllDiaryData();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }

        internal void DeleteSelected(object? o)
        {
            var listForRem = new List<DiaryDataModel>();
            for (int i = 0; i < SelectedItems.Count; i++)
            {
                listForRem.Add(SelectedItems[i]);
            }
            DiaryManager.DelDiaryData(listForRem);
            foreach (var item in listForRem)
            {
                AllDiaryItems.Remove(item);
            }
            SelectedItems.Clear();
            base.OnPropertyChanged(nameof(IsSelectedNotNull));
            base.OnPropertyChanged(nameof(IsNotNullAll));
        }

        internal DateTime LastPasswordRequest { get; set; }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsEnabled));
            }
        }

        public bool IsEnabled
        {
            get
            {
                return jgebhdhs && SettingsManager.Settings.DiaryIsOn;
            }
            set
            {
                if (!_isPro) return;
                SettingsManager.Settings.DiaryIsOn = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsSaveOneWordSentences
        {
            get
            {
                return SettingsManager.Settings.IsSaveOneWordSentences;
            }
            set
            {
                SettingsManager.Settings.IsSaveOneWordSentences = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsSelectedNotNull
        {
            get
            {
                return SelectedItems.Count != 0;
            }
        }

        public bool IsNotNullAll
        {
            get { return AllDiaryItems.Count != 0; }
        }

        public int MaxDiaryItems
        {
            get
            {
                if (SettingsManager.Settings.MaxDiaryItems == 0)
                {
                    SettingsManager.Settings.MaxDiaryItems = 600;
                }
                return SettingsManager.Settings.MaxDiaryItems;
            }
            set
            {
                if (value == 0)
                {
                    SettingsManager.Settings.MaxDiaryItems = 600;
                }
                else
                {
                    SettingsManager.Settings.MaxDiaryItems = value;
                }

                base.OnPropertyChanged();
            }
        }

        private void GetAllDiaryDataFromDb()
        {
            var collection = DiaryManager.GetAllDiaryData();
            AllDiaryItems.Clear();
            AllDiaryItems.AddRange(collection.OrderBy(x => x.DateTime).Reverse());
        }

        private void AddNewDiaryData(string text)
        {
            if (IsEnabled)
            {
                text = text.Trim();
                if (!SettingsManager.Settings.IsSaveOneWordSentences && !text.Contains(" "))
                {
                    return;
                }
                if (!string.IsNullOrEmpty(text) && IsContainsNotLetters(text))
                {

                    if (CheckActiveProcessFileName.CheckDiary())
                    {
                        var shortText = text.Length > 150 ? text.Substring(0, 150) + "......" : text;
                        var lastActiveProcess = CheckActiveProcessFileName.GetActiveProcessName();
                        try
                        {
                            lastActiveProcess = Path.GetFileName(lastActiveProcess);
                        }
                        catch
                        {
                            // ignored
                        }
                        AddDiaryData(new DiaryDataModel() { DateText = DateTime.Now.Date.ToString("D"), Text = text, ShortText = shortText, Application = lastActiveProcess, DateTime = DateTime.Now });
                    }
                }
            }
        }

        internal void SaveAllToDb()
        {
            DiaryManager.SaveAllData(AllDiaryItems);
        }

        private static bool IsContainsNotLetters(string text)
        {
            bool result = false;
            foreach (var t in text)
            {
                if (char.IsLetter(t))
                {
                    result = true;
                }
            }
            return result;
        }

        private void AddDiaryData(DiaryDataModel diaryDataModel)
        {
            Task.Run(async () =>
            {
                try
                {
                    var dataModelEmpy =
                        AllDiaryItems.FirstOrDefault(x => x.ShortText == null || x.Text == null || x.DateText == null);
                    if (dataModelEmpy != null)
                    {
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            new Action(() => { AllDiaryItems.Remove(dataModelEmpy); }));
                    }

                    var diaryDataModelOld = AllDiaryItems.FirstOrDefault(x => String.Equals(x?.Text?.Trim(),
                        diaryDataModel?.Text?.Trim(), StringComparison.CurrentCultureIgnoreCase));

                    if (diaryDataModelOld != null)
                    {
                        DiaryManager.DelDiaryData(new List<DiaryDataModel>() { diaryDataModelOld });
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                            new Action(() => { AllDiaryItems.Remove(diaryDataModelOld); }));

                    }

                    if (diaryDataModel.ShortText == null) return;
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal,
                        new Action(() => { AllDiaryItems.Insert(0, diaryDataModel); }));

                    DiaryManager.AddDiaryData(diaryDataModel);

                    if (AllDiaryItems.Count > SettingsManager.Settings.MaxDiaryItems)
                    {
                        var listForRem = AllDiaryItems.Skip(SettingsManager.Settings.MaxDiaryItems).ToList();
                        DiaryManager.DelDiaryData(listForRem);
                        await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        {
                            AllDiaryItems.RemoveRange(listForRem);
                        }));
                    }

                    base.OnPropertyChanged(nameof(IsSelectedNotNull));
                    base.OnPropertyChanged(nameof(IsNotNullAll));
                }
                catch
                {
                    // ignored
                }
            });
        }
    }
}
