﻿<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Everylang.App.View.MainControls.DiaryView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    xmlns:dataModel="clr-namespace:Everylang.App.Data.DataModel"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">

    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}" IsEnabled="{Binding Path=DiaryViewModel.jgebhdhs}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200*" />
        </Grid.RowDefinitions>
        <WrapPanel
            Grid.Row="0"
            HorizontalAlignment="Left"
            Margin="5"
            Orientation="Horizontal"
            VerticalAlignment="Top">
            <telerik:RadAutoSuggestBox
                ClearButtonVisibility="Auto"
                WatermarkContent="{telerik:LocalizableResource Key=SearchHelperText}"
                Name="SearchTextBox"
                TextChanged="TextBoxBase_OnTextChanged"
                Width="250" />
            <Button
                Command="{Binding Path=DiaryViewModel.DeleteSelectedCommand}"
                Content="{telerik:LocalizableResource Key=HistoryDelSelected}"
                IsEnabled="{Binding DiaryViewModel.IsSelectedNotNull}"
                Margin="10,0,0,0"
                ToolTip="{telerik:LocalizableResource Key=HistoryDelSelected}" />
            <Button
                Command="{Binding Path=DiaryViewModel.ClearAllCommand}"
                Content="{telerik:LocalizableResource Key=HistoryClear}"
                IsEnabled="{Binding DiaryViewModel.IsNotNullAll}"
                Margin="10,0,0,0"
                ToolTip="{telerik:LocalizableResource Key=HistoryClear}" />
        </WrapPanel>
        <telerik:RadToggleSwitchButton
            CheckedContent="{telerik:LocalizableResource Key=DiaryOn}"
            ContentPosition="Left"
            Grid.Row="0"
            HorizontalAlignment="Right"
            IsChecked="{Binding Path=DiaryViewModel.IsEnabled}"
            Margin="0,0,10,0"
            UncheckedContent="{telerik:LocalizableResource Key=DiaryOff}"
            VerticalAlignment="Center" />
        <Grid
            Grid.Row="1"
            HorizontalAlignment="Center"
            VerticalAlignment="Stretch"
            x:Name="GridPassword">
            <StackPanel
                HorizontalAlignment="Center"
                Orientation="Horizontal"
                VerticalAlignment="Center">
                <telerik:RadPasswordBox
                    IsEnabled="{Binding DiaryViewModel.IsEnabled}"
                    PreviewKeyUp="PasswordBoxMu_OnPreviewKeyUp"
                    WatermarkContent="{telerik:LocalizableResource Key=DiaryPassword}"
                    Width="250"
                    x:Name="PasswordBoxMu" />
            </StackPanel>
        </Grid>
        <telerik:RadGridView
            AutoExpandGroups="True"
            AutoGenerateColumns="False"
            EnableColumnVirtualization="True"
            EnableRowVirtualization="True"
            FrozenColumnsSplitterVisibility="Hidden"
            Grid.Row="1"
            IsPropertyChangedAggregationEnabled="False"
            IsReadOnly="True"
            ItemsSource="{Binding DiaryViewModel.AllDiaryItems, IsAsync=True}"
            Margin="5,0,5,5"
            MouseDoubleClick="listBoxItem_DoubleClick"
            RowIndicatorVisibility="Collapsed"
            SelectionChanged="lvDiary_SelectionChanged"
            IsLocalizationLanguageRespected="False"
            SelectionMode="Extended"
            SelectionUnit="FullRow"
            ShowGroupPanel="False"
            TextSearch.TextPath="ShortText"
            VirtualizingPanel.IsVirtualizingWhenGrouping="True"
            x:Name="lvDiary">
            <telerik:RadContextMenu.ContextMenu>
                <telerik:RadContextMenu x:Name="GridContextMenu">
                    <MenuItem Click="MenuItemCopyClick" Header="{telerik:LocalizableResource Key=CopyButton}" />
                </telerik:RadContextMenu>
            </telerik:RadContextMenu.ContextMenu>
            <telerik:RadGridView.Columns>
                <telerik:GridViewDataColumn
                    DataMemberBinding="{Binding ShortText}"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderText}"
                    IsFilterable="False"
                    Width="500*">
                    <telerik:GridViewColumn.ToolTipTemplate>
                        <DataTemplate  DataType="{x:Type dataModel:DiaryDataModel}">
                            <TextBlock Text="{Binding Text}" />
                        </DataTemplate>
                    </telerik:GridViewColumn.ToolTipTemplate>
                </telerik:GridViewDataColumn>
                <telerik:GridViewDataColumn
                    DataMemberBinding="{Binding Application}"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderApp}"
                    Width="140" />
                <telerik:GridViewDataColumn
                    FilterMemberPath="DateTimeDate"
                    GroupMemberPath="DateTimeDate"
                    Header="{telerik:LocalizableResource Key=DiaryHeaderDate}"
                    SortMemberPath="DateTime"
                    Width="Auto">
                    <telerik:GridViewDataColumn.CellTemplate>
                        <DataTemplate  DataType="{x:Type dataModel:DiaryDataModel}">
                            <TextBlock>
                                <TextBlock.Text>
                                    <Binding Path="DateTime" StringFormat="{}{0:g}" />
                                </TextBlock.Text>
                            </TextBlock>
                        </DataTemplate>
                    </telerik:GridViewDataColumn.CellTemplate>
                </telerik:GridViewDataColumn>
            </telerik:RadGridView.Columns>
        </telerik:RadGridView>
    </Grid>

</UserControl>