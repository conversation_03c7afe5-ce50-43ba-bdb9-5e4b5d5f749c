﻿using Everylang.App.SwitcherLang;
using System.Windows.Controls.Primitives;
using System.Windows.Input;

namespace Everylang.App.View.Controls.LangInfo
{
    /// <summary>
    /// Interaction logic for Window1.xaml
    /// </summary>
    internal partial class LangInfoWindow : Popup
    {
        internal LangInfoWindow()
        {
            InitializeComponent();
            MouseDown += OnMouseDown;
        }

        private void OnMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Right)
            {
                KeyboardLayoutSwitcher.SwitchLayout();
            }

        }
    }
}
