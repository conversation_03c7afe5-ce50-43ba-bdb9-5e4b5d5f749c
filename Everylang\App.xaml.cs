﻿using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using Everylang.App.View;
using Everylang.Common.LogManager;
using System;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            if (SingleInstance.AlreadyRunning())
                Environment.Exit(-2);
            AppDomain.CurrentDomain.UnhandledException += CurrentDomainUnhandledException;
            if (!SettingsManager.Init())
            {
                RadWindow.Alert(new DialogParameters() { Content = "Error init settings", Header = "Everylang", });
                Application.Current.Shutdown();
                return;
            }
#if DEBUG
#else
            if (StartUp.CheckStartAdmin()) Environment.Exit(-2);
            StartUp.SetLastTryStartAdmin(false);
#endif
            StartUp.CheckStartUp();
            new MainWindow().Show();
            base.OnStartup(e);
        }

        private static void CurrentDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var ex = e.ExceptionObject as Exception;
            if (ex == null)
            {
                Logger.LogTo.Fatal("UnhandledException");
            }
            else
            {
                Logger.LogTo.Fatal(ex, ex.ToString());
            }
            Environment.Exit(-1);
        }
    }
}
