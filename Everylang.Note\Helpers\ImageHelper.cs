﻿using MaterialDesignColors;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Color = System.Windows.Media.Color;

namespace Everylang.Note.Helpers
{
    class ImageHelper
    {
        internal static BitmapSource CreateBitmapSource(Swatch swatch)
        {
            int width = 128;
            int height = width;
            int stride = width / 8;
            byte[] pixels = new byte[height * stride];

            List<Color> colors = new List<Color>();
            var color = swatch.PrimaryHues.ToList()[1].Color;
            colors.Add(Color.FromRgb(color.R, color.G, color.B));
            BitmapPalette myPalette = new BitmapPalette(colors);

            BitmapSource image = BitmapSource.Create(
                width,
                height,
                96,
                96,
                PixelFormats.Indexed1,
                myPalette,
                pixels,
                stride);

            return image;
        }
    }
}
