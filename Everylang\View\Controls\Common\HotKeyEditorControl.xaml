﻿<UserControl
    x:Class="Everylang.App.View.Controls.Common.HotKeyEditorControl"
    x:Name="UserControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    mc:Ignorable="d" x:ClassModifier="internal">
    <Grid >
        <telerik:RadWatermarkTextBox
            x:Name="HotkeyTextBox"
            HorizontalContentAlignment="Center"
            VerticalContentAlignment="Center"
            FontSize="14"
            IsReadOnly="True"
            Text="{Binding HotKey, ElementName=UserControl, Mode=OneWay}"
            WatermarkContent="{telerik:LocalizableResource Key=HotKeyWithoutShortcutNull}">
            <telerik:RadWatermarkTextBox.ContextMenu>
                <ContextMenu Visibility="Collapsed" />
            </telerik:RadWatermarkTextBox.ContextMenu>
        </telerik:RadWatermarkTextBox>
        <TextBox Width="0" PreviewKeyDown="HotKeyTextBox_PreviewKeyDown" x:Name="MuGrid"></TextBox>
    </Grid>
</UserControl>