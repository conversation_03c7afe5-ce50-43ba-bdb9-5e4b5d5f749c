<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Controls.GanttView</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer">
            <summary>
            Provides a class that exposes a single cell in the RadGanttView grid section to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.#ctor(System.Windows.FrameworkElement,System.Int32,System.Int32,Telerik.Windows.Controls.GanttView.CellInfo,Telerik.Windows.Automation.Peers.GanttGridAutomationPeer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer"/> class.
            </summary>
            <param name="owner"> The owner panel.</param>
            <param name="rowIndex"> The index of the row which contains the owner panel.</param>
            <param name="columnIndex"> The index of the column which contains the owner panel.</param>
            <param name="cellInfo"> The source data of the owner panel.</param>
            <param name="containingGrid"> The parent Grid.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.System#Windows#Automation#Provider#IGridItemProvider#Column">
            <summary>
            Gets the ordinal number of the column that contains the cell or item.
            </summary>
            <returns>A zero-based ordinal number that identifies the column containing the cell or item.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.System#Windows#Automation#Provider#IGridItemProvider#ColumnSpan">
            <summary>
            Gets the number of columns spanned by a cell or item.
            </summary>
            <returns> The number of columns spanned.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.System#Windows#Automation#Provider#IGridItemProvider#ContainingGrid">
            <summary>
            Gets a UI Automation provider that implements System.Windows.Automation.Provider.IGridProvider
            and represents the container of the cell or item.
            </summary>
            <returns> A UI Automation provider that implements the System.Windows.Automation.GridPattern
            and represents the cell or item container.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.System#Windows#Automation#Provider#IGridItemProvider#Row">
            <summary>
            Gets the ordinal number of the row that contains the cell or item.
            </summary>
            <returns> A zero-based ordinal number that identifies the row containing the cell or item.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.System#Windows#Automation#Provider#IGridItemProvider#RowSpan">
            <summary>
            Gets the number of rows spanned by a cell or item.
            </summary>
            <returns> The number of rows spanned.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the collection of child elements of the System.Windows.UIElement that
            is associated with this System.Windows.Automation.Peers.UIElementAutomationPeer.
            This method is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns> A list of child System.Windows.Automation.Peers.AutomationPeer elements.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.ScrollIntoView">
            <summary>
            Scrolls the content area in order to display the data item 
            within the visible region (viewport) of the container.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetRowHeaderItems">
            <summary>
             Retrieves a collection of UI Automation providers representing all the row headers 
             associated with a table item or cell.
            </summary>
            <returns>A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetColumnHeaderItems">
            <summary>
             Retrieves a collection of UI Automation providers representing all the column 
             headers associated with a table item or cell.
            </summary>
            <returns>A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetNameCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetName().
            </summary>
            <returns> The string that contains the label.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType().
            </summary>
            <returns>The control type.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttCellProvider">
            <summary>
            Provides a class that represents a provider for current cell of the RadGanttView grid section.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttCellProvider.#ctor(Telerik.Windows.Controls.GanttView.CellInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttCellProvider"/> class.
            </summary>
            <param name="cellInfo">The cell owner.</param>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer">
            <summary>
            Provides a class that exposes a column header in the RadGanttView grid section to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.#ctor(System.Windows.UIElement,System.Int32,Telerik.Windows.Controls.GanttView.ColumnDefinition,Telerik.Windows.Automation.Peers.GanttGridAutomationPeer)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer"/> class.
            </summary>
            <param name="owner"> The owner panel.</param>
            <param name="columnIndex"> The index of the column which contains the header panel.</param>
            <param name="columnDefinition"> The source data of the column.</param>
            <param name="containingGrid"> The parent Grid.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.Column">
            <summary>
            Gets the ordinal number of the column that contains the cell or item.
            </summary>
            <returns>A zero-based ordinal number that identifies the column containing the cell or item.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.ColumnSpan">
            <summary>
            Gets the number of columns spanned by a cell or item.
            </summary>
            <returns> The number of columns spanned.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.RowSpan">
            <summary>
            Gets the number of rows spanned by a cell or item.
            </summary>
            <returns> The number of rows spanned.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.ContainingGrid">
            <summary>
            Gets a UI Automation provider that implements System.Windows.Automation.Provider.IGridProvider
            and represents the container of the cell or item.
            </summary>
            <returns> A UI Automation provider that implements the System.Windows.Automation.GridPattern
            and represents the cell or item container.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.Row">
            <summary>
            Gets the ordinal number of the row that contains the cell or item.
            </summary>
            <returns> A zero-based ordinal number that identifies the row containing the cell or item.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetColumnHeaderItems">
            <summary>
            Retrieves a collection of UI Automation providers representing all the column
            headers associated with a table item or cell.
            </summary>
            <returns>A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetRowHeaderItems">
            <summary>
            Retrieves a collection of UI Automation providers representing all the row
            headers associated with a table item or cell.
            </summary>
            <returns>A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            When overridden in a derived class, gets the control pattern that is associated
            with the specified System.Windows.Automation.Peers.PatternInterface.
            </summary>
            <param name="patternInterface"> A value from the System.Windows.Automation.Peers.PatternInterface enumeration.</param>
            <returns> The object that implements the pattern interface; null if this peer does
            not support this interface.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetClassNameCore">
            <summary>
            Gets a name that is used with System.Windows.Automation.Peers.AutomationControlType,
            to differentiate the control that is represented by this System.Windows.Automation.Peers.AutomationPeer.
            </summary>
            <returns>The class name.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType().
            </summary>
            <returns>The control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.</summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderAutomationPeer.GetNameCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetName().
            </summary>
            <returns> The string that contains the label.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttHeaderProvider">
            <summary>
            Provides a class that represents a provider for current header of the RadGanttView grid section.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttHeaderProvider.#ctor(Telerik.Windows.Controls.GanttView.ColumnDefinition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttHeaderProvider"/> class.
            </summary>
            <param name="columnDefinition">The header owner.</param>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttTaskProvider">
            <summary>
            Provides a class that represents a provider for current item of the RadGanttView grid section.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.#ctor(Telerik.Windows.Controls.GanttView.IGanttTask,Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttTaskProvider"/> class.
            </summary>
            <param name="ganttTask"></param>
            <param name="presenter"></param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTaskProvider.IsSelected">
            <summary>
            Gets a value that indicates whether an item is selected.
            </summary>
            <returns> true if the element is selected; otherwise false.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTaskProvider.SelectionContainer">
            <summary>
            Gets the UI Automation provider that implements System.Windows.Automation.Provider.ISelectionProvider
            and acts as the container for the calling object.
            </summary>
            <returns> The provider that supports System.Windows.Automation.Provider.ISelectionProvider.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.GetPatternProvider(System.Int32)">
            <summary>
            Returns the object that supports the specified pattern.
            </summary>
            <param name="patternId">ID of the pattern.</param>
            <returns>Object that implements IInvokeProvider.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.AddToSelection">
            <summary>
            Adds the current element to the collection of selected items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.RemoveFromSelection">
            <summary>
            Removes the current element from the collection of selected items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.Select">
            <summary>
            Deselects any selected items and then selects the current element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskProvider.GetName">
            <summary>
            Gets the content of the associated Gantt task.
            </summary>
            <returns> The title of the Gantt task.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer">
            <summary>
            Provides a class that exposes a Gantt task to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.#ctor(Telerik.Windows.Controls.GanttView.IGanttTask,Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer"/> class.
            </summary>
            <param name="task"> The Gantt task.</param>
            <param name="owner"> The owner presenter.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsSelected">
            <summary>
            Gets a value that indicates whether an item is selected.
            </summary>
            <returns> true if the element is selected; otherwise false.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.SelectionContainer">
            <summary>
            Gets the UI Automation provider that implements System.Windows.Automation.Provider.ISelectionProvider
            and acts as the container for the calling object.
            </summary>
            <returns> The provider that supports System.Windows.Automation.Provider.ISelectionProvider.</returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.ExpandCollapseState">
            <summary>
            Gets the state, expanded or collapsed, of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.Collapse">
            <summary>
            Hides all nodes, controls, or content that are descendants of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.Expand">
            <summary>
            Displays all child nodes, controls, or content of the control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.ScrollIntoView">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.AddToSelection">
            <summary>
            Adds the current element to the collection of selected items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.RemoveFromSelection">
            <summary>
            Removes the current element from the collection of selected items.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.Select">
            <summary>
            Deselects any selected items and then selects the current element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            When overridden in a derived class, gets the control pattern that is associated
            with the specified System.Windows.Automation.Peers.PatternInterface.
            </summary>
            <param name="patternInterface"> A value from the System.Windows.Automation.Peers.PatternInterface enumeration.</param>
            <returns> The object that implements the pattern interface; null if this peer does
            not support this interface.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.</summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetNameCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetName().
            </summary>
            <returns> The string that contains the label.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetClassNameCore">
            <summary>
            Gets a name that is used with System.Windows.Automation.Peers.AutomationControlType,
            to differentiate the control that is represented by this System.Windows.Automation.Peers.AutomationPeer.
            </summary>
            <returns>The class name.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetAcceleratorKeyCore">
            <summary>
            Gets the accelerator key combinations for the element that is associated
            with the UI Automation peer.
            </summary>
            <returns>The accelerator key.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetAccessKeyCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAccessKey().
            </summary>
            <returns>The string that contains the access key.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType().
            </summary>
            <returns>The control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetAutomationIdCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationId().
            </summary>
            <returns>The string that contains the identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetBoundingRectangleCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetBoundingRectangle().
            </summary>
            <returns>The bounding rectangle.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetChildrenCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns>The collection of child elements.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetClickablePointCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetClickablePoint().
            </summary>
            <returns> A point within the clickable area of the element.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetHelpTextCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetHelpText().
            </summary>
            <returns> The help text.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetItemStatusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetItemStatus().
            </summary>
            <returns>The status.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetItemTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetItemType().
            </summary>
            <returns> The kind of item.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetLabeledByCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetLabeledBy().
            </summary>
            <returns>The System.Windows.Automation.Peers.LabelAutomationPeer for the element that
            is targeted by the System.Windows.Controls.Label.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.GetOrientationCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetOrientation().
            </summary>
            <returns> The orientation of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.HasKeyboardFocusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.HasKeyboardFocus().
            </summary>
            <returns> True if the element has keyboard focus; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsContentElementCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsContentElement().
            </summary>
            <returns> True if the element is a content element; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsControlElementCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsControlElement().
            </summary>
            <returns> True if the element is a control; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsEnabledCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsEnabled().
            </summary>
            <returns> True if the automation peer can receive and send events; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsKeyboardFocusableCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsKeyboardFocusable().
            </summary>
            <returns> True if the element can accept keyboard focus; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsOffscreenCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsOffscreen().
            </summary>
            <returns> True if the element is not on the screen otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsPasswordCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsPassword().
            </summary>
            <returns> True if the element contains sensitive content; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.IsRequiredForFormCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsRequiredForForm().
            </summary>
            <returns> True if the element is must be completed; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTaskAutomationPeer.SetFocusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.SetFocus().
            </summary>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttTimeRulerAutomationPeer">
            <summary>
            Provides a class that exposes the time ruler section of RadGanttView to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerAutomationPeer.#ctor(Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttTimeRulerAutomationPeer"/> class.
            </summary>
            <param name="presenter">The items presenter which contains the owner panel.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the System.Windows.UIElement that
            is associated with this System.Windows.Automation.Peers.UIElementAutomationPeer.
            This method is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns> A list of child System.Windows.Automation.Peers.AutomationPeer elements.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttTimeRulerItemAutomationPeer">
            <summary>
            Provides a class that exposes a single item in the time ruler section of RadGanttView to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerItemAutomationPeer.#ctor(System.Windows.FrameworkElement)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttTimeRulerItemAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner panel which is Framework element of type IDataContainer.</param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerItemAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeRulerItemAutomationPeer.GetNameCore">
            <summary>
            Gets the text label of the System.Windows.ContentElement that is associated
            with this System.Windows.Automation.Peers.ContentElementAutomationPeer. Called
            by System.Windows.Automation.Peers.AutomationPeer.GetName().
            </summary>
            <returns>The text label of the element that is associated with this automation peer.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer">
            <summary>
            Provides a class that exposes the RadGanttView to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.#ctor(Telerik.Windows.Controls.RadGanttView,Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.CanSelectMultiple">
            <summary>
            Gets a value that specifies whether the UI Automation provider allows more
            than one child element to be selected concurrently.
            </summary>
            <returns>
            True if multiple selection is allowed; otherwise false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.IsSelectionRequired">
            <summary>
            Gets a value that specifies whether the UI Automation provider requires at
            least one child element to be selected.
            </summary>
            <returns>
            true if selection is required; otherwise false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetSelection">
            <summary>
            Retrieves a UI Automation provider for each child element that is selected.
            </summary>
            <returns>
            A collection of UI Automation providers.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the pattern.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetClassNameCore">
            <summary>
            Returns the name of the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetClassName"/>.
            </summary>
            <returns>
            The name of the owner type that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. See Remarks.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            Returns the control type for the <see cref="T:System.Windows.UIElement"/> that is associated with this <see cref="T:System.Windows.Automation.Peers.FrameworkElementAutomationPeer"/>. This method is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType"/>.
            </summary>
            <returns>A value of the enumeration.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.RadGanttViewAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the System.Windows.UIElement that
            is associated with this System.Windows.Automation.Peers.UIElementAutomationPeer.
            This method is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns> A list of child System.Windows.Automation.Peers.AutomationPeer elements.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer">
            <summary>
            Provides a class that exposes the grid section of RadGanttView to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.#ctor(Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer"/> class.
            </summary>
            <param name="owner">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.ColumnCount">
            <summary>
            Gets the total number of columns in a grid.
            </summary>
            <returns>
            The total number of columns in a grid.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.RowCount">
            <summary>
            Gets the total number of rows in a grid.
            </summary>
            <returns>
            The total number of rows in a grid.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.RowOrColumnMajor">
            <summary>
            Retrieves the primary direction of traversal for the table.
            </summary>
            <returns>
            The primary direction of traversal.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.VerticalViewSize">
            <summary>
            Gets the vertical view size.
            </summary>
            <returns>
            The vertical size of the viewable region as a percentage of the total content
            area within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.HorizontalViewSize">
            <summary>
            Gets the current horizontal view size.
            </summary>
            <returns>
            The horizontal size of the viewable region as a percentage of the total content
            area within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.HorizontallyScrollable">
            <summary>
            Gets a value that indicates whether the control can scroll horizontally.
            </summary>
            <returns>
            true if the control can scroll horizontally; otherwise false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.VerticallyScrollable">
            <summary>
            Gets a value that indicates whether the control can scroll vertically.
            </summary>
            <returns>
            true if the control can scroll vertically; otherwise false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.HorizontalScrollPercent">
            <summary>
            Gets the current horizontal scroll position.
            </summary>
            <returns>
            The horizontal scroll position as a percentage of the total content area
            within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.VerticalScrollPercent">
            <summary>
            Gets the current vertical scroll position.
            </summary>
            <returns>
            The vertical scroll position as a percentage of the total content area within
            the control.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetItem(System.Int32,System.Int32)">
            <summary>
            Retrieves the UI Automation provider for the specified cell.
            </summary>
            <param name="row"> The ordinal number of the row of interest.</param>
            <param name="column"> The ordinal number of the column of interest.</param>
            <returns> The UI Automation provider for the specified cell.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetColumnHeaders">
            <summary>
            Gets a collection of UI Automation providers that represents all the column
            headers in a table.
            </summary>
            <returns> A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetRowHeaders">
            <summary>
            Retrieves a collection of UI Automation providers that represents all row
            headers in the table.
            </summary>
            <returns> A collection of UI Automation providers.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.Scroll(System.Windows.Automation.ScrollAmount,System.Windows.Automation.ScrollAmount)">
            <summary>
            Scrolls the visible region of the content area horizontally and vertically.
            </summary>
            <param name="horizontalAmount">
            The horizontal increment specific to the control. System.Windows.Automation.ScrollPatternIdentifiers.NoScroll
            should be passed in if the control cannot be scrolled in this direction.
            </param>
            <param name="verticalAmount">
            The vertical increment specific to the control. System.Windows.Automation.ScrollPatternIdentifiers.NoScroll
            should be passed in if the control cannot be scrolled in this direction.
            </param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.SetScrollPercent(System.Double,System.Double)">
            <summary>
            Sets the horizontal and vertical scroll position as a percentage of the total
            content area within the control.
            </summary>
            <param name="horizontalPercent">
            The horizontal position as a percentage of the content area's total range.
            System.Windows.Automation.ScrollPatternIdentifiers.NoScroll should be passed
            in if the control cannot be scrolled in this direction.
            </param>
            <param name="verticalPercent">
            The vertical position as a percentage of the content area's total range.
            System.Windows.Automation.ScrollPatternIdentifiers.NoScroll should be passed
            in if the control cannot be scrolled in this direction.
            </param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the pattern.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the System.Windows.UIElement that
            is associated with this System.Windows.Automation.Peers.UIElementAutomationPeer.
            This method is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns> A list of child System.Windows.Automation.Peers.AutomationPeer elements.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetAcceleratorKeyCore">
            <summary>
            Gets the accelerator key combinations for the element that is associated
            with the UI Automation peer.
            </summary>
            <returns>The accelerator key.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetAccessKeyCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAccessKey().
            </summary>
            <returns>The string that contains the access key.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetAutomationControlTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationControlType().
            </summary>
            <returns>The control type.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetAutomationIdCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetAutomationId().
            </summary>
            <returns>The string that contains the identifier.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetBoundingRectangleCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetBoundingRectangle().
            </summary>
            <returns>The bounding rectangle.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetClassNameCore">
            <summary>
            Gets a name that is used with System.Windows.Automation.Peers.AutomationControlType,
            to differentiate the control that is represented by this System.Windows.Automation.Peers.AutomationPeer.
            </summary>
            <returns>The class name.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetClickablePointCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetClickablePoint().
            </summary>
            <returns> A point within the clickable area of the element.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetHelpTextCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetHelpText().
            </summary>
            <returns> The help text.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetItemStatusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetItemStatus().
            </summary>
            <returns>The status.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetItemTypeCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetItemType().
            </summary>
            <returns> The kind of item.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetLabeledByCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetLabeledBy().
            </summary>
            <returns>The System.Windows.Automation.Peers.LabelAutomationPeer for the element that
            is targeted by the System.Windows.Controls.Label.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetNameCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetName().
            </summary>
            <returns> The string that contains the label.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.GetOrientationCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.GetOrientation().
            </summary>
            <returns> The orientation of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.HasKeyboardFocusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.HasKeyboardFocus().
            </summary>
            <returns> True if the element has keyboard focus; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsContentElementCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsContentElement().
            </summary>
            <returns> True if the element is a content element; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsControlElementCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsControlElement().
            </summary>
            <returns> True if the element is a control; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsEnabledCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsEnabled().
            </summary>
            <returns> True if the automation peer can receive and send events; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsKeyboardFocusableCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsKeyboardFocusable().
            </summary>
            <returns> True if the element can accept keyboard focus; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsOffscreenCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsOffscreen().
            </summary>
            <returns> True if the element is not on the screen otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsPasswordCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsPassword().
            </summary>
            <returns> True if the element contains sensitive content; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.IsRequiredForFormCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.IsRequiredForForm().
            </summary>
            <returns> True if the element is must be completed; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttGridAutomationPeer.SetFocusCore">
            <summary>
            When overridden in a derived class, is called by System.Windows.Automation.Peers.AutomationPeer.SetFocus().
            </summary>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer">
            <summary>
            Provides a class that exposes the time line section of RadGanttView to UI Automation.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.#ctor(Telerik.Windows.Controls.GanttView.GanttItemsPresenter)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer"/> class.
            </summary>
            <param name="presenter">The owner.</param>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.HorizontalScrollPercent">
            <summary>
            Gets the current horizontal scroll position.
            </summary>
            <returns>
            The horizontal scroll position as a percentage of the total content area
            within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.VerticalScrollPercent">
            <summary>
            Gets the current vertical scroll position.
            </summary>
            <returns>
            The vertical scroll position as a percentage of the total content area within
            the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.VerticalViewSize">
            <summary>
            Gets the vertical view size.
            </summary>
            <returns>
            The vertical size of the viewable region as a percentage of the total content
            area within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.HorizontalViewSize">
            <summary>
            Gets the current horizontal view size.
            </summary>
            <returns>
            The horizontal size of the viewable region as a percentage of the total content
            area within the control.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.HorizontallyScrollable">
            <summary>
            Gets a value that indicates whether the control can scroll horizontally.
            </summary>
            <returns>
            true if the control can scroll horizontally; otherwise false.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.VerticallyScrollable">
            <summary>
            Gets a value that indicates whether the control can scroll vertically.
            </summary>
            <returns>
            true if the control can scroll vertically; otherwise false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.Scroll(System.Windows.Automation.ScrollAmount,System.Windows.Automation.ScrollAmount)">
            <summary>
            Scrolls the visible region of the content area horizontally and vertically.
            </summary>
            <param name="horizontalAmount">
            The horizontal increment specific to the control. System.Windows.Automation.ScrollPatternIdentifiers.NoScroll
            should be passed in if the control cannot be scrolled in this direction.
            </param>
            <param name="verticalAmount">
            The vertical increment specific to the control. System.Windows.Automation.ScrollPatternIdentifiers.NoScroll
            should be passed in if the control cannot be scrolled in this direction.
            </param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.SetScrollPercent(System.Double,System.Double)">
            <summary>
            Sets the horizontal and vertical scroll position as a percentage of the total
            content area within the control.
            </summary>
            <param name="horizontalPercent">
            The horizontal position as a percentage of the content area's total range.
            System.Windows.Automation.ScrollPatternIdentifiers.NoScroll should be passed
            in if the control cannot be scrolled in this direction.
            </param>
            <param name="verticalPercent">
            The vertical position as a percentage of the content area's total range.
            System.Windows.Automation.ScrollPatternIdentifiers.NoScroll should be passed
            in if the control cannot be scrolled in this direction.
            </param>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.GetPattern(System.Windows.Automation.Peers.PatternInterface)">
            <summary>
            Gets the pattern.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.GetLocalizedControlTypeCore">
            <summary>When overridden in a derived class, is called by <see cref="M:System.Windows.Automation.Peers.AutomationPeer.GetLocalizedControlType"/>.
            </summary>
            <returns>The type of the control.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.GanttTimeLineAutomationPeer.GetChildrenCore">
            <summary>
            Gets the collection of child elements of the System.Windows.UIElement that
            is associated with this System.Windows.Automation.Peers.UIElementAutomationPeer.
            This method is called by System.Windows.Automation.Peers.AutomationPeer.GetChildren().
            </summary>
            <returns> A list of child System.Windows.Automation.Peers.AutomationPeer elements.</returns>
        </member>
        <member name="T:Telerik.Windows.Automation.Peers.SimpleProviderBase">
            <summary>
            Provides a basic implementation of data driven provider.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.SimpleProviderBase.HostRawElementProvider">
            <summary>
            Tells UI Automation that this control is hosted in an HWND, which has its own
            provider.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Automation.Peers.SimpleProviderBase.ProviderOptions">
            <summary>
            Retrieves provider options.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SimpleProviderBase.GetPatternProvider(System.Int32)">
            <summary>
            Returns the object that supports the specified pattern.
            </summary>
            <param name="patternId">ID of the pattern.</param>
            <returns>Object that implements IInvokeProvider.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SimpleProviderBase.GetPropertyValue(System.Int32)">
            <summary>
            Returns property values.
            </summary>
            <param name="propertyId">Property identifier.</param>
            <returns>Property value.</returns>
        </member>
        <member name="M:Telerik.Windows.Automation.Peers.SimpleProviderBase.GetName">
            <summary>
            When overridden gets the display name of the associated item.
            </summary>
            <returns> The name of the item.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.ColumnLength">
            <summary>
            Describes the length of a column which could be either fixed-sized or auto-sized.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.ColumnLength.Type">
            <summary>
            Gets the type of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> instance.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.ColumnLength.Value">
            <summary>
            Gets the actual fixed size of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> instance. If the length is not fixed-size, double.NaN is returned instead.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.FromDouble(System.Double)">
            <summary>
            Creates a new fixed-size instance of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> class using the provided double value.
            </summary>
            <param name="length">The actual fixed size of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> instance.</param>
            <returns>A new fixed-size instance of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> class using the provided double value.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.FromColumnLengthType(Telerik.Windows.Controls.ColumnLengthType)">
            <summary>
            Creates a new instance of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> class by type.
            </summary>
            <param name="type">The type of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> instance.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.op_Implicit(System.Double)~Telerik.Windows.Controls.ColumnLength">
            <summary>
            Converts from <see cref="T:System.Double"/> to a fixed-size <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.
            </summary>
            <param name="length">The length of the fixed-size <see cref="T:Telerik.Windows.Controls.ColumnLength"/> to be created.</param>
            <returns>A fixed-size <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.op_Implicit(Telerik.Windows.Controls.ColumnLengthType)~Telerik.Windows.Controls.ColumnLength">
            <summary>
            Converts from <see cref="T:Telerik.Windows.Controls.ColumnLengthType"/> to <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.
            </summary>
            <param name="type">The type of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> to be created.</param>
            <returns>A <see cref="T:Telerik.Windows.Controls.ColumnLength"/> of the type specified.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.op_Equality(Telerik.Windows.Controls.ColumnLength,Telerik.Windows.Controls.ColumnLength)">
            <summary>
            Compares two instance of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> struct for equality.
            </summary>
            <param name="left">The left-hand operand of the equality operator.</param>
            <param name="right">The right-hand operand of the equality operator.</param>
            <returns>true if the left and the right-hand operands are equal; false otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.op_Inequality(Telerik.Windows.Controls.ColumnLength,Telerik.Windows.Controls.ColumnLength)">
            <summary>
            Compares two instance of the <see cref="T:Telerik.Windows.Controls.ColumnLength"/> struct for equality.
            </summary>
            <param name="left">The left-hand operand of the equality operator.</param>
            <param name="right">The right-hand operand of the equality operator.</param>
            <returns>true if the left and the right-hand operands are not equal; false otherwise.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.Equals(Telerik.Windows.Controls.ColumnLength)">
            <summary>
            Determines whether the specified <see cref="T:Telerik.Windows.Controls.ColumnLength"/> is equal to the current <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.
            </summary>
            <param name="other">The object to compare with the current object.</param>
            <returns>true if the specified <see cref="T:Telerik.Windows.Controls.ColumnLength"/> is equal to the current <see cref="T:Telerik.Windows.Controls.ColumnLength"/>; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified <see cref="T:System.Object"/> is of type <see cref="T:Telerik.Windows.Controls.ColumnLength"/> and is equal to the current <see cref="T:Telerik.Windows.Controls.ColumnLength"/>; 
            otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLength.GetHashCode">
            <summary>
            Returns the hash code for this instance.
            </summary>
            <returns>A 32-bit signed integer that is the hash code for this instance.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.ColumnLengthType">
            <summary>
            Describes the type of column length.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ColumnLengthType.FixedSize">
            <summary>
            Fixed length, identified by a double value.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ColumnLengthType.AutoHeader">
            <summary>
            The size is automatically calculated using the header only.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.ColumnLengthType.AutoHeaderAndContent">
            <summary>
            The size is automatically calculated using both the header and the content.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.ColumnLengthTypeConverter">
            <summary>
            Represents a converter from <see cref="T:System.String"/> to <see cref="T:Telerik.Windows.Controls.ColumnLength"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLengthTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert an object of the given type to
            the type of this converter, using the specified context.
            </summary>
            <param name="context">An System.ComponentModel.ITypeDescriptorContext that provides a format context.</param>
            <param name="sourceType">A System.Type that represents the type you want to convert from.</param>
            <returns>true if this converter can perform the conversion; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLengthTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert the object to the specified type,
            using the specified context.
            </summary>
            <param name="context">An System.ComponentModel.ITypeDescriptorContext that provides a format context.</param>
            <param name="destinationType">A System.Type that represents the type you want to convert to.</param>
            <returns>true if this converter can perform the conversion; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLengthTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified
            context and culture information.
            </summary>
            <param name="context">An System.ComponentModel.ITypeDescriptorContext that provides a format context.</param>
            <param name="culture">The System.Globalization.CultureInfo to use as the current culture.</param>
            <param name="value">The System.Object to convert.</param>
            <returns>An System.Object that represents the converted value.</returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed.</exception>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLengthTypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Returns whether this object supports a standard set of values that can be
            picked from a list, using the specified context.
            </summary>
            <param name="context">An System.ComponentModel.ITypeDescriptorContext that provides a format context.</param>
            <returns>
            True if System.ComponentModel.TypeConverter.GetStandardValues() should be
            called to find a common set of values the object supports; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.ColumnLengthTypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Returns a collection of standard values for the data type this type converter
            is designed for when provided with a format context.
            </summary>
            <param name="context">
            An System.ComponentModel.ITypeDescriptorContext that provides a format context
            that can be used to extract additional information about the environment
            from which this converter is invoked. This parameter or properties of this
            parameter can be null.
            </param>
            <returns>
            A System.ComponentModel.TypeConverter.StandardValuesCollection that holds
            a standard set of valid values, or null if the data type does not support
            a standard set of values.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttCommands">
            <summary>
            This class contains Routed commands for the GanttView control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttCommands.CommandId.InlineEdit">
            <summary>
            Initiates inline editing.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttCommands.InlineEditCommand">
            <summary>
            Gets value that represents the inline edit command.
            </summary>
            <value>The inline edit command.</value>
            <remarks>This command initiates an inline editing operation in the GanttView control.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttCommands.InlineEdit">
            <summary>
            Gets value that represents the inline edit command.
            </summary>
            <value>The inline edit command.</value>
            <remarks>This command initiates an inline editing operation in the GanttView control.</remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ColumnDefinition">
            <summary>
            This class represents a GanttView column definition. A collection of ColumnDefinitions is used for describing the tree-list part of the GanttView control.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellEditTemplateProperty">
            <summary>
            Identifies the CellEditTemplate dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellTemplateProperty">
            <summary>
            Identifies the CellTemplate dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellHighlightTemplateProperty">
            <summary>
            Identifies the CellHighlightTemplate dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellSelectionTemplateProperty">
            <summary>
            Identifies the CellSelectionTemplate dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.MinWidthProperty">
            <summary>
            Identifies the MinWidth dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.MaxWidthProperty">
            <summary>
            Identifies the MaxWidth dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinition.IsResizableProperty">
            <summary>
            Identifies the IsResizable dependency property. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellEditTemplate">
            <summary>
            Gets or sets the template of the cells in this column when in Edit mode. If this template is not set, editing is not allowed in the corresponding column. This is a dependency property.
            </summary>
            <remarks>The DataContext of this template is the event in the given row.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellTemplate">
            <summary>
            Gets or sets the template of the cells in this column when in normal state. If the CellHighlightTemplate or CellSelectionTemplate are not set, 
            the selected or highlighted cells also use this template. This is a dependency property.
            </summary>
            <remarks>When this template is set the GanttView's rendering is slower than using directly the MemberBinding property, so consider carefully if you need to use this property or not.
            Please notice that the DataContext of this template is not actually the event in the given row, but a Proxy object containing the following properties: 
            Start, End, Title, FormattedValue, OriginalEvent. The OriginalEvent is the event in the row and the FormattedValue is the value extracted through the MemberBinding.
            Use FormattedValue for best performance.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellHighlightTemplate">
            <summary>
            Gets or sets the template of the cells in this column when the mouse is over the cell. If this property is not set, the value of the CellTemplate is used. This is a dependency property.
            </summary>
            <remarks>Please notice that the DataContext of this template is not actually the event in the given row, but a Proxy object containing the following properties: 
            Start, End, Title, FormattedValue, OriginalEvent. The OriginalEvent is the event in the row and the FormattedValue is the value extracted through the MemberBinding.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.CellSelectionTemplate">
            <summary>
            Gets or sets the template of the cells in this column when the mouse is over the cell. If this property is not set, the value of the CellTemplate is used.
            </summary>
            <remarks>Please notice that the DataContext of this template is not actually the event in the given row, but a Proxy object containing the following properties: 
            Start, End, Title, FormattedValue, OriginalEvent. The OriginalEvent is the event in the row and the FormattedValue is the value extracted through the MemberBinding.</remarks>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.MinWidth">
            <summary>
            Gets or sets the minimum width of the column. The value of this property doesn't restrict the ColumnWidth property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.MaxWidth">
            <summary>
            Gets or sets the maximum width of the column. The value of this property doesn't restrict the ColumnWidth property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.IsResizable">
            <summary>
            Gets or sets a value indicating whether the column can be resized through the column resizer or not.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinition.MemberBinding">
            <summary>
            Gets or sets the binding to be used to evaluate the Cell content.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnDefinition.GetDataContextForItemOverride(System.Object)">
            <summary>
            When overridden in the derived class this method determines which is the actual data item for the row for a given item from the list (in most cases an HierarchicalItem).
            </summary>
            <param name="itemData">The original item from the source collection. In most cases this is an HierarchicalItem.</param>
            <returns>The item to be used as a data item for the cell, generated for this column for the given data item. The default implementation just extracts the SourceItem from the HierarchicalItem.</returns>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.HeaderProperty">
            <summary>
            Identifies the Header dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.IsFrozenColumnProperty">
            <summary>
            Identifies the IsFrozen dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.ColumnWidthProperty">
            <summary>
            Identifies the ColumnWidth dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.WidthProperty">
            <summary>
            Identifies the Width dependency property. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.Header">
            <summary>
            Gets or sets the Header. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.ColumnWidth">
            <summary>
            Gets or sets the Width of the column. If set to NaN the width is calculated depending on the Header. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.Width">
            <summary>
            Gets or sets a value describing the width of the column.
            </summary>
            <value>
            A <see cref="T:Telerik.Windows.Controls.ColumnLength"/> value which is either AutoHeader, AutoHeaderAndContent or FixedSize, combined with a double value defining the actual fixed size.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.IsFrozenColumn">
            <summary>
            Gets or sets a value determining whether the column is frozen or not. Only root-level columns could be frozen or not and all 
            frozen columns are displayed before the non-frozen columns. This is a dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase.GetSubItems">
            <summary>
            When overridden in the derived class this method returns the sub columns of the column. By default it doesn't return any columns.
            </summary>
            <returns>The sub columns of the column. By default it doesn't return any columns.</returns>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.ColumnDefinitionGroup.ChildrenProperty">
            <summary>
            Identifies the Children dependency property. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ColumnDefinitionGroup.Children">
            <summary>
            Gets or sets the Children. This is a dependency property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ArrowType">
            <summary>
            TODO: Update summary.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellHighlightContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SimpleTreeCellContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SimpleTreeCellContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellEditingContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellEditingContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellHighlightContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TreeCellHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer">
            <summary>
            Represents a UI of a column header.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer.MeasureOverride(System.Windows.Size)">
            <summary>
            Called to re-measure a control.
            </summary>
            <param name="availableSize">The maximum size that the method can return.</param>
            <returns>The size of the control, up to the maximum specified by constraint.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ColumnHeaderContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellEditingContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.CellEditingContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DeadlineContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DeadlineContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DropReorderHighlightContainer.DataItem">
            <summary>
            Gets or sets the data item.
            </summary>
            <value>
            The data item.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DropReorderHighlightContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DropReorderHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DependencyIndicator.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DependencyIndicator.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer">
            <summary>
            Class representing the container drawing the relation item.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.ArrowThicknessProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.ArrowThickness"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.MinEdgeLengthProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.MinEdgeLength"/> dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.ArrowThickness">
            <summary>
            Gets or sets the ArrowThickness that defines the thickness of the DragRelationSlotContainer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.MinEdgeLength">
            <summary>
            Gets or sets the MinEdgeLength.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.DataItem">
            <summary>
            Gets or sets the data item.
            </summary>
            <value>
            The data item.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.OnApplyTemplate">
            <summary>
             When overridden in a derived class, is invoked whenever application code or internal processes call System.Windows.FrameworkElement.ApplyTemplate().
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragRelationSlotContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragResizeVisualCue.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragResizeVisualCue.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.EventDecoratorContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.EventDecoratorContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.MilestoneContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.MilestoneContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.EventContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.EventContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.OnTimeIndicatorContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.OnTimeIndicatorContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.RelationContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.RelationContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationContainer.ArrowProxy.PathData">
            <summary>
            Gets or sets PathData and notifies for changes.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SimpleCellContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SimpleCellContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SummaryContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.SummaryContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.DragResizeSummarySlotHighlightContainer">
            <summary>
            Class representing the container drawing the dragged summary item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSummarySlotHighlightContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.DragResizeSummarySlotHighlightContainer"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSummarySlotHighlightContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSummarySlotHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.DragResizeMilestoneSlotHighlightContainer">
            <summary>
            Class representing the container drawing the dragged milestone item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeMilestoneSlotHighlightContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.DragResizeMilestoneSlotHighlightContainer"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeMilestoneSlotHighlightContainer.ResetThemeOverride">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeMilestoneSlotHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer">
            <summary>
            Class representing the container drawing the dragged regular task item.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.DataItem">
            <summary>
            Gets or sets the data item.
            </summary>
            <value>
            The data item.
            </value>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.StartProperty">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.EndProperty">
            <summary>
            
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.TaskProperty">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.Task">
            <summary>
            Gets or sets the task.
            </summary>
            <value>
            The task.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.Start">
            <summary>
            Gets or sets the start.
            </summary>
            <value>
            The start.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.DragResizeSlotHighlightContainer.Proxy.End">
            <summary>
            Gets or sets the end.
            </summary>
            <value>
            The end.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IDependant.Dependencies">
            <summary>
            Collection with dependencies from other tasks.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.GanttDragDependenciesBehavior">
            <summary>
            The class for drag-drop linking operation behaviors for the RadGanttView control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDependenciesBehavior.Link(Telerik.Windows.Controls.Scheduling.SchedulingLinkState)">
            <summary>
            Applies the effect of the link operation when it is successful.
            </summary>
            <param name="state">SchedulingLinkState that provides context for the current operation.</param>
            <remarks>
            This method is called only if the link operation is successful and it is meant to do the actual link.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDependenciesBehavior.LinkCanceled(Telerik.Windows.Controls.Scheduling.SchedulingLinkState)">
            <summary>
            When overridden in a derived class cleans up a cancelled link operation. This method is called only in the context of the link source control.
            </summary>
            <param name="state">SchedulingLinkState that provides context for the current operation.</param>
            <remarks>
            This method is called only when the link operation is cancelled by the user. If this method is called, the Link method is not called.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDependenciesBehavior.CanLink(Telerik.Windows.Controls.Scheduling.SchedulingLinkState)">
            <summary>
            Gets the value specifying whether the link operation can be completed, or not.
            </summary>
            <param name="state">SchedulingLinkState identifying the current link operation.</param>
            <returns>True when the link operation can be finished, otherwise false.</returns>
            <remarks>
            This method is called on every mouse move.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDependenciesBehavior.CreateInstanceOverride">
            <summary>
            When implemented in a derived class, creates a new instance of the Telerik.Windows.Core.PlatformIndependentFreezable derived class.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.GanttDragDropBehavior">
            <summary>
            The behavior class responsive for the drag-drop operation in the RadGanttView control.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDropBehavior.TryApplyReorder(Telerik.Windows.Controls.Scheduling.SchedulingDragDropState,Telerik.Windows.Controls.IDateRange,System.Object)">
            <summary>
            This method applies the changes for the reorder operation in the grid section.
            </summary>
            <param name="state">The SchedulingInteractionState state used for the operation.</param>
            <param name="draggedItem">The item which is being dragged.</param>
            <param name="destinationGroupKey">The destination groupKey, where the dragged item is being dropped.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDropBehavior.CanDrop(Telerik.Windows.Controls.Scheduling.SchedulingDragDropState)">
            <summary>
            Gets the value specifying whether the drag operation can be completed, or not.
            </summary>
            <param name="state">DragDropState identifying the current drag operation.</param>
            <returns>True when the drag operation can be finished, otherwise false.</returns>
            <remarks>
            This method is called on every mouse move. All properties in the DragDropState are valid.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttDragDropBehavior.Drop(Telerik.Windows.Controls.Scheduling.SchedulingDragDropState)">
            <summary>
            When overridden in a derived class completes the drop operation. This method is called only in the context of the drop target control.
            </summary>
            <param name="state">DragDropState that provides context for the current operation.</param>
            <remarks>
            When the drag source and the drop target are the same control, this method is called before DragCompleted.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.GanttDataConverter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.GanttTask">
            <summary>
            Represents the objects displayed from RadGanttView control. Every task can be a milestone, summary and a regular task.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.GanttTask"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.#ctor(System.DateTime,System.DateTime)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.GanttTask"/> class.
            </summary>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.#ctor(System.DateTime,System.DateTime,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.GanttTask"/> class.
            </summary>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <param name="title">The title.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Start">
            <summary>
            Gets or sets the start.
            </summary>
            <value>
            The start.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.End">
            <summary>
            Gets or sets the end.
            </summary>
            <value>
            The end.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.IsExpired">
            <summary>
            Gets whether the GanttTask is expired, i.e. its Start or End is after the Deadline date.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Children">
            <summary>
            Gets the children (subtasks).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Dependencies">
            <summary>
            Collection with relations to other tasks.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Resources">
            <summary>
            Collection with all resources associated with this task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Duration">
            <summary>
            Gets or set the duration of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Title">
            <summary>
            Gets or set the title of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Description">
            <summary>
            Gets or set the description of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Progress">
            <summary>
            Gets or set the progress in percent of completion of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Deadline">
            <summary>
            Gets or set the deadline date of the task. This property is also used for calculating the critical path.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.IsMilestone">
            <summary>
            Gets or sets a value indicating whether the instance is a milestone.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.IsSummary">
            <summary>
            Gets a value indicating whether the instance is a summary.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.UniqueId">
            <summary>
            Gets or sets the unique id.
            </summary>
            <value>
            The unique id.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Telerik#Windows#Core#IHierarchical#Children">
            <summary>
            Gets the children (subtasks).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Telerik#Windows#Controls#GanttView#IGanttTask#Dependencies">
            <summary>
            Collection with relations to other tasks.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.GanttTask.Telerik#Windows#Controls#Scheduling#IResourceContainer#Resources">
            <summary>
            Gets the resources assigned to the task.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.AddDependency(Telerik.Windows.Controls.GanttView.IGanttTask,Telerik.Windows.Controls.GanttView.DependencyType)">
            <summary>
            Adds a dependency to the task. Returns the created dependency.
            </summary>
            <param name="fromTask">A <see cref="T:Telerik.Windows.Controls.GanttView.IGanttTask"/> from which the dependency will be added. </param>
            <param name="type">The type of the dependency.</param>
            <returns>Returns an object the added dependency.</returns>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttTask.RemoveDependency(Telerik.Windows.Controls.GanttView.IDependency)">
            <summary>
            Removes a dependency from a task.
            </summary>
            <param name="dependency">The dependency to be removed.</param>
            <returns>Returns true if item is successfully removed; otherwise, false. This method also returns false if item was not found into the collection of dependencies.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.IGanttTask">
            <summary>
            Defines basic methods and properties of a gantt task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Dependencies">
            <summary>
            Collection with dependencies from other tasks.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Duration">
            <summary>
            Gets or set the duration of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Title">
            <summary>
            Gets or set the title of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Description">
            <summary>
            Gets or set the description of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Progress">
            <summary>
            Gets or set the progress in percent of completion of the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IGanttTask.Deadline">
            <summary>
             Gets or set the deadline date of the task. This property is also used for calculating the critical path.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.IMilestone">
            <summary>
            Interface representing objects that can be set as a milestone.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IMilestone.IsMilestone">
            <summary>
            Gets or sets a value indicating whether the instance is a milestone.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.IDependency">
            <summary>
            Interface representing objects that define relation between two tasks.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IDependency.FromTask">
            <summary>
            Represents the destination task of relation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IDependency.Type">
            <summary>
            Represents the type of relation between two tasks,
            such as finish to start, start to finish,
            start to start, and finish to finish. 
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ISummary">
            <summary>
            Interface representing objects that can be a summary and can have children.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ISummary.IsSummary">
            <summary>
            Gets a value indicating whether the instance is a summary.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.DependencyType">
            <summary>
             Represents the type of relation between two tasks, such as finish to start, start to finish, start to start, and finish to finish. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DependencyType.FinishStart">
            <summary>
            Finish to start type of relation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DependencyType.StartStart">
            <summary>
            Start to start type of relation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DependencyType.FinishFinish">
            <summary>
            Finish to finish type of relation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.DependencyType.StartFinish">
            <summary>
            Start to finish type of relation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.RelationSlot">
            <summary>
            Class representing a slot of the dragged relation.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.IsRelationFromEnd">
            <summary>
            Gets or sets a value determining whether the relation is from the Source's start or end.
            </summary>
            <value>True if the target is relation is from Source's end; false otherwise.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.IsRelationToEnd">
            <summary>
            Gets or sets a value determining whether the relation is to the Target's start or end.
            </summary>
            <value>True if the target is relation is to Target's end; false otherwise.</value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.Start">
            <summary>
            Gets or sets the start.
            </summary>
            <value>
            The start.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.End">
            <summary>
            Gets or sets the end.
            </summary>
            <value>
            The end.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.Source">
            <summary>
            Gets the IGanttTask from which the relation began.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationSlot.Target">
            <summary>
            Gets the IGanttTask where the relation ends.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.GanttItemsPresenter.AutoSizeTimelineProperty">
            <summary>
            Identifies the AutoSizeTimeline dependency property. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttItemsPresenter.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.GanttItemsPresenter.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.IImageExporter">
            <summary>
            Interface that provides the image exporter object which is returned after calling<see cref="M:Telerik.Windows.Controls.GanttView.ImageExportingService.BeginExporting(Telerik.Windows.Controls.GanttView.ImageExportSettings)"/> method.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IImageExporter.ImageInfos">
            <summary>
            Gets the image wrappers which are used to export an BitmapSource.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IImageExporter.Columns">
            <summary>
            Gets how many columns are generated using the target area.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.IImageExporter.Rows">
            <summary>
            Gets how manu rows are generated using the target area.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.IImageExporter.ExportToImage(System.Windows.Rect)">
            <summary>
            Export target area from <see cref="T:Telerik.Windows.Controls.RadGanttView"/> as BitmapSource.
            </summary>
            <param name="targetArea">The visual part of the GanttView.</param>
            <returns>BitmapSource object.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ImageExportingService">
            <summary>
            Provides basic functionality for exporting images from <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ImageExportingService.BeginExporting(Telerik.Windows.Controls.GanttView.ImageExportSettings)">
            <summary>
            Calling this method initialize exporting and prepare an object which is used for performing exporting.
            </summary>
            <param name="imageExportSettings">The image exporting settings.</param>
            <returns><see cref="T:Telerik.Windows.Controls.GanttView.IImageExporter"/> object which is used to export the images.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.GanttArea">
            <summary>
            Enumerates the areas in <see cref="T:Telerik.Windows.Controls.RadGanttView"/> control. This enum is used in <see cref="T:Telerik.Windows.Controls.GanttView.ImageExportSettings"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ImageExportSettings">
            <summary>
            These settings are used for exporting images using <see cref="T:Telerik.Windows.Controls.GanttView.ImageExportingService"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ImageExportSettings.ExportArea">
            <summary>
            Gets the area which will be exported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ImageExportSettings.ImageSize">
            <summary>
            Gets the size of the exported image.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.ImageExportSettings.ShowHeaders">
            <summary>
            Gets whether to export the headers or not.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.ImageInfo">
            <summary>
            Represents wrapper which helps exporting images.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.ImageInfo.Export">
            <summary>
            Exports BitmapSource for this object.
            </summary>
            <returns>The BitmapSource object.</returns>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea">
            <summary>
            Represents an enumeration for the areas of the <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.None">
            <summary>
            None.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.TimeLineArea">
            <summary>
            The TimeLine area of the <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.GridViewArea">
            <summary>
            The GridView area of the <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.Scrolling.VerticalScrollPosition">
            <summary>
            Represents an enumeration of the vertical scroll positions available for the <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.ScrollSettings"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.Scrolling.HorizontalScrollPosition">
            <summary>
            Represents an enumeration of the horizontal scroll positions available for the <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.ScrollSettings"/>.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs.#ctor(System.Windows.RoutedEvent,Telerik.Windows.Controls.GanttView.IGanttTask)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event.</param>
            <param name="task">The task.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,Telerik.Windows.Controls.GanttView.IGanttTask)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event.</param>
            <param name="source">The source.</param>
            <param name="task">The task.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.TaskEditedEventArgs.Task">
            <summary>
            Gets the task.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs.#ctor(System.Windows.RoutedEvent,Telerik.Windows.Controls.GanttView.IGanttTask)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event.</param>
            <param name="task">The task.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs.#ctor(System.Windows.RoutedEvent,System.Object,Telerik.Windows.Controls.GanttView.IGanttTask)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs"/> class.
            </summary>
            <param name="routedEvent">The routed event.</param>
            <param name="source">The source.</param>
            <param name="task">The task.</param>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.TaskEditingEventArgs.Task">
            <summary>
            Gets the task.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationInfo.IsTimeReversed">
            <summary>
            True if arrow starts from the "left" side of the "left" appointment (i.e. should have curve at this side); otherwise false.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.GanttView.RelationInfo.IsSameSide">
            <summary>
            True if the relation is StartStart or EndEnd; false otherwise.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.DateTimeToCurrentUICultureStringConverter">
            <summary>
            The class converts DateTime values to their string representation using the <see cref="P:System.Globalization.CultureInfo.CurrentUICulture"/>.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.DateTimeToCurrentUICultureStringConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Represents the converter that turns a DateTime values to the appropriate UI Culture set in your application.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.UpperCaseStringConverter">
            <summary>
            Represents the converter that converts strings to uppercase.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TaskEditingEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadGanttView.TaskEditing"/> event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TaskEditedEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadGanttView.TaskEdited"/> event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TaskSavingEvent">
            <summary>
            Identifies the <see cref="E:Telerik.Windows.Controls.RadGanttView.TaskSaving"/> event.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.SelectionChangedEvent">
            <summary>
            Identifies the SelectionChanged routed event. 
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadGanttView.TaskEditing">
            <summary>
            Raised when a task editing is initiated through the UI.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadGanttView.TaskEdited">
            <summary>
            Raised when a task has been edited through the UI.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadGanttView.TaskSaving">
            <summary>
            Raised when edit operation through the UI is being committed.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Controls.RadGanttView.SelectionChanged">
            <summary>
            Occurs when the selection of a Selector changes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.SelectedItemProperty">
            <summary>
            Identifies the SelectedItem dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.ColumnsProperty">
            <summary>
            Identifies the ColumnsProperty dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeRulerLinesProperty">
            <summary>
            Identifies the TimeRulerLines dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TasksSourceProperty">
            <summary>
            Identifies the TasksSource dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.GapBetweenRowsProperty">
            <summary>
            Identifies the GapBetweenRows dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.GapBetweenColumnsProperty">
            <summary>
            Identifies the GapBetweenColumns dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.PixelLengthProperty">
            <summary>
            Identifies the PixelLength dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.ShowCurrentHourIndicatorProperty">
            <summary>
            Identifies the ShowCurrentHourIndicator dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.FirstDayOfWeekProperty">
            <summary>
            Identifies the FirstDayOfWeek dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.VisibleRangeProperty">
            <summary>
            Identifies the VisibleRange dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.HighlightedItemsSourceProperty">
            <summary>
            Identifies the HighlightedItemsSource dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.AutoSizeTimelineProperty">
            <summary>
            Identifies the AutoSizeTimeline dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeLineFilteringBehaviorProperty">
            <summary>
            Identifies the TimeLineFilteringBehavior dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.SpecialSlotsGeneratorProperty">
            <summary>
            Identifies the SpecialSlotsGenerator dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.HighlightsBehaviorProperty">
            <summary>
            Identifies the HighlightsBehavior dependency property. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.DragVisualStyleProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.DragVisualStyle"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.ResizeVisualStyleProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.ResizeVisualStyle"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.DragDropBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.DragDropBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.DragDependenciesBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.DragDependenciesBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.ResizeBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.ResizeBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.DataConverterProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.DataConverter"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.SelectionBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.SelectionBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeRulerVisualizationBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.TimeRulerVisualizationBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeLineVisualizationBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.TimeLineVisualizationBehavior"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeRulerContainerSelectorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.TimeRulerContainerSelector"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.TimeLineContainerSelectorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.TimelineContainerSelector"/> dependency property.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Controls.RadGanttView.InitialExpandBehaviorProperty">
            <summary>
            Identifies the <see cref="P:Telerik.Windows.Controls.RadGanttView.InitialExpandBehavior"/> dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.ExportingService">
            <summary>
            Gets the service, responsible for image exporting. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.ScrollingService">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService"/> used by the <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.DragDropBehavior">
            <summary>
            Gets or sets the drag-drop behavior that defines how RadGanttView will perform drag.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.DragDependenciesBehavior">
            <summary>
            Gets or sets the drag dependencies behavior that defines how RadGanttView will perform creating dependencies with mouse.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.ResizeBehavior">
            <summary>
            Gets or sets the resize behavior that defines how RadGanttView will perform resize.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.DragVisualStyle">
            <summary>
            Gets or sets the DragVisualStyle property. It defines the look and behavior for the visual element that is displayed when dragging tasks.
            The default value is null. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.ResizeVisualStyle">
            <summary>
            Gets or sets the resize visual style.
            </summary>
            <value>
            The resize visual style.
            </value>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.GapBetweenRows">
            <summary>
            Gets or sets the distance between two consecutive rows. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.GapBetweenColumns">
            <summary>
            Gets or sets the distance between two consecutive columns. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.PixelLength">
            <summary>
            Gets or sets the time interval represented by a pixel. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.ShowCurrentHourIndicator">
            <summary>
            Gets or sets the ShowCurrentHourIndicator. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.FirstDayOfWeek">
            <summary>
            Gets or sets the FirstDayOfWeek. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.VisibleRange">
            <summary>
            Gets or sets the VisibleRange. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.TimeRulerLines">
            <summary>
            Gets or sets the TimeRulerLines. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.TimeRulerVisualizationBehavior">
            <summary>
            Gets or sets the TimeRulerBehavior. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.TimeRulerContainerSelector">
            <summary>
            Gets or sets the TimeRulerContainerSelector. This is a dependency property.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.RadGanttView.TimelineContainerSelector">
            <summary>
            Gets or sets the TimelineContainerSelector. This is a dependency property.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadGanttView.ResetTheme">
            <summary>
            Resets the theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadGanttView.OnSelectionChanged(System.Windows.Controls.SelectionChangedEventArgs)">
            <summary>
            Called when the selection changes.
            </summary>
            <param name="e">The event data.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.RadGanttView.OnInitialized(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.FrameworkElement.Initialized"/> event. This method is invoked whenever <see cref="P:System.Windows.FrameworkElement.IsInitialized"/> is set to true internally.
            </summary>
            <param name="e">The <see cref="T:System.Windows.RoutedEventArgs"/> that contains the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.RadGanttView.SettingsPropertyMetadata`1">
            <summary>
            This class is intended to be used for more simple property changed callbacks in cases when the dependency property is bound to a setting from the settings container.
            Its callback is not called if the sender is not the GanttView and its SettingsContainer is not initialized. The first parameter of the callback is the settings container
            and the second is the new value from e.NewValue.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.RadGanttView.SettingsPropertyMetadata`1.#ctor(`0,System.Action{Telerik.Windows.Controls.GanttView.GanttSettingsContainer,`0})">
            <summary>
            Initializes a new instance of the SettingsPropertyMetadata class.
            This class is intended to be used for more simple property changed callbacks in cases when the dependency property is bound to a setting from the settings container.
            Its callback is not called if the sender is not the GanttView and its SettingsContainer is not initialized. The first parameter of the callback is the settings container
            and the second is the new value from e.NewValue.
            </summary>
            <param name="defaultValue">The default value to be used.</param>
            <param name="propertyChangedCallback">The callback to be called when the value of the property is changed. It is not called if the sender is not the GanttView and its SettingsContainer 
            is not initialized. The first parameter of the callback is the settings container and the second is the new value from e.NewValue.</param>
        </member>
        <member name="T:Telerik.Windows.Controls.Scheduling.ColumnSettings">
            <summary>
            This class represents the column setting of the GanttView.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Scheduling.DataSourceSettings">
            <summary>
            This class represents the data source of the GanttView.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService">
            <summary>
            Represents a class that provides properties and methods for scrolling the <see cref="T:Telerik.Windows.Controls.RadGanttView"/>.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollSettings">
            <summary>
            The default <see cref="P:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollSettings"/> used from the <see cref="T:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService"/> for horizontal and vertical scrolling.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollIntoView(System.Object)">
            <summary>
            Scrolls both <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/>s horizontally and vertically to the passed item using the default <see cref="P:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollSettings"/>.
            </summary>
            <param name="item">The item to which will be scrolled.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollIntoView(System.Object,Telerik.Windows.Controls.GanttView.Scrolling.ScrollSettings)">
            <summary>
            Scrolls both <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/>s horizontally and vertically to the passed item using the passed <see cref="P:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollSettings"/>.
            </summary>
            <param name="item">The item to which will be scrolled.</param>
            <param name="settings">The <see cref="P:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollSettings"/> with which to scroll.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollHorizontalTo(System.Double,Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea)">
            <summary>
            Scrolls the passed <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/> horizontally with the specified offset (in pixels).
            </summary>
            <param name="offset">The offset (in pixels).</param>
            <param name="ganttArea">The <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/> that will be scrolled.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollVerticalTo(System.Double)">
            <summary>
            Scrolls both <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/>s vertically from the initial scroll position with the specified offset (in pixels).
            </summary>
            <param name="offset">The offset (in pixels).</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollVerticalWith(System.Double)">
            <summary>
            Scrolls both <see cref="T:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea"/>s vertically from the current scroll position with the specified offset (in pixels).
            </summary>
            <param name="offset">The offset (in pixels).</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollToDateTime(System.DateTime)">
            <summary>
            Scrolls the <see cref="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.TimeLineArea"/> to the specified <see cref="T:System.DateTime"/>.
            </summary>
            <param name="dateTime">The <see cref="T:System.DateTime"/> to which to scroll.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollToColumn(System.Int32)">
            <summary>
            Scrolls the <see cref="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.GridViewArea"/> to the specified by its index column.
            </summary>
            <param name="columnIndex">The column index to which to scroll.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollToColumn(Telerik.Windows.Controls.GanttView.ColumnDefinitionBase)">
            <summary>
            Scrolls the <see cref="F:Telerik.Windows.Controls.GanttView.Scrolling.GanttScrollArea.GridViewArea"/> to the specified by its <see cref="T:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase"/> column.
            </summary>
            <param name="columnDefinition">The <see cref="T:Telerik.Windows.Controls.GanttView.ColumnDefinitionBase"/> to which to scroll.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ScrollToRow(System.Int32)">
            <summary>
            Scrolls vertically so that the specified by its index row is made visible.
            </summary>
            <param name="rowIndex">The row index to scroll to.</param>
        </member>
        <member name="M:Telerik.Windows.Controls.Gantt.Scrolling.GanttScrollingService.ResumeExecution">
            <summary>
            Invokes the queued actions.
            </summary>
        </member>
    </members>
</doc>
