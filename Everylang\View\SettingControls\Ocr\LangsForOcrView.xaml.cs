﻿using Everylang.App.OCR;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;

namespace Everylang.App.View.SettingControls.Ocr
{
    /// <summary>
    /// Interaction logic for LangsForOcrView.xaml
    /// </summary>
    internal partial class LangsForOcrView
    {
        internal static readonly RoutedEvent HidePanelEvent = EventManager.RegisterRoutedEvent("HidePanel",
            RoutingStrategy.Direct, typeof(RoutedEventHandler), typeof(LangsForOcrView));

        private ObservableCollection<CheckedListItem<Language>>? _languages;

        internal event RoutedEventHandler HidePanel
        {
            add { AddHandler(HidePanelEvent, value); }
            remove { RemoveHandler(HidePanelEvent, value); }
        }

        public ObservableCollection<CheckedListItem<Language>> Languages
        {
            get
            {
                if (_languages == null)
                {
                    _languages = new ObservableCollection<CheckedListItem<Language>>();
                    foreach (var lang in OcrManager.Instance.LangsAll)
                    {
                        _languages.Add(new CheckedListItem<Language>(new Language(lang), OcrManager.Instance.LangsAllForRequest.Contains(lang)));
                    }
                }
                return _languages;
            }
            set => _languages = value;
        }

        internal LangsForOcrView()
        {
            InitializeComponent();
        }

        internal void ApplyLanguages()
        {
            OcrManager.Instance.LangsAllForRequest.Clear();
            for (var i = 0; i < Languages.Count; i++)
            {
                if (Languages[i].IsChecked)
                {
                    OcrManager.Instance.LangsAllForRequest.Add(Languages[i].Item.Name);
                }
            }
        }

        private void HidePanelButtonClick(object sender, RoutedEventArgs e)
        {
            RoutedEventArgs newEventArgs = new RoutedEventArgs(HidePanelEvent);
            RaiseEvent(newEventArgs);
        }

        private void ToggleButton_OnChecked(object sender, RoutedEventArgs e)
        {
            ApplyLanguages();
        }

        internal class CheckedListItem<T> : INotifyPropertyChanged
        {
            public event PropertyChangedEventHandler? PropertyChanged;

            private bool _isChecked;
            private T _item;

            internal CheckedListItem(T item, bool isChecked)
            {
                this._item = item;
                this._isChecked = isChecked;
            }

            public T Item
            {
                get { return _item; }
                set
                {
                    _item = value;
                    if (PropertyChanged != null) PropertyChanged(this, new PropertyChangedEventArgs("Item"));
                }
            }

            public bool IsChecked
            {
                get { return _isChecked; }
                set
                {
                    _isChecked = value;
                    if (PropertyChanged != null) PropertyChanged(this, new PropertyChangedEventArgs("IsChecked"));
                }
            }
        }

        internal new record Language
        {
            internal Language(string name)
            {
                Name = name;
            }

            public string Name { get; set; }
        }
    }


}
