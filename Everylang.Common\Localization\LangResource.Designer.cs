﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Everylang.Common.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class LangResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public LangResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Everylang.Common.Localization.LangResource", typeof(LangResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string About {
            get {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the welcome window.
        /// </summary>
        public static string AboutOpenStartWindow {
            get {
                return ResourceManager.GetString("AboutOpenStartWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset all program settings.
        /// </summary>
        public static string AboutResetSettings {
            get {
                return ResourceManager.GetString("AboutResetSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset all settings and program data?.
        /// </summary>
        public static string AboutResetSettingsQuestion {
            get {
                return ResourceManager.GetString("AboutResetSettingsQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ACKNOWLEDGEMENTS.
        /// </summary>
        public static string AboutSettingsAckowledgementsHeader {
            get {
                return ResourceManager.GetString("AboutSettingsAckowledgementsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONTACT.
        /// </summary>
        public static string AboutSettingsContactHeader {
            get {
                return ResourceManager.GetString("AboutSettingsContactHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact form.
        /// </summary>
        public static string AboutSettingsContactLink {
            get {
                return ResourceManager.GetString("AboutSettingsContactLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit your comments and/or questions.
        /// </summary>
        public static string AboutSettingsContactText {
            get {
                return ResourceManager.GetString("AboutSettingsContactText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program is a free tool allowing you to translate texts, spell check and switch the layout in any programs.
        /// </summary>
        public static string AboutSettingsDesc {
            get {
                return ResourceManager.GetString("AboutSettingsDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ABOUT.
        /// </summary>
        public static string AboutSettingsHeader {
            get {
                return ResourceManager.GetString("AboutSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program is started as an administrator.
        /// </summary>
        public static string AboutSettingsIsAdmin {
            get {
                return ResourceManager.GetString("AboutSettingsIsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program is not started as an administrator.
        /// </summary>
        public static string AboutSettingsIsNotAdmin {
            get {
                return ResourceManager.GetString("AboutSettingsIsNotAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License agreement.
        /// </summary>
        public static string AboutSettingsLicense {
            get {
                return ResourceManager.GetString("AboutSettingsLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string AboutSettingsUpdate {
            get {
                return ResourceManager.GetString("AboutSettingsUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update available.
        /// </summary>
        public static string AboutSettingsUpdateAvailable {
            get {
                return ResourceManager.GetString("AboutSettingsUpdateAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version:.
        /// </summary>
        public static string AboutSettingsVersion {
            get {
                return ResourceManager.GetString("AboutSettingsVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string AboutTab {
            get {
                return ResourceManager.GetString("AboutTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adjective.
        /// </summary>
        public static string adjective {
            get {
                return ResourceManager.GetString("adjective", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adverb.
        /// </summary>
        public static string adverb {
            get {
                return ResourceManager.GetString("adverb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All applications.
        /// </summary>
        public static string AllApp {
            get {
                return ResourceManager.GetString("AllApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appearance.
        /// </summary>
        public static string AppearanceTab {
            get {
                return ResourceManager.GetString("AppearanceTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For the correct operation of the program with all applications, you must run it as an administrator.
        /// </summary>
        public static string AppNotAsAdmin {
            get {
                return ResourceManager.GetString("AppNotAsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The application is updated, list of changes on the site.
        /// </summary>
        public static string AppUpdated {
            get {
                return ResourceManager.GetString("AppUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string AutochangeAddNew {
            get {
                return ResourceManager.GetString("AutochangeAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consider the case of letters.
        /// </summary>
        public static string AutochangeCaseLetters {
            get {
                return ResourceManager.GetString("AutochangeCaseLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace by:.
        /// </summary>
        public static string AutochangeChangeMethods {
            get {
                return ResourceManager.GetString("AutochangeChangeMethods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string AutochangeDelete {
            get {
                return ResourceManager.GetString("AutochangeDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete all snippets with this tag?.
        /// </summary>
        public static string AutochangeDelWithTag {
            get {
                return ResourceManager.GetString("AutochangeDelWithTag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change.
        /// </summary>
        public static string AutochangeEdit {
            get {
                return ResourceManager.GetString("AutochangeEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippet editor.
        /// </summary>
        public static string AutochangeEditor {
            get {
                return ResourceManager.GetString("AutochangeEditor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What replaced.
        /// </summary>
        public static string AutochangeHeaderFromText {
            get {
                return ResourceManager.GetString("AutochangeHeaderFromText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What to replace.
        /// </summary>
        public static string AutochangeHeaderToText {
            get {
                return ResourceManager.GetString("AutochangeHeaderToText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string AutochangeHelperCancel {
            get {
                return ResourceManager.GetString("AutochangeHelperCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace when typing.
        /// </summary>
        public static string AutochangeHelperChangeAtOnce {
            get {
                return ResourceManager.GetString("AutochangeHelperChangeAtOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description:.
        /// </summary>
        public static string AutochangeHelperDesc {
            get {
                return ResourceManager.GetString("AutochangeHelperDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text to replace (not necessary):.
        /// </summary>
        public static string AutochangeHelperFromText {
            get {
                return ResourceManager.GetString("AutochangeHelperFromText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text to replace:.
        /// </summary>
        public static string AutochangeHelperFromTextTootlTip {
            get {
                return ResourceManager.GetString("AutochangeHelperFromTextTootlTip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What language to switch the layout to.
        /// </summary>
        public static string AutochangeHelperLangListDesc {
            get {
                return ResourceManager.GetString("AutochangeHelperLangListDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do not switch.
        /// </summary>
        public static string AutochangeHelperLangListNoSwitch {
            get {
                return ResourceManager.GetString("AutochangeHelperLangListNoSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string AutochangeHelperOk {
            get {
                return ResourceManager.GetString("AutochangeHelperOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save cursor position.
        /// </summary>
        public static string AutochangeHelperSaveCursorPosition {
            get {
                return ResourceManager.GetString("AutochangeHelperSaveCursorPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags (by spaces):.
        /// </summary>
        public static string AutochangeHelperTags {
            get {
                return ResourceManager.GetString("AutochangeHelperTags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string AutochangeHelperTagsWatermark {
            get {
                return ResourceManager.GetString("AutochangeHelperTagsWatermark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippet text:.
        /// </summary>
        public static string AutochangeHelperToText {
            get {
                return ResourceManager.GetString("AutochangeHelperToText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort according to the frequency of use.
        /// </summary>
        public static string AutochangeIsEnabledCountUsage {
            get {
                return ResourceManager.GetString("AutochangeIsEnabledCountUsage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets is disabled.
        /// </summary>
        public static string AutochangeIsOff {
            get {
                return ResourceManager.GetString("AutochangeIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets is enabled.
        /// </summary>
        public static string AutochangeIsOn {
            get {
                return ResourceManager.GetString("AutochangeIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the snippets list to insert.
        /// </summary>
        public static string AutochangeKeyboardShortcuts {
            get {
                return ResourceManager.GetString("AutochangeKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add the selected text in the snippets.
        /// </summary>
        public static string AutochangeKeyboardShortcutsAddNew {
            get {
                return ResourceManager.GetString("AutochangeKeyboardShortcutsAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto text replacement is on.
        /// </summary>
        public static string AutochangeOnInSnippetsList {
            get {
                return ResourceManager.GetString("AutochangeOnInSnippetsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key Enter.
        /// </summary>
        public static string AutochangeOnInter {
            get {
                return ResourceManager.GetString("AutochangeOnInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Space.
        /// </summary>
        public static string AutochangeOnSpace {
            get {
                return ResourceManager.GetString("AutochangeOnSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Space or Enter.
        /// </summary>
        public static string AutochangeOnSpaceOrInter {
            get {
                return ResourceManager.GetString("AutochangeOnSpaceOrInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key Tab.
        /// </summary>
        public static string AutochangeOnTab {
            get {
                return ResourceManager.GetString("AutochangeOnTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tab or Enter.
        /// </summary>
        public static string AutochangeOnTabOrInter {
            get {
                return ResourceManager.GetString("AutochangeOnTabOrInter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace if set in a different layout.
        /// </summary>
        public static string AutochangeOtherLayout {
            get {
                return ResourceManager.GetString("AutochangeOtherLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show hint when typing.
        /// </summary>
        public static string AutochangeShowMiniWindow {
            get {
                return ResourceManager.GetString("AutochangeShowMiniWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit snippets.
        /// </summary>
        public static string AutochangeSnippetsList {
            get {
                return ResourceManager.GetString("AutochangeSnippetsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets.
        /// </summary>
        public static string AutochangeTab {
            get {
                return ResourceManager.GetString("AutochangeTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets.
        /// </summary>
        public static string AutochangeTextHeader {
            get {
                return ResourceManager.GetString("AutochangeTextHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a word to the autoswitching rules? Enter - YES.
        /// </summary>
        public static string AutoSwitchAcceptText {
            get {
                return ResourceManager.GetString("AutoSwitchAcceptText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        public static string AutoSwitcherSettingsAction {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch layout after stopping word input.
        /// </summary>
        public static string AutoSwitcherSettingsAfterPause {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAfterPause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All layouts.
        /// </summary>
        public static string AutoSwitcherSettingsAllLayouts {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsAllLayouts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string AutoSwitcherSettingsCombination {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsCombination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The number of manual switching of words to include in the rules.
        /// </summary>
        public static string AutoSwitcherSettingsCountCheckRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsCountCheckRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Don`t switch the keyboard layout if the keyboard layout was change manually.
        /// </summary>
        public static string AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Autoswitching layouts.
        /// </summary>
        public static string AutoSwitcherSettingsHeader {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Autoswitching Rules.
        /// </summary>
        public static string AutoSwitcherSettingsHelpWindowTitle {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsHelpWindowTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically add switching rules.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnAddingRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnAddingRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct after pressing the Enter button.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnByEnter {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnByEnter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fix accidental pressing of Caps Lock.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnFixWrongUpperCase {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnFixWrongUpperCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct two capital letters at the beginning of words.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnTwoUpperCaseLetters {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnTwoUpperCaseLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do not correct if all the letters in a word uppercase.
        /// </summary>
        public static string AutoSwitcherSettingsIsOnUpperCaseNotSwitch {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsOnUpperCaseNotSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add one-letter words to the rules.
        /// </summary>
        public static string AutoSwitcherSettingsIsSwitchOneLetter {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsIsSwitchOneLetter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List of rules for autoswitching.
        /// </summary>
        public static string AutoSwitcherSettingsListRulesHeader {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsListRulesHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show candidates.
        /// </summary>
        public static string AutoSwitcherSettingsListRulesHeaderShowAll {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsListRulesHeaderShowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch layout only after entering the whole word.
        /// </summary>
        public static string AutoSwitcherSettingsOnlyAfterSeparator {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsOnlyAfterSeparator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View list of autoswitching rules.
        /// </summary>
        public static string AutoSwitcherSettingsOpenRulesList {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsOpenRulesList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete all the switching rules.
        /// </summary>
        public static string AutoSwitcherSettingsResetRule {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsResetRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionConvert {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionConvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candidate.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionIntermediate {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionIntermediate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do not switch.
        /// </summary>
        public static string AutoSwitcherSettingsRuleActionNotConvert {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsRuleActionNotConvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add rules only after confirming.
        /// </summary>
        public static string AutoSwitcherSettingsShowAcceptWindow {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsShowAcceptWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The list of languages ​​for which will run autoswitching.
        /// </summary>
        public static string AutoSwitcherSettingsTrueListOfLang {
            get {
                return ResourceManager.GetString("AutoSwitcherSettingsTrueListOfLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Autoswitching layouts.
        /// </summary>
        public static string AutoSwitcherTab {
            get {
                return ResourceManager.GetString("AutoSwitcherTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto-switching of the layout is disabled.
        /// </summary>
        public static string AutoSwitchSettingsIsOff {
            get {
                return ResourceManager.GetString("AutoSwitchSettingsIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto-switching of the layout is enabled.
        /// </summary>
        public static string AutoSwitchSettingsIsOn {
            get {
                return ResourceManager.GetString("AutoSwitchSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auxiliary verb.
        /// </summary>
        public static string auxiliary_verb {
            get {
                return ResourceManager.GetString("auxiliary verb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available a new version, restart application.
        /// </summary>
        public static string AvailableNewVersion {
            get {
                return ResourceManager.GetString("AvailableNewVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        public static string bCopy {
            get {
                return ResourceManager.GetString("bCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To split the text on newlines.
        /// </summary>
        public static string BreakInterButton {
            get {
                return ResourceManager.GetString("BreakInterButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To split the text on whitespace.
        /// </summary>
        public static string BreakSpaceButton {
            get {
                return ResourceManager.GetString("BreakSpaceButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace.
        /// </summary>
        public static string bReplace {
            get {
                return ResourceManager.GetString("bReplace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace all.
        /// </summary>
        public static string bReplaceAll {
            get {
                return ResourceManager.GetString("bReplaceAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text.
        /// </summary>
        public static string bReplaceText {
            get {
                return ResourceManager.GetString("bReplaceText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skip.
        /// </summary>
        public static string bSkip {
            get {
                return ResourceManager.GetString("bSkip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skip all.
        /// </summary>
        public static string bSkipAll {
            get {
                return ResourceManager.GetString("bSkipAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return.
        /// </summary>
        public static string buttonBack {
            get {
                return ResourceManager.GetString("buttonBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string ButtonClose {
            get {
                return ResourceManager.GetString("ButtonClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CAPSLOCK turned on.
        /// </summary>
        public static string CapsLockIsOn {
            get {
                return ResourceManager.GetString("CapsLockIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spellcheck.
        /// </summary>
        public static string CheckSpellingTab {
            get {
                return ResourceManager.GetString("CheckSpellingTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear all.
        /// </summary>
        public static string ClearAll {
            get {
                return ResourceManager.GetString("ClearAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        public static string ClearButton {
            get {
                return ResourceManager.GetString("ClearButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CLIPBOARD.
        /// </summary>
        public static string ClipboardHeaderButton {
            get {
                return ResourceManager.GetString("ClipboardHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequential inserts the text from the Clipboard to the current window (each subsequent insert will be the previous value from the clipboard).
        /// </summary>
        public static string ClipboardKeyboardRoundShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardRoundShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequential inserts the text from the Clipboard.
        /// </summary>
        public static string ClipboardKeyboardRoundShortcutsShort {
            get {
                return ResourceManager.GetString("ClipboardKeyboardRoundShortcutsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text without formatting and paste the path of the copied file.
        /// </summary>
        public static string ClipboardKeyboardShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the clipboard history by shortcuts.
        /// </summary>
        public static string ClipboardKeyboardViewShortcuts {
            get {
                return ResourceManager.GetString("ClipboardKeyboardViewShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard history size.
        /// </summary>
        public static string ClipboardMaxClipboardItems {
            get {
                return ResourceManager.GetString("ClipboardMaxClipboardItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard.
        /// </summary>
        public static string ClipboardMenuItem {
            get {
                return ResourceManager.GetString("ClipboardMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard Manager is disabled.
        /// </summary>
        public static string ClipboardOff {
            get {
                return ResourceManager.GetString("ClipboardOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard Manager is enabled.
        /// </summary>
        public static string ClipboardOn {
            get {
                return ResourceManager.GetString("ClipboardOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard Manager.
        /// </summary>
        public static string ClipboardSettingsHeader {
            get {
                return ResourceManager.GetString("ClipboardSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text by Ctrl+Shift+(number) - number 1, 2, 3, 4, 5, 6, 7, 8, 9 - the index of the entry in the clipboard history.
        /// </summary>
        public static string ClipboardSettingsPasteByIndexIsOn {
            get {
                return ResourceManager.GetString("ClipboardSettingsPasteByIndexIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When inserting text from the history, do not replace the current value in the clipboard.
        /// </summary>
        public static string ClipboardSettingsReplaceWithoutChangeClipboard {
            get {
                return ResourceManager.GetString("ClipboardSettingsReplaceWithoutChangeClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save the path to the copied file in the clipboard history.
        /// </summary>
        public static string ClipboardSettingsSaveFilePath {
            get {
                return ResourceManager.GetString("ClipboardSettingsSaveFilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save images in the clipboard history.
        /// </summary>
        public static string ClipboardSettingsSaveImage {
            get {
                return ResourceManager.GetString("ClipboardSettingsSaveImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The sound of the clipboard changes.
        /// </summary>
        public static string ClipboardSound {
            get {
                return ResourceManager.GetString("ClipboardSound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard.
        /// </summary>
        public static string ClipboardTab {
            get {
                return ResourceManager.GetString("ClipboardTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string CloseHeaderButton {
            get {
                return ResourceManager.GetString("CloseHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close the program?.
        /// </summary>
        public static string CloseQuestion {
            get {
                return ResourceManager.GetString("CloseQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To insert text, press the key.
        /// </summary>
        public static string CommonWindowPressKeyForPast {
            get {
                return ResourceManager.GetString("CommonWindowPressKeyForPast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conjunction.
        /// </summary>
        public static string conjunction {
            get {
                return ResourceManager.GetString("conjunction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string ConverterAdd {
            get {
                return ResourceManager.GetString("ConverterAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string ConverterDelete {
            get {
                return ResourceManager.GetString("ConverterDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On right.
        /// </summary>
        public static string ConverterEnd {
            get {
                return ResourceManager.GetString("ConverterEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keyboard shortcuts.
        /// </summary>
        public static string ConverterKeyboardShortcuts {
            get {
                return ResourceManager.GetString("ConverterKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search and replace in selected text.
        /// </summary>
        public static string ConverterReplaceSelText {
            get {
                return ResourceManager.GetString("ConverterReplaceSelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to text for example.
        /// </summary>
        public static string ConverterSampleText {
            get {
                return ResourceManager.GetString("ConverterSampleText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert text to CamelCase style.
        /// </summary>
        public static string ConverterSettingsCamelCase {
            get {
                return ResourceManager.GetString("ConverterSettingsCamelCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert depending on the current keyboard layout.
        /// </summary>
        public static string ConverterSettingsConvertDependsOnKeyboardLayout {
            get {
                return ResourceManager.GetString("ConverterSettingsConvertDependsOnKeyboardLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConverterSettingsDescr {
            get {
                return ResourceManager.GetString("ConverterSettingsDescr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Framing the selected text.
        /// </summary>
        public static string ConverterSettingsEncloseTextQuotationMarks {
            get {
                return ResourceManager.GetString("ConverterSettingsEncloseTextQuotationMarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert numbers and dates to strings, evaluate expressions.
        /// </summary>
        public static string ConverterSettingsExpression {
            get {
                return ResourceManager.GetString("ConverterSettingsExpression", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string ConverterSettingsExpressionHelp {
            get {
                return ResourceManager.GetString("ConverterSettingsExpressionHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert dates.
        /// </summary>
        public static string ConverterSettingsForDateTime {
            get {
                return ResourceManager.GetString("ConverterSettingsForDateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculating expressions.
        /// </summary>
        public static string ConverterSettingsForExpression {
            get {
                return ResourceManager.GetString("ConverterSettingsForExpression", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert numbers.
        /// </summary>
        public static string ConverterSettingsForNumber {
            get {
                return ResourceManager.GetString("ConverterSettingsForNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text converter.
        /// </summary>
        public static string ConverterSettingsHeader {
            get {
                return ResourceManager.GetString("ConverterSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Converter is on.
        /// </summary>
        public static string ConverterSettingsIsOn {
            get {
                return ResourceManager.GetString("ConverterSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lowercase the first character of the word under the cursor.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsFirstLetterToDown {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uppercase the first character of the word under the cursor.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsFirstLetterToUp {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure hotkeys.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchCapsSettings {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchCapsSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert selected text to lowercase.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invert the case of the selected text.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert selected text to uppercase.
        /// </summary>
        public static string ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp {
            get {
                return ResourceManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transliteration of selected text.
        /// </summary>
        public static string ConverterSettingsTransliteration {
            get {
                return ResourceManager.GetString("ConverterSettingsTransliteration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On left.
        /// </summary>
        public static string ConverterStart {
            get {
                return ResourceManager.GetString("ConverterStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text converter.
        /// </summary>
        public static string ConverterTab {
            get {
                return ResourceManager.GetString("ConverterTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy text.
        /// </summary>
        public static string CopyButton {
            get {
                return ResourceManager.GetString("CopyButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy Html.
        /// </summary>
        public static string CopyButtonHtml {
            get {
                return ResourceManager.GetString("CopyButtonHtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy Rtf.
        /// </summary>
        public static string CopyButtonRtf {
            get {
                return ResourceManager.GetString("CopyButtonRtf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy translated text.
        /// </summary>
        public static string CopyTranslatedText {
            get {
                return ResourceManager.GetString("CopyTranslatedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary.
        /// </summary>
        public static string DiareTab {
            get {
                return ResourceManager.GetString("DiareTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to APPLICATION.
        /// </summary>
        public static string DiaryHeaderApp {
            get {
                return ResourceManager.GetString("DiaryHeaderApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DIARY.
        /// </summary>
        public static string DiaryHeaderButton {
            get {
                return ResourceManager.GetString("DiaryHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DATETIME.
        /// </summary>
        public static string DiaryHeaderDate {
            get {
                return ResourceManager.GetString("DiaryHeaderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TYPE.
        /// </summary>
        public static string DiaryHeaderFormat {
            get {
                return ResourceManager.GetString("DiaryHeaderFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TEXT.
        /// </summary>
        public static string DiaryHeaderText {
            get {
                return ResourceManager.GetString("DiaryHeaderText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary is disabled.
        /// </summary>
        public static string DiaryIsOff {
            get {
                return ResourceManager.GetString("DiaryIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary is enabled.
        /// </summary>
        public static string DiaryIsOn {
            get {
                return ResourceManager.GetString("DiaryIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of entries in the diary.
        /// </summary>
        public static string DiaryMaxItems {
            get {
                return ResourceManager.GetString("DiaryMaxItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary.
        /// </summary>
        public static string DiaryMenuItem {
            get {
                return ResourceManager.GetString("DiaryMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary is off.
        /// </summary>
        public static string DiaryOff {
            get {
                return ResourceManager.GetString("DiaryOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entered password is not correct, reset current password? In this case, all data from the diary will be removed.
        /// </summary>
        public static string DiaryOldPasswordWrong {
            get {
                return ResourceManager.GetString("DiaryOldPasswordWrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary is enabled.
        /// </summary>
        public static string DiaryOn {
            get {
                return ResourceManager.GetString("DiaryOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password for the diary.
        /// </summary>
        public static string DiaryPassword {
            get {
                return ResourceManager.GetString("DiaryPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the old password for the diary.
        /// </summary>
        public static string DiaryPasswordOld {
            get {
                return ResourceManager.GetString("DiaryPasswordOld", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password was reset.
        /// </summary>
        public static string DiaryPasswordReset {
            get {
                return ResourceManager.GetString("DiaryPasswordReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New password saved.
        /// </summary>
        public static string DiaryPasswordSaved {
            get {
                return ResourceManager.GetString("DiaryPasswordSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary.
        /// </summary>
        public static string DiarySettingsHeader {
            get {
                return ResourceManager.GetString("DiarySettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open diary.
        /// </summary>
        public static string DiaryShortcuts {
            get {
                return ResourceManager.GetString("DiaryShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English.
        /// </summary>
        public static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to register the keyboard shortcuts.
        /// </summary>
        public static string ErrorHotkey {
            get {
                return ResourceManager.GetString("ErrorHotkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred, the program will be terminated. Please send a text with errors to <NAME_EMAIL>.
        /// </summary>
        public static string ErrorText {
            get {
                return ResourceManager.GetString("ErrorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        public static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press the keyboard&apos;s number key to insert the text.
        /// </summary>
        public static string FastActionIndex {
            get {
                return ResourceManager.GetString("FastActionIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tab - Search. Esc - Cancel and clear.
        /// </summary>
        public static string FastActionTextWindowSearch {
            get {
                return ResourceManager.GetString("FastActionTextWindowSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The trial period for 40 days is activated, all functions are included.
        /// </summary>
        public static string FirstStartWithEvaluate {
            get {
                return ResourceManager.GetString("FirstStartWithEvaluate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fix window.
        /// </summary>
        public static string FixWindow {
            get {
                return ResourceManager.GetString("FixWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French.
        /// </summary>
        public static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From lang:.
        /// </summary>
        public static string FromLang {
            get {
                return ResourceManager.GetString("FromLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show close button.
        /// </summary>
        public static string GeneralSettingsCanClose {
            get {
                return ResourceManager.GetString("GeneralSettingsCanClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folder for save application settings.
        /// </summary>
        public static string GeneralSettingsDataFilePath {
            get {
                return ResourceManager.GetString("GeneralSettingsDataFilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        public static string GeneralSettingsExport {
            get {
                return ResourceManager.GetString("GeneralSettingsExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export-Import settings.
        /// </summary>
        public static string GeneralSettingsExportImport {
            get {
                return ResourceManager.GetString("GeneralSettingsExportImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font.
        /// </summary>
        public static string GeneralSettingsFont {
            get {
                return ResourceManager.GetString("GeneralSettingsFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General settings.
        /// </summary>
        public static string GeneralSettingsHeader {
            get {
                return ResourceManager.GetString("GeneralSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import.
        /// </summary>
        public static string GeneralSettingsImport {
            get {
                return ResourceManager.GetString("GeneralSettingsImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check for updates.
        /// </summary>
        public static string GeneralSettingsIsCheckUpdate {
            get {
                return ResourceManager.GetString("GeneralSettingsIsCheckUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update to beta versions.
        /// </summary>
        public static string GeneralSettingsIsCheckUpdateBeta {
            get {
                return ResourceManager.GetString("GeneralSettingsIsCheckUpdateBeta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use system proxy settings.
        /// </summary>
        public static string GeneralSettingsIsProxyUseIE {
            get {
                return ResourceManager.GetString("GeneralSettingsIsProxyUseIE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable all functions in programs running in full screen mode.
        /// </summary>
        public static string GeneralSettingsIsStopWorkingFullScreen {
            get {
                return ResourceManager.GetString("GeneralSettingsIsStopWorkingFullScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The language of the program interface.
        /// </summary>
        public static string GeneralSettingsLanguageProgram {
            get {
                return ResourceManager.GetString("GeneralSettingsLanguageProgram", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restart the application to change the language?.
        /// </summary>
        public static string GeneralSettingsLanguageProgramRestart {
            get {
                return ResourceManager.GetString("GeneralSettingsLanguageProgramRestart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimize to tray.
        /// </summary>
        public static string GeneralSettingsMinimizeToTray {
            get {
                return ResourceManager.GetString("GeneralSettingsMinimizeToTray", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miscellaneous.
        /// </summary>
        public static string GeneralSettingsOther {
            get {
                return ResourceManager.GetString("GeneralSettingsOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proxy settings.
        /// </summary>
        public static string GeneralSettingsProxy {
            get {
                return ResourceManager.GetString("GeneralSettingsProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error the proxy server, change the settings.
        /// </summary>
        public static string GeneralSettingsProxyError {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter password.
        /// </summary>
        public static string GeneralSettingsProxyPassword {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the port.
        /// </summary>
        public static string GeneralSettingsProxyPort {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the server address.
        /// </summary>
        public static string GeneralSettingsProxyServer {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your username.
        /// </summary>
        public static string GeneralSettingsProxyUsername {
            get {
                return ResourceManager.GetString("GeneralSettingsProxyUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save your proxy settings.
        /// </summary>
        public static string GeneralSettingsSaveProxy {
            get {
                return ResourceManager.GetString("GeneralSettingsSaveProxy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run as administrator.
        /// </summary>
        public static string GeneralSettingsStartAdmin {
            get {
                return ResourceManager.GetString("GeneralSettingsStartAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Starting with windows.
        /// </summary>
        public static string GeneralSettingsStartUpWithWindows {
            get {
                return ResourceManager.GetString("GeneralSettingsStartUpWithWindows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theme.
        /// </summary>
        public static string GeneralSettingsTheme {
            get {
                return ResourceManager.GetString("GeneralSettingsTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Styles of design.
        /// </summary>
        public static string GeneralSettingsThemeAccent {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeAccent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day or night.
        /// </summary>
        public static string GeneralSettingsThemeDayNight {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeDayNight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restart the program to change the theme?.
        /// </summary>
        public static string GeneralSettingsThemeProgramRestart {
            get {
                return ResourceManager.GetString("GeneralSettingsThemeProgramRestart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use a dark theme in the evening hours.
        /// </summary>
        public static string GeneralSettingsUseNightTheme {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Night clock to.
        /// </summary>
        public static string GeneralSettingsUseNightThemeEnd {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightThemeEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Night clock from.
        /// </summary>
        public static string GeneralSettingsUseNightThemeStart {
            get {
                return ResourceManager.GetString("GeneralSettingsUseNightThemeStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main settings.
        /// </summary>
        public static string GeneralTab {
            get {
                return ResourceManager.GetString("GeneralTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move the result to the main window.
        /// </summary>
        public static string GoToMainWindowButton {
            get {
                return ResourceManager.GetString("GoToMainWindowButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click here to add new item.
        /// </summary>
        public static string GridViewAlwaysVisibleNewRow {
            get {
                return ResourceManager.GetString("GridViewAlwaysVisibleNewRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Filter.
        /// </summary>
        public static string GridViewClearFilter {
            get {
                return ResourceManager.GetString("GridViewClearFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Columns.
        /// </summary>
        public static string GridViewColumnsSelectionButtonTooltip {
            get {
                return ResourceManager.GetString("GridViewColumnsSelectionButtonTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        public static string GridViewFilter {
            get {
                return ResourceManager.GetString("GridViewFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to And.
        /// </summary>
        public static string GridViewFilterAnd {
            get {
                return ResourceManager.GetString("GridViewFilterAnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contains.
        /// </summary>
        public static string GridViewFilterContains {
            get {
                return ResourceManager.GetString("GridViewFilterContains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Does not contain.
        /// </summary>
        public static string GridViewFilterDoesNotContain {
            get {
                return ResourceManager.GetString("GridViewFilterDoesNotContain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ends with.
        /// </summary>
        public static string GridViewFilterEndsWith {
            get {
                return ResourceManager.GetString("GridViewFilterEndsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is contained in.
        /// </summary>
        public static string GridViewFilterIsContainedIn {
            get {
                return ResourceManager.GetString("GridViewFilterIsContainedIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is empty.
        /// </summary>
        public static string GridViewFilterIsEmpty {
            get {
                return ResourceManager.GetString("GridViewFilterIsEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is equal to.
        /// </summary>
        public static string GridViewFilterIsEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is greater than.
        /// </summary>
        public static string GridViewFilterIsGreaterThan {
            get {
                return ResourceManager.GetString("GridViewFilterIsGreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is greater than or equal to.
        /// </summary>
        public static string GridViewFilterIsGreaterThanOrEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsGreaterThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is less than.
        /// </summary>
        public static string GridViewFilterIsLessThan {
            get {
                return ResourceManager.GetString("GridViewFilterIsLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is less than or equal to.
        /// </summary>
        public static string GridViewFilterIsLessThanOrEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsLessThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is not contained in.
        /// </summary>
        public static string GridViewFilterIsNotContainedIn {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotContainedIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is not empty.
        /// </summary>
        public static string GridViewFilterIsNotEmpty {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is not equal to.
        /// </summary>
        public static string GridViewFilterIsNotEqualTo {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is not null.
        /// </summary>
        public static string GridViewFilterIsNotNull {
            get {
                return ResourceManager.GetString("GridViewFilterIsNotNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is null.
        /// </summary>
        public static string GridViewFilterIsNull {
            get {
                return ResourceManager.GetString("GridViewFilterIsNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Match case.
        /// </summary>
        public static string GridViewFilterMatchCase {
            get {
                return ResourceManager.GetString("GridViewFilterMatchCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Or.
        /// </summary>
        public static string GridViewFilterOr {
            get {
                return ResourceManager.GetString("GridViewFilterOr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        public static string GridViewFilterSelectAll {
            get {
                return ResourceManager.GetString("GridViewFilterSelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show rows with value that.
        /// </summary>
        public static string GridViewFilterShowRowsWithValueThat {
            get {
                return ResourceManager.GetString("GridViewFilterShowRowsWithValueThat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Starts with.
        /// </summary>
        public static string GridViewFilterStartsWith {
            get {
                return ResourceManager.GetString("GridViewFilterStartsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag a column header and drop it here to group by that column.
        /// </summary>
        public static string GridViewGroupPanelText {
            get {
                return ResourceManager.GetString("GridViewGroupPanelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Header.
        /// </summary>
        public static string GridViewGroupPanelTopText {
            get {
                return ResourceManager.GetString("GridViewGroupPanelTopText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grouped by:.
        /// </summary>
        public static string GridViewGroupPanelTopTextGrouped {
            get {
                return ResourceManager.GetString("GridViewGroupPanelTopTextGrouped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Text Search.
        /// </summary>
        public static string GridViewSearchPanelTopText {
            get {
                return ResourceManager.GetString("GridViewSearchPanelTopText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimise.
        /// </summary>
        public static string HideHeaderButton {
            get {
                return ResourceManager.GetString("HideHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        public static string HistoryClear {
            get {
                return ResourceManager.GetString("HistoryClear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End:.
        /// </summary>
        public static string HistoryDateEnd {
            get {
                return ResourceManager.GetString("HistoryDateEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start:.
        /// </summary>
        public static string HistoryDateStart {
            get {
                return ResourceManager.GetString("HistoryDateStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string HistoryDelSelected {
            get {
                return ResourceManager.GetString("HistoryDelSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HISTORY.
        /// </summary>
        public static string HistoryHeaderButton {
            get {
                return ResourceManager.GetString("HistoryHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to History.
        /// </summary>
        public static string HistoryMenuItem {
            get {
                return ResourceManager.GetString("HistoryMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string HistoryToggleSwitch {
            get {
                return ResourceManager.GetString("HistoryToggleSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double keypress.
        /// </summary>
        public static string HotKeyDoubleKeyDown {
            get {
                return ResourceManager.GetString("HotKeyDoubleKeyDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the key for double keypress.
        /// </summary>
        public static string HotKeyDoubleKeyDownSelectKey {
            get {
                return ResourceManager.GetString("HotKeyDoubleKeyDownSelectKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Alt.
        /// </summary>
        public static string HotKeyDoubleLeftAlt {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftAlt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Ctrl.
        /// </summary>
        public static string HotKeyDoubleLeftCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left or right Ctrl.
        /// </summary>
        public static string HotKeyDoubleLeftOrRightCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftOrRightCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left or right Shift.
        /// </summary>
        public static string HotKeyDoubleLeftOrRightShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftOrRightShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Shift.
        /// </summary>
        public static string HotKeyDoubleLeftShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleLeftShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Alt.
        /// </summary>
        public static string HotKeyDoubleRightAlt {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightAlt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Ctrl.
        /// </summary>
        public static string HotKeyDoubleRightCtrl {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Shift.
        /// </summary>
        public static string HotKeyDoubleRightShift {
            get {
                return ResourceManager.GetString("HotKeyDoubleRightShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotkeys are disabled.
        /// </summary>
        public static string HotKeyIsOff {
            get {
                return ResourceManager.GetString("HotKeyIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotkeys are enabled.
        /// </summary>
        public static string HotKeyIsON {
            get {
                return ResourceManager.GetString("HotKeyIsON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clicking the mouse button.
        /// </summary>
        public static string HotKeyMouse {
            get {
                return ResourceManager.GetString("HotKeyMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an additional mouse button.
        /// </summary>
        public static string HotKeyMouseSelectKey {
            get {
                return ResourceManager.GetString("HotKeyMouseSelectKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortcut.
        /// </summary>
        public static string HotKeyShortcut {
            get {
                return ResourceManager.GetString("HotKeyShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotkeys.
        /// </summary>
        public static string HotkeysMenuItem {
            get {
                return ResourceManager.GetString("HotkeysMenuItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use double keypress.
        /// </summary>
        public static string HotKeyUseDoubleKeyDown {
            get {
                return ResourceManager.GetString("HotKeyUseDoubleKeyDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use hotkeys.
        /// </summary>
        public static string HotKeyUseHotkey {
            get {
                return ResourceManager.GetString("HotKeyUseHotkey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clicking mouse button.
        /// </summary>
        public static string HotKeyUseMouseXKey {
            get {
                return ResourceManager.GetString("HotKeyUseMouseXKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press hotkeys.
        /// </summary>
        public static string HotKeyWithoutPressShortcut {
            get {
                return ResourceManager.GetString("HotKeyWithoutPressShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing.
        /// </summary>
        public static string HotKeyWithoutShortcutNull {
            get {
                return ResourceManager.GetString("HotKeyWithoutShortcutNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adjust.
        /// </summary>
        public static string ImageEditor_Adjust {
            get {
                return ResourceManager.GetString("ImageEditor_Adjust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        public static string ImageEditor_Amount {
            get {
                return ResourceManager.GetString("ImageEditor_Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto.
        /// </summary>
        public static string ImageEditor_Auto {
            get {
                return ResourceManager.GetString("ImageEditor_Auto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background:.
        /// </summary>
        public static string ImageEditor_Background {
            get {
                return ResourceManager.GetString("ImageEditor_Background", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Border Color:.
        /// </summary>
        public static string ImageEditor_BorderColor {
            get {
                return ResourceManager.GetString("ImageEditor_BorderColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Border Thickness:.
        /// </summary>
        public static string ImageEditor_BorderThickness {
            get {
                return ResourceManager.GetString("ImageEditor_BorderThickness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canvas Resize.
        /// </summary>
        public static string ImageEditor_CanvasResize {
            get {
                return ResourceManager.GetString("ImageEditor_CanvasResize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Canvas Size.
        /// </summary>
        public static string ImageEditor_CanvasSize {
            get {
                return ResourceManager.GetString("ImageEditor_CanvasSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to White.
        /// </summary>
        public static string ImageEditor_ColorPicker_NoColorText_White {
            get {
                return ResourceManager.GetString("ImageEditor_ColorPicker_NoColorText_White", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crop.
        /// </summary>
        public static string ImageEditor_Crop {
            get {
                return ResourceManager.GetString("ImageEditor_Crop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draw Text.
        /// </summary>
        public static string ImageEditor_DrawText {
            get {
                return ResourceManager.GetString("ImageEditor_DrawText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your text here.
        /// </summary>
        public static string ImageEditor_DrawText_YourTextHere {
            get {
                return ResourceManager.GetString("ImageEditor_DrawText_YourTextHere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draw.
        /// </summary>
        public static string ImageEditor_DrawTool {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brush Color:.
        /// </summary>
        public static string ImageEditor_DrawTool_BrushColor {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool_BrushColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brush Size:.
        /// </summary>
        public static string ImageEditor_DrawTool_BrushSize {
            get {
                return ResourceManager.GetString("ImageEditor_DrawTool_BrushSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blur.
        /// </summary>
        public static string ImageEditor_Effect_Blur {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Blur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brightness.
        /// </summary>
        public static string ImageEditor_Effect_Brightness {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Brightness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contrast.
        /// </summary>
        public static string ImageEditor_Effect_ContrastAdjust {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_ContrastAdjust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hue Shift.
        /// </summary>
        public static string ImageEditor_Effect_HueShift {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_HueShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invert Colors.
        /// </summary>
        public static string ImageEditor_Effect_InvertColors {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_InvertColors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturation.
        /// </summary>
        public static string ImageEditor_Effect_Saturation {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Saturation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sharpen.
        /// </summary>
        public static string ImageEditor_Effect_Sharpen {
            get {
                return ResourceManager.GetString("ImageEditor_Effect_Sharpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effects.
        /// </summary>
        public static string ImageEditor_Effects {
            get {
                return ResourceManager.GetString("ImageEditor_Effects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flip Horizontal.
        /// </summary>
        public static string ImageEditor_FlipHorizontal {
            get {
                return ResourceManager.GetString("ImageEditor_FlipHorizontal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flip Vertical.
        /// </summary>
        public static string ImageEditor_FlipVertical {
            get {
                return ResourceManager.GetString("ImageEditor_FlipVertical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font Size.
        /// </summary>
        public static string ImageEditor_FontSize {
            get {
                return ResourceManager.GetString("ImageEditor_FontSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Height:.
        /// </summary>
        public static string ImageEditor_Height {
            get {
                return ResourceManager.GetString("ImageEditor_Height", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Horizontal Position.
        /// </summary>
        public static string ImageEditor_HorizontalPosition {
            get {
                return ResourceManager.GetString("ImageEditor_HorizontalPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image Alignment.
        /// </summary>
        public static string ImageEditor_ImageAlignment {
            get {
                return ResourceManager.GetString("ImageEditor_ImageAlignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image Preview.
        /// </summary>
        public static string ImageEditor_ImagePreview {
            get {
                return ResourceManager.GetString("ImageEditor_ImagePreview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image Size.
        /// </summary>
        public static string ImageEditor_ImageSize {
            get {
                return ResourceManager.GetString("ImageEditor_ImageSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        public static string ImageEditor_Open {
            get {
                return ResourceManager.GetString("ImageEditor_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Options.
        /// </summary>
        public static string ImageEditor_Options {
            get {
                return ResourceManager.GetString("ImageEditor_Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preserve Aspect Ratio.
        /// </summary>
        public static string ImageEditor_PreserveAspectRatio {
            get {
                return ResourceManager.GetString("ImageEditor_PreserveAspectRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Radius:.
        /// </summary>
        public static string ImageEditor_Radius {
            get {
                return ResourceManager.GetString("ImageEditor_Radius", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redo.
        /// </summary>
        public static string ImageEditor_Redo {
            get {
                return ResourceManager.GetString("ImageEditor_Redo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relative Size.
        /// </summary>
        public static string ImageEditor_RelativeSize {
            get {
                return ResourceManager.GetString("ImageEditor_RelativeSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resize.
        /// </summary>
        public static string ImageEditor_Resize {
            get {
                return ResourceManager.GetString("ImageEditor_Resize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rotate 180°.
        /// </summary>
        public static string ImageEditor_Rotate180 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate180", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rotate 270°.
        /// </summary>
        public static string ImageEditor_Rotate270 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate270", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rotate 90°.
        /// </summary>
        public static string ImageEditor_Rotate90 {
            get {
                return ResourceManager.GetString("ImageEditor_Rotate90", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rotation.
        /// </summary>
        public static string ImageEditor_Rotation {
            get {
                return ResourceManager.GetString("ImageEditor_Rotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Round Corners.
        /// </summary>
        public static string ImageEditor_RoundCorners {
            get {
                return ResourceManager.GetString("ImageEditor_RoundCorners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string ImageEditor_Save {
            get {
                return ResourceManager.GetString("ImageEditor_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shape.
        /// </summary>
        public static string ImageEditor_Shape {
            get {
                return ResourceManager.GetString("ImageEditor_Shape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ellipse.
        /// </summary>
        public static string ImageEditor_Shapes_Ellipse {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Ellipse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string ImageEditor_Shapes_Line {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rectangle.
        /// </summary>
        public static string ImageEditor_Shapes_Rectangle {
            get {
                return ResourceManager.GetString("ImageEditor_Shapes_Rectangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Border Color.
        /// </summary>
        public static string ImageEditor_ShapeTool_BorderColor {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_BorderColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Border Thickness.
        /// </summary>
        public static string ImageEditor_ShapeTool_BorderThickness {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_BorderThickness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fill Shape.
        /// </summary>
        public static string ImageEditor_ShapeTool_FillShape {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_FillShape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock Ratio.
        /// </summary>
        public static string ImageEditor_ShapeTool_LockRatio {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_LockRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shape.
        /// </summary>
        public static string ImageEditor_ShapeTool_Shape {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_Shape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shape Fill.
        /// </summary>
        public static string ImageEditor_ShapeTool_ShapeFill {
            get {
                return ResourceManager.GetString("ImageEditor_ShapeTool_ShapeFill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string ImageEditor_Text {
            get {
                return ResourceManager.GetString("ImageEditor_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Color.
        /// </summary>
        public static string ImageEditor_TextColor {
            get {
                return ResourceManager.GetString("ImageEditor_TextColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file cannot be opened..
        /// </summary>
        public static string ImageEditor_TheFileCannotBeOpened {
            get {
                return ResourceManager.GetString("ImageEditor_TheFileCannotBeOpened", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file cannot be opened. It might be locked by another application..
        /// </summary>
        public static string ImageEditor_TheFileIsLocked {
            get {
                return ResourceManager.GetString("ImageEditor_TheFileIsLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transform.
        /// </summary>
        public static string ImageEditor_Transform {
            get {
                return ResourceManager.GetString("ImageEditor_Transform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to save the file..
        /// </summary>
        public static string ImageEditor_UnableToSaveFile {
            get {
                return ResourceManager.GetString("ImageEditor_UnableToSaveFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Undo.
        /// </summary>
        public static string ImageEditor_Undo {
            get {
                return ResourceManager.GetString("ImageEditor_Undo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsupported file format..
        /// </summary>
        public static string ImageEditor_UnsupportedFileFormat {
            get {
                return ResourceManager.GetString("ImageEditor_UnsupportedFileFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vertical Position.
        /// </summary>
        public static string ImageEditor_VerticalPosition {
            get {
                return ResourceManager.GetString("ImageEditor_VerticalPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Width:.
        /// </summary>
        public static string ImageEditor_Width {
            get {
                return ResourceManager.GetString("ImageEditor_Width", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On your computer, for some languages, more than one keyboard layout is installed, which can negatively affect the correct operation of the keyboard layout switcher function.
        /// </summary>
        public static string InputLanguagesError {
            get {
                return ResourceManager.GetString("InputLanguagesError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interjection.
        /// </summary>
        public static string interjection {
            get {
                return ResourceManager.GetString("interjection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press keyboard shortcuts.
        /// </summary>
        public static string InterKeyboardShortcuts {
            get {
                return ResourceManager.GetString("InterKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide the indicator in programs running in fullscreen.
        /// </summary>
        public static string IsHideIndicateInFullScreenApp {
            get {
                return ResourceManager.GetString("IsHideIndicateInFullScreenApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show CAPSLOCK indicator.
        /// </summary>
        public static string IsIndicateCapsLockState {
            get {
                return ResourceManager.GetString("IsIndicateCapsLockState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicate current language in the keyboard LED.
        /// </summary>
        public static string IsIndicateCurrentLangInKeyboardLed {
            get {
                return ResourceManager.GetString("IsIndicateCurrentLangInKeyboardLed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current input language in the system tray.
        /// </summary>
        public static string IsLangInfoInTray {
            get {
                return ResourceManager.GetString("IsLangInfoInTray", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show country flag.
        /// </summary>
        public static string IsLangInfoShowIconsImage {
            get {
                return ResourceManager.GetString("IsLangInfoShowIconsImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display name of the language.
        /// </summary>
        public static string IsLangInfoShowIconsText {
            get {
                return ResourceManager.GetString("IsLangInfoShowIconsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current input language in a text cursor.
        /// </summary>
        public static string IsLangInfoWindowShowForCaret {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForCaret", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advanced features.
        /// </summary>
        public static string IsLangInfoWindowShowForCaretEx {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForCaretEx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current input language on the mouse pointer.
        /// </summary>
        public static string IsLangInfoWindowShowForMouse {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowForMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Separate window of the language indicator.
        /// </summary>
        public static string IsLangInfoWindowShowLargeWindow {
            get {
                return ResourceManager.GetString("IsLangInfoWindowShowLargeWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save single-word sentences.
        /// </summary>
        public static string IsSaveOneWordSentences {
            get {
                return ResourceManager.GetString("IsSaveOneWordSentences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Italian.
        /// </summary>
        public static string Italian {
            get {
                return ResourceManager.GetString("Italian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error has occurred.
        /// </summary>
        public static string LabelError {
            get {
                return ResourceManager.GetString("LabelError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no errors.
        /// </summary>
        public static string LabelNoErrors {
            get {
                return ResourceManager.GetString("LabelNoErrors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Options:.
        /// </summary>
        public static string LabelOptions {
            get {
                return ResourceManager.GetString("LabelOptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check completed.
        /// </summary>
        public static string LabelOptionsEnd {
            get {
                return ResourceManager.GetString("LabelOptionsEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no options.
        /// </summary>
        public static string LabelOptionsNo {
            get {
                return ResourceManager.GetString("LabelOptionsNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The text is too long.
        /// </summary>
        public static string LabelTextTooLong {
            get {
                return ResourceManager.GetString("LabelTextTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layout indicator.
        /// </summary>
        public static string LangFlagTab {
            get {
                return ResourceManager.GetString("LangFlagTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language not supported.
        /// </summary>
        public static string LangNotCorrect {
            get {
                return ResourceManager.GetString("LangNotCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Latin.
        /// </summary>
        public static string LatinButton {
            get {
                return ResourceManager.GetString("LatinButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the list in the main window.
        /// </summary>
        public static string ListOnMainWindow {
            get {
                return ResourceManager.GetString("ListOnMainWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximise.
        /// </summary>
        public static string MaxHeaderButton {
            get {
                return ResourceManager.GetString("MaxHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string MiminoteTab {
            get {
                return ResourceManager.GetString("MiminoteTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The application is running and minimized.
        /// </summary>
        public static string MinimizedText {
            get {
                return ResourceManager.GetString("MinimizedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No matches found!.
        /// </summary>
        public static string NoMatchFound {
            get {
                return ResourceManager.GetString("NoMatchFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Archive of notes.
        /// </summary>
        public static string NoteArchiveList {
            get {
                return ResourceManager.GetString("NoteArchiveList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note color.
        /// </summary>
        public static string NoteColor {
            get {
                return ResourceManager.GetString("NoteColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert to note.
        /// </summary>
        public static string NoteConvertToNote {
            get {
                return ResourceManager.GetString("NoteConvertToNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert to task list.
        /// </summary>
        public static string NoteConvertToTaskList {
            get {
                return ResourceManager.GetString("NoteConvertToTaskList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete all forever.
        /// </summary>
        public static string NoteDeleteAllForever {
            get {
                return ResourceManager.GetString("NoteDeleteAllForever", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted notes.
        /// </summary>
        public static string NoteDeletedList {
            get {
                return ResourceManager.GetString("NoteDeletedList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete forever.
        /// </summary>
        public static string NoteDeleteForever {
            get {
                return ResourceManager.GetString("NoteDeleteForever", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font family and size for note.
        /// </summary>
        public static string NoteFontFamilyAndSize {
            get {
                return ResourceManager.GetString("NoteFontFamilyAndSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste as plain text.
        /// </summary>
        public static string NotePasteAsPlainText {
            get {
                return ResourceManager.GetString("NotePasteAsPlainText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste as text.
        /// </summary>
        public static string NotePasteAsText {
            get {
                return ResourceManager.GetString("NotePasteAsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes are removed from the trash after 7 days.
        /// </summary>
        public static string NoteRemoveSevenDays {
            get {
                return ResourceManager.GetString("NoteRemoveSevenDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restore note.
        /// </summary>
        public static string NoteRestore {
            get {
                return ResourceManager.GetString("NoteRestore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add one note.
        /// </summary>
        public static string NotesAddNew {
            get {
                return ResourceManager.GetString("NotesAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes are disabled.
        /// </summary>
        public static string NotesIsOff {
            get {
                return ResourceManager.GetString("NotesIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes are enabled.
        /// </summary>
        public static string NotesIsOn {
            get {
                return ResourceManager.GetString("NotesIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes list.
        /// </summary>
        public static string NotesList {
            get {
                return ResourceManager.GetString("NotesList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show notes.
        /// </summary>
        public static string NotesShow {
            get {
                return ResourceManager.GetString("NotesShow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To the archive.
        /// </summary>
        public static string NotesToArchive {
            get {
                return ResourceManager.GetString("NotesToArchive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string NoteTab {
            get {
                return ResourceManager.GetString("NoteTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transparency for inactive notes.
        /// </summary>
        public static string NoteTransparencyForInactiveNotes {
            get {
                return ResourceManager.GetString("NoteTransparencyForInactiveNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Noun.
        /// </summary>
        public static string noun {
            get {
                return ResourceManager.GetString("noun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asian languages:.
        /// </summary>
        public static string OcrAsianLang {
            get {
                return ResourceManager.GetString("OcrAsianLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture area.
        /// </summary>
        public static string OcrCaptureArea {
            get {
                return ResourceManager.GetString("OcrCaptureArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copied image.
        /// </summary>
        public static string OcrCopyImage {
            get {
                return ResourceManager.GetString("OcrCopyImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy text.
        /// </summary>
        public static string OcrCopyText {
            get {
                return ResourceManager.GetString("OcrCopyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select European or Asian languages by default.
        /// </summary>
        public static string OcrDescDefault {
            get {
                return ResourceManager.GetString("OcrDescDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select European or Asian languages.
        /// </summary>
        public static string OcrDescLang {
            get {
                return ResourceManager.GetString("OcrDescLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Using both European and Asian languages is not supported.
        /// </summary>
        public static string OcrDescNotSup {
            get {
                return ResourceManager.GetString("OcrDescNotSup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit image.
        /// </summary>
        public static string OcrEditImage {
            get {
                return ResourceManager.GetString("OcrEditImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to European languages:.
        /// </summary>
        public static string OcrEuropeanLang {
            get {
                return ResourceManager.GetString("OcrEuropeanLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From clipboard.
        /// </summary>
        public static string OcrFromClipboard {
            get {
                return ResourceManager.GetString("OcrFromClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text recognising.
        /// </summary>
        public static string OcrHeader {
            get {
                return ResourceManager.GetString("OcrHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        public static string OcrImage {
            get {
                return ResourceManager.GetString("OcrImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please wait while the module is loading.
        /// </summary>
        public static string OcrInit {
            get {
                return ResourceManager.GetString("OcrInit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keyboard shortcut to launch recognition of the text.
        /// </summary>
        public static string OcrKeyboardShortcuts {
            get {
                return ResourceManager.GetString("OcrKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to download the module.
        /// </summary>
        public static string OcrLoadLibs {
            get {
                return ResourceManager.GetString("OcrLoadLibs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OCR module not loaded.
        /// </summary>
        public static string OcrModuleNotLoaded {
            get {
                return ResourceManager.GetString("OcrModuleNotLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Image or PDF file.
        /// </summary>
        public static string OcrOpenImageOrPDFFile {
            get {
                return ResourceManager.GetString("OcrOpenImageOrPDFFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recognize.
        /// </summary>
        public static string OcrRecognize {
            get {
                return ResourceManager.GetString("OcrRecognize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recognize barcode.
        /// </summary>
        public static string OcrRecognizeBarcode {
            get {
                return ResourceManager.GetString("OcrRecognizeBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recognized text.
        /// </summary>
        public static string OcrRecognizedText {
            get {
                return ResourceManager.GetString("OcrRecognizedText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Languages.
        /// </summary>
        public static string OcrSelectLanguages {
            get {
                return ResourceManager.GetString("OcrSelectLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture text on screen or load image file.
        /// </summary>
        public static string OcrStartText {
            get {
                return ResourceManager.GetString("OcrStartText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text recognising.
        /// </summary>
        public static string OcrTab {
            get {
                return ResourceManager.GetString("OcrTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The text has been copied.
        /// </summary>
        public static string OcrWaitResult {
            get {
                return ResourceManager.GetString("OcrWaitResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The text is not recognized.
        /// </summary>
        public static string OcrWaitResultFail {
            get {
                return ResourceManager.GetString("OcrWaitResultFail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string On {
            get {
                return ResourceManager.GetString("On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only in PRO version.
        /// </summary>
        public static string OnlyInProVersion {
            get {
                return ResourceManager.GetString("OnlyInProVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (PRO version).
        /// </summary>
        public static string OnlyPro {
            get {
                return ResourceManager.GetString("OnlyPro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language indicator transparency.
        /// </summary>
        public static string OpacityIconLangInfo {
            get {
                return ResourceManager.GetString("OpacityIconLangInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keyboard shortcut to open the main window.
        /// </summary>
        public static string OpenMainWindowShortcut {
            get {
                return ResourceManager.GetString("OpenMainWindowShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order of functions.
        /// </summary>
        public static string OrderFunctionsTab {
            get {
                return ResourceManager.GetString("OrderFunctionsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parenthetic.
        /// </summary>
        public static string parenthetic {
            get {
                return ResourceManager.GetString("parenthetic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interjection.
        /// </summary>
        public static string participle {
            get {
                return ResourceManager.GetString("participle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text.
        /// </summary>
        public static string PastButton {
            get {
                return ResourceManager.GetString("PastButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste emulating input.
        /// </summary>
        public static string PasteButtonWithoutClipboard {
            get {
                return ResourceManager.GetString("PasteButtonWithoutClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position of the  indicator in a text cursor.
        /// </summary>
        public static string PosCarret {
            get {
                return ResourceManager.GetString("PosCarret", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position of the indicator on the mouse pointer.
        /// </summary>
        public static string PosMouse {
            get {
                return ResourceManager.GetString("PosMouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preposition.
        /// </summary>
        public static string preposition {
            get {
                return ResourceManager.GetString("preposition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your license is locked, all PRO functions will be disabled.
        /// </summary>
        public static string ProBlocked {
            get {
                return ResourceManager.GetString("ProBlocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You had exaggerated the limit to the number of seats for the license, all PRO functions will be disabled.
        /// </summary>
        public static string ProBlockedByEarlyActivation {
            get {
                return ResourceManager.GetString("ProBlockedByEarlyActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exaggerated license re-activation limit for the last 30 days, all PRO functions will be disabled.
        /// </summary>
        public static string ProBlockedByMonthActivateLimit {
            get {
                return ResourceManager.GetString("ProBlockedByMonthActivateLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PRO version has expired, a new license can be purchased on the EVERYLANG.NET.
        /// </summary>
        public static string ProBlockedExp {
            get {
                return ResourceManager.GetString("ProBlockedExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The license for the PRO version will expire soon.
        /// </summary>
        public static string ProBlockedExpToDays {
            get {
                return ResourceManager.GetString("ProBlockedExpToDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string ProgramsExceptionsAddNew {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add .exe file.
        /// </summary>
        public static string ProgramsExceptionsAddNewExeFile {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewExeFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a folder.
        /// </summary>
        public static string ProgramsExceptionsAddNewFilesFromFolder {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewFilesFromFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click the mouse in the required program.
        /// </summary>
        public static string ProgramsExceptionsAddNewHelp {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add by window title.
        /// </summary>
        public static string ProgramsExceptionsAddNewTitle {
            get {
                return ResourceManager.GetString("ProgramsExceptionsAddNewTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Functions.
        /// </summary>
        public static string ProgramsExceptionsCurrentInfo {
            get {
                return ResourceManager.GetString("ProgramsExceptionsCurrentInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark functions that will work for the program.
        /// </summary>
        public static string ProgramsExceptionsCurrentInfoTooltip {
            get {
                return ResourceManager.GetString("ProgramsExceptionsCurrentInfoTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string ProgramsExceptionsDelete {
            get {
                return ResourceManager.GetString("ProgramsExceptionsDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adding with the cursor.
        /// </summary>
        public static string ProgramsExceptionsFromPoint {
            get {
                return ResourceManager.GetString("ProgramsExceptionsFromPoint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exclusion programs.
        /// </summary>
        public static string ProgramsExceptionsHeader {
            get {
                return ResourceManager.GetString("ProgramsExceptionsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets.
        /// </summary>
        public static string ProgramsExceptionsIsOnAutochange {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto switch language.
        /// </summary>
        public static string ProgramsExceptionsIsOnAutoSwitch {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnAutoSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard history.
        /// </summary>
        public static string ProgramsExceptionsIsOnClipboard {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save images.
        /// </summary>
        public static string ProgramsExceptionsIsOnClipboardImage {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnClipboardImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Converter.
        /// </summary>
        public static string ProgramsExceptionsIsOnConverter {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary text input.
        /// </summary>
        public static string ProgramsExceptionsIsOnDiary {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnDiary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotkeys.
        /// </summary>
        public static string ProgramsExceptionsIsOnHotKeys {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnHotKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layout indicator.
        /// </summary>
        public static string ProgramsExceptionsIsOnLayoutFlag {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnLayoutFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching the layout.
        /// </summary>
        public static string ProgramsExceptionsIsOnLayoutSwitcher {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnLayoutSwitcher", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string ProgramsExceptionsIsOnSmartClick {
            get {
                return ResourceManager.GetString("ProgramsExceptionsIsOnSmartClick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adding from the list of programs.
        /// </summary>
        public static string ProgramsExceptionsProgramsList {
            get {
                return ResourceManager.GetString("ProgramsExceptionsProgramsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exclusion.
        /// </summary>
        public static string ProgramsExceptionsTab {
            get {
                return ResourceManager.GetString("ProgramsExceptionsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Programs-languages.
        /// </summary>
        public static string ProgramsSetLayoutHeader {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default layouts.
        /// </summary>
        public static string ProgramsSetLayoutTab {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default language for selected programs.
        /// </summary>
        public static string ProgramsSetLayoutTabHeader {
            get {
                return ResourceManager.GetString("ProgramsSetLayoutTabHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate PRO.
        /// </summary>
        public static string ProHeaderButton {
            get {
                return ResourceManager.GetString("ProHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pronoun.
        /// </summary>
        public static string pronoun {
            get {
                return ResourceManager.GetString("pronoun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information about your license is sent to the email:.
        /// </summary>
        public static string ProSendEmailLic {
            get {
                return ResourceManager.GetString("ProSendEmailLic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate.
        /// </summary>
        public static string ProSettingsActivation {
            get {
                return ResourceManager.GetString("ProSettingsActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation failed, your license is blocked.
        /// </summary>
        public static string ProSettingsActivationBlocked {
            get {
                return ResourceManager.GetString("ProSettingsActivationBlocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation failed.
        /// </summary>
        public static string ProSettingsActivationError {
            get {
                return ResourceManager.GetString("ProSettingsActivationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation failed, possibly an invalid email.
        /// </summary>
        public static string ProSettingsActivationErrorEmail {
            get {
                return ResourceManager.GetString("ProSettingsActivationErrorEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation failed, check the network connection.
        /// </summary>
        public static string ProSettingsActivationInternetError {
            get {
                return ResourceManager.GetString("ProSettingsActivationInternetError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation is successful.
        /// </summary>
        public static string ProSettingsActivationOk {
            get {
                return ResourceManager.GetString("ProSettingsActivationOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program was activated at the new workplace, but it was deactivated on the computer.
        /// </summary>
        public static string ProSettingsActivationReactivated {
            get {
                return ResourceManager.GetString("ProSettingsActivationReactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation not possible due to reactivation of this license on another computer.
        /// </summary>
        public static string ProSettingsBlockedByEarlyActivation {
            get {
                return ResourceManager.GetString("ProSettingsBlockedByEarlyActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation not possible due to exceeding the limit of reactivations, acquiring the number of working computers on the license.
        /// </summary>
        public static string ProSettingsBlockedByMonthActivateLimit {
            get {
                return ResourceManager.GetString("ProSettingsBlockedByMonthActivateLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter code.
        /// </summary>
        public static string ProSettingsCode {
            get {
                return ResourceManager.GetString("ProSettingsCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter code and email.
        /// </summary>
        public static string ProSettingsCodeEmail {
            get {
                return ResourceManager.GetString("ProSettingsCodeEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove license.
        /// </summary>
        public static string ProSettingsDelete {
            get {
                return ResourceManager.GetString("ProSettingsDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Email.
        /// </summary>
        public static string ProSettingsEmail {
            get {
                return ResourceManager.GetString("ProSettingsEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evaluate now.
        /// </summary>
        public static string ProSettingsEvaluation {
            get {
                return ResourceManager.GetString("ProSettingsEvaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your license has expired.
        /// </summary>
        public static string ProSettingsExpired {
            get {
                return ResourceManager.GetString("ProSettingsExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trial period expired.
        /// </summary>
        public static string ProSettingsExpiredEva {
            get {
                return ResourceManager.GetString("ProSettingsExpiredEva", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License info.
        /// </summary>
        public static string ProSettingsGetData {
            get {
                return ResourceManager.GetString("ProSettingsGetData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activating PRO functions.
        /// </summary>
        public static string ProSettingsHeader {
            get {
                return ResourceManager.GetString("ProSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation code.
        /// </summary>
        public static string ProSettingsInput {
            get {
                return ResourceManager.GetString("ProSettingsInput", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO is evaluation.
        /// </summary>
        public static string ProSettingsIsEvaluation {
            get {
                return ResourceManager.GetString("ProSettingsIsEvaluation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation date:.
        /// </summary>
        public static string ProSettingsLicenseActivateDate {
            get {
                return ResourceManager.GetString("ProSettingsLicenseActivateDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free seats:.
        /// </summary>
        public static string ProSettingsLicenseCountFreeSeats {
            get {
                return ResourceManager.GetString("ProSettingsLicenseCountFreeSeats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of available reactivations:.
        /// </summary>
        public static string ProSettingsLicenseCountReact {
            get {
                return ResourceManager.GetString("ProSettingsLicenseCountReact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email:.
        /// </summary>
        public static string ProSettingsLicenseEmail {
            get {
                return ResourceManager.GetString("ProSettingsLicenseEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiry date:.
        /// </summary>
        public static string ProSettingsLicenseExpiryDate {
            get {
                return ResourceManager.GetString("ProSettingsLicenseExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License info.
        /// </summary>
        public static string ProSettingsLicenseInfo {
            get {
                return ResourceManager.GetString("ProSettingsLicenseInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License type:.
        /// </summary>
        public static string ProSettingsLicenseIsEnterprise {
            get {
                return ResourceManager.GetString("ProSettingsLicenseIsEnterprise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO is not activated.
        /// </summary>
        public static string ProSettingsLicenseNotPro {
            get {
                return ResourceManager.GetString("ProSettingsLicenseNotPro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Owner:.
        /// </summary>
        public static string ProSettingsLicenseUserName {
            get {
                return ResourceManager.GetString("ProSettingsLicenseUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of seats:.
        /// </summary>
        public static string ProSettingsLicenseUsersCount {
            get {
                return ResourceManager.GetString("ProSettingsLicenseUsersCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your activation code has been updated.
        /// </summary>
        public static string ProSettingsNewLic {
            get {
                return ResourceManager.GetString("ProSettingsNewLic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Perpetual license.
        /// </summary>
        public static string ProSettingsNoExpiry {
            get {
                return ResourceManager.GetString("ProSettingsNoExpiry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase.
        /// </summary>
        public static string ProSettingsPurchase {
            get {
                return ResourceManager.GetString("ProSettingsPurchase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string ProSettingsStatus {
            get {
                return ResourceManager.GetString("ProSettingsStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evaluate for 40 days.
        /// </summary>
        public static string ProSettingsStatusEvaluate {
            get {
                return ResourceManager.GetString("ProSettingsStatusEvaluate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO is activated.
        /// </summary>
        public static string ProSettingsStatusOk {
            get {
                return ResourceManager.GetString("ProSettingsStatusOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License.
        /// </summary>
        public static string ProTab {
            get {
                return ResourceManager.GetString("ProTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRO functions.
        /// </summary>
        public static string ProTabs {
            get {
                return ResourceManager.GetString("ProTabs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace with:.
        /// </summary>
        public static string ReplaceTo {
            get {
                return ResourceManager.GetString("ReplaceTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset All.
        /// </summary>
        public static string ResetAll {
            get {
                return ResourceManager.GetString("ResetAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Russian.
        /// </summary>
        public static string Russian {
            get {
                return ResourceManager.GetString("Russian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search:.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search....
        /// </summary>
        public static string SearchText {
            get {
                return ResourceManager.GetString("SearchText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset settings.
        /// </summary>
        public static string SetDefaultSetting {
            get {
                return ResourceManager.GetString("SetDefaultSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font.
        /// </summary>
        public static string SetFont {
            get {
                return ResourceManager.GetString("SetFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SETTINGS.
        /// </summary>
        public static string SettingHeaderButton {
            get {
                return ResourceManager.GetString("SettingHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show History.
        /// </summary>
        public static string ShowHistoryButton {
            get {
                return ResourceManager.GetString("ShowHistoryButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open in browser.
        /// </summary>
        public static string SiteSourceButton {
            get {
                return ResourceManager.GetString("SiteSourceButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open translation service website.
        /// </summary>
        public static string SiteSourceTextButton {
            get {
                return ResourceManager.GetString("SiteSourceTextButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Increase the size of the language indicator in percent.
        /// </summary>
        public static string SizeIconLangInfo {
            get {
                return ResourceManager.GetString("SizeIconLangInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position of the window relative to the mouse pointer.
        /// </summary>
        public static string SmartClickMiniPos {
            get {
                return ResourceManager.GetString("SmartClickMiniPos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Window size.
        /// </summary>
        public static string SmartClickMiniSize {
            get {
                return ResourceManager.GetString("SmartClickMiniSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hotkeys for opening the SmartClick window.
        /// </summary>
        public static string SmartClickShortcutSettingsHeader {
            get {
                return ResourceManager.GetString("SmartClickShortcutSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SNIPPETS.
        /// </summary>
        public static string SnippetsHeaderButton {
            get {
                return ResourceManager.GetString("SnippetsHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Listen.
        /// </summary>
        public static string SoundButton {
            get {
                return ResourceManager.GetString("SoundButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select sound.
        /// </summary>
        public static string SoundFormSelectTrack {
            get {
                return ResourceManager.GetString("SoundFormSelectTrack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string SoundFormVolume {
            get {
                return ResourceManager.GetString("SoundFormVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound on.
        /// </summary>
        public static string SoundOnOff {
            get {
                return ResourceManager.GetString("SoundOnOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spell check.
        /// </summary>
        public static string SpellCheckHeader {
            get {
                return ResourceManager.GetString("SpellCheckHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The keyboard shortcut to check the spelling of the selected text.
        /// </summary>
        public static string SpellcheckingKeyboardShortcuts {
            get {
                return ResourceManager.GetString("SpellcheckingKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check the spelling of the selected text.
        /// </summary>
        public static string SpellcheckingKeyboardShortcutsShort {
            get {
                return ResourceManager.GetString("SpellcheckingKeyboardShortcutsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close the window, if there is no error, after 2 seconds.
        /// </summary>
        public static string SpellcheckingSettingsCloseByTimer {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsCloseByTimer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spellchecking.
        /// </summary>
        public static string SpellcheckingSettingsHeader {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spellchecking is enabled.
        /// </summary>
        public static string SpellcheckingSettingsIsOn {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check spelling while typing.
        /// </summary>
        public static string SpellcheckingSettingsWhileTyping {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTyping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound settings when checking spelling.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingSoundEdit {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingSoundEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound to check spelling when typing.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingSoundOn {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingSoundOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For quick replacement use numbers.
        /// </summary>
        public static string SpellcheckingSettingsWhileTypingUseNumber {
            get {
                return ResourceManager.GetString("SpellcheckingSettingsWhileTypingUseNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before using the program, familiarize yourself with the functionality.
        /// </summary>
        public static string StartPageHeader {
            get {
                return ResourceManager.GetString("StartPageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn the features of the program.
        /// </summary>
        public static string StartPageHelp {
            get {
                return ResourceManager.GetString("StartPageHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase a license.
        /// </summary>
        public static string StartPageLicense {
            get {
                return ResourceManager.GetString("StartPageLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Program website.
        /// </summary>
        public static string StartPageSite {
            get {
                return ResourceManager.GetString("StartPageSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Watch the video presentation.
        /// </summary>
        public static string StartPageVideo {
            get {
                return ResourceManager.GetString("StartPageVideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please familiarize yourself with functionality before you start using the program.
        /// </summary>
        public static string StartWindowTitle {
            get {
                return ResourceManager.GetString("StartWindowTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dont close.
        /// </summary>
        public static string StayOnTopButton {
            get {
                return ResourceManager.GetString("StayOnTopButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keyboard shortcut to disable all functions.
        /// </summary>
        public static string StopWorkingShortcut {
            get {
                return ResourceManager.GetString("StopWorkingShortcut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setting keys to switch to a specific language.
        /// </summary>
        public static string SwitcherLangAndKeysForSwitch {
            get {
                return ResourceManager.GetString("SwitcherLangAndKeysForSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Timeout for keystroke emulation.
        /// </summary>
        public static string SwitcherPauseTimeForKeysSend {
            get {
                return ResourceManager.GetString("SwitcherPauseTimeForKeysSend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to disable autoswitching layout?.
        /// </summary>
        public static string SwitcherSettingsAskToDeactivateAutoswitcherOff {
            get {
                return ResourceManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable autoswitching layout?.
        /// </summary>
        public static string SwitcherSettingsAskToDeactivateAutoswitcherOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsAskToDeactivateAutoswitcherOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching the keyboard layout.
        /// </summary>
        public static string SwitcherSettingsHeader {
            get {
                return ResourceManager.GetString("SwitcherSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Autoswitch is enabled.
        /// </summary>
        public static string SwitcherSettingsHeaderAuto {
            get {
                return ResourceManager.GetString("SwitcherSettingsHeaderAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching layout is disabled.
        /// </summary>
        public static string SwitcherSettingsIsOff {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching layout is enabled.
        /// </summary>
        public static string SwitcherSettingsIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching from the beginning of the line.
        /// </summary>
        public static string SwitcherSettingsIsOnInsert {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsOnInsert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching by pressing the Break key.
        /// </summary>
        public static string SwitcherSettingsIsUseBreak {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseBreak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching by double pressing on the ScrollLock key.
        /// </summary>
        public static string SwitcherSettingsIsUseScrollLock {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseScrollLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching by double pressing on the Shift key.
        /// </summary>
        public static string SwitcherSettingsIsUseShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsIsUseShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching the layout of the last word.
        /// </summary>
        public static string SwitcherSettingsKeyboardShortcutsSwitch {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardShortcutsSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changing the layout of the selected text.
        /// </summary>
        public static string SwitcherSettingsKeyboardShortcutsSwitchSelected {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardShortcutsSwitchSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch the layout by button.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On right or left Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLRCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLRCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On right or left Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLRShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLRShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnLShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnLShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Ctrl.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRCtrl {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Ctrl or CapsLock.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right Shift.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnRShift {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnRShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System settings.
        /// </summary>
        public static string SwitcherSettingsKeyboardSwitchOnStandart {
            get {
                return ResourceManager.GetString("SwitcherSettingsKeyboardSwitchOnStandart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Leave the text selected after switching the layout.
        /// </summary>
        public static string SwitcherSettingsLeaveTextSelectedAfterSwitch {
            get {
                return ResourceManager.GetString("SwitcherSettingsLeaveTextSelectedAfterSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecting the layout switching method.
        /// </summary>
        public static string SwitcherSettingsMethodSelect {
            get {
                return ResourceManager.GetString("SwitcherSettingsMethodSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound settings switching layouts.
        /// </summary>
        public static string SwitcherSettingsSoundEdit {
            get {
                return ResourceManager.GetString("SwitcherSettingsSoundEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use Ctrl+(number) to switch to a specific language.
        /// </summary>
        public static string SwitcherSettingsSwitcherCtrlNumberIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitcherCtrlNumberIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound switch layouts.
        /// </summary>
        public static string SwitcherSettingsSwitcherSountIsOn {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitcherSountIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emulation of layout switch keys.
        /// </summary>
        public static string SwitcherSettingsSwitchMethod1 {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitchMethod1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Executing the Windows command.
        /// </summary>
        public static string SwitcherSettingsSwitchMethod2 {
            get {
                return ResourceManager.GetString("SwitcherSettingsSwitchMethod2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use LShift+RShift to change capitals mode on/off.
        /// </summary>
        public static string SwitcherSettingsToolTipForCurrentSwitchOnKey {
            get {
                return ResourceManager.GetString("SwitcherSettingsToolTipForCurrentSwitchOnKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The list of languages ​​for which will switch the layout.
        /// </summary>
        public static string SwitcherSettingsTrueListOfLang {
            get {
                return ResourceManager.GetString("SwitcherSettingsTrueListOfLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching the layout.
        /// </summary>
        public static string SwitcherTab {
            get {
                return ResourceManager.GetString("SwitcherTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program is minimized to the system tray.
        /// </summary>
        public static string SystemTrayHide {
            get {
                return ResourceManager.GetString("SystemTrayHide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To lang:.
        /// </summary>
        public static string ToLang {
            get {
                return ResourceManager.GetString("ToLang", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New snippet.
        /// </summary>
        public static string ToReplacerButton {
            get {
                return ResourceManager.GetString("ToReplacerButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total matches found:.
        /// </summary>
        public static string TotalMatchFound {
            get {
                return ResourceManager.GetString("TotalMatchFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total replacements made:.
        /// </summary>
        public static string TotalMatchFoundReplace {
            get {
                return ResourceManager.GetString("TotalMatchFoundReplace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close ESC.
        /// </summary>
        public static string TransCloseHeaderButton {
            get {
                return ResourceManager.GetString("TransCloseHeaderButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translate.
        /// </summary>
        public static string TranslateButton {
            get {
                return ResourceManager.GetString("TranslateButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error in the translation, please try again or choose a different  provider of translation.
        /// </summary>
        public static string TranslateError {
            get {
                return ResourceManager.GetString("TranslateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show only selected languages.
        /// </summary>
        public static string TranslateOnlyFavoriteLanguages {
            get {
                return ResourceManager.GetString("TranslateOnlyFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show all.
        /// </summary>
        public static string TranslateSowAll {
            get {
                return ResourceManager.GetString("TranslateSowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translator.
        /// </summary>
        public static string TranslationTab {
            get {
                return ResourceManager.GetString("TranslationTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace text ENTER.
        /// </summary>
        public static string TransReplaceTextButton {
            get {
                return ResourceManager.GetString("TransReplaceTextButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose your favorite languages.
        /// </summary>
        public static string TransSettingsChooseYourFavoriteLanguages {
            get {
                return ResourceManager.GetString("TransSettingsChooseYourFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear all translation history.
        /// </summary>
        public static string TransSettingsClearAllHistory {
            get {
                return ResourceManager.GetString("TransSettingsClearAllHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Favorite languages.
        /// </summary>
        public static string TransSettingsFavoriteLanguages {
            get {
                return ResourceManager.GetString("TransSettingsFavoriteLanguages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translation.
        /// </summary>
        public static string TransSettingsHeader {
            get {
                return ResourceManager.GetString("TransSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store the translation history.
        /// </summary>
        public static string TransSettingsHistoryIsOn {
            get {
                return ResourceManager.GetString("TransSettingsHistoryIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press keyboard shortcuts.
        /// </summary>
        public static string TransSettingsInterKeyboardShortcuts {
            get {
                return ResourceManager.GetString("TransSettingsInterKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translation is enabled.
        /// </summary>
        public static string TransSettingsIsOn {
            get {
                return ResourceManager.GetString("TransSettingsIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortcut to translate the selected text.
        /// </summary>
        public static string TransSettingsKeyboardShortcuts {
            get {
                return ResourceManager.GetString("TransSettingsKeyboardShortcuts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default language to translate from.
        /// </summary>
        public static string TransSettingsLanguageFromTranslate {
            get {
                return ResourceManager.GetString("TransSettingsLanguageFromTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The main language for the translation.
        /// </summary>
        public static string TransSettingsMainLanguageForTheTranslation {
            get {
                return ResourceManager.GetString("TransSettingsMainLanguageForTheTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default language to translate into.
        /// </summary>
        public static string TransSettingsNativeLanguage {
            get {
                return ResourceManager.GetString("TransSettingsNativeLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provider of translation.
        /// </summary>
        public static string TransSettingsProviderOfTranslation {
            get {
                return ResourceManager.GetString("TransSettingsProviderOfTranslation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translate when you select text with the mouse.
        /// </summary>
        public static string TransSettingsTranslationIsAlways {
            get {
                return ResourceManager.GetString("TransSettingsTranslationIsAlways", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use double pressing on the Ctrl key.
        /// </summary>
        public static string TransSettingsUseDoubleCtrl {
            get {
                return ResourceManager.GetString("TransSettingsUseDoubleCtrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ukrainian.
        /// </summary>
        public static string Ukrainian {
            get {
                return ResourceManager.GetString("Ukrainian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open snippets list.
        /// </summary>
        public static string UniAutochange {
            get {
                return ResourceManager.GetString("UniAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert selected text to CamelCase style.
        /// </summary>
        public static string UniCamelCase {
            get {
                return ResourceManager.GetString("UniCamelCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open clipboard history.
        /// </summary>
        public static string UniClipboardHistory {
            get {
                return ResourceManager.GetString("UniClipboardHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text converter.
        /// </summary>
        public static string UniConverter {
            get {
                return ResourceManager.GetString("UniConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert numbers and dates to strings, evaluate expressions.
        /// </summary>
        public static string UniConvertExpressions {
            get {
                return ResourceManager.GetString("UniConvertExpressions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        public static string UniCopy {
            get {
                return ResourceManager.GetString("UniCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open diary.
        /// </summary>
        public static string UniDiaryHistory {
            get {
                return ResourceManager.GetString("UniDiaryHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert selected text to lowercase.
        /// </summary>
        public static string UniDownCase {
            get {
                return ResourceManager.GetString("UniDownCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create an email message.
        /// </summary>
        public static string UniEmail {
            get {
                return ResourceManager.GetString("UniEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enclose the selected text with quotation marks.
        /// </summary>
        public static string UniEnclose {
            get {
                return ResourceManager.GetString("UniEnclose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invert the case of the selected text.
        /// </summary>
        public static string UniInvertCase {
            get {
                return ResourceManager.GetString("UniInvertCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open this link in a browser.
        /// </summary>
        public static string UniLink {
            get {
                return ResourceManager.GetString("UniLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short link generation.
        /// </summary>
        public static string UniLinkShorter {
            get {
                return ResourceManager.GetString("UniLinkShorter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translate site.
        /// </summary>
        public static string UniLinkTranslate {
            get {
                return ResourceManager.GetString("UniLinkTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text.
        /// </summary>
        public static string UniPaste {
            get {
                return ResourceManager.GetString("UniPaste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste text without formatting.
        /// </summary>
        public static string UniPasteUnf {
            get {
                return ResourceManager.GetString("UniPasteUnf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call search.
        /// </summary>
        public static string UniSearch {
            get {
                return ResourceManager.GetString("UniSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Spelling.
        /// </summary>
        public static string UniSpellCheck {
            get {
                return ResourceManager.GetString("UniSpellCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snippets.
        /// </summary>
        public static string UniTextAutochange {
            get {
                return ResourceManager.GetString("UniTextAutochange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard.
        /// </summary>
        public static string UniTextClipboardHistory1 {
            get {
                return ResourceManager.GetString("UniTextClipboardHistory1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to history.
        /// </summary>
        public static string UniTextClipboardHistory2 {
            get {
                return ResourceManager.GetString("UniTextClipboardHistory2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Converter.
        /// </summary>
        public static string UniTextConverter {
            get {
                return ResourceManager.GetString("UniTextConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expressions.
        /// </summary>
        public static string UniTextConvertExpressions {
            get {
                return ResourceManager.GetString("UniTextConvertExpressions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        public static string UniTextCopy {
            get {
                return ResourceManager.GetString("UniTextCopy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diary.
        /// </summary>
        public static string UniTextDiaryHistory {
            get {
                return ResourceManager.GetString("UniTextDiaryHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lowercase.
        /// </summary>
        public static string UniTextDownCase {
            get {
                return ResourceManager.GetString("UniTextDownCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string UniTextEmail {
            get {
                return ResourceManager.GetString("UniTextEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enclose.
        /// </summary>
        public static string UniTextEnclose {
            get {
                return ResourceManager.GetString("UniTextEnclose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invert case.
        /// </summary>
        public static string UniTextInvertCase {
            get {
                return ResourceManager.GetString("UniTextInvertCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link.
        /// </summary>
        public static string UniTextLink {
            get {
                return ResourceManager.GetString("UniTextLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short URL.
        /// </summary>
        public static string UniTextLinkShorter {
            get {
                return ResourceManager.GetString("UniTextLinkShorter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trans. site.
        /// </summary>
        public static string UniTextLinkTranslate {
            get {
                return ResourceManager.GetString("UniTextLinkTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste.
        /// </summary>
        public static string UniTextPaste {
            get {
                return ResourceManager.GetString("UniTextPaste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unformatted.
        /// </summary>
        public static string UniTextPasteUnf1 {
            get {
                return ResourceManager.GetString("UniTextPasteUnf1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to paste.
        /// </summary>
        public static string UniTextPasteUnf2 {
            get {
                return ResourceManager.GetString("UniTextPasteUnf2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string UniTextSearch {
            get {
                return ResourceManager.GetString("UniTextSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spellcheck.
        /// </summary>
        public static string UniTextSpellCheck {
            get {
                return ResourceManager.GetString("UniTextSpellCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translate.
        /// </summary>
        public static string UniTextTranslate {
            get {
                return ResourceManager.GetString("UniTextTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translit.
        /// </summary>
        public static string UniTextTranslit {
            get {
                return ResourceManager.GetString("UniTextTranslit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uppercase.
        /// </summary>
        public static string UniTextUpCase {
            get {
                return ResourceManager.GetString("UniTextUpCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uppercase.
        /// </summary>
        public static string UniTextUppercase {
            get {
                return ResourceManager.GetString("UniTextUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translate.
        /// </summary>
        public static string UniTranslate {
            get {
                return ResourceManager.GetString("UniTranslate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open the history translations.
        /// </summary>
        public static string UniTranslateHistory {
            get {
                return ResourceManager.GetString("UniTranslateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transliteration of selected text.
        /// </summary>
        public static string UniTranslit {
            get {
                return ResourceManager.GetString("UniTranslit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convert selected text to uppercase.
        /// </summary>
        public static string UniUpCase {
            get {
                return ResourceManager.GetString("UniUpCase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change case of the selected text.
        /// </summary>
        public static string UniUppercase {
            get {
                return ResourceManager.GetString("UniUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string UniversalWindowSettingsHeader {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark the functions that will be available.
        /// </summary>
        public static string UniversalWindowSettingsItemsCheck {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsItemsCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice your favorite search service for the SmartClick.
        /// </summary>
        public static string UniversalWindowSettingsSearchServices {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsSearchServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show an auxiliary window after text selection.
        /// </summary>
        public static string UniversalWindowSettingsShowMiniOn {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowMiniOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open by double clicking on the middle mouse button.
        /// </summary>
        public static string UniversalWindowSettingsShowOnDoubleMiddle {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnDoubleMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use hotkeys to open.
        /// </summary>
        public static string UniversalWindowSettingsShowOnPressHotKeys {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnPressHotKeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open by pressing the left and then the right mouse button.
        /// </summary>
        public static string UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick is disabled.
        /// </summary>
        public static string UniversalWindowSettingsUniversalWindowIsOff {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsUniversalWindowIsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick is enabled.
        /// </summary>
        public static string UniversalWindowSettingsUniversalWindowIsOn {
            get {
                return ResourceManager.GetString("UniversalWindowSettingsUniversalWindowIsOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartClick.
        /// </summary>
        public static string UniversalWindowTab {
            get {
                return ResourceManager.GetString("UniversalWindowTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update available, restart the application.
        /// </summary>
        public static string UpdateAvailable {
            get {
                return ResourceManager.GetString("UpdateAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verb.
        /// </summary>
        public static string verb {
            get {
                return ResourceManager.GetString("verb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable the program.
        /// </summary>
        public static string WorkingOff {
            get {
                return ResourceManager.GetString("WorkingOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The program is disabled.
        /// </summary>
        public static string WorkingOffTitle {
            get {
                return ResourceManager.GetString("WorkingOffTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable the program.
        /// </summary>
        public static string WorkingOn {
            get {
                return ResourceManager.GetString("WorkingOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
