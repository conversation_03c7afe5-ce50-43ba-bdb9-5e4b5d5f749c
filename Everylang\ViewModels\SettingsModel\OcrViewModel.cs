﻿using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class OcrViewModel : ViewModelBase
    {
        public ObservableCollection<CheckedListItem<Language>> Languages { get; set; }

        public OcrViewModel()
        {
            Languages = new ObservableCollection<CheckedListItem<Language>>();

            foreach (var lang in OcrManager.Instance.LangsAll)
            {
                Languages.Add(new CheckedListItem<Language>(new Language() { Name = lang }, false));
            }

            LoadLanguages();
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
            }
        }


        internal void LoadLanguages()
        {

            foreach (var s in SettingsManager.Settings.OcrLangsList)
            {
                var lang = Languages.FirstOrDefault(x => x.Item.Name == s);
                if (lang != null)
                {
                    lang.IsChecked = true;
                }
            }
        }

        public string Shortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.OcrShortcut);
            }
            set
            {
                SettingsManager.Settings.OcrShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public class CheckedListItem<T> : INotifyPropertyChanged
        {
            public event PropertyChangedEventHandler? PropertyChanged;

            private bool _isChecked;
            private T _item;

            internal CheckedListItem(T item, bool isChecked)
            {
                this._item = item;
                this._isChecked = isChecked;
            }

            public T Item
            {
                get { return _item; }
                set
                {
                    _item = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs("Item"));
                }
            }

            public bool IsChecked
            {
                get { return _isChecked; }
                set
                {
                    _isChecked = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs("IsChecked"));
                }
            }
        }

        public class Language
        {
            public string Name { get; set; } = String.Empty;
        }

    }
}
