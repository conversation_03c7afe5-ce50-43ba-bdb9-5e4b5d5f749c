﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Everylang.App</RootNamespace>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>true</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Release\</OutputPath>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject></StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>i11.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>el_key.pfx</AssemblyOriginatorKeyFile>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Resources\logo.png" />
    <None Remove="Translator\Languages\all_languages.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="CustomMarshalers" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Design" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows" />
    <Reference Include="Telerik.Windows.Controls">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.Data">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.Data.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.Docking">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.Docking.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.GridView">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.GridView.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.ImageEditor">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.ImageEditor.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.Input">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.Input.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Controls.Navigation">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Controls.Navigation.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Data">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Data.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Documents.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.FormatProviders.Rtf">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Documents.FormatProviders.Rtf.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Themes.Windows11">
      <HintPath>..\lib\Telerik UI for WPF 2024 Q4\Binaries.NoXaml\WPF462\Telerik.Windows.Themes.Windows11.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="OCR\OcrEngine\Libs\ocr.zip" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\bg" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\cz" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\de" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\en" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\fr" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\it" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\pl" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\pt" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\ru" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\tr" />
    <EmbeddedResource Include="SwitcherLang\autolangdic\uk" />
    <EmbeddedResource Include="SwitcherLang\autolang\be" />
    <EmbeddedResource Include="SwitcherLang\autolang\bg" />
    <EmbeddedResource Include="SwitcherLang\autolang\cs" />
    <EmbeddedResource Include="SwitcherLang\autolang\de" />
    <EmbeddedResource Include="SwitcherLang\autolang\dk" />
    <EmbeddedResource Include="SwitcherLang\autolang\el" />
    <EmbeddedResource Include="SwitcherLang\autolang\en" />
    <EmbeddedResource Include="SwitcherLang\autolang\es" />
    <EmbeddedResource Include="SwitcherLang\autolang\fr" />
    <EmbeddedResource Include="SwitcherLang\autolang\he" />
    <EmbeddedResource Include="SwitcherLang\autolang\hr" />
    <EmbeddedResource Include="SwitcherLang\autolang\hu" />
    <EmbeddedResource Include="SwitcherLang\autolang\it" />
    <EmbeddedResource Include="SwitcherLang\autolang\nl" />
    <EmbeddedResource Include="SwitcherLang\autolang\pl" />
    <EmbeddedResource Include="SwitcherLang\autolang\pt" />
    <EmbeddedResource Include="SwitcherLang\autolang\ro" />
    <EmbeddedResource Include="SwitcherLang\autolang\ru" />
    <EmbeddedResource Include="SwitcherLang\autolang\se" />
    <EmbeddedResource Include="SwitcherLang\autolang\tr" />
    <EmbeddedResource Include="SwitcherLang\autolang\uk" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HookManager\ELshellkhook32.dll" />
    <EmbeddedResource Include="HookManager\ELshellkhook64.dll" />
    <EmbeddedResource Include="LangFlag\FlagsImages\abkhazia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\afghanistan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\aland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\albania.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\algeria.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\american-samoa.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\andorra.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\angola.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\anguilla.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\antarctica.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\antigua-and-barbuda.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\argentina.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\armenia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\aruba.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\australia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\austria.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\azerbaijan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bahamas.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bahrain.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bangladesh.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\barbados.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\basque-country.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\belarus.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\belgium.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\belize.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\benin.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bermuda.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bhutan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bolivia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bosnia-and-herzegovina.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\botswana.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\brazil.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\british-antarctic-territory.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\british-virgin-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\brunei.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\bulgaria.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\burkina-faso.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\burundi.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cambodia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cameroon.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\canada.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\canary-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cape-verde.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cayman-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\central-african-republic.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\chad.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\chile.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\china.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\christmas-island.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cocos-keeling-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\colombia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\commonwealth.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\comoros.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cook-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\costa-rica.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cote-divoire.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\croatia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cuba.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\curacao.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\cyprus.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\czechia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\democratic-republic-of-the-congo.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\denmark.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\djibouti.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\dominica.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\dominican-republic.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\east-timor.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\ecuador.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\egypt.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\el-salvador.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\england.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\equatorial-guinea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\eritrea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\estonia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\ethiopia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\european-union.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\falkland-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\faroes.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\fiji.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\finland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\france.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\french-polynesia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\french-southern-territories.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\gabon.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\gambia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\georgia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\germany.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\ghana.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\gibraltar.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\gosquared.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\greece.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\greenland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\grenada.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guam.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guatemala.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guernsey.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guinea-bissau.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guinea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\guyana.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\haiti.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\honduras.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\hong-kong.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\hungary.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\iceland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\india.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\indonesia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\iran.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\iraq.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\ireland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\isle-of-man.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\israel.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\italy.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\jamaica.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\japan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\jersey.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\jordan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kazakhstan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kenya.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kiribati.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kosovo.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kuwait.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\kyrgyzstan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\laos.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\latvia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\lebanon.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\lesotho.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\liberia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\libya.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\liechtenstein.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\lithuania.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\luxembourg.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\macau.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\macedonia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\madagascar.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\malawi.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\malaysia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\maldives.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mali.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\malta.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mars.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\marshall-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\martinique.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mauritania.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mauritius.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mayotte.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mexico.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\micronesia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\moldova.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\monaco.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mongolia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\montenegro.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\montserrat.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\morocco.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\mozambique.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\myanmar.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nagorno-karabakh.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\namibia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nato.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nauru.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nepal.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\netherlands-antilles.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\netherlands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\new-caledonia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\new-zealand.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nicaragua.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\niger.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\nigeria.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\niue.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\norfolk-island.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\north-korea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\northern-cyprus.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\northern-mariana-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\norway.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\olympics.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\oman.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\pakistan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\palau.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\palestine.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\panama.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\papua-new-guinea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\paraguay.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\peru.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\philippines.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\pitcairn-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\poland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\portugal.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\puerto-rico.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\qatar.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\red-cross.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\republic-of-the-congo.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\romania.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\russia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\rwanda.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-barthelemy.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-helena.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-kitts-and-nevis.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-lucia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-martin.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saint-vincent-and-the-grenadines.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\samoa.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\san-marino.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\sao-tome-and-principe.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\saudi-arabia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\scotland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\senegal.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\serbia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\seychelles.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\sierra-leone.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\singapore.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\slovakia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\slovenia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\solomon-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\somalia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\somaliland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\south-africa.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\south-georgia-and-the-south-sandwich-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\south-korea.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\south-ossetia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\south-sudan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\spain.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\sri-lanka.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\sudan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\suriname.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\swaziland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\sweden.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\switzerland.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\syria.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\taiwan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tajikistan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tanzania.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\thailand.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\togo.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tokelau.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tonga.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\trinidad-and-tobago.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tunisia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\turkey.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\turkmenistan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\turks-and-caicos-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\tuvalu.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\uganda.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\ukraine.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\united-arab-emirates.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\united-kingdom.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\united-nations.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\united-states.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\unknown.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\uruguay.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\us-virgin-islands.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\uzbekistan.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\vanuatu.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\vatican-city.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\venezuela.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\vietnam.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\wales.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\wallis-and-futuna.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\western-sahara.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\yemen.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\zambia.png" />
    <EmbeddedResource Include="LangFlag\FlagsImages\zimbabwe.png" />
    <EmbeddedResource Include="Translator\Languages\all_languages.txt" />
    <EmbeddedResource Include="Translator\Languages\microsoft.txt" />
    <Resource Include="Resources\capslock.ico" />
    <Resource Include="Resources\capslock.png" />
    <Resource Include="Resources\capslock_on.ico" />
    <Resource Include="Resources\capslock_on.png" />
    <Resource Include="Resources\i1.ico" />
    <Resource Include="Resources\i11.ico" />
    <Resource Include="Resources\i12.ico" />
    <Resource Include="Resources\i13.ico" />
    <Resource Include="Resources\i13.png" />
    <Resource Include="Resources\logo.png" />
    <Resource Include="Resources\logoSmall.png" />
    <Resource Include="Resources\numlock-off.ico" />
    <Resource Include="Resources\numlock-on.ico" />
    <Resource Include="Resources\numlock_off.ico" />
    <Resource Include="Resources\numlock_off.png" />
    <Resource Include="Resources\numlock_on.ico" />
    <Resource Include="Resources\numlock_on.png" />
    <EmbeddedResource Include="Translator\Languages\bing.txt" />
    <EmbeddedResource Include="Translator\Languages\deepl.txt" />
    <EmbeddedResource Include="Translator\Languages\google.txt" />
    <EmbeddedResource Include="Translator\Languages\yandex.txt" />
    <Resource Include="i11.ico" />
    <EmbeddedResource Include="Utilities\sounds\Burrod.wav" />
    <EmbeddedResource Include="Utilities\sounds\Ceech.wav" />
    <EmbeddedResource Include="Utilities\sounds\Denot.wav" />
    <EmbeddedResource Include="Utilities\sounds\Eldyp.wav" />
    <EmbeddedResource Include="Utilities\sounds\Honond.wav" />
    <EmbeddedResource Include="Utilities\sounds\Lorsam.wav" />
    <EmbeddedResource Include="Utilities\sounds\Nykorm.wav" />
    <EmbeddedResource Include="Utilities\sounds\Ormild.wav" />
    <EmbeddedResource Include="Utilities\sounds\Reskel.wav" />
    <EmbeddedResource Include="Utilities\sounds\Riltur.wav" />
    <EmbeddedResource Include="Utilities\sounds\Suled.wav" />
    <EmbeddedResource Include="Utilities\sounds\Tesul.wav" />
    <EmbeddedResource Include="Utilities\sounds\Untos.wav" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CaseConverter">
      <Version>2.0.1</Version>
    </PackageReference>
    <PackageReference Include="Costura.Fody" Version="6.0.0" Condition="'$(Configuration)' == 'Release'">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FlaUI.UIA3">
      <Version>4.0.0</Version>
    </PackageReference>
    <PackageReference Include="gong-wpf-dragdrop">
      <Version>4.0.0</Version>
    </PackageReference>
    <PackageReference Include="H.InputSimulator">
      <Version>1.5.0</Version>
    </PackageReference>
    <PackageReference Include="H.NotifyIcon.Wpf">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Humanizer">
      <Version>2.14.1</Version>
    </PackageReference>
    <PackageReference Include="LiteDB">
      <Version>5.0.21</Version>
    </PackageReference>
    <PackageReference Include="Material.Icons.WPF">
      <Version>2.1.10</Version>
    </PackageReference>
    <PackageReference Include="MaterialDesignThemes">
      <Version>5.1.0</Version>
    </PackageReference>
    <PackageReference Include="MathParser.org-mXparser">
      <Version>6.1.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf">
      <Version>1.1.135</Version>
    </PackageReference>
    <PackageReference Include="NAudio">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NHotkey.Wpf">
      <Version>3.0.0</Version>
    </PackageReference>
    <PackageReference Include="NickBuhro.Translit">
      <Version>1.4.5</Version>
    </PackageReference>
    <PackageReference Include="Octokit">
      <Version>13.0.1</Version>
    </PackageReference>
    <PackageReference Include="Ookii.Dialogs.Wpf">
      <Version>5.0.1</Version>
    </PackageReference>
    <PackageReference Include="PlatformSpellCheck">
      <Version>1.1.0</Version>
    </PackageReference>
    <PackageReference Include="RestSharp">
      <Version>112.1.0</Version>
    </PackageReference>
    <PackageReference Include="Serilog">
      <Version>4.2.0</Version>
    </PackageReference>
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.ComponentModel.Composition" Version="9.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.DirectoryServices" Version="9.0.0" />
    <PackageReference Include="System.Management" Version="9.0.0" />
    <PackageReference Include="System.Reactive">
      <Version>6.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Json">
      <Version>8.0.5</Version>
    </PackageReference>
    <PackageReference Include="Tesseract">
      <Version>5.2.0</Version>
    </PackageReference>
    <PackageReference Include="Vanara.PInvoke.Accessibility">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="Vanara.PInvoke.UxTheme">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="Vanara.SystemServices">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="Vanara.Windows.Shell">
      <Version>4.0.4</Version>
    </PackageReference>
    <PackageReference Include="WindowsAPICodePackShell">
      <Version>8.0.6</Version>
    </PackageReference>
    <PackageReference Include="WpfScreenHelper">
      <Version>2.1.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Everylang.Common\Everylang.Common.csproj" />
    <ProjectReference Include="..\Everylang.Note\Everylang.Note.csproj" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent" Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Exec Command="call ../build.bat" />
  </Target>
</Project>