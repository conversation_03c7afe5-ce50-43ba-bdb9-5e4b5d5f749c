﻿using Everylang.App.Converter;
using Everylang.App.SettingsApp;
using Everylang.App.View.Controls.Common;
using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.SettingControls.CapsText
{
    /// <summary>
    /// Interaction logic for SwitcherCapsControl.xaml
    /// </summary>
    internal partial class ConverterCapsControl
    {
        internal ConverterCapsControl()
        {
            InitializeComponent();
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void SwitchSelectedCapsOpenWindowClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsCapsOpenWindow"), SettingsManager.Settings.ConverterShortcutCapsOpenWindow, nameof(SettingsManager.Settings.ConverterShortcutCapsOpenWindow), ConverterManager.Instance.PressedSwitcherOpenWindowCaps);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutOpenWindowCaps = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchSelectedCapsShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert"), SettingsManager.Settings.ConverterShortcutCapsInvert, nameof(SettingsManager.Settings.ConverterShortcutCapsInvert), ConverterManager.Instance.PressedSwitcherSelectedCaps);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCaps = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }


        private void SwitchSelectedCapsUpShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp"), SettingsManager.Settings.ConverterShortcutCapsUp, nameof(SettingsManager.Settings.ConverterShortcutCapsUp), ConverterManager.Instance.PressedSwitcherSelectedCapsUp);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCapsUp = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }


        private void SwitchSelectedCapsDownShortcutClick(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown"), SettingsManager.Settings.ConverterShortcutCapsDown, nameof(SettingsManager.Settings.ConverterShortcutCapsDown), ConverterManager.Instance.PressedSwitcherSelectedCapsDown);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutSelectedCapsDown = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchFirstLetterToDown(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToDown"), SettingsManager.Settings.ConverterFirstLetterToDown, nameof(SettingsManager.Settings.ConverterFirstLetterToDown), ConverterManager.Instance.PressedSwitcherFirstLetterToDown);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutFirstLetterToDown = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }

        private void SwitchFirstLetterToUp(object sender, RoutedEventArgs e)
        {
            HotKeyControl? hotKeyControl = new HotKeyControl(LocalizationManager.GetString("ConverterSettingsKeyboardShortcutsFirstLetterToUp"), SettingsManager.Settings.ConverterFirstLetterToUp, nameof(SettingsManager.Settings.ConverterFirstLetterToUp), ConverterManager.Instance.PressedSwitcherFirstLetterToUp);
            hotKeyControl.Save += (_, _) =>
            {
                VMContainer.Instance.ConverterSettingsViewModel.ShortcutFirstLetterToUp = hotKeyControl.NewShortCut;
            };
            PageTransitionControl.Content = hotKeyControl;
            hotKeyControl.HidePanel += (_, _) =>
            {
                PageTransitionControl.Content = null;
                hotKeyControl = null;
            };
        }
    }
}
