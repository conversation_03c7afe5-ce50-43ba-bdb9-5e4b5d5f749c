﻿using Everylang.App.License.LicenseCore;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities.NetRequest;
using Everylang.Common.LogManager;
using System;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Everylang.App.License;

internal class LicenseInfo
{

    private const string SECRET = "njjgj84jwUU8230fhbhasdfk2349430j3fjj23fjcmasdj2390";

    //private const string URL_TO_CHECK = "https://data.everylang.net/api/LicCheck";

    //private const string URL_TO_CHECK_TRIAL = "https://data.everylang.net/api/LicCheck";

    private const string URL_TO_CHECK = "https://data.everylang.net/api/LicCheck";

    private const string URL_TO_CHECK_TRIAL = "https://data.everylang.net/api/LicCheck";

    internal async Task<string> CheckLicInternal(string lic, string email)
    {
        return await CheckInternal(lic, email);
    }

    internal async Task<LicenseModel> CheckLicExternal(string lic)
    {

        return await GetLicExternalAsync(lic);
    }

    internal async Task<LicenseModel> CheckTrial()
    {
        return await GetLicExternalTrialAsync();
    }

    internal async Task<string> Check(string lic, string email, bool trial, bool timer)
    {
        if (trial) email = "eltrial";

        if (!timer) return await CheckInternal(lic, email);

        LicenseModel licInfo;

        if (trial)
            licInfo = await GetLicExternalTrialAsync();
        else
            licInfo = await GetLicExternalAsync(lic);

        if (licInfo.IsConnectOk)
        {
            if (licInfo.ErrorLic != "") return Secure.EncryptStringAES("ErrorLic", lic);

            if (licInfo.IsBlockedByEarlyActivation)
                return Secure.EncryptStringAES("LicenseBlockedByEarlyActivation", lic);

            if (licInfo.IsBlockedByMonthActivateLimit)
                return Secure.EncryptStringAES("LicenseBlockedByMonthActivateLimit", lic);

            if (licInfo.LicIsBlocked) return Secure.EncryptStringAES("LicenseBlocked", lic);

            if (licInfo.LicExpiryDate < DateTime.Now) return Secure.EncryptStringAES("LicenseExp", lic);

            return Secure.EncryptStringAES("ErrorHol", lic);
        }

        if (!licInfo.IsConnectOk && !string.IsNullOrEmpty(licInfo.ErrorConnect))
        {
            var res = await NetLib.CheckInternetConnectionAsync();

            if (!res) return await CheckInternal(lic, email);
        }

        return Secure.EncryptStringAES("Error", lic);
    }

    internal async Task<string> CheckInternal(string license, string email)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (HardwareId.GetHash(license).ToUpper() == "788F428790AD61997A5C04FE41E72F7B")
                    return Secure.EncryptStringAES("LicenseError", license);
                StandardKey? decryptKey = null;
                try
                {
                    decryptKey = LicenseHandler.FromProductCode<StandardKey>(license, email);
                    if (decryptKey != null && decryptKey.ExpiryDate == DateTime.MinValue) decryptKey = null;
                }
                catch
                {
                    // ignore
                }

                if (decryptKey == null) decryptKey = LicenseHandler.FromProductCode<StandardKey>(license, email);

                if (decryptKey != null && decryptKey.ExpiryDate < DateTime.Now) return Secure.EncryptStringAES("LicenseExpired", license);

                return Secure.EncryptStringAES("LicenseHol", license);
            }
            catch
            {
                // ignore
            }

            return Secure.EncryptStringAES("LicenseError", license);
        });
    }

    internal async Task<LicenseModel> GetLicExternalAsync(string license)
    {
        return await Task.Run(() =>
        {
            var licenseModel = new LicenseModel();
            try
            {
                var stringBuilder = new StringBuilder();

                stringBuilder.Append("MACHINEID=" + SettingsManager.MachineId + "&");

                stringBuilder.Append("LICENSE=" + license + "&");

                stringBuilder.Append("MACHINENAME=" + Environment.MachineName + "&");

                stringBuilder.Append("TRIAL=false" + "&");
                stringBuilder.Append("DOMAINNAME=" + Environment.UserDomainName + "&");

                stringBuilder.Append("PARAM=" + Environment.UserName + " " +
                                     System.Reflection.Assembly.GetExecutingAssembly().GetName().Version);

                var data = "=" + HttpUtility.UrlEncode(Secure.EncryptStringAES(stringBuilder.ToString(), SECRET));

                var netLib = new NetLib(URL_TO_CHECK, data);

                var webResult = netLib.StartPostWebRequest();

                if (!webResult.WithError)
                {
                    var result = Secure.DecryptStringAES(webResult.ResultText?.Trim('"') ?? throw new InvalidOperationException(), SECRET);

                    var collection = HttpUtility.ParseQueryString(result);

                    licenseModel.IsConnectOk = true;

                    licenseModel.ErrorConnect = "";
                    if (collection["ERRORLIC"] != null) licenseModel.ErrorLic = collection["ERRORLIC"]!;

                    if (string.IsNullOrEmpty(licenseModel.ErrorLic))
                    {
                        if (collection["EMAIL"] != null) licenseModel.Email = collection["EMAIL"]!;

                        if (collection["USERSCOUNT"] != null)
                            licenseModel.UsersCount = Convert.ToInt32(collection["USERSCOUNT"]);

                        if (collection["USERNAME"] != null) licenseModel.UserName = collection["USERNAME"]!;

                        if (collection["LICENSE"] != null) licenseModel.License = collection["LICENSE"]!;

                        if (collection["MACHINENAME"] != null) licenseModel.MachineName = collection["MACHINENAME"]!;

                        if (collection["ISBLOCKEDBYEARLYACTIVATION"] != null)
                            licenseModel.IsBlockedByEarlyActivation =
                                Convert.ToBoolean(collection["ISBLOCKEDBYEARLYACTIVATION"]);

                        if (collection["ISBLOCKEDBYMONTHACTIVATELIMIT"] != null)
                            licenseModel.IsBlockedByMonthActivateLimit =
                                Convert.ToBoolean(collection["ISBLOCKEDBYMONTHACTIVATELIMIT"]);

                        if (collection["LICISBLOCKED"] != null)
                            licenseModel.LicIsBlocked = Convert.ToBoolean(collection["LICISBLOCKED"]);

                        if (collection["LICISTRIAL"] != null)
                            licenseModel.LicIsTrial = Convert.ToBoolean(collection["LICISTRIAL"]);

                        var ruCultureInfo = CultureInfo.CreateSpecificCulture("ru-RU");
                        if (collection["LICEXPIRYDATE"] != null)
                            licenseModel.LicExpiryDate = DateTime.ParseExact(collection["LICEXPIRYDATE"]!, "dd.MM.yyyy HH:mm:ss",
                                ruCultureInfo);
                        if (collection["ACTIVATEDATE"] != null)
                            licenseModel.ActivateDate = DateTime.ParseExact(collection["ACTIVATEDATE"]!, "dd.MM.yyyy HH:mm:ss",
                                ruCultureInfo);

                        if (collection["ISREACTIVATED"] != null)
                            licenseModel.IsReactivated = Convert.ToBoolean(collection["ISREACTIVATED"]);
                        if (collection["LASTSEAT"] != null) licenseModel.LastSeat = collection["LASTSEAT"]!;
                    }
                }
                else
                {
                    licenseModel.IsConnectOk = false;
                    licenseModel.ErrorConnect = webResult.ErrorText;
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
                licenseModel.IsConnectOk = false;
                licenseModel.ErrorParce = e.ToString();
            }

            return licenseModel;
        });
    }

    internal async Task<LicenseModel> GetLicExternalTrialAsync()
    {
        return await Task.Run(() =>
        {
            var sbfr = new LicenseModel();
            try
            {
                var stringBuilder = new StringBuilder();

                stringBuilder.Append("MACHINEID=" + SettingsManager.MachineId + "&");

                stringBuilder.Append("MACHINENAME=" + Environment.MachineName + "&");

                stringBuilder.Append("LICENSE=&");

                stringBuilder.Append("TRIAL=true");

                var data = "=" + HttpUtility.UrlEncode(Secure.EncryptStringAES(stringBuilder.ToString(), SECRET));

                var netLib = new NetLib(URL_TO_CHECK_TRIAL, data);

                var webResult = netLib.StartPostWebRequest();

                if (!webResult.WithError)
                {
                    try
                    {
                        if (webResult.ResultText != null)
                        {
                            var result = Secure.DecryptStringAES(webResult.ResultText.Trim('"'), SECRET);

                            var collection = HttpUtility.ParseQueryString(result);

                            if (collection["ERRORLIC"] != null) sbfr.ErrorLic = collection["ERRORLIC"]!;

                            if (string.IsNullOrEmpty(sbfr.ErrorLic) && collection["ACTIVATEDATE"] != null && collection["LICENSE"] != null)
                            {
                                var ruCultureInfo = CultureInfo.CreateSpecificCulture("ru-RU");
                                sbfr.ActivateDate = DateTime.ParseExact(collection["ACTIVATEDATE"]!, "dd.MM.yyyy HH:mm:ss",
                                    ruCultureInfo);

                                sbfr.LicExpiryDate = sbfr.ActivateDate.AddDays(40);

                                sbfr.License = collection["LICENSE"]!;
                            }
                        }

                        sbfr.IsConnectOk = true;
                        sbfr.LicIsTrial = true;
                        return sbfr;
                    }
                    catch (Exception e)
                    {
                        sbfr.IsConnectOk = false;
                        sbfr.ErrorParce = e.Message;
                    }
                }
                else
                {
                    sbfr.IsConnectOk = false;
                    sbfr.ErrorConnect = webResult.ErrorText;
                }

                return sbfr;
            }
            catch (Exception e)
            {
                sbfr.IsConnectOk = false;

                sbfr.ErrorConnect = e.Message;
            }

            return sbfr;
        });
    }
}

