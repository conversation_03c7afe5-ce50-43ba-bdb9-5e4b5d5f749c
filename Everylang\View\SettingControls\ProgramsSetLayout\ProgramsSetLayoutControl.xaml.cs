﻿using Everylang.App.ViewModels;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;

namespace Everylang.App.View.SettingControls.ProgramsSetLayout
{
    /// <summary>
    /// Interaction logic for ProgramsExceptionsControl.xaml
    /// </summary>
    internal partial class ProgramsSetLayoutControl : UserControl
    {
        internal ProgramsSetLayoutControl()
        {
            InitializeComponent();
        }

        private void HelpOpenClick(object sender, RoutedEventArgs e)
        {
            Process.Start("https://docs.everylang.net");
        }

        private void Selector_OnSelected(object sender, RoutedEventArgs e)
        {
            VMContainer.Instance.ProgramsSetLayoutViewModel.UpdateAllInDb();
        }
    }
}
