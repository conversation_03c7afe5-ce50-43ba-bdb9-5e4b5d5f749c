﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.SettingControls.Miminote.MiminoteControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib"
    xmlns:componentModel="clr-namespace:System.ComponentModel;assembly=WindowsBase"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
    xmlns:viewModels="clr-namespace:Everylang.App.ViewModels"
    x:ClassModifier="internal"
    DataContext="{Binding Source={x:Static viewModels:VMContainer.Instance}}">
    <UserControl.Resources>
        <CollectionViewSource x:Key="SystemFontFamilies" Source="{Binding Source={x:Static Fonts.SystemFontFamilies}}" >
            <CollectionViewSource.SortDescriptions>
                <componentModel:SortDescription PropertyName="Source" />
            </CollectionViewSource.SortDescriptions>
        </CollectionViewSource>
        <ObjectDataProvider x:Key="SystemFontSizes">
            <ObjectDataProvider.ObjectInstance>
                <x:Array Type="system:Double">
                    <system:Double>8</system:Double>
                    <system:Double>9</system:Double>
                    <system:Double>10</system:Double>
                    <system:Double>11</system:Double>
                    <system:Double>12</system:Double>
                    <system:Double>13</system:Double>
                    <system:Double>14</system:Double>
                    <system:Double>15</system:Double>
                    <system:Double>16</system:Double>
                    <system:Double>18</system:Double>
                    <system:Double>20</system:Double>
                    <system:Double>22</system:Double>
                    <system:Double>24</system:Double>
                    <system:Double>26</system:Double>
                    <system:Double>28</system:Double>
                    <system:Double>36</system:Double>
                    <system:Double>48</system:Double>
                    <system:Double>72</system:Double>
                </x:Array>
            </ObjectDataProvider.ObjectInstance>
        </ObjectDataProvider>
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}" IsEnabled="{Binding Path=NotesListControlViewModel.jgebhdhs}">
        <telerik:RadButton
            Click="HelpOpenClick"
            CornerRadius="2,2,2,2"
            Focusable="False"
            Grid.ZIndex="1"
            HorizontalAlignment="Right"
            IsBackgroundVisible="False"
            Margin="2"
            MinHeight="0"
            Padding="10"
            VerticalAlignment="Top">
            <wpf:MaterialIcon
                Height="15"
                Kind="Help"
                Width="15" />
        </telerik:RadButton>
        <telerik:RadTransitionControl
            Duration="0:0:0.5"
            Grid.Column="0"
            Grid.Row="0"
            Grid.ZIndex="1"
            Margin="0"
            Transition="Fade"
            x:Name="PageTransitionControl" />
        <StackPanel Margin="20,10,0,0">
            <StackPanel Margin="0,0,0,0" Orientation="Horizontal">
                <TextBlock FontSize="15" FontWeight="Bold" Text="{telerik:LocalizableResource Key=NoteTab}" />
                <TextBlock FontSize="15" Margin="7,0,0,0" FontWeight="Bold" Text="{telerik:LocalizableResource Key=OnlyPro}" />
            </StackPanel>

            <StackPanel Margin="0,10,0,0">
                <TextBlock FontSize="14" Text="{telerik:LocalizableResource Key=NotesAddNew}" />
                <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                    <TextBox
                        Background="Transparent"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        IsReadOnly="True"
                        Text="{Binding Path=NotesListControlViewModel.AddNewShortcut}"
                        ToolTip="{Binding Path=NotesListControlViewModel.AddNewShortcut}"
                        Width="350" />
                    <telerik:RadButton
                        Click="NewShortCutClick"
                        Content="{telerik:LocalizableResource Key=Edit}"
                        Focusable="False"
                        HorizontalAlignment="Left"
                        Margin="5,0,0,0"
                        Padding="5,0,5,0" />
                </StackPanel>
            </StackPanel>

            <TextBlock Margin="0 10 0 5" FontSize="14" Text="{telerik:LocalizableResource Key=NoteFontFamilyAndSize}"/>
            <StackPanel Orientation="Horizontal">
                <telerik:RadComboBox Name="cbFontFamily" Width="200"
                                     IsEditable="False"
                                     Focusable="False"
                                     SelectedItem="{Binding NotesListControlViewModel.FontFamilyDefault}"
                                     IsTextSearchEnabled="True"
                                     IsManipulationEnabled="True"
                                     ToolTip="Font">
                    <telerik:RadComboBox.ItemsSource>
                        <Binding Source="{StaticResource SystemFontFamilies}" />
                    </telerik:RadComboBox.ItemsSource>
                </telerik:RadComboBox>

                <telerik:RadComboBox  Name="cbFontSize"
                                      Margin="15,0,0,0"
                                      Width="80"
                                      Text="{Binding NotesListControlViewModel.SizeFontDefaultText,Mode=TwoWay}"
                                      VerticalAlignment="Bottom" 
                                      IsEditable="True"
                                      IsTextSearchEnabled="True"
                                      IsManipulationEnabled="True"
                                      ToolTip="Font"
                                      Focusable="False"
                                      FontSize="14">
                    <telerik:RadComboBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <VirtualizingStackPanel />
                        </ItemsPanelTemplate>
                    </telerik:RadComboBox.ItemsPanel>
                    <telerik:RadComboBox.ItemsSource>
                        <Binding Source="{StaticResource SystemFontSizes}" />
                    </telerik:RadComboBox.ItemsSource>
                </telerik:RadComboBox>
            </StackPanel>

            <TextBlock Margin="0, 15, 0, 0" FontSize="14" Text="{telerik:LocalizableResource Key=NoteTransparencyForInactiveNotes}"/>
            <telerik:RadSlider Width="250" Margin="5, 5, 0, 0" Value="{Binding NotesListControlViewModel.TransparencyForNotes}" AutoToolTipPlacement="BottomRight" HorizontalAlignment="Left" Maximum="100" Minimum="50"/>

        </StackPanel>
    </Grid>
</UserControl>
