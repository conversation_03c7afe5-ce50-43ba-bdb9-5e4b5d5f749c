﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Linq;

namespace Everylang.App.Data.DataStore
{
    internal class SettingsDataManager
    {
        private static bool _isGetNow;

        internal static void GetSettings(SettingsDataModel settings)
        {
            _isGetNow = true;
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("SettingsDataModel");
                    if (schemelessCollection != null)
                    {
                        var bsonDocument = schemelessCollection.FindAll().FirstOrDefault();
                        if (bsonDocument != null)
                        {
                            if (!bsonDocument["HashFromSite"].IsNull)
                                settings.HashFromSite = bsonDocument["HashFromSite"].AsString;
                            if (!bsonDocument["LastVersionForCheckHash"].IsNull)
                                settings.LastVersionForCheckHash = bsonDocument["LastVersionForCheckHash"].AsString;
                            if (!bsonDocument["MainFormMinimizeToTray"].IsNull)
                                settings.MainFormMinimizeToTray = bsonDocument["MainFormMinimizeToTray"].AsBoolean;
                            if (!bsonDocument["CanClose"].IsNull)
                                settings.CanClose = bsonDocument["CanClose"].AsBoolean;
                            if (!bsonDocument["AppUILang"].IsNull)
                                settings.AppUILang = bsonDocument["AppUILang"].AsString;
                            if (!bsonDocument["AppUITheme"].IsNull)
                                settings.AppUITheme = bsonDocument["AppUITheme"].AsString;
                            if (!bsonDocument["AppUIAccent"].IsNull)
                                settings.AppUIAccent = bsonDocument["AppUIAccent"].AsString;
                            if (!bsonDocument["AppFont"].IsNull)
                                settings.AppFont = bsonDocument["AppFont"].AsString;
                            if (!bsonDocument["AppFontSize"].IsNull)
                                settings.AppFontSize = bsonDocument["AppFontSize"].AsInt32;
                            if (!bsonDocument["OpenMainWindowShortcut"].IsNull)
                                settings.OpenMainWindowShortcut = bsonDocument["OpenMainWindowShortcut"].AsString;
                            if (!bsonDocument["StopWorkingShortcut"].IsNull)
                                settings.StopWorkingShortcut = bsonDocument["StopWorkingShortcut"].AsString;
                            if (!bsonDocument["IsCheckUpdate"].IsNull)
                                settings.IsCheckUpdate = bsonDocument["IsCheckUpdate"].AsBoolean;
                            if (!bsonDocument["IsCheckUpdateBeta"].IsNull)
                                settings.IsCheckUpdateBeta = bsonDocument["IsCheckUpdateBeta"].AsBoolean;
                            if (!bsonDocument["IsCheckedStartAsAdmin"].IsNull)
                                settings.IsCheckedStartAsAdmin = bsonDocument["IsCheckedStartAsAdmin"].AsBoolean;
                            if (!bsonDocument["CurrentVersion"].IsNull)
                                settings.CurrentVersion = bsonDocument["CurrentVersion"].AsString;
                            if (!bsonDocument["FunctionOrder"].IsNull)
                                settings.FunctionOrder = bsonDocument["FunctionOrder"].AsString;
                            if (!bsonDocument["ThemeTimeStartNight"].IsNull)
                                settings.ThemeTimeStartNight = bsonDocument["ThemeTimeStartNight"].AsDateTime;
                            if (!bsonDocument["ThemeTimeEndNight"].IsNull)
                                settings.ThemeTimeEndNight = bsonDocument["ThemeTimeEndNight"].AsDateTime;
                            if (!bsonDocument["IsUseNightTheme"].IsNull)
                                settings.IsUseNightTheme = bsonDocument["IsUseNightTheme"].AsBoolean;
                            if (!bsonDocument["IsStopWorkingFullScreen"].IsNull)
                                settings.IsStopWorkingFullScreen = bsonDocument["IsStopWorkingFullScreen"].AsBoolean;
                            if (!bsonDocument["ProxyUseIE"].IsNull)
                                settings.ProxyUseIE = bsonDocument["ProxyUseIE"].AsBoolean;
                            if (!bsonDocument["ProxyServer"].IsNull)
                                settings.ProxyServer = bsonDocument["ProxyServer"].AsString;
                            if (!bsonDocument["ProxyPort"].IsNull)
                                settings.ProxyPort = bsonDocument["ProxyPort"].AsString;
                            if (!bsonDocument["ProxyUserName"].IsNull)
                                settings.ProxyUserName = bsonDocument["ProxyUserName"].AsString;
                            if (!bsonDocument["ProxyPassword"].IsNull)
                                settings.ProxyPassword = bsonDocument["ProxyPassword"].AsString;
                            if (!bsonDocument["LicCode"].IsNull) settings.LicCode = bsonDocument["LicCode"].AsString;
                            if (!bsonDocument["LicEmail"].IsNull) settings.LicEmail = bsonDocument["LicEmail"].AsString;
                            if (!bsonDocument["LicIsActive"].IsNull)
                                settings.LicIsActive = bsonDocument["LicIsActive"].AsBoolean;
                            if (!bsonDocument["LicIsEvaluate"].IsNull)
                                settings.LicIsEvaluate = bsonDocument["LicIsEvaluate"].AsBoolean;
                            if (!bsonDocument["LicEvaluateCode"].IsNull)
                                settings.LicEvaluateCode = bsonDocument["LicEvaluateCode"].AsString;
                            if (!bsonDocument["LicEvaluateStart"].IsNull)
                                settings.LicEvaluateStart = bsonDocument["LicEvaluateStart"].AsDateTime;
                            if (!bsonDocument["ClipboardFastActionWindowSize"].IsNull)
                                settings.ClipboardFastActionWindowSize =
                                    bsonDocument["ClipboardFastActionWindowSize"].AsString;
                            if (!bsonDocument["DiaryActionWindowSize"].IsNull)
                                settings.DiaryActionWindowSize = bsonDocument["DiaryActionWindowSize"].AsString;
                            if (!bsonDocument["AutochangeActionWindowSize"].IsNull)
                                settings.SnippetsActionWindowSize =
                                    bsonDocument["AutochangeActionWindowSize"].AsString;
                            if (!bsonDocument["TranslateLangFrom"].IsNull)
                                settings.TranslateLangFrom = bsonDocument["TranslateLangFrom"].AsString;
                            if (!bsonDocument["TranslateLangTo"].IsNull)
                                settings.TranslateLangTo = bsonDocument["TranslateLangTo"].AsString;
                            if (!bsonDocument["TranslateProvider"].IsNull)
                                settings.TranslateProvider = bsonDocument["TranslateProvider"].AsInt32;
                            if (!bsonDocument["TranslateFavoriteLanguages"].IsNull)
                                settings.TranslateFavoriteLanguages = bsonDocument["TranslateFavoriteLanguages"].AsString;
                            if (!bsonDocument["TranslateOnlyFavoriteLanguages"].IsNull)
                                settings.TranslateOnlyFavoriteLanguages = bsonDocument["TranslateOnlyFavoriteLanguages"].AsBoolean;
                            if (!bsonDocument["TranslateShowMiniFormAlwaysWhenSelectText"].IsNull)
                                settings.TranslateShowMiniFormAlwaysWhenSelectText =
                                    bsonDocument["TranslateShowMiniFormAlwaysWhenSelectText"].AsBoolean;
                            if (!bsonDocument["TranslateShowMiniFormDoubleCtrl"].IsNull)
                                settings.TranslateShowMiniFormDoubleCtrl =
                                    bsonDocument["TranslateShowMiniFormDoubleCtrl"].AsBoolean;
                            if (!bsonDocument["TranslateShowMiniFormShortcut"].IsNull)
                                settings.TranslateShowMiniFormShortcut =
                                    bsonDocument["TranslateShowMiniFormShortcut"].AsString;
                            if (!bsonDocument["TranslateIsOn"].IsNull)
                                settings.TranslateIsOn = bsonDocument["TranslateIsOn"].AsBoolean;
                            if (!bsonDocument["TranslateHistoryIsOn"].IsNull)
                                settings.TranslateHistoryIsOn = bsonDocument["TranslateHistoryIsOn"].AsBoolean;
                            if (!bsonDocument["TranslateFontFamily"].IsNull)
                                settings.TranslateFontFamily = bsonDocument["TranslateFontFamily"].AsString;
                            if (!bsonDocument["TranslateFontSize"].IsNull)
                                settings.TranslateFontSize = bsonDocument["TranslateFontSize"].AsString;
                            if (!bsonDocument["SpellCheckShortcut"].IsNull)
                                settings.SpellCheckShortcut = bsonDocument["SpellCheckShortcut"].AsString;
                            if (!bsonDocument["SpellCheckCloseByTimer"].IsNull)
                                settings.SpellCheckCloseByTimer = bsonDocument["SpellCheckCloseByTimer"].AsBoolean;
                            if (!bsonDocument["SpellCheckWhileTyping"].IsNull)
                                settings.SpellCheckWhileTyping = bsonDocument["SpellCheckWhileTyping"].AsBoolean;
                            if (!bsonDocument["SpellCheckWhileTypingSoundOn"].IsNull)
                                settings.SpellCheckWhileTypingSoundOn =
                                    bsonDocument["SpellCheckWhileTypingSoundOn"].AsBoolean;
                            if (!bsonDocument["SpellCheckWhileTypingUseNumber"].IsNull)
                                settings.SpellCheckWhileTypingUseNumber =
                                    bsonDocument["SpellCheckWhileTypingUseNumber"].AsBoolean;
                            if (!bsonDocument["SpellCheckIsOn"].IsNull)
                                settings.SpellCheckIsOn = bsonDocument["SpellCheckIsOn"].AsBoolean;
                            if (!bsonDocument["SwitcherIsOn"].IsNull)
                                settings.SwitcherIsOn = bsonDocument["SwitcherIsOn"].AsBoolean;
                            if (!bsonDocument["SwitcherLeaveTextSelectedAfterSwitch"].IsNull)
                                settings.SwitcherLeaveTextSelectedAfterSwitch =
                                    bsonDocument["SwitcherLeaveTextSelectedAfterSwitch"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchTextLangShortcut"].IsNull)
                                settings.SwitcherSwitchTextLangShortcut =
                                    bsonDocument["SwitcherSwitchTextLangShortcut"].AsString;
                            if (!bsonDocument["SwitcherSwitchTextLangForAllLineShortcut"].IsNull)
                                settings.SwitcherSwitchTextLangForAllLineShortcut =
                                    bsonDocument["SwitcherSwitchTextLangForAllLineShortcut"].AsString;
                            if (!bsonDocument["SwitcherShortcutSwitchLangSelectedText"].IsNull)
                                settings.SwitcherShortcutSwitchLangSelectedTextShortcut =
                                    bsonDocument["SwitcherShortcutSwitchLangSelectedText"].AsString;
                            if (!bsonDocument["ConverterShortcutCapsOpenWindow"].IsNull)
                                settings.ConverterShortcutCapsOpenWindow =
                                    bsonDocument["ConverterShortcutCapsOpenWindow"].AsString;
                            if (!bsonDocument["SwitcherShortcutSwitchCapsSelectedText"].IsNull)
                                settings.ConverterShortcutCapsInvert =
                                    bsonDocument["SwitcherShortcutSwitchCapsSelectedText"].AsString;
                            if (!bsonDocument["SwitcherShortcutSwitchCapsUpSelectedTextShortcut"].IsNull)
                                settings.ConverterShortcutCapsUp =
                                    bsonDocument["SwitcherShortcutSwitchCapsUpSelectedTextShortcut"].AsString;
                            if (!bsonDocument["SwitcherShortcutSwitchCapsDownSelectedTextShortcut"].IsNull)
                                settings.ConverterShortcutCapsDown =
                                    bsonDocument["SwitcherShortcutSwitchCapsDownSelectedTextShortcut"].AsString;
                            if (!bsonDocument["ConverterFirstLetterToUp"].IsNull)
                                settings.ConverterFirstLetterToUp = bsonDocument["ConverterFirstLetterToUp"].AsString;
                            if (!bsonDocument["ConverterFirstLetterToDown"].IsNull)
                                settings.ConverterFirstLetterToDown =
                                    bsonDocument["ConverterFirstLetterToDown"].AsString;

                            if (!bsonDocument["SwitcherSwitchTextLangByBreak"].IsNull)
                                settings.SwitcherSwitchTextLangByBreak =
                                    bsonDocument["SwitcherSwitchTextLangByBreak"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchTextLangByDoubleShift"].IsNull)
                                settings.SwitcherSwitchTextLangByDoubleShift =
                                    bsonDocument["SwitcherSwitchTextLangByDoubleShift"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchTextLangByDoubleScrollLock"].IsNull)
                                settings.SwitcherSwitchTextLangByDoubleScrollLock =
                                    bsonDocument["SwitcherSwitchTextLangByDoubleScrollLock"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchTextLangForAllLineByInsert"].IsNull)
                                settings.SwitcherSwitchTextLangForAllLineByInsert =
                                    bsonDocument["SwitcherSwitchTextLangForAllLineByInsert"].AsBoolean;

                            if (!bsonDocument["SwitcherSountIsOn"].IsNull)
                                settings.SwitcherSountIsOn = bsonDocument["SwitcherSountIsOn"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchLangByCtrlPlusNumberIsOn"].IsNull)
                                settings.SwitcherSwitchLangByCtrlPlusNumberIsOn =
                                    bsonDocument["SwitcherSwitchLangByCtrlPlusNumberIsOn"].AsBoolean;
                            if (!bsonDocument["SwitcherSwitchLangByNonStandartKey"].IsNull)
                                settings.SwitcherSwitchLangByNonStandartKey =
                                    bsonDocument["SwitcherSwitchLangByNonStandartKey"].AsInt32;
                            if (!bsonDocument["SwitcherSwitchMethod"].IsNull)
                                settings.SwitcherSwitchMethod =
                                    bsonDocument["SwitcherSwitchMethod"].AsInt32;
                            if (!bsonDocument["SwitcherTrueListOfLang"].IsNull)
                                settings.SwitcherNotTrueListOfLang = bsonDocument["SwitcherTrueListOfLang"].AsString;
                            if (!bsonDocument["SwitcherLangAndKeysForSwitch"].IsNull)
                                settings.SwitcherLangAndKeysForSwitch =
                                    bsonDocument["SwitcherLangAndKeysForSwitch"].AsString;
                            if (!bsonDocument["AutoSwitcherIsOn"].IsNull)
                                settings.AutoSwitcherIsOn = bsonDocument["AutoSwitcherIsOn"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherTrueListOfLang"].IsNull)
                                settings.AutoSwitcherNotTrueListOfLang =
                                    bsonDocument["AutoSwitcherTrueListOfLang"].AsString;
                            if (!bsonDocument["AutoSwitcherFixTwoUpperCaseLettersInStart"].IsNull)
                                settings.AutoSwitcherFixTwoUpperCaseLettersInStart =
                                    bsonDocument["AutoSwitcherFixTwoUpperCaseLettersInStart"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherFixWrongUpperCase"].IsNull)
                                settings.AutoSwitcherFixWrongUpperCase =
                                    bsonDocument["AutoSwitcherFixWrongUpperCase"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherIsSwitchOneLetter"].IsNull)
                                settings.AutoSwitcherIsSwitchOneLetter =
                                    bsonDocument["AutoSwitcherIsSwitchOneLetter"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters"].IsNull)
                                settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters =
                                    bsonDocument["AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherSwitchTextLangAfterPressEnter"].IsNull)
                                settings.AutoSwitcherSwitchTextLangAfterPressEnter =
                                    bsonDocument["AutoSwitcherSwitchTextLangAfterPressEnter"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherAddRule"].IsNull)
                                settings.AutoSwitcherAddRule = bsonDocument["AutoSwitcherAddRule"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherDisableAutoSwitchAfterManualSwitch"].IsNull)
                                settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch =
                                    bsonDocument["AutoSwitcherDisableAutoSwitchAfterManualSwitch"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherShowAcceptWindow"].IsNull)
                                settings.AutoSwitcherShowAcceptWindow =
                                    bsonDocument["AutoSwitcherShowAcceptWindow"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherOnlyAfterSeparator"].IsNull)
                                settings.AutoSwitcherOnlyAfterSeparator =
                                    bsonDocument["AutoSwitcherOnlyAfterSeparator"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherAfterPause"].IsNull)
                                settings.AutoSwitcherAfterPause =
                                    bsonDocument["AutoSwitcherAfterPause"].AsBoolean;
                            if (!bsonDocument["AutoSwitcherCountCheckRule"].IsNull)
                                settings.AutoSwitcherCountCheckRule =
                                    bsonDocument["AutoSwitcherCountCheckRule"].AsInt32;
                            if (!bsonDocument["LangFlagShowForMouse"].IsNull)
                                settings.LangFlagShowForMouse = bsonDocument["LangFlagShowForMouse"].AsBoolean;
                            if (!bsonDocument["LangFlagShowForCaret"].IsNull)
                                settings.LangFlagShowForCaret = bsonDocument["LangFlagShowForCaret"].AsBoolean;
                            if (!bsonDocument["LangFlagShowLargeWindow"].IsNull)
                                settings.LangFlagShowLargeWindow = bsonDocument["LangFlagShowLargeWindow"].AsBoolean;
                            if (!bsonDocument["LangFlagShowInTray"].IsNull)
                                settings.LangFlagShowInTray = bsonDocument["LangFlagShowInTray"].AsBoolean;
                            if (!bsonDocument["LangFlagShowIcons"].IsNull)
                                settings.LangFlagShowIcons = bsonDocument["LangFlagShowIcons"].AsBoolean;
                            if (!bsonDocument["LangFlagPosCarretX"].IsNull)
                                settings.LangFlagPosCarretX = bsonDocument["LangFlagPosCarretX"].AsInt32;
                            if (!bsonDocument["LangFlagPosCarretY"].IsNull)
                                settings.LangFlagPosCarretY = bsonDocument["LangFlagPosCarretY"].AsInt32;
                            if (!bsonDocument["LangFlagPosMouseX"].IsNull)
                                settings.LangFlagPosMouseX = bsonDocument["LangFlagPosMouseX"].AsInt32;
                            if (!bsonDocument["LangFlagPosMouseY"].IsNull)
                                settings.LangFlagPosMouseY = bsonDocument["LangFlagPosMouseY"].AsInt32;
                            if (!bsonDocument["LangInfoLargeWindowPosX"].IsNull)
                                settings.LangInfoLargeWindowPosX = bsonDocument["LangInfoLargeWindowPosX"].AsDouble;
                            if (!bsonDocument["LangInfoLargeWindowPosY"].IsNull)
                                settings.LangInfoLargeWindowPosY = bsonDocument["LangInfoLargeWindowPosY"].AsDouble;
                            if (!bsonDocument["LangFlagOpacityIcon"].IsNull)
                                settings.LangFlagOpacityIcon = bsonDocument["LangFlagOpacityIcon"].AsInt32;
                            if (!bsonDocument["LangFlagSizeIcon"].IsNull)
                                settings.LangFlagSizeIcon = bsonDocument["LangFlagSizeIcon"].AsInt32;
                            if (!bsonDocument["IsHideIndicateInFullScreenApp"].IsNull)
                                settings.IsHideIndicateInFullScreenApp =
                                    bsonDocument["IsHideIndicateInFullScreenApp"].AsBoolean;
                            if (!bsonDocument["LangFlagIsIndicateCapsLockState"].IsNull)
                                settings.LangFlagIsIndicateCapsLockState =
                                    bsonDocument["LangFlagIsIndicateCapsLockState"].AsBoolean;
                            if (!bsonDocument["LangFlagIsIndicateNumLockState"].IsNull)
                                settings.LangFlagIsIndicateNumLockState =
                                    bsonDocument["LangFlagIsIndicateNumLockState"].AsBoolean;
                            if (!bsonDocument["ClipboardPasteWithoutFormattingShortcutIsOn"].IsNull)
                                settings.ClipboardPasteWithoutFormattingShortcutIsOn =
                                    bsonDocument["ClipboardIsOn"].AsBoolean;
                            if (!bsonDocument["ClipboardHistoryIsOn"].IsNull)
                                settings.ClipboardHistoryIsOn = bsonDocument["ClipboardHistoryIsOn"].AsBoolean;
                            if (!bsonDocument["ClipboardShowHistoryShortcut"].IsNull)
                                settings.ClipboardShowHistoryShortcut =
                                    bsonDocument["ClipboardShowHistoryShortcut"].AsString;
                            if (!bsonDocument["ClipboardPasteWithoutFormattingShortcut"].IsNull)
                                settings.ClipboardPasteWithoutFormattingShortcut =
                                    bsonDocument["ClipboardPasteWithoutFormattingShortcut"].AsString;
                            if (!bsonDocument["ClipboardReplaceWithoutChangeClipboard"].IsNull)
                                settings.ClipboardReplaceWithoutChangeClipboard =
                                    bsonDocument["ClipboardReplaceWithoutChangeClipboard"].AsBoolean;
                            if (!bsonDocument["ClipboardPasteRoundIsOn"].IsNull)
                                settings.ClipboardPasteRoundIsOn = bsonDocument["ClipboardPasteRoundIsOn"].AsBoolean;
                            if (!bsonDocument["ClipboardPasteRoundShortcut"].IsNull)
                                settings.ClipboardPasteRoundShortcut =
                                    bsonDocument["ClipboardPasteRoundShortcut"].AsString;
                            if (!bsonDocument["ClipboardPasteByCtrlPlusIndexIsOn"].IsNull)
                                settings.ClipboardPasteByCtrlPlusIndexIsOn =
                                    bsonDocument["ClipboardPasteByCtrlPlusIndexIsOn"].AsBoolean;
                            if (!bsonDocument["ClipboardSaveFilePath"].IsNull)
                                settings.ClipboardSaveFilePath = bsonDocument["ClipboardSaveFilePath"].AsBoolean;
                            if (!bsonDocument["ClipboardSaveImage"].IsNull)
                                settings.ClipboardSaveImage = bsonDocument["ClipboardSaveImage"].AsBoolean;
                            if (!bsonDocument["ClipboardMaxClipboardItems"].IsNull)
                                settings.ClipboardMaxClipboardItems =
                                    bsonDocument["ClipboardMaxClipboardItems"].AsInt32;
                            if (!bsonDocument["ClipboardSountIsOn"].IsNull)
                                settings.ClipboardSountIsOn =
                                    bsonDocument["ClipboardSountIsOn"].AsBoolean;
                            if (!bsonDocument["DiaryIsOn"].IsNull)
                                settings.DiaryIsOn = bsonDocument["DiaryIsOn"].AsBoolean;
                            if (!bsonDocument["MaxDiaryItems"].IsNull)
                                settings.MaxDiaryItems = bsonDocument["MaxDiaryItems"].AsInt32;
                            if (!bsonDocument["DiaryPassword"].IsNull)
                                settings.DiaryPassword = bsonDocument["DiaryPassword"].AsString;
                            if (!bsonDocument["IsSaveOneWordSentences"].IsNull)
                                settings.IsSaveOneWordSentences = bsonDocument["IsSaveOneWordSentences"].AsBoolean;
                            if (!bsonDocument["DiaryShowShortcut"].IsNull)
                                settings.DiaryShowShortcut = bsonDocument["DiaryShowShortcut"].AsString;
                            if (!bsonDocument["SmartClickIsOn"].IsNull)
                                settings.SmartClickIsOn = bsonDocument["SmartClickIsOn"].AsBoolean;
                            if (!bsonDocument["SmartClickSearchService"].IsNull)
                                settings.SmartClickSearchService = bsonDocument["SmartClickSearchService"].AsInt32;
                            if (!bsonDocument["SmartClickShowOnPressLeftAndRightMouseButtons"].IsNull)
                                settings.SmartClickShowOnPressLeftAndRightMouseButtons =
                                    bsonDocument["SmartClickShowOnPressLeftAndRightMouseButtons"].AsBoolean;
                            if (!bsonDocument["SmartClickShowOnPressHotKeys"].IsNull)
                                settings.SmartClickShowOnPressHotKeys =
                                    bsonDocument["SmartClickShowOnPressHotKeys"].AsBoolean;
                            if (!bsonDocument["SmartClickShortcut"].IsNull)
                                settings.SmartClickShortcut = bsonDocument["SmartClickShortcut"].AsString;
                            if (!bsonDocument["SmartClickShowOnDoubleMiddle"].IsNull)
                                settings.SmartClickShowOnDoubleMiddle =
                                    bsonDocument["SmartClickShowOnDoubleMiddle"].AsBoolean;
                            if (!bsonDocument["SmartClickMiniIsOn"].IsNull)
                                settings.SmartClickMiniIsOn = bsonDocument["SmartClickMiniIsOn"].AsBoolean;
                            if (!bsonDocument["SmartClickMiniSizeIcon"].IsNull)
                                settings.SmartClickMiniSizeIcon = bsonDocument["SmartClickMiniSizeIcon"].AsInt32;
                            if (!bsonDocument["SmartClickMiniPosX"].IsNull)
                                settings.SmartClickMiniPosX = bsonDocument["SmartClickMiniPosX"].AsDouble;
                            if (!bsonDocument["SmartClickMiniPosY"].IsNull)
                                settings.SmartClickMiniPosY = bsonDocument["SmartClickMiniPosY"].AsDouble;
                            if (!bsonDocument["SmartClickCheckedItems"].IsNull)
                                settings.SmartClickCheckedItems = bsonDocument["SmartClickCheckedItems"].AsString;
                            if (!bsonDocument["AutoReplaceIsOn"].IsNull)
                                settings.SnippetsIsOn = bsonDocument["AutoReplaceIsOn"].AsBoolean;
                            if (!bsonDocument["AutochangeIsEnabledCountUsage"].IsNull)
                                settings.SnippetsIsEnabledCountUsage = bsonDocument["AutochangeIsEnabledCountUsage"].AsBoolean;
                            if (!bsonDocument["SnippetsIsEnabledSortingAlphabet"].IsNull)
                                settings.SnippetsIsEnabledSortingAlphabet = bsonDocument["SnippetsIsEnabledSortingAlphabet"].AsBoolean;
                            if (!bsonDocument["AutoReplaceIsShowTipWindow"].IsNull)
                                settings.SnippetsIsShowTipWindow =
                                    bsonDocument["AutoReplaceIsShowTipWindow"].AsBoolean;
                            if (!bsonDocument["AutoReplaceWithOtherLayout"].IsNull)
                                settings.SnippetsWithOtherLayout =
                                    bsonDocument["AutoReplaceWithOtherLayout"].AsBoolean;
                            if (!bsonDocument["AutoReplaceIsCaseSensitive"].IsNull)
                                settings.SnippetsIsCaseSensitive =
                                    bsonDocument["AutoReplaceIsCaseSensitive"].AsBoolean;
                            if (!bsonDocument["AutoReplaceMethodPastByKey"].IsNull)
                                settings.SnippetsMethodPastByKey =
                                    bsonDocument["AutoReplaceMethodPastByKey"].AsInt32;
                            if (!bsonDocument["AutoReplaceShowAllShortcut"].IsNull)
                                settings.SnippetsShowAllShortcut =
                                    bsonDocument["AutoReplaceShowAllShortcut"].AsString;
                            if (!bsonDocument["AutoReplaceAddNewShortcut"].IsNull)
                                settings.SnippetsAddNewShortcut =
                                    bsonDocument["AutoReplaceAddNewShortcut"].AsString;
                            if (!bsonDocument["ConverterTransliterationShortcut"].IsNull)
                                settings.ConverterTransliterationShortcut =
                                    bsonDocument["ConverterTransliterationShortcut"].AsString;
                            if (!bsonDocument["ConverterExpresionShortcut"].IsNull)
                                settings.ConverterExpresionShortcut =
                                    bsonDocument["ConverterExpresionShortcut"].AsString;
                            if (!bsonDocument["ConverterOpenWindowShortcut"].IsNull)
                                settings.ConverterOpenWindowShortcut =
                                    bsonDocument["ConverterOpenWindowShortcut"].AsString;
                            if (!bsonDocument["ConverterEncloseTextQuotationMarksShortcut"].IsNull)
                                settings.ConverterEncloseTextQuotationMarksShortcut =
                                    bsonDocument["ConverterEncloseTextQuotationMarksShortcut"].AsString;
                            if (!bsonDocument["ConverterShortcutCamelCase"].IsNull)
                                settings.ConverterShortcutCamelCase =
                                    bsonDocument["ConverterShortcutCamelCase"].AsString;
                            if (!bsonDocument["ConverterShortcutPascalCase"].IsNull)
                                settings.ConverterShortcutPascalCase =
                                    bsonDocument["ConverterShortcutPascalCase"].AsString;
                            if (!bsonDocument["ConverterShortcutSnakeCase"].IsNull)
                                settings.ConverterShortcutSnakeCase =
                                    bsonDocument["ConverterShortcutSnakeCase"].AsString;
                            if (!bsonDocument["ConverterShortcutKebabCase"].IsNull)
                                settings.ConverterShortcutKebabCase =
                                    bsonDocument["ConverterShortcutKebabCase"].AsString;
                            if (!bsonDocument["ConverterShortcutReplaceSelText"].IsNull)
                                settings.ConverterShortcutReplaceSelText =
                                    bsonDocument["ConverterShortcutReplaceSelText"].AsString;
                            if (!bsonDocument["ConverterFramesList"].IsNull)
                                settings.ConverterFramesList = bsonDocument["ConverterFramesList"].AsString;
                            if (!bsonDocument["SoundForLangSwitch"].IsNull)
                                settings.SoundForLangSwitch = bsonDocument["SoundForLangSwitch"].AsString;
                            if (!bsonDocument["SoundForSpellCheck"].IsNull)
                                settings.SoundForSpellCheck = bsonDocument["SoundForSpellCheck"].AsString;
                            if (!bsonDocument["SoundForClipboard"].IsNull)
                                settings.SoundForClipboard = bsonDocument["SoundForClipboard"].AsString;
                            if (!bsonDocument["SoundVolumeForLangSwitch"].IsNull)
                                settings.SoundVolumeForLangSwitch = bsonDocument["SoundVolumeForLangSwitch"].AsInt32;
                            if (!bsonDocument["SoundVolumeForClipboard"].IsNull)
                                settings.SoundVolumeForClipboard = bsonDocument["SoundVolumeForClipboard"].AsInt32;

                            if (!bsonDocument["OcrShortcut"].IsNull)
                                settings.OcrShortcut = bsonDocument["OcrShortcut"].AsString;
                            if (!bsonDocument["OcrLangsListStr"].IsNull)
                                settings.OcrLangsListStr = bsonDocument["OcrLangsListStr"].AsString;
                        }
                        else
                        {
                            CreateSettings(settings);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

            _isGetNow = false;
        }

        internal static void CreateSettingsOnlyLic(SettingsDataModel settings)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("SettingsDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    bsonDocument["_id"] = ObjectId.NewObjectId();
                    bsonDocument["ProxyUseIE"] = settings.ProxyUseIE;
                    bsonDocument["ProxyServer"] = settings.ProxyServer;
                    bsonDocument["ProxyPort"] = settings.ProxyPort;
                    bsonDocument["ProxyUserName"] = settings.ProxyUserName;
                    bsonDocument["ProxyPassword"] = settings.ProxyPassword;
                    bsonDocument["LicCode"] = settings.LicCode;
                    bsonDocument["LicEmail"] = settings.LicEmail;
                    bsonDocument["LicIsActive"] = settings.LicIsActive;
                    bsonDocument["LicIsEvaluate"] = settings.LicIsEvaluate;
                    bsonDocument["LicEvaluateCode"] = settings.LicEvaluateCode;
                    bsonDocument["LicEvaluateStart"] = settings.LicEvaluateStart;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void CreateSettings(SettingsDataModel settings)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("SettingsDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    bsonDocument["_id"] = ObjectId.NewObjectId();
                    bsonDocument["HashFromSite"] = settings.HashFromSite;
                    bsonDocument["LastVersionForCheckHash"] = settings.LastVersionForCheckHash;
                    bsonDocument["MainFormMinimizeToTray"] = settings.MainFormMinimizeToTray;
                    bsonDocument["CanClose"] = settings.CanClose;
                    bsonDocument["AppUILang"] = settings.AppUILang;
                    bsonDocument["AppUITheme"] = settings.AppUITheme;
                    bsonDocument["AppUIAccent"] = settings.AppUIAccent;
                    bsonDocument["AppFont"] = settings.AppFont;
                    bsonDocument["AppFontSize"] = settings.AppFontSize;
                    bsonDocument["OpenMainWindowShortcut"] = settings.OpenMainWindowShortcut;
                    bsonDocument["StopWorkingShortcut"] = settings.StopWorkingShortcut;
                    bsonDocument["IsCheckUpdate"] = settings.IsCheckUpdate;
                    bsonDocument["IsCheckUpdateBeta"] = settings.IsCheckUpdateBeta;
                    bsonDocument["IsCheckedStartAsAdmin"] = settings.IsCheckedStartAsAdmin;
                    bsonDocument["CurrentVersion"] = settings.CurrentVersion;
                    bsonDocument["FunctionOrder"] = settings.FunctionOrder;
                    bsonDocument["ThemeTimeStartNight"] = settings.ThemeTimeStartNight;
                    bsonDocument["ThemeTimeEndNight"] = settings.ThemeTimeEndNight;
                    bsonDocument["IsUseNightTheme"] = settings.IsUseNightTheme;
                    bsonDocument["IsStopWorkingFullScreen"] = settings.IsStopWorkingFullScreen;
                    bsonDocument["ProxyUseIE"] = settings.ProxyUseIE;
                    bsonDocument["ProxyServer"] = settings.ProxyServer;
                    bsonDocument["ProxyPort"] = settings.ProxyPort;
                    bsonDocument["ProxyUserName"] = settings.ProxyUserName;
                    bsonDocument["ProxyPassword"] = settings.ProxyPassword;
                    bsonDocument["LicCode"] = settings.LicCode;
                    bsonDocument["LicEmail"] = settings.LicEmail;
                    bsonDocument["LicIsActive"] = settings.LicIsActive;
                    bsonDocument["LicIsEvaluate"] = settings.LicIsEvaluate;
                    bsonDocument["LicEvaluateCode"] = settings.LicEvaluateCode;
                    bsonDocument["LicEvaluateStart"] = settings.LicEvaluateStart;
                    bsonDocument["ClipboardFastActionWindowSize"] = settings.ClipboardFastActionWindowSize;
                    bsonDocument["DiaryActionWindowSize"] = settings.DiaryActionWindowSize;
                    bsonDocument["AutochangeActionWindowSize"] = settings.SnippetsActionWindowSize;
                    bsonDocument["AutochangeIsEnabledCountUsage"] = settings.SnippetsIsEnabledCountUsage;
                    bsonDocument["SnippetsIsEnabledSortingAlphabet"] = settings.SnippetsIsEnabledSortingAlphabet;
                    bsonDocument["TranslateLangFrom"] = settings.TranslateLangFrom;
                    bsonDocument["TranslateLangTo"] = settings.TranslateLangTo;
                    bsonDocument["TranslateProvider"] = settings.TranslateProvider;
                    bsonDocument["TranslateFavoriteLanguages"] = settings.TranslateFavoriteLanguages;
                    bsonDocument["TranslateOnlyFavoriteLanguages"] = settings.TranslateOnlyFavoriteLanguages;
                    bsonDocument["TranslateShowMiniFormAlwaysWhenSelectText"] = settings.TranslateShowMiniFormAlwaysWhenSelectText;
                    bsonDocument["TranslateShowMiniFormDoubleCtrl"] = settings.TranslateShowMiniFormDoubleCtrl;
                    bsonDocument["TranslateShowMiniFormShortcut"] = settings.TranslateShowMiniFormShortcut;
                    bsonDocument["TranslateIsOn"] = settings.TranslateIsOn;
                    bsonDocument["TranslateHistoryIsOn"] = settings.TranslateHistoryIsOn;
                    bsonDocument["TranslateFontFamily"] = settings.TranslateFontFamily;
                    bsonDocument["TranslateFontSize"] = settings.TranslateFontSize;
                    bsonDocument["SpellCheckShortcut"] = settings.SpellCheckShortcut;
                    bsonDocument["SpellCheckCloseByTimer"] = settings.SpellCheckCloseByTimer;
                    bsonDocument["SpellCheckWhileTyping"] = settings.SpellCheckWhileTyping;
                    bsonDocument["SpellCheckWhileTypingSoundOn"] = settings.SpellCheckWhileTypingSoundOn;
                    bsonDocument["SpellCheckWhileTypingUseNumber"] = settings.SpellCheckWhileTypingUseNumber;
                    bsonDocument["SpellCheckIsOn"] = settings.SpellCheckIsOn;
                    bsonDocument["SwitcherIsOn"] = settings.SwitcherIsOn;
                    bsonDocument["SwitcherLeaveTextSelectedAfterSwitch"] = settings.SwitcherLeaveTextSelectedAfterSwitch;
                    bsonDocument["SwitcherSwitchTextLangShortcut"] = settings.SwitcherSwitchTextLangShortcut;
                    bsonDocument["SwitcherSwitchTextLangForAllLineShortcut"] = settings.SwitcherSwitchTextLangForAllLineShortcut;
                    bsonDocument["SwitcherShortcutSwitchLangSelectedText"] = settings.SwitcherShortcutSwitchLangSelectedTextShortcut;
                    bsonDocument["ConverterShortcutCapsOpenWindow"] = settings.ConverterShortcutCapsOpenWindow;
                    bsonDocument["SwitcherShortcutSwitchCapsSelectedText"] = settings.ConverterShortcutCapsInvert;
                    bsonDocument["SwitcherShortcutSwitchCapsUpSelectedTextShortcut"] = settings.ConverterShortcutCapsUp;
                    bsonDocument["SwitcherShortcutSwitchCapsDownSelectedTextShortcut"] = settings.ConverterShortcutCapsDown;
                    bsonDocument["ConverterFirstLetterToDown"] = settings.ConverterFirstLetterToDown;
                    bsonDocument["ConverterFirstLetterToUp"] = settings.ConverterFirstLetterToUp;
                    bsonDocument["SwitcherSountIsOn"] = settings.SwitcherSountIsOn;
                    bsonDocument["SwitcherSwitchLangByCtrlPlusNumberIsOn"] = settings.SwitcherSwitchLangByCtrlPlusNumberIsOn;
                    bsonDocument["SwitcherSwitchLangByNonStandartKey"] = settings.SwitcherSwitchLangByNonStandartKey;
                    bsonDocument["SwitcherSwitchMethod"] = settings.SwitcherSwitchMethod;
                    bsonDocument["SwitcherTrueListOfLang"] = settings.SwitcherNotTrueListOfLang;
                    bsonDocument["SwitcherLangAndKeysForSwitch"] = settings.SwitcherLangAndKeysForSwitch;
                    bsonDocument["AutoSwitcherIsOn"] = settings.AutoSwitcherIsOn;
                    bsonDocument["AutoSwitcherTrueListOfLang"] = settings.AutoSwitcherNotTrueListOfLang;
                    bsonDocument["AutoSwitcherFixTwoUpperCaseLettersInStart"] = settings.AutoSwitcherFixTwoUpperCaseLettersInStart;
                    bsonDocument["AutoSwitcherFixWrongUpperCase"] = settings.AutoSwitcherFixWrongUpperCase;
                    bsonDocument["AutoSwitcherIsSwitchOneLetter"] = settings.AutoSwitcherIsSwitchOneLetter;
                    bsonDocument["AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters"] = settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters;
                    bsonDocument["AutoSwitcherSwitchTextLangAfterPressEnter"] = settings.AutoSwitcherSwitchTextLangAfterPressEnter;
                    bsonDocument["AutoSwitcherAddRule"] = settings.AutoSwitcherAddRule;
                    bsonDocument["AutoSwitcherDisableAutoSwitchAfterManualSwitch"] = settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch;
                    bsonDocument["AutoSwitcherShowAcceptWindow"] = settings.AutoSwitcherShowAcceptWindow;
                    bsonDocument["AutoSwitcherOnlyAfterSeparator"] = settings.AutoSwitcherOnlyAfterSeparator;
                    bsonDocument["AutoSwitcherAfterPause"] = settings.AutoSwitcherAfterPause;
                    bsonDocument["AutoSwitcherCountCheckRule"] = settings.AutoSwitcherCountCheckRule;
                    bsonDocument["LangFlagShowForMouse"] = settings.LangFlagShowForMouse;
                    bsonDocument["LangFlagShowForCaret"] = settings.LangFlagShowForCaret;
                    bsonDocument["LangFlagShowLargeWindow"] = settings.LangFlagShowLargeWindow;
                    bsonDocument["LangFlagShowInTray"] = settings.LangFlagShowInTray;
                    bsonDocument["LangFlagShowIcons"] = settings.LangFlagShowIcons;
                    bsonDocument["LangFlagPosCarretX"] = settings.LangFlagPosCarretX;
                    bsonDocument["LangFlagPosCarretY"] = settings.LangFlagPosCarretY;
                    bsonDocument["LangFlagPosMouseX"] = settings.LangFlagPosMouseX;
                    bsonDocument["LangFlagPosMouseY"] = settings.LangFlagPosMouseY;
                    bsonDocument["LangInfoLargeWindowPosX"] = settings.LangInfoLargeWindowPosX;
                    bsonDocument["LangInfoLargeWindowPosY"] = settings.LangInfoLargeWindowPosY;
                    bsonDocument["LangFlagOpacityIcon"] = settings.LangFlagOpacityIcon;
                    bsonDocument["LangFlagSizeIcon"] = settings.LangFlagSizeIcon;
                    //bsonDocument["IndicateCurrentLangInKeyboardLed"] = settings.IndicateCurrentLangInKeyboardLed;
                    bsonDocument["IsHideIndicateInFullScreenApp"] = settings.IsHideIndicateInFullScreenApp;
                    bsonDocument["LangFlagIsIndicateCapsLockState"] = settings.LangFlagIsIndicateCapsLockState;
                    bsonDocument["LangFlagIsIndicateNumLockState"] = settings.LangFlagIsIndicateNumLockState;
                    bsonDocument["ClipboardIsOn"] = settings.ClipboardPasteWithoutFormattingShortcutIsOn;
                    bsonDocument["ClipboardHistoryIsOn"] = settings.ClipboardHistoryIsOn;
                    bsonDocument["ClipboardShowHistoryShortcut"] = settings.ClipboardShowHistoryShortcut;
                    bsonDocument["ClipboardPasteWithoutFormattingShortcut"] = settings.ClipboardPasteWithoutFormattingShortcut;
                    bsonDocument["ClipboardPasteRoundIsOn"] = settings.ClipboardPasteRoundIsOn;
                    bsonDocument["ClipboardReplaceWithoutChangeClipboard"] = settings.ClipboardReplaceWithoutChangeClipboard;
                    bsonDocument["ClipboardPasteRoundShortcut"] = settings.ClipboardPasteRoundShortcut;
                    bsonDocument["ClipboardPasteByCtrlPlusIndexIsOn"] = settings.ClipboardPasteByCtrlPlusIndexIsOn;
                    bsonDocument["ClipboardSaveFilePath"] = settings.ClipboardSaveFilePath;
                    bsonDocument["ClipboardSaveImage"] = settings.ClipboardSaveImage;
                    bsonDocument["ClipboardMaxClipboardItems"] = settings.ClipboardMaxClipboardItems;
                    bsonDocument["ClipboardSountIsOn"] = settings.ClipboardSountIsOn;
                    bsonDocument["DiaryIsOn"] = settings.DiaryIsOn;
                    bsonDocument["MaxDiaryItems"] = settings.MaxDiaryItems;
                    bsonDocument["DiaryPassword"] = settings.DiaryPassword;
                    bsonDocument["IsSaveOneWordSentences"] = settings.IsSaveOneWordSentences;
                    bsonDocument["DiaryShowShortcut"] = settings.DiaryShowShortcut;
                    bsonDocument["SmartClickIsOn"] = settings.SmartClickIsOn;
                    bsonDocument["SmartClickSearchService"] = settings.SmartClickSearchService;
                    bsonDocument["SmartClickShowOnPressLeftAndRightMouseButtons"] = settings.SmartClickShowOnPressLeftAndRightMouseButtons;
                    bsonDocument["SmartClickShowOnPressHotKeys"] = settings.SmartClickShowOnPressHotKeys;
                    bsonDocument["SmartClickShortcut"] = settings.SmartClickShortcut;
                    bsonDocument["SmartClickShowOnDoubleMiddle"] = settings.SmartClickShowOnDoubleMiddle;
                    bsonDocument["SmartClickMiniIsOn"] = settings.SmartClickMiniIsOn;
                    bsonDocument["SmartClickMiniPosY"] = settings.SmartClickMiniPosY;
                    bsonDocument["SmartClickMiniPosX"] = settings.SmartClickMiniPosX;
                    bsonDocument["SmartClickMiniSizeIcon"] = settings.SmartClickMiniSizeIcon;
                    bsonDocument["SmartClickCheckedItems"] = settings.SmartClickCheckedItems;
                    bsonDocument["AutoReplaceIsOn"] = settings.SnippetsIsOn;
                    bsonDocument["AutoReplaceIsShowTipWindow"] = settings.SnippetsIsShowTipWindow;
                    bsonDocument["AutoReplaceWithOtherLayout"] = settings.SnippetsWithOtherLayout;
                    bsonDocument["AutoReplaceIsCaseSensitive"] = settings.SnippetsIsCaseSensitive;
                    bsonDocument["AutoReplaceMethodPastByKey"] = settings.SnippetsMethodPastByKey;
                    bsonDocument["AutoReplaceShowAllShortcut"] = settings.SnippetsShowAllShortcut;
                    bsonDocument["AutoReplaceAddNewShortcut"] = settings.SnippetsAddNewShortcut;
                    bsonDocument["ConverterTransliterationShortcut"] = settings.ConverterTransliterationShortcut;
                    bsonDocument["ConverterOpenWindowShortcut"] = settings.ConverterOpenWindowShortcut;
                    bsonDocument["ConverterExpresionShortcut"] = settings.ConverterExpresionShortcut;
                    bsonDocument["ConverterEncloseTextQuotationMarksShortcut"] = settings.ConverterEncloseTextQuotationMarksShortcut;
                    bsonDocument["ConverterShortcutCamelCase"] = settings.ConverterShortcutCamelCase;
                    bsonDocument["ConverterShortcutSnakeCase"] = settings.ConverterShortcutSnakeCase;
                    bsonDocument["ConverterShortcutKebabCase"] = settings.ConverterShortcutKebabCase;
                    bsonDocument["ConverterShortcutPascalCase"] = settings.ConverterShortcutPascalCase;
                    bsonDocument["ConverterShortcutReplaceSelText"] = settings.ConverterShortcutReplaceSelText;
                    bsonDocument["ConverterFramesList"] = settings.ConverterFramesList;
                    bsonDocument["SoundForLangSwitch"] = settings.SoundForLangSwitch;
                    bsonDocument["SoundForSpellCheck"] = settings.SoundForSpellCheck;
                    bsonDocument["SoundVolumeForLangSwitch"] = settings.SoundVolumeForLangSwitch;
                    bsonDocument["SoundVolumeForSpellCheck"] = settings.SoundVolumeForSpellCheck;
                    bsonDocument["SoundVolumeForClipboard"] = settings.SoundVolumeForClipboard;
                    bsonDocument["SoundForClipboard"] = settings.SoundForClipboard;
                    bsonDocument["OcrShortcut"] = settings.OcrShortcut;
                    bsonDocument["OcrLangsListStr"] = settings.OcrLangsListStr;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void SaveSettings(SettingsDataModel settings)
        {
            if (_isGetNow) return;
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("SettingsDataModel");
                    var bsonDocument = schemelessCollection.FindAll().FirstOrDefault();
                    if (bsonDocument != null)
                    {
                        bsonDocument["HashFromSite"] = settings.HashFromSite;
                        bsonDocument["LastVersionForCheckHash"] = settings.LastVersionForCheckHash;
                        bsonDocument["MainFormMinimizeToTray"] = settings.MainFormMinimizeToTray;
                        bsonDocument["CanClose"] = settings.CanClose;
                        bsonDocument["AppUILang"] = settings.AppUILang;
                        bsonDocument["AppUITheme"] = settings.AppUITheme;
                        bsonDocument["AppUIAccent"] = settings.AppUIAccent;
                        bsonDocument["AppFont"] = settings.AppFont;
                        bsonDocument["AppFontSize"] = settings.AppFontSize;
                        bsonDocument["OpenMainWindowShortcut"] = settings.OpenMainWindowShortcut;
                        bsonDocument["StopWorkingShortcut"] = settings.StopWorkingShortcut;
                        bsonDocument["IsCheckUpdate"] = settings.IsCheckUpdate;
                        bsonDocument["IsCheckUpdateBeta"] = settings.IsCheckUpdateBeta;
                        bsonDocument["IsCheckedStartAsAdmin"] = settings.IsCheckedStartAsAdmin;
                        bsonDocument["CurrentVersion"] = settings.CurrentVersion;
                        bsonDocument["FunctionOrder"] = settings.FunctionOrder;
                        bsonDocument["ThemeTimeStartNight"] = settings.ThemeTimeStartNight;
                        bsonDocument["ThemeTimeEndNight"] = settings.ThemeTimeEndNight;
                        bsonDocument["IsUseNightTheme"] = settings.IsUseNightTheme;
                        bsonDocument["IsStopWorkingFullScreen"] = settings.IsStopWorkingFullScreen;
                        bsonDocument["ProxyUseIE"] = settings.ProxyUseIE;
                        bsonDocument["ProxyServer"] = settings.ProxyServer;
                        bsonDocument["ProxyPort"] = settings.ProxyPort;
                        bsonDocument["ProxyUserName"] = settings.ProxyUserName;
                        bsonDocument["ProxyPassword"] = settings.ProxyPassword;
                        bsonDocument["LicCode"] = settings.LicCode;
                        bsonDocument["LicEmail"] = settings.LicEmail;
                        bsonDocument["LicIsActive"] = settings.LicIsActive;
                        bsonDocument["LicIsEvaluate"] = settings.LicIsEvaluate;
                        bsonDocument["LicEvaluateCode"] = settings.LicEvaluateCode;
                        bsonDocument["LicEvaluateStart"] = settings.LicEvaluateStart;
                        bsonDocument["ClipboardFastActionWindowSize"] = settings.ClipboardFastActionWindowSize;
                        bsonDocument["DiaryActionWindowSize"] = settings.DiaryActionWindowSize;
                        bsonDocument["AutochangeActionWindowSize"] = settings.SnippetsActionWindowSize;
                        bsonDocument["AutochangeIsEnabledCountUsage"] = settings.SnippetsIsEnabledCountUsage;
                        bsonDocument["SnippetsIsEnabledSortingAlphabet"] = settings.SnippetsIsEnabledSortingAlphabet;
                        bsonDocument["TranslateLangFrom"] = settings.TranslateLangFrom;
                        bsonDocument["TranslateLangTo"] = settings.TranslateLangTo;
                        bsonDocument["TranslateProvider"] = settings.TranslateProvider;
                        bsonDocument["TranslateFavoriteLanguages"] = settings.TranslateFavoriteLanguages;
                        bsonDocument["TranslateOnlyFavoriteLanguages"] = settings.TranslateOnlyFavoriteLanguages;
                        bsonDocument["TranslateShowMiniFormAlwaysWhenSelectText"] = settings.TranslateShowMiniFormAlwaysWhenSelectText;
                        bsonDocument["TranslateShowMiniFormDoubleCtrl"] = settings.TranslateShowMiniFormDoubleCtrl;
                        bsonDocument["TranslateShowMiniFormShortcut"] = settings.TranslateShowMiniFormShortcut;
                        bsonDocument["TranslateIsOn"] = settings.TranslateIsOn;
                        bsonDocument["TranslateHistoryIsOn"] = settings.TranslateHistoryIsOn;
                        bsonDocument["TranslateFontFamily"] = settings.TranslateFontFamily;
                        bsonDocument["TranslateFontSize"] = settings.TranslateFontSize;
                        bsonDocument["SpellCheckShortcut"] = settings.SpellCheckShortcut;
                        bsonDocument["SpellCheckCloseByTimer"] = settings.SpellCheckCloseByTimer;
                        bsonDocument["SpellCheckWhileTyping"] = settings.SpellCheckWhileTyping;
                        bsonDocument["SpellCheckWhileTypingSoundOn"] = settings.SpellCheckWhileTypingSoundOn;
                        bsonDocument["SpellCheckWhileTypingUseNumber"] = settings.SpellCheckWhileTypingUseNumber;
                        bsonDocument["SpellCheckIsOn"] = settings.SpellCheckIsOn;
                        bsonDocument["SwitcherIsOn"] = settings.SwitcherIsOn;
                        bsonDocument["SwitcherLeaveTextSelectedAfterSwitch"] = settings.SwitcherLeaveTextSelectedAfterSwitch;
                        bsonDocument["SwitcherSwitchTextLangShortcut"] = settings.SwitcherSwitchTextLangShortcut;
                        bsonDocument["SwitcherSwitchTextLangForAllLineShortcut"] = settings.SwitcherSwitchTextLangForAllLineShortcut;
                        bsonDocument["SwitcherShortcutSwitchLangSelectedText"] = settings.SwitcherShortcutSwitchLangSelectedTextShortcut;
                        bsonDocument["SwitcherSountIsOn"] = settings.SwitcherSountIsOn;
                        bsonDocument["SwitcherSwitchLangByCtrlPlusNumberIsOn"] = settings.SwitcherSwitchLangByCtrlPlusNumberIsOn;
                        bsonDocument["SwitcherSwitchLangByNonStandartKey"] = settings.SwitcherSwitchLangByNonStandartKey;
                        bsonDocument["SwitcherSwitchMethod"] = settings.SwitcherSwitchMethod;
                        bsonDocument["SwitcherLangAndKeysForSwitch"] = settings.SwitcherLangAndKeysForSwitch;
                        bsonDocument["SwitcherTrueListOfLang"] = settings.SwitcherNotTrueListOfLang;
                        bsonDocument["AutoSwitcherIsOn"] = settings.AutoSwitcherIsOn;
                        bsonDocument["AutoSwitcherTrueListOfLang"] = settings.AutoSwitcherNotTrueListOfLang;
                        bsonDocument["AutoSwitcherFixTwoUpperCaseLettersInStart"] = settings.AutoSwitcherFixTwoUpperCaseLettersInStart;
                        bsonDocument["AutoSwitcherFixWrongUpperCase"] = settings.AutoSwitcherFixWrongUpperCase;
                        bsonDocument["AutoSwitcherIsSwitchOneLetter"] = settings.AutoSwitcherIsSwitchOneLetter;
                        bsonDocument["AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters"] = settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters;
                        bsonDocument["AutoSwitcherSwitchTextLangAfterPressEnter"] = settings.AutoSwitcherSwitchTextLangAfterPressEnter;
                        bsonDocument["AutoSwitcherAddRule"] = settings.AutoSwitcherAddRule;
                        bsonDocument["AutoSwitcherDisableAutoSwitchAfterManualSwitch"] = settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch;
                        bsonDocument["AutoSwitcherCountCheckRule"] = settings.AutoSwitcherCountCheckRule;
                        bsonDocument["AutoSwitcherShowAcceptWindow"] = settings.AutoSwitcherShowAcceptWindow;
                        bsonDocument["AutoSwitcherOnlyAfterSeparator"] = settings.AutoSwitcherOnlyAfterSeparator;
                        bsonDocument["AutoSwitcherAfterPause"] = settings.AutoSwitcherAfterPause;
                        bsonDocument["LangFlagShowForMouse"] = settings.LangFlagShowForMouse;
                        bsonDocument["LangFlagShowForCaret"] = settings.LangFlagShowForCaret;
                        bsonDocument["LangFlagShowLargeWindow"] = settings.LangFlagShowLargeWindow;
                        bsonDocument["LangFlagShowInTray"] = settings.LangFlagShowInTray;
                        bsonDocument["LangFlagShowIcons"] = settings.LangFlagShowIcons;
                        bsonDocument["LangFlagPosCarretX"] = settings.LangFlagPosCarretX;
                        bsonDocument["LangFlagPosCarretY"] = settings.LangFlagPosCarretY;
                        bsonDocument["LangFlagPosMouseX"] = settings.LangFlagPosMouseX;
                        bsonDocument["LangFlagPosMouseY"] = settings.LangFlagPosMouseY;
                        bsonDocument["LangInfoLargeWindowPosX"] = settings.LangInfoLargeWindowPosX;
                        bsonDocument["LangInfoLargeWindowPosY"] = settings.LangInfoLargeWindowPosY;
                        bsonDocument["LangFlagOpacityIcon"] = settings.LangFlagOpacityIcon;
                        bsonDocument["LangFlagSizeIcon"] = settings.LangFlagSizeIcon;
                        //bsonDocument["IndicateCurrentLangInKeyboardLed"] = settings.IndicateCurrentLangInKeyboardLed;
                        bsonDocument["IsHideIndicateInFullScreenApp"] = settings.IsHideIndicateInFullScreenApp;
                        bsonDocument["LangFlagIsIndicateCapsLockState"] = settings.LangFlagIsIndicateCapsLockState;
                        bsonDocument["LangFlagIsIndicateNumLockState"] = settings.LangFlagIsIndicateNumLockState;
                        bsonDocument["ClipboardIsOn"] = settings.ClipboardPasteWithoutFormattingShortcutIsOn;
                        bsonDocument["ClipboardHistoryIsOn"] = settings.ClipboardHistoryIsOn;
                        bsonDocument["ClipboardShowHistoryShortcut"] = settings.ClipboardShowHistoryShortcut;
                        bsonDocument["ClipboardPasteWithoutFormattingShortcut"] = settings.ClipboardPasteWithoutFormattingShortcut;
                        bsonDocument["ClipboardPasteRoundIsOn"] = settings.ClipboardPasteRoundIsOn;
                        bsonDocument["ClipboardReplaceWithoutChangeClipboard"] = settings.ClipboardReplaceWithoutChangeClipboard;
                        bsonDocument["ClipboardPasteRoundShortcut"] = settings.ClipboardPasteRoundShortcut;
                        bsonDocument["ClipboardPasteByCtrlPlusIndexIsOn"] = settings.ClipboardPasteByCtrlPlusIndexIsOn;
                        bsonDocument["ClipboardSaveFilePath"] = settings.ClipboardSaveFilePath;
                        bsonDocument["ClipboardSaveImage"] = settings.ClipboardSaveImage;
                        bsonDocument["ClipboardMaxClipboardItems"] = settings.ClipboardMaxClipboardItems;
                        bsonDocument["ClipboardSountIsOn"] = settings.ClipboardSountIsOn;
                        bsonDocument["DiaryIsOn"] = settings.DiaryIsOn;
                        bsonDocument["MaxDiaryItems"] = settings.MaxDiaryItems;
                        bsonDocument["DiaryPassword"] = settings.DiaryPassword;
                        bsonDocument["IsSaveOneWordSentences"] = settings.IsSaveOneWordSentences;
                        bsonDocument["DiaryShowShortcut"] = settings.DiaryShowShortcut;
                        bsonDocument["SmartClickIsOn"] = settings.SmartClickIsOn;
                        bsonDocument["SmartClickSearchService"] = settings.SmartClickSearchService;
                        bsonDocument["SmartClickShowOnPressLeftAndRightMouseButtons"] = settings.SmartClickShowOnPressLeftAndRightMouseButtons;
                        bsonDocument["SmartClickShowOnPressHotKeys"] = settings.SmartClickShowOnPressHotKeys;
                        bsonDocument["SmartClickShortcut"] = settings.SmartClickShortcut;
                        bsonDocument["SmartClickShowOnDoubleMiddle"] = settings.SmartClickShowOnDoubleMiddle;
                        bsonDocument["SmartClickMiniIsOn"] = settings.SmartClickMiniIsOn;
                        bsonDocument["SmartClickMiniPosY"] = settings.SmartClickMiniPosY;
                        bsonDocument["SmartClickMiniPosX"] = settings.SmartClickMiniPosX;
                        bsonDocument["SmartClickMiniSizeIcon"] = settings.SmartClickMiniSizeIcon;
                        bsonDocument["SmartClickCheckedItems"] = settings.SmartClickCheckedItems;
                        bsonDocument["AutoReplaceIsOn"] = settings.SnippetsIsOn;
                        bsonDocument["AutoReplaceIsShowTipWindow"] = settings.SnippetsIsShowTipWindow;
                        bsonDocument["AutoReplaceWithOtherLayout"] = settings.SnippetsWithOtherLayout;
                        bsonDocument["AutoReplaceIsCaseSensitive"] = settings.SnippetsIsCaseSensitive;
                        bsonDocument["AutoReplaceMethodPastByKey"] = settings.SnippetsMethodPastByKey;
                        bsonDocument["AutoReplaceShowAllShortcut"] = settings.SnippetsShowAllShortcut;
                        bsonDocument["AutoReplaceAddNewShortcut"] = settings.SnippetsAddNewShortcut;
                        bsonDocument["ConverterTransliterationShortcut"] = settings.ConverterTransliterationShortcut;
                        bsonDocument["ConverterOpenWindowShortcut"] = settings.ConverterOpenWindowShortcut;
                        bsonDocument["ConverterExpresionShortcut"] = settings.ConverterExpresionShortcut;
                        bsonDocument["ConverterShortcutCapsOpenWindow"] = settings.ConverterShortcutCapsOpenWindow;
                        bsonDocument["SwitcherShortcutSwitchCapsSelectedText"] = settings.ConverterShortcutCapsInvert;
                        bsonDocument["SwitcherShortcutSwitchCapsUpSelectedTextShortcut"] = settings.ConverterShortcutCapsUp;
                        bsonDocument["SwitcherShortcutSwitchCapsDownSelectedTextShortcut"] = settings.ConverterShortcutCapsDown;
                        bsonDocument["ConverterFirstLetterToDown"] = settings.ConverterFirstLetterToDown;
                        bsonDocument["ConverterFirstLetterToUp"] = settings.ConverterFirstLetterToUp;
                        bsonDocument["ConverterEncloseTextQuotationMarksShortcut"] = settings.ConverterEncloseTextQuotationMarksShortcut;
                        bsonDocument["ConverterShortcutCamelCase"] = settings.ConverterShortcutCamelCase;
                        bsonDocument["ConverterShortcutSnakeCase"] = settings.ConverterShortcutSnakeCase;
                        bsonDocument["ConverterShortcutKebabCase"] = settings.ConverterShortcutKebabCase;
                        bsonDocument["ConverterShortcutPascalCase"] = settings.ConverterShortcutPascalCase;
                        bsonDocument["ConverterShortcutReplaceSelText"] = settings.ConverterShortcutReplaceSelText;
                        bsonDocument["ConverterFramesList"] = settings.ConverterFramesList;
                        bsonDocument["SoundForLangSwitch"] = settings.SoundForLangSwitch;
                        bsonDocument["SoundForSpellCheck"] = settings.SoundForSpellCheck;
                        bsonDocument["SoundVolumeForLangSwitch"] = settings.SoundVolumeForLangSwitch;
                        bsonDocument["SoundVolumeForSpellCheck"] = settings.SoundVolumeForSpellCheck;
                        bsonDocument["SoundVolumeForClipboard"] = settings.SoundVolumeForClipboard;
                        bsonDocument["SoundForClipboard"] = settings.SoundForClipboard;
                        bsonDocument["OcrShortcut"] = settings.OcrShortcut;
                        bsonDocument["OcrLangsListStr"] = settings.OcrLangsListStr;

                        schemelessCollection.Update(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }
    }
}
