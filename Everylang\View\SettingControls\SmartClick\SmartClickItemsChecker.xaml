﻿<UserControl x:Class="Everylang.App.View.SettingControls.SmartClick.SmartClickItemsChecker"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
             xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF"
             xmlns:smartClick="clr-namespace:Everylang.App.View.SettingControls.SmartClick"
             mc:Ignorable="d" x:ClassModifier="internal">
    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel  Margin="10,8,0,0" Orientation="Horizontal" VerticalAlignment="Top">
            <telerik:RadButton Width="35" Height="35" Click="HidePanelButtonClick" Cursor="Hand" telerik:CornerRadiusHelper.ClipRadius="5" MinHeight="0" Padding="0">
                <wpf:MaterialIcon Kind="ArrowLeftBold"  Height="20" Width="20"/>
            </telerik:RadButton>
            <TextBlock Margin="10,0,0,0" FontSize="15" VerticalAlignment="Center" HorizontalAlignment="Left" Text="{telerik:LocalizableResource Key=UniversalWindowSettingsItemsCheck}" />
        </StackPanel>

        <telerik:RadListBox x:Name="LvItems" Grid.Row="1" VerticalAlignment="Stretch" BorderBrush="{x:Null}" Margin="10" >
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate DataType="smartClick:SmartClickItem">
                    <CheckBox Margin="0,0,0,0" FontSize="14" Focusable="False" MinHeight="0" IsChecked="{Binding Checked}" Checked="ToggleButton_OnChecked" Unchecked="ToggleButton_OnChecked">
                        <TextBlock FontSize="14" Text="{Binding Name}" />
                    </CheckBox>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
        </telerik:RadListBox>
    </Grid>
</UserControl>
