﻿using System;

namespace Everylang.App.License.LicenseCore;

internal class ZBase32Encoder : Base32Encoder
{
    private const string DEF_ENCODING_TABLE = "ybndrfg8ejkmcpqxot1uwisza345h769";

    private const char DEF_PADDING = '=';

    internal ZBase32Encoder()
        : base(DEF_ENCODING_TABLE, DEF_PADDING)
    {
    }

    internal override string Encode(byte[] input)
    {
        var encoded = base.Encode(input);

        return encoded.TrimEnd(DEF_PADDING);
    }

    internal override byte[] Decode(string data)
    {
        var expectedOrigSize = Convert.ToInt32(Math.Floor(data.Length / 1.6));

        var expectedPaddedLength = 8 * Convert.ToInt32(Math.Ceiling(expectedOrigSize / 5.0));

        var base32Data = data.PadRight(expectedPaddedLength, DEF_PADDING).ToLower();
        return base.Decode(base32Data);
    }
}