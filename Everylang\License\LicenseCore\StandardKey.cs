﻿using System;
using System.Globalization;
using System.IO;

namespace Everylang.App.License.LicenseCore
{
    internal class StandardKey : LicenseKeyData
    {
        internal enum Edition
        {
            Standard,
            Enterprise
        }

        internal Edition LicensedEdition
        {
            get;
            set;
        }

        internal byte LicensedMajorVersion
        {
            get;
            set;
        }

        internal byte LicensedMinorVersion
        {
            get;
            set;
        }

        internal ushort LicensedUsers
        {
            get;
            set;
        }

        internal DateTime ExpiryDate
        {
            get;
            set;
        }

        public StandardKey()
        { }

        private byte Calcheader()
        {

            return (byte)((LicensedUsers +
                           (short)Math.Pow((double)LicensedEdition, ExpiryDate.Day) +
                           Math.Pow(LicensedMajorVersion, ((DateOnly)ExpiryDate).ToInt())) % 255);
        }

        public override string ToString()
        {
            return LicensedEdition + " Edition, Major version " + LicensedMajorVersion + " Minor Version:" + LicensedMinorVersion + " Licensed for " + LicensedUsers +

                   " Users, Expires " + ExpiryDate.ToString(CultureInfo.InvariantCulture);

        }

        private byte Calcfooter(byte headervalue)
        {
            return (byte)((((DateOnly)ExpiryDate).ToInt() - LicensedMajorVersion * LicensedMinorVersion / (byte)(LicensedEdition + 1) + LicensedUsers ^ headervalue) % 255);
        }

        public StandardKey(Stream sourceData) : base(sourceData)
        {
            using BinaryReader br = new BinaryReader(sourceData);
            var header = br.ReadByte();

            LicensedEdition = (Edition)br.ReadInt16();
            LicensedMajorVersion = br.ReadByte();
            LicensedMinorVersion = br.ReadByte();
            LicensedUsers = br.ReadUInt16();
            var exDay = br.ReadByte();
            var exMonth = br.ReadByte();
            var exYear = br.ReadUInt16();
            try
            {
                _ = new DateTime(exYear, exMonth, exDay);
            }
            catch
            {
                return;
            }
            ExpiryDate = new DateOnly(exDay, exMonth, exYear);
            var footer = br.ReadByte();
            var calculatedHeader = Calcheader();
            var calculatedFooter = Calcfooter(header);

            if (calculatedHeader != header || calculatedFooter != footer)
            {
                throw new InvalidKeyException("Invalid Product Key.");
            }
        }

        internal override void PersistData(Stream target)
        {
            byte header = Calcheader();
            byte footer = Calcfooter(header);
            var major = LicensedMajorVersion;
            var minor = LicensedMinorVersion;

            using BinaryWriter bw = new BinaryWriter(target);

            bw.Write(header);
            bw.Write((short)LicensedEdition);
            bw.Write(major);
            bw.Write(minor);
            bw.Write(LicensedUsers);
            DateOnly dw = new DateOnly(ExpiryDate);
            bw.Write(dw.Day);
            bw.Write(dw.Month);
            bw.Write(dw.Year);
            bw.Write(footer);
        }
    }


}
