﻿using System.Collections.Generic;
using System.Globalization;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace Everylang.App.License.LicenseCore
{
    internal class HardwareId
    {
        internal static string? GetHash(string s)
        {
#pragma warning disable SYSLIB0021
            MD5 sec = new MD5CryptoServiceProvider();
#pragma warning restore SYSLIB0021
            var bt = Encoding.ASCII.GetBytes(s);
            return GetHexString(sec.ComputeHash(bt));
        }

        private static string? GetHexString(IList<byte> bt)
        {
            var s = string.Empty;
            for (var i = 0; i < bt.Count; i++)
            {
                var b = bt[i];
                int n = b;
                var n1 = n & 15;
                var n2 = (n >> 4) & 15;
                if (n2 > 9)
                    s += ((char)(n2 - 10 + 'A')).ToString(CultureInfo.InvariantCulture);
                else
                    s += n2.ToString(CultureInfo.InvariantCulture);
                if (n1 > 9)
                    s += ((char)(n1 - 10 + 'A')).ToString(CultureInfo.InvariantCulture);
                else
                    s += n1.ToString(CultureInfo.InvariantCulture);
            }

            return s;
        }

        private static string? _fingerPrint = string.Empty;

        internal static string? GetId()
        {
            if (string.IsNullOrEmpty(_fingerPrint))
                try
                {
                    _fingerPrint = GetHash("CPU >> " + CpuId() + "\nBIOS >> " + BiosId() + "\nBASE >> " + BaseId());
                }
                catch
                {
                    // ignored
                }

            return _fingerPrint;
        }

        private static string? Identifier(string wmiClass, string wmiProperty, string wmiMustBeTrue)
        {
            var result = "";

            ManagementObjectCollection moc;
            using (var mc = new ManagementClass(wmiClass))
            {
                moc = mc.GetInstances();
            }

            foreach (var mo in moc)
            {
                if (mo[wmiMustBeTrue].ToString() != "True") continue;
                if (result != "") continue;
                try
                {
                    result = mo[wmiProperty].ToString();
                    break;
                }
                catch
                {
                    // ignored
                }
            }

            return result;
        }

        private static string? Identifier(string wmiClass, string wmiProperty)
        {
            var result = "";
            try
            {
                ManagementObjectCollection moc;
                using (var mc = new ManagementClass(wmiClass))
                {
                    moc = mc.GetInstances();
                }

                foreach (var mo in moc)
                {
                    if (result != "") continue;
                    try
                    {
                        if (wmiProperty != null) result = mo[wmiProperty].ToString();
                        break;
                    }
                    catch
                    {
                        // ignored
                    }
                }
            }
            catch
            {
                // ignored
            }

            return result;
        }

        private static string? CpuId()
        {
            var retVal = Identifier("Win32_Processor", "UniqueId");
            if (retVal != "")
                return retVal;
            retVal = Identifier("Win32_Processor", "ProcessorId");
            if (retVal != "")
                return retVal;
            retVal = Identifier("Win32_Processor", "Name");
            if (retVal == "") //If no Name, use Manufacturer
                retVal = Identifier("Win32_Processor", "Manufacturer");
            retVal += Identifier("Win32_Processor", "MaxClockSpeed");
            return retVal;
        }

        private static string BiosId()
        {
            return Identifier("Win32_BIOS", "Manufacturer") + Identifier("Win32_BIOS", "SMBIOSBIOSVersion") +
                   Identifier("Win32_BIOS", "IdentificationCode") + Identifier("Win32_BIOS", "SerialNumber") +
                   Identifier("Win32_BIOS", "ReleaseDate") + Identifier("Win32_BIOS", "Version");
        }

        private static string DiskId()
        {
            return Identifier("Win32_DiskDrive", "Model") + Identifier("Win32_DiskDrive", "Manufacturer") +
                   Identifier("Win32_DiskDrive", "Signature") + Identifier("Win32_DiskDrive", "TotalHeads");
        }

        private static string BaseId()
        {
            return Identifier("Win32_BaseBoard", "Model") + Identifier("Win32_BaseBoard", "Manufacturer") +
                   Identifier("Win32_BaseBoard", "Name") + Identifier("Win32_BaseBoard", "SerialNumber");
        }

        private static string VideoId()
        {
            return Identifier("Win32_VideoController", "DriverVersion") + Identifier("Win32_VideoController", "Name");
        }

        private static string? MacId()
        {
            return Identifier("Win32_NetworkAdapterConfiguration", "MACAddress", "IPEnabled");
        }
    }

}
